#[approck::http(GET /malawi/attendance/{guard_attendance_uuid:Uuid}/edit; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::web::malawi::attendance::mapper::index::guard_attendance_detail;
        use maud::html;

        doc.set_title("Edit Attendance");

        let mut form_panel =
            bux::component::save_cancel_form_panel("Edit Attendance", "/attendance/list");

        // Get attendance details
        let guard = guard_attendance_detail::call(
            app,
            identity,
            guard_attendance_detail::Input {
                guard_attendance_uuid: path.guard_attendance_uuid,
            },
        )
        .await?;

        form_panel.set_hidden(
            "guard_attendance_uuid",
            guard.guard_attendance_uuid.to_string(),
        );

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            (bux::input::datetime::bux_input_datetime("start_ts", "Start Time", Some(guard.start_ts.into())))
            (bux::input::datetime::bux_input_datetime("end_ts", "End Time", Some(guard.end_ts.into())))
            (bux::input::textarea::string::name_label_value("note", "Note", guard.note.as_deref()))
        ));

        doc.add_body(html! {
            bux-action-panel {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod attendance_edit {
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub guard_attendance_uuid: Uuid,
        pub start_ts: DateTimeUtc,
        pub end_ts: DateTimeUtc,
        pub note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub guard_attendance_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.attendance_edit(input.guard_attendance_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to attendance edit".to_string(),
            ));
        }

        let mut dbcx = app.postgres_dbcx().await?;

        // Transaction block
        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $guard_attendance_uuid: &input.guard_attendance_uuid,
                    $start_ts: &input.start_ts,
                    $end_ts: &input.end_ts,
                    $note: &input.note,
                };
                row = {
                    guard_attendance_uuid: Uuid,
                };

                UPDATE
                    appcove_malawi.guard_attendance
                SET
                    start_ts = $start_ts,
                    end_ts = $end_ts,
                    note = $note
                WHERE
                    guard_attendance_uuid = $guard_attendance_uuid
                RETURNING
                    guard_attendance_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Response::Output(Output {
            guard_attendance_uuid: row.guard_attendance_uuid,
            detail_url: crate::ml_guard_attendance(row.guard_attendance_uuid),
        }))
    }
}

import "./edit.mcss";
import "@bux/input/datetime.mts";
import "@bux/input/textarea/string.mts";

import { SE, SEC } from "@granite/lib.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";
import { attendance_edit } from "./editλ.mts";
import FormPanel from "@bux/component/form_panel.mts";
import BuxInputDatetime from "@bux/input/datetime.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $guard_attendance_uuid = SE($form, "[name=guard_attendance_uuid]") as HTMLInputElement;
const $start_ts = SEC(BuxInputDatetime, $form, "[name=start_ts]");
const $end_ts = SEC(BuxInputDatetime, $form, "[name=end_ts]");
const $note = SEC(BuxInputTextareaString, $form, "[name=note]");

new FormPanel({
    $form,
    api: attendance_edit.api,
    on_cancel: go_back,

    err: (errors) => {
        $start_ts.set_e(errors.start_ts);
        $end_ts.set_e(errors.end_ts);
        $note.set_e(errors.note);
    },

    get: () => {
        return {
            guard_attendance_uuid: $guard_attendance_uuid.value,
            start_ts: $start_ts.value,
            end_ts: $end_ts.value,
            note: $note.value_option,
        };
    },

    set: (_value) => {
        // No need to set values for edit form
    },

    out: (output) => {
        go_next((output as any).detail_url);
    },
});

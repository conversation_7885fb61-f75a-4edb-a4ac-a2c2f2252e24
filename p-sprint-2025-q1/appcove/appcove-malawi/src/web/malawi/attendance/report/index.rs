#[approck::http(GET /malawi/attendance/report/; AUTH None; return HTML;)]
pub mod page {
    use chrono::Local;
    use granite::{DateTimeUtc, NaiveDate, Uuid};
    use std::collections::HashMap;

    #[derive(Debug)]
    struct DailyReport {
        date: NaiveDate,
        guard_name: String,
        scheduled_start: Option<DateTimeUtc>,
        scheduled_end: Option<DateTimeUtc>,
        actual_start: Option<DateTimeUtc>,
        actual_end: Option<DateTimeUtc>,
    }

    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use crate::web::malawi::attendance::index::guard_attendance_list;
        use crate::web::malawi::schedule::index::guard_schedule_list;
        use maud::html;

        let attendance = guard_attendance_list::call(
            app,
            identity,
            guard_attendance_list::Input {
                keyword: None,
                active: Some(true),
            },
        )
        .await?;

        let schedule = guard_schedule_list::call(
            app,
            identity,
            guard_schedule_list::Input {
                keyword: None,
                active: Some(true),
            },
        )
        .await?;

        // Create a map to combine data by date and guard
        let mut daily_reports: HashMap<(NaiveDate, Uuid), DailyReport> = HashMap::new();

        // Helper function to create a new DailyReport
        let create_daily_report = |date: NaiveDate, guard_name: String| DailyReport {
            date,
            guard_name,
            scheduled_start: None,
            scheduled_end: None,
            actual_start: None,
            actual_end: None,
        };

        // Process schedule data
        for sched in schedule.guard_schedule_list {
            let date = sched.start_ts.date_naive();
            let key = (date, sched.guard_uuid);

            let report = daily_reports
                .entry(key)
                .or_insert_with(|| create_daily_report(date, sched.guard_name.clone()));
            report.scheduled_start = Some(sched.start_ts);
            report.scheduled_end = Some(sched.end_ts);
        }

        // Process attendance data
        for att in attendance.guard_attendance_list {
            let date = att.start_ts.date_naive();
            let key = (date, att.guard_uuid);

            let report = daily_reports
                .entry(key)
                .or_insert_with(|| create_daily_report(date, att.guard_name.clone()));
            report.actual_start = Some(att.start_ts);
            report.actual_end = Some(att.end_ts);
        }

        // Convert to sorted vector
        let mut combined_data: Vec<DailyReport> = daily_reports.into_values().collect();
        combined_data.sort_by(|a, b| a.date.cmp(&b.date).then(a.guard_name.cmp(&b.guard_name)));

        // Helper function to format optional time in local timezone
        let format_time = |time: Option<DateTimeUtc>| {
            html! {
                @if let Some(t) = time {
                    (t.with_timezone(&Local).format("%H:%M"))
                } @else {
                    "-"
                }
            }
        };

        let mut dt = bux::component::detail_table(combined_data);
        dt.add_column("Date", |r| html! { (r.date.format("%Y-%m-%d")) });
        dt.add_column("Guard", |r| html! { (r.guard_name) });
        dt.add_column("Scheduled Start Time", |r| format_time(r.scheduled_start));
        dt.add_column("Actual Start Time", |r| format_time(r.actual_start));
        dt.add_column("Scheduled End Time", |r| format_time(r.scheduled_end));
        dt.add_column("Actual End Time", |r| format_time(r.actual_end));

        doc.set_title("Attendance Report");

        doc.add_body(html! {(dt)});
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::http(GET /malawi/guard/{guard_uuid:Uuid}/edit; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::web::malawi::guard::mapper::index::guard_detail;
        use maud::html;

        doc.set_title("Edit Guard");

        let mut form_panel = bux::component::save_cancel_form_panel("Edit Guard", "/guard/list");

        // Get guard details
        let guard = guard_detail::call(
            app,
            identity,
            guard_detail::Input {
                guard_uuid: path.guard_uuid,
            },
        )
        .await?;

        form_panel.set_hidden("guard_uuid", guard.guard_uuid.to_string());

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            (bux::input::text::string::name_label_value("first_name", "First Name", Some(&guard.first_name)))
            (bux::input::text::string::name_label_value("last_name", "Last Name", Some(&guard.last_name)))
            (bux::input::text::string::name_label_value("phone", "Phone:", guard.phone.as_deref()))
            (bux::input::textarea::string::name_label_value("note", "Note:", guard.note.as_deref()))
        ));

        doc.add_body(html! {
            bux-action-panel {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod guard_edit {
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub guard_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub phone: Option<String>,
        pub note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub guard_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.guard_edit(input.guard_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to guard edit".to_string(),
            ));
        }

        let mut dbcx = app.postgres_dbcx().await?;

        // Transaction block
        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $guard_uuid: &input.guard_uuid,
                    $first_name: &input.first_name,
                    $last_name: &input.last_name,
                    $phone: &input.phone,
                    $note: &input.note,
                };
                row = {
                    guard_uuid: Uuid,
                };

                UPDATE
                    appcove_malawi.guard
                SET
                    first_name = $first_name,
                    last_name = $last_name,
                    phone = $phone,
                    note = $note
                WHERE
                    guard_uuid = $guard_uuid
                RETURNING
                    guard_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Response::Output(Output {
            guard_uuid: row.guard_uuid,
            detail_url: crate::ml_guard(row.guard_uuid),
        }))
    }
}

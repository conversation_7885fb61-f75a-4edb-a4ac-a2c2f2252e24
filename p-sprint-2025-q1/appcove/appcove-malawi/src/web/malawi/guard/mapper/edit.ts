import "./edit.mcss";
import "@bux/input/checkbox.mjs";
import "@bux/input/select/nilla.mts";
import "@bux/input/datetime.mts";
import "@bux/input/textarea/string.mts";

import { SE, SEC } from "@granite/lib.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";
import { guard_edit } from "./editλ.mts";
import FormPanel from "@bux/component/form_panel.mts";
import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $guard_uuid = SE($form, "[name=guard_uuid]") as HTMLInputElement;
const $first_name = SEC(BuxInputTextString, $form, "[name=first_name]");
const $last_name = SEC(BuxInputTextString, $form, "[name=last_name]");
const $phone = SEC(BuxInputTextString, $form, "[name=phone]");
const $note = SEC(BuxInputTextareaString, $form, "[name=note]");

new FormPanel({
    $form,
    api: guard_edit.api,
    on_cancel: go_back,

    err: (errors) => {
        $first_name.set_e(errors.first_name);
        $last_name.set_e(errors.last_name);
        $phone.set_e(errors.phone);
        $note.set_e(errors.note);
    },

    get: () => {
        return {
            guard_uuid: $guard_uuid.value,
            first_name: $first_name.value,
            last_name: $last_name.value,
            phone: $phone.value_option,
            note: $note.value_option,
        };
    },

    set: (_value) => {
        // No need to set values for edit form
    },

    out: (output) => {
        go_next((output as any).detail_url);
    },
});

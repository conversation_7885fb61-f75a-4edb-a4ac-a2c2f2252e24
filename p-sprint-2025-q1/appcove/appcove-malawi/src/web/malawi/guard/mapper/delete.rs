#[approck::http(GET /malawi/guard/{guard_uuid:Uuid}/delete; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::web::malawi::guard::mapper::index::guard_detail;
        use maud::html;

        // Get guard details
        let guard = guard_detail::call(
            app,
            identity,
            guard_detail::Input {
                guard_uuid: path.guard_uuid,
            },
        )
        .await?;

        doc.set_title("Delete Guard");

        let title = format!("Delete Guard for {} {}?", guard.first_name, guard.last_name);

        let mut panel = bux::component::delete_cancel_form_panel(&title, &crate::ml_guard_list());
        panel.set_hidden("guard_uuid", path.guard_uuid.to_string());

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            p {
                "This will deactivate the guard for "
                strong { (format!("{} {}", guard.first_name, guard.last_name)) }
                ". "
                "The guard will no longer appear in active guard lists, but "
                "all historical data will be preserved."
            }
            (bux::input::checkbox::name_label_checked(
                "confirm",
                "I understand the above and want to proceed with deleting this guard.",
                false
            ))
        ));

        doc.add_body(html!(
            bux-action-panel {
                (panel)
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod guard_delete {

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub guard_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.guard_delete(input.guard_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to delete guard".to_string(),
            ));
        }

        let dbcx = app.postgres_dbcx().await?;

        // Soft delete by setting active = false
        granite::pg_execute!(
            db = dbcx;
            args = {
                $guard_uuid: &input.guard_uuid,
            };
            UPDATE
                appcove_malawi.guard
            SET
                active = false
            WHERE
                guard_uuid = $guard_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: crate::ml_guard_list(),
            message: "Guard deleted".into(),
        }))
    }
}

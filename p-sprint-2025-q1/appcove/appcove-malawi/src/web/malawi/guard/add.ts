import "./add.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/textarea/string.mts";

import { SE } from "@granite/lib.mts";
import { guard_add } from "./addλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $first_name: BuxInputTextString = SE($form, "[name=first_name]");
const $last_name: BuxInputTextString = SE($form, "[name=last_name]");
const $phone: BuxInputTextString = SE($form, "[name=phone]");
const $note: BuxInputTextareaString = SE($form, "[name=note]");

new FormPanel({
    $form,
    api: guard_add.api,
    on_cancel: go_back,

    err: (errors) => {
        $first_name.set_e(errors.first_name);
        $last_name.set_e(errors.last_name);
        $phone.set_e(errors.phone);
        $note.set_e(errors.note);
    },

    get: () => {
        return {
            first_name: $first_name.value,
            last_name: $last_name.value,
            phone: $phone.value_option,
            note: $note.value_option,
        };
    },

    set: (value) => {
        $first_name.value = value.first_name;
        $last_name.value = value.last_name;
        $phone.value = value.phone;
        $note.value = value.note;
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});

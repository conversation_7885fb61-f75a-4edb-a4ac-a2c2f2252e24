#[approck::http(GET /malawi/guard/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        qs: QueryString,
    ) -> Result<Response> {
        use maud::html;

        // TODO: Figure out how to add a button to the page nav
        doc.page_nav_add_record("Add New Guard", &crate::ml_guard_add());
        doc.set_title("Guards");

        use crate::web::malawi::guard::index::guard_list;

        let active = bux::parse_active_qs(&qs.active, Some(true));
        let output = guard_list::call(
            app,
            identity,
            guard_list::Input {
                keyword: qs.keyword.clone(),
                active,
            },
        )
        .await?;

        // Create a detail table with agencies directly in the constructor
        let mut dt = bux::component::detail_table(output.guard_list);
        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        dt.add_name_column(|a| &a.name);
        dt.add_create_ts(|a| a.create_ts);
        dt.add_active_status_column("Status", |a| a.active);
        dt.add_details_column(|a| crate::ml_guard(a.guard_uuid));

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod guard_list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
        pub active: Option<bool>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub guard_list: Vec<Guard>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Guard {
        pub guard_uuid: Uuid,
        pub create_ts: DateTimeUtc,
        pub name: String,
        pub note: Option<String>,
        pub active: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.guard_list() {
            return_authorization_error!("insufficient permissions to guard list");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $keyword: &input.keyword,
                $active: &input.active,
            };
            row = {
                guard_uuid: Uuid,
                create_ts: DateTimeUtc,
                name: String,
                note: Option<String>,
                active: bool,
            };
            SELECT
                guard_uuid,
                create_ts,
                first_name || " " || last_name AS name,
                note,
                active
            FROM
                appcove_malawi.guard
            WHERE true
                AND ($keyword::text IS NULL OR (first_name ILIKE "%" || $keyword::text || "%" OR last_name ILIKE "%" || $keyword::text || "%"))
                AND ($active::bool IS NULL OR active = $active::bool)
            ORDER BY
                first_name,
                last_name
        )
        .await?;

        Ok(Output {
            guard_list: rows
                .into_iter()
                .map(|r| Guard {
                    guard_uuid: r.guard_uuid,
                    create_ts: r.create_ts,
                    name: r.name,
                    note: r.note,
                    active: r.active,
                })
                .collect(),
        })
    }
}

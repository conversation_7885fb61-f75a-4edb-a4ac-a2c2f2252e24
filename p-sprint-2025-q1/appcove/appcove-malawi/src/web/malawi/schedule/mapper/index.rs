#[approck::http(GET /malawi/schedule/{guard_schedule_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    use chrono::Local;

    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::web::malawi::schedule::mapper::index::guard_schedule_detail;
        use maud::html;

        let guard_schedule = guard_schedule_detail::call(
            app,
            identity,
            guard_schedule_detail::Input {
                guard_schedule_uuid: path.guard_schedule_uuid,
            },
        )
        .await?;

        doc.page_nav_edit_record(
            "Edit Schedule",
            &crate::ml_schedule_edit(guard_schedule.guard_schedule_uuid),
        );
        doc.page_nav_delete_record(
            "Delete Schedule",
            &crate::ml_schedule_delete(guard_schedule.guard_schedule_uuid),
        );

        doc.set_title("Guard Schedule Details");

        let mut table = bux::component::info_table(guard_schedule);
        table.set_heading("Guard Schedule Information");
        table.add_row("Guard:", |u| html! { (u.guard_name) });
        table.add_row(
            "Start Time:",
            |u| html! { (u.start_ts.with_timezone(&Local).format("%Y-%m-%d %H:%M")) },
        );
        table.add_row(
            "End Time:",
            |u| html! { (u.end_ts.with_timezone(&Local).format("%Y-%m-%d %H:%M")) },
        );
        table.add_note_row(|u| u.note.as_deref().unwrap_or(""));
        table.add_active_status_row("Status:", |u| u.active);

        doc.add_body(html!((table)));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod guard_schedule_detail {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub guard_schedule_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub guard_schedule_uuid: Uuid,
        pub guard_uuid: Uuid,
        pub guard_name: String,
        pub create_ts: DateTimeUtc,
        pub start_ts: DateTimeUtc,
        pub end_ts: DateTimeUtc,
        pub note: Option<String>,
        pub active: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.schedule_read(input.guard_schedule_uuid) {
            return_authorization_error!("insufficient permissions to schedule read");
        }

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $guard_schedule_uuid: &input.guard_schedule_uuid,
            };
            row = {
                guard_schedule_uuid: Uuid,
                guard_uuid: Uuid,
                guard_name: String,
                create_ts: DateTimeUtc,
                start_ts: DateTimeUtc,
                end_ts: DateTimeUtc,
                note: Option<String>,
                active: bool,
            };
            SELECT
                s.guard_schedule_uuid,
                s.guard_uuid,
                g.first_name || " " || g.last_name AS guard_name,
                s.create_ts,
                s.start_ts,
                s.end_ts,
                s.note,
                s.active
            FROM
                appcove_malawi.guard_schedule s
                INNER JOIN appcove_malawi.guard g ON g.guard_uuid = s.guard_uuid
            WHERE true
                AND s.guard_schedule_uuid = $guard_schedule_uuid::uuid
        )
        .await?;

        Ok(Output {
            guard_schedule_uuid: row.guard_schedule_uuid,
            guard_uuid: row.guard_uuid,
            guard_name: row.guard_name,
            create_ts: row.create_ts,
            start_ts: row.start_ts,
            end_ts: row.end_ts,
            note: row.note,
            active: row.active,
        })
    }
}

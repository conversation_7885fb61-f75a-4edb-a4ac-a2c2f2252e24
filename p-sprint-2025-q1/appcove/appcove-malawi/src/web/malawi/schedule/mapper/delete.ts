import "./delete.mcss";
import "@bux/input/select/nilla.mts";
import "@bux/input/datetime.mts";
import "@bux/input/textarea/string.mts";
import "@bux/input/checkbox.mts";

import { SE } from "@granite/lib.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";
import { schedule_delete } from "./deleteλ.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $guard_schedule_uuid = SE($form, "[name=guard_schedule_uuid]") as HTMLInputElement;
const $confirm = SE($form, "input[type=checkbox]") as HTMLInputElement;
const $submitButton = SE($form, "button[type=submit]") as HTMLButtonElement;

// Initially disable submit button
$submitButton.disabled = true;

// Enable/disable submit button based on checkbox state
$confirm.addEventListener("change", () => {
    $submitButton.disabled = !$confirm.checked;
});

new FormPanel({
    $form,
    api: schedule_delete.api,
    on_cancel: go_back,

    err: (errors) => {
        // Handle any validation errors if needed
        console.error("Delete Schedule errors:", errors);
    },

    get: () => {
        return {
            guard_schedule_uuid: $guard_schedule_uuid.value,
        };
    },

    set: (_value) => {
        // No need to set values for delete form
    },

    out: (output) => {
        go_next((output as any).detail_url);
    },
});

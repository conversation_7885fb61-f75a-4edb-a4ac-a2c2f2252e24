#[approck::http(GET /malawi/schedule/{guard_schedule_uuid:Uuid}/delete; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::web::malawi::schedule::mapper::index::guard_schedule_detail;
        use maud::html;

        // Get schedule details
        let schedule = guard_schedule_detail::call(
            app,
            identity,
            guard_schedule_detail::Input {
                guard_schedule_uuid: path.guard_schedule_uuid,
            },
        )
        .await?;

        doc.set_title("Delete Schedule");

        let title = format!("Delete Schedule for {}?", schedule.guard_name);

        let mut panel =
            bux::component::delete_cancel_form_panel(&title, &crate::ml_schedule_list());
        panel.set_hidden("guard_schedule_uuid", path.guard_schedule_uuid.to_string());

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            p {
                "This will deactivate the schedule for "
                strong { (schedule.guard_name) }
                " from " (schedule.start_ts.format("%Y-%m-%d %H:%M:%S"))
                " to " (schedule.end_ts.format("%Y-%m-%d %H:%M:%S")) ". "
                "The schedule will no longer appear in active schedule lists, but all historical data will be preserved."
            }
            (bux::input::checkbox::name_label_checked("confirm", "I understand the above and want to proceed with deleting this schedule.", false))
        ));

        doc.add_body(html!(
            bux-action-panel {
                (panel)
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod schedule_delete {
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub guard_schedule_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.schedule_delete(input.guard_schedule_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to delete schedule".to_string(),
            ));
        }

        let dbcx = app.postgres_dbcx().await?;

        // Soft delete by setting active = false
        granite::pg_execute!(
            db = dbcx;
            args = {
                $guard_schedule_uuid: &input.guard_schedule_uuid,
            };
            UPDATE
                appcove_malawi.guard_schedule
            SET
                active = false
            WHERE
                guard_schedule_uuid = $guard_schedule_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: crate::ml_schedule_list(),
            message: "Schedule deleted".into(),
        }))
    }
}

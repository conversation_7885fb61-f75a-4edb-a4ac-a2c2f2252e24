#[approck::http(GET /malawi/schedule/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    use chrono::Local;

    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        qs: QueryString,
    ) -> Result<Response> {
        use maud::html;

        doc.page_nav_add_record("Add New Schedule", &crate::ml_schedule_add());
        doc.set_title("Schedule");

        use crate::web::malawi::schedule::index::guard_schedule_list;

        let active = bux::parse_active_qs(&qs.active, Some(true));
        let output = guard_schedule_list::call(
            app,
            identity,
            guard_schedule_list::Input {
                keyword: qs.keyword.clone(),
                active,
            },
        )
        .await?;

        let mut dt = bux::component::detail_table(output.guard_schedule_list);
        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        dt.add_column("Guard", |a| html! { (a.guard_name) });
        dt.add_column(
            "Start Time",
            |a| html! { (a.start_ts.with_timezone(&Local).format("%Y-%m-%d %H:%M")) },
        );
        dt.add_column(
            "End Time",
            |a| html! { (a.end_ts.with_timezone(&Local).format("%Y-%m-%d %H:%M")) },
        );
        dt.add_active_status_column("Status", |a| a.active);
        dt.add_details_column(|a| crate::ml_guard_schedule(a.guard_schedule_uuid));

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod guard_schedule_list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
        pub active: Option<bool>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub guard_schedule_list: Vec<GuardSchedule>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct GuardSchedule {
        pub guard_schedule_uuid: Uuid,
        pub guard_uuid: Uuid,
        pub guard_name: String,
        pub create_ts: DateTimeUtc,
        pub start_ts: DateTimeUtc,
        pub end_ts: DateTimeUtc,
        pub note: Option<String>,
        pub active: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.schedule_list() {
            return_authorization_error!("insufficient permissions to schedule list");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $keyword: &input.keyword,
                $active: &input.active,
            };
            row = {
                guard_schedule_uuid: Uuid,
                guard_uuid: Uuid,
                guard_name: String,
                create_ts: DateTimeUtc,
                start_ts: DateTimeUtc,
                end_ts: DateTimeUtc,
                note: Option<String>,
                active: bool,
            };
            SELECT
                s.guard_schedule_uuid,
                s.guard_uuid,
                g.first_name || " " || g.last_name AS guard_name,
                s.create_ts,
                s.start_ts,
                s.end_ts,
                s.note,
                s.active
            FROM
                appcove_malawi.guard_schedule s
                INNER JOIN appcove_malawi.guard g ON g.guard_uuid = s.guard_uuid
            WHERE true
                AND ($keyword::text IS NULL OR g.first_name || " " || g.last_name ILIKE "%" || $keyword::text || "%")
                AND ($active::bool IS NULL OR s.active = $active::bool)
            ORDER BY
                start_ts
        )
        .await?;

        Ok(Output {
            guard_schedule_list: rows
                .into_iter()
                .map(|r| GuardSchedule {
                    guard_schedule_uuid: r.guard_schedule_uuid,
                    guard_uuid: r.guard_uuid,
                    guard_name: r.guard_name,
                    create_ts: r.create_ts,
                    start_ts: r.start_ts,
                    end_ts: r.end_ts,
                    note: r.note,
                    active: r.active,
                })
                .collect(),
        })
    }
}

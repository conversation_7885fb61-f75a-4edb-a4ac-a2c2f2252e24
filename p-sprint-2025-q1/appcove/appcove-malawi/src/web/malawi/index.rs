#[approck::http(GET /malawi/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        qs: QueryString,
    ) -> Result<Response> {
        use maud::html;
        use chrono::Local;

        doc.set_title("Malawi Dashboard");

        // Parse active filter
        let active = bux::parse_active_qs(&qs.active, Some(true));

        // Get recent guards
        let guards_output = crate::web::malawi::guard::index::guard_list::call(
            app,
            identity,
            crate::web::malawi::guard::index::guard_list::Input {
                keyword: qs.keyword.clone(),
                active,
            },
        )
        .await?;

        // Get recent schedules
        let schedules_output = crate::web::malawi::schedule::index::guard_schedule_list::call(
            app,
            identity,
            crate::web::malawi::schedule::index::guard_schedule_list::Input {
                keyword: qs.keyword.clone(),
                active,
            },
        )
        .await?;

        // Get recent attendance
        let attendance_output = crate::web::malawi::attendance::index::guard_attendance_list::call(
            app,
            identity,
            crate::web::malawi::attendance::index::guard_attendance_list::Input {
                keyword: qs.keyword.clone(),
                active,
            },
        )
        .await?;

        // Create guards table
        let mut guards_dt = bux::component::detail_table(guards_output.guard_list.into_iter().take(10).collect::<Vec<_>>());
        guards_dt.set_heading("Recent Guards");
        guards_dt.add_keyword_filter(qs.keyword.as_deref());
        guards_dt.add_active_filter(active);

        guards_dt.add_link_column(
            "Name",
            |g| crate::ml_guard(g.guard_uuid),
            |g| g.name.clone(),
        );
        guards_dt.add_column("Note", |g| html! { (g.note.as_deref().unwrap_or("")) });
        guards_dt.add_create_ts(|g| g.create_ts);
        guards_dt.add_active_status_column("Status", |g| g.active);

        // Create schedules table
        let mut schedules_dt = bux::component::detail_table(schedules_output.guard_schedule_list.into_iter().take(10).collect::<Vec<_>>());
        schedules_dt.set_heading("Recent Schedules");
        schedules_dt.add_keyword_filter(qs.keyword.as_deref());
        schedules_dt.add_active_filter(active);

        schedules_dt.add_column("Guard", |s| html! { (s.guard_name) });
        schedules_dt.add_column(
            "Start Time",
            |s| html! { (s.start_ts.with_timezone(&Local).format("%Y-%m-%d %H:%M")) },
        );
        schedules_dt.add_column(
            "End Time",
            |s| html! { (s.end_ts.with_timezone(&Local).format("%Y-%m-%d %H:%M")) },
        );
        schedules_dt.add_active_status_column("Status", |s| s.active);
        schedules_dt.add_details_column(|s| crate::ml_guard_schedule(s.guard_schedule_uuid));

        // Create attendance table
        let mut attendance_dt = bux::component::detail_table(attendance_output.guard_attendance_list.into_iter().take(10).collect::<Vec<_>>());
        attendance_dt.set_heading("Recent Attendance");
        attendance_dt.add_keyword_filter(qs.keyword.as_deref());
        attendance_dt.add_active_filter(active);

        attendance_dt.add_column("Guard", |a| html! { (a.guard_name) });
        attendance_dt.add_column(
            "Start Time",
            |a| html! {
                @if let Some(start_ts) = a.start_ts {
                    (start_ts.with_timezone(&Local).format("%Y-%m-%d %H:%M"))
                } @else {
                    "Not started"
                }
            },
        );
        attendance_dt.add_column(
            "End Time",
            |a| html! {
                @if let Some(end_ts) = a.end_ts {
                    (end_ts.with_timezone(&Local).format("%Y-%m-%d %H:%M"))
                } @else {
                    "In progress"
                }
            },
        );
        attendance_dt.add_active_status_column("Status", |a| a.active);
        attendance_dt.add_details_column(|a| crate::ml_guard_attendance(a.guard_attendance_uuid));

        doc.add_body(html! {
            div.container {
                h1 { "Malawi Security Management Dashboard" }
                p { "Manage guards, schedules, and attendance records for security operations." }

                hr;

                // Quick stats
                div.row.mb-4 {
                    div.col-md-4 {
                        div.card.text-center {
                            div.card-body {
                                h5.card-title { "Active Guards" }
                                h2.text-primary { (guards_output.guard_list.iter().filter(|g| g.active).count()) }
                            }
                        }
                    }
                    div.col-md-4 {
                        div.card.text-center {
                            div.card-body {
                                h5.card-title { "Active Schedules" }
                                h2.text-success { (schedules_output.guard_schedule_list.iter().filter(|s| s.active).count()) }
                            }
                        }
                    }
                    div.col-md-4 {
                        div.card.text-center {
                            div.card-body {
                                h5.card-title { "Attendance Records" }
                                h2.text-info { (attendance_output.guard_attendance_list.len()) }
                            }
                        }
                    }
                }

                // Quick actions
                div.row.mb-4 {
                    div.col-12 {
                        h3 { "Quick Actions" }
                        div.btn-group.mb-3 role="group" {
                            a.btn.btn-primary href="/malawi/guard/" { "View All Guards" }
                            a.btn.btn-success href="/malawi/schedule/" { "View All Schedules" }
                            a.btn.btn-info href="/malawi/attendance/" { "View All Attendance" }
                        }
                    }
                }

                // Tables
                (guards_dt)
                (schedules_dt)
                (attendance_dt)
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}

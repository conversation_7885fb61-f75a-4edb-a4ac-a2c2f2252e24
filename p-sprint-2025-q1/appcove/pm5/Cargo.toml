[package]
name = "pm5"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.approck.app]
port = 3015
docmap.auth-fence.DocumentPublic = "Document"
extends = ["pm5-zero", "pm5-public", "approck", "bux", "granite", "auth-fence", "appstruct"]

[dependencies]
appstruct = { workspace = true }
pm5-zero = { path = "../pm5-zero" }
pm5-public = { path = "../pm5-public" }
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-redis = { workspace = true }
approck-postgres = { workspace = true }
auth-fence = { workspace = true }


clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
toml = { workspace = true }
chrono = { workspace = true, features = ["serde"] }



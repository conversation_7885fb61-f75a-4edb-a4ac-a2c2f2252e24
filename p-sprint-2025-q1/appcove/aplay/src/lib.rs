// ----------------------------------------------------------------------------------------------
// Imports
pub mod extends;
pub mod web;

#[path = "libλ.rs"]
pub mod libλ;

// ----------------------------------------------------------------------------------------------
// Create application

#[derive(serde::Deserialize)]
pub struct AppConfig {
    pub webserver: approck::server::ModuleConfig,
}

pub struct AppStruct {
    pub webserver: approck::server::Mo<PERSON>le,
}

#[derive(Debug)]
pub enum IdentityStruct {
    Anonymous,
}

pub use crate::web::Document::Document as DocumentStruct;

// ----------------------------------------------------------------------------------------------
// This app is also used for web and api requests

pub trait App: approck::App + approck::server::App {}
pub trait Identity: approck::Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}

pub trait Document: bux::document::Base + bux::document::Cliffy {}

// ----------------------------------------------------------------------------------------------
// Implement application

impl approck::App for AppStruct {
    type Config = AppConfig;
    type Identity = IdentityStruct;

    fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            webserver: approck::server::Module::new(config.webserver)?,
        })
    }

    async fn init(&self) -> granite::Result<()> {
        approck::Module::init(&self.webserver).await?;
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {crate_name}");
        Ok(())
    }

    async fn auth(&self, _req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        Ok(IdentityStruct::Anonymous)
    }
}

impl approck::Identity for IdentityStruct {}

// ----------------------------------------------------------------------------------------------
// Implement traits

impl App for AppStruct {}
impl Identity for IdentityStruct {}

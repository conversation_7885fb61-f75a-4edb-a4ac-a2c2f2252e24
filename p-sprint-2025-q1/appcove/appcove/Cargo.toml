[package]
name = "appcove"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.approck.app]
port = 3008
docmap.auth-fence.DocumentPublic = "Document"
extends = ["appcove-zero", "appcove-public", "appcove-malawi", "approck", "bux", "granite", "auth-fence", "appstruct"]

[dependencies]
appcove-zero = { path = "../appcove-zero" }
appcove-public = { path = "../appcove-public" }
appcove-malawi = { path = "../appcove-malawi" }
approck = { workspace = true }
appstruct = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-redis = { workspace = true }
approck-postgres = { workspace = true }
auth-fence = { workspace = true }


clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
toml = { workspace = true }
chrono = { workspace = true, features = ["serde"] }



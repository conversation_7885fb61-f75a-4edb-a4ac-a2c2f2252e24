use bux::html;

bux::document! {

    pub struct Document {}

    impl Document {
        pub fn new(
            _app: &'static crate::AppStruct,
            identity: &crate::IdentityStruct,
            req: &approck::server::Request,
        ) -> Self {
            // trait Nav2 must be in scope for set_identity() and nav2_menu_add()
            use bux::document::{Nav1, Nav2};

            println!("Request {:?}", req.path());

            let mut this = Self {
                ..Default::default()
            };

            // Base setup

            // Add Nav1 menu links
            this.add_nav1_menu_link("App Directory", "/", "");
            this.add_nav1_menu_link("Bux Documentation", "/doc", "");

            // Nav2 setup
            this.set_identity(identity);

            this
        }
    }


    impl bux::document::Base for Document {
        fn render_body(&self) -> maud::Markup {
            use bux::document::{Base, Nav1, Nav2};
            use maud::{html, PreEscaped};
            html!(
                layout-wrapper-outer {
                    layout-wrapper-inner {
                        header-bar.disabled id="header-bar" {}
                        nav-wrapper {
                            content-container {
                                nav-header id="horizontal-nav-header" {
                                    a class="home-link" href="/" { i class="fa fa-home" {} }
                                }
                                (Nav1::render_nav1(self))
                                (Nav2::render(self))
                            }
                        }
                        content-container {
                            (Base::render_body_inner(self))
                        }
                    }
                    footer {
                        p id="footer-copyright" {
                            small {
                                (PreEscaped("&copy;"))
                                "AppCove 2024"
                            }
                        }
                    }
                }

            )
        }
    }
    impl bux::document::Nav1 for Document {}
    impl bux::document::Nav2 for Document {}
    impl bux::document::FooterSocial for Document {}
    impl bux::document::FooterLinks for Document {}
    impl bux::document::PageNav for Document {}

}

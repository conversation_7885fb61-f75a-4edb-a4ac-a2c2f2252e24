[package]
name = "candoc"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.approck.app]
port = 8000
extends = ["bux", "candoc-bux", "approck", "granite"]

[dependencies]
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
candoc-bux = { path = "../candoc-bux" }


clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
toml = { workspace = true }

#[approck::http(GET /docs/layout/column; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.add_body(html!(
            panel {
                content {
                    grid-12 {
                        cell-3 {
                            (super::super::super::render_docs_nav())
                        }
                        cell-6 {
                            h1 { "Columns" }
                            p { "To create multi-column layouts, you can use grid tags. These define responsive column layouts for their direct child elements, stacking vertically on smaller screens (below 992px). The four available grid tags are: " code { "grid-2" } ", " code { "grid-3" } ", " code { "grid-4" } ", and " code { "grid-12" } "." }

                            h2 { "grid-2" }
                            p { "This grid tag creates two equal-width columns." }

                            p { b { "Example" } }
                            panel {
                                content {
                                    grid-2 {
                                        panel style="margin-bottom: 0;" {
                                            content {}
                                        }
                                        panel style="margin-bottom: 0;" {
                                            content {}
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "grid-2 {\n"
                                            "    panel {...}\n"
                                            "    panel {...}\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            h2 { "grid-3" }
                            p { "This grid tag creates three equal-width columns." }

                            p { b { "Example" } }
                            panel {
                                content {
                                    grid-3 {
                                        panel style="margin-bottom: 0;" {
                                            content {}
                                        }
                                        panel style="margin-bottom: 0;" {
                                            content {}
                                        }
                                        panel style="margin-bottom: 0;" {
                                            content {}
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "grid-3 {\n"
                                            "    panel {...}\n"
                                            "    panel {...}\n"
                                            "    panel {...}\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            h2 { "grid-4" }
                            p { "This grid tag creates four equal-width columns." }

                            p { b { "Example" } }
                            panel {
                                content {
                                    grid-4 {
                                        panel style="margin-bottom: 0;" {
                                            content {}
                                        }
                                        panel style="margin-bottom: 0;" {
                                            content {}
                                        }
                                        panel style="margin-bottom: 0;" {
                                            content {}
                                        }
                                        panel style="margin-bottom: 0;" {
                                            content {}
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "grid-4 {\n"
                                            "    panel {...}\n"
                                            "    panel {...}\n"
                                            "    panel {...}\n"
                                            "    panel {...}\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            h2 { "grid-12" }
                            p { "This grid tag creates twelve equal-width columns. It's meant to be used with " code { "cell-*" } " tags to give you precise control over how many columns each child element spans." }
                            p { "The available " code { "cell-*" } " tags range from " code { "cell-2" } " to " code { "cell-10" } "." }

                            p { b { "Examples" } }
                            panel {
                                content {
                                    grid-12 {
                                        cell-4 {
                                            panel style="margin-bottom: 0;" {
                                                content {}
                                            }
                                        }
                                        cell-8 {
                                            panel style="margin-bottom: 0;" {
                                                content {}
                                            }
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "grid-12 {\n"
                                            "    cell-4 {\n"
                                            "        panel {...}\n"
                                            "    }\n"
                                            "    cell-8 {\n"
                                            "        panel {...}\n"
                                            "    }\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            panel {
                                content {
                                    grid-12 {
                                        cell-3 {
                                            panel style="margin-bottom: 0;" {
                                                content {}
                                            }
                                        }
                                        cell-6 {
                                            panel style="margin-bottom: 0;" {
                                                content {}
                                            }
                                        }
                                        cell-3 {
                                            panel style="margin-bottom: 0;" {
                                                content {}
                                            }
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "grid-12 {\n"
                                            "    cell-3 {\n"
                                            "        panel {...}\n"
                                            "    }\n"
                                            "    cell-6 {\n"
                                            "        panel {...}\n"
                                            "    }\n"
                                            "    cell-3 {\n"
                                            "        panel {...}\n"
                                            "    }\n"
                                            "}"
                                        }
                                    }
                                }
                            }
                        }
                        cell-3 {
                            h3 { "On this page" }
                            hr;
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

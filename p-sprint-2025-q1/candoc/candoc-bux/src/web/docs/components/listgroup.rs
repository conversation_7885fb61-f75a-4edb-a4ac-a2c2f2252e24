#[approck::http(GET /docs/components/listgroup; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.add_body(html!(
            panel {
                content {
                    grid-12 {
                        cell-3 {
                            (super::super::super::render_docs_nav())
                        }
                        cell-6 {
                            h1 { "List group" }
                            p { "A stylized version of a standard HTML list." }

                            h2 { "Unordered list" }
                            p { "Use the " code { "list-group" } " tag to create an unordered list with enhanced styling." }
                            panel {
                                content {
                                    grid-2 {
                                        list-group {
                                            ul {
                                                li { "First item" }
                                                li { "Second item" }
                                                li { "Third item" }
                                            }
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "list-group {\n"
                                            "    ul {\n"
                                            "        li { \"First item\" }\n"
                                            "        li { \"Second item\" }\n"
                                            "        li { \"Third item\" }\n"
                                            "    }\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            h2 { "Ordered list" }
                            p { "Use the " code { "list-group" } " tag to create an ordered list with enhanced styling." }

                            panel {
                                content {
                                    grid-2 {
                                        list-group {
                                            ol {
                                                li { "First item" }
                                                li { "Second item" }
                                                li { "Third item" }
                                            }
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "list-group {\n"
                                            "    ol {\n"
                                            "        li { \"First item\" }\n"
                                            "        li { \"Second item\" }\n"
                                            "        li { \"Third item\" }\n"
                                            "    }\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            p { b { "Note:" } " " code { "list-style-position: inside" } " moves the " code { "::marker" } " (number, bullet, etc.) into the list item's content box instead of rendering it in a separate box. This allows the " code { "::marker" } " to be affected by styles applied to the " code { "<li>" } ", such as border and padding. While this was intentionally done in order to accommodate enhanced styling for ordered list groups, it does alter the list's visual rhythm and layout. Please see the comparison below." }

                            panel {
                                content {
                                    list-group {
                                        ol {
                                            li { "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque suscipit lacus vitae tortor varius, eu dictum neque aliquam." }
                                            li { "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque suscipit lacus vitae tortor varius, eu dictum neque aliquam." }
                                            li { "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque suscipit lacus vitae tortor varius, eu dictum neque aliquam." }
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "list-group {\n"
                                            "    ol {\n"
                                            "        li {...}\n"
                                            "        li {...}\n"
                                            "        li {...}\n"
                                            "    }\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            panel {
                                content {
                                    ol style="margin-bottom: 0;" {
                                        li { "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque suscipit lacus vitae tortor varius, eu dictum neque aliquam." }
                                        li { "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque suscipit lacus vitae tortor varius, eu dictum neque aliquam." }
                                        li { "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque suscipit lacus vitae tortor varius, eu dictum neque aliquam." }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "ol {\n"
                                            "    li {...}\n"
                                            "    li {...}\n"
                                            "    li {...}\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            h2 { "Links" }
                            p { "Use " code { "<a>" } " to create " em { "actionable" } " list group items with hover, disabled, and active states by adding " code { ".action" } ". This class makes each list item focusable and clickable." }

                            panel {
                                content {
                                    grid-2 {
                                        list-group.action {
                                            ul {
                                                li { a.active href="" { "First item" } }
                                                li { a href="" { "Second item" } }
                                                li { a href="" { "Third item" } }
                                                li { a.disabled href="" { "Fourth item" } }
                                            }
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "list-group.action {\n"
                                            "    ul {\n"
                                            "        li {\n"
                                            "            a.active href=\"\" { \"First item\" }\n"
                                            "        }\n"
                                            "        li {\n"
                                            "            a href=\"\" { \"Second item\" }\n"
                                            "        }\n"
                                            "        li {\n"
                                            "            a href=\"\" { \"Third item\" }\n"
                                            "        }\n"
                                            "        li {\n"
                                            "            a.disabled href=\"\" { \"Fourth item\" }\n"
                                            "        }\n"
                                            "    }\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            h3 { "Mixed content" }
                            p { "You can also nest mixed content, including block-level elements, inside each " code { "<a>" } " to provide context or visual enhancements." }
                            panel {
                                content {
                                    list-group.action {
                                        ul {
                                            li { a.active href="" { 
                                                h5 { "List group heading" }
                                                p  style="margin-bottom: 0;" { "Some placeholder content in a paragraph." } 
                                                }
                                            }
                                            li { a href="" { "Second item" } }
                                            li { a href="" { "Third item" } }
                                            li { a href="" { "Fourth item" } }
                                        }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "list-group.action {\n"
                                            "    ul {\n"
                                            "        li {\n"
                                            "            a.active href=\"\" { \"First item\" }\n"
                                            "        }\n"
                                            "        li {\n"
                                            "            a href=\"\" { \"Second item\" }\n"
                                            "        }\n"
                                            "        li {\n"
                                            "            a href=\"\" { \"Third item\" }\n"
                                            "        }\n"
                                            "        li {\n"
                                            "            a.disabled href=\"\" { \"Fourth item\" }\n"
                                            "        }\n"
                                            "    }\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            h2 { "Flush" }
                            p { "Add " code { ".flush" } " to remove some borders and rounded corners to render list group items edge-to-edge in a parent container (e.g., panels)." }

                            panel {
                                list-group.flush {
                                    ol {
                                        li { "First item" }
                                        li { "Second item" }
                                        li { "Third item" }
                                    }
                                }
                                footer {
                                    pre {
                                        code {
                                            "list-group.flush {\n"
                                            "    ol {\n"
                                            "        li { \"First item\" }\n"
                                            "        li { \"Second item\" }\n"
                                            "        li { \"Third item\" }\n"
                                            "    }\n"
                                            "}"
                                        }
                                    }
                                }
                            }
                        }
                        cell-3 {
                            h3 { "On this page" }
                            hr;
                            ul {
                                li {
                                    a href="" { "Unordered list" }
                                }
                                li {
                                    a href="" { "Ordered list" }
                                }
                                li {
                                    a href="" { "Links" }
                                }
                                li {
                                    a href="" { "Buttons" }
                                }
                                li {
                                    a href="" { "Flush" }
                                }
                                li {
                                    a href="" { "Contextual classes" }
                                }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

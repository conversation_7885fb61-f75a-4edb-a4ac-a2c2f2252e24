#[approck::http(GET /docs/components/panel; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        let mut panel = bux::component::delete_cancel_form_panel("Delete User", "/");

        panel.add_body(maud::html!(
            p style="margin-bottom: 0; text-align: center;" { "Are you sure you want to delete this user?" }
        ));

        doc.add_body(html!(
            panel {
                content {
                    grid-12 {
                        cell-3 {
                            (super::super::super::render_docs_nav())
                        }
                        cell-6 {
                            h1 { "Panels" }
                            p { "Panels are flexible content containers with support for a variety of content. They have no fixed width, so they'll naturally fit the width of their parent container unless specified otherwise." }

                            h2 { "Body" }
                            p { "The building block of a panel is the " code { "content" } " element. It creates a padded section within the panel and is meant for the main content of the panel." }

                            panel {
                                content {
                                    panel style="margin-bottom: 0;" {
                                        content { "This is some text within the content area." }
                                    }
                                }
                                footer style="text-align: left;" {
                                    pre {
                                        code {
                                            "panel {\n"
                                            "    content { \"This is some text within the content area.\" }\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            h2 { "Header" }
                            p { "Add an optional header to the panel." }

                            panel {
                                content {
                                    panel style="margin-bottom: 0;" {
                                        header { "Featured" }
                                        content { "This is some text within the content area." }
                                    }
                                }
                                footer style="text-align: left;" {
                                    pre {
                                        code {
                                            "panel {\n"
                                            "    header { \"Featured\" }\n"
                                            "    content { \"This is some text within the content area.\" }\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            p { "Panel headers can be styled by nesting an " code { "<h5>" } " tag."}
                            panel {
                                content {
                                    panel style="margin-bottom: 0;" {
                                        header {
                                            h5 { "Featured" }
                                        }
                                        content { "This is some text within the content area." }
                                    }
                                }
                                footer style="text-align: left;" {
                                    pre {
                                        code {
                                            "panel {\n"
                                            "    header {\n"
                                            "        h5 { \"Featured\" }\n"
                                            "    }\n"
                                            "    content { \"This is some text within the content area.\" }\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            h2 { "Footer" }
                            p { "Add an optional footer to the panel." }

                            panel {
                                content {
                                    panel style="margin-bottom: 0;" {
                                        header {
                                            h5 { "Featured" }
                                        }
                                        content { "This is some text within the content area." }
                                        footer { (bux::button::link::label_class("Go Somewhere", "/", "primary")) }
                                    }
                                }
                                footer style="text-align: left;" {
                                    pre {
                                        code {
                                            "panel {\n"
                                            "    header {\n"
                                            "        h5 { \"Featured\" }\n"
                                            "    }\n"
                                            "    content { \"This is some text within the content area.\" }\n"
                                            "    footer {\n" 
                                            "        a.btn.primary href=\"\" { \"Go Somewhere\" }\n"
                                            "    }\n"
                                            "}"
                                        }
                                    }
                                }
                            }

                            h2 { "Standardized panels" }
                            p { "Standardized panels help maintain consistency across the UI and reduce repetitive markup." }

                            h3 { "form_panel" }
                            p { code { "/lib/bux/src/component/form_panel" } }
                            p { "This component consists of a shared rendering structure with several different helper functions. Current support includes the following use cases:" }

                            ul {
                                li {
                                    code { "save_cancel_form_panel" } 
                                    " - for editing existing items"
                                }
                                li {
                                    code { "add_cancel_panel" } 
                                    " - for creating new items"
                                }
                                li {
                                    code { "delete_cancel_panel" } 
                                    " - for deleting items"
                                }
                            }

                            p { b { "Example" } }
                            panel {
                                content style="padding-bottom: 0;" {
                                    (panel)
                                }
                                footer  style="text-align: left;" {
                                    pre {
                                        code {
                                            "let mut panel = bux::component::delete_cancel_panel(\"Delete User\", \"/\");\n\n"
                                            "panel.add_body(maud::html!(\n"
                                            "    p { \"Are you sure you want to delete this user?\" }\n"
                                            "));\n\n"
                                            "doc.add_body(html!(\n"
                                            "    (panel)\n"
                                            "));"
                                        }
                                    }
                                }
                            }

                            h3 { "set_status_panel" }

                            p { b { "Example" }}
                            panel {
                                content {

                                }
                                footer style="text-align: left;" {
                                    pre {
                                        code {

                                        }
                                    }
                                }
                            }
                        }
                        cell-3 {
                            h3 { "On this page" }
                            hr;
                            ul {
                                li {
                                    a href="" { "Body" }
                                }
                                li {
                                    a href="" { "Header" }
                                }
                                li {
                                    a href="" { "Footer" }
                                }
                                li {
                                    a href="" { "Standardized panels" }
                                }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

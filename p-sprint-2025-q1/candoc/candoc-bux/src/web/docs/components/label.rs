#[approck::http(GET /docs/components/label; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::{PreEscaped, html};

        doc.add_body(html!(
            panel {
                content {
                    grid-12 {
                        cell-3 {
                            (super::super::super::render_docs_nav())
                        }
                        cell-6 {
                            h1 { "Labels" }
                            p { "Use labels to visually highlight or categorize text." }

                            h2 { "Examples" }
                            p {
                                "You must use one of the provided classes in order for " 
                                code { "label-tag" } 
                                " to be styled correctly. These include "
                                code { "default" }
                                ", "
                                code { "primary" }
                                ", "
                                code { "success" }
                                ", "
                                code { "info" }
                                ", "
                                code { "warning" }
                                ", and "
                                code { "danger" }
                                "."
                            }

                            panel {
                                content {
                                    label-tag.default { "Default" }
                                    (PreEscaped("&nbsp;"))
                                    label-tag.primary { "Primary" }
                                    (PreEscaped("&nbsp;"))
                                    label-tag.success { "Success" }
                                    (PreEscaped("&nbsp;"))
                                    label-tag.info { "Info" }
                                    (PreEscaped("&nbsp;"))
                                    label-tag.warning { "Warning" }
                                    (PreEscaped("&nbsp;"))
                                    label-tag.danger { "Danger" }
                                }
                                footer {
                                    pre {
                                        code {
                                            "label-tag.default { \"Default\" }\n"
                                            "label-tag.primary { \"Primary\" }\n"
                                            "label-tag.success { \"Success\" }\n"
                                            "label-tag.info { \"Info\" }\n"
                                            "label-tag.warning { \"Warning\" }\n"
                                            "label-tag.danger { \"Danger\" }\n"
                                        }
                                    }
                                }
                            }
                            h3 { "Conveying meaning to assistive technologies" }
                            p { "Since color is a visual indicator, ensure that meaning conveyed by color is clear from the content itself. Otherwise, provide additional context through alternative means, such as visually hidden text or the use of ARIA attributes." }

                        }
                        cell-3 {

                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

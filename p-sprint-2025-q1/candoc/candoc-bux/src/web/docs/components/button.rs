#[approck::http(GET /docs/components/button; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::{PreEscaped, html};

        doc.add_body(html!(
            panel {
                content {
                    grid-12 {
                        cell-3 {
                            (super::super::super::render_docs_nav())
                        }
                        cell-6 {
                            h1 { "Buttons" }
                            p { "Buttons should be used when the primary intent is to trigger an action  or submit information." }

                            panel {
                                content {
                                    button type="button" {"Default"}
                                }
                                footer {
                                    pre {
                                        code {
                                            "<!-- Default button -->\n"
                                            "button type=\"button\" {\"Default\"}"
                                        }
                                    }
                                }
                            }

                            p {
                                b { "Note:" }
                                " It is best practice to include the "
                                code { "type" }
                                " attribute when using the "
                                code { "button" }
                                " element. If you don't specify the "
                                code { "type" }
                                " , browsers will often default to "
                                code { "type=\"submit\""}
                                " , which can lead to potential conflicts and unintended form submissions."
                            }

                            h2 { "Contextual classes" }
                            p {"There are six contextual classes, each serving its own semantic purpose."}

                            panel {
                                content {
                                    button.primary type="button" {"Primary"}
                                    (PreEscaped("&nbsp;"))
                                    button.secondary type="button" {"Secondary"}
                                    (PreEscaped("&nbsp;"))
                                    button.success type="button" {"Success"}
                                    (PreEscaped("&nbsp;"))
                                    button.danger type="button" {"Danger"}
                                    (PreEscaped("&nbsp;"))
                                    button.warning type="button" {"Warning"}
                                    (PreEscaped("&nbsp;"))
                                    button.info type="button" {"Info"}
                                }
                                footer {
                                    pre {
                                        code {
                                            "<!-- Identifies the primary action in a set of buttons -->\n"
                                            "button.primary type=\"button\" {\"Primary\"}\n\n"
                                            "<!-- Represents a secondary action that is still important, but not the focus -->\n"
                                            "button.secondary type=\"button\" {\"Secondary\"}\n\n"
                                            "<!-- Indicates a successful or positive action -->\n"
                                            "button.success type=\"button\" {\"Success\"}\n\n"
                                            "<!-- Indicates a dangerous or potentially negative action -->\n"
                                            "button.danger type=\"button\" {\"Danger\"}\n\n"
                                            "<!-- Indicates caution should be taken with this action -->\n"
                                            "button.warning type=\"button\" {\"Warning\"}\n\n"
                                            "<!-- Contextual button for informational alert messages -->\n"
                                            "button.info type=\"button\" {\"Info\"}"
                                        }
                                    }
                                }
                            }
                            h3 { "Conveying meaning to assistive technologies" }
                            p { "Since color is a visual indicator, ensure that meaning conveyed by color is clear from the content itself. Otherwise, provide additional context through alternative means, such as visually hidden text or the use of ARIA attributes." }

                            h2 { "Button tags" }
                            p { "Use the " code { "btn" } " class to make an anchor tag look like a default button." }
                            panel {
                                content {
                                    a.btn href="" {"Button Tag"}
                                }
                                footer {
                                    pre {
                                        code {
                                            "a.btn href=\"\" {\"Button Tag\"}"
                                        }
                                    }
                                }
                            }

                            p { "This can also be combined with one of the six contextual classes." }
                            panel {
                                content {
                                    a.btn.primary href="" type="button" {"Primary"}
                                    (PreEscaped("&nbsp;"))
                                    a.btn.secondary href="" type="button" {"Secondary"}
                                    (PreEscaped("&nbsp;"))
                                    a.btn.success href="" type="button" {"Success"}
                                    (PreEscaped("&nbsp;"))
                                    a.btn.danger href="" type="button" {"Danger"}
                                    (PreEscaped("&nbsp;"))
                                    a.btn.warning href="" type="button" {"Warning"}
                                    (PreEscaped("&nbsp;"))
                                    a.btn.info href="" type="button" {"Info"}
                                }
                                footer {
                                    pre {
                                        code {
                                            "a.btn.primary href=\"\" {\"Primary\"}\n\n"
                                            "a.btn.secondary href=\"\" {\"Secondary\"}\n\n"
                                            "a.btn.success href=\"\" {\"Success\"}\n\n"
                                            "a.btn.danger href=\"\" {\"Danger\"}\n\n"
                                            "a.btn.warning href=\"\" {\"Warning\"}\n\n"
                                            "a.btn.info href=\"\" {\"Info\"}"
                                        }
                                    }
                                }
                            }

                            p { b { "Note:" } " The " code { "button" } " element should be used when the primary intent is to trigger an action or submit information. However, if you must use an anchor tag to act as a button, include " code { "role=\"button\"" } " to ensure assistive technologies interpret it correctly." }

                            h2 { "Sizes" }
                            p { "Add the " code { "xs" } ", " code { "sm" } ", or " code { "lg" } " class for additional sizes." }
                            panel {
                                content {
                                    p {
                                        button.primary.lg type="button" {"Large button"}
                                        (PreEscaped("&nbsp;&nbsp;"))
                                        a.btn.lg href="" {"Large button tag"}
                                    }

                                    p {
                                        button.primary type="button" {"Default button"}
                                        (PreEscaped("&nbsp;&nbsp;"))
                                        button type="button" {"Default button tag"}
                                    }

                                    p {
                                        button.primary.sm type="button" {"Small button"}
                                        (PreEscaped("&nbsp;&nbsp;"))
                                        button.sm type="button" {"Small button tag"}
                                    }

                                    button.primary.xs type="button" {"X-small button"}
                                    (PreEscaped("&nbsp;&nbsp;"))
                                    button.xs type="button" {"X-small button tag"}
                                }
                                footer {
                                    pre {
                                        code {
                                            "button.primary.lg type=\"button\" {\"Large button\"}\n"
                                            "a.btn.lg href=\"\" {\"Large button tag\"}\n\n"
                                            "button.primary type=\"button\" {\"Default button\"}\n"
                                            "a.btn href=\"\" {\"Default button tag\"}\n\n"
                                            "button.primary.sm type=\"button\" {\"Small button\"}\n"
                                            "a.btn.sm href=\"\" {\"Small button tag\"}\n\n"
                                            "button.primary.xs type=\"button\" {\"X-small button\"}\n"
                                            "a.btn.xs href=\"\" {\"X-small button tag\"}"
                                        }
                                    }
                                }
                            }

                            h2 { "Block buttons" }
                            p { "Create block level buttons — those that span the full width of the parent — by adding the " code { "block" } " class." }
                            panel {
                                content {
                                    button.primary.block type="button" {"Block level button"}
                                    (PreEscaped("&nbsp;"))
                                    a.btn.block href="" {"Block level button tag"}
                                }
                                footer {
                                    pre {
                                        code {
                                            "button.primary.block type=\"button\" {\"Block level button\"}\n"
                                            "a.btn.block href=\"\" {\"Block level button tag\"}"
                                        }
                                    }
                                }
                            }

                            h2 { "Disabled state" }
                            p {
                                "Make buttons look inactive by adding the "
                                code { "disabled" } 
                                " attribute to any "
                                code { "<button>" }
                                " element. Disabled buttons have "
                                code { "pointer-events: none" }
                                " applied to them, preventing hover and active states from triggering."
                            }
                            panel {
                                content {
                                    button.primary type="button" disabled { "Disabled button" }
                                    (PreEscaped("&nbsp;"))
                                    button type="button" disabled { "Disabled button" }
                                }
                                footer {
                                    pre {
                                        code {
                                            "button.primary type=\"button\" disabled { \"Disabled button\" }\n"
                                            "button type=\"button\" disabled { \"Disabled button\" } "
                                        }
                                    }
                                }
                            }

                            p { "Disabled button tags behave a bit differently:" }
                            ul {
                                li { code { "<a>" } " tags don't support the " code { "disabled" } " attribute, so you must add the " code { ".disabled" } " class to make it visually appear disabled." }
                                li { "Disabled button tags should include the " code { "aria-disabled=\"true\"" } " attribute to indicate the state of the element to assistive technologies." }
                                li { "It is crucial to include " code { "tabindex=\"-1\"" } " for accessibility. This attribute ensures that the element is not focusable via keyboard navigation." }
                            }
                            panel {
                                content {
                                    a.btn.primary.disabled href="" role="button" tabindex="-1" aria-disabled="true" { "Disabled button tag" }
                                    (PreEscaped("&nbsp;"))
                                    a.btn.disabled href="" role="button" tabindex="-1" aria-disabled="true" { "Disabled button tag" }
                                }
                                footer {
                                    pre {
                                        code {
                                            "a.btn.primary.disabled href=\"\" role=\"button\" tabindex=\"-1\" aria-disabled=\"true\" { \"Disabled button tag\" }\n"
                                            "a.btn.disabled href=\"\" role=\"button\" tabindex=\"-1\" aria-disabled=\"true\" { \"Disabled button tag\" }"
                                        }
                                    }
                                }
                            }

                            h2 { "Helper functions" }
                            p { "A helper function encapsulates a common UI pattern into a reusable function. This helps maintain consistency across the UI and reduces repetitive markup." }

                            h3 { "Submit" }
                            p { code { "/lib/bux/src/button/submit"} }
                            p { "Use one of these helper functions to create a submit button for a form. Guidelines for the best use case scenario for each helper function are provided in the source code. Current support includes the following use cases:"}
                            ul {
                                li {
                                    code { "icon" }
                                    " , "
                                    code { "icon_class" }
                                    " , "
                                    code { "label" }
                                    " , "
                                    code { "label_class" }
                                    " , "
                                    code { "label_icon" }
                                    " , "
                                    code { "label_icon_class" }
                                    " , "
                                    code { "save" }
                                    " , "
                                    code { "delete" }
                                    " , "
                                    code { "activate" }
                                    " , "
                                    code { "deactivate" }
                                    " , "
                                    code { "ok" }
                                }
                            }
                            p { b { "Examples" } }
                            panel {
                                content {
                                    (bux::button::submit::icon_class("Submit", "fas fa-check", "success"))
                                    (PreEscaped("&nbsp;"))
                                    (bux::button::submit::label_icon_class("Add User", "fas fa-plus", "primary"))
                                    (PreEscaped("&nbsp;"))
                                    (bux::button::submit::save("Submit"))
                                    (PreEscaped("&nbsp;"))
                                    (bux::button::submit::delete("Delete"))
                                }
                                footer {
                                    pre {
                                        code {
                                            "(bux::button::submit::icon_class(\"Submit\", \"fas fa-check\", \"success\"))\n"
                                            "(bux::button::submit::label_icon_class(\"Add User\", \"fas fa-plus\", \"primary\"))\n"
                                            "(bux::button::submit::save(\"Submit\"))\n"
                                            "(bux::button::submit::delete(\"Delete\"))"
                                        }
                                    }
                                }
                            }

                            h3 { "Link" }
                            p { code { "/lib/bux/src/button/link"} }
                            p { "Use one of these helper functions when you need a link that looks like a button. Guidelines for the best use case scenario for each helper function are provided in the source code. Current support includes the following use cases:"}
                            ul {
                                li {
                                    code {"icon"}
                                    " , "
                                    code { "icon_class" }
                                    " , "
                                    code { "label" }
                                    " , "
                                    code { "label_class" }
                                    " , "
                                    code { "label_icon" }
                                    " , "
                                    code { "label_icon_class" }
                                    " , "
                                    code { "cancel" }
                                    " , "
                                    code { "details" }
                                    " , "
                                    code { "add" }
                                    " , "
                                    code { "edit" }
                                    " , "
                                    code { "go_back" }
                                }
                            }
                            p { b { "Examples" } }
                            panel {
                                content {
                                    (bux::button::link::label_class("Submit", "/", "success"))
                                    (PreEscaped("&nbsp;"))
                                    (bux::button::link::label_icon_class("Add User", "fas fa-plus", "/", "primary"))
                                    (PreEscaped("&nbsp;"))
                                    (bux::button::link::cancel("/"))
                                    (PreEscaped("&nbsp;"))
                                    (bux::button::link::details("/"))
                                }
                                footer {
                                    pre {
                                        code {
                                            "(bux::button::link::label_class(\"Activate\", \"/\", \"success\"))\n"
                                            "(bux::button::link::label_icon_class(\"Add User\", \"fas fa-plus\", \"/\", \"primary\"))\n"
                                            "(bux::button::link::cancel(\"/\"))\n"
                                            "(bux::button::link::details(\"/\"))"
                                        }
                                    }
                                }
                            }
                        }
                        cell-3 {
                            h3 { "On this page" }
                            hr;
                            ul {
                                li {
                                    a href="" { "Contextual classes" }
                                }
                                li {
                                    a href="" { "Button tags" }
                                }
                                li {
                                    a href="" { "Sizes" }
                                }
                                li {
                                    a href="" { "Block buttons" }
                                }
                                li {
                                    a href="" { "Disabled state" }
                                }
                                li {
                                    a href="" { "Helper functions" }
                                }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

#[approck::http(GET /bux-ui-quick-info-box; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Admin Quick Box");

        let parser = pulldown_cmark::Parser::new(include_str!(
            "../../../../lib/bux/src/ui/quick_info_box.md"
        ));
        let mut html_output = String::new();
        pulldown_cmark::html::push_html(&mut html_output, parser);

        doc.add_body(html!(
            section {
                panel {
                    (maud::PreEscaped(html_output))
                }
            }
        ));
        Response::HTML(doc.into())
    }
}

# Documentation Index

This is an AI-generated index of all documentation files in the `doc/` directory to help AI assistants determine what to read when needed.

## Files

### example-approck-route.rs

Complete example of an approck route implementation demonstrating the `#[approck::route()]` macro pattern with URL parameters (`/admin/client/{client_uuid:Uuid}`), authentication using Identity trait, both GET and POST handlers, HTML generation using Maud templating, document manipulation (title, navigation, body content), and table-based data display with semantic CSS classes.

### submit-form-via-typescript.md

Modern TypeScript form submission example showing proper use of `requestSubmit()` method with `preventDefault()` event handling to ensure HTML5 form validation is respected, demonstrating best practices for clean separation between button click handling and form submission logic.

bux::document! {

    pub struct Document {}

    impl Document {
        pub fn new(
            app: &'static crate::AppSystem,
            identity: &crate::Identity,
            req: &approck::server::Request,
        ) -> Self {
            use bux::document::Cliffy;
            use bux::document::{Base, Nav1, Nav2, Nav2Dropdown, Nav2<PERSON>ogo, FooterSocial, FooterLinks};

            let mut this = Self {
                ..Default::default()
            };

            // Base setup
            this.set_uri(req.path());
            this.set_title("HomeWaters Club - Admin"); // default title
            this.set_site_name("HomeWaters Club Admin");
            this.set_owner("HomeWaters. All Rights Reserved.");
            this.add_logo("/admin/", "https://www.homewatersclub.com/wp-content/uploads/2018/05/hw-logo-629x87.png");

            // Add Google Fonts
            this.add_head(maud::html!(
                link href="https://fonts.googleapis.com/css2?family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&display=swap" rel="stylesheet";
            ));

            // Nav2 setup
            this.set_identity(identity);

            // Add admin navigation menu
            this.add_nav1_menu_link("Dashboard", "/admin/", "");

            // Add "Members" dropdown menu
            let members_menu = this.add_nav1_menu_dropdown("Members", "members-menu");
            members_menu.add_link("View All", "", "/admin/members/", "");
            members_menu.add_link("Add Member", "", "/admin/members/add", "");
            members_menu.add_link("Member Reports", "", "/admin/members/reports", "");

            // Add "Reservations" dropdown menu
            let reservations_menu = this.add_nav1_menu_dropdown("Reservations", "reservations-menu");
            reservations_menu.add_link("Calendar", "", "/admin/reservation/calendar", "");
            reservations_menu.add_link("All Reservations", "", "/admin/reservations/", "");

            // Add "Resources" dropdown menu
            let resources_menu = this.add_nav1_menu_dropdown("Resources", "resources-menu");
            resources_menu.add_link("Lodging", "", "/admin/resources/lodging", "");
            resources_menu.add_link("Fishing Guides", "", "/admin/resources/fishing_guide", "");
            resources_menu.add_link("Water Beat", "", "/admin/resources/waterbeat", "");
            resources_menu.add_link("Other Resources", "", "/admin/resources/otherresources", "");

            // Add "Schedule" dropdown menu
            let schedule_menu = this.add_nav1_menu_dropdown("Schedule", "schedule-menu");
            schedule_menu.add_link("Event Manager", "", "/admin/schedule/eventmanager", "");
            schedule_menu.add_link("View Schedule", "", "/admin/schedule/", "");

            this.add_nav1_menu_link("Reports", "/admin/report/", "");

            // Check if user is logged in and add user menu
            match homewaters_zero::Identity::is_logged_in(identity) {
                true => {
                    if let Some(name) = bux::Identity::name(identity) {
                        let user_menu: &mut Nav2Dropdown;
                        if let Some(avatar_uri) = bux::Identity::avatar_uri(identity) {
                            let user_logo = Nav2Logo { url: avatar_uri, alt: None };
                            user_menu = this.add_nav2_menu_dropdown(&name, "user-menu", Some(user_logo), None);
                        } else {
                            user_menu = this.add_nav2_menu_dropdown(&name, "user-menu", None, None);
                        }
                        user_menu.add_link("Admin Dashboard", "/admin/", None);
                        user_menu.add_span(format!("Admin: {}", name).as_str());
                        user_menu.add_link("Logout", "/auth/logout", None);

                        this.add_footer_link("Logout", "/auth/logout");
                    };
                }
                false => {
                    this.add_nav2_menu_link("Admin Login", "/auth/", "");
                    this.add_footer_link("Admin Login", "/auth/");
                }
            }

            // Add social media to the footer
            this.footer_social_facebook("https://www.facebook.com/HomeWatersClub");
            this.footer_social_instagram("https://www.instagram.com/homewaters_pa");
            this.footer_social_twitter("https://twitter.com/HomeWaters");

            this
        }
    }

    impl bux::document::Base for Document {
        fn render_body(&self) -> maud::Markup {
            bux::document::Cliffy::render_body(self)
        }
    }
    impl bux::document::Nav1 for Document {}
    impl bux::document::Nav2 for Document {}
    impl bux::document::PageNav for Document {}
    impl bux::document::Cliffy for Document {}
    impl bux::document::FooterSocial for Document {}
    impl bux::document::FooterLinks for Document {}

}

impl bux::Identity for crate::IdentityStruct {
    fn role(&self) -> String {
        match &self.auth_fence {
            Some(auth_fence) => auth_fence.name.to_string(),
            None => "anonymous".to_string(),
        }
    }
    fn name(&self) -> Option<String> {
        self.auth_fence
            .as_ref()
            .map(|auth_fence| auth_fence.name.to_string())
    }
    fn email(&self) -> Option<String> {
        match &self.auth_fence {
            Some(auth_fence) => auth_fence.email.clone(),
            None => None,
        }
    }
    fn avatar_uri(&self) -> Option<String> {
        match &self.auth_fence {
            Some(auth_fence) => auth_fence.avatar_uri.clone(),
            None => None,
        }
    }
}

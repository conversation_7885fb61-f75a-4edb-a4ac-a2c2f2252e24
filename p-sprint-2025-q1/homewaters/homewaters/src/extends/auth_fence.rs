impl auth_fence::Identity for crate::IdentityStruct {
    fn is_logged_in(&self) -> bool {
        self.auth_fence.is_some()
    }
    fn identity_uuid(&self) -> Option<granite::Uuid> {
        self.auth_fence
            .as_ref()
            .map(|auth_fence| auth_fence.identity_uuid)
    }
    fn remote_address(&self) -> std::net::IpAddr {
        todo!()
    }
    fn session_token(&self) -> String {
        todo!()
    }
}

impl auth_fence::App for crate::AppStruct {
    fn after_login_next_url<'a>(&self, next_uri: Option<&'a str>) -> &'a str {
        match next_uri {
            Some(next_uri) => next_uri,
            None => "/member/",
        }
    }
    fn auth_fence_system(&self) -> &auth_fence::types::ModuleStruct {
        &self.auth_fence
    }
}

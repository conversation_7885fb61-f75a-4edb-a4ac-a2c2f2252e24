use auth_fence::App;

bux::document! {

    pub struct Document {}

    impl Document {
        pub fn new(
            app: &'static crate::AppStruct,
            identity: &crate::IdentityStruct,
            req: &approck::server::Request,
        ) -> Self {

            // trait Nav2 must be in scope for set_identity() and nav2_menu_add()
            use bux::document::{Cliffy, Base, Nav1, Nav2, Nav2Dropdown, Nav2Logo, FooterSocial, FooterLinks};

            println!("Request {:?}", req.path());

            let mut this = Self {
                ..Default::default()
            };

            // Base setup
            this.set_uri(req.path());
            this.set_site_name("HomeWaters Club");
            this.add_logo("/dashboard/", "https://www.homewatersclub.com/wp-content/uploads/2018/05/hw-logo-629x87.png");

            // Add Google Fonts
            this.add_head(maud::html!(
                link href="https://fonts.googleapis.com/css2?family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&display=swap" rel="stylesheet";
            ));

            // This includes bux stuff and whatever you put in ./Document.mcss


            // This includes bux stuff


            // Nav2 setup
            this.set_identity(identity);

            this.add_nav1_menu_link("Home", "/", "");

            // Add "About" dropdown menu
            let about_menu = this.add_nav1_menu_dropdown("About", "about-menu");
            about_menu.add_link("Location", "", "", "");
            about_menu.add_link("Our Staff", "", "", "");
            about_menu.add_link("Founding Family", "", "", "");
            about_menu.add_link("Retreats", "", "", "");
            about_menu.add_link("Fundraising", "", "", "");
            about_menu.add_link("Additional Activities", "", "", "");
            about_menu.add_link("Mission & Values", "", "/about/mission", "");
            about_menu.add_link("History", "", "/about/history", "");

            // Add "Fishing" dropdown menu
            let fishing_menu = this.add_nav1_menu_dropdown("Fishing", "fishing-menu");
            fishing_menu.add_link("Streams", "", "", "");
            fishing_menu.add_link("The Fish", "", "", "");
            fishing_menu.add_link("Our Guides", "", "", "");
            fishing_menu.add_link("The Stewardship", "", "", "");
            fishing_menu.add_link("Hosted Destination Trips", "", "", "");

            this.add_nav1_menu_link("Lodging", "/", "");

            // Add "Access" dropdown menu
            let access_menu = this.add_nav1_menu_dropdown("Access", "access-menu");
            access_menu.add_link("Memberships", "", "", "");
            access_menu.add_link("Non-Member Access", "", "", "");

            // Add "News" dropdown menu
            let news_menu = this.add_nav1_menu_dropdown("News", "news-menu");
            news_menu.add_link("Stream Reports", "", "", "");
            news_menu.add_link("Blog", "", "", "");
            news_menu.add_link("Photos/Videos", "", "", "");

            this.add_nav1_menu_link("Contact", "/", "");


            match homewaters_zero::Identity::is_logged_in(identity) {
                true => {
                    if let Some(name) = bux::Identity::name(identity) {
                        let user_menu: &mut Nav2Dropdown;
                        if let Some(avatar_uri) = bux::Identity::avatar_uri(identity) {
                            let user_logo = Nav2Logo { url: avatar_uri, alt: None };
                            user_menu = this.add_nav2_menu_dropdown(&name, "user-menu", Some(user_logo), None);
                        } else {
                            user_menu = this.add_nav2_menu_dropdown(&name, "user-menu", None, None);
                        }
                        user_menu.add_link("Dashboard", "/member/", None);
                        user_menu.add_span(format!("Logged in as {name}").as_str());
                        user_menu.add_link("Logout", auth_fence::App::logout_url(app), None);

                        this.add_footer_link("Logout", auth_fence::App::logout_url(app));
                    };
                }
                false => {
                    this.add_nav2_menu_link("Member Login", app.login_url(), "");
                    this.add_footer_link("Member Login", "/auth/");
                }
            }

            // Set copyright text
            this.set_owner("HomeWaters. All Rights Reserved.");

            // Add social media to the footer
            this.footer_social_facebook("https://www.facebook.com/HomeWatersClub");
            this.footer_social_instagram("https://www.instagram.com/homewaters_pa");
            this.footer_social_twitter("https://twitter.com/HomeWaters");

            this
        }
    }

    impl bux::document::Base for Document {
        fn render_body(&self) -> maud::Markup {
            bux::document::Cliffy::render_body(self)
        }
    }
    impl bux::document::Nav1 for Document {}
    impl bux::document::Nav2 for Document {}
    impl bux::document::PageNav for Document {}
    impl bux::document::Cliffy for Document {}
    impl bux::document::FooterSocial for Document {}
    impl bux::document::FooterLinks for Document {}
    impl auth_fence::Document for Document {}
    impl auth_fence::DocumentPublic for Document {}

}

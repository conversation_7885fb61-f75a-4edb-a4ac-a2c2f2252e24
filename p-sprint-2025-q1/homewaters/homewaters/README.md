# `demo-template`

This is a directory for cloning into a new demo project.

## To use this template:

### Step 1:

Copy the directory to another `demo-*` directory, like `demo-foobar`

### Step 2:

search and replace `template` with `foobar`

### Step 3:

Make sure that it's added to:

- `~/pnpm-workspace.json`
- `~/Cargo.toml`

### Step 4:

Choose a free port number and place it in `./Cargo.toml`

### Step 5

Add to your `LOCAL.toml` with:

```
[app.demo-foobar]
redis.host = "127.0.0.1"
redis.database = 0
```

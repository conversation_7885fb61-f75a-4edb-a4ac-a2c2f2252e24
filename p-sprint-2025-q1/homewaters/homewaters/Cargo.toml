[package]
name = "homewaters"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.approck.app]
port = 3013
docmap.auth-fence.DocumentPublic = "Document"

extends = ["bux", "homewaters-zero", "homewaters-member", "homewaters-public", "homewaters-admin", "approck", "granite", "auth-fence", "appstruct"]

[dependencies]
appstruct = { workspace = true }
approck = { workspace = true }
auth-fence = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-redis = { workspace = true }
approck-postgres = { workspace = true }
homewaters-zero = { path = "../homewaters-zero" }
homewaters-member = { path = "../homewaters-member" }
homewaters-public = { path = "../homewaters-public" }
homewaters-admin = { path = "../homewaters-admin" }


clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
toml = { workspace = true }

[package]
name = "df4l-icover"
version = "0.1.0"
edition = "2024"

[package.metadata.approck.mod]
extends = [
    "df4l-zero",
    "approck",
    "bux",
    "granite",
    "auth-fence",
    "auth-fence-provider",
]

[dependencies]
approck = { workspace = true }
auth-fence = { workspace = true }
auth-fence-provider = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-postgres = { workspace = true }
approck-redis = { workspace = true }
df4l-zero = { path = "../df4l-zero" }


maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }

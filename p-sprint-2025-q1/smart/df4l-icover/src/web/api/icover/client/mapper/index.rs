#[approck::http(GET /api/icover/client/{client_uuid:Uuid}/; AUTH None; return JSON|Text;)]
pub mod get {
    use crate::api::client::detail as client_api;

    pub async fn request(
        app: App,
        req: Request,
        identity: Identity,
        path: Path,
    ) -> Result<Response> {
        // Permissions checked in API

        let input = client_api::get::Input {
            client_uuid: path.client_uuid,
        };

        let output = match client_api::get::call(app, identity.as_ref(), input).await {
            Ok(output) => output,
            Err(e) => {
                if matches!(e.get_error_type(), granite::ErrorType::Authorization) {
                    approck::error!(
                        "Unauthorized access attempt to iCover data for client {} from IP {}",
                        path.client_uuid,
                        req.remote_ip(),
                    );

                    let response = Text {
                        status: approck::StatusCode::UNAUTHORIZED,
                        content: "insufficient permissions to read client policy information"
                            .to_string(),
                        ..Default::default()
                    };
                    return Ok(Response::Text(response));
                } else {
                    approck::error!(
                        "Error retrieving iCover data for client {}",
                        path.client_uuid
                    );
                    return Err(e);
                }
            }
        };

        // Client will not expect Some(value), etc.. so we're using serde instead
        Ok(Response::JSON(serde_json::json!(output).into()))
    }
}

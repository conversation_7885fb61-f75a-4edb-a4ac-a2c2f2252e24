// Client and Policy Data Endpoint:
// Host: df4l-1.us-east-2.acp7.link
// GET /api/icover/client-policy/{client_uuid}
pub mod get {
    pub struct Input {
        pub client_uuid: granite::Uuid,
    }

    #[derive(serde::Serialize, Debug)]
    pub struct Output {
        pub agent: Agent,
        pub client: Client,
    }

    #[derive(serde::Serialize, Debug)]
    #[serde(rename_all = "camelCase")]
    pub struct Agent {
        pub agent_email: String,
        pub agent_first_name: String,
        pub agent_last_name: String,
        pub agent_contact_number: String,
        pub agent_access_level: String,
        pub agent_states_approved: Vec<AgentStateApproval>,
        // TODO: Make GBU agent id required in schema (??? iCover insists this must be non-nullable)
        pub client_agent_id: String, // GBU Agent ID
        pub agent_address: AgentAddress,
    }

    #[derive(serde::Serialize, serde::Deserialize, Debug)]
    #[serde(rename_all = "camelCase")]
    pub struct AgentStateApproval {
        pub product_name: String, // Debt2Capital
        pub states: Vec<String>,
    }

    #[derive(serde::Serialize, Debug)]
    #[serde(rename_all = "camelCase")]
    pub struct AgentAddress {
        pub street_one: String,
        pub street_two: Option<String>,
        pub city: String,
        pub state: String,
        pub zipcode: String,
    }

    #[derive(serde::Serialize, Debug)]
    #[serde(rename_all = "camelCase")]
    pub struct Client {
        pub client_uuid: granite::Uuid,
        pub first_name: String,
        pub last_name: String,
        pub street_one: String,
        pub street_two: Option<String>,
        pub city: String,
        pub state: String,
        pub zipcode: String,
        pub email: String,
        pub phone: String,
        pub gender: String,
        pub birth_date: String,
        pub target_premium: granite::Decimal,
        pub pua_split: Option<String>,
        pub pua_recurring_amount: granite::Decimal,
        pub pua_recurring_duration: u8,
        pub pua_lump_sum_amount: Option<granite::Decimal>,
    }

    #[derive(serde::Serialize, Debug)]
    #[serde(rename_all = "snake_case")]
    pub enum PuaType {
        LumpSum,
        Recurring,
    }
    struct PolicyData {
        target_premium: granite::Decimal,
        pua_split: Option<String>,
        pua_recurring_amount: granite::Decimal,
        pua_recurring_duration: u8,
        pua_lump_sum_amount: granite::Decimal,
    }

    impl std::fmt::Display for PuaType {
        fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
            match self {
                PuaType::LumpSum => write!(f, "lump_sum"),
                PuaType::Recurring => write!(f, "recurring"),
            }
        }
    }

    pub async fn call(
        app: &impl crate::App,
        identity: &impl crate::Identity,
        input: Input,
    ) -> granite::Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return Err(granite::authorization_error!(
                "insufficient permissions to read client policy information"
            ));
        }

        // load the wizard
        let wizard = df4l_zero::client::wizard::Wizard::load(app, &dbcx, input.client_uuid).await?;

        // Extract PUA data
        let pua_ok = match wizard.pua_result {
            Ok(pua) => pua,
            Err(e) => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .set_external_message(format!("PUA Error: {e}")));
            }
        };

        // Extract advisor data
        let advisor_ok = match wizard.advisor_result {
            Ok(advisor) => advisor,
            Err(e) => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .set_external_message(format!("Advisor Error: {e}")));
            }
        };

        let contact_ok = match wizard.contact_result {
            Ok(contact) => contact,
            Err(e) => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .set_external_message(format!("Contact Error: {e}")));
            }
        };

        let policy_data = match wizard.client_type {
            df4l_zero::client::wizard::ClientType::DebtManagement => PolicyData {
                target_premium: pua_ok.monthly_total_premium,
                pua_split: pua_ok.pua_split.icover_identifier(),
                pua_recurring_amount: pua_ok.monthly_pua_premium,
                pua_recurring_duration: 10,
                pua_lump_sum_amount: pua_ok.pua_contribution,
            },
            df4l_zero::client::wizard::ClientType::PolicyOnly => PolicyData {
                target_premium: pua_ok.monthly_total_premium,
                pua_split: pua_ok.pua_split.icover_identifier(),
                pua_recurring_amount: pua_ok.monthly_pua_premium,
                pua_recurring_duration: if pua_ok.monthly_pua_premium.is_zero() {
                    0
                } else {
                    10
                },
                pua_lump_sum_amount: pua_ok.pua_contribution,
            },
        };

        let pua_lump_sum_amount = if policy_data.pua_lump_sum_amount.is_zero() {
            None
        } else {
            Some(policy_data.pua_lump_sum_amount)
        };

        // strict 10 digits
        let agent_contact_number = {
            let p = advisor_ok
                .phone
                .chars()
                .filter(|c| c.is_numeric())
                .collect::<String>();

            // remove 1 if it is a prefix
            if let Some(p) = p.strip_prefix("1") {
                p.to_string()
            } else {
                p
            }
        };

        // strict 10 digits
        let contact_phone = {
            let p = contact_ok
                .phone
                .chars()
                .filter(|c| c.is_numeric())
                .collect::<String>();

            // remove 1 if it is a prefix
            if let Some(p) = p.strip_prefix("1") {
                p.to_string()
            } else {
                p
            }
        };

        Ok(Output {
            agent: Agent {
                agent_email: advisor_ok.email,
                agent_first_name: advisor_ok.first_name,
                agent_last_name: advisor_ok.last_name,
                agent_contact_number,
                agent_access_level: "agent".to_string(),
                agent_states_approved: vec![AgentStateApproval {
                    product_name: "Debt2Capital".to_string(),
                    states: advisor_ok.statelic,
                }],
                client_agent_id: advisor_ok.gbu_esid,
                agent_address: AgentAddress {
                    street_one: advisor_ok.address1,
                    street_two: advisor_ok.address2,
                    city: advisor_ok.city,
                    state: advisor_ok.state,
                    zipcode: advisor_ok.zip,
                },
            },
            client: Client {
                client_uuid: wizard.client_uuid,
                first_name: contact_ok.first_name,
                last_name: contact_ok.last_name,
                street_one: contact_ok.address1,
                street_two: contact_ok.address2,
                city: contact_ok.city,
                state: contact_ok.state,
                zipcode: contact_ok.zip,
                // They only want digits in the phone number
                phone: contact_phone,
                email: contact_ok.email,
                gender: match contact_ok.gender {
                    df4l_zero::types::Gender::Male => "Male".to_string(),
                    df4l_zero::types::Gender::Female => "Female".to_string(),
                },

                // format birthdate as MM/DD/YYYY
                birth_date: contact_ok.birth_date.format("%m/%d/%Y").to_string(),

                // Target premium is the monthly extra budget
                target_premium: policy_data.target_premium,
                pua_split: policy_data.pua_split,
                pua_recurring_amount: policy_data.pua_recurring_amount,
                pua_recurring_duration: policy_data.pua_recurring_duration,
                pua_lump_sum_amount,
            },
        })
    }
}

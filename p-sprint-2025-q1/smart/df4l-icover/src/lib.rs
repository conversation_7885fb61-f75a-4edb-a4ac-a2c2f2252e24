pub mod api;
pub mod web;

pub trait App:
    approck::App
    + approck_postgres::App
    + approck_redis::App
    + auth_fence_provider::App
    + df4l_zero::App
{
    fn auth_fence_provider(&self) -> &auth_fence_provider::ModuleStruct;
}

pub trait Identity:
    approck::Identity + auth_fence::Identity + auth_fence_provider::Identity
{
    fn web_usage(&self) -> bool;
    fn api_usage(&self) -> bool;

    #[allow(async_fn_in_trait)]
    async fn client_read(
        &self,
        dbcx: &impl approck_postgres::DB,
        client_uuid: granite::Uuid,
    ) -> bool;
}

# GBU Insurance Carrier & Debt2Captial API Integration

- **Author**: AppCove Inc.  
- **Version**: 1.0-draft  
- **Technical Contact**: <EMAIL>
- **Documentation Updates**: This document will be updated as the API evolves
- **Version**: 1.0
- **Last Updated**: Aug 19, 2025

## Overview

This document outlines the API integration between GBU (the insurance carrier) and D2C (Debt2Capital). GBU underwrites insurance policies for D2C clients and provides daily updates on policy status and financial metrics.

## Bearer Token Authentication
- **Method**: Bearer <PERSON>ken
- **Header**: `Authorization: Bearer <token>`
- **Token Assignment**: Tokens are assigned by D2C to GBU
- **Security**: All API requests must include a valid bearer token

## Daily Policy Push Endpoint

- **URL**: `https://app.debt2capital.com/api/gbu/daily-push`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Description**: GBU sends daily updates for all D2C policies regardless of status, allowing D2C to react accordingly to policy changes.

## Policy Types

The API supports three policy types based on their current status. The `type` field in each policy object determines the record structure and which fields are required:

- **`ActivePolicy`**: Must include all financial fields (`cash_value`, `loan_available`, `loan_balance`)
- **`PendingPolicy`**: Only includes basic identification fields (no financial data)
- **`InactivePolicy`**: Only includes basic identification fields (no financial data)

---

### `ActivePolicy`
Active policies that are in good standing and have financial values.

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `type` | String | Must be `"ActivePolicy"` | Yes |
| `d2c_client_uuid` | UUID | D2C client identifier | Yes |
| `gbu_policy_identifier` | String | GBU policy identifier | Yes |
| `cash_value` | String | Current cash value of policy (as string) | Yes |
| `loan_available` | String | Available loan amount (as string) | Yes |
| `loan_balance` | String | Current loan balance (as string) | Yes |

---

### `PendingPolicy`
Policies that are currently in underwriting and not yet approved or declined.

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `type` | String | Must be `"PendingPolicy"` | Yes |
| `d2c_client_uuid` | UUID | D2C client identifier | Yes |
| `gbu_policy_identifier` | String | GBU policy identifier | Yes |

---

### `InactivePolicy`
Inactive policies that are lapsed, cancelled, or otherwise not in good standing.

| Field | Type | Description | Required |
|-------|------|-------------|----------|
| `type` | String | Must be `"InactivePolicy"` | Yes |
| `d2c_client_uuid` | UUID | D2C client identifier | Yes |
| `gbu_policy_identifier` | String | GBU policy identifier | Yes |

## Request Format

```json
{
  "idempotency_token": "sent-2025-01-01",
  "policies": [
    {
      "type": "ActivePolicy",
      "d2c_client_uuid": "550e8400-e29b-41d4-a716-************",
      "gbu_policy_identifier": "GBU-POL-123456",
      "cash_value": "15000.50",
      "loan_available": "12000.00",
      "loan_balance": "3000.00"
    },
    {
      "type": "PendingPolicy",
      "d2c_client_uuid": "550e8400-e29b-41d4-a716-************",
      "gbu_policy_identifier": "GBU-POL-123457"
    },
    {
      "type": "InactivePolicy",
      "d2c_client_uuid": "550e8400-e29b-41d4-a716-************",
      "gbu_policy_identifier": "GBU-POL-123458"
    }
  ]
}
```

### `idempotency_token` (String, Required)
- Unique identifier for this request to ensure idempotent processing
- Should be unique per daily batch
- Recommended format: `gbu-daily-{date}-{version}`
- Example: `"gbu-daily-2025-01-15-v1"`

### `policies` (Array, Required)
Array of policy objects containing all D2C policies underwritten by GBU.

### `policies[].type` (String, Required)
- Policy type identifier
- Must be one of: `"ActivePolicy"`, `"PendingPolicy"`, or `"InactivePolicy"`
- Example: `"ActivePolicy"`

### `policies[].d2c_client_uuid` (String, Required)
- Format: UUID v4 or v7
- D2C client identifier
- Example: `"550e8400-e29b-41d4-a716-************"`

### `policies[].gbu_policy_identifier` (String, Required)
- GBU's internal policy identifier
- Must be unique across all GBU policies
- Example: `"POL-123456"`

### `policies[].cash_value` (String, Required for `ActivePolicy`)
- Current cash value of the policy in USD as string
- Should represent decimal value with 2 decimal places
- Must represent value >= 0
- Example: `"16125.50"`

### `policies[].loan_available` (String, Required for `ActivePolicy`)
- Available loan amount in USD as string
- Should represent decimal value with 2 decimal places
- Must represent value >= 0
- Example: `"12000.00"`

### `policies[].loan_balance` (String, Required for `ActivePolicy`)
- Current loan balance in USD as string
- Should represent decimal value with 2 decimal places
- Must represent value >= 0
- Example: `"3000.00"`

## Success Response (200 OK)

```json
{
  "message": "Policy data successfully processed for 3 records"
}
```

## Authentication Error (401 Unauthorized)
```json
{
  "message": "Invalid or missing bearer token"
}
```

## Validation Error (400 Bad Request)
```json
{
  "message": "Invalid request data: policies[0].d2c_client_uuid has invalid UUID format, policies[1].cash_value must be a valid decimal string"
}
```

## Server Error (500 Internal Server Error)
```json
{
  "message": "An internal server error occurred"
}
```

------

## Implementation Notes

### Data Completeness
- **All Policies Required**: The payload must contain ALL D2C policies underwritten by GBU, regardless of status
- **Daily Updates**: Data should be sent once daily, preferably during off-peak hours
- **Status Changes**: Include policies with status changes since the last update

### Error Handling
- **Retry Logic**: Implement exponential backoff for failed requests
- **Timeout**: Set reasonable timeout values (recommended: 30 seconds)
- **Logging**: Log all API interactions for audit purposes

### Data Validation
- **UUID Validation**: Ensure all `client_uuid` values are valid UUIDs
- **String Numeric Format**: Ensure `cash_value` and `credit_available` are valid decimal strings with 2 decimal places
- **Boolean Validation**: Verify `active` field is a proper boolean value
- **Idempotency**: Use unique `idempotency_token` for each daily batch to prevent duplicate processing

### Security Considerations
- **HTTPS Only**: All communications must use HTTPS
- **Token Security**: Store bearer tokens securely
- **Rate Limiting**: Respect any rate limiting implemented by D2C

## Testing

### Test Environment
- **URL**: `https://df4l-1.app.us-east-2.src7.link/api/gbu/daily-push`
- **Test Tokens**: Contact D2C technical team for test bearer tokens

### Sample Test Data
Use the JSON payload structure above with test `client_uuid` values provided by D2C.


pub mod web;

pub trait App: approck::App + approck_postgres::App + approck_redis::App + df4l_zero::App {}

pub trait Identity: approck::Identity + auth_fence::Identity {
    fn web_usage(&self) -> bool;
    fn api_usage(&self) -> bool;

    #[allow(async_fn_in_trait)]
    async fn client_read(
        &self,
        dbcx: &impl approck_postgres::DB,
        client_uuid: granite::Uuid,
    ) -> bool;
}

pub trait Document: bux::document::Base {}

[package]
name = "rrr"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.approck.app]
port = 3012
docmap.auth-fence.DocumentPublic = "DocumentRRR"

extends = ["bux", "granite", "auth-fence", "rrr-zero", "rrr-agent", "rrr-public", "approck", "api-sendgrid", "api-twilio", "api-sentry", "rrr-admin", "appstruct"]


[dependencies]
approck = {workspace = true}
appstruct = { workspace = true }
auth-fence = { workspace = true }
bux = { workspace = true }
rrr-zero = { path = "../rrr-zero" }
rrr-agent = { path = "../rrr-agent" }
rrr-public = { path = "../rrr-public" }
rrr-admin = { path = "../rrr-admin" }
api-sendgrid = { workspace = true }
api-twilio = { workspace = true }
api-sentry = { workspace = true }


chrono = { workspace = true, features = ["serde"] }
clap = { workspace = true, features = ["derive"] }


granite = { workspace = true }
approck-postgres = { workspace = true }
approck-redis = { workspace = true }

rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }

maud = {workspace = true}
serde_json = { workspace = true }
serde = { workspace = true, features = ["derive"] }

tokio = { workspace = true, features = ["full"] }
toml = { workspace = true }


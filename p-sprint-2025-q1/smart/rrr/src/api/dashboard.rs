#[approck::api]
pub mod entity_picker {

    #[granite::gtype(ApiInput)]
    pub struct Input {}

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub rows: Option<Vec<Entity>>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Entity {
        pub uuid: Uuid,
        pub icon: String,
        pub name: String,
        pub role: String,
        pub link: String,
    }

    /// If they have no identity or no found rows, then redirect them to the home page /
    pub async fn call(app: App, identity: Identity) -> Result<Output> {
        let identity_uuid = match identity.identity_uuid() {
            Some(identity_uuid) => identity_uuid,
            None => {
                return Ok(Output { rows: None });
            }
        };

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $identity_uuid: &identity_uuid,
            };
            row = {
                entity_type: i32,
                entity_uuid: Uuid,
                name: String,
            };

            // Admin links?
            SELECT
                1,
                identity.identity_uuid,
                identity.name
            FROM
                rrr_admin.identity as rrr_admin_identity
                INNER JOIN auth_fence.identity USING(identity_uuid)
            WHERE true
                AND identity.active
                AND identity.identity_uuid = $identity_uuid::uuid

            UNION

            SELECT
                2,
                agent_uuid,
                first_name || " " || last_name AS name
            FROM
                rrr.agent
            WHERE true
                AND active
                AND identity_uuid = $identity_uuid::uuid

            ORDER BY
                1,2
        )
        .await?;

        let rows: Vec<Entity> = rows
            .into_iter()
            .filter_map(|r| match r.entity_type {
                1 => Some(Entity {
                    uuid: r.entity_uuid,
                    icon: "🔑".to_string(),
                    name: r.name,
                    role: "Admin".to_string(),
                    link: "/admin/".to_string(),
                }),
                2 => Some(Entity {
                    uuid: r.entity_uuid,
                    icon: "👤".to_string(),
                    name: r.name,
                    role: "Agent".to_string(),
                    link: rrr_agent::ml_agent(r.entity_uuid),
                }),
                _ => None,
            })
            .collect();

        Ok(Output { rows: Some(rows) })
    }
}

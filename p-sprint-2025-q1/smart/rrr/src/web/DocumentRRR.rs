bux::document! {
    pub struct DocumentRRR {}

    impl DocumentRRR {
        pub fn new(
            _app: &'static crate::AppStruct,
            identity: &crate::IdentityStruct,
            req: &approck::server::Request,
        ) -> Self {
            // trait Nav2 must be in scope for set_identity() and nav2_menu_add()
            use bux::document::{Base, Nav2};

            println!("Request {:?}", req.path());

            let mut this = Self {
                ..Default::default()
            };

            // Base setup
            this.set_uri(req.path());
            this.set_title("Real Return Reporter™"); // default title

            // Nav2 setup
            this.set_identity(identity);

            this
        }
    }

    impl bux::document::Base for DocumentRRR {
        fn render_body(&self) -> maud::Markup {
            use bux::document::{Base, BodyDisplay};

            use maud::html;

            html!(
                layout-wrapper-outer {
                    layout-wrapper-inner {
                        nav-wrapper {
                            content-container.fluid {
                                nav-header id="horizontal-nav-header" {
                                    a class="home-link" href="/dashboard" {
                                        img src="https://asset7.net/Zagula/Smart/RRR/3R-white-logo-v2.svg" alt="RRR Logo" {}
                                    }
                                }
                                nav aria-label="Primary Navigation" id="primary-navigation" {
                                    menu-wrapper-outer {
                                        menu-wrapper-inner {



                                            ul.nav-menu {
                                                @for menu in Base::menu(self).iter_for_render() {
                                                    @for item in &menu.items {
                                                        @match &item.inner {
                                                            approck::MenuItemInner::Link { uri } => {
                                                                li.menu-item selected=[if Base::get_uri(self) == uri { Some("selected") } else { None }] {
                                                                    @let icon = match &item.icon {
                                                                        Some(icon) => html!{(icon) " "},
                                                                        None => html! {},
                                                                    };
                                                                    a.menu-link href=(uri) { (icon) (item.label) }
                                                                }
                                                            }
                                                            approck::MenuItemInner::ExternalLink { uri } => {
                                                                li.menu-item {
                                                                    @let icon = match &item.icon {
                                                                        Some(icon) => html!{(icon) " "},
                                                                        None => html! {},
                                                                    };
                                                                    a.menu-link.external-link href=(uri) target="_blank" rel="noopener noreferrer" aria-label=(format!("{} (opens in a new tab)", item.label)) {
                                                                        (icon) (item.label)
                                                                        i.fas.fa-external-link-alt aria-hidden="true" {}
                                                                    }
                                                                }
                                                            }
                                                            approck::MenuItemInner::SubMenu { .. } => {
                                                                // This is the flyout
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }

                            }
                        }
                        @match Base::get_body_display(self) {
                            BodyDisplay::Fluid => {
                                content-container.fluid {
                                    (Base::render_body_inner(self))
                                }
                            },
                            BodyDisplay::Container => {
                                content-container {
                                    (Base::render_body_inner(self))
                                }
                            },
                            BodyDisplay::Narrow => {
                                content-container.narrow {
                                    (Base::render_body_inner(self))
                                }
                            },
                            BodyDisplay::Plain => {
                                bux-content-wrapper.plain {
                                    (Base::render_body_inner(self))
                                }
                            },
                            BodyDisplay::Fixed => {
                                bux-content-wrapper.fixed {
                                    (Base::render_body_inner(self))
                                }
                            },
                        }
                    }
                    footer {
                        p id="footer-copyright" {
                            small { "Copyright © 2025 Smart Retirement Corporation. All rights reserved." }
                        }
                    }
                }
            )
        }
    }

    impl bux::document::Nav1 for DocumentRRR {}
    impl bux::document::Nav2 for DocumentRRR {}
    impl bux::document::PageNav for DocumentRRR {}

    impl rrr_agent::DocumentRRR for DocumentRRR {}
    impl rrr_public::DocumentRRR for DocumentRRR {}
    impl crate::DocumentRRR for DocumentRRR {}
    impl auth_fence::DocumentPublic for DocumentRRR {}
}

# Plan Calculation Summary

This document provides a detailed overview of how the debt payoff plan is calculated in the df4l-zero system.

## Overview

The plan calculation system creates two contrasting debt payoff scenarios to demonstrate the financial impact of using life insurance policy loans:

### Scenario Comparison

| Aspect                 | Minimum Payment Scenario                | D2C (Debt-to-Cash) Scenario                              |
| ---------------------- | --------------------------------------- | -------------------------------------------------------- |
| **Strategy**           | Traditional debt management             | Accelerated payoff using policy loans                    |
| **Payment Method**     | Only minimum required payments          | Debt snowball + policy loan payoffs                      |
| **Interest Sources**   | Only debt interest (credit cards, etc.) | Debt interest + 5.3% policy loan interest                |
| **Payment Allocation** | Fixed minimum to each debt              | All payment capacity focused on smallest debt            |
| **Debt Elimination**   | Gradual reduction over time             | Immediate payoff via policy loans                        |
| **Cash Flow**          | Consistent monthly payments             | Variable: high initially, decreasing as debts eliminated |
| **Timeline**           | Typically much longer                   | Significantly accelerated                                |
| **Total Interest**     | Usually much higher                     | Often dramatically lower despite loan interest           |

## Core Components

### Input Structure (`PlanInput`)

- `policy_start_date`: When the insurance policy begins
- `current_date`: The calculation reference point
- `debts`: Array of client debts with balances, rates, and payment amounts
- `policy_output`: Insurance policy projections with cash values and borrowing capacity

### Output Structure (`PlanOutput`)

- Monthly debt payment totals
- Payoff timelines for both scenarios (D2C vs minimum payments)
- Interest paid comparisons
- Month-by-month debt and loan projections
- Final debt balances with current date as balance date

## Calculation Process

### Phase 1: Debt Preparation

1. **Starting Balance Calculation**: For each debt, calculate the current balance by applying monthly interest and payments from the original balance date to the current date
2. **Debt Sorting**: Sort debts by balance amount (smallest first) for debt snowball strategy
3. **Payment Aggregation**: Sum all monthly debt payments to determine total available payment capacity

### Phase 2: Scenario Calculations

#### D2C (Debt-to-Cash) Scenario - Accelerated Payoff Strategy

**Philosophy**: Use policy loans to immediately eliminate debts, then redirect all debt payments to rapidly repay the policy loan at a lower interest rate.

**Monthly Process**:

1. **Loan Interest Calculation**: Apply 5.3% annual interest (0.44% monthly) to existing policy loan balance
2. **Debt Interest Charges**: Calculate monthly interest on remaining debt balances
3. **Minimum Debt Payments**: Apply required monthly payments to all remaining debts
4. **Debt Snowball**: Focus ALL available payment capacity on the smallest remaining debt
5. **Policy Loan Payoffs**: When policy has sufficient credit available, immediately pay off entire debt balances (smallest first)
6. **Payment Reallocation**: Once a debt is eliminated, redirect its monthly payment to policy loan repayment

**Key Advantages**:

- Eliminates high-interest debt immediately (often 18-29% APR credit cards)
- Replaces multiple debt payments with single loan repayment at 5.3%
- Accelerates overall payoff timeline dramatically
- Simplifies monthly cash flow management

**Key Mechanics**:

- **Available Credit**: 90% of policy cash value minus current loan balance
- **Payoff Priority**: Smallest debt balances first (debt snowball method)
- **Interest Rate Arbitrage**: Replace high-rate debt with 5.3% policy loan
- **Compounding Effect**: Each paid-off debt increases loan repayment capacity

#### Minimum Payment Scenario - Traditional Approach

**Philosophy**: Make only the required minimum payments on each debt, allowing natural amortization over time.

**Monthly Process**:

1. **Interest Charges**: Apply monthly interest to each debt balance (often 18-29% APR)
2. **Minimum Payments**: Make only the required minimum payment on each debt
3. **Slow Principal Reduction**: Small portion of each payment reduces principal
4. **No Acceleration**: No extra payments or strategic debt elimination
5. **Extended Timeline**: Debts paid off according to their individual amortization schedules

**Key Characteristics**:

- Maintains status quo debt management
- Highest total interest paid over time
- Longest payoff timeline
- Multiple ongoing monthly payments
- No strategic debt elimination

**Why This Scenario Often Fails**:

- Minimum payments barely cover interest on high-rate debt
- Principal reduction is extremely slow
- Total interest can exceed original debt amounts
- Psychological burden of multiple ongoing payments

### Phase 3: Comparative Results Analysis

The system generates side-by-side comparisons to demonstrate the dramatic differences between approaches:

#### Timeline Comparison

- **D2C Payoff Months**: Often 24-60 months for complete debt freedom (including policy loan repayment)
- **Minimum Payment Months**: Typically 120-360+ months, sometimes never fully paid off
- **Time Savings**: D2C usually achieves debt freedom 5-15 years faster

#### Interest Cost Analysis

- **D2C Total Interest**: Debt interest (until payoff) + policy loan interest at 5.3%
- **Minimum Payment Interest**: Full debt interest over extended timeline at 18-29% rates
- **Interest Savings**: D2C typically saves $50,000-$200,000+ in interest costs

#### Cash Flow Impact

- **D2C Pattern**: High initial payments → decreasing over time → complete freedom
- **Minimum Payment Pattern**: Consistent payments → extended indefinitely
- **Freedom Timeline**: D2C achieves complete payment freedom years earlier

## Key Algorithms

### Starting Balance Calculation

```
For each debt:
1. Start with original balance at balance_date
2. For each month from balance_date to current_date:
   - Add monthly interest charge
   - Subtract monthly payment
   - Update running balance
3. Result is the debt balance as of current_date
```

### D2C Debt Snowball Implementation

```
For each month in D2C scenario:
1. Sort debts by current balance (smallest first)
2. Apply minimum payments to all debts
3. Apply ALL extra payment capacity to smallest debt
4. If policy loan available and sufficient:
   - Pay off smallest debt entirely with loan
   - Redirect debt's monthly payment to loan repayment
5. Repeat until all debts and loans are paid off
```

### Minimum Payment Implementation

```
For each month in minimum payment scenario:
1. Apply monthly interest to each debt balance
2. Make only minimum required payment on each debt
3. Continue until natural amortization completes
4. No strategic acceleration or loan utilization
```

### Policy Loan Management

```
Available Credit = (Policy Cash Value × 0.90) - Current Loan Balance
Monthly Loan Interest = Loan Balance × (0.053 ÷ 12)
Loan Repayment = Sum of monthly payments from paid-off debts
```

## Output Metrics

### Financial Comparisons

- **Total Interest Paid**: D2C scenario vs minimum payments
- **Payoff Timeline**: Months to complete debt freedom
- **Monthly Cash Flow**: Payment amounts and loan servicing

### Monthly Projections

- **Debt Balances**: Running balances for each debt
- **Loan Activity**: Draws, payments, interest, and balances
- **Payment Allocation**: How monthly payments are distributed

## Error Handling

- Validates debt balance dates (cannot be more than 1 year old)
- Ensures sufficient policy borrowing capacity
- Handles edge cases like zero balances and paid-off debts

This calculation system enables clients to see the financial impact of using their life insurance policy's cash value to accelerate debt payoff through strategic borrowing and the debt snowball method.

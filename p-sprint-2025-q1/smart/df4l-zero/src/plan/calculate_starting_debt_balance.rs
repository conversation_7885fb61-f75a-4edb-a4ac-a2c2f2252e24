use crate::debt::Debt;
use chrono::Months;
use granite::{DateUtc, Decimal};

/// Apply the monthly interest and payments from the balance date until the start date to determine the starting balance
pub fn calculate(debt: &Debt, start_date: DateUtc) -> Result<Decimal, String> {
    if debt.balance_date > start_date {
        return Err("Balance date is after start date.".to_string());
    }

    // If balance date equals start date, no calculation needed
    if debt.balance_date == start_date {
        return Ok(debt.balance);
    }

    // Start with the debt balance at the balance date
    let mut current_balance = debt.balance;
    let mut iter_date = debt.balance_date;
    let mut iterations = 0;

    // Apply monthly interest and payments until we reach the start date
    while iter_date < start_date {
        iterations += 1;
        if iterations > 13 {
            return Err(format!(
                "The balance on debt `{}` is more than 1 year out of date.",
                debt.name
            ));
        }

        // Move to the next month
        iter_date = match iter_date.checked_add_months(Months::new(1)) {
            Some(date) => date,
            None => return Err("Date overflow while calculating months".to_string()),
        };

        // Calculate monthly interest - convert annual rate to monthly decimal
        let monthly_interest = (current_balance
            * (debt.annual_interest_percentage / Decimal::new(100, 0))
            / Decimal::new(12, 0))
        .round_dp(2);

        // Apply interest and subtract payment
        current_balance = current_balance + monthly_interest - debt.monthly_payment_amount;

        // If balance goes negative, set to zero (debt is paid off)
        if current_balance < Decimal::ZERO {
            current_balance = Decimal::ZERO;
            break;
        }

        // If we've reached or passed the start date, break
        if iter_date >= start_date {
            break;
        }
    }

    Ok(current_balance.round_dp(2))
}

#[cfg(test)]
mod tests {
    use super::*;
    use granite::{NaiveDate, Uuid};

    fn create_test_debt(
        balance: Decimal,
        balance_date: DateUtc,
        annual_interest_percentage: Decimal,
        monthly_payment_amount: Decimal,
    ) -> Debt {
        let debt_row = crate::debt::DebtRow {
            client_debt_uuid: Uuid::new_v4(),
            client_uuid: Uuid::new_v4(),
            client_debt_esid: None,
            name: Some("Test Debt".to_string()),
            balance: Some(balance),
            balance_date: Some(balance_date),
            annual_interest_percentage: Some(annual_interest_percentage),
            monthly_payment_amount: Some(monthly_payment_amount),
            active: true,
        };

        Debt::new(balance_date, &debt_row).unwrap()
    }

    #[test]
    fn test_same_date_no_calculation() {
        let debt = create_test_debt(
            Decimal::new(1000, 0), // $1000.00
            NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
            Decimal::new(12, 0),  // 12%
            Decimal::new(100, 0), // $100.00
        );
        let start_date = NaiveDate::from_ymd_opt(2024, 1, 1).unwrap();

        let result = calculate(&debt, start_date).unwrap();
        assert_eq!(result, Decimal::new(1000, 0));
    }

    #[test]
    fn test_balance_date_after_start_date_error() {
        let debt = create_test_debt(
            Decimal::new(1000, 0),
            NaiveDate::from_ymd_opt(2024, 2, 1).unwrap(),
            Decimal::new(12, 0),
            Decimal::new(100, 0),
        );
        let start_date = NaiveDate::from_ymd_opt(2024, 1, 1).unwrap();

        let result = calculate(&debt, start_date);
        assert!(result.is_err());
        assert_eq!(result.unwrap_err(), "Balance date is after start date.");
    }

    #[test]
    fn test_one_month_calculation() {
        let debt = create_test_debt(
            Decimal::new(1000, 0), // $1000.00
            NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
            Decimal::new(12, 0),  // 12% annual = 1% monthly
            Decimal::new(100, 0), // $100.00
        );
        let start_date = NaiveDate::from_ymd_opt(2024, 2, 1).unwrap();

        let result = calculate(&debt, start_date).unwrap();
        // Expected: $1000 + $10 interest - $100 payment = $910
        assert_eq!(result, Decimal::new(910, 0));
    }

    #[test]
    fn test_multiple_months_calculation() {
        let debt = create_test_debt(
            Decimal::new(1000, 0), // $1000.00
            NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
            Decimal::new(12, 0), // 12% annual = 1% monthly
            Decimal::new(50, 0), // $50.00
        );
        let start_date = NaiveDate::from_ymd_opt(2024, 3, 1).unwrap(); // 2 months later

        let result = calculate(&debt, start_date).unwrap();
        // Month 1: $1000 + $10 - $50 = $960
        // Month 2: $960 + $9.60 - $50 = $919.60
        assert_eq!(result, Decimal::new(91960, 2)); // $919.60
    }

    #[test]
    fn test_debt_paid_off() {
        let debt = create_test_debt(
            Decimal::new(100, 0), // $100.00
            NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
            Decimal::new(12, 0),  // 12% annual = 1% monthly
            Decimal::new(200, 0), // $200.00 (more than balance + interest)
        );
        let start_date = NaiveDate::from_ymd_opt(2024, 2, 1).unwrap();

        let result = calculate(&debt, start_date).unwrap();
        // Expected: $100 + $1 interest - $200 payment = $0 (debt paid off)
        assert_eq!(result, Decimal::ZERO);
    }

    #[test]
    fn test_zero_interest_rate() {
        let debt = create_test_debt(
            Decimal::new(1000, 0), // $1000.00
            NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
            Decimal::ZERO,        // 0% interest
            Decimal::new(100, 0), // $100.00
        );
        let start_date = NaiveDate::from_ymd_opt(2024, 2, 1).unwrap();

        let result = calculate(&debt, start_date).unwrap();
        // Expected: $1000 + $0 interest - $100 payment = $900
        assert_eq!(result, Decimal::new(900, 0));
    }

    #[test]
    fn test_zero_payment() {
        let debt = create_test_debt(
            Decimal::new(1000, 0), // $1000.00
            NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
            Decimal::new(12, 0), // 12% annual = 1% monthly
            Decimal::ZERO,       // $0.00 payment
        );
        let start_date = NaiveDate::from_ymd_opt(2024, 2, 1).unwrap();

        let result = calculate(&debt, start_date).unwrap();
        // Expected: $1000 + $10 interest - $0 payment = $1010
        assert_eq!(result, Decimal::new(1010, 0));
    }

    #[test]
    fn test_max_iterations_exceeded() {
        let debt = create_test_debt(
            Decimal::new(1000, 0),
            NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
            Decimal::new(12, 0),
            Decimal::new(5, 0), // Very small payment so debt won't be paid off
        );
        let start_date = NaiveDate::from_ymd_opt(2025, 3, 1).unwrap(); // 14 months later

        let result = calculate(&debt, start_date);
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err(),
            "The balance on debt `Test Debt` is more than 1 year out of date."
        );
    }

    #[test]
    fn test_rounding_precision() {
        let debt = create_test_debt(
            Decimal::new(100033, 2), // $1000.33
            NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
            Decimal::new(1234, 2),  // 12.34% annual
            Decimal::new(10567, 2), // $105.67
        );
        let start_date = NaiveDate::from_ymd_opt(2024, 2, 1).unwrap();

        let result = calculate(&debt, start_date).unwrap();
        // Should be rounded to 2 decimal places
        assert_eq!(result.scale(), 2);
    }
}

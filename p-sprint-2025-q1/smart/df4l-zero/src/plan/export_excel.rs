use super::generate_grid::{CellValue, Grid};
use bux::{format_currency_us_2, format_date_us};
use rust_xlsxwriter::{Color, Format, Workbook, Worksheet};

pub fn export_excel(plan_output: &super::PlanOutput) -> granite::Result<Vec<u8>> {
    // Generate grids
    let d2c_grid = plan_output.generate_d2c_grid();
    let min_grid = plan_output.generate_min_grid();

    // Create a new Excel workbook in memory
    let mut workbook = Workbook::new();

    // Create and write D2C sheet
    {
        let d2c_worksheet = workbook.add_worksheet();
        d2c_worksheet.set_name("D2C Debt Snowball")?;
        write_grid_to_worksheet(d2c_worksheet, &d2c_grid)?;
    }

    // Create and write Min sheet
    {
        let min_worksheet = workbook.add_worksheet();
        min_worksheet.set_name("Minimum Payment")?;
        write_grid_to_worksheet(min_worksheet, &min_grid)?;
    }

    // Save to memory buffer
    let buffer = workbook.save_to_buffer()?;
    Ok(buffer)
}

fn write_grid_to_worksheet(worksheet: &mut Worksheet, grid: &Grid) -> granite::Result<()> {
    let mut current_row = 0u32;

    // Write header rows
    for header_row in &grid.header_rows {
        let mut current_col = 0u16;

        for header_cell in &header_row.cells {
            // Create format for header cell
            let mut header_format = Format::new()
                .set_bold()
                .set_align(rust_xlsxwriter::FormatAlign::Center)
                .set_border(rust_xlsxwriter::FormatBorder::Thin);

            // Set background color
            if let Ok(color) = parse_hex_color(&header_cell.background_color) {
                header_format = header_format.set_background_color(color);
            }

            // Write the header cell
            worksheet.write_string_with_format(
                current_row,
                current_col,
                &header_cell.label,
                &header_format,
            )?;

            // Handle colspan by merging cells if needed
            if header_cell.colspan > 1 {
                let end_col = current_col + header_cell.colspan as u16 - 1;
                worksheet.merge_range(
                    current_row,
                    current_col,
                    current_row,
                    end_col,
                    &header_cell.label,
                    &header_format,
                )?;
                current_col = end_col + 1;
            } else {
                current_col += 1;
            }
        }
        current_row += 1;
    }

    // Write data rows
    for data_row in &grid.data_rows {
        for (current_col, (cell_index, cell)) in data_row.cells.iter().enumerate().enumerate() {
            let current_col = current_col as u16;
            // Create format for data cell
            let mut cell_format = Format::new().set_border(rust_xlsxwriter::FormatBorder::Thin);

            // Set background color
            if let Ok(color) = parse_hex_color(&cell.background_color) {
                cell_format = cell_format.set_background_color(color);
            }

            // Set alignment based on cell type and position
            if cell_index < 2 {
                // First two columns (Month, Date) - center aligned
                cell_format = cell_format.set_align(rust_xlsxwriter::FormatAlign::Center);
            } else {
                // Other columns - right aligned for numbers/currency
                cell_format = cell_format.set_align(rust_xlsxwriter::FormatAlign::Right);
            }

            // Write cell value based on type
            match &cell.value {
                Some(CellValue::Number(num)) => {
                    worksheet.write_number_with_format(
                        current_row,
                        current_col,
                        *num as f64,
                        &cell_format.clone().set_bold(),
                    )?;
                }
                Some(CellValue::Date(date)) => {
                    worksheet.write_string_with_format(
                        current_row,
                        current_col,
                        format_date_us(*date),
                        &cell_format,
                    )?;
                }
                Some(CellValue::Currency(amount)) => {
                    if amount.is_zero() {
                        let gray_format = cell_format.clone().set_font_color(Color::RGB(0x999999));
                        worksheet.write_string_with_format(
                            current_row,
                            current_col,
                            "-",
                            &gray_format,
                        )?;
                    } else {
                        worksheet.write_string_with_format(
                            current_row,
                            current_col,
                            format_currency_us_2(*amount),
                            &cell_format,
                        )?;
                    }
                }
                None => {
                    worksheet.write_string_with_format(
                        current_row,
                        current_col,
                        "",
                        &cell_format,
                    )?;
                }
            }
        }
        current_row += 1;
    }

    // Auto-fit columns for better readability
    for col in 0..50u16 {
        // Adjust range as needed
        worksheet.set_column_width(col, 12.0)?;
    }

    // Freeze the top 2 rows and left 2 columns
    // This will pin the header rows and the first two data columns (Month, Date)
    worksheet.set_freeze_panes(2, 2)?;

    Ok(())
}

fn parse_hex_color(hex: &str) -> Result<Color, &'static str> {
    if !hex.starts_with('#') || hex.len() != 7 {
        return Err("Invalid hex color format");
    }

    let hex = &hex[1..]; // Remove the '#'
    let r = u8::from_str_radix(&hex[0..2], 16).map_err(|_| "Invalid red component")?;
    let g = u8::from_str_radix(&hex[2..4], 16).map_err(|_| "Invalid green component")?;
    let b = u8::from_str_radix(&hex[4..6], 16).map_err(|_| "Invalid blue component")?;

    Ok(Color::RGB(
        ((r as u32) << 16) | ((g as u32) << 8) | (b as u32),
    ))
}

use granite::{DateUtc, Decimal};

#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub enum CellValue {
    Number(u32),
    Date(DateUtc),
    Currency(Decimal),
}

pub struct Grid {
    pub header_rows: Vec<HeaderRow>,
    pub data_rows: Vec<Row>,
}

pub struct HeaderRow {
    pub cells: Vec<HeaderCell>,
}

pub struct HeaderCell {
    pub label: String,
    pub colspan: u32,
    pub background_color: String,
}

pub struct Row {
    pub cells: Vec<Cell>,
}

pub struct Cell {
    pub value: Option<CellValue>,
    pub background_color: String,
}

// Pastel colors for different debts
const DEBT_COLORS: &[&str] = &[
    "#ffcdd2", // Light red
    "#f8bbd9", // Light pink
    "#e1bee7", // Light purple
    "#d1c4e9", // Light deep purple
    "#c5cae9", // Light indigo
    "#bbdefb", // Light blue
    "#b3e5fc", // Light light blue
    "#b2ebf2", // Light cyan
    "#b2dfdb", // Light teal
    "#c8e6c9", // Light green
    "#dcedc8", // Light light green
    "#f0f4c3", // Light lime
    "#fff9c4", // Light yellow
    "#ffecb3", // Light amber
    "#ffe0b2", // Light orange
    "#ffccbc", // Light deep orange
];

pub fn generate_d2c_grid(plan_output: &super::PlanOutput) -> Grid {
    let mut header_rows = Vec::new();
    let mut data_rows = Vec::new();

    // Create first header row with group headers
    let mut group_header_cells = Vec::new();
    group_header_cells.push(HeaderCell {
        label: "Basic Info".to_string(),
        colspan: 2,
        background_color: "#e8f4f8".to_string(),
    });
    group_header_cells.push(HeaderCell {
        label: "Policy Data".to_string(),
        colspan: 6,
        background_color: "#e3f2fd".to_string(),
    });
    group_header_cells.push(HeaderCell {
        label: "Loan Data".to_string(),
        colspan: 5,
        background_color: "#fff3e0".to_string(),
    });

    // Add debt group headers with different colors
    for (debt_index, debt) in plan_output.debts.iter().enumerate() {
        let color = DEBT_COLORS[debt_index % DEBT_COLORS.len()];
        group_header_cells.push(HeaderCell {
            label: debt.name.clone(),
            colspan: 6,
            background_color: color.to_string(),
        });
    }

    header_rows.push(HeaderRow {
        cells: group_header_cells,
    });

    // Create second header row with specific column headers
    let mut column_header_cells = Vec::new();

    // Basic info headers
    column_header_cells.push(HeaderCell {
        label: "Month".to_string(),
        colspan: 1,
        background_color: "#e8f4f8".to_string(),
    });
    column_header_cells.push(HeaderCell {
        label: "Date".to_string(),
        colspan: 1,
        background_color: "#e8f4f8".to_string(),
    });

    // Policy headers
    let policy_headers = [
        "Cash Value Base",
        "Cash Value PUA",
        "Cash Value Total",
        "Credit Limit",
        "Credit Available Start",
        "Credit Available End",
    ];
    for header in policy_headers {
        column_header_cells.push(HeaderCell {
            label: header.to_string(),
            colspan: 1,
            background_color: "#e3f2fd".to_string(),
        });
    }

    // Loan headers
    let loan_headers = [
        "Start Balance",
        "Interest",
        "Draw",
        "Payment",
        "End Balance",
    ];
    for header in loan_headers {
        column_header_cells.push(HeaderCell {
            label: header.to_string(),
            colspan: 1,
            background_color: "#fff3e0".to_string(),
        });
    }

    // Debt headers with different colors
    let debt_headers = [
        "Start Bal",
        "Interest",
        "Regular Pay",
        "Extra Pay",
        "Payoff Pay",
        "End Bal",
    ];
    for (debt_index, _debt) in plan_output.debts.iter().enumerate() {
        let color = DEBT_COLORS[debt_index % DEBT_COLORS.len()];
        for header in debt_headers {
            column_header_cells.push(HeaderCell {
                label: header.to_string(),
                colspan: 1,
                background_color: color.to_string(),
            });
        }
    }

    header_rows.push(HeaderRow {
        cells: column_header_cells,
    });

    // Create data rows for D2C months
    for d2c_month in &plan_output.months_d2c {
        let mut cells = vec![
            // Basic month info
            Cell {
                value: Some(CellValue::Number(d2c_month.month_number)),
                background_color: "#f8f9fa".to_string(),
            },
            Cell {
                value: Some(CellValue::Date(d2c_month.date)),
                background_color: "#f8f9fa".to_string(),
            },
            // Policy columns
            Cell {
                value: Some(CellValue::Currency(d2c_month.cash_value_base)),
                background_color: "#e3f2fd".to_string(),
            },
            Cell {
                value: Some(CellValue::Currency(d2c_month.cash_value_pua)),
                background_color: "#e3f2fd".to_string(),
            },
            Cell {
                value: Some(CellValue::Currency(d2c_month.cash_value_total)),
                background_color: "#e3f2fd".to_string(),
            },
            Cell {
                value: Some(CellValue::Currency(d2c_month.credit_limit)),
                background_color: "#e3f2fd".to_string(),
            },
            Cell {
                value: Some(CellValue::Currency(d2c_month.credit_available_starting)),
                background_color: "#e3f2fd".to_string(),
            },
            Cell {
                value: Some(CellValue::Currency(d2c_month.credit_available_ending)),
                background_color: "#e3f2fd".to_string(),
            },
            // Loan columns
            Cell {
                value: Some(CellValue::Currency(d2c_month.loan_starting_balance)),
                background_color: "#fff3e0".to_string(),
            },
            Cell {
                value: Some(CellValue::Currency(d2c_month.loan_interest_amount)),
                background_color: "#fff3e0".to_string(),
            },
            Cell {
                value: Some(CellValue::Currency(d2c_month.loan_draw)),
                background_color: "#fff3e0".to_string(),
            },
            Cell {
                value: Some(CellValue::Currency(d2c_month.loan_payment)),
                background_color: "#fff3e0".to_string(),
            },
            Cell {
                value: Some(CellValue::Currency(d2c_month.loan_ending_balance)),
                background_color: "#fff3e0".to_string(),
            },
        ];

        // Debt columns with different colors
        for (debt_index, _debt) in plan_output.debts.iter().enumerate() {
            let base_color = DEBT_COLORS[debt_index % DEBT_COLORS.len()];

            if let Some(debt_d2c) = d2c_month.debts.get(debt_index) {
                cells.push(Cell {
                    value: Some(CellValue::Currency(debt_d2c.starting_balance)),
                    background_color: base_color.to_string(),
                });
                cells.push(Cell {
                    value: Some(CellValue::Currency(debt_d2c.interest_charge)),
                    background_color: base_color.to_string(),
                });
                cells.push(Cell {
                    value: Some(CellValue::Currency(debt_d2c.regular_payment)),
                    background_color: base_color.to_string(),
                });
                cells.push(Cell {
                    value: Some(CellValue::Currency(debt_d2c.extra_payment)),
                    background_color: base_color.to_string(),
                });
                cells.push(Cell {
                    value: Some(CellValue::Currency(debt_d2c.payoff_payment)),
                    background_color: base_color.to_string(),
                });
                cells.push(Cell {
                    value: Some(CellValue::Currency(debt_d2c.ending_balance)),
                    background_color: base_color.to_string(),
                });
            } else {
                // Add empty cells if debt data is not available
                for _ in 0..6 {
                    cells.push(Cell {
                        value: None,
                        background_color: "#f5f5f5".to_string(),
                    });
                }
            }
        }

        data_rows.push(Row { cells });
    }

    Grid {
        header_rows,
        data_rows,
    }
}

pub fn generate_min_grid(plan_output: &super::PlanOutput) -> Grid {
    let mut header_rows = Vec::new();
    let mut data_rows = Vec::new();

    // Create first header row with group headers for Min Payment scenario
    let mut group_header_cells = Vec::new();
    group_header_cells.push(HeaderCell {
        label: "Basic Info".to_string(),
        colspan: 2,
        background_color: "#e8f4f8".to_string(),
    });

    // Add debt group headers with different colors
    for (debt_index, debt) in plan_output.debts.iter().enumerate() {
        let color = DEBT_COLORS[debt_index % DEBT_COLORS.len()];
        group_header_cells.push(HeaderCell {
            label: debt.name.clone(),
            colspan: 4,
            background_color: color.to_string(),
        });
    }

    header_rows.push(HeaderRow {
        cells: group_header_cells,
    });

    // Create second header row with specific column headers
    let mut column_header_cells = Vec::new();

    // Basic info headers
    column_header_cells.push(HeaderCell {
        label: "Month".to_string(),
        colspan: 1,
        background_color: "#e8f4f8".to_string(),
    });
    column_header_cells.push(HeaderCell {
        label: "Date".to_string(),
        colspan: 1,
        background_color: "#e8f4f8".to_string(),
    });

    // Debt headers with different colors
    let debt_headers = ["Start Bal", "Interest", "Regular Pay", "End Bal"];
    for (debt_index, _debt) in plan_output.debts.iter().enumerate() {
        let color = DEBT_COLORS[debt_index % DEBT_COLORS.len()];
        for header in debt_headers {
            column_header_cells.push(HeaderCell {
                label: header.to_string(),
                colspan: 1,
                background_color: color.to_string(),
            });
        }
    }

    header_rows.push(HeaderRow {
        cells: column_header_cells,
    });

    // Create data rows for Min months
    for min_month in &plan_output.months_min {
        let mut cells = Vec::new();

        // Basic month info
        cells.push(Cell {
            value: Some(CellValue::Number(min_month.month_number)),
            background_color: "#f8f9fa".to_string(),
        });
        cells.push(Cell {
            value: Some(CellValue::Date(min_month.date)),
            background_color: "#f8f9fa".to_string(),
        });

        // Debt columns with different colors
        for (debt_index, _debt) in plan_output.debts.iter().enumerate() {
            let base_color = DEBT_COLORS[debt_index % DEBT_COLORS.len()];

            if let Some(debt_min) = min_month.debts.get(debt_index) {
                cells.push(Cell {
                    value: Some(CellValue::Currency(debt_min.starting_balance)),
                    background_color: base_color.to_string(),
                });
                cells.push(Cell {
                    value: Some(CellValue::Currency(debt_min.interest_charge)),
                    background_color: base_color.to_string(),
                });
                cells.push(Cell {
                    value: Some(CellValue::Currency(debt_min.regular_payment)),
                    background_color: base_color.to_string(),
                });
                cells.push(Cell {
                    value: Some(CellValue::Currency(debt_min.ending_balance)),
                    background_color: base_color.to_string(),
                });
            } else {
                // Add empty cells if debt data is not available
                for _ in 0..4 {
                    cells.push(Cell {
                        value: None,
                        background_color: "#f5f5f5".to_string(),
                    });
                }
            }
        }

        data_rows.push(Row { cells });
    }

    Grid {
        header_rows,
        data_rows,
    }
}

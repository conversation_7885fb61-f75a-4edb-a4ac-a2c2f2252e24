pub mod calculate_starting_debt_balance;
pub mod export_excel;
pub mod generate_grid;

use crate::debt::Debt;
use crate::policy::PolicyOutput;
use granite::{DateUtc, Decimal, Uuid};
use itertools::Itertools;

#[derive(Debug)]
pub struct PlanInput<'a> {
    pub policy_start_date: DateUtc,
    pub current_date: DateUtc,
    pub debts: &'a [Debt],
    pub policy_output: PolicyOutput,
}

#[derive(Debug)]
pub struct PlanOutput {
    pub debt_principal_total: Decimal,

    pub monthly_payment_total: Decimal,
    pub monthly_interest_total: Decimal,
    pub monthly_principal_total: Decimal,
    pub monthly_payment_percent_that_is_interest: Decimal,
    pub monthly_payment_percent_that_is_principal: Decimal,

    pub d2c_payoff_in_months: u32,
    pub d2c_interest_total: Decimal,
    pub d2c_principal_total: Decimal,
    pub d2c_payments_total: Decimal,
    pub d2c_payments_percent_that_is_interest: Decimal,
    pub d2c_payments_percent_that_is_principal: Decimal,

    pub min_payoff_in_months: u32,
    pub min_interest_total: Decimal,
    pub min_principal_total: Decimal,
    pub min_payments_total: Decimal,
    pub min_payments_percent_that_is_interest: Decimal,
    pub min_payments_percent_that_is_principal: Decimal,

    pub benefit_interest_savings: Decimal,
    pub benefit_20year_cash_value: Decimal,
    pub benefit_total: Decimal,

    /// Structured payoff information for each debt
    pub payoff_items: Vec<PayoffItem>,

    /// This is the list of debts with the current_date being the balance date
    pub debts: Vec<Debt>,

    /// This is the plan of months for the D2C DEBT SNOWBALL SCENARIO
    pub months_d2c: Vec<MonthD2C>,

    /// This is the plan of months for the MINIMUM PAYMENT SCENARIO
    pub months_min: Vec<MonthMin>,

    /// This is the policy output (copied from the input)
    pub policy: PolicyOutput,
}

#[derive(Debug)]
#[granite::gtype]
pub struct PayoffItem {
    /// Unique identifier for this debt
    pub debt_uuid: Uuid,

    /// Name of the debt (e.g., "Credit Card", "Auto Loan")
    pub debt_name: String,

    /// Current balance of the debt
    pub total_balance: Decimal,

    /// Date when debt is paid off in minimum payment scenario
    pub payoff_date_min: DateUtc,

    /// Date when debt is paid off in D2C scenario
    pub payoff_date_d2c: DateUtc,

    /// Month number when debt is paid off in minimum payment scenario
    pub payoff_month_min: u32,

    /// Month number when debt is paid off in D2C scenario
    pub payoff_month_d2c: u32,

    /// Total interest paid over life of debt in minimum payment scenario
    pub total_interest_paid_min: Decimal,

    /// Total interest paid over life of debt in D2C scenario
    pub total_interest_paid_d2c: Decimal,

    /// Interest savings from using D2C vs minimum payments
    pub interest_savings: Decimal,

    /// Time savings in months from using D2C vs minimum payments
    pub time_savings_months: u32,
}

#[derive(Debug)]
pub struct MonthD2C {
    /// First entry will be 1 month after policy start date
    pub date: DateUtc,

    /// this is a 1 based number, 1-360
    pub month_number: u32,

    /// Current cash value base (from policy)
    pub cash_value_base: Decimal,

    /// Current cash value pua (from pua additions - doesn't get impacted by any loans)
    pub cash_value_pua: Decimal,

    /// Combined cash value at this month
    pub cash_value_total: Decimal,

    /// Credit limit is 90% of cash value
    pub credit_limit: Decimal,

    /// At the beginning of the calculations, how much credit is available to borrow
    pub credit_available_starting: Decimal,

    /// At the end of the calculations, how much credit is available to borrow
    pub credit_available_ending: Decimal,

    /// How much of the credit limit is used in policy loan(s)
    pub loan_starting_balance: Decimal,

    /// Interest is 5.3% on GBU cash value loans
    pub loan_interest_amount: Decimal,

    /// How much was drawn against the loan to pay off a debt
    pub loan_draw: Decimal,

    /// How much was paid against the loan
    pub loan_payment: Decimal,

    /// Final loan balance
    pub loan_ending_balance: Decimal,

    /// A ordered vec of debt status this month which matches the order of the vec in the Plan struct
    pub debts: Vec<MonthDebtD2C>,

    // Are all debts paid off including the loan?
    pub paid_off: bool,
}

#[derive(Debug)]
pub struct MonthMin {
    /// First entry will be 1 month after policy start date
    pub date: DateUtc,

    /// this is a 1 based number, 1-360
    pub month_number: u32,

    /// A ordered vec of debt status this month which matches the order of the vec in the Plan struct
    pub debts: Vec<MonthDebtMin>,

    // Are all debts paid off?
    pub paid_off: bool,
}

#[derive(Debug)]
pub struct MonthDebtD2C {
    /// (duplicate) date of this monthly calculation
    pub date: DateUtc,

    /// (duplicate) 1 based month number
    pub month_number: u32,

    /// (duplicate) client_debt_uuid
    pub client_debt_uuid: Uuid,

    /// (duplicate) debt name
    pub name: String,

    /// Starting balance for this debt
    pub starting_balance: Decimal,

    /// Interest charged this month
    pub interest_charge: Decimal,

    /// Minimum payment paid this month
    pub regular_payment: Decimal,

    /// Extra payment paid this month
    pub extra_payment: Decimal,

    /// Payoff payment paid this month
    pub payoff_payment: Decimal,

    /// Ending balance for this debt
    pub ending_balance: Decimal,

    /// Is this debt paid off at the end of this month?
    pub paid_off: bool,
}

#[derive(Debug)]
pub struct MonthDebtMin {
    /// (duplicate) date of this monthly calculation
    pub date: DateUtc,

    /// (duplicate) 1 based month number
    pub month_number: u32,

    /// (duplicate) client_debt_uuid
    pub client_debt_uuid: Uuid,

    /// (duplicate) debt name
    pub name: String,

    /// Starting balance for this debt
    pub starting_balance: Decimal,

    /// Interest charged this month
    pub interest_charge: Decimal,

    /// Minimum payment paid this month
    pub regular_payment: Decimal,

    /// Ending balance for this debt
    pub ending_balance: Decimal,

    /// Is this debt paid off at the end of this month?
    pub paid_off: bool,
}

impl PlanInput<'_> {
    pub fn into_plan_output(self) -> Result<PlanOutput, String> {
        // Calculate:
        // 1. a view of the debts with the current_date being the balance date using the
        //    calculate_starting_debt_balance function.
        // 2. the sum of the monthly_payment_amount from each debt to be used to determine
        //    how much can be paid extra on the smallest debt
        let (debts, monthly_debt_payment_total) = {
            let mut debts = Vec::with_capacity(self.debts.len());
            let mut monthly_debt_payment_total = Decimal::ZERO;

            // Filter out zero balance debts, and advance the balance date to the current date
            // Validation of debt requires that the payment be > 0 if the balance > 0
            for debt in self.debts.iter() {
                if debt.balance > Decimal::ZERO {
                    debts.push(debt.clone_with_current_balance_info(self.current_date)?);
                    monthly_debt_payment_total += debt.monthly_payment_amount;
                }
            }

            // The order is crucial to the correct paydown of small debts first.
            // This must come after the balance date is advanced to the current date above.
            debts.sort_by_key(|d| d.balance);

            (debts, monthly_debt_payment_total)
        };

        let mut d2c_interest_total_per_debt: Vec<Decimal> =
            debts.iter().map(|_| Decimal::ZERO).collect();
        let mut min_interest_total_per_debt: Vec<Decimal> =
            debts.iter().map(|_| Decimal::ZERO).collect();
        let mut d2c_payoff_dates_per_debt: Vec<Option<DateUtc>> =
            debts.iter().map(|_| None).collect();
        let mut min_payoff_dates_per_debt: Vec<Option<DateUtc>> =
            debts.iter().map(|_| None).collect();
        let mut d2c_payoff_month_per_debt: Vec<Option<u32>> = debts.iter().map(|_| None).collect();
        let mut min_payoff_month_per_debt: Vec<Option<u32>> = debts.iter().map(|_| None).collect();

        // CALCULATE THE D2C SNOWBALL SCENARIO
        let months_d2c = {
            // These variables hold the running balance as we iterate
            let mut running_loan_balance = Decimal::ZERO;
            let mut running_debt_balances: Vec<Decimal> = debts.iter().map(|d| d.balance).collect();

            // Iterate over each month (and within, each debt)
            let mut months = Vec::with_capacity(self.policy_output.month_list.len());
            for policy_month in &self.policy_output.month_list {
                // Setup the loan parameters for this  month
                let loan_starting_balance = running_loan_balance;

                // How much credit is available to borrow at the start of the month
                let credit_available_starting =
                    policy_month.amount_available_to_borrow - loan_starting_balance;

                // loan is at 5.3% interest (0.0053 per year)
                let loan_interest_amount = (loan_starting_balance
                    * (Decimal::new(53, 4) / Decimal::new(12, 0)))
                .trunc_with_scale(2);

                // Increment running loan balance by the interest amount
                running_loan_balance += loan_interest_amount;

                // Will be decreased to zero as we apply payments
                let mut available_to_pay_this_month = monthly_debt_payment_total;

                // Will be increased based on available money to pay back loan
                let mut current_loan_payment = Decimal::ZERO;

                // Will be increased if a payoff is executed
                let mut current_loan_draw = Decimal::ZERO;

                let mut current_starting_balances: Vec<Decimal> =
                    debts.iter().map(|_| Decimal::ZERO).collect();
                let mut current_interest_charges: Vec<Decimal> =
                    debts.iter().map(|_| Decimal::ZERO).collect();
                let mut current_regular_payments: Vec<Decimal> =
                    debts.iter().map(|_| Decimal::ZERO).collect();
                let mut current_extra_payments: Vec<Decimal> =
                    debts.iter().map(|_| Decimal::ZERO).collect();
                let mut current_payoff_payments: Vec<Decimal> =
                    debts.iter().map(|_| Decimal::ZERO).collect();

                // 1. Calculate the interest for each debt
                for (index, debt) in debts.iter().enumerate() {
                    let starting_balance = running_debt_balances[index];

                    // Calculate the interest charge
                    let i = (starting_balance * debt.annual_interest_percentage
                        / Decimal::new(100, 0)
                        / Decimal::new(12, 0))
                    .trunc_with_scale(2);

                    // Store the results
                    current_starting_balances[index] = starting_balance;
                    current_interest_charges[index] = i;

                    // Accumulate the total interest paid per debt
                    d2c_interest_total_per_debt[index] += i;

                    // Increase the running debt balance
                    running_debt_balances[index] += i;
                }

                // 2. Calculate the regular payment for each debt
                for (index, debt) in debts.iter().enumerate() {
                    // Apply the maximum regular payment up to the running balance
                    let v =
                        std::cmp::min(debt.monthly_payment_amount, running_debt_balances[index]);

                    // Store the results
                    current_regular_payments[index] = v;

                    // Decrease the running balance
                    running_debt_balances[index] -= v;

                    // Reduce available to pay
                    available_to_pay_this_month -= v;
                }

                // 3. Apply any remaining available money to the loan
                if running_loan_balance > Decimal::ZERO {
                    current_loan_payment =
                        std::cmp::min(available_to_pay_this_month, running_loan_balance);
                    running_loan_balance -= current_loan_payment;
                    available_to_pay_this_month -= current_loan_payment;
                }

                // 4. Calculate the extra payment for each debt until we run out of money
                for index in 0..debts.len() {
                    // Apply the maximum extra payment up to the running balance
                    let v =
                        std::cmp::min(available_to_pay_this_month, running_debt_balances[index]);

                    // Store the results
                    current_extra_payments[index] = v;

                    // Decrease the running balance
                    running_debt_balances[index] -= v;

                    // Reduce available to pay
                    available_to_pay_this_month -= v;
                }

                // 5. Calculate the payoff payment for each debt
                for index in 0..debts.len() {
                    // available credit is the total credit limit less the running loan balance
                    let credit_available =
                        policy_month.amount_available_to_borrow - running_loan_balance;

                    // If that exceeds the current loan running balance, then execute a payoff
                    if credit_available > running_debt_balances[index] {
                        let payoff_amount = running_debt_balances[index];
                        current_loan_draw += payoff_amount;
                        current_payoff_payments[index] += payoff_amount;
                        running_debt_balances[index] -= payoff_amount;
                        running_loan_balance += payoff_amount;
                    }
                }

                // 6. Create the MonthDebtD2C struct
                let mut month_debts = Vec::new();
                for (index, debt) in debts.iter().enumerate() {
                    let starting_balance = current_starting_balances[index];
                    let interest_charge = current_interest_charges[index];
                    let regular_payment = current_regular_payments[index];
                    let extra_payment = current_extra_payments[index];
                    let payoff_payment = current_payoff_payments[index];
                    let ending_balance = starting_balance + interest_charge
                        - regular_payment
                        - extra_payment
                        - payoff_payment;

                    assert!(
                        ending_balance == running_debt_balances[index],
                        "Debt {} ending balance {} does not match running debt balance {} at month {}",
                        debt.name,
                        ending_balance,
                        running_debt_balances[index],
                        policy_month.month_number
                    );

                    let paid_off = ending_balance <= Decimal::ZERO;

                    // Record the exact date of payoff once and only once.
                    if paid_off && d2c_payoff_dates_per_debt[index].is_none() {
                        d2c_payoff_dates_per_debt[index] = Some(policy_month.date);
                        d2c_payoff_month_per_debt[index] = Some(policy_month.month_number);
                    }

                    month_debts.push(MonthDebtD2C {
                        date: policy_month.date,
                        month_number: policy_month.month_number,
                        client_debt_uuid: debt.client_debt_uuid,
                        name: debt.name.clone(),
                        starting_balance,
                        interest_charge,
                        regular_payment,
                        extra_payment,
                        payoff_payment,
                        ending_balance,
                        paid_off,
                    });
                }

                let loan_ending_balance = {
                    loan_starting_balance + loan_interest_amount + current_loan_draw
                        - current_loan_payment
                };

                assert!(
                    loan_ending_balance == running_loan_balance,
                    "Cash Value Loan ending balance {} does not match running balance {} at month {}",
                    loan_ending_balance,
                    running_loan_balance,
                    policy_month.month_number
                );

                let credit_available_ending =
                    policy_month.amount_available_to_borrow - loan_ending_balance;

                assert!(
                    credit_available_ending
                        == credit_available_starting - loan_interest_amount - current_loan_draw
                            + current_loan_payment,
                    "Credit available ending balance {} does not match running balance {} at month {}",
                    credit_available_ending,
                    credit_available_starting - current_loan_draw + current_loan_payment,
                    policy_month.month_number
                );

                // Determine if we are paid off fully
                let paid_off =
                    month_debts.iter().all(|d| d.paid_off) && loan_ending_balance <= Decimal::ZERO;

                // update the running loan balance and push the month onto the plan
                running_loan_balance = loan_ending_balance;
                months.push(MonthD2C {
                    date: policy_month.date,
                    month_number: policy_month.month_number,
                    cash_value_base: policy_month.cash_value_base,
                    cash_value_pua: policy_month.cash_value_pua,
                    cash_value_total: policy_month.cash_value_total,
                    credit_limit: policy_month.amount_available_to_borrow,
                    credit_available_starting,
                    credit_available_ending,
                    loan_starting_balance,
                    loan_interest_amount,
                    loan_draw: current_loan_draw,
                    loan_payment: current_loan_payment,
                    loan_ending_balance,
                    debts: month_debts,
                    paid_off,
                });
            }
            months
        };

        // CALCULATE THE MINIMUM PAYMENT SCENARIO
        let months_min = {
            // These variables hold the running balance as we iterate
            let mut running_debt_balances: Vec<Decimal> = debts.iter().map(|d| d.balance).collect();

            // Iterate over each month (and within, each debt)
            let mut months = Vec::with_capacity(self.policy_output.month_list.len());
            for policy_month in &self.policy_output.month_list {
                // Iterate over each debt, zipped with a mutable loan balance
                let mut month_debts = Vec::new();
                let mut all_paid_off = true;

                for (index, debt) in debts.iter().enumerate() {
                    let starting_balance = running_debt_balances[index];

                    let interest_charge = (starting_balance * debt.annual_interest_percentage
                        / Decimal::new(100, 0)
                        / Decimal::new(12, 0))
                    .trunc_with_scale(2);

                    // Accumulate the total interest paid per debt
                    min_interest_total_per_debt[index] += interest_charge;

                    let regular_payment = std::cmp::min(
                        debt.monthly_payment_amount,
                        starting_balance + interest_charge,
                    )
                    .trunc_with_scale(2);

                    let ending_balance =
                        (starting_balance + interest_charge - regular_payment).trunc_with_scale(2);

                    if ending_balance > Decimal::ZERO {
                        all_paid_off = false;
                    }

                    let paid_off = ending_balance <= Decimal::ZERO;

                    // Record the exact date of payoff once and only once.
                    if paid_off && min_payoff_dates_per_debt[index].is_none() {
                        min_payoff_dates_per_debt[index] = Some(policy_month.date);
                        min_payoff_month_per_debt[index] = Some(policy_month.month_number);
                    }

                    // Update mutable debt_balance and push MonthDebt onto the month
                    running_debt_balances[index] = ending_balance;
                    month_debts.push(MonthDebtMin {
                        date: policy_month.date,
                        month_number: policy_month.month_number,
                        client_debt_uuid: debt.client_debt_uuid,
                        name: debt.name.clone(),
                        starting_balance,
                        interest_charge,
                        regular_payment,
                        ending_balance,
                        paid_off,
                    });
                }

                let paid_off = month_debts.iter().all(|d| d.paid_off);

                months.push(MonthMin {
                    date: policy_month.date,
                    month_number: policy_month.month_number,
                    debts: month_debts,
                    paid_off,
                });

                // Exit looping early if everything is paid off
                if all_paid_off {
                    break;
                }
            }
            months
        };

        // post-process variables to remove option types, returning an error if any are still None
        let d2c_payoff_dates_per_debt = d2c_payoff_dates_per_debt
            .into_iter()
            .map(|d| d.ok_or("Missing payoff date for D2C scenario"))
            .collect::<Result<Vec<_>, _>>()?;

        // post-process variables to remove option types, returning an error if any are still None
        let min_payoff_dates_per_debt = min_payoff_dates_per_debt
            .into_iter()
            .map(|d| d.ok_or("Missing payoff date for Minimum Payment scenario"))
            .collect::<Result<Vec<_>, _>>()?;

        // post-process variables to remove option types, returning an error if any are still None
        let d2c_payoff_month_per_debt = d2c_payoff_month_per_debt
            .into_iter()
            .map(|d| d.ok_or("Missing payoff month for D2C scenario"))
            .collect::<Result<Vec<_>, _>>()?;

        // post-process variables to remove option types, returning an error if any are still None
        let min_payoff_month_per_debt = min_payoff_month_per_debt
            .into_iter()
            .map(|d| d.ok_or("Missing payoff month for Minimum Payment scenario"))
            .collect::<Result<Vec<_>, _>>()?;

        // --------------------------------------------------------------------------------------
        // Total Calculations
        let debt_principal_total = debts.iter().map(|d| d.balance).sum::<Decimal>();

        // --------------------------------------------------------------------------------------
        // Monthly Calculations
        let monthly_payment_total = debts
            .iter()
            .map(|d| d.monthly_payment_amount)
            .sum::<Decimal>();

        let monthly_interest_total = debts
            .iter()
            .map(|d| d.monthly_interest_amount)
            .sum::<Decimal>();

        let monthly_principal_total = debts
            .iter()
            .map(|d| d.monthly_principal_amount)
            .sum::<Decimal>();

        let monthly_payment_percent_that_is_interest =
            ((monthly_interest_total / monthly_payment_total) * Decimal::new(100, 0)).round();

        let monthly_payment_percent_that_is_principal =
            ((monthly_principal_total / monthly_payment_total) * Decimal::new(100, 0)).round();

        // --------------------------------------------------------------------------------------
        // D2C Scenario Calculations

        // Calculate the number of months to payoff of everything under the D2C scenario
        let d2c_payoff_in_months = months_d2c
            .iter()
            .find(|m| m.paid_off)
            .map(|m| m.month_number)
            .ok_or("D2C scenario never reaches full payoff")?;

        let d2c_interest_total = d2c_interest_total_per_debt.iter().sum::<Decimal>();

        let d2c_principal_total = debt_principal_total;

        let d2c_payments_total = d2c_interest_total + d2c_principal_total;

        let d2c_payments_percent_that_is_interest =
            ((d2c_interest_total / d2c_payments_total) * Decimal::new(100, 0)).round();

        let d2c_payments_percent_that_is_principal =
            ((d2c_principal_total / d2c_payments_total) * Decimal::new(100, 0)).round();

        // --------------------------------------------------------------------------------------
        // Minimum Payment Scenario Calculations

        let min_payoff_in_months = months_min
            .iter()
            .find(|m| m.paid_off)
            .map(|m| m.month_number)
            .ok_or("Minimum payment scenario never reaches full payoff")?;

        let min_interest_total = min_interest_total_per_debt.iter().sum::<Decimal>();

        let min_principal_total = debt_principal_total;

        let min_payments_total = min_interest_total + min_principal_total;

        let min_payments_percent_that_is_interest =
            ((min_interest_total / min_payments_total) * Decimal::new(100, 0)).round();

        let min_payments_percent_that_is_principal =
            ((min_principal_total / min_payments_total) * Decimal::new(100, 0)).round();

        // --------------------------------------------------------------------------------------
        // Benefit Calculations

        let benefit_interest_savings = min_interest_total - d2c_interest_total;

        let benefit_20year_cash_value = self
            .policy_output
            .month_list
            .get(199)
            .ok_or("Missing 20 year cash value")?
            .cash_value_total;

        let benefit_total = benefit_interest_savings + benefit_20year_cash_value;

        // --------------------------------------------------------------------------------------
        // Payoff Items
        let payoff_items: Vec<PayoffItem> = debts
            .iter()
            .enumerate()
            .map(|(index, debt)| {
                let total_interest_paid_d2c = d2c_interest_total_per_debt[index];
                let total_interest_paid_min = min_interest_total_per_debt[index];

                // Calculate interest savings
                let interest_savings = total_interest_paid_min - total_interest_paid_d2c;

                let payoff_month_d2c = d2c_payoff_month_per_debt[index];
                let payoff_month_min = min_payoff_month_per_debt[index];

                let payoff_date_d2c = d2c_payoff_dates_per_debt[index];
                let payoff_date_min = min_payoff_dates_per_debt[index];

                // Calculate time savings (difference in payoff months for this specific debt)
                let time_savings_months = payoff_month_min - payoff_month_d2c;

                PayoffItem {
                    debt_uuid: debt.client_debt_uuid,
                    debt_name: debt.name.clone(),
                    total_balance: debt.balance,
                    payoff_month_min,
                    payoff_month_d2c,
                    total_interest_paid_min,
                    total_interest_paid_d2c,
                    interest_savings,
                    payoff_date_min,
                    payoff_date_d2c,
                    time_savings_months,
                }
            })
            // Crucial sort step to ensure timeline_position_left is correct
            .sorted_by_key(|item| (item.payoff_month_d2c, item.debt_name.clone()))
            .collect();

        // --------------------------------------------------------------------------------------
        // Policy Output
        let policy = self.policy_output;

        Ok(PlanOutput {
            debt_principal_total,
            monthly_payment_total,
            monthly_interest_total,
            monthly_principal_total,
            monthly_payment_percent_that_is_interest,
            monthly_payment_percent_that_is_principal,
            d2c_payoff_in_months,
            d2c_interest_total,
            d2c_principal_total,
            d2c_payments_total,
            d2c_payments_percent_that_is_interest,
            d2c_payments_percent_that_is_principal,
            min_payoff_in_months,
            min_interest_total,
            min_principal_total,
            min_payments_total,
            min_payments_percent_that_is_interest,
            min_payments_percent_that_is_principal,
            benefit_interest_savings,
            benefit_20year_cash_value,
            benefit_total,
            payoff_items,
            debts,
            months_d2c,
            months_min,
            policy,
        })
    }
}

impl PlanOutput {
    pub fn generate_d2c_grid(&self) -> self::generate_grid::Grid {
        self::generate_grid::generate_d2c_grid(self)
    }

    pub fn generate_min_grid(&self) -> self::generate_grid::Grid {
        self::generate_grid::generate_min_grid(self)
    }

    pub fn export_excel(&self) -> granite::Result<Vec<u8>> {
        self::export_excel::export_excel(self)
    }
}

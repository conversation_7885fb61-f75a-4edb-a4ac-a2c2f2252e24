use approck_postgres::DB;

//functions defined in df4l_zero::gbu_agent::validate(gbu_agent_esid: &str)
pub async fn validate(dbcx: &impl DB, gbu_agent_esid: &str) -> granite::Result<bool> {
    // Load database record for gbu_agent_esid
    // Return bool if it is valid
    let gbu_agent_esid = gbu_agent_esid.trim();
    if gbu_agent_esid.is_empty() {
        return Ok(false);
    }

    let row = granite::pg_row_option!(
        db = dbcx;
        args = {
            $gbu_agent_esid: &gbu_agent_esid,
        };
        row = {
            gbu_agent_uuid: Uuid,
        };
        SELECT
            gbu_agent_uuid
        FROM
            df4l.gbu_agent
        WHERE active
            AND upper(gbu_agent_esid) = upper($gbu_agent_esid)
    )
    .await?;

    Ok(row.is_some())
}

use std::collections::HashMap;

use granite::Utc;
use itertools::Itertools;

/// This function returns a merged (complete) view of the manually entered debts plus
/// the primary applicant and/or spouse credit report debts.
///
/// Step 1: Start with the debts from the database that are not remote (client_debt_esid is null
/// Step 2: Add to that the applicant debts from the credit report
/// Step 3: Add to that the spouse debts from the credit report if they are not duplicates
/// Step 4: Gather the database records that are remote (client_debt_esid is not null)
/// Step 5: Hydrate step 1-3 data with {annual_interest_percentage, active} from step 4
/// Step 6: Sort the data and return it.
pub async fn get_client_debts_merged(
    app: &impl crate::App,
    client_uuid: granite::Uuid,
) -> granite::Result<Result<Vec<crate::debt::DebtRow>, String>> {
    let dbcx = app.postgres_dbcx().await?;

    let mut merged_debts = Vec::new();

    // First, get the api_crs_user_uuid_applicant from the client record
    let client_row = granite::pg_row!(
        db = dbcx;
        args = {
            $client_uuid: &client_uuid,
        };
        row = {
            api_crs_user_enabled_applicant: Option<bool>,
            api_crs_user_uuid_applicant: Option<Uuid>,
            api_crs_user_enabled_spouse: Option<bool>,
            api_crs_user_uuid_spouse: Option<Uuid>,
        };
        SELECT
            api_crs_user_enabled_applicant,
            api_crs_user_uuid_applicant,
            api_crs_user_enabled_spouse,
            api_crs_user_uuid_spouse
        FROM
            df4l.client
        WHERE
            client_uuid = $client_uuid::uuid
    )
    .await?;

    // This is a quick lookup map to be able to hydrate the data in the database.
    // If a given crs id is the same across primary applicant and spouse, we want to use the data
    // from the primary applicant.
    let mut esid_to_cr_debt_map: HashMap<String, api_crs::types::CRDebt> = HashMap::new();

    match client_row.api_crs_user_enabled_applicant {
        // Client has enabled the soft pull
        Some(true) => match client_row.api_crs_user_uuid_applicant {
            Some(api_crs_user_uuid) => {
                match api_crs::core::user::load(&dbcx, api_crs_user_uuid).await? {
                    api_crs::core::user::User::WithReport(with_report) => {
                        // Lookup the credit report debts
                        let debt_view = with_report.get_debt_view(app).await?;
                        for cr_debt in debt_view.debts {
                            // don't overwrite the debt if it already exists
                            if !esid_to_cr_debt_map.contains_key(&cr_debt.id) {
                                esid_to_cr_debt_map.insert(cr_debt.id.clone(), cr_debt);
                            }
                        }
                    }
                    _ => {
                        return Ok(Err("Primary Applicant soft credit pull setup has been started but not finished.".to_string()))
                    }
                }
            }
            None => {
                return Ok(Err(
                    "Primary Applicant soft credit pull setup has not been finished.".to_string(),
                ));
            }
        },

        // Client has disabled the soft pull
        Some(false) => (),

        // Client has not decided
        None => {
            return Ok(Err(
                "Primary Applicant soft credit pull must either be completed or disabled."
                    .to_string(),
            ));
        }
    };

    match client_row.api_crs_user_enabled_spouse {
        // Client has enabled the soft pull
        Some(true) => match client_row.api_crs_user_uuid_spouse {
            Some(api_crs_user_uuid) => {
                match api_crs::core::user::load(&dbcx, api_crs_user_uuid).await? {
                    api_crs::core::user::User::WithReport(with_report) => {
                        // Lookup the credit report debts
                        let debt_view = with_report.get_debt_view(app).await?;
                        for cr_debt in debt_view.debts {
                            // don't overwrite the debt if it already exists
                            if !esid_to_cr_debt_map.contains_key(&cr_debt.id) {
                                esid_to_cr_debt_map.insert(cr_debt.id.clone(), cr_debt);
                            }
                        }
                    }
                    _ => {
                        return Ok(Err(
                            "Spouse soft credit pull setup has been started but not finished."
                                .to_string(),
                        ));
                    }
                }
            }
            None => {
                return Ok(Err(
                    "Spouse soft credit pull setup has not been finished.".to_string()
                ));
            }
        },

        // Client has disabled the soft pull
        Some(false) => (),

        // Client has not decided
        None => (),
    };

    // Pull all the debts from the DB
    let rows = granite::pg_row_vec!(
        db = dbcx;
        args = {
            $client_uuid: &client_uuid,
        };
        row = {
            client_debt_uuid: Uuid,
            client_uuid: Uuid,
            client_debt_esid: Option<String>,
            name: Option<String>,
            balance: Option<Decimal>,
            balance_date: Option<DateUtc>,
            annual_interest_percentage: Option<Decimal>,
            monthly_payment_amount: Option<Decimal>,
            active: bool,
        };
        SELECT
            client_debt_uuid,
            client_uuid,
            client_debt_esid,
            name,
            balance,
            balance_date,
            annual_interest_percentage,
            monthly_payment_amount,
            active
        FROM
            df4l.client_debt
        WHERE
            client_uuid = $client_uuid::uuid
        ORDER BY
            client_debt_uuid
    )
    .await?;

    // Iterate through the database records and hydrate them with credit report data if available, or
    // simply add them to the output if they are manually entered.
    for row in rows {
        match row.client_debt_esid {
            // Credit report debt (e.g. active status or interest rate -- mostly empty records)
            // Only add it if it is part of the credit report mapping (e.g. still a debt)
            Some(client_debt_esid) => {
                // Use .remove() so that the remaining debts in the map can be simply added later
                if let Some(cr_debt) = esid_to_cr_debt_map.remove(&client_debt_esid) {
                    merged_debts.push(crate::debt::DebtRow {
                        client_debt_uuid: row.client_debt_uuid,
                        client_uuid: row.client_uuid,
                        client_debt_esid: Some(client_debt_esid),
                        name: Some(cr_debt.name),
                        balance: Some(cr_debt.balance),
                        balance_date: Some(Utc::now().date_naive()), //TODO: where does this come from?
                        annual_interest_percentage: row.annual_interest_percentage,
                        monthly_payment_amount: Some(cr_debt.monthly_payment),
                        active: row.active,
                    });
                }
            }
            // Regular manually entered debt -> add it to the output unmodified
            None => {
                merged_debts.push(crate::debt::DebtRow {
                    client_debt_uuid: row.client_debt_uuid,
                    client_uuid: row.client_uuid,
                    client_debt_esid: None,
                    name: row.name.clone(),
                    balance: row.balance,
                    balance_date: row.balance_date,
                    annual_interest_percentage: row.annual_interest_percentage,
                    monthly_payment_amount: row.monthly_payment_amount,
                    active: row.active,
                });
            }
        }
    }

    // Whatever else hasn't already matched on client_debt_esid is a new debt:
    // 1. Insert a new record into the database
    // 2. Add it to the output
    for (_, cr_debt) in esid_to_cr_debt_map.into_iter() {
        let client_debt_uuid = granite::pg_row!(
            db = dbcx;
            row = {
                client_debt_uuid: Uuid,
            };
            args = {
                $client_uuid: &client_uuid,
                $client_debt_esid: &cr_debt.id,
            };
            INSERT INTO
                df4l.client_debt
                (
                    client_uuid,
                    client_debt_esid
                )
            VALUES
                (
                    $client_uuid,
                    $client_debt_esid
                )
            RETURNING
                client_debt_uuid
        )
        .await?
        .client_debt_uuid;

        merged_debts.push(crate::debt::DebtRow {
            client_debt_uuid,
            client_uuid,
            client_debt_esid: Some(cr_debt.id),
            name: Some(cr_debt.name),
            balance: Some(cr_debt.balance),
            balance_date: Some(Utc::now().date_naive()),
            annual_interest_percentage: None,
            monthly_payment_amount: Some(cr_debt.monthly_payment),
            active: true,
        });
    }

    let debts: Vec<crate::debt::DebtRow> = merged_debts
        .into_iter()
        .sorted_by_key(|debt| {
            (
                -debt.balance.unwrap_or_default(),
                debt.name.clone().unwrap_or_default(),
            )
        })
        .collect();

    Ok(Ok(debts))
}

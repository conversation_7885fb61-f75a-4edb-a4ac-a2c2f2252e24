pub mod debt;
pub mod wizard;

use crate::types::CreditReportSubject;

/// This function will lookup and return either the applicant or spouse
/// SECON: assumed
pub struct CreditReportSubjectInfo {
    pub api_crs_user_uuid: Option<granite::Uuid>,
    pub enabled: Option<bool>,
}

pub async fn get_credit_report_subject_info(
    dbcx: &impl approck_postgres::DB,
    client_uuid: granite::Uuid,
    credit_report_subject: CreditReportSubject,
) -> granite::Result<CreditReportSubjectInfo> {
    match credit_report_subject {
        CreditReportSubject::Applicant => {
            let row = granite::pg_row!(
                db = dbcx;
                args = {
                    $client_uuid: &client_uuid,
                };
                row = {
                    api_crs_user_enabled_applicant: Option<bool>,
                    api_crs_user_uuid_applicant: Option<Uuid>,
                };
                SELECT
                    api_crs_user_enabled_applicant,
                    api_crs_user_uuid_applicant
                FROM
                    df4l.client
                WHERE
                    client_uuid = $client_uuid
            )
            .await?;

            Ok(CreditReportSubjectInfo {
                enabled: row.api_crs_user_enabled_applicant,
                api_crs_user_uuid: row.api_crs_user_uuid_applicant,
            })
        }
        CreditReportSubject::Spouse => {
            let row = granite::pg_row!(
                db = dbcx;
                args = {
                    $client_uuid: &client_uuid,
                };
                row = {
                    api_crs_user_enabled_spouse: Option<bool>,
                    api_crs_user_uuid_spouse: Option<Uuid>,
                };
                SELECT
                    api_crs_user_enabled_spouse,
                    api_crs_user_uuid_spouse
                FROM
                    df4l.client
                WHERE
                    client_uuid = $client_uuid
            )
            .await?;

            Ok(CreditReportSubjectInfo {
                enabled: row.api_crs_user_enabled_spouse,
                api_crs_user_uuid: row.api_crs_user_uuid_spouse,
            })
        }
    }
}

/// This method is used to toggle the enabled or disabled status of a credit report subject
/// The fields are on client.api_crs_user_enabled_{applicant,spouse}
pub async fn toggle_api_crs_user(
    dbcx: &impl approck_postgres::DB,
    client_uuid: granite::Uuid,
    credit_report_subject: CreditReportSubject,
    active: bool,
) -> granite::Result<()> {
    match credit_report_subject {
        CreditReportSubject::Applicant => {
            granite::pg_execute!(
                db = dbcx;
                args = {
                    $client_uuid: &client_uuid,
                    $active: &active,
                };
                UPDATE
                    df4l.client
                SET
                    api_crs_user_enabled_applicant = $active
                WHERE
                    client_uuid = $client_uuid
            )
            .await?;
        }
        CreditReportSubject::Spouse => {
            granite::pg_execute!(
                db = dbcx;
                args = {
                    $client_uuid: &client_uuid,
                    $active: &active,
                };
                UPDATE
                    df4l.client
                SET
                    api_crs_user_enabled_spouse = $active
                WHERE
                    client_uuid = $client_uuid
            )
            .await?;
        }
    }

    Ok(())
}

// SECON: assumed by caller
pub async fn allocate_api_crs_user(
    dbcx: &mut approck_postgres::DBCX<'_>,
    client_uuid: granite::Uuid,
    credit_report_subject: CreditReportSubject,
) -> granite::Result<granite::Uuid> {
    let dbtx = dbcx.transaction().await?;

    // assert that the api_crs_user_uuid is not already allocated
    let api_crs_user_ref =
        get_credit_report_subject_info(&dbtx, client_uuid, credit_report_subject).await?;

    // Fail if the user is already allocated
    if api_crs_user_ref.api_crs_user_uuid.is_some() {
        return Err(granite::Error::new(granite::ErrorType::InvalidOperation)
            .set_internal_message("api_crs_user already allocated".to_string())
            .add_context(format!("client_uuid: {client_uuid} {credit_report_subject} already has an api_crs_user_uuid"))
        );
    }

    // load client data
    let row = granite::pg_row!(
        db = dbtx;
        args = {
            $client_uuid: &client_uuid,
        };
        row = {
            first_name: String,
            last_name: String,
            email: Option<String>,
            phone: Option<String>,
            is_demo: bool,
        };
        SELECT
            first_name,
            last_name,
            email,
            phone,
            is_demo
        FROM
            df4l.client
        WHERE
            client_uuid = $client_uuid
    )
    .await?;

    let to_not_created = api_crs::core::user::ToNotCreated {
        is_demo: row.is_demo,
        first_name: match credit_report_subject {
            CreditReportSubject::Applicant => Some(row.first_name),
            CreditReportSubject::Spouse => None,
        },
        last_name: Some(row.last_name),
        email: match credit_report_subject {
            CreditReportSubject::Applicant => row.email,
            CreditReportSubject::Spouse => None,
        },
        phone: match credit_report_subject {
            CreditReportSubject::Applicant => row.phone,
            CreditReportSubject::Spouse => None,
        },
    };

    let user = ::api_crs::core::user::new(&dbtx, to_not_created).await?;

    // update client record
    match credit_report_subject {
        CreditReportSubject::Applicant => {
            granite::pg_execute!(
                db = dbtx;
                args = {
                    $client_uuid: &client_uuid,
                    $api_crs_user_uuid: &user.api_crs_user_uuid,
                };
                UPDATE
                    df4l.client
                SET
                    api_crs_user_uuid_applicant = $api_crs_user_uuid,
                    api_crs_user_enabled_applicant = true
                WHERE
                    client_uuid = $client_uuid
            )
            .await?;
        }
        CreditReportSubject::Spouse => {
            granite::pg_execute!(
                db = dbtx;
                args = {
                    $client_uuid: &client_uuid,
                    $api_crs_user_uuid: &user.api_crs_user_uuid,
                };
                UPDATE
                    df4l.client
                SET
                    api_crs_user_uuid_spouse = $api_crs_user_uuid,
                    api_crs_user_enabled_spouse = true
                WHERE
                    client_uuid = $client_uuid
            )
            .await?;
        }
    }

    // commit
    dbtx.commit().await?;

    Ok(user.api_crs_user_uuid)
}

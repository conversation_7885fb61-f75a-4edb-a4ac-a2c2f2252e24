#![allow(unused_variables)]

use super::{Gender, Version};
use granite::{DateUtc, Decimal, age_in_years_now};

/// GBU takes 5.75% of the PUA contribution amount as a fee
pub fn get_pua_contribution_fee(
    version: &Version,
    pua_contribution_amount: Decimal,
) -> Result<Decimal, String> {
    match version {
        // return 5.75% of the pua_contribution_amount
        Version::V202506 => Ok((pua_contribution_amount * Decimal::new(575, 4)).round_dp(2)),
    }
}

// TODO: <PERSON> is checking into this
pub fn get_pua_balance_growth(
    version: &Version,
    pua_balance: Decimal,
    insured_age: i32,
    insured_gender: Gender,
) -> Result<Decimal, String> {
    match version {
        Version::V202506 => {
            // return 2.1% / 12 of the pua_balance
            Ok((pua_balance * (Decimal::new(21, 4) / Decimal::new(12, 0))).round_dp(2))
        }
    }
}

pub fn calculate_max_pua(birth_date: DateUtc, gender: &Gender) -> super::PuaSplit {
    let age = age_in_years_now(birth_date);

    let (base_percentage, pua_percentage) = match (gender, age) {
        (Gender::Male, 51) => (51, 49),
        (Gender::Male, 52) => (51, 49),
        (Gender::Male, 53) => (52, 48),
        (Gender::Male, 54) => (52, 48),
        (Gender::Male, 55) => (53, 47),
        (Gender::Male, 56) => (53, 47),
        (Gender::Male, 57) => (53, 47),
        (Gender::Male, 58) => (54, 46),
        (Gender::Male, 59) => (54, 46),
        (Gender::Male, 60) => (54, 46),
        (Gender::Female, 55) => (51, 49),
        (Gender::Female, 56) => (51, 49),
        (Gender::Female, 57) => (51, 49),
        (Gender::Female, 58) => (52, 48),
        (Gender::Female, 59) => (52, 48),
        (Gender::Female, 60) => (52, 48),
        _ => (50, 50),
    };

    super::PuaSplit::new(base_percentage, pua_percentage)
}

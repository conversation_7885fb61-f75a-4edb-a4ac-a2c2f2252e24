#![allow(unused_variables)]

use super::{Gender, Version};
use granite::Decimal;

/// Takes
pub fn get_units_of_insurance(
    version: &Version,
    insured_age_at_issue: i32,
    insured_gender: Gender,
    annual_premium_amount: Decimal,
) -> Result<Decimal, String> {
    // mechanism of lookup is to used the table illustrated on `stored premium rates`
    // there are two tables, one for female, one for male.
    // the lookup key is (Gender, AgeAtIssue)

    let premium_rate = match lookup_premium_rate(insured_gender, insured_age_at_issue) {
        Some(v) => v,
        None => return Err("Premium rate not found for insured age and gender".to_string()),
    };

    Ok((annual_premium_amount / premium_rate).round_dp(4))
}

static PREMIUM_RATES: &[(Gender, i32, (i64, u32))] = &[
    (Gender::Female, 25, (92, 1)),
    (Gender::Female, 26, (96, 1)),
    (Gender::Female, 27, (100, 1)),
    (Gender::Female, 28, (104, 1)),
    (Gender::Female, 29, (108, 1)),
    (Gender::Female, 30, (113, 1)),
    (Gender::Female, 31, (118, 1)),
    (Gender::Female, 32, (123, 1)),
    (Gender::Female, 33, (129, 1)),
    (Gender::Female, 34, (134, 1)),
    (Gender::Female, 35, (140, 1)),
    (Gender::Female, 36, (146, 1)),
    (Gender::Female, 37, (152, 1)),
    (Gender::Female, 38, (158, 1)),
    (Gender::Female, 39, (165, 1)),
    (Gender::Female, 40, (172, 1)),
    (Gender::Female, 41, (181, 1)),
    (Gender::Female, 42, (189, 1)),
    (Gender::Female, 43, (199, 1)),
    (Gender::Female, 44, (209, 1)),
    (Gender::Female, 45, (219, 1)),
    (Gender::Female, 46, (230, 1)),
    (Gender::Female, 47, (241, 1)),
    (Gender::Female, 48, (253, 1)),
    (Gender::Female, 49, (266, 1)),
    (Gender::Female, 50, (279, 1)),
    (Gender::Female, 51, (288, 1)),
    (Gender::Female, 52, (298, 1)),
    (Gender::Female, 53, (308, 1)),
    (Gender::Female, 54, (318, 1)),
    (Gender::Female, 55, (329, 1)),
    (Gender::Female, 56, (340, 1)),
    (Gender::Female, 57, (351, 1)),
    (Gender::Female, 58, (363, 1)),
    (Gender::Female, 59, (375, 1)),
    (Gender::Female, 60, (387, 1)),
    (Gender::Male, 25, (103, 1)),
    (Gender::Male, 26, (107, 1)),
    (Gender::Male, 27, (112, 1)),
    (Gender::Male, 28, (116, 1)),
    (Gender::Male, 29, (121, 1)),
    (Gender::Male, 30, (126, 1)),
    (Gender::Male, 31, (131, 1)),
    (Gender::Male, 32, (137, 1)),
    (Gender::Male, 33, (143, 1)),
    (Gender::Male, 34, (149, 1)),
    (Gender::Male, 35, (156, 1)),
    (Gender::Male, 36, (162, 1)),
    (Gender::Male, 37, (169, 1)),
    (Gender::Male, 38, (176, 1)),
    (Gender::Male, 39, (183, 1)),
    (Gender::Male, 40, (191, 1)),
    (Gender::Male, 41, (201, 1)),
    (Gender::Male, 42, (211, 1)),
    (Gender::Male, 43, (222, 1)),
    (Gender::Male, 44, (233, 1)),
    (Gender::Male, 45, (245, 1)),
    (Gender::Male, 46, (256, 1)),
    (Gender::Male, 47, (268, 1)),
    (Gender::Male, 48, (281, 1)),
    (Gender::Male, 49, (294, 1)),
    (Gender::Male, 50, (308, 1)),
    (Gender::Male, 51, (320, 1)),
    (Gender::Male, 52, (331, 1)),
    (Gender::Male, 53, (344, 1)),
    (Gender::Male, 54, (357, 1)),
    (Gender::Male, 55, (370, 1)),
    (Gender::Male, 56, (382, 1)),
    (Gender::Male, 57, (394, 1)),
    (Gender::Male, 58, (407, 1)),
    (Gender::Male, 59, (420, 1)),
    (Gender::Male, 60, (434, 1)),
];

fn lookup_premium_rate(gender: Gender, age: i32) -> Option<Decimal> {
    PREMIUM_RATES
        .iter()
        .find(|&&(g, a, _)| g == gender && a == age)
        .map(|&(_, _, v)| Decimal::new(v.0, v.1))
}

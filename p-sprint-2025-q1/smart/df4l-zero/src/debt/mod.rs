use chrono::Months;
use granite::{DateUtc, Decimal};

#[granite::gtype(ApiOutput)]
pub struct DebtRow {
    pub client_debt_uuid: Uuid,
    pub client_uuid: Uuid,
    pub client_debt_esid: Option<String>,
    pub name: Option<String>,
    pub balance: Option<Decimal>,
    pub balance_date: Option<DateUtc>,
    pub annual_interest_percentage: Option<Decimal>,
    pub monthly_payment_amount: Option<Decimal>,
    pub active: bool,
}

#[granite::gtype(ApiOutput)]
pub struct Debts {
    pub debts: Vec<Debt>,
    pub monthly_payment_total: Decimal,
    pub monthly_interest_total: Decimal,
    pub monthly_principal_total: Decimal,
    pub percent_that_is_interest: Option<Decimal>,
}

#[granite::gtype(ApiOutput)]
pub struct DebtsError {
    pub outer: String,
    pub inner: Vec<DebtError>,
}

/// The validations in this ::new() are crucial to other aspects of the system
/// functioning correctly.
#[granite::gtype(ApiOutput)]
pub struct Debt {
    pub client_debt_uuid: Uuid,
    pub is_external: bool,
    pub name: String,
    pub balance: Decimal,
    pub balance_date: DateUtc,
    pub annual_interest_percentage: Decimal,
    pub monthly_payment_amount: Decimal,
    pub monthly_interest_amount: Decimal,
    pub monthly_principal_amount: Decimal,
    pub percent_that_is_interest: Option<Decimal>,

    // This field ensures that only this file can construct this debt
    _private: bool,
}

#[granite::gtype(ApiOutput)]
pub struct DebtError {
    pub client_debt_uuid: Uuid,
    pub name: Option<String>,
    pub balance: Option<String>,
    pub balance_date: Option<String>,
    pub annual_interest_percentage: Option<String>,
    pub monthly_payment_amount: Option<String>,
}

impl Debts {
    pub fn new(date: DateUtc, input: &[DebtRow]) -> Result<Debts, DebtsError> {
        let mut debts = Vec::with_capacity(input.len());
        let mut errors = Vec::new();

        // filter out inactive debts 100% of the time
        for debt in input.iter().filter(|d| d.active) {
            match Debt::new(date, debt) {
                Ok(debt) => debts.push(debt),
                Err(error) => errors.push(*error),
            }
        }

        // Return early if there are any errors
        if !errors.is_empty() {
            return Err(DebtsError {
                outer: "There were errors in your debt inputs.".to_string(),
                inner: errors,
            });
        }

        // calculate some totals
        let mut monthly_payment_total = Decimal::ZERO;
        let mut monthly_interest_total = Decimal::ZERO;
        let mut monthly_principal_total = Decimal::ZERO;

        for debt in debts.iter() {
            monthly_payment_total += debt.monthly_payment_amount;
            monthly_interest_total += debt.monthly_interest_amount;
            monthly_principal_total += debt.monthly_principal_amount;
        }

        let percent_that_is_interest = if monthly_payment_total > Decimal::ZERO {
            Some(
                (monthly_interest_total / monthly_payment_total)
                    * Decimal::new(100, 0).trunc_with_scale(2),
            )
        } else {
            None
        };

        Ok(Debts {
            debts,
            monthly_payment_total,
            monthly_interest_total,
            monthly_principal_total,
            percent_that_is_interest,
        })
    }
}

impl Debt {
    pub fn new(date: DateUtc, debt: &DebtRow) -> Result<Debt, Box<DebtError>> {
        let mut error = DebtError {
            client_debt_uuid: debt.client_debt_uuid,
            name: None,
            balance: None,
            balance_date: None,
            annual_interest_percentage: None,
            monthly_payment_amount: None,
        };

        let (name, balance, balance_date, annual_interest_percentage, monthly_payment_amount) = {
            match (
                &debt.name,
                debt.balance,
                debt.balance_date,
                debt.annual_interest_percentage,
                debt.monthly_payment_amount,
            ) {
                (
                    Some(name),
                    Some(balance),
                    Some(balance_date),
                    Some(annual_interest_percentage),
                    Some(monthly_payment_amount),
                ) => (
                    name.trim().to_string(),
                    balance.trunc_with_scale(2),
                    balance_date,
                    annual_interest_percentage.trunc_with_scale(2),
                    monthly_payment_amount.trunc_with_scale(2),
                ),
                (
                    name,
                    balance,
                    balance_date,
                    annual_interest_percentage,
                    monthly_payment_amount,
                ) => {
                    return Err(Box::new(DebtError {
                        client_debt_uuid: debt.client_debt_uuid,
                        name: name.is_none().then(|| "Name is required.".to_string()),
                        balance: balance
                            .is_none()
                            .then(|| "Balance is required.".to_string()),
                        balance_date: balance_date
                            .is_none()
                            .then(|| "Balance date is required.".to_string()),
                        annual_interest_percentage: annual_interest_percentage
                            .is_none()
                            .then(|| "Annual interest percentage is required.".to_string()),
                        monthly_payment_amount: monthly_payment_amount
                            .is_none()
                            .then(|| "Monthly payment amount is required.".to_string()),
                    }));
                }
            }
        };

        if name.is_empty() {
            error.name = Some("Name is required.".to_string());
        }

        // allow paid off debts (e.g. balance is 0)
        if balance < Decimal::ZERO || balance > Decimal::new(1000000000, 0) {
            error.balance = Some("Invalid balance.".to_string());
        }

        if balance_date > date {
            error.balance_date = Some("Balance date is in the future.".to_string());
        }

        if balance_date
            < date
                .checked_sub_months(Months::new(12))
                .expect("valid date operation")
        {
            error.balance_date = Some("Balance date is more than 1 year in the past.".to_string());
        }

        // zero % interest is allowed
        if annual_interest_percentage < Decimal::ZERO
            || annual_interest_percentage > Decimal::new(100, 0)
        {
            error.annual_interest_percentage =
                Some("Invalid annual interest percentage.".to_string());
        }

        // zero payment is allowed but only of the balace is zero (per reverse amortization rules below)
        if monthly_payment_amount < Decimal::ZERO
            || monthly_payment_amount > Decimal::new(1000000, 0)
        {
            error.monthly_payment_amount = Some("Invalid monthly payment amount.".to_string());
        }

        // bail out early if errors
        if error.name.is_some()
            || error.balance.is_some()
            || error.balance_date.is_some()
            || error.annual_interest_percentage.is_some()
            || error.monthly_payment_amount.is_some()
        {
            return Err(Box::new(error));
        }

        // Calculate monthly interest
        let monthly_interest_amount =
            (balance * (annual_interest_percentage / Decimal::new(100, 0)) / Decimal::new(12, 0))
                .trunc_with_scale(2);

        // Reverse Amortization check.  Interest cannot exceed payments.
        if monthly_interest_amount > monthly_payment_amount {
            error.monthly_payment_amount = Some(format!(
                "Monthly payment must be greater than ${monthly_interest_amount}, which is the monthly interest amount."
            ));
        }
        // If balance is > 0 and payment is less than $1, this is an error
        // Note the ELSE check because otherwise this would overwrite the above message
        else if balance > Decimal::ZERO && monthly_payment_amount < Decimal::ONE {
            error.monthly_payment_amount = Some(
                "Monthly payment must be at least $1 for any debt that has a balance.".to_string(),
            );
        }

        // bail out if additional errors found
        if error.monthly_payment_amount.is_some() {
            return Err(Box::new(error));
        }

        // Calculate principal amount
        let monthly_principal_amount =
            (monthly_payment_amount - monthly_interest_amount).trunc_with_scale(2);

        // Only have an answer here if there actually is a payment, otherwise None
        let percent_that_is_interest = if monthly_payment_amount > Decimal::ZERO {
            Some(
                ((monthly_interest_amount / monthly_payment_amount) * Decimal::new(100, 0)).round(),
            )
        } else {
            None
        };

        Ok(Debt {
            client_debt_uuid: debt.client_debt_uuid,
            is_external: debt.client_debt_esid.is_some(),
            name,
            balance,
            balance_date,
            annual_interest_percentage,
            monthly_payment_amount,
            monthly_interest_amount,
            monthly_principal_amount,
            percent_that_is_interest,
            _private: true,
        })
    }

    /// Generate a clone of this debt with the balance info updated to a specific date
    pub fn clone_with_current_balance_info(&self, balance_date: DateUtc) -> Result<Self, String> {
        let balance = crate::plan::calculate_starting_debt_balance::calculate(self, balance_date)?;
        Ok(Debt {
            client_debt_uuid: self.client_debt_uuid,
            is_external: self.is_external,
            name: self.name.clone(),
            balance,
            balance_date,
            annual_interest_percentage: self.annual_interest_percentage,
            monthly_payment_amount: self.monthly_payment_amount,
            monthly_interest_amount: self.monthly_interest_amount,
            monthly_principal_amount: self.monthly_principal_amount,
            percent_that_is_interest: self.percent_that_is_interest,
            _private: true,
        })
    }
}

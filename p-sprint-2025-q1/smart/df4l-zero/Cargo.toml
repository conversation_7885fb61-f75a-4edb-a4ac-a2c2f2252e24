[package]
name = "df4l-zero"
version = "0.1.0"
edition = "2024"

[package.metadata.approck.mod]
extends = ["approck", "auth-fence", "bux", "granite", "api-crs"]


[dependencies]
approck = { workspace = true }
auth-fence = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-postgres = { workspace = true }
postgres-types = { workspace = true }
api-crs = { workspace = true }

itertools = { workspace = true }
maud = { workspace = true }
serde_json = { workspace = true }
serde = { workspace = true, features = ["derive"] }
chrono = { workspace = true, features = ["serde"] }
rust_xlsxwriter = { workspace = true }

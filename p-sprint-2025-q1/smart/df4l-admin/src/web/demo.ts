import "./demo.mcss";

import { AllCommunityModule, createGrid, ModuleRegistry } from "ag-grid-community";

import { expect, SE } from "@granite/lib.mts";
import { admin_client_list } from "@crate/api/admin/client0/listλ.mts";

ModuleRegistry.registerModules([AllCommunityModule]);

const output = expect(
    admin_client_list.Output_decode((globalThis as any).script_data),
    "Failed to decode output",
);

// Create Grid: Create new grid within the #myGrid div, using the Grid Options object
createGrid(
    SE(document, "#myGrid"),
    {
        // Data to be displayed
        rowData: output.client_list,
        // Columns to be displayed (Should match rowData properties)
        columnDefs: [
            { field: "name", filter: true },
            { field: "advisor_esid", filter: true },
            { field: "active", filter: true },
            { field: "create_ts", filter: true },
            { field: "client_uuid", filter: true },
            { field: "advisor_uuid", filter: true },
        ],
        defaultColDef: {
            resizable: true,
            sortable: true,
            floatingFilter: false,
        },
        // Auto-size columns on grid ready
        onGridReady: (params) => {
            params.api.autoSizeAllColumns();
        },
    },
);

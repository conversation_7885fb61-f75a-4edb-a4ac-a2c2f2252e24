#[approck::http(GET /demo; AUTH None; return HTML;)]
pub mod page {
    use granite::GTypeEncode;

    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        use crate::api::admin::client0::list::admin_client_list;
        let output = admin_client_list::call(
            app,
            identity,
            admin_client_list::Input {
                keyword: None,
                active: Some(true),
                advisor_uuid: None,
            },
        )
        .await?;
        doc.set_body_display_fixed();
        doc.set_title("Demo");
        doc.set_script_json(output.gtype_encode());
        doc.add_body(html!(
            div #myGrid style="width: 100%; height: 100%;" {

            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

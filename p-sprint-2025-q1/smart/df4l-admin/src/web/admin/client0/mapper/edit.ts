import "./edit.mcss";
import "@bux/input/text/string.mts";

import { SE } from "@granite/lib.mts";
import { admin_client_edit } from "@crate/api/admin/client0/editλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $client_uuid: HTMLInputElement = SE($form, "[name=client_uuid]");
const $first_name: BuxInputTextString = SE($form, "[name=first_name]");
const $last_name: BuxInputTextString = SE($form, "[name=last_name]");
const $email: BuxInputTextString = SE($form, "[name=email]");
const $phone: BuxInputTextString = SE($form, "[name=phone]");
const $phone2: BuxInputTextString = SE($form, "[name=phone2]");

new FormPanel({
    $form,
    api: admin_client_edit.api,
    on_cancel: go_back,

    err: (errors) => {
        $first_name.set_e(errors.first_name);
        $last_name.set_e(errors.last_name);
        $email.set_e(errors.email);
        $phone.set_e(errors.phone);
        $phone2.set_e(errors.phone2);
    },

    get: () => {
        return {
            client_uuid: $client_uuid.value,
            first_name: $first_name.value,
            last_name: $last_name.value,
            email: $email.value_option,
            phone: $phone.value_option,
            phone2: $phone2.value_option,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});

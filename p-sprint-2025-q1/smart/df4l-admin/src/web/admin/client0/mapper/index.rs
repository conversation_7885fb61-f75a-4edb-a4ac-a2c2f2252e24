#[approck::http(GET /admin/client0/{client_uuid:Uuid}/?keyword=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::client0::detail::admin_client_detail;
        use approck::html;

        let client = admin_client_detail::call(
            app,
            identity,
            admin_client_detail::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        doc.page_nav_edit_record("Edit Client", &crate::ml_client0_edit(client.client_uuid));
        doc.set_title("Client Details");

        let mut table = bux::component::info_table(&client);
        table.set_heading("Client Information");
        table.add_row("Client Name:", |c| html! { (c.name) });
        table.add_row(
            "Client Email:",
            |c| html! { (c.email.as_deref().unwrap_or("")) },
        );
        table.add_row(
            "Client Phone:",
            |c| html! { (c.phone.as_deref().unwrap_or("")) },
        );
        table.add_row(
            "Additional Phone:",
            |c| html! { (c.phone2.as_deref().unwrap_or("")) },
        );
        table.add_row(
            "Created On:",
            |c| html! { (c.create_ts.format("%B %d, %Y").to_string()) },
        );
        table.add_link_row(
            "Advisor Name:",
            |c| c.advisor_link.to_string(),
            |c| c.advisor_name.to_string(),
        );

        table.add_active_status_row("Status:", |c| c.active);

        //DF4LStatus
        table.add_row("Activated:", |c| {
            maud::PreEscaped(c.df4l_status_html.clone())
        });

        table.add_row("Active Debts:", |c| html! { (c.active_debts) });
        table.add_row(
            "Minimum Monthly Debt Payments:",
            |c| html! { (c.minimum_monthly_payment_html) },
        );
        table.add_row(
            "Combined Premium And Debt Snowball Budget:",
            |c| html! { (c.annual_budget_html) },
        );
        table.add_row(
            "Total Insurance Premium:",
            |c| html! {(c.annual_insurance_premium_html) },
        );
        table.add_row(
            "Insurance Premium Base:",
            |c| html! {(c.annual_insurance_base_html) },
        );
        table.add_row(
            "Insurance Premium PUA:",
            |c| html! {(c.annual_insurance_pua_html) },
        );

        doc.add_body(html!((table)));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::http(GET /admin/client0/{client_uuid:Uuid}/assign-advisor; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Assign Advisor Client");

        let mut panel = bux::component::save_cancel_form_panel(
            "Assign Advisor",
            "/admin/client0/00000000-0000-0000-0000-000000000000/",
        );

        let client_name = Some("Adrian Gallinal");
        let client_email = Some("<EMAIL>");
        let mobile_phone = Some("(*************");
        let selected_advisor = Some("<PERSON><PERSON>");

        let advisor_options = vec![
            ("<PERSON><PERSON>", "<PERSON><PERSON>"),
            ("<PERSON>", "<PERSON>"),
        ];

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            grid-12 {
                cell-6 {
                    (bux::input::text::string::name_label_value("client_name", "Client Name:", client_name))
                    (bux::input::text::string::name_label_value("client_email", "Client Email:", client_email))
                    (bux::input::text::string::name_label_value("mobile_phone", "Client Mobile Phone:", mobile_phone))
                }
                cell-6 {
                    (bux::input::select::nilla::nilla_select("select_advisor", "★ Select Advisor:", &advisor_options, selected_advisor))
                    (bux::input::textarea::string::name_label_value("note", "Note:", None))

                }
            }
        ));
        doc.add_body(html! {
            section {
                (panel)
            }
        });

        Response::HTML(doc.into())
    }
}

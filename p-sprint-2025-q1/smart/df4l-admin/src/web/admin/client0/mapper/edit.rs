#[approck::http(GET /admin/client0/{client_uuid:Uuid}/edit?keyword=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::client0::detail::admin_client_detail;
        use approck::html;
        let client = admin_client_detail::call(
            app,
            identity,
            admin_client_detail::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        doc.set_title(&format!("Edit Client: {}", client.name));

        let mut form_panel = bux::component::save_cancel_form_panel(
            &format!("Edit Client: {}", client.name),
            &crate::ml_client0(client.client_uuid),
        );

        form_panel.set_hidden("client_uuid", path.client_uuid);

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            (bux::input::text::string::name_label_value("first_name", "First Name:", Some(&client.first_name)))
            (bux::input::text::string::name_label_value("last_name", "Last Name:", Some(&client.last_name)))
            (bux::input::text::string::name_label_value("email", "Email:", Some(&client.email.unwrap_or_default())))
            (bux::input::text::string::name_label_value("phone", "Send Text Messages To Phone Number:", Some(&client.phone.unwrap_or_default())))
            (bux::input::text::string::name_label_value("phone2", "Send Text Messages To Additional Phone Number:", Some(&client.phone2.unwrap_or_default())))
        ));
        doc.add_body(html! {
            bux-action-panel {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::http(GET /admin/?name=String; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("Admin Dashboard");

        use crate::api::admin::dashboard::admin_dashboard;
        let dashboard_data = admin_dashboard::call(app).await?;

        // Get user name for welcome message
        let user_name = identity.name().unwrap_or_else(|| "Admin".to_string());
        let heading = format!("Welcome, {user_name}!");

        // Create string values for tile data
        let agency_count_str = dashboard_data.agency_count.to_string();
        let client_count_str = dashboard_data.client_count.to_string();
        let legacy_client_count_str = dashboard_data.legacy_client_count.to_string();
        let advisor_count_str = dashboard_data.advisor_count.to_string();

        doc.add_css("/admin/index.css");

        doc.add_body(html!(
            div class="admin-dashboard" {
                h1 { (heading) }

                p { "Here's a quick overview of your system metrics." }

                // Simple metric tiles
                div.metrics-grid {
                    div.metric-card {
                        div.metric-icon {
                            i.fas.fa-building aria-hidden="true" {}
                        }
                        div.metric-content {
                            div.metric-label { "Agencies" }
                            div.metric-value { (agency_count_str) }
                        }
                        a href="/admin/agency/" aria-label="View Agencies" {
                            i.fas.fa-chevron-right {}
                        }
                    }
                    div.metric-card {
                        div.metric-icon {
                            i.fas.fa-users aria-hidden="true" {}
                        }
                        div.metric-content {
                            div.metric-label { "Clients" }
                            div.metric-value { (client_count_str) }
                        }
                        a href="/admin/client/" aria-label="View Clients" {
                            i.fas.fa-chevron-right {}
                        }
                    }
                    div.metric-card {
                        div.metric-icon {
                            i.fas.fa-user-clock aria-hidden="true" {}
                        }
                        div.metric-content {
                            div.metric-label { "Legacy Clients" }
                            div.metric-value { (legacy_client_count_str) }
                        }
                        a href="/admin/client0/" aria-label="View Legacy Clients" {
                            i.fas.fa-chevron-right {}
                        }
                    }
                    div.metric-card {
                        div.metric-icon {
                            i.fas.fa-user-tie aria-hidden="true" {}
                        }
                        div.metric-content {
                            div.metric-label { "Advisors" }
                            div.metric-value { (advisor_count_str) }
                        }
                        a href="/admin/advisor/" aria-label="View Advisors" {
                            i.fas.fa-chevron-right {}
                        }
                    }
                }

                // Main layout with charts and KPI sidebar
                main {
                    // Sales overview section
                    section aria-labelledby="sales-title" {
                        header {
                            h2 id="sales-title" { "Client Onboarding Activity" }
                            select.period-selector {
                                option value="current" selected { "Current Period" }
                                option value="previous" { "Previous Period" }
                                option value="quarter" { "Last Quarter" }
                            }
                        }
                        figure {
                            div id="client_onboarding_chart"
                                data-chart-type="grouped-bar"
                                data-labels=r#"["Jan","Feb","Mar","Apr","May","Jun","Jul"]"#
                                data-data=r#"[23,31,28,35,42,38,47]"#
                                data-data2=r#"[18,25,22,30,38,35,42]"#
                                data-label="New Clients Onboarded"
                                data-label2="Previous Period"
                                data-title="Client Onboarding Activity" {}
                        }
                    }

                    // KPI sidebar
                    aside aria-label="KPI summary" {
                        section aria-labelledby="kpi-1-title" {
                            header class="kpi" {
                                h2 id="kpi-1-title" { "Monthly Credit Reports Pulled" }
                            }
                            div class="kpi" {
                                h3 { "4" }
                                div class="growth-indicator" { "Growth indicator" }
                                figure {
                                    div id="credit_reports_chart"
                                        data-chart-type="donut"
                                        data-labels=r#"["Current","Previous"]"#
                                        data-data=r#"[75,25]"#
                                        data-label="Credit Reports"
                                        data-show-legend="false"
                                        data-inner-radius="0.7" {}
                                }
                                div class="legend" {
                                    div class="legend-item" {
                                        div class="dot current" {}
                                        span { "Current" }
                                    }
                                    div class="legend-item" {
                                        div class="dot previous" {}
                                        span { "Previous" }
                                    }
                                }
                            }
                        }

                        section aria-labelledby="kpi-2-title" {
                            header class="kpi" {
                                h2 id="kpi-2-title" { "Policy Creation Trends" }
                            }
                            div class="kpi" {
                                h3 { "306" }
                                div class="growth-indicator" { "Growth indicator" }
                                figure {
                                    div id="policy_trends_mini_chart"
                                        data-chart-type="area"
                                        data-labels=r#"["W1","W2","W3","W4","W5"]"#
                                        data-data=r#"[8,12,15,11,18]"#
                                        data-label="Policies"
                                        data-show-axes="false"
                                        data-show-legend="false"
                                        data-fill-opacity="0.3" {}
                                }
                            }
                        }

                    }
                }

                // Bottom layout with additional charts and data table
                footer {
                    section aria-labelledby="timeline-title" {
                        header {
                            h2 id="timeline-title" { "Recent Activity" }
                        }
                        x-timeline {
                            x-timeline-item class="b-blue" {
                                x-timeline-time { "Today" }
                                x-timeline-message {
                                    "New client onboarded: "
                                    strong { "Premium Policy #1234" }
                                }
                            }
                            x-timeline-item class="b-green" {
                                x-timeline-time { "2h ago" }
                                x-timeline-message {
                                    "Credit report generated for "
                                    strong { "John Smith" }
                                }
                            }
                            x-timeline-item class="b-orange" {
                                x-timeline-time { "4h ago" }
                                x-timeline-message {
                                    "Policy created "
                                    a href="#" aria-label="View policy POL-5678" { "#POL-5678" }
                                }
                            }
                            x-timeline-item class="b-red" {
                                x-timeline-time { "8h ago" }
                                x-timeline-message { "System maintenance completed" }
                            }
                        }
                    }

                    // Data table
                    section aria-labelledby="table-title" {
                        header {
                            h2 id="table-title" { "Agencies" }
                        }
                        table aria-label="Agencies table" {
                            thead {
                                tr {
                                    th scope="col" { "Agency Name" }
                                    th scope="col" { "Agency ID" }
                                    th scope="col" { "Created On" }
                                    th scope="col" { "Status" }
                                }
                            }
                            tbody {
                                tr {
                                    td {
                                        label { "Cincy SMART" }
                                    }
                                    td { "66-204f" }
                                    td { "Apr 17, 2024 10:38 PM" }
                                    td {
                                        label-tag.success { "Active" }
                                    }
                                }
                                tr {
                                    td {
                                        label { "SMART Retirement" }
                                    }
                                    td { "2e-5c14" }
                                    td { "Aug 25, 2014 03:48 AM" }
                                    td {
                                        label-tag.success { "Active" }
                                    }
                                }
                                tr {
                                    td {
                                        label { "True North Financial" }
                                    }
                                    td { "68-d634" }
                                    td { "Sep 26, 2025 06:35 AM" }
                                    td {
                                        label-tag.success { "Active" }
                                    }
                                }
                                tr {
                                    td {
                                        label { "Venture Wealth Advisors" }
                                    }
                                    td { "ed-cb43" }
                                    td { "Jun 03, 2016 12:19 AM" }
                                    td {
                                        label-tag.success { "Active" }
                                    }
                                }
                            }
                        }
                    }
                }

                // Useful links
                x-links {
                    h2 { "Useful Links" }
                    x-link-list {
                        x-link-item {
                            a href="/admin/legal/" {
                                x-link-icon {
                                    i.fas.fa-balance-scale aria-hidden="true" {}
                                }
                                x-link-label { "Legal Documents" }
                            }
                        }
                        x-link-item {
                            a href="/help" {
                                x-link-icon {
                                    i.fas.fa-life-ring aria-hidden="true" {}
                                }
                                x-link-label { "Get Help" }
                            }
                        }
                        x-link-item {
                            a href="/terms" {
                                x-link-icon {
                                    i.fas.fa-file-contract aria-hidden="true" {}
                                }
                                x-link-label { "Terms of Service" }
                            }
                        }
                        x-link-item {
                            a href="/admin/client/" {
                                x-link-icon {
                                    i.fas.fa-users aria-hidden="true" {}
                                }
                                x-link-label { "Clients" }
                            }
                        }
                        x-link-item {
                            a href="/admin/advisor/" {
                                x-link-icon {
                                    i.fas.fa-user-tie aria-hidden="true" {}
                                }
                                x-link-label { "Advisors" }
                            }
                        }
                        x-link-item {
                            a href="/admin/agency/" {
                                x-link-icon {
                                    i.fas.fa-building aria-hidden="true" {}
                                }
                                x-link-label { "Agencies" }
                            }
                        }
                        x-link-item {
                            a href="/myaccount/" {
                                x-link-icon {
                                    i.fas.fa-user aria-hidden="true" {}
                                }
                                x-link-label { "My Account" }
                            }
                        }
                        x-link-item {
                            a href="/admin/twilio/" {
                                x-link-icon {
                                    i.fas.fa-sms aria-hidden="true" {}
                                }
                                x-link-label { "Twilio Admin" }
                            }
                        }
                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}

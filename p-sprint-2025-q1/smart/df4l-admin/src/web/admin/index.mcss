/* Admin Dashboard Styles - Modern Design */
.admin-dashboard {
    --bg: #f8fafc;
    --card: #ffffff;
    --muted: #64748b;
    --text: #1e293b;
    --text-light: #64748b;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --primary: #3b82f6;
    --primary-light: #dbeafe;
    --accent: #06b6d4;
    --accent-light: #cffafe;
    --success: #10b981;
    --success-light: #d1fae5;
    --warning: #f59e0b;
    --warning-light: #fef3c7;
    --danger: #ef4444;
    --danger-light: #fee2e2;
    --info: #3b82f6;
    --info-light: #dbeafe;
    --border: #e2e8f0;
    --border-light: #f1f5f9;

    * {
        box-sizing: border-box;
    }

    display: block;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
        "Helvetica Neue", Arial, sans-serif;
    background: var(--bg);
    color: var(--text);
    padding: 2rem;
    margin: 0;
    min-height: 100vh;
    line-height: 1.6;

    /* Dashboard heading styles */
    h1 {
        font-size: 2.25rem;
        font-weight: 700;
        margin: 0 0 0.5rem 0;
        color: var(--text);
        letter-spacing: -0.025em;
    }

    /* Subtitle text */
    p {
        color: var(--text-light);
        font-size: 1rem;
        margin: 0 0 2rem 0;
    }

    /* Metrics grid for key metrics */
    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
        margin-bottom: 2rem;

        .metric-card {
            position: relative;
            background: var(--card);
            border-radius: 0.75rem;
            box-shadow: var(--shadow);
            padding: 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.2s ease;
            border-left: 4px solid var(--primary);
            overflow: hidden;

            &:hover {
                transform: translateY(-2px);
                box-shadow: var(--shadow-lg);
            }

            /* Different colored left borders matching the design */
            &:nth-child(1) {
                border-left-color: #3b82f6; /* Blue */
            }

            &:nth-child(2) {
                border-left-color: #06b6d4; /* Cyan */
            }

            &:nth-child(3) {
                border-left-color: #10b981; /* Green */
            }

            &:nth-child(4) {
                border-left-color: #f59e0b; /* Orange */
            }

            .metric-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 2.5rem;
                height: 2.5rem;
                color: var(--text-light);
                font-size: 1.25rem;
                flex-shrink: 0;
            }

            .metric-content {
                flex: 1;
                min-width: 0;

                .metric-label {
                    display: block;
                    font-size: 0.875rem;
                    color: var(--text-light);
                    margin-bottom: 0.5rem;
                    font-weight: 500;
                }

                .metric-value {
                    display: block;
                    font-size: 2rem;
                    font-weight: 700;
                    color: var(--text);
                    line-height: 1;
                }
            }

            a {
                text-decoration: none;
                color: var(--text-light);
                display: flex;
                align-items: center;
                justify-content: center;
                width: 1.5rem;
                height: 1.5rem;
                flex-shrink: 0;
                font-size: 1rem;
                transition: color 0.2s ease;

                &:hover {
                    color: var(--primary);
                }

                &::after {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                }
            }
        }
    }

    /* Main layout - semantic dashboard structure */
    main {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    /* Bottom layout */
    footer {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    /* Card styling for sections and aside */
    section,
    aside {
        background: var(--card);
        border-radius: 0.75rem;
        box-shadow: var(--shadow);
        padding: 1.5rem;
        border: 1px solid var(--border-light);
    }

    /* Header styling within cards */
    header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;

        h2 {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
            color: var(--text);
        }

        /* Period selector dropdown styling */
        select.period-selector {
            border: 1px solid var(--border);
            background: var(--card);
            border-radius: 0.375rem;
            padding: 0.5rem 2rem 0.5rem 0.75rem;
            font-size: 0.875rem;
            color: var(--text);
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 1rem;
            min-width: 140px;

            &:hover {
                border-color: var(--primary);
            }

            &:focus {
                outline: none;
                border-color: var(--primary);
                box-shadow: 0 0 0 3px var(--primary-light);
            }

            option {
                background: var(--card);
                color: var(--text);
                padding: 0.5rem;

                &:checked {
                    background: var(--primary);
                    color: white;
                }
            }
        }
    }

    /* KPI header styling */
    header.kpi {
        margin-bottom: 1rem;

        h2 {
            font-size: 1rem;
            font-weight: 600;
            margin: 0;
            color: var(--text-light);
        }
    }

    /* Chart containers */
    figure {
        margin: 0;
        height: 600px;
        background: var(--border-light);
        border-radius: 0.5rem;
        padding: 1rem;

        div[id$="_chart"] {
            width: 100%;
            height: 100%;
            background: var(--card);
            border-radius: 0.375rem;
        }
    }

    /* KPI sidebar styling */
    aside {
        display: grid;
        gap: 1.5rem;

        section {
            padding: 1.5rem;
            position: relative;
        }

        .kpi {
            text-align: center;
            margin-bottom: 1rem;

            h3 {
                margin: 0 0 0.5rem 0;
                font-size: 2.5rem;
                font-weight: 700;
                color: var(--text);
                line-height: 1;
            }

            /* Growth indicator */
            .growth-indicator {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.25rem;
                font-size: 0.75rem;
                color: var(--success);
                margin-top: 0.5rem;

                &::before {
                    content: "▲";
                    font-size: 0.625rem;
                }
            }

            /* Chart container for donut */
            figure {
                height: 120px;
                background: transparent;
                padding: 0;
                margin: 1rem 0;

                div[id$="_chart"] {
                    background: transparent;
                    border-radius: 0;
                }
            }

            /* Legend for donut chart */
            .legend {
                display: flex;
                justify-content: center;
                gap: 1rem;
                margin-top: 0.5rem;
                font-size: 0.75rem;
                color: var(--text-light);

                .legend-item {
                    display: flex;
                    align-items: center;
                    gap: 0.375rem;

                    .dot {
                        width: 0.5rem;
                        height: 0.5rem;
                        border-radius: 50%;
                        background: var(--muted);

                        &.current {
                            background: var(--primary);
                        }

                        &.previous {
                            background: var(--border);
                        }
                    }
                }
            }
        }
    }

    /* Timeline section with grid layout */
    section.timeline {
        display: grid;
        grid-template-columns: 1fr 2fr;
        gap: 1.5rem;
        align-items: start;

        header {
            grid-column: 1 / -1;
            margin-bottom: 1rem;
        }

        .timeline-content {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
    }

    /* Timeline styling */
    x-timeline {
        display: block;

        x-timeline-item {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            position: relative;
            padding: 0.75rem 0;

            &::before {
                content: "";
                width: 0.75rem;
                height: 0.75rem;
                border-radius: 50%;
                flex-shrink: 0;
                margin-top: 0.125rem;
            }

            &.b-blue::before {
                background: var(--primary);
            }

            &.b-green::before {
                background: var(--success);
            }

            &.b-orange::before {
                background: var(--warning);
            }

            &.b-red::before {
                background: var(--danger);
            }

            x-timeline-time {
                font-size: 0.75rem;
                color: var(--text-light);
                min-width: 3rem;
                flex-shrink: 0;
                font-weight: 500;
            }

            x-timeline-message {
                font-size: 0.875rem;
                color: var(--text);
                line-height: 1.5;

                a {
                    color: var(--primary);
                    text-decoration: none;
                    font-weight: 500;

                    &:hover {
                        text-decoration: underline;
                    }
                }

                strong {
                    font-weight: 600;
                    color: var(--text);
                }
            }
        }
    }

    /* Table styling */
    table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.875rem;

        thead {
            th {
                text-align: left;
                padding: 1rem 0.75rem;
                font-weight: 600;
                color: var(--text-light);
                border-bottom: 1px solid var(--border);
                background: var(--border-light);
                font-size: 0.75rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;

                &:first-child {
                    border-top-left-radius: 0.5rem;
                }

                &:last-child {
                    border-top-right-radius: 0.5rem;
                }
            }
        }

        tbody {
            tr {
                border-bottom: 1px solid var(--border-light);
                transition: background-color 0.2s ease;

                &:hover {
                    background: var(--border-light);
                }

                &:last-child {
                    border-bottom: none;
                }

                td {
                    padding: 1rem 0.75rem;
                    color: var(--text);
                    vertical-align: middle;

                    label {
                        display: inline-block;
                        font-weight: 500;
                        color: var(--text);
                    }


                }
            }
        }
    }

    /* Links section styling */
    x-links {
        display: block;
        background: var(--card);
        border-radius: 0.75rem;
        box-shadow: var(--shadow);
        padding: 1.5rem;
        border: 1px solid var(--border-light);

        h2 {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0 0 1.5rem 0;
            color: var(--text);
        }

        x-link-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 0.75rem;

            x-link-item {
                display: block;

                a {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 1rem;
                    background: var(--border-light);
                    border-radius: 0.5rem;
                    text-decoration: none;
                    color: var(--text);
                    transition: all 0.2s ease;
                    border: 1px solid transparent;

                    &:hover {
                        background: var(--card);
                        border-color: var(--border);
                        transform: translateY(-1px);
                        box-shadow: var(--shadow);
                    }

                    x-link-icon {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 2rem;
                        height: 2rem;
                        color: var(--text-light);
                        font-size: 1rem;
                        transition: color 0.2s ease;
                    }

                    x-link-label {
                        font-size: 0.875rem;
                        font-weight: 500;
                        color: var(--text);
                    }

                    &:hover x-link-icon {
                        color: var(--primary);
                    }
                }
            }
        }
    }

    /* Disable chart legend clicking and hover effects */
    .ag-chart-legend-item,
    ag-charts-proxy-elem .ag-chart-legend-item,
    .ag-chart-legend-item *,
    ag-charts-proxy-elem .ag-chart-legend-item * {
        pointer-events: none !important;
        cursor: default !important;
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
        outline: none !important;
    }

    /* Remove hover effects and pill buttons */
    .ag-chart-legend-item:hover,
    ag-charts-proxy-elem .ag-chart-legend-item:hover,
    .ag-chart-legend-item:hover *,
    ag-charts-proxy-elem .ag-chart-legend-item:hover * {
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
        outline: none !important;
        transform: none !important;
    }

    /* Disable all interactions on chart legend */
    [class*="ag-chart-legend"] {
        pointer-events: none !important;
        cursor: default !important;
    }

    /* Remove any button-like styling from legend items */
    [class*="ag-chart-legend"] button,
    [class*="ag-chart-legend"] .ag-chart-legend-item-button {
        display: none !important;
        pointer-events: none !important;
    }

    /* Override AG Charts default pointer events */
    .ag-charts-proxy-elem {
        pointer-events: none !important;
    }
}
import "./index.mcss";
import { AgCharts } from "ag-charts-community";

// Chart creation with all types
const create_chart = (
    el: HTMLElement,
    type: string,
    labels: string[],
    data: number[],
    data2?: number[],
) => {
    const chart_data = type === "grouped-bar" && data2
        ? labels.map((label, i) => ({
            category: label,
            series1: data[i] || 0,
            series2: data2[i] || 0,
        }))
        : labels.map((label, i) => ({ category: label, value: data[i] || 0 }));

    const series_configs = {
        line: [{
            type: "line",
            xKey: "category",
            yKey: "value",
            stroke: "#a6b3f2",
            strokeWidth: 2,
        }],
        bar: [{ type: "bar", xKey: "category", yKey: "value", fill: "#a6b3f2", cornerRadius: 4 }],
        "grouped-bar": [
            { type: "bar", xKey: "category", yKey: "series1", fill: "#a6b3f2", cornerRadius: 4 },
            { type: "bar", xKey: "category", yKey: "series2", fill: "#a6c4ff", cornerRadius: 4 },
        ],
        donut: [{
            type: "donut",
            angleKey: "value",
            categoryKey: "category",
            fills: ["#a6b3f2", "#a6c4ff"],
            strokeWidth: 0,
        }],
        area: [{
            type: "area",
            xKey: "category",
            yKey: "value",
            fill: "#a6b3f2",
            fillOpacity: 0.3,
            stroke: "#a6b3f2",
            strokeWidth: 2,
            marker: { enabled: false },
        }],
    };

    const axes_configs = {
        line: [{ type: "category", position: "bottom" }, { type: "number", position: "left" }],
        bar: [{ type: "category", position: "bottom" }, { type: "number", position: "left" }],
        "grouped-bar": [{ type: "category", position: "bottom" }, {
            type: "number",
            position: "left",
        }],
        donut: undefined,
        area: [{ type: "category", position: "bottom", visible: false }, {
            type: "number",
            position: "left",
            visible: false,
        }],
    };

    AgCharts.create({
        container: el,
        data: chart_data,
        background: { fill: "transparent" },
        legend: {
            enabled: type === "grouped-bar",
            item: {
                toggleSeriesVisible: false,
                showSeriesStroke: false,
                marker: {
                    enabled: true,
                },
            },
            listeners: {
                legendItemClick: (event: any) => {
                    event.preventDefault();
                    return false;
                },
                legendItemDoubleClick: (event: any) => {
                    event.preventDefault();
                    return false;
                },
            },
        },
        series: series_configs[type as keyof typeof series_configs],
        axes: axes_configs[type as keyof typeof axes_configs],
    } as any);
};

// Initialize charts and period selectors
document.addEventListener("DOMContentLoaded", () => {
    // Create all charts
    document.querySelectorAll('[id$="_chart"]').forEach((el) => {
        const chart_el = el as HTMLElement;
        const type = chart_el.getAttribute("data-chart-type");
        if (!type) return;

        try {
            const labels = JSON.parse(chart_el.getAttribute("data-labels") || "[]");
            const data = JSON.parse(chart_el.getAttribute("data-data") || "[]");
            const data2 = JSON.parse(chart_el.getAttribute("data-data2") || "[]");

            if (labels.length && data.length) {
                create_chart(chart_el, type, labels, data, data2.length ? data2 : undefined);
            }
        } catch (e) {
            console.warn("Chart failed:", e);
        }
    });

    // Period selector handling
    document.querySelectorAll(".period-selector").forEach((selector) => {
        selector.addEventListener("change", (e) => {
            const period = (e.target as HTMLSelectElement).value;
            const chart_el = selector.closest("section")?.querySelector(
                '[id$="_chart"]',
            ) as HTMLElement;
            if (!chart_el) return;

            const data_sets = {
                current: [[23, 31, 28, 35, 42, 38, 47], [18, 25, 22, 30, 38, 35, 42]],
                previous: [[18, 25, 22, 30, 38, 35, 42], [15, 20, 18, 25, 32, 30, 38]],
                quarter: [[65, 78, 72, 85, 92, 88, 95], [58, 68, 65, 75, 82, 78, 85]],
            };

            const [data1, data2] = data_sets[period as keyof typeof data_sets] || data_sets.current;
            chart_el.setAttribute("data-data", JSON.stringify(data1));
            chart_el.setAttribute("data-data2", JSON.stringify(data2));
            chart_el.innerHTML = "";

            try {
                const type = chart_el.getAttribute("data-chart-type");
                const labels = JSON.parse(chart_el.getAttribute("data-labels") || "[]");
                if (type && labels.length) {
                    create_chart(chart_el, type, labels, data1 as number[], data2 as number[]);
                }
            } catch (e) {
                console.warn("Chart update failed:", e);
            }
        });
    });
});

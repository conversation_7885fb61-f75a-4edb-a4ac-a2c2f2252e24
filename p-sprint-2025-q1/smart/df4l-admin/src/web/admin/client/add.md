# Add Client Functionality

This module provides the admin interface for adding new clients to the DF4L system.

## Features

- **Advisor Selection**: Dropdown to select which advisor the client will be assigned to
- **Client Type Selection**: Choose between two client types:
  - `DebtManagement`: "Debt2Capital™ Insurance & Debt Snowball Client"
  - `PolicyOnly`: "Debt2Capital™ Insurance Policy Only Client"
- **Personal Information**: Capture client's basic details (name, email, phone, address)
- **Budget Information**: Optional fields for budget-related data
- **Notes**: Free-form text field for additional client information

## Form Fields

### Required

- Advisor UUID
- Client Type
- First Name
- Last Name

### Optional

- Email
- Phone
- Address (street, city, state, zip)
- Time Zone
- Gender
- Birth Date
- Budget fields (extra debts, savings, retirement, surplus)
- PUA split and contribution
- Policy total premium
- Notes

## Database Integration

Creates a new record in the `df4l.client` table with all provided information and returns the generated `client_uuid` for further operations.

#[approck::http(GET /admin/client/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        qs: QueryString,
    ) -> Result<Response> {
        use maud::html;

        doc.page_nav_add_record("Add New Client", &crate::ml_client_add());
        doc.set_title("Clients");
        doc.set_body_display_fluid();

        use crate::api::admin::client::list::admin_client_list;

        let active = bux::parse_active_qs(&qs.active, Some(true));
        let output = admin_client_list::call(
            app,
            identity,
            admin_client_list::Input {
                keyword: qs.keyword.clone(),
                active,
                advisor_uuid: None,
            },
        )
        .await?;

        let mut dt = bux::component::detail_table(output.client_list);

        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        dt.add_column("Name", |a| {
            html! {
                (a.name.clone())
                @if a.is_demo {
                    " " label-tag.warning { "DEMO" }
                }
            }
        });

        dt.add_active_status_column("Client Status", |a| a.active);
        dt.add_column(
            "Client Phone",
            |a| html! { (a.phone.as_deref().unwrap_or("")) },
        );
        dt.add_email_column(|a| a.email.as_deref().unwrap_or(""));
        dt.add_link_column(
            "Advisor ID",
            |a| crate::ml_advisor(a.advisor_uuid),
            |a| a.advisor_esid.to_string(),
        );
        dt.add_status_column("Advisor Status", |a| match a.active {
            true => "Active",
            false => "Inactive",
        });
        dt.add_column("Created On", |a| {
            html! {
                (a.create_ts.format("%B %d, %Y"))
            }
        });
        dt.add_details_column(|a| crate::ml_client(a.client_uuid));

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}

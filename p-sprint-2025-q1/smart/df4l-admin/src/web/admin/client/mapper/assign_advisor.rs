#[approck::http(GET /admin/client/{client_uuid:Uuid}/assign_advisor; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::client::detail::admin_client_detail;
        use maud::html;

        let client = admin_client_detail::call(
            app,
            identity,
            admin_client_detail::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        doc.set_title(&format!("Assign New Advisor To Client: {}", client.name));

        let mut form_panel = bux::component::save_cancel_form_panel(
            &format!("Assign New Advisor To Client: {}", client.name),
            &crate::ml_client(client.client_uuid),
        );
        use crate::api::admin::advisor::list::admin_advisor_list;

        let advisors = admin_advisor_list::call(
            app,
            identity,
            admin_advisor_list::Input {
                keyword: None,
                active: Some(true),
            },
        )
        .await?;

        // Format advisors for dropdown: "Name (ESID)"
        let advisor_options_strings: Vec<(String, String)> = advisors
            .advisor_list
            .iter()
            .map(|a| {
                let display = format!("{} {}", a.first_name, a.last_name);
                (a.advisor_uuid.to_string(), display)
            })
            .collect();

        // Convert to the expected type for nilla_select
        let advisor_options: Vec<(&str, &str)> = advisor_options_strings
            .iter()
            .map(|(id, name)| (id.as_str(), name.as_str()))
            .collect();

        form_panel.set_hidden("client_uuid", path.client_uuid);

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            // add readonly for advisor
            (bux::input::text::string::name_label_readonly("advisor_name", "Advisor Name:", &client.advisor_name))
            //select for new advisor
            (bux::input::select::nilla::nilla_select(
                "advisor_uuid", 
                "Select Advisor:", 
                &advisor_options, 
                None,   
            ))
                   
        ));
        doc.add_body(html! {
            bux-action-panel {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}

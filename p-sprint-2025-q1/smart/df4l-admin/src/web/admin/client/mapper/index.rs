#[approck::http(GET /admin/client/{client_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::client::detail::admin_client_detail;
        use maud::html;

        let client = admin_client_detail::call(
            app,
            identity,
            admin_client_detail::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        doc.set_title("Client Details");

        // Prepare display values using structured functional blocks
        let display_values = {
            let display_email = client.email.as_deref().unwrap_or("Email not available");
            let display_phone = client.phone.as_deref().unwrap_or("Phone not available");

            (display_email, display_phone)
        };

        // Prepare URLs for edit links using structured functional blocks
        let urls = {
            let edit_url = crate::ml_client_edit(client.client_uuid);
            let assign_advisor_url = crate::ml_client_assign_advisor(client.client_uuid);

            (edit_url, assign_advisor_url)
        };

        // Create separate InsightDeck instances for different logical groupings using structured functional blocks

        // 1. Client Identification InsightDeck (with action buttons in header)
        let client_identification = {
            let mut deck = bux::component::insight_deck::InsightDeck::new("Client Identification");
            deck.description("Core client identifiers and account creation information.");

            // Add general action buttons to the header
            deck.add_button(bux::button::link::label_icon_class(
                "Visit Advisor Dashboard",
                "fas fa-external-link-alt",
                &df4l_zero::ml_advisor(client.advisor_uuid),
                "sm primary",
            ));
            deck.add_button(html!(" "));
            deck.add_button(bux::button::link::label_icon_class(
                "Edit",
                "fas fa-pencil-alt",
                &urls.0,
                "sm",
            ));

            deck.add_basic_row(
                "fas fa-id-badge",
                "Client UUID",
                html!((client.client_uuid)),
            );

            deck.add_basic_row(
                "fas fa-calendar-plus",
                "Created On",
                html!((client.create_ts.format("%b %d, %Y %I:%M %p"))),
            );

            deck
        };

        // 2. Contact Information InsightDeck
        let contact_information = {
            let mut deck = bux::component::insight_deck::InsightDeck::new("Contact Information");
            deck.description("Client contact details and address information.");

            // Add unified edit button to the header
            deck.add_button(bux::button::link::label_icon_class(
                "Edit",
                "fas fa-pencil-alt",
                &urls.0,
                "sm",
            ));

            // Add basic rows without individual edit buttons
            deck.add_basic_row(
                "fas fa-envelope-open-text",
                "Email",
                html! {
                    @if let Some(email) = &client.email {
                        (email)
                    } @else {
                        "None"
                    }
                },
            );

            deck.add_basic_row(
                "fas fa-phone",
                "Phone",
                html! {
                    @if let Some(phone) = &client.phone {
                        (phone)
                    } @else {
                        "None"
                    }
                },
            );

            deck.add_basic_row(
                "fas fa-map-marker-alt",
                "Address",
                html! {
                    @if !client.address().trim().is_empty() {
                        (client.address())
                    } @else {
                        "None"
                    }
                },
            );

            deck
        };

        // 3. Administrative Information InsightDeck
        let administrative_information = {
            let mut deck =
                bux::component::insight_deck::InsightDeck::new("Administrative Information");
            deck.description("Advisor assignment and administrative notes for this client.");

            // Add administrative action buttons to the header
            deck.add_button(bux::button::link::label_icon_class(
                "Reassign Advisor",
                "fas fa-exchange-alt",
                &urls.1,
                "sm primary",
            ));
            deck.add_button(html!(" "));
            deck.add_button(bux::button::link::label_icon_class(
                "View Advisor",
                "fas fa-eye",
                &client.advisor_link,
                "sm secondary",
            ));

            deck.add_basic_row(
                "fas fa-user-tie",
                "Assigned Advisor",
                html! {
                    a href=(client.advisor_link) {
                        (format!("{} ({})", client.advisor_name, client.advisor_esid))
                    }
                },
            );

            deck.add_edit_row(
                "fas fa-toggle-on",
                "Status",
                html! {
                    @if client.active {
                        label-tag.success { "Active" }
                    } @else {
                        label-tag.warning { "Inactive" }
                    }
                },
                &urls.0,
            );

            // Admin note if available
            if let Some(note) = &client.note {
                if !note.trim().is_empty() {
                    deck.add_edit_row("fas fa-sticky-note", "Admin Note", html!((note)), &urls.0);
                }
            }

            deck
        };

        let html_content = html!(
            insight-deck {
                grid-12 {
                    cell-3 {
                        panel {
                            content {
                                contact-info {
                                    h1 {
                                (client.name)
                                @if client.is_demo {
                                    " " label-tag.warning { "DEMO" }
                                }
                            }
                                    p.phone.mb-0 {
                                        (display_values.1)
                                    }
                                    p.email {
                                        @if let Some(email) = &client.email {
                                            a href=(format!("mailto:{}", email)) { (email) }
                                        } @else {
                                            (display_values.0)
                                        }
                                    }
                                    hr;
                                    @if client.active {
                                        label-tag.success { "Active Client" }
                                    } @else {
                                        label-tag.warning { "Inactive Client" }
                                    }
                                }
                            }
                        }
                    }
                    cell-9 {
                        // This is the new rendering implementation for client details using the insight deck
                        (client_identification)
                        (contact_information)
                        (administrative_information)
                    }
                }
            }
        );

        doc.add_body(html_content);
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::http(GET /admin/client/{client_uuid:Uuid}/crs; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        use super::get_credit_reports;
        let reports = get_credit_reports::call(
            app,
            identity,
            get_credit_reports::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        doc.set_title("CRS");

        doc.add_body(html!(
            grid-2 {
                panel {
                    header {
                        "Primary Applicant"
                    }
                    content {
                        @match reports.primary_report {
                            Ok(Some(report)) => {
                                h3 { "Raw Report" }
                                pre {
                                    (format!("{:#?}", report))
                                }
                            }
                            Ok(None) => {
                                p { "No report found" }
                            }
                            Err(e) => {
                                p { "Error: " (e) }
                            }
                        }
                        @if let Some(debt_view) = reports.primary_debt_view {
                            h3 { "Debt View" }
                            pre {
                                (format!("{:#?}", debt_view))
                            }
                        }
                    }
                }
                panel {
                    header {
                        "Spouse"
                    }
                    content {
                        @match reports.secondary_report {
                            Ok(Some(report)) => {
                                h3 { "Raw Report" }
                                pre {
                                    (format!("{:#?}", report))
                                }
                            }
                            Ok(None) => {
                                p { "No report found" }
                            }
                            Err(e) => {
                                p { "Error: " (e) }
                            }
                        }
                        @if let Some(debt_view) = reports.secondary_debt_view {
                            h3 { "Debt View" }
                            pre {
                                (format!("{:#?}", debt_view))
                            }
                        }
                    }
                }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::function]
pub mod get_credit_reports {
    use granite::return_authorization_error;

    pub struct Input {
        pub client_uuid: granite::Uuid,
    }

    pub struct Output {
        pub primary_report: granite::Result<Option<api_crs::types::CrsUserLastReportUS1B>>,
        pub secondary_report: granite::Result<Option<api_crs::types::CrsUserLastReportUS1B>>,
        pub primary_debt_view: Option<api_crs::types::CRDebtView>,
        pub secondary_debt_view: Option<api_crs::types::CRDebtView>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.client_read(input.client_uuid) {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        // Lookup up primary and spouse api_crs_user_uuid
        let dbcx = app.postgres_dbcx().await?;
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                api_crs_user_uuid_applicant: Option<Uuid>,
                api_crs_user_uuid_spouse: Option<Uuid>,
            };
            SELECT
                api_crs_user_uuid_applicant,
                api_crs_user_uuid_spouse
            FROM
                df4l.client
            WHERE
                client_uuid = $client_uuid::uuid
        )
        .await?;

        let applicant_report = match row.api_crs_user_uuid_applicant {
            Some(uuid) => match api_crs::core::user::load(&dbcx, uuid).await? {
                api_crs::core::user::User::WithReport(with_report) => {
                    with_report.get_latest_report_b1r(app).await.map(Some)
                }
                _ => Ok(None),
            },
            None => Ok(None),
        };

        let spouse_report = match row.api_crs_user_uuid_spouse {
            Some(uuid) => match api_crs::core::user::load(&dbcx, uuid).await? {
                api_crs::core::user::User::WithReport(with_report) => {
                    with_report.get_latest_report_b1r(app).await.map(Some)
                }
                _ => Ok(None),
            },
            None => Ok(None),
        };

        let primary_debt_view = match &applicant_report {
            Ok(Some(report)) => Some(report.last_report_data.to_debt_view()),
            _ => None,
        };

        let secondary_debt_view = match &spouse_report {
            Ok(Some(report)) => Some(report.last_report_data.to_debt_view()),
            _ => None,
        };

        Ok(Output {
            primary_report: applicant_report,
            secondary_report: spouse_report,
            primary_debt_view,
            secondary_debt_view,
        })
    }
}

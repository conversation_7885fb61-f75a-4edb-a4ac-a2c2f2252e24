#[approck::http(GET /admin/client/add; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use df4l_zero::types::ClientType;
        use maud::html;

        doc.set_title("Add Client");

        let mut form_panel =
            bux::component::add_cancel_form_panel("Add New Client", &crate::ml_client_list());

        use crate::api::admin::advisor::list::admin_advisor_list;

        let advisors = admin_advisor_list::call(
            app,
            identity,
            admin_advisor_list::Input {
                keyword: None,
                active: Some(true),
            },
        )
        .await?;

        // Format advisors for dropdown: "Name (ESID)"
        let advisor_options_strings: Vec<(String, String)> = advisors
            .advisor_list
            .iter()
            .map(|a| {
                let display = format!("{} {}", a.first_name, a.last_name);
                (a.advisor_uuid.to_string(), display)
            })
            .collect();

        // Convert to the expected type for nilla_select
        let advisor_options: Vec<(&str, &str)> = advisor_options_strings
            .iter()
            .map(|(id, name)| (id.as_str(), name.as_str()))
            .collect();

        let client_type_options = vec![
            (
                ClientType::DebtManagement.key(),
                ClientType::DebtManagement.label(),
            ),
            (ClientType::PolicyOnly.key(), ClientType::PolicyOnly.label()),
        ];

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            grid-12 {
                cell-6 {
                    (bux::input::select::nilla::nilla_select(
                        "advisor_uuid",
                        "Advisor",
                        &advisor_options,
                        None,
                    ))
                    (bux::input::select::nilla::nilla_select(
                        "client_type",
                        "Client Type",
                        &client_type_options,
                        None,
                    ))
                    (bux::input::text::string::name_label_value("first_name", "Client First Name:", None))
                    (bux::input::text::string::name_label_value("last_name", "Client Last Name:", None))
                    (bux::input::text::string::name_label_value("email", "Email:", None))
                    (bux::input::text::string::name_label_value_help("phone", "Send Text Messages To Phone Number", None, "Do not include country code."))
                }
                cell-6 {
                    (bux::input::text::string::name_label_value("address1", "Address Line 1", None))
                    (bux::input::text::string::name_label_value("address2", "Address Line 2", None))
                    grid-3 {
                        (bux::input::text::string::name_label_value("city", "City", None))
                        (addr_iso::input::address_us_select::us_state_select_with_help(app, "state", "State", None, "").await?)
                        (bux::input::text::string::name_label_value("zip", "ZIP", None))
                    }
                    (bux::input::textarea::string::name_label_value("note", "Note:", None))

                }
            }
        ));
        doc.add_body(html! {
            section {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod admin_client_add {
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub client_type: ClientType,
        pub first_name: String,
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub note: Option<String>,
    }

    #[granite::gtype(ApiInput, ts_from = "@df4l-zero/typesλ.mts")]
    pub type ClientType = ::df4l_zero::types::ClientType;

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.client_add() {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to client add".to_string(),
            ));
        }

        let mut dbcx = app.postgres_dbcx().await?;

        // Transaction block
        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $advisor_uuid: &input.advisor_uuid,
                    $client_type: &input.client_type,
                    $first_name: &input.first_name,
                    $last_name: &input.last_name,
                    $email: &input.email,
                    $phone: &input.phone,
                    $address1: &input.address1,
                    $address2: &input.address2,
                    $city: &input.city,
                    $state: &input.state,
                    $zip: &input.zip,
                    $note: &input.note,
                };
                row = {
                    client_uuid: Uuid,
                };

                INSERT INTO
                    df4l.client
                    (
                        advisor_uuid,
                        client_type,
                        first_name,
                        last_name,
                        email,
                        phone,
                        address1,
                        address2,
                        city,
                        state,
                        zip,
                        note
                    )
                VALUES
                    (
                        $advisor_uuid,
                        $client_type,
                        $first_name,
                        $last_name,
                        $email,
                        $phone,
                        $address1,
                        $address2,
                        $city,
                        $state,
                        $zip,
                        $note
                    )
                RETURNING
                    client_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Response::Output(Output {
            client_uuid: row.client_uuid,
            detail_url: crate::ml_client(row.client_uuid),
        }))
    }
}

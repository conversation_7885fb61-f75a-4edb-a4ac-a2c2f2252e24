#[approck::http(GET /admin/advisor/{advisor_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {

    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        use crate::api::admin::advisor::detail::admin_advisor_detail;

        let advisor = admin_advisor_detail::call(
            app,
            identity,
            admin_advisor_detail::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        // Fetch identity details if advisor has an identity_uuid
        let identity_details = if let Some(identity_uuid) = advisor.identity_uuid {
            match auth_fence::api::admin::identity::detail::detail::call(
                app,
                identity,
                auth_fence::api::admin::identity::detail::detail::Input { identity_uuid },
            )
            .await
            {
                Ok(details) => Some(details),
                Err(e) => {
                    approck::warn!(
                        "Failed to fetch identity details for advisor {}: {}",
                        advisor.advisor_uuid,
                        e
                    );
                    None
                }
            }
        } else {
            None
        };

        // Fetch SSO providers if identity exists
        let sso_providers = if let Some(identity_uuid) = advisor.identity_uuid {
            match auth_fence::api::identity::sso::providers::index::call(
                app,
                identity,
                auth_fence::api::identity::sso::providers::index::Input { identity_uuid },
            )
            .await
            {
                Ok(providers) => Some(providers.providers),
                Err(e) => {
                    approck::warn!(
                        "Failed to fetch SSO providers for advisor {}: {}",
                        advisor.advisor_uuid,
                        e
                    );
                    None
                }
            }
        } else {
            None
        };

        doc.set_title("Advisor Details");

        // Prepare display values using structured functional blocks
        let display_values = {
            let display_email = advisor.email.as_deref().unwrap_or("Email not available");
            let display_phone = advisor.phone.as_deref().unwrap_or("Phone not available");

            (display_email, display_phone)
        };

        // Prepare URLs for edit links using structured functional blocks
        let urls = {
            let edit_url = crate::ml_advisor_edit(advisor.advisor_uuid);
            let add_agency_url = crate::ml_advisor_add_agency(advisor.advisor_uuid);
            let reassign_identity_url =
                format!("/admin/advisor/{}/reassign-identity", advisor.advisor_uuid);
            let statelic_edit_url = format!("/myaccount/advisor/{}/statelic", advisor.advisor_uuid);
            let status_edit_url = if let Some(identity_uuid) = advisor.identity_uuid {
                format!("/admin/auth/identity/{identity_uuid}/edit")
            } else {
                // Fallback to general advisor edit if no identity_uuid
                crate::ml_advisor_edit(advisor.advisor_uuid)
            };

            (
                edit_url,
                add_agency_url,
                reassign_identity_url,
                statelic_edit_url,
                status_edit_url,
            )
        };

        // Create separate InsightDeck instances for different logical groupings using structured functional blocks

        // 1. Advisor Identification InsightDeck (with action buttons in header)
        let advisor_identification = {
            let mut deck = bux::component::insight_deck::InsightDeck::new("Advisor Identification");
            deck.description("Core advisor identifiers and agency assignment information.");

            // Add general action buttons to the header
            deck.add_button(bux::button::link::label_icon_class(
                "Visit Advisor Dashboard",
                "fas fa-external-link-alt",
                &df4l_zero::ml_advisor(advisor.advisor_uuid),
                "sm primary",
            ));
            deck.add_button(html!(" "));
            deck.add_button(bux::button::link::label_icon_class(
                "Edit",
                "fas fa-pencil-alt",
                &urls.0,
                "sm",
            ));
            deck.add_button(html!(" "));
            deck.add_button(bux::button::link::label_icon_class(
                "Delete",
                "far fa-trash-alt",
                &crate::ml_advisor_delete(advisor.advisor_uuid),
                "sm danger",
            ));

            deck.add_basic_tile(
                "fas fa-id-badge",
                "Advisor ID",
                html!((advisor.advisor_esid)),
            );

            deck.add_edit_tile(
                "fas fa-pen",
                "GBU Advisor ID",
                match &advisor.gbu_advisor_esid {
                    Some(id) => html!((id)),
                    None => html!("None"),
                },
                urls.0.clone(),
            );

            deck.add_basic_row(
                "fas fa-calendar-plus",
                "Created On",
                html!((advisor.create_ts.format("%b %d, %Y %I:%M %p"))),
            );

            deck.add_edit_row(
               "fas fa-building",
               "Agency Assignment",
               html! {
                   @if advisor.agency_uuid.is_some() {
                       a href=(crate::ml_agency(advisor.agency_uuid.unwrap_or_default())) {
                           (advisor.agency_name.clone().unwrap_or_default()) " (" (advisor.agency_esid.clone().unwrap_or_default()) ")"
                       }
                   } @else {
                       "No agency assigned"
                   }
               },
               if advisor.agency_uuid.is_some() {
                   &urls.0
               } else {
                   &urls.1
               }
           );

            deck
        };

        // 2. Contact Information InsightDeck
        let contact_information = {
            let mut deck = bux::component::insight_deck::InsightDeck::new("Contact Information");
            deck.description("Advisor contact details and address information.");

            // Add unified edit button to the header
            deck.add_button(bux::button::link::label_icon_class(
                "Edit",
                "fas fa-pencil-alt",
                &urls.0,
                "sm",
            ));

            // Add basic rows without individual edit buttons
            deck.add_basic_row(
                "fas fa-envelope-open-text",
                "Email",
                html! {
                    @if let Some(email) = &advisor.email {
                        (email)
                    } @else {
                        "None"
                    }
                },
            );

            deck.add_basic_row(
                "fas fa-phone",
                "Phone",
                html! {
                    @if let Some(phone) = &advisor.phone {
                        (phone)
                    } @else {
                        "None"
                    }
                },
            );

            deck.add_basic_row(
                "fas fa-map-marker-alt",
                "Address",
                html! {
                    @if !advisor.address().trim().is_empty() {
                        (advisor.address())
                    } @else {
                        "None"
                    }
                },
            );

            deck
        };

        // 3. Administrative Information InsightDeck
        let administrative_information = {
            let mut deck =
                bux::component::insight_deck::InsightDeck::new("Administrative Information");
            deck.description("Status, licensing, and administrative details for this advisor.");

            deck.add_edit_row(
                "fas fa-toggle-on",
                "Status",
                html! {
                    @if advisor.active {
                        label-tag.success { "Active" }
                    } @else {
                        label-tag.warning { "Inactive" }
                    }
                },
                &urls.4,
            );

            deck.add_edit_row(
                "fas fa-id-badge",
                "States of Licensure",
                html! {
                    @if advisor.statelics.is_empty() {
                        label-tag.default { "None" }
                    } @else {
                        @for (i, statelic) in advisor.statelics.iter().enumerate() {
                            @if i > 0 {
                                " "
                            }
                            label-tag.primary { (statelic.label) }
                        }
                    }
                },
                &urls.3,
            );

            // Stripe Customer ID if available, link it to admin/stripe/customer/{customer_id}
            if let Some(stripe_customer_uuid) = advisor.api_stripe_customer_uuid {
                if let Some(stripe_url) = advisor.ml_stripe_customer() {
                    deck.add_basic_row(
                        "fab fa-cc-stripe",
                        "Stripe Customer ID",
                        html!(a href=(stripe_url) { (stripe_customer_uuid) }),
                    );
                }
            }

            // Admin note if available
            if let Some(admin_note) = &advisor.admin_note {
                if !admin_note.trim().is_empty() {
                    deck.add_edit_row(
                        "fas fa-sticky-note",
                        "Admin Note",
                        html!((admin_note)),
                        &urls.0,
                    );
                }
            }

            deck
        };

        // 4. Identity Information InsightDeck
        let identity_information = {
            let mut deck = bux::component::insight_deck::InsightDeck::new("Identity Information");
            deck.description("Login credentials and account access details for this advisor.");

            // Add identity-related action buttons to the header
            if advisor.identity_uuid.is_some() {
                deck.add_button(bux::button::link::label_icon_class(
                    "Reassign Identity",
                    "fas fa-exchange-alt",
                    &urls.2,
                    "sm primary",
                ));
                deck.add_button(html!(" "));
                deck.add_button(bux::button::link::label_icon_class(
                    "View Identity",
                    "fas fa-eye",
                    &auth_fence::api::admin::identity::types::ml_admin(
                        &advisor.identity_uuid.unwrap(),
                        "",
                    ),
                    "sm secondary",
                ));
            }

            deck.add_basic_row(
                "fas fa-user-circle",
                "Identity Status",
                html! {
                    @if let Some(identity_uuid) = &advisor.identity_uuid {
                        a href=(auth_fence::api::admin::identity::types::ml_admin(identity_uuid, "")) {
                            (identity_uuid)
                        }
                    } @else {
                        span.text-muted { "No identity linked (Login not available)" }
                    }
                },
            );

            // Identity details if available
            if let Some(ref details) = identity_details {
                deck.add_basic_row("fas fa-user", "Identity Name", html!((details.name)));

                if let Some(ref email) = details.email {
                    deck.add_basic_row(
                        "fas fa-envelope",
                        "Identity Email",
                        html!(a href=(format!("mailto:{}", email)) { (email) }),
                    );
                }
            }

            // Sign-in methods if available
            if let Some(ref providers) = sso_providers {
                let google_provider = providers
                    .iter()
                    .find(|p| p.ssopro_xsid.to_lowercase() == "google");
                let microsoft_provider = providers
                    .iter()
                    .find(|p| p.ssopro_xsid.to_lowercase() == "microsoft");

                // Google
                if let Some(google) = google_provider {
                    deck.add_basic_row(
                        "fab fa-google",
                        "Login with Google",
                        html! {
                            @if google.is_connected {
                                label-tag.success { "Enabled" }
                            } @else {
                                label-tag.default { "Not Enabled" }
                            }
                        },
                    );
                } else {
                    deck.add_basic_row(
                        "fab fa-google",
                        "Login with Google",
                        html! {
                            label-tag.default { "Not Available" }
                        },
                    );
                }

                // Microsoft
                if let Some(microsoft) = microsoft_provider {
                    deck.add_basic_row(
                        "fab fa-microsoft",
                        "Login with Microsoft",
                        html! {
                            @if microsoft.is_connected {
                                label-tag.success { "Enabled" }
                            } @else {
                                label-tag.default { "Not Enabled" }
                            }
                        },
                    );
                } else {
                    deck.add_basic_row(
                        "fab fa-microsoft",
                        "Login with Microsoft",
                        html! {
                            label-tag.default { "Not Available" }
                        },
                    );
                }
            }

            // Login/Password
            deck.add_basic_row(
                "fas fa-key",
                "Login with Username/Password",
                html! {
                    @if identity_details.is_some() {
                        label-tag.success { "Enabled" }
                    } @else {
                        label-tag.default { "Unknown" }
                    }
                },
            );

            deck
        };

        let html_content = html!(
            insight-deck {
                grid-12 {
                    cell-3 {
                        panel {
                            content {
                                contact-info {
                                    h1 { (advisor.name()) }
                                    p.phone.mb-0 {
                                        (display_values.1)
                                    }
                                    p.email {
                                        @if let Some(email) = &advisor.email {
                                            a href=(format!("mailto:{}", email)) { (email) }
                                        } @else {
                                            (display_values.0)
                                        }
                                    }
                                    hr;
                                    @if advisor.active {
                                        label-tag.success { "Active Advisor" }
                                    } @else {
                                        label-tag.warning { "Inactive Advisor" }
                                    }
                                }
                            }
                        }
                    }
                    cell-9 {
                        // This is the new rendering implementation for advisor details using the insight deck
                        (advisor_identification)
                        (identity_information)
                        (contact_information)
                        (administrative_information)
                    }
                }
            }
        );

        doc.add_body(html_content);
        Ok(Response::HTML(doc.into()))
    }
}

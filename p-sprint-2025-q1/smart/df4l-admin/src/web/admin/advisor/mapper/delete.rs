#[approck::http(GET /admin/advisor/{advisor_uuid:Uuid}/delete; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::advisor::detail::admin_advisor_detail;
        use maud::html;

        // Get advisor details
        let advisor = admin_advisor_detail::call(
            app,
            identity,
            admin_advisor_detail::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        doc.set_title("Delete Advisor");

        let title = format!(
            "Delete Advisor: {} {}?",
            advisor.first_name, advisor.last_name
        );

        let mut panel =
            bux::component::delete_cancel_form_panel(&title, &crate::ml_advisor(path.advisor_uuid));
        panel.set_hidden("advisor_uuid", path.advisor_uuid.to_string());

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            p {
                "This will deactivate the advisor "
                strong { (advisor.first_name) " " (advisor.last_name) }
                " (" (advisor.advisor_esid) "). "
                "The advisor will no longer appear in active advisor lists, but all historical data will be preserved."
            }
            p {
                "Any clients currently assigned to this advisor will remain assigned to them, but the advisor will not be able to access the system."
            }
            @if advisor.agency_uuid.is_some() {
                p {
                    "This advisor is currently associated with agency "
                    strong { (advisor.agency_name.as_deref().unwrap_or("Unknown")) }
                    ". The agency association will remain intact."
                }
            }
            (bux::input::checkbox::name_label_checked("confirm", "I understand the above and want to proceed with deleting this advisor.", false))
        ));

        doc.add_body(html!(
            bux-action-panel {
                (panel)
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

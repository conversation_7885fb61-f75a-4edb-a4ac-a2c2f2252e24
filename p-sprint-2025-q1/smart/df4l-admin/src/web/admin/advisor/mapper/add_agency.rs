#[approck::http(GET /admin/advisor/{advisor_uuid:Uuid}/add_agency; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::advisor::detail::admin_advisor_detail;
        use crate::api::admin::agency::list::admin_agency_list;
        use maud::html;

        // Get advisor details
        let advisor = admin_advisor_detail::call(
            app,
            identity,
            admin_advisor_detail::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        // Get list of all active agencies
        let agencies = admin_agency_list::call(
            app,
            identity,
            admin_agency_list::Input {
                keyword: None,
                active: Some(true), // Only show active agencies
            },
        )
        .await?;

        // Format agencies for dropdown: "Name (ESID)"
        // Convert to Vec<(&str, &str)> for nilla_select
        let agency_options_strings: Vec<(String, String)> = agencies
            .agency_list
            .iter()
            .map(|a| {
                let display = format!("{} ({})", a.name, a.agency_esid);
                (a.agency_uuid.to_string(), display)
            })
            .collect();

        // Convert to the expected type for nilla_select
        let agency_options: Vec<(&str, &str)> = agency_options_strings
            .iter()
            .map(|(id, name)| (id.as_str(), name.as_str()))
            .collect();

        doc.set_title(&format!("Add Agency to {}", advisor.name()));

        let mut form_panel = bux::component::add_cancel_form_panel(
            &format!("Add Agency to Advisor: {}", advisor.name()),
            &format!("/admin/advisor/{}/", path.advisor_uuid),
        );

        form_panel.set_hidden("advisor_uuid", path.advisor_uuid);

        #[rustfmt::skip]
        form_panel.add_body(html!(
            (bux::input::select::nilla::nilla_select(
                "agency_uuid",
                "Select Agency:",
                &agency_options,
                None
            ))
        ));

        doc.add_body(html! {
            bux-action-panel {
                (form_panel)
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::http(GET /admin/advisor/{advisor_uuid:Uuid}/client0/assign-advisor; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Assign Advisor Client");

        let mut panel = bux::component::save_cancel_form_panel(
            "Assign Advisor",
            "/admin/advisor/00000000-0000-0000-0000-000000000000/client0/",
        );

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            grid-12 {
                cell-6 {
                    (bux::input::text::string::name_label_value("client_name", "Client Name:", None))
                    (bux::input::text::string::name_label_value("client_email", "Client Email:", None))
                    (bux::input::text::string::name_label_value("mobile_phone", "Client Mobile Phone:",None))
                }
                cell-6 {
                    (bux::input::select::nilla::nilla_select("select_advisor", "★ Select Advisor:", &[], None))
                    (bux::input::textarea::string::name_label_value("notes", "Notes:", None))
                }
            }

        ));
        doc.add_body(html! {
            section {
                (panel)
            }
        });
        Response::HTML(doc.into())
    }
}

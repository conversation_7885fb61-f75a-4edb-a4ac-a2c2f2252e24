#[approck::http(GET /admin/advisor/{advisor_uuid:Uuid}/client0/upcoming-changes; AUTH None; return HTML;)]
pub mod page {
    use crate::web::admin::advisor::mapper::client0::upcoming_changes::calculate_next3months;

    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Upcoming Changes");
        doc.set_body_display_fluid();

        use crate::api::admin::client0::upcoming_changes::admin_client_list;

        let output = admin_client_list::call(
            app,
            identity,
            admin_client_list::Input {
                keyword: None,
                active: Some(true),
                advisor_uuid: Some(path.advisor_uuid),
            },
        )
        .await?;

        let next_months = calculate_next3months();

        let mut dt = bux::component::detail_table(output.client_list);
        dt.add_link_column(
            "Client Name",
            |a| crate::ml_client0(a.client_uuid),
            |a| a.name.clone(),
        );

        // Add DF4L Status column
        dt.add_column("DF4L Status", |a| {
            html! {
                (maud::PreEscaped(&a.debt_free_status_html))
            }
        });

        // Add columns in a loop
        for month in &next_months {
            dt.add_column(month, |_a| html! { "" });
        }

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}

//function needs to be moved to api
pub fn calculate_next3months() -> Vec<String> {
    use chrono::{Datelike, Local, NaiveDate};

    // Get current date
    let current_date = Local::now().date_naive();

    // Calculate next month and next 2 months
    let onemonth_date = if current_date.month() == 12 {
        NaiveDate::from_ymd_opt(current_date.year() + 1, 1, current_date.day().min(31)).unwrap()
    } else {
        NaiveDate::from_ymd_opt(
            current_date.year(),
            current_date.month() + 1,
            current_date.day().min(31),
        )
        .unwrap()
    };

    let twomonths_date = if current_date.month() >= 11 {
        NaiveDate::from_ymd_opt(
            current_date.year() + (current_date.month() == 12) as i32,
            (current_date.month() + 2) % 12,
            current_date.day().min(31),
        )
        .unwrap()
    } else {
        NaiveDate::from_ymd_opt(
            current_date.year(),
            current_date.month() + 2,
            current_date.day().min(31),
        )
        .unwrap()
    };

    // Format dates
    let current_month = format!("{}, {}", current_date.format("%B"), current_date.year());

    let one_month = format!("{}, {}", onemonth_date.format("%B"), onemonth_date.year());

    let two_months = format!("{}, {}", twomonths_date.format("%B"), twomonths_date.year());

    vec![current_month, one_month, two_months]
}

#[approck::http(GET /admin/advisor/{advisor_uuid:Uuid}/client0/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        qs: QueryString,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Clients");

        use crate::api::admin::client0::list::admin_client_list;

        let active = bux::parse_active_qs(&qs.active, Some(true));
        let output = admin_client_list::call(
            app,
            identity,
            admin_client_list::Input {
                keyword: qs.keyword.clone(),
                active,
                advisor_uuid: Some(path.advisor_uuid),
            },
        )
        .await?;

        let mut dt = bux::component::detail_table(output.client_list);

        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        dt.add_link_column(
            "Client Name",
            |a| crate::ml_client0(a.client_uuid),
            |a| a.name.clone(),
        );
        dt.add_column(
            "Added On",
            |a| html! { (a.create_ts.format("%B %d, %Y").to_string()) },
        );
        dt.add_active_status_column("Client Status", |a| a.active);
        dt.add_details_column(|a| crate::ml_client0(a.client_uuid));

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}

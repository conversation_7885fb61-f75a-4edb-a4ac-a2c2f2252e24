#[approck::http(GET /admin/advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/delete?inactive; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document, qs: QueryString) -> Response {
        let is_inactive: bool = qs.inactive;

        println!("Derived is_inactive: {is_inactive}");

        use crate::web::status::AdminStatusSelection;
        use bux::component::set_status_panel::StatusPanel;
        use maud::{Render, html};

        doc.set_title("Remove Client");

        let status_panel = StatusPanel::with_defaults(
            "",
            html! {
                p { "Select the status to remove the Client." }
            },
            vec![AdminStatusSelection::Active, AdminStatusSelection::Inactive],
            "/admin/advisor/00000000-0000-0000-0000-000000000000/client0/",
        );

        doc.add_body(html!(
            @if is_inactive {
                bux-action-panel  {
                    (status_panel.render())
                }
            }
            @else {
                bux-action-panel  {
                    (status_panel.render())
                }
            }
        ));
        Response::HTML(doc.into())
    }
}

#[approck::http(GET /admin/agency/{agency_uuid:Uuid}/clientlist/?keyword=Option<String>&active=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        qs: QueryString,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Client List");

        use crate::api::admin::agency::client_list::admin_agency_client_list;

        let active = bux::parse_active_qs(&qs.active, Some(true));
        let output = admin_agency_client_list::call(
            app,
            identity,
            admin_agency_client_list::Input {
                agency_uuid: path.agency_uuid,
                keyword: qs.keyword.clone(),
                active,
            },
        )
        .await?;

        // Create a detail table with agency clients
        let mut dt = bux::component::detail_table(output.client_list);
        dt.add_keyword_filter(qs.keyword.as_deref());
        dt.add_active_filter(active);

        dt.add_column("Client Name", |c| {
            let href = crate::ml_client(c.client_uuid);
            let name = format!("{} {}", c.first_name, c.last_name);
            html! {
                a href=(href) {
                    (name)
                    @if c.is_demo {
                        " " label-tag.warning { "DEMO" }
                    }
                }
            }
        });

        dt.add_active_status_column("Client Status", |c| c.active);
        dt.add_column("Email", |c| html! { (c.email.as_deref().unwrap_or("")) });
        dt.add_column("Phone", |c| html! { (c.phone.as_deref().unwrap_or("")) });
        dt.add_link_column(
            "Advisor Name",
            |c| crate::ml_advisor(c.advisor_uuid),
            |c| c.advisor_name.clone(),
        );
        dt.add_column("Advisor ID", |c| html! { (c.advisor_esid) });
        dt.add_active_status_column("Advisor Status", |c| c.advisor_active);

        dt.add_column(
            "Added On",
            |c| html! { (c.create_ts.format("%B %d, %Y").to_string()) },
        );
        dt.add_details_column(|c| crate::ml_client(c.client_uuid));

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}

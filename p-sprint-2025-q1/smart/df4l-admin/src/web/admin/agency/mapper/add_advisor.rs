#[approck::http(GET /admin/agency/{agency_uuid:Uuid}/add_advisor; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::advisor::list::admin_advisor_list;
        use crate::api::admin::agency::detail::admin_agency_detail;
        use maud::html;

        let agency = admin_agency_detail::call(
            app,
            identity,
            admin_agency_detail::Input {
                agency_uuid: path.agency_uuid,
            },
        )
        .await?;

        // Get list of all active advisors
        let advisors = admin_advisor_list::call(
            app,
            identity,
            admin_advisor_list::Input {
                keyword: None,
                active: Some(true), // Only show active advisors
            },
        )
        .await?;

        // Format advisors for dropdown: "First Last (ESID)"
        // Convert to Vec<(&str, &str)> for nilla_select
        let advisor_options_strings: Vec<(String, String)> = advisors
            .advisor_list
            .iter()
            .map(|a| {
                let display = format!("{} {} ({})", a.first_name, a.last_name, a.advisor_esid);
                (a.advisor_uuid.to_string(), display)
            })
            .collect();

        // Convert to the expected type for nilla_select
        let advisor_options: Vec<(&str, &str)> = advisor_options_strings
            .iter()
            .map(|(id, name)| (id.as_str(), name.as_str()))
            .collect();

        doc.set_title(&format!("Add Advisor to {}", agency.name));

        let mut form_panel = bux::component::add_cancel_form_panel(
            &format!("Add Advisor to Agency: {}", agency.name),
            &crate::ml_agency(path.agency_uuid),
        );

        form_panel.set_hidden("agency_uuid", path.agency_uuid);

        #[rustfmt::skip]
        form_panel.add_body(html!(
            (bux::input::select::nilla::nilla_select(
                "advisor_uuid",
                "Select Advisor:",
                &advisor_options,
                None
            ))
        ));

        doc.add_body(html! {
            bux-action-panel {
                (form_panel)
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}

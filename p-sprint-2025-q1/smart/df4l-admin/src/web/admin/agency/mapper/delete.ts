import "./delete.mcss";
import "@bux/input/checkbox.mjs";
import { SE } from "@granite/lib.mts";
import { admin_agency_delete } from "@crate/api/admin/agency/deleteλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
// Hidden agency_uuid
const $agency_uuid: HTMLInputElement = SE($form, "[name=agency_uuid]");
// Confirm checkbox
const $confirm: HTMLInputElement = SE($form, "input[type=checkbox]");
// Submit button
const $submitButton: HTMLButtonElement = SE($form, "button[type=submit]");

// Initially disable the submit button
$submitButton.disabled = true;

// Enable/disable submit button based on checkbox state
$confirm.addEventListener("change", () => {
    $submitButton.disabled = !$confirm.checked;
});

new FormPanel({
    $form,
    api: admin_agency_delete.api,
    on_cancel: go_back,

    err: (errors) => {
        // Handle any validation errors if needed
        console.error("Delete agency errors:", errors);
    },

    get: () => {
        return {
            agency_uuid: $agency_uuid.value,
        };
    },

    set: (_value) => {
        // No need to set values for delete form
    },

    out: (output) => {
        go_next((output as any).detail_url);
    },
});

#[approck::http(GET /admin/agency/{agency_uuid:Uuid}/delete; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::agency::detail::admin_agency_detail;
        use maud::html;

        // Get agency details
        let agency = admin_agency_detail::call(
            app,
            identity,
            admin_agency_detail::Input {
                agency_uuid: path.agency_uuid,
            },
        )
        .await?;

        doc.set_title("Delete Agency");

        let title = format!("Delete Agency: {}?", agency.name);

        let mut panel =
            bux::component::delete_cancel_form_panel(&title, &crate::ml_agency(path.agency_uuid));
        panel.set_hidden("agency_uuid", path.agency_uuid.to_string());

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            p {
                "This will deactivate the agency "
                strong { (agency.name) }
                " (" (agency.agency_esid) "). "
                "The agency will no longer appear in active agency lists, but all historical data will be preserved."
            }
            p {
                "Any advisors currently associated with this agency will remain in the system but will need to be reassigned to a new agency."
            }
            (bux::input::checkbox::name_label_checked("confirm", "I understand the above and want to proceed with deleting this agency.", false))
        ));

        doc.add_body(html!(
            bux-action-panel {
                (panel)
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod calculate {
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub numerator: Decimal,
        pub denominator: <PERSON><PERSON><PERSON>,
        pub operator: Operator,
    }

    #[granite::gtype(ApiInput)]
    pub enum Operator {
        Add,
        Subtract,
        Multiply,
        Divide,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub answer: Decimal,
    }

    pub async fn call(app: App, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        // get a random value from the db
        let random_value = granite::pg_row!(db = dbcx;
            row = {
                random_value: Decimal,
            };
            SELECT
                (random()*1000)::numeric AS random_value
        )
        .await?
        .random_value;

        approck::info!("random_value: {}", random_value);

        let answer_result = match input.operator {
            Operator::Add => input.numerator.checked_add(input.denominator),
            Operator::Subtract => input.numerator.checked_sub(input.denominator),
            Operator::Multiply => input.numerator.checked_mul(input.denominator),
            Operator::Divide => input.numerator.checked_div(input.denominator),
        };

        match answer_result {
            Some(answer) => Ok(Output { answer }),
            None => Err(granite::Error::new(granite::ErrorType::Validation)),
        }
    }
}

#[approck::api]
pub mod multiply {
    #[granite::gtype(ApiInput)]
    pub struct Input(pub i32, pub i32);

    #[granite::gtype(ApiOutput)]
    pub struct Output(pub i32);

    pub async fn call(input: Input) -> Result<Output> {
        Ok(Output(input.0 * input.1))
    }
}

#[approck::api]
pub mod admin_agency_edit {

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub agency_uuid: Uuid,
        pub agency_esid: String,
        pub name: Option<String>,
        pub admin_note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.agency_write(input.agency_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to edit agency".to_string(),
            ));
        }

        let dbcx = app.postgres_dbcx().await?;

        granite::pg_execute!(
            db = dbcx;
            args = {
                $agency_uuid: &input.agency_uuid,
                $agency_esid: &input.agency_esid,
                $name: &input.name,
                $admin_note: &input.admin_note
            };
            UPDATE
                df4l.agency
            SET
                agency_esid = $agency_esid,
                name = $name,
                admin_note = $admin_note
            WHERE
                agency_uuid = $agency_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: crate::ml_agency(input.agency_uuid),
            message: "Agency updated".into(),
        }))
    }
}

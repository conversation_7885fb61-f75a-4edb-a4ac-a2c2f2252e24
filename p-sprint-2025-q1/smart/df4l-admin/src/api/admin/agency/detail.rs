#[approck::api]
pub mod admin_agency_detail {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub agency_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub agency_uuid: Uuid,
        pub agency_esid: String,
        pub create_ts: DateTimeUtc,
        pub name: String,
        pub admin_note: Option<String>,
        pub active: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.agency_read(input.agency_uuid) {
            return_authorization_error!("insufficient permissions to agency {}", input.agency_uuid);
        }

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $agency_uuid: &input.agency_uuid,
            };
            row = {
                agency_uuid: Uuid,
                agency_esid: String,
                create_ts: DateTimeUtc,
                name: String,
                admin_note: Option<String>,
                active: bool,
            };
            SELECT
                agency_uuid,
                agency_esid,
                create_ts,
                name,
                admin_note,
                active
            FROM
                df4l.agency
            WHERE true
                AND agency_uuid = $agency_uuid::uuid
        )
        .await?;

        Ok(Output {
            agency_uuid: row.agency_uuid,
            agency_esid: row.agency_esid,
            create_ts: row.create_ts,
            name: row.name,
            admin_note: row.admin_note,
            active: row.active,
        })
    }
}

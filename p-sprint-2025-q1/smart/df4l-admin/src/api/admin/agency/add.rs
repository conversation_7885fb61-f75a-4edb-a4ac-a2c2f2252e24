#[approck::api]
pub mod admin_agency_add {
    use granite::{NestedError, ResultExt};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        #[gtype(trim=both; max=128; no_empty;)]
        pub name: String,

        #[gtype(<(trim=both;)>)]
        pub admin_note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub agency_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.agency_add() {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to agency add".to_string(),
            ));
        }

        if input.name == "bad" {
            let input_error = Input_Error {
                name: Some("name cannot be bad".into()),
                admin_note: None,
            };

            return Ok(Response::ValidationError(NestedError {
                outer: "validation error".to_string(),
                inner: Some(input_error),
            }));
        }

        let mut dbcx = app.postgres_dbcx().await?;

        // Transaction block
        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;
            let mut i = 0;

            // loop 10 times and query the database to make sure the new ESID is not present
            let agency_esid = loop {
                let agency_esid = new_agent_id();

                if granite::pg_row!(db = dbtx;
                    args = {
                        $agency_esid: &agency_esid,
                    };
                    row = {
                        ok: bool,
                    };
                    SELECT NOT EXISTS (
                        SELECT
                        FROM df4l.agency
                        WHERE agency_esid = $agency_esid::varchar
                    ) AS ok
                )
                .await?
                .ok
                {
                    break agency_esid;
                }

                i += 1;
                if i > 10 {
                    return Err(granite::Error::new(granite::ErrorType::Unexpected)
                        .add_context("failed to generate unique agency_esid"));
                }
            };

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $name: &input.name,
                    $agency_esid: &agency_esid,
                    $admin_note: &input.admin_note,
                };
                row = {
                    agency_uuid: Uuid,
                };

                INSERT INTO
                    df4l.agency
                    (name, agency_esid, admin_note)
                VALUES
                    ($name, $agency_esid, $admin_note)
                RETURNING
                    agency_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Response::Output(Output {
            agency_uuid: row.agency_uuid,
            detail_url: crate::ml_agency(row.agency_uuid),
        }))
    }

    fn new_agent_id() -> String {
        use rand::Rng;
        let mut rng = rand::rng();

        let first = rng.random_range(0..1000);
        let second = rng.random_range(0..100);

        format!("{first:03}-{second:02}")
    }
}

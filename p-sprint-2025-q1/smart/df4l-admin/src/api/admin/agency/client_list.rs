#[approck::api]
pub mod admin_agency_client_list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub agency_uuid: Uuid,
        pub keyword: Option<String>,
        pub active: Option<bool>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_list: Vec<Client>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Client {
        pub client_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub advisor_uuid: Uuid,
        pub advisor_esid: String,
        pub advisor_name: String,
        pub advisor_active: bool,
        pub agency_uuid: Uuid,
        pub agency_name: String,
        pub agency_esid: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub create_ts: DateTimeUtc,
        pub active: bool,
        pub note: Option<String>,
        pub is_demo: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.agency_client_list(input.agency_uuid) {
            return_authorization_error!("insufficient permissions to agency client list");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $agency_uuid: &input.agency_uuid,
                $keyword: &input.keyword,
                $active: &input.active,
            };
            row = {
                client_uuid: Uuid,
                first_name: String,
                last_name: String,
                advisor_uuid: Uuid,
                advisor_esid: String,
                advisor_first_name: String,
                advisor_last_name: String,
                advisor_active: bool,
                agency_uuid: Uuid,
                agency_name: String,
                agency_esid: String,
                email: Option<String>,
                phone: Option<String>,
                create_ts: DateTimeUtc,
                active: bool,
                note: Option<String>,
                is_demo: bool,
            };
            SELECT
                c.client_uuid,
                c.first_name,
                c.last_name,
                a.advisor_uuid,
                a.advisor_esid,
                a.first_name as advisor_first_name,
                a.last_name as advisor_last_name,
                a.active as advisor_active,
                ag.agency_uuid,
                ag.name as agency_name,
                ag.agency_esid,
                c.email,
                c.phone,
                c.create_ts,
                c.active,
                c.note,
                c.is_demo
            FROM
                df4l.client c
            INNER JOIN
                df4l.advisor a ON c.advisor_uuid = a.advisor_uuid
            INNER JOIN
                df4l.agency ag ON a.agency_uuid = ag.agency_uuid
            WHERE
                ag.agency_uuid = $agency_uuid::uuid
                AND ($keyword::text IS NULL OR
                    c.first_name ILIKE "%" || $keyword::text || "%" OR
                    c.last_name ILIKE "%" || $keyword::text || "%")
                AND ($active::bool IS NULL OR c.active = $active::bool)
            ORDER BY
                c.first_name, c.last_name
        )
        .await?;

        Ok(Output {
            client_list: rows
                .into_iter()
                .map(|r| Client {
                    client_uuid: r.client_uuid,
                    first_name: r.first_name,
                    last_name: r.last_name,
                    advisor_uuid: r.advisor_uuid,
                    advisor_esid: r.advisor_esid,
                    advisor_name: format!("{} {}", r.advisor_first_name, r.advisor_last_name),
                    advisor_active: r.advisor_active,
                    agency_uuid: r.agency_uuid,
                    agency_name: r.agency_name,
                    agency_esid: r.agency_esid,
                    email: r.email,
                    phone: r.phone,
                    create_ts: r.create_ts,
                    active: r.active,
                    note: r.note,
                    is_demo: r.is_demo,
                })
                .collect(),
        })
    }
}

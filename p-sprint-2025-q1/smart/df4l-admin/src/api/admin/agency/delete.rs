#[approck::api]
pub mod admin_agency_delete {

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub agency_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.agency_write(input.agency_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to delete agency".to_string(),
            ));
        }

        let dbcx = app.postgres_dbcx().await?;

        // Soft delete by setting active = false
        granite::pg_execute!(
            db = dbcx;
            args = {
                $agency_uuid: &input.agency_uuid,
            };
            UPDATE
                df4l.agency
            SET
                active = false
            WHERE
                agency_uuid = $agency_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: "/admin/agency/".to_string(),
            message: "Agency deleted".into(),
        }))
    }
}

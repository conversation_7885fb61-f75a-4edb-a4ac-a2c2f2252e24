#[approck::api]
pub mod advsior_unlink {

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub agency_uuid: Uuid,
        pub advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.agency_write(input.agency_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to remove advisor from this agency".to_string(),
            ));
        }

        let dbcx = app.postgres_dbcx().await?;

        // Update the advisor to remove the agency association
        granite::pg_execute!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };

            UPDATE df4l.advisor
            SET agency_uuid = NULL
            WHERE advisor_uuid = $advisor_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: format!("/admin/agency/{}/advisorlist/", input.agency_uuid),
            message: "Advisor removed from agency".into(),
        }))
    }
}

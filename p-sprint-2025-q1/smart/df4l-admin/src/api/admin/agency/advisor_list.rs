#[approck::api]
pub mod admin_agency_advisor_list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub agency_uuid: Uuid,
        pub keyword: Option<String>,
        pub active: Option<bool>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub advisor_list: Vec<Advisor>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Advisor {
        pub advisor_uuid: Uuid,
        pub advisor_esid: String,
        pub gbu_advisor_esid: Option<String>,
        pub create_ts: DateTimeUtc,
        pub first_name: String,
        pub last_name: String,
        pub active: bool,
        pub admin_note: Option<String>,
        pub advisor_client_active_count: i64,
        pub advisor_client_active_count_df4l_activated: i64,
        pub advisor_client_debt_balance: Option<Decimal>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.agency_advisor_list(input.agency_uuid) {
            return_authorization_error!("insufficient permissions to agency advisor list");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $agency_uuid: &input.agency_uuid,
                $keyword: &input.keyword,
                $active: &input.active,
            };
            row = {
                advisor_uuid: Uuid,
                advisor_esid: String,
                gbu_advisor_esid: Option<String>,
                create_ts: DateTimeUtc,
                first_name: String,
                last_name: String,
                admin_note: Option<String>,
                active: bool,
                advisor_client_active_count: i64,
                advisor_client_active_count_df4l_activated: i64,
                advisor_client_debt_balance: Option<Decimal>,
            };
            SELECT
                advisor_uuid,
                advisor_esid,
                gbu_advisor_esid,
                create_ts,
                first_name,
                last_name,
                admin_note,
                active,
                (
                SELECT
                    COALESCE(COUNT(*), 0)
                FROM
                    df4l.client0 AS ac2
                WHERE
                    ac2.active
                    AND advisor_uuid = advisor.advisor_uuid
                ) as advisor_client_active_count,
                (
                SELECT
                    COALESCE(COUNT(*), 0)
                FROM
                    df4l.client0 AS ac2
                WHERE
                    ac2.active
                    AND ac2.debt_free_active
                    AND advisor_uuid = advisor.advisor_uuid
                ) as advisor_client_active_count_df4l_activated,
                (
                SELECT
                    SUM(balance)
                FROM
                    df4l.client0_debt
                INNER JOIN df4l.client0 USING(client_uuid)
                WHERE
                    client0.debt_free_active
                    AND advisor_uuid = advisor.advisor_uuid
                ) as advisor_client_debt_balance
            FROM
                df4l.advisor
            WHERE
                agency_uuid = $agency_uuid::uuid
                AND ($keyword::text IS NULL OR first_name ILIKE "%" || $keyword::text || "%" OR last_name ILIKE "%" || $keyword::text || "%")
                AND ($active::bool IS NULL OR active = $active::bool)
            ORDER BY
                first_name, last_name
        )
        .await?;

        Ok(Output {
            advisor_list: rows
                .into_iter()
                .map(|r| Advisor {
                    advisor_uuid: r.advisor_uuid,
                    advisor_esid: r.advisor_esid,
                    gbu_advisor_esid: r.gbu_advisor_esid,
                    create_ts: r.create_ts,
                    first_name: r.first_name,
                    last_name: r.last_name,
                    admin_note: r.admin_note,
                    active: r.active,
                    advisor_client_active_count: r.advisor_client_active_count,
                    advisor_client_active_count_df4l_activated: r
                        .advisor_client_active_count_df4l_activated,
                    advisor_client_debt_balance: r.advisor_client_debt_balance,
                })
                .collect(),
        })
    }
}

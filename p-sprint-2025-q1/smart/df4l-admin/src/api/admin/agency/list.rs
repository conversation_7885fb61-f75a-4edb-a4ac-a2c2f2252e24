#[approck::api]
pub mod admin_agency_list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
        pub active: Option<bool>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub agency_list: Vec<Agency>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Agency {
        pub agency_uuid: Uuid,
        pub agency_esid: String,
        pub create_ts: DateTimeUtc,
        pub name: String,
        pub admin_note: Option<String>,
        pub active: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.agency_list() {
            return_authorization_error!("insufficient permissions to agency list");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $keyword: &input.keyword,
                $active: &input.active,
            };
            row = {
                agency_uuid: Uuid,
                agency_esid: String,
                create_ts: DateTimeUtc,
                name: String,
                admin_note: Option<String>,
                active: bool,
            };
            SELECT
                agency_uuid,
                agency_esid,
                create_ts,
                name,
                admin_note,
                active
            FROM
                df4l.agency
            WHERE true
                AND ($keyword::text IS NULL OR name ILIKE "%" || $keyword::text || "%")
                AND ($active::bool IS NULL OR active = $active::bool)
            ORDER BY
                name
        )
        .await?;

        Ok(Output {
            agency_list: rows
                .into_iter()
                .map(|r| Agency {
                    agency_uuid: r.agency_uuid,
                    agency_esid: r.agency_esid,
                    create_ts: r.create_ts,
                    name: r.name,
                    admin_note: r.admin_note,
                    active: r.active,
                })
                .collect(),
        })
    }
}

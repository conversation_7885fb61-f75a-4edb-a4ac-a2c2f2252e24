#[approck::api]
pub mod admin_client_list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
        pub active: Option<bool>,
        pub advisor_uuid: Option<Uuid>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_list: Vec<Client>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Client {
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
        pub advisor_esid: String,
        pub create_ts: DateTimeUtc,
        pub active: bool,
        pub name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub debt_free_active: bool,
    }
    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.client_list() {
            return_authorization_error!("insufficient permissions to client list");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $keyword: &input.keyword,
                $active: &input.active,
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                client_uuid: Uuid,
                advisor_uuid: Uuid,
                advisor_esid: String,
                create_ts: DateTimeUtc,
                active: bool,
                name: String,
                email: Option<String>,
                phone: Option<String>,
                debt_free_active: bool,
            };
            SELECT
                c.client_uuid,
                c.advisor_uuid,
                a.advisor_esid,
                c.create_ts,
                c.active,
                c.first_name || " " || c.last_name AS name,
                c.email,
                c.phone,
                c.debt_free_active
            FROM
                df4l.client0 c
                INNER JOIN df4l.advisor a ON c.advisor_uuid = a.advisor_uuid
            WHERE true
                AND (
                    $keyword::text IS NULL
                    OR
                    c.first_name ILIKE "%" || $keyword::text || "%"
                    OR
                    c.last_name ILIKE "%" || $keyword::text || "%"
                    OR
                    (c.first_name || " " || c.last_name) ILIKE "%" || $keyword::text || "%"
                )
                AND ($active::bool IS NULL OR c.active = $active::bool)
                AND ($advisor_uuid::uuid IS NULL OR c.advisor_uuid = $advisor_uuid::uuid)
            ORDER BY
                c.first_name, c.last_name
        )
        .await?;

        Ok(Output {
            client_list: rows
                .into_iter()
                .map(|r| Client {
                    client_uuid: r.client_uuid,
                    advisor_uuid: r.advisor_uuid,
                    advisor_esid: r.advisor_esid,
                    create_ts: r.create_ts,
                    active: r.active,
                    name: r.name,
                    email: r.email,
                    phone: r.phone,
                    debt_free_active: r.debt_free_active,
                })
                .collect(),
        })
    }
}

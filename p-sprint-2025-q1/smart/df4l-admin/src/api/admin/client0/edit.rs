#[approck::api]
pub mod admin_client_edit {

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub phone2: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.client_write(input.client_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to edit client".to_string(),
            ));
        }

        let dbcx = app.postgres_dbcx().await?;

        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
                $first_name: &input.first_name,
                $last_name: &input.last_name,
                $email: &input.email,
                $phone: &input.phone,
                $phone2: &input.phone2,
            };
            UPDATE
                df4l.client0
            SET
                first_name = $first_name,
                last_name = $last_name,
                email = $email,
                phone = $phone,
                phone2 = $phone2

            WHERE
                client_uuid = $client_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: crate::ml_client0(input.client_uuid),
            message: "Aadvisor updated".into(),
        }))
    }
}

#[approck::api]
pub mod admin_client_detail {
    use granite::Decimal;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub enum DF4LStatus {
        Active(String),
        HasDataButNotEnrolled,
        NotEnrolled,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
        pub advisor_name: String,
        pub advisor_link: String,
        pub create_ts: DateTimeUtc,
        pub name: String,
        pub first_name: String,
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub phone2: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,

        pub active: bool,
        pub insurance_premium_formatted: String,
        pub insurance_pua_formatted: String,
        pub total_insurance_premium_formatted: String,
        pub df4l_status: DF4LStatus,
        pub df4l_status_html: String,
        pub active_debts: i64,
        pub minimum_monthly_payment: Option<Decimal>,
        pub minimum_monthly_payment_html: String,
        pub annual_budget: Option<Decimal>,
        pub annual_budget_html: String,
        pub annual_insurance_premium: Option<Decimal>,
        pub annual_insurance_premium_html: String,
        pub annual_insurance_base: Option<Decimal>,
        pub annual_insurance_base_html: String,
        pub annual_insurance_pua: Option<Decimal>,
        pub annual_insurance_pua_html: String,
    }

    // Format decimal optional value as $2,050/mo $24,600/yr with proper comma formatting
    fn format_decimal_optional(value: Option<Decimal>) -> String {
        if let Some(value) = value {
            let monthly = value / Decimal::from(12);
            let yearly = value;

            // Use bux formatting for proper comma formatting
            let monthly_str = if monthly.fract().is_zero() {
                format!("{}/mo", bux::format_currency_us_0(monthly.trunc()))
            } else {
                format!("{}/mo", bux::format_currency_us_2(monthly))
            };

            let yearly_str = if yearly.fract().is_zero() {
                format!("{}/yr", bux::format_currency_us_0(yearly.trunc()))
            } else {
                format!("{}/yr", bux::format_currency_us_2(yearly))
            };

            format!("{monthly_str} {yearly_str}")
        } else {
            "".to_string()
        }
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.client_read(input.client_uuid) {
            return_authorization_error!(
                "insufficient permissions to advisor {}",
                input.client_uuid
            );
        }

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_uuid: Uuid,
                advisor_uuid: Uuid,
                advisor_name: String,
                create_ts: DateTimeUtc,
                name: String,
                first_name: String,
                last_name: String,
                email: Option<String>,
                phone: Option<String>,
                phone2: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,

                active: bool,
                annual_insurance_premium: Option<Decimal>,
                annual_insurance_pua: Option<Decimal>,
                net_cash_at_end: Option<Decimal>,
                debt_free_active: bool,
                debt_free_start_date: Option<DateUtc>,
            };
            SELECT
                c.client_uuid,
                a.advisor_uuid,
                a.first_name || " " || a.last_name AS advisor_name,
                c.create_ts,
                c.first_name || " " || c.last_name AS name,
                c.first_name,
                c.last_name,
                c.email,
                c.phone,
                c.phone2,
                c.address1,
                c.address2,
                c.city,
                c.state,
                c.zip,

                c.active,
                c.annual_insurance_premium,
                c.annual_insurance_pua,
                c.net_cash_at_end,
                c.debt_free_active,
                c.debt_free_start_date

            FROM
                df4l.client0 as c
                INNER JOIN df4l.advisor as a
                    ON c.advisor_uuid = a.advisor_uuid
            WHERE true
                AND c.client_uuid = $client_uuid::uuid
        )
        .await?;

        let has_data = !granite::pg_row_vec!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = { exists: bool, };
            SELECT EXISTS(
                SELECT 1 FROM df4l.client0_debt
                WHERE client_uuid = $client_uuid::uuid LIMIT 1
            ) AS exists
        )
        .await?
        .is_empty();

        let df4l_status = if row.debt_free_active && row.debt_free_start_date.is_some() {
            let formatted_date = row
                .debt_free_start_date
                .map(|date| date.format("%b %d, %Y").to_string())
                .unwrap_or_default();
            DF4LStatus::Active(format!("Active As Of {formatted_date}"))
        } else if has_data && !row.debt_free_active {
            DF4LStatus::HasDataButNotEnrolled
        } else {
            DF4LStatus::NotEnrolled
        };

        let df4l_status_html = match &df4l_status {
            DF4LStatus::Active(s) => maud::html! {
                label-tag.primary { (s) }
            }
            .into_string(),
            DF4LStatus::HasDataButNotEnrolled => maud::html! {
                label-tag.warning { "Has Data But Not Enrolled" }
            }
            .into_string(),
            DF4LStatus::NotEnrolled => maud::html! {
                label-tag.default { "Not Enrolled" }
            }
            .into_string(),
        };

        let active_debts = granite::pg_row!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                active_debts: i64,
            };
            SELECT
                COUNT(*) AS active_debts
            FROM
                df4l.client0_debt
            WHERE
                client_uuid = $client_uuid::uuid
                AND active
        )
        .await?
        .active_debts;

        use df4l_zero::api::client_debt_free_info::client_debt_free_info;
        let debt_info = client_debt_free_info::call(
            app,
            identity,
            client_debt_free_info::Input {
                client_uuid: input.client_uuid,
                advisor_uuid: row.advisor_uuid,
                generate_plan: false,
            },
        )
        .await?
        .debt_info;

        Ok(Output {
            client_uuid: row.client_uuid,
            advisor_uuid: row.advisor_uuid,
            advisor_name: row.advisor_name,
            advisor_link: crate::ml_advisor(row.advisor_uuid),
            create_ts: row.create_ts,
            name: row.name,
            first_name: row.first_name,
            last_name: row.last_name,
            email: row.email,
            phone: row.phone,
            phone2: row.phone2,
            address1: row.address1,
            address2: row.address2,
            city: row.city,
            state: row.state,
            zip: row.zip,

            active: row.active,
            insurance_premium_formatted: format_decimal_optional(row.annual_insurance_premium),
            insurance_pua_formatted: format_decimal_optional(row.annual_insurance_pua),
            total_insurance_premium_formatted: if let (Some(premium), Some(pua)) =
                (row.annual_insurance_premium, row.annual_insurance_pua)
            {
                format_decimal_optional(Some(premium + pua))
            } else {
                "".to_string()
            },
            df4l_status,
            df4l_status_html,
            active_debts,

            minimum_monthly_payment: debt_info.minimum_monthly_payment,
            minimum_monthly_payment_html: format!(
                "{}/mo",
                bux::format_currency_us_0(debt_info.minimum_monthly_payment.unwrap_or_default())
            ),

            annual_budget: debt_info.annual_budget,
            annual_budget_html: format_decimal_optional(debt_info.annual_budget),

            annual_insurance_premium: debt_info.annual_insurance_premium,
            annual_insurance_premium_html: format_decimal_optional(
                debt_info.annual_insurance_premium,
            ),

            annual_insurance_base: debt_info.annual_insurance_base,
            annual_insurance_base_html: format_decimal_optional(debt_info.annual_insurance_base),

            annual_insurance_pua: debt_info.annual_insurance_pua,
            annual_insurance_pua_html: format_decimal_optional(debt_info.annual_insurance_pua),
        })
    }
}

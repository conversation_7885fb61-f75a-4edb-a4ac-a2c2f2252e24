#[approck::api]
pub mod admin_client_list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
        pub active: Option<bool>,
        pub advisor_uuid: Option<Uuid>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_list: Vec<Client>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Client {
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
        pub advisor_esid: String,
        pub create_ts: DateTimeUtc,
        pub active: bool,
        pub name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub debt_free_active: bool,
        pub debt_free_start_date: Option<DateUtc>,
        pub debt_free_status_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.client_list() {
            return_authorization_error!("insufficient permissions to client list");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $keyword: &input.keyword,
                $active: &input.active,
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                client_uuid: Uuid,
                advisor_uuid: Uuid,
                advisor_esid: String,
                create_ts: DateTimeUtc,
                active: bool,
                name: String,
                email: Option<String>,
                phone: Option<String>,
                debt_free_active: bool,
                debt_free_start_date: Option<DateUtc>,
            };
            SELECT
                c.client_uuid,
                c.advisor_uuid,
                a.advisor_esid,
                c.create_ts,
                c.active,
                c.first_name || " " || c.last_name AS name,
                c.email,
                c.phone,
                c.debt_free_active,
                c.debt_free_start_date
            FROM
                df4l.client0 c
                INNER JOIN df4l.advisor a ON c.advisor_uuid = a.advisor_uuid
            WHERE true
                AND c.debt_free_active = true

                AND (
                    $keyword::text IS NULL
                    OR
                    c.first_name ILIKE "%" || $keyword::text || "%"
                    OR
                    c.last_name ILIKE "%" || $keyword::text || "%"
                )
                AND ($active::bool IS NULL OR c.active = $active::bool)
                AND ($advisor_uuid::uuid IS NULL OR c.advisor_uuid = $advisor_uuid::uuid)
            ORDER BY
                c.first_name, c.last_name
        )
        .await?;

        let mut client_list = Vec::with_capacity(rows.len());

        for r in rows {
            let client = Client {
                client_uuid: r.client_uuid,
                advisor_uuid: r.advisor_uuid,
                advisor_esid: r.advisor_esid,
                create_ts: r.create_ts,
                active: r.active,
                name: r.name,
                email: r.email,
                phone: r.phone,
                debt_free_active: r.debt_free_active,
                debt_free_start_date: r.debt_free_start_date,
                debt_free_status_html: String::new(), // Initialize with empty string
            };

            // Check if debt data exists
            let has_data = !granite::pg_row_vec!(
                db = dbcx;
                args = {
                    $client_uuid: &client.client_uuid,
                };
                row = { exists: bool, };
                SELECT EXISTS(
                    SELECT 1 FROM df4l.client0_debt
                    WHERE client_uuid = $client_uuid::uuid LIMIT 1
                ) AS exists
            )
            .await?
            .is_empty()
                && granite::pg_row_vec!(
                    db = dbcx;
                    args = {
                        $client_uuid: &client.client_uuid,
                    };
                    row = { exists: bool, };
                    SELECT EXISTS(
                        SELECT 1 FROM df4l.client0_debt
                        WHERE client_uuid = $client_uuid::uuid LIMIT 1
                    ) AS exists
                )
                .await?[0]
                    .exists;

            // Calculate debt_free_status_html directly here
            let debt_free_status =
                if client.debt_free_active && client.debt_free_start_date.is_some() {
                    let formatted_date = client
                        .debt_free_start_date
                        .map(|date| date.format("%b %d, %Y").to_string())
                        .unwrap_or_default();
                    maud::html! {
                        label-tag.primary { "Active As Of " (formatted_date) }
                    }
                    .into_string()
                } else if has_data && !client.debt_free_active {
                    maud::html! {
                        label-tag.warning { "Has Data But Not Enrolled" }
                    }
                    .into_string()
                } else {
                    maud::html! {
                        label-tag.default { "Not Enrolled" }
                    }
                    .into_string()
                };

            client_list.push(Client {
                debt_free_status_html: debt_free_status,
                ..client
            });
        }

        Ok(Output { client_list })
    }
}

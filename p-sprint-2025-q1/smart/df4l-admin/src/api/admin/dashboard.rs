#[approck::api]
pub mod admin_dashboard {

    #[granite::gtype(ApiInput)]
    pub struct Input {}

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub agency_count: i64,
        pub advisor_count: i64,
        pub client_count: i64,
        pub legacy_client_count: i64,
    }

    pub async fn call(app: App) -> Result<Output> {
        // Auth check is handled automatically by the api macro

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            row = {
                agency_count: i64,
                advisor_count: i64,
                client_count: i64,
                legacy_client_count: i64,
            };
            SELECT
                (SELECT COUNT(*) FROM df4l.agency WHERE active = true) AS agency_count,
                (SELECT COUNT(*) FROM df4l.advisor WHERE active = true) AS advisor_count,
                (SELECT COUNT(*) FROM df4l.client WHERE active = true) AS client_count,
                (SELECT COUNT(*) FROM df4l.client0 WHERE active = true) AS legacy_client_count
        )
        .await?;

        Ok(Output {
            agency_count: row.agency_count,
            advisor_count: row.advisor_count,
            client_count: row.client_count,
            legacy_client_count: row.legacy_client_count,
        })
    }
}

#[approck::api]
pub mod admin_client_assign_advisor {

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.client_write(input.client_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to edit client".to_string(),
            ));
        }

        let dbcx = app.postgres_dbcx().await?;

        // Update the client's advisor_uuid
        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
                $advisor_uuid: &input.advisor_uuid,
            };
            UPDATE
                df4l.client
            SET
                advisor_uuid = $advisor_uuid
            WHERE
                client_uuid = $client_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: crate::ml_client(input.client_uuid),
            message: "Advisor assigned to client".into(),
        }))
    }
}

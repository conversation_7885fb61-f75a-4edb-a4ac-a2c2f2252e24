#[approck::api]
pub mod admin_advisor_delete {

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.advisor_write(input.advisor_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to delete advisor".to_string(),
            ));
        }

        let dbcx = app.postgres_dbcx().await?;

        // Soft delete by setting active = false
        granite::pg_execute!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            UPDATE
                df4l.advisor
            SET
                active = false
            WHERE
                advisor_uuid = $advisor_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: "/admin/advisor/".to_string(),
            message: "Advisor deleted".into(),
        }))
    }
}

#[approck::api]
pub mod admin_advisor_list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
        pub active: Option<bool>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub advisor_list: Vec<Advisor>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Advisor {
        pub advisor_uuid: Uuid,
        pub agency_uuid: Option<Uuid>,
        pub agency_name: Option<String>,
        pub create_ts: DateTimeUtc,
        pub advisor_esid: String,
        pub gbu_advisor_esid: Option<String>,
        pub first_name: String,
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub active: bool,
        pub admin_note: Option<String>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.advisor_list() {
            return_authorization_error!("insufficient permissions to advisor list");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $keyword: &input.keyword,
                $active: &input.active,
            };
            row = {
                advisor_uuid: Uuid,
                agency_uuid: Option<Uuid>,
                agency_name: Option<String>,
                create_ts: DateTimeUtc,
                advisor_esid: String,
                gbu_advisor_esid: Option<String>,
                first_name: String,
                last_name: String,
                email: Option<String>,
                phone: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,
                active: bool,
                admin_note: Option<String>,
            };
            SELECT
                ad.advisor_uuid,
                ad.agency_uuid,
                ag.name AS agency_name,
                ad.create_ts,
                ad.advisor_esid,
                ad.gbu_advisor_esid,
                ad.first_name,
                ad.last_name,
                ad.email,
                ad.phone,
                ad.address1,
                ad.address2,
                ad.city,
                ad.state,
                ad.zip,
                ad.active,
                ad.admin_note
            FROM
                df4l.advisor AS ad
            LEFT JOIN
                df4l.agency AS ag ON ad.agency_uuid = ag.agency_uuid
            WHERE true
                AND ($keyword::text IS NULL OR ad.first_name ILIKE "%" || $keyword::text || "%" OR ad.last_name ILIKE "%" || $keyword::text || "%" OR (ad.first_name || " " || ad.last_name) ILIKE "%" || $keyword::text || "%")
                AND ($active::bool IS NULL OR ad.active = $active::bool)
            ORDER BY
                ad.first_name, ad.last_name
        )
        .await?;

        Ok(Output {
            advisor_list: rows
                .into_iter()
                .map(|r| Advisor {
                    advisor_uuid: r.advisor_uuid,
                    agency_uuid: r.agency_uuid,
                    agency_name: r.agency_name,
                    create_ts: r.create_ts,
                    advisor_esid: r.advisor_esid,
                    gbu_advisor_esid: r.gbu_advisor_esid,
                    first_name: r.first_name,
                    last_name: r.last_name,
                    email: r.email,
                    phone: r.phone,
                    address1: r.address1,
                    address2: r.address2,
                    city: r.city,
                    state: r.state,
                    zip: r.zip,
                    active: r.active,
                    admin_note: r.admin_note,
                })
                .collect(),
        })
    }
}

#[approck::api]
pub mod admin_advisor_edit {

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub agency_uuid: Option<Uuid>,
        pub gbu_advisor_esid: Option<String>,
        #[gtype(trim=both; max=128; no_empty;)]
        pub first_name: String,
        #[gtype(trim=both; max=128; no_empty;)]
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub admin_note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.advisor_write(input.advisor_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to edit advisor".to_string(),
            ));
        }

        let dbcx = app.postgres_dbcx().await?;
        //add validation for gbu_advisor_esid
        let mut error = Input_Error {
            advisor_uuid: None,
            agency_uuid: None,
            first_name: None,
            last_name: None,
            email: None,
            phone: None,
            address1: None,
            address2: None,
            city: None,
            state: None,
            zip: None,
            gbu_advisor_esid: None,
            admin_note: None,
        };

        let gbu_agent_id = match input.gbu_advisor_esid {
            Some(gbu_agent_id) => {
                let gbu_agent_id = gbu_agent_id.trim().to_string();
                if !df4l_zero::gbu_agent::validate(&dbcx, &gbu_agent_id).await? {
                    error.gbu_advisor_esid = Some("Invalid GBU Agent ID".to_string());
                    return Ok(Response::ValidationError(granite::NestedError {
                        outer: "Invalid GBU Agent ID".to_string(),
                        inner: Some(error),
                    }));
                }
                Some(gbu_agent_id)
            }
            None => None,
        };

        granite::pg_execute!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
                $agency_uuid: &input.agency_uuid,
                $gbu_advisor_esid: &gbu_agent_id,
                $first_name: &input.first_name,
                $last_name: &input.last_name,
                $email: &input.email,
                $phone: &input.phone,
                $address1: &input.address1,
                $address2: &input.address2,
                $city: &input.city,
                $state: &input.state,
                $zip: &input.zip,
                $admin_note: &input.admin_note
            };
            UPDATE
                df4l.advisor
            SET
                agency_uuid = $agency_uuid,
                gbu_advisor_esid = $gbu_advisor_esid,
                first_name = $first_name,
                last_name = $last_name,
                email = $email,
                phone = $phone,
                address1 = $address1,
                address2 = $address2,
                city = $city,
                state = $state,
                zip = $zip,
                admin_note = $admin_note
            WHERE
                advisor_uuid = $advisor_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: crate::ml_advisor(input.advisor_uuid),
            message: "Aadvisor updated".into(),
        }))
    }
}

#[approck::api]
pub mod admin_advisor_add {
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub gbu_advisor_esid: Option<String>,
        #[gtype(trim=both; max=128; no_empty;)]
        pub first_name: String,
        #[gtype(trim=both; max=128; no_empty;)]
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub admin_note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub advisor_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.advisor_add() {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to advisor add".to_string(),
            ));
        }

        let mut dbcx = app.postgres_dbcx().await?;

        // Transaction block
        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;
            let mut i = 0;

            // loop 10 times and query the database to make sure the new ESID is not present
            let advisor_esid = loop {
                let advisor_esid = new_advisor_esid();

                if granite::pg_row!(db = dbtx;
                    args = {
                        $advisor_esid: &advisor_esid,
                    };
                    row = {
                        ok: bool,
                    };
                    SELECT NOT EXISTS (
                        SELECT
                        FROM df4l.advisor
                        WHERE advisor_esid = $advisor_esid::varchar
                    ) AS ok
                )
                .await?
                .ok
                {
                    break advisor_esid;
                }

                i += 1;
                if i > 10 {
                    return Err(granite::Error::new(granite::ErrorType::Unexpected)
                        .add_context("failed to generate unique advisor_esid"));
                }
            };
            let mut error = Input_Error {
                first_name: None,
                last_name: None,
                email: None,
                phone: None,
                address1: None,
                address2: None,
                city: None,
                state: None,
                zip: None,
                gbu_advisor_esid: None,
                admin_note: None,
            };

            let gbu_agent_id = match input.gbu_advisor_esid {
                Some(gbu_agent_id) => {
                    let gbu_agent_id = gbu_agent_id.trim().to_string();
                    if !df4l_zero::gbu_agent::validate(&dbtx, &gbu_agent_id).await? {
                        error.gbu_advisor_esid = Some("Invalid GBU Agent ID".to_string());
                        return Ok(Response::ValidationError(granite::NestedError {
                            outer: "Invalid GBU Agent ID".to_string(),
                            inner: Some(error),
                        }));
                    }
                    Some(gbu_agent_id)
                }
                None => None,
            };

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $gbu_advisor_esid: &gbu_agent_id,
                    $advisor_esid: &advisor_esid,
                    $first_name: &input.first_name,
                    $last_name: &input.last_name,
                    $email: &input.email,
                    $phone: &input.phone,
                    $address1: &input.address1,
                    $address2: &input.address2,
                    $city: &input.city,
                    $state: &input.state,
                    $zip: &input.zip,
                    $admin_note: &input.admin_note
                };
                row = {
                    advisor_uuid: Uuid,
                };

                INSERT INTO
                    df4l.advisor
                    (
                        gbu_advisor_esid,
                        advisor_esid,
                        first_name,
                        last_name,
                        email,
                        phone,
                        address1,
                        address2,
                        city,
                        state,
                        zip,
                        admin_note
                    )
                VALUES
                    (
                        $gbu_advisor_esid,
                        $advisor_esid,
                        $first_name,
                        $last_name,
                        $email,
                        $phone,
                        $address1,
                        $address2,
                        $city,
                        $state,
                        $zip,
                        $admin_note
                    )
                RETURNING
                    advisor_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Response::Output(Output {
            advisor_uuid: row.advisor_uuid,
            detail_url: crate::ml_advisor(row.advisor_uuid),
        }))
    }

    fn new_advisor_esid() -> String {
        use rand::Rng;
        let mut rng = rand::rng();

        let first = rng.random_range(0..1000);
        let second = rng.random_range(0..100);

        format!("{first:03}-{second:02}")
    }
}

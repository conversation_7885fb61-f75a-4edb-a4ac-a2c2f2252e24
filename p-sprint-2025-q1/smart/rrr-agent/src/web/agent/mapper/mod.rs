pub mod realrr;
pub mod resources;

#[approck::prefix(/agent/{agent_uuid:Uuid}/)]
pub mod prefix {
    pub fn menu(menu: Menu, agent_uuid: Uuid) {
        menu.add_link_icon(
            "Real Return Reporter",
            &crate::ml_agent(agent_uuid),
            approck::Icon::emoji_list(),
        );
        menu.add_link_icon(
            "Resources",
            &crate::ml_agent_resources(agent_uuid),
            approck::Icon::emoji_scroll(),
        );
        menu.add_link_icon("Sign Out", "/auth/logout", approck::Icon::emoji_minus());
    }
}

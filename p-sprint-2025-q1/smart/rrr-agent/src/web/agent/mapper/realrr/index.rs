#[approck::http(GET /agent/{agent_uuid:Uuid}/realrr/; AUTH None; return HTML|Redirect;)]
pub mod page {
    async fn request(doc: DocumentRRR) -> Result<Response> {
        doc.add_head(maud::PreEscaped(r#"<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.1/chart.umd.js" integrity="sha512-ZwR1/gSZM3ai6vCdI+LVF1zSq/5HznD3ZSTk7kajkaj4D292NLuduDCO1c/NT8Id+jE58KYLKT7hXnbtryGmMg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>"#.to_string()));

        // add bootstrap css
        doc.add_head(maud::PreEscaped(r#"<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">"#.to_string()));

        let data = crate::core::realrr::data::to_json();
        doc.set_script_json(data);

        // note: HS() taken care of by only injecting integers
        let year_options_html = (1..=40).fold(String::new(), |mut acc, i| {
            acc.push_str(&format!(r#"<option value="{i}">year {i}</option>"#));
            acc
        });

        #[rustfmt::skip]
        let html = format!(
            "{}{}{}{}{}{}{}{}{}",
            r#"
            <div class="real-rr">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card mb-3">
                            <div class="card-header text-center clearfix">
                                <h4>Initial Balance</h4>
                            </div>
                            <div class="card-body">
                                <div style="display: flex; flex-wrap: wrap; column-gap: 15px; align-items: center;">
                                    <div id="mts-initial-balance" class="mb-3" style="flex: 1; min-width: 10em;">
                                        <label>Initial Balance:</label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input class="form-control" value="100,000" type="text">
                                        </div>
                                    </div>
                                    <div style="flex: 3; text-align: center;">
                                        <em>Past Performance is Not Indicative of Future Results</em>
                                    </div>
                                </div>
                            </div>
                        </div>
    
                        <div class="card mb-3">
                            <div class="card-header text-center clearfix">
                                <h4>Contract Terms &amp; Indices To Compare</h4>
                            </div>
    
                            <div id="mts-toggle-button-background" class="card-body x-flashing-background">
                                <div class="alert alert-danger" role="alert" id="mts-choose-indices-error" style="display: none;"></div>
    
                                <div class="panel-container">
                                    <div class="card panel-fixed-rate">
                                        <div class="card-body">
                                            <div class="mb-3 io-fixed-rate" id="mts-fixed-rate">
                                                <div class="mb-3">
                                                    <label>Fixed Rate:</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">%</span>
                                                        <input class="form-control" value="4.00" type="text">
                                                    </div>
                                                </div>
                                                <div class="x-error"></div>
                                            </div>
                                            <div class="local-toggle-buttons">
                                                <button id="mts-tb-fixed-rate" selected="false">Example Fixed Rate</button>
                                            </div>
    
                                        </div>                        
                                    </div>
                                    <div class="card panel-indices">
                                        <div class="card-body">
                                            <div class="mb-3 io-standard-annual-fee" id="mts-standard-annual-fee">
                                                <div class="mb-3">
                                                    <label>Annual Fee:</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">%</span>
                                                        <input class="form-control" value="1" type="text">
                                                    </div>
                                                </div>
                                                <div class="x-error"></div>
                                            </div>
                                            <div class="local-toggle-buttons">
                                            <button id="mts-tb-dow-jones" selected="false">Dow Jones Industrial</button>
                                            <button id="mts-tb-sp-500" selected="false">S&P 500</button>
                                            <button id="mts-tb-nasdaq" selected="false">Nasdaq</button>
                                            <button id="mts-tb-invesco" selected="false">QQQ</button>
                                            <button id="mts-tb-agg" selected="false">AGG</button>
                                            </div>
                                        </div>                        
                                    </div>
                                    <div class="card panel-barclays">
                                        <div class="card-body">
                                            <div style="display: flex; flex-wrap: wrap; column-gap: 15px;">
                                                <div style="flex: 1; min-width: 7em;" id="mts-annual-fee">
                                                    <div class="mb-3">
                                                        <label>Annual&nbsp;Fee:</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text">%</span>
                                                            <input class="form-control" value="0.00" type="text">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div style="flex: 1; min-width: 7em;" id="mts-participation">
                                                    <div class="mb-3">
                                                        <label>Participation:</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text">%</span>
                                                            <input class="form-control" value="60.00" type="text">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="local-toggle-buttons">
                                                <button id="mts-tb-us-tech" selected="false">Barclays US Tech (FIA)</button>
                                                <button id="mts-tb-fortune-500" selected="false">Barclays Fortune 500 (FIA)</button>
                                                <button id="mts-tb-agility-shield" selected="false">Barclays Agility Shield</button>
                                            </div>
                                        </div>                        
                                    </div>
                                </div>
                            </div>                    
                        </div>
                        
                        <div class="card mts-timeframe" style="margin-bottom:1rem;">
                            <div class="card-header text-center clearfix">
                                <h4>Select Timeframe</h4>
                            </div>
                            <div class="card-body">
                                <div id="mts-year-range">
                                    Illustrate up to 
                                    <input type="number" step="1" style="width: 6em;"/> 
                                    years ending in
                                    <select style="width: 6em;"></select>
                                    .
                                </div>
                                <div class="local-chart-container">
                                    <canvas id="mts-chart" height="400" width="1000"></canvas>                                    
                                </div>
                            </div>
                        </div>
                        <div style="margin-bottom:.25rem;">
                            <p id="mts-source-data-text" style="display:none; color:#ffffff; font-weight:bold; margin-right:12px;">Source Data:</p>
                            <ul id="mts-sources">
                                <li style="display:none;" data-name="DowJones"><a class="btn mts-src-btn" href="https://finance.yahoo.com/quote/%5EDJI?p=%255EDJI" target="_blank">Dow Jones Industrial <i class="fa fa-external-link" aria-hidden="true"></i></a></li>
                                <li style="display:none;" data-name="SP500"><a class="btn mts-src-btn" href="https://finance.yahoo.com/quote/%5EGSPC?p=%5EGSPC" target="_blank">S&P 500 <i class="fa fa-external-link" aria-hidden="true"></i></a></li>
                                <li style="display:none;" data-name="Nasdaq"><a class="btn mts-src-btn" href="https://finance.yahoo.com/quote/%5EIXIC?p=%255EIXIC" target="_blank">Nasdaq <i class="fa fa-external-link" aria-hidden="true"></i></a></li>
                                <li style="display:none;" data-name="SP100QQQ"><a class="btn mts-src-btn" href="https://finance.yahoo.com/quote/QQQ?p=QQQ" target="_blank">QQQ <i class="fa fa-external-link" aria-hidden="true"></i></a></li>
                                <li style="display:none;" data-name="BarclaysUSTech"><a class="btn mts-src-btn" href="https://indices.cib.barclays/IM/33/en/indices/static/ustech.app" target="_blank">Barclays US Tech (FIA) <i class="fa fa-external-link" aria-hidden="true"></i></a></li>
                                <li style="display:none;" data-name="BarclaysFortune500"><a class="btn mts-src-btn" href="https://indices.barclays/Fortune500VolControl" target="_blank">Barclays Fortune 500 (FIA) <i class="fa fa-external-link" aria-hidden="true"></i></a></li>
                                <li style="display:none;" data-name="BarclaysAgilityShield"><a class="btn mts-src-btn" href="https://indices.cib.barclays/IM/33/en/indices/details.app;ticker=BXIIAS7E" target="_blank">Barclays Agility Shield <i class="fa fa-external-link" aria-hidden="true"></i></a></li>
                                <li style="display:none;" data-name="AGG"><a class="btn mts-src-btn" href="https://finance.yahoo.com/quote/AGG?p=AGG" target="_blank">AGG <i class="fa fa-external-link" aria-hidden="true"></i></a></li>
                            </ul>
                        </div>

                        <div class="card mts-withdraw" style="margin-bottom:10px;">
                            <div class="card-header text-center clearfix">
                                <h4>Contributions &amp; Withdrawals</h4>
                            </div>
                            <div class="card-body">
                                <div style="display: grid; gap: 10px; grid-template-columns: repeat(6, auto); width: fit-content; align-items: center; margin: 0 auto;">
                                    <b>Contribute</b>
                                    <span id="mts-contribute-amount-wrapper"><span id="mts-contribute-symbol">$</span><input id="mts-contribute-amount" type="text" style="width: 5em;"/></span>
                                    <span>per year from</span>
                                    <select id="mts-contribute-from"><option></option>"#, year_options_html, r#"</select>
                                    <span>until</span>
                                    <span><select id="mts-contribute-to"><option></option>"#, year_options_html, r#"</select></span>

                                    <b>Withdraw</b>
                                    <span id="mts-withdrawal-amount-wrapper"><span id="mts-withdrawal-symbol">$</span><input id="mts-withdrawal-amount" type="text" style="width: 5em;"/></span>
                                    <span>per year from</span>
                                    <select id="mts-withdrawal-from"><option></option>"#, year_options_html, r#"</select>
                                    <span>until</span>
                                    <span><select id="mts-withdrawal-to"><option></option>"#, year_options_html, r##"</select></span>
                                </div>
                                <div style="text-align: center; margin-top: 9pt; font-size: 9pt;">
                                    <a id="mts-clear2" href="#">clear</a>
                                </div>
                            </div>
                        </div>

                        <div style="margin: 1em; text-align: center;">
                            <a class="btn btn-primary" href="../resources" target="_blank">🔗 View Advisor Resources</a>
                        </div>
                    </div>
                    <div class="col-lg-6" id="mts-display-right">
                        <div class="table-responsive">
                            <table id="mts-stats-table" class="table x-panel">
                            </table>
                        </div>
    
                        <div class="card mts-performance" style="margin-bottom:10px;">
                            <div class="card-header text-center clearfix">
                                <h4>Comparative Performance</h4>
                            </div>
                            <div class="card-body">
                                <canvas id="mts-performance-chart" height="800" width="1000"></canvas>
                            </div>
                        </div>
    
                        <div class="table-responsive">
                            <table id="mts-performance-table" class="table x-panel">
                            </table>
                        </div>
    
                    </div>
                </div>
                <div class="local-disclaimer">
                    Past Performance Is Not Indicative of Future Results.
                </div>
            </div>
        "##
        );

        //doc.hide_page_nav();
        doc.set_body_display_fluid();
        doc.add_body(maud::PreEscaped(html.to_string()));
        Ok(Response::HTML(doc.into()))
    }
}

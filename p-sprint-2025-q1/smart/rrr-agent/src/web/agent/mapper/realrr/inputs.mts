import {
    type Amount<PERSON>earFromTo,
    DEF_RANGE,
    IndiceColor,
    MAX_RANGE,
    MIN_RANGE,
    YearRange,
} from "./types.mts";

class NumericInput {
    private $input: HTMLInputElement;
    private $error: HTMLDivElement;
    private change_handler: () => void;
    private value: number | null;

    constructor(eid: string) {
        this.$input = document.querySelector(`#${eid} input`) as HTMLInputElement;
        this.$error = document.getElementById(`#${eid} .x-error`) as HTMLDivElement;
        this.change_handler = () => {};
        this.value = null;

        // Attach an event listener to handle input changes
        this.$input.addEventListener("change", this.on_change_handler.bind(this));

        // format the existing value and replace it, setting self.value in the process
        this.on_change_handler();
    }

    private on_change_handler(): void {
        const input_value = this.$input.value;
        // strip commas and parse the value
        let value = Number.parseFloat(input_value.replace(/,/g, ""));
        if (Number.isNaN(value)) {
            value = 0;
        }
        if (value < 0) {
            value = 0;
        }

        // now format the number and replace the value
        this.value = value;
        this.$input.value = value.toLocaleString();
        this.change_handler();
    }

    on_change(handler: () => void): void {
        this.change_handler = handler;
    }

    set_error(error: string): void {
        this.$error.innerText = error;
    }

    get_value(): number | null {
        return this.value;
    }
}

class FlashingBackground {
    private $element: HTMLElement;

    constructor(eid: string) {
        this.$element = document.getElementById(eid) as HTMLElement;
    }

    set(on_off: boolean): void {
        if (on_off) {
            this.enable();
        } else {
            this.disable();
        }
    }

    enable(): void {
        this.$element.classList.add("x-flashing-background");
    }

    disable(): void {
        this.$element.classList.remove("x-flashing-background");
    }
}

class ToggleButtonInput {
    private $button: HTMLButtonElement;
    private $checkicon: HTMLElement;
    private background_color: string;
    private text_color: string;
    private change_handler: () => void;
    private selected: boolean;

    constructor(eid: string, background_color: string, text_color: string) {
        this.$button = document.getElementById(eid) as HTMLButtonElement;
        this.$button.type = "button";
        this.selected = this.$button.getAttribute("selected") === "true";

        this.$checkicon = document.createElement("i");
        this.$checkicon.classList.add("fa", "fa-check");
        this.$checkicon.style.display = "none";
        this.$button.insertBefore(this.$checkicon, this.$button.firstChild);

        this.background_color = background_color;
        this.text_color = text_color;

        this.change_handler = () => {};

        // Attach an event listener to handle input changes
        this.$button.addEventListener("click", this.on_button_click.bind(this));

        // set initial state
        this.refresh();
    }

    private on_button_click(): void {
        this.selected = !this.selected;
        this.refresh();
        this.change_handler();
    }

    private refresh(): void {
        this.$button.setAttribute("selected", this.selected.toString());
        this.$checkicon.style.display = this.selected ? "inline-block" : "none";
        this.$button.style.backgroundColor = this.selected ? this.background_color : "";
        this.$button.style.color = this.selected ? this.text_color : "";
    }

    on_change(handler: () => void): void {
        this.change_handler = handler;
    }

    get_value(): boolean {
        return this.selected;
    }
}

class YearRangeInput {
    private $div: HTMLDivElement;
    private $years_to_project: HTMLInputElement;
    private $year_ending: HTMLSelectElement;
    private change_handler: () => void;
    private allowed_year_range: YearRange | null;
    private years_to_project: number;
    private year_ending: number | null;

    constructor(eid: string) {
        this.$div = document.getElementById(eid) as HTMLDivElement;
        this.$years_to_project = this.$div.querySelector("input") as HTMLInputElement;
        this.$year_ending = this.$div.querySelector("select") as HTMLSelectElement;

        // set the max and min values
        this.$years_to_project.min = MIN_RANGE.toFixed();
        this.$years_to_project.max = MAX_RANGE.toFixed();

        this.$years_to_project.addEventListener("change", this.on_change_handler.bind(this));
        this.$year_ending.addEventListener("change", this.on_change_handler.bind(this));

        this.change_handler = () => {};

        this.allowed_year_range = null;
        this.years_to_project = DEF_RANGE;
        this.year_ending = null;
    }

    set_allowed_year_range(year_range: YearRange | null): void {
        this.allowed_year_range = year_range;
        this.refresh();
    }

    set_selected_year_range(selected_year_range: YearRange | null): void {
        if (selected_year_range === null) {
            this.years_to_project = DEF_RANGE;
            this.year_ending = null;
        } else {
            this.years_to_project = Math.min(selected_year_range.year_count(), MAX_RANGE);
            this.year_ending = selected_year_range.year_max;
        }
        this.refresh();
    }

    get_selected_year_range(): YearRange | null {
        if (this.year_ending === null) {
            return null;
        }
        if (this.year_ending !== null && this.year_ending !== undefined) {
            return new YearRange(this.year_ending - this.years_to_project + 1, this.year_ending);
        }
        // If the condition is not met, handle the case where this.year_ending is null or undefined
        // For example, you might throw an error, return a default value, or handle it in some other way
        return null;
    }

    on_change(handler: () => void): void {
        this.change_handler = handler;
    }

    private on_change_handler(): void {
        this.year_ending = Number.parseInt(this.$year_ending.value);
        this.years_to_project = Number.parseInt(this.$years_to_project.value);
        this.change_handler();
    }

    private refresh(): void {
        // clear both inputs if no valid range
        if (this.allowed_year_range === null) {
            this.$years_to_project.value = "";
            this.$year_ending.innerHTML = "";
            this.year_ending = null;
            this.$div.style.opacity = "0";
            return;
        }

        // set the years_to_project
        this.$years_to_project.value = this.years_to_project.toString();

        // set the dropdown options for the year range
        this.$year_ending.innerHTML = "";

        // iterate over the allowed_year_range.iter_years
        for (const [_i, year] of this.allowed_year_range.enumerate()) {
            const option = document.createElement("option");
            option.value = year.toString();
            option.text = year.toString();
            this.$year_ending.appendChild(option);
        }

        this.$year_ending.value = this.year_ending?.toString() || "";
        this.$div.style.opacity = "1";
    }
}

class AmountRangeInput {
    private $amount: HTMLInputElement;
    private $from: HTMLSelectElement;
    private $to: HTMLSelectElement;

    private change_handler: () => void;

    constructor(amount_eid: string, from_eid: string, to_eid: string) {
        this.$amount = document.getElementById(amount_eid) as HTMLInputElement;
        this.$from = document.getElementById(from_eid) as HTMLSelectElement;
        this.$to = document.getElementById(to_eid) as HTMLSelectElement;
        this.change_handler = () => {};

        this.$amount.addEventListener("change", () => {
            this.change_handler();
        });
        this.$from.addEventListener("change", () => {
            this.change_handler();
        });
        this.$to.addEventListener("change", () => {
            this.change_handler();
        });
    }

    on_change(handler: () => void): void {
        this.change_handler = handler;
    }

    get_value(): AmountYearFromTo | null {
        let amount = Number.parseFloat(this.$amount.value.replace(/[,$]/g, ""));
        const from = Number.parseInt(this.$from.value);
        const to = Number.parseInt(this.$to.value);

        if (amount === 0 || Number.isNaN(amount) || amount < 0) {
            amount = 0;
        }

        if (amount === 0) {
            this.$amount.value = "";
        } else {
            this.$amount.value = amount.toLocaleString();
        }

        // if from is between 1 and 40 and to is between 1 and 40 and to is greater than from, then return it
        if (amount > 0 && from >= 1 && from <= 40 && to >= 1 && to <= 40 && to >= from) {
            return { amount, year_from: from, year_to: to };
        }
        return null;
    }

    clear(): void {
        this.$amount.value = "";
        this.$from.value = "";
        this.$to.value = "";
        this.change_handler();
    }
}

type Inputs = {
    initial_balance: NumericInput;
    participation: NumericInput;
    annual_fee: NumericInput;
    fixed_rate: NumericInput;
    standard_annual_fee: NumericInput;
    toggle_button_background: FlashingBackground;
    tb_dow_jones: ToggleButtonInput;
    tb_sp_500: ToggleButtonInput;
    tb_nasdaq: ToggleButtonInput;
    tb_invesco: ToggleButtonInput;
    tb_us_tech: ToggleButtonInput;
    tb_fortune_500: ToggleButtonInput;
    tb_agility_shield: ToggleButtonInput;
    tb_agg: ToggleButtonInput;
    tb_fixed_rate: ToggleButtonInput;
    year_range: YearRangeInput;
    contribute: AmountRangeInput;
    withdrawal: AmountRangeInput;
    clear2: HTMLAnchorElement;
};

export function inputs_init(): Inputs {
    return {
        // input fields
        initial_balance: new NumericInput("mts-initial-balance"),
        participation: new NumericInput("mts-participation"),
        annual_fee: new NumericInput("mts-annual-fee"),
        fixed_rate: new NumericInput("mts-fixed-rate"),
        standard_annual_fee: new NumericInput("mts-standard-annual-fee"),

        // Toggle button background
        toggle_button_background: new FlashingBackground("mts-toggle-button-background"),

        // toggle buttons
        tb_dow_jones: new ToggleButtonInput("mts-tb-dow-jones", IndiceColor.DowJones, "white"),
        tb_sp_500: new ToggleButtonInput("mts-tb-sp-500", IndiceColor.SP500, "white"),
        tb_nasdaq: new ToggleButtonInput("mts-tb-nasdaq", IndiceColor.Nasdaq, "white"),
        tb_invesco: new ToggleButtonInput("mts-tb-invesco", IndiceColor.SP100QQQ, "white"),
        tb_us_tech: new ToggleButtonInput("mts-tb-us-tech", IndiceColor.BarclaysUSTech, "white"),
        tb_fortune_500: new ToggleButtonInput(
            "mts-tb-fortune-500",
            IndiceColor.BarclaysFortune500,
            "white",
        ),
        tb_agility_shield: new ToggleButtonInput(
            "mts-tb-agility-shield",
            IndiceColor.BarclaysAgilityShield,
            "white",
        ),
        tb_agg: new ToggleButtonInput("mts-tb-agg", IndiceColor.AGG, "white"),
        tb_fixed_rate: new ToggleButtonInput("mts-tb-fixed-rate", IndiceColor.FixedRate, "white"),

        // range selection
        year_range: new YearRangeInput("mts-year-range"),

        // contribution
        contribute: new AmountRangeInput(
            "mts-contribute-amount",
            "mts-contribute-from",
            "mts-contribute-to",
        ),
        withdrawal: new AmountRangeInput(
            "mts-withdrawal-amount",
            "mts-withdrawal-from",
            "mts-withdrawal-to",
        ),
        clear2: document.getElementById("mts-clear2") as HTMLAnchorElement,
    };
}

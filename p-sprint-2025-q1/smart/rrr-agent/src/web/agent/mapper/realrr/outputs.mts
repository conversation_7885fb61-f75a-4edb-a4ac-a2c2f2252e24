import { Calc<PERSON>rror, type CalcOutput, IndiceColor, type IndiceSelection } from "./types.mts";
import { CUR0, HS } from "@granite/util.mts";

declare global {
    interface Window {
        // make Chart a function that takes any and returns any
        // biome-ignore lint: no-explicit-any
        Chart: any;
    }
}

function add_cols($table: HTMLTableElement, indice_selection: IndiceSelection) {
    // add cols for background color
    const add_col = (color: string) => {
        const $col = document.createElement("col");
        $col.style.backgroundColor = `${color}33`; // Using template literal to add opacity
        $table.appendChild($col);
    };
    add_col("#ffffff");
    if (indice_selection.DowJones) add_col(IndiceColor.DowJones);
    if (indice_selection.SP500) add_col(IndiceColor.SP500);
    if (indice_selection.Nasdaq) add_col(IndiceColor.Nasdaq);
    if (indice_selection.SP100QQQ) add_col(IndiceColor.SP100QQQ);
    if (indice_selection.BarclaysUSTech) add_col(IndiceColor.BarclaysUSTech);
    if (indice_selection.BarclaysFortune500) add_col(IndiceColor.BarclaysFortune500);
    if (indice_selection.BarclaysAgilityShield) add_col(IndiceColor.BarclaysAgilityShield);
    if (indice_selection.AGG) add_col(IndiceColor.AGG);
    if (indice_selection.FixedRate) add_col(IndiceColor.FixedRate);
}

class OutputStats {
    $table: HTMLTableElement;

    constructor(eid: string) {
        this.$table = document.getElementById(eid) as HTMLTableElement;
    }

    draw(calc_output: CalcOutput | CalcError) {
        //  clear the table
        this.$table.innerHTML = "";

        if (calc_output instanceof CalcError) {
            {
                const $tr = document.createElement("tr");
                const $th = document.createElement("th");
                $th.innerText = "Market Statistics";
                $th.style.textAlign = "center";
                $tr.appendChild($th);
                this.$table.appendChild($tr);
            }
            {
                const $tr = document.createElement("tr");
                const $td = document.createElement("td");
                $td.style.textAlign = "center";
                $td.innerText = calc_output.message;
                $tr.appendChild($td);
                this.$table.appendChild($tr);
            }
            return;
        }

        // Add color definition cols
        add_cols(this.$table, calc_output.indice_selection);

        // draw the top row
        {
            const $tr = document.createElement("tr");
            const add_th = (text: string, textAlign: string) => {
                const $th = document.createElement("th");
                $th.style.textAlign = textAlign;
                $th.innerText = text;
                $tr.appendChild($th);
            };
            add_th("Market Statistics", "left");
            if (calc_output.indice_selection.DowJones) add_th("Dow Jones", "right");
            if (calc_output.indice_selection.SP500) add_th("S&P 500", "right");
            if (calc_output.indice_selection.Nasdaq) add_th("Nasdaq", "right");
            if (calc_output.indice_selection.SP100QQQ) add_th("QQQ", "right");
            if (calc_output.indice_selection.BarclaysUSTech) {
                add_th("Barclays US Tech (FIA)", "right");
            }
            if (calc_output.indice_selection.BarclaysFortune500) {
                add_th("Barclays Fortune 500 (FIA)", "right");
            }
            if (calc_output.indice_selection.BarclaysAgilityShield) {
                add_th("Barclays Agility Shield", "right");
            }
            if (calc_output.indice_selection.AGG) add_th("AGG", "right");
            if (calc_output.indice_selection.FixedRate) add_th("Fixed Rate", "right");
            this.$table.appendChild($tr);
        }

        // Draw the data rows
        {
            const $tr_average_ror = document.createElement("tr");
            const $tr_actual_ror = document.createElement("tr");
            const $tr_one_dollar = document.createElement("tr");

            const add_text_td = ($tr: HTMLTableRowElement, text: string) => {
                const $td = document.createElement("td");
                $td.style.textAlign = "left";
                $td.innerText = text;
                $tr.appendChild($td);
            };

            const add_ror_td = ($tr: HTMLTableRowElement, input: number) => {
                const $td = document.createElement("td");
                $td.innerText = `${
                    input.toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                    })
                }%`;
                $tr.appendChild($td);
            };

            const add_dollar_td = ($tr: HTMLTableRowElement, input: number) => {
                const $td = document.createElement("td");
                $td.innerText = `$${
                    input.toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                    })
                }`;
                $tr.appendChild($td);
            };

            add_text_td(
                $tr_average_ror,
                `Average ROR over ${calc_output.year_range.year_count()} year period`,
            );
            add_text_td(
                $tr_actual_ror,
                `Actual ROR over ${calc_output.year_range.year_count()} year period`,
            );
            add_text_td($tr_one_dollar, "$1 Invested would equal");

            if (calc_output.indice_selection.DowJones) {
                add_ror_td($tr_average_ror, calc_output.average_annual_return.DowJones);
                add_ror_td($tr_actual_ror, calc_output.actual_annual_return.DowJones);
                add_dollar_td($tr_one_dollar, calc_output.one_dollar_equals.DowJones);
            }
            if (calc_output.indice_selection.SP500) {
                add_ror_td($tr_average_ror, calc_output.average_annual_return.SP500);
                add_ror_td($tr_actual_ror, calc_output.actual_annual_return.SP500);
                add_dollar_td($tr_one_dollar, calc_output.one_dollar_equals.SP500);
            }
            if (calc_output.indice_selection.Nasdaq) {
                add_ror_td($tr_average_ror, calc_output.average_annual_return.Nasdaq);
                add_ror_td($tr_actual_ror, calc_output.actual_annual_return.Nasdaq);
                add_dollar_td($tr_one_dollar, calc_output.one_dollar_equals.Nasdaq);
            }
            if (calc_output.indice_selection.SP100QQQ) {
                add_ror_td($tr_average_ror, calc_output.average_annual_return.SP100QQQ);
                add_ror_td($tr_actual_ror, calc_output.actual_annual_return.SP100QQQ);
                add_dollar_td($tr_one_dollar, calc_output.one_dollar_equals.SP100QQQ);
            }
            if (calc_output.indice_selection.BarclaysUSTech) {
                add_ror_td($tr_average_ror, calc_output.average_annual_return.BarclaysUSTech);
                add_ror_td($tr_actual_ror, calc_output.actual_annual_return.BarclaysUSTech);
                add_dollar_td($tr_one_dollar, calc_output.one_dollar_equals.BarclaysUSTech);
            }
            if (calc_output.indice_selection.BarclaysFortune500) {
                add_ror_td($tr_average_ror, calc_output.average_annual_return.BarclaysFortune500);
                add_ror_td($tr_actual_ror, calc_output.actual_annual_return.BarclaysFortune500);
                add_dollar_td($tr_one_dollar, calc_output.one_dollar_equals.BarclaysFortune500);
            }
            if (calc_output.indice_selection.BarclaysAgilityShield) {
                add_ror_td(
                    $tr_average_ror,
                    calc_output.average_annual_return.BarclaysAgilityShield,
                );
                add_ror_td($tr_actual_ror, calc_output.actual_annual_return.BarclaysAgilityShield);
                add_dollar_td($tr_one_dollar, calc_output.one_dollar_equals.BarclaysAgilityShield);
            }
            if (calc_output.indice_selection.AGG) {
                add_ror_td($tr_average_ror, calc_output.average_annual_return.AGG);
                add_ror_td($tr_actual_ror, calc_output.actual_annual_return.AGG);
                add_dollar_td($tr_one_dollar, calc_output.one_dollar_equals.AGG);
            }
            if (calc_output.indice_selection.FixedRate) {
                add_ror_td($tr_average_ror, calc_output.average_annual_return.FixedRate);
                add_ror_td($tr_actual_ror, calc_output.actual_annual_return.FixedRate);
                add_dollar_td($tr_one_dollar, calc_output.one_dollar_equals.FixedRate);
            }

            this.$table.appendChild($tr_average_ror);
            this.$table.appendChild($tr_actual_ror);
            this.$table.appendChild($tr_one_dollar);
        }
    }
}

class OutputChart {
    $canvas: HTMLCanvasElement;
    $ctx: CanvasRenderingContext2D;
    // biome-ignore lint: no-explicit-any
    chart: any;

    constructor(eid: string) {
        this.$canvas = document.getElementById(eid) as HTMLCanvasElement;
        const $ctx = this.$canvas.getContext("2d");
        if ($ctx === null) {
            throw new Error("Could not get canvas context");
        }
        this.$ctx = $ctx;

        this.chart = new window.Chart(this.$ctx, {
            type: "line",
            data: [],
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: "top",
                    },
                    title: {
                        display: true,
                        text: "Balance at end of each year",
                    },
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        suggestedMin: 0,
                        stacked: true,
                    },
                },
            },
        });
    }

    draw(calc_output: CalcOutput | CalcError) {
        // fail on error
        if (calc_output instanceof CalcError) {
            this.chart.data.datasets = [];
            this.chart.data.labels = [];
            this.chart.update();
            return;
        }

        const datasets: {
            label: string;
            data: number[];
            borderColor: string;
            backgroundColor: string;
            type: string;
            stack: string;
        }[] = [];
        const labels: string[] = ["Starting"];

        // Add the labels
        for (const projected_year of calc_output.projected_years) {
            labels.push(projected_year.year.toFixed(0));
        }

        // add the data
        {
            if (calc_output.projected_years.some((x) => x.contribute_value !== null)) {
                datasets.push({
                    label: "Contributions",
                    data: [0, ...calc_output.projected_years.map((x) => x.contribute_value ?? 0)],
                    borderColor: "darkgreen",
                    backgroundColor: "darkgreen",
                    type: "bar",
                    stack: "contributions_and_withdrawals",
                });
            }

            if (calc_output.projected_years.some((x) => x.withdrawal_value !== null)) {
                datasets.push({
                    label: "Withdrawals",
                    data: [0, ...calc_output.projected_years.map((x) => x.withdrawal_value ?? 0)],
                    borderColor: "darkred",
                    backgroundColor: "darkred",
                    type: "bar",
                    stack: "contributions_and_withdrawals",
                });
            }

            const add_dataset = (label: string, color: string, data: number[]) => {
                datasets.push({
                    label: label,
                    data: data,
                    borderColor: color,
                    backgroundColor: color,
                    type: "line",
                    stack: label,
                });
            };

            if (calc_output.indice_selection.DowJones) {
                add_dataset("Dow Jones", IndiceColor.DowJones, [
                    calc_output.starting_balance.DowJones,
                    ...calc_output.projected_years.map((x) => x.portfolio_value.DowJones),
                ]);
            }
            if (calc_output.indice_selection.SP500) {
                add_dataset("S&P 500", IndiceColor.SP500, [
                    calc_output.starting_balance.SP500,
                    ...calc_output.projected_years.map((x) => x.portfolio_value.SP500),
                ]);
            }
            if (calc_output.indice_selection.Nasdaq) {
                add_dataset("Nasdaq", IndiceColor.Nasdaq, [
                    calc_output.starting_balance.Nasdaq,
                    ...calc_output.projected_years.map((x) => x.portfolio_value.Nasdaq),
                ]);
            }
            if (calc_output.indice_selection.SP100QQQ) {
                add_dataset("QQQ", IndiceColor.SP100QQQ, [
                    calc_output.starting_balance.SP100QQQ,
                    ...calc_output.projected_years.map((x) => x.portfolio_value.SP100QQQ),
                ]);
            }
            if (calc_output.indice_selection.BarclaysUSTech) {
                add_dataset("Barclays US Tech (FIA)", IndiceColor.BarclaysUSTech, [
                    calc_output.starting_balance.BarclaysUSTech,
                    ...calc_output.projected_years.map((x) => x.portfolio_value.BarclaysUSTech),
                ]);
            }
            if (calc_output.indice_selection.BarclaysFortune500) {
                add_dataset("Barclays Fortune 500 (FIA)", IndiceColor.BarclaysFortune500, [
                    calc_output.starting_balance.BarclaysFortune500,
                    ...calc_output.projected_years.map((x) => x.portfolio_value.BarclaysFortune500),
                ]);
            }
            if (calc_output.indice_selection.BarclaysAgilityShield) {
                add_dataset("Barclays Agility Shield", IndiceColor.BarclaysAgilityShield, [
                    calc_output.starting_balance.BarclaysAgilityShield,
                    ...calc_output.projected_years.map((x) =>
                        x.portfolio_value.BarclaysAgilityShield
                    ),
                ]);
            }
            if (calc_output.indice_selection.AGG) {
                add_dataset("AGG", IndiceColor.AGG, [
                    calc_output.starting_balance.AGG,
                    ...calc_output.projected_years.map((x) => x.portfolio_value.AGG),
                ]);
            }
            if (calc_output.indice_selection.FixedRate) {
                add_dataset("Fixed Rate", IndiceColor.FixedRate, [
                    calc_output.starting_balance.FixedRate,
                    ...calc_output.projected_years.map((x) => x.portfolio_value.FixedRate),
                ]);
            }
        }

        this.chart.data.datasets = datasets;
        this.chart.data.labels = labels;
        this.chart.update();
    }
}

class OutputPerformance {
    $table: HTMLTableElement;

    constructor(eid: string) {
        this.$table = document.getElementById(eid) as HTMLTableElement;
    }

    draw(calc_output: CalcOutput | CalcError) {
        // clear the table
        this.$table.innerHTML = "";

        // If error, exit
        if (calc_output instanceof CalcError) {
            {
                const $tr = document.createElement("tr");
                const $th = document.createElement("th");
                $th.innerText = "Market Statistics";
                $th.style.textAlign = "center";
                $tr.appendChild($th);
                this.$table.appendChild($tr);
            }
            {
                const $tr = document.createElement("tr");
                const $td = document.createElement("td");
                $td.style.textAlign = "center";
                $td.innerText = "";
                $tr.appendChild($td);
                this.$table.appendChild($tr);
            }
            return;
        }

        // Add color definition cols
        add_cols(this.$table, calc_output.indice_selection);

        // generate header row
        {
            const $tr = document.createElement("tr");
            const add_th = (text: string, textAlign: string) => {
                const $th = document.createElement("th");
                $th.style.textAlign = textAlign;
                $th.innerText = text;
                $tr.appendChild($th);
            };
            add_th("Year Ending", "left");
            if (calc_output.indice_selection.DowJones) add_th("Dow Jones", "right");
            if (calc_output.indice_selection.SP500) add_th("S&P 500", "right");
            if (calc_output.indice_selection.Nasdaq) add_th("Nasdaq", "right");
            if (calc_output.indice_selection.SP100QQQ) add_th("QQQ", "right");
            if (calc_output.indice_selection.BarclaysUSTech) {
                add_th("Barclays US Tech (FIA)", "right");
            }
            if (calc_output.indice_selection.BarclaysFortune500) {
                add_th("Barclays Fortune 500 (FIA)", "right");
            }
            if (calc_output.indice_selection.BarclaysAgilityShield) {
                add_th("Barclays Agility Shield", "right");
            }
            if (calc_output.indice_selection.AGG) add_th("AGG", "right");
            if (calc_output.indice_selection.FixedRate) add_th("Fixed Rate", "right");
            this.$table.appendChild($tr);
        }

        // add each year values
        const add_td = (
            $tr: HTMLTableRowElement,
            balance: number,
            annual_return: number | null,
        ) => {
            const $td = document.createElement("td");
            $td.innerText = `$${
                balance.toLocaleString(undefined, {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                })
            }`;
            $tr.appendChild($td);
            if (annual_return !== null) {
                const $small = document.createElement("small");
                $small.className = "x-percentage";
                $small.innerText = `${
                    annual_return.toLocaleString(undefined, {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                    })
                }%`;
                $td.appendChild($small);
            }
        };

        // add starting balance row
        {
            const $tr = document.createElement("tr");
            const $td = document.createElement("td");
            $td.innerText = "Starting Balance";
            $td.style.textAlign = "left";
            $tr.appendChild($td);

            if (calc_output.indice_selection.DowJones) {
                add_td($tr, calc_output.starting_balance.DowJones, null);
            }
            if (calc_output.indice_selection.SP500) {
                add_td($tr, calc_output.starting_balance.SP500, null);
            }
            if (calc_output.indice_selection.Nasdaq) {
                add_td($tr, calc_output.starting_balance.Nasdaq, null);
            }
            if (calc_output.indice_selection.SP100QQQ) {
                add_td($tr, calc_output.starting_balance.SP100QQQ, null);
            }
            if (calc_output.indice_selection.BarclaysUSTech) {
                add_td($tr, calc_output.starting_balance.BarclaysUSTech, null);
            }
            if (calc_output.indice_selection.BarclaysFortune500) {
                add_td($tr, calc_output.starting_balance.BarclaysFortune500, null);
            }
            if (calc_output.indice_selection.BarclaysAgilityShield) {
                add_td($tr, calc_output.starting_balance.BarclaysAgilityShield, null);
            }
            if (calc_output.indice_selection.AGG) {
                add_td($tr, calc_output.starting_balance.AGG, null);
            }
            if (calc_output.indice_selection.FixedRate) {
                add_td($tr, calc_output.starting_balance.FixedRate, null);
            }
            this.$table.appendChild($tr);
        }

        // generate data rows
        for (const projected_year of calc_output.projected_years) {
            const $tr = document.createElement("tr");

            // Add the year
            {
                const yearNumber = projected_year.year_number;

                const $td = document.createElement("td");
                $td.style.textAlign = "left";
                $td.innerText = projected_year.year.toFixed(0);
                // biome-ignore format: readability
                $td.innerHTML = `<div><span>${
                    HS(projected_year.year.toFixed(0))
                }</span><span class="x-year">(year&nbsp;${HS(yearNumber.toFixed())})</span></div>`;

                if (projected_year.contribute_value !== null) {
                    const $small = document.createElement("small");
                    $small.className = "x-contribute";
                    // biome-ignore format: readability
                    $small.innerHTML = `Contribute ${HS(CUR0(projected_year.contribute_value))}`;
                    $td.appendChild($small);
                }

                if (projected_year.withdrawal_value !== null) {
                    const $small = document.createElement("small");
                    $small.className = "x-withdrawal";
                    // biome-ignore format: readability
                    $small.innerHTML = `Withdraw ${HS(CUR0(projected_year.withdrawal_value))}`;
                    $td.appendChild($small);
                }

                $tr.appendChild($td);
            }

            if (calc_output.indice_selection.DowJones) {
                add_td(
                    $tr,
                    projected_year.portfolio_value.DowJones,
                    projected_year.annual_return.DowJones,
                );
            }
            if (calc_output.indice_selection.SP500) {
                add_td(
                    $tr,
                    projected_year.portfolio_value.SP500,
                    projected_year.annual_return.SP500,
                );
            }
            if (calc_output.indice_selection.Nasdaq) {
                add_td(
                    $tr,
                    projected_year.portfolio_value.Nasdaq,
                    projected_year.annual_return.Nasdaq,
                );
            }
            if (calc_output.indice_selection.SP100QQQ) {
                add_td(
                    $tr,
                    projected_year.portfolio_value.SP100QQQ,
                    projected_year.annual_return.SP100QQQ,
                );
            }
            if (calc_output.indice_selection.BarclaysUSTech) {
                add_td(
                    $tr,
                    projected_year.portfolio_value.BarclaysUSTech,
                    projected_year.annual_return.BarclaysUSTech,
                );
            }
            if (calc_output.indice_selection.BarclaysFortune500) {
                add_td(
                    $tr,
                    projected_year.portfolio_value.BarclaysFortune500,
                    projected_year.annual_return.BarclaysFortune500,
                );
            }
            if (calc_output.indice_selection.BarclaysAgilityShield) {
                add_td(
                    $tr,
                    projected_year.portfolio_value.BarclaysAgilityShield,
                    projected_year.annual_return.BarclaysAgilityShield,
                );
            }
            if (calc_output.indice_selection.AGG) {
                add_td($tr, projected_year.portfolio_value.AGG, projected_year.annual_return.AGG);
            }
            if (calc_output.indice_selection.FixedRate) {
                add_td(
                    $tr,
                    projected_year.portfolio_value.FixedRate,
                    projected_year.annual_return.FixedRate,
                );
            }

            this.$table.appendChild($tr);
        }
    }
}

class Attribution {
    private $ul: HTMLElement;
    private $dow_jones: HTMLLIElement;
    private $sp_500: HTMLLIElement;
    private $nasdaq: HTMLLIElement;
    private $sp100_qqq: HTMLLIElement;
    private $barclays_us_tech: HTMLLIElement;
    private $barclays_fortune_500: HTMLLIElement;
    private $barclays_agility_shield: HTMLLIElement;
    private $agg: HTMLLIElement;
    private $source_data_text: HTMLElement;

    constructor(eid: string) {
        this.$ul = document.getElementById(eid) as HTMLElement;

        this.$dow_jones = this.$ul.querySelector('[data-name="DowJones"]') as HTMLLIElement;
        this.$sp_500 = this.$ul.querySelector('[data-name="SP500"]') as HTMLLIElement;
        this.$nasdaq = this.$ul.querySelector('[data-name="Nasdaq"]') as HTMLLIElement;
        this.$sp100_qqq = this.$ul.querySelector('[data-name="SP100QQQ"]') as HTMLLIElement;
        this.$barclays_us_tech = this.$ul.querySelector(
            '[data-name="BarclaysUSTech"]',
        ) as HTMLLIElement;
        this.$barclays_fortune_500 = this.$ul.querySelector(
            '[data-name="BarclaysFortune500"]',
        ) as HTMLLIElement;
        this.$barclays_agility_shield = this.$ul.querySelector(
            '[data-name="BarclaysAgilityShield"]',
        ) as HTMLLIElement;
        this.$agg = this.$ul.querySelector('[data-name="AGG"]') as HTMLLIElement;
        this.$source_data_text = document.getElementById("mts-source-data-text") as HTMLElement;
    }

    refresh(indice_selection: IndiceSelection): void {
        this.$dow_jones.style.display = indice_selection.DowJones ? "inline" : "none";
        this.$sp_500.style.display = indice_selection.SP500 ? "inline" : "none";
        this.$nasdaq.style.display = indice_selection.Nasdaq ? "inline" : "none";
        this.$sp100_qqq.style.display = indice_selection.SP100QQQ ? "inline" : "none";
        this.$barclays_us_tech.style.display = indice_selection.BarclaysUSTech ? "inline" : "none";
        this.$barclays_fortune_500.style.display = indice_selection.BarclaysFortune500
            ? "inline"
            : "none";
        this.$barclays_agility_shield.style.display = indice_selection.BarclaysAgilityShield
            ? "inline"
            : "none";
        this.$agg.style.display = indice_selection.AGG ? "inline" : "none";

        // Check if IndiceSelection exists and show/hide $source_data_text accordingly
        if (
            indice_selection.DowJones ||
            indice_selection.SP500 ||
            indice_selection.Nasdaq ||
            indice_selection.SP100QQQ ||
            indice_selection.BarclaysUSTech ||
            indice_selection.BarclaysFortune500 ||
            indice_selection.BarclaysAgilityShield ||
            indice_selection.AGG
        ) {
            this.$source_data_text.style.display = "inline";
        } else {
            this.$source_data_text.style.display = "none";
        }
    }
}

type Outputs = {
    stats_table: OutputStats;
    performance_chart: OutputChart;
    performance_table: OutputPerformance;
    sources: Attribution;
};

export function outputs_init(): Outputs {
    // call calculate function;

    return {
        stats_table: new OutputStats("mts-stats-table"),
        performance_chart: new OutputChart("mts-performance-chart"),
        performance_table: new OutputPerformance("mts-performance-table"),
        sources: new Attribution("mts-sources"),
    };
}

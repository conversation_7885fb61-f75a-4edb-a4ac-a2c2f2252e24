import {
    IndiceColor,
    type IndiceQueryResult,
    type IndiceSelection,
    type IndiceYear,
} from "./types.mts";

export class Chart {
    $canvas: HTMLCanvasElement;
    $ctx: CanvasRenderingContext2D;
    width = 1000;
    height = 400;

    constructor(eid: string) {
        this.$canvas = document.getElementById(eid) as HTMLCanvasElement;
        const $ctx = this.$canvas.getContext("2d");
        if ($ctx === null) {
            throw new Error("Could not get canvas context");
        }
        this.$ctx = $ctx;
    }

    draw(query_result: IndiceQueryResult): void {
        this.$ctx.resetTransform();
        this.$ctx.clearRect(0, 0, this.width, this.height);

        if (query_result.year_range === null) {
            // draw a message in the middle
            this.$ctx.font = "30px Arial";
            this.$ctx.textAlign = "center";
            this.$ctx.textBaseline = "middle";
            this.$ctx.fillStyle = "#444444";
            const text = "please select at least one index";
            this.$ctx.fillText(text, this.width / 2, this.height / 2);
            return;
        }

        if (query_result.year_selection === null) {
            // draw a message in the middle
            this.$ctx.font = "30px Arial";
            this.$ctx.textAlign = "center";
            this.$ctx.textBaseline = "middle";
            this.$ctx.fillStyle = "#444444";
            const text = "please select a valid range to display";
            this.$ctx.fillText(text, this.width / 2, this.height / 2);
            return;
        }

        // At this point, we know year_range is not null because of the previous check
        // Therefore, the non-null assertion operator (!) is not needed and was removed
        const year_range = query_result.year_range;
        const y0 = this.height / 2;
        const pad_left = 20;
        const pad_right = 20;
        const pad_top = 20;
        const pad_bottom = 20;
        const chart_top = pad_top;
        const chart_left = pad_left;
        const chart_right = this.width - pad_right;
        const chart_bottom = this.height - pad_bottom;
        const chart_height = this.height - pad_top - pad_bottom;
        const chart_width = chart_right - chart_left;
        const hmax = chart_height / 2;
        const year_width = chart_width / year_range.year_count();
        const year_spacing = 5;
        const year_width_less_padding = year_width - year_spacing;

        // draw the x axis
        this.$ctx.beginPath();
        this.$ctx.strokeStyle = "#cccccc";
        this.$ctx.lineWidth = 1;
        this.$ctx.moveTo(chart_left, y0);
        this.$ctx.lineTo(chart_right, y0);
        this.$ctx.stroke();

        // draw the righthand y axis
        this.$ctx.beginPath();
        this.$ctx.lineWidth = 1;
        this.$ctx.strokeStyle = "#cccccc";
        this.$ctx.moveTo(chart_right, chart_top);
        this.$ctx.lineTo(chart_right, chart_bottom);
        this.$ctx.stroke();

        let selection_x1 = 0;
        let selection_x2 = 0;

        let last_text_x = -1000;
        // draw vertical lines for each year
        for (const [i, year] of year_range.enumerate()) {
            const x = pad_left + i * year_width;
            // biome-ignore lint/style/noNonNullAssertion: tested app logic
            const year_row = query_result.indice_years_max[i]!;

            // set selection size
            if (year === query_result.year_selection.year_max) {
                selection_x2 = x + year_width;
                selection_x1 = selection_x2 -
                    query_result.indice_years_selected.length * year_width;
            }

            // draw a dividing line
            this.$ctx.beginPath();
            this.$ctx.lineWidth = 1;
            this.$ctx.strokeStyle = "#cccccc";
            this.$ctx.moveTo(x, chart_top);
            this.$ctx.lineTo(x, chart_bottom);
            this.$ctx.stroke();

            // draw the year at the bottom
            if (x - last_text_x > 40) {
                last_text_x = x;
                this.$ctx.font = "14px Arial";
                this.$ctx.textAlign = "center";
                this.$ctx.textBaseline = "top";
                this.$ctx.fillStyle = "#666666";
                const text = year.toString();
                this.$ctx.fillText(text, x + year_width / 2, chart_bottom + 5);
            }

            draw_bars(
                this.$ctx,
                query_result.indice_selection,
                year_row,
                x + year_spacing / 2,
                year_width_less_padding,
                y0,
                hmax,
            );
        }

        // Draw selection box in light yellow transparent
        this.$ctx.beginPath();
        this.$ctx.fillStyle = "rgba(255,255,0,0.2)";
        this.$ctx.fillRect(selection_x1, chart_top, selection_x2 - selection_x1, chart_bottom);
    }
}

function draw_bars(
    ctx: CanvasRenderingContext2D,
    indice_selection: IndiceSelection,
    indice_year: IndiceYear,
    start: number,
    width: number,
    y0: number,
    hmax: number,
): void {
    const selections: [string, number][] = [];
    // Check if DowJones is selected and its year is not null before pushing it to selections
    if (indice_selection.DowJones && indice_year.DowJones !== null) {
        selections.push([IndiceColor.DowJones, indice_year.DowJones]);
    }
    if (indice_selection.SP500 && indice_year.SP500 !== null) {
        selections.push([IndiceColor.SP500, indice_year.SP500]);
    }
    if (indice_selection.Nasdaq && indice_year.Nasdaq !== null) {
        selections.push([IndiceColor.Nasdaq, indice_year.Nasdaq]);
    }
    if (indice_selection.SP100QQQ && indice_year.SP100QQQ !== null) {
        selections.push([IndiceColor.SP100QQQ, indice_year.SP100QQQ]);
    }
    if (indice_selection.BarclaysUSTech && indice_year.BarclaysUSTech !== null) {
        selections.push([IndiceColor.BarclaysUSTech, indice_year.BarclaysUSTech]);
    }
    if (indice_selection.BarclaysFortune500 && indice_year.BarclaysFortune500 !== null) {
        selections.push([IndiceColor.BarclaysFortune500, indice_year.BarclaysFortune500]);
    }
    if (indice_selection.BarclaysAgilityShield && indice_year.BarclaysAgilityShield !== null) {
        selections.push([IndiceColor.BarclaysAgilityShield, indice_year.BarclaysAgilityShield]);
    }
    if (indice_selection.AGG && indice_year.AGG !== null) {
        selections.push([IndiceColor.AGG, indice_year.AGG]);
    }
    if (indice_selection.FixedRate && indice_year.FixedRate !== null) {
        selections.push([IndiceColor.FixedRate, indice_year.FixedRate]);
    }

    const bar_width = width / selections.length;
    // Loop through the selections array to draw each bar
    for (let i = 0; i < selections.length; i++) {
        // Ensure selections[i] is defined before destructuring
        const selection = selections[i];
        if (selection) {
            const [color, value] = selection; // Destructure color and value from selection
            const x = start + i * bar_width; // Calculate the x position for the bar
            const h = (value / 100) * hmax; // Calculate the height of the bar based on value

            // Set the fill color for the bar
            ctx.fillStyle = color;

            // Draw the bar at the calculated position with the specified dimensions
            ctx.fillRect(x, y0 - h, bar_width, h);
        }
    }
}

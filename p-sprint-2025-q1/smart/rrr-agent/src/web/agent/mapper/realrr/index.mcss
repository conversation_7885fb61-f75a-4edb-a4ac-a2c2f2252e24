body {
    background-color: #000!important;
}

.real-rr > .row { 
    margin-right: 0;
    margin-left: 0;
}

/* Style for the card */
.card {
    border: 1px solid #ccc;
    border-radius: 5px;
    overflow: hidden; /* Ensure rounded corners with padding */

}

/* Style for the Font Awesome icon */
.icon {
    margin-right: 5px;
}
            
#mts-chart {
    width: 100%; /* Ensure it takes up the available width */
    height: auto; /* Maintain the aspect ratio */
}             

/* in the .local-toggle-buttons button class, display buttons on a grid */
.local-toggle-buttons {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    grid-gap: 10px;
}

/* give each button a padding */
.local-toggle-buttons button {
    padding: .375rem .75rem;
    border-radius: 5px;
}            
            
.local-toggle-buttons button > i {
    display: inline-block;
    margin-right: 4pt;
}            

.local-toggle-buttons button[selected="true"] {
    background-color: #337ab7;
    color: white;
}
            
#bodydiv {
    background: none;
    background-color: #222;
}

.real-rr .local-disclaimer {
    color: rgb(0, 0, 0);
    font-family: 'Source Serif 4', serif;
    font-size: 16pt;
    text-align: center;
    margin-top: 2em;
    margin-bottom: 2em;
}
            
.real-rr {
    .card {
        border: 2px solid #999;
        box-shadow: none;

        &:not(.panel-fixed-rate):not(.panel-indices):not(.panel-barclays) {
            border-left: 5px solid #ac8950;;
            border-radius: 21px;
        }
    }
}

.real-rr .card .card-header {
    background: #11294f !important; /* Header background updated to requested color */
    color: white; /* Text color for the header */
    border-top-left-radius: 0; /* Rounded top-left corner */
    border-top-right-radius: 0; /* Rounded top-right corner */
    padding: 0.7rem 0;
}
            
.real-rr .card .card-header h4 {
    font-family: Arial, sans-serif;
    font-size: 1.3em;
    color: white;
    margin-bottom: 0!important;
}

.real-rr .card-body {
    padding: 15px; /* Adjust padding if needed */
    background-color: white; /* Background color for the body */
    color: #333; /* Text color for the body */
    border-bottom-left-radius: 8px; /* Rounded bottom-left corner, if no footer is used */
    border-bottom-right-radius: 8px; /* Rounded bottom-right corner, if no footer is used */
}

.real-rr table.x-panel {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-family: Arial, sans-serif;
    font-size: 1.3em;
    border: 2px solid #999; /* Adding border */
    border-radius: 10px; /* Adding rounded corners */
    overflow: hidden; /* Ensures the border radius applies to contained cells */
    background-color: white;
    box-shadow: none;
    margin-bottom: 1rem;
    font-size: 12pt;
    border-left: 5px solid #ac8950;;
    border-radius: 10px;
}

.real-rr table.x-panel th, .real-rr table.x-panel td {
    padding: 8px 10px;
    border-bottom: 1px solid #333; /* Border for each cell */
    border-right: 1px solid #333; /* Border for each cell */
    font-size: 12pt;
}

.real-rr table.x-panel th {
    background: #11294f !important;
    color: white;
    text-align: center !important;
    font-size: 1.3em;
    font-weight: 100;
}

.real-rr table.x-panel td {
    text-align: right; /* Assuming most data is numeric */
}

.real-rr table.x-panel th:last-child, .real-rr table.x-panel td:last-child {
    border-right: none; /* Removing right border for the last cell */
}

.real-rr table.x-panel tr:last-child td {
    border-bottom: none; /* Removing bottom border for the last row */
}

#mts-performance-table td small {
    font-size: 11pt;
    background-color: inherit !important; /* Use !important to override other styles if needed */        
    margin: 0;
    padding: 0;
    display: block;
}

#mts-performance-table td div {
    display: flex;
    gap: .3em;
    align-items: center;
    padding: 0;

    span.x-year {
        font-size: 9pt;
        color: #999;
    }
}

#mts-performance-table td small.x-contribute {
    color: darkgreen;
}

#mts-performance-table td small.x-withdrawal {
    color: darkred;
}
                    
@keyframes changeColor {
    0% {
        background-color: #FFFFFF;
    }
    50% {
        background-color: #FFFF99;
    }
    100% {
        background-color: #FFFFFF;
    }
}

#mts-toggle-button-background.x-flashing-background {
    animation: changeColor 5s infinite;
}

#mts-sources {
    list-style: none;
    padding: 0;
    display: inline;
}

#mts-sources li {
    display: inline;
    margin-right: 12px; /* Adjust the margin as needed */
}

.panel-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 10px;
}



/* Added New Styles from Bux */

a.btn {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;

  &:hover {
    background-color: #0a58ca;
    border-color: #0a58ca;
  }

  &:active {
    background-color: #2c3a83;
  }

  &:focus {
    outline: 0;
    color: #fff;
    background-color: #0b5ed7;
    border-color: #07377f;
    box-shadow: 0 0 0 0.25rem rgba(49, 132, 253, 0.5);
  }

  &.mts-src-btn {
    align-items: center;
    background-image: radial-gradient(100% 100% at 100% 0, #00c6ff 0, #5468ff 100%);
    border: 0;
    border-radius: 6px;
    box-shadow: 0 2px 4px #2d234266, 0 7px 13px -3px #2d23424d, inset 0 -3px #3a416f80;
    box-sizing: border-box;
    color: #ffffff;
    cursor: pointer;
    height: 35px;
    justify-content: center;
    text-align: left;
    transition: box-shadow .15s, transform .15s;
    user-select: none;
    touch-action: manipulation;
    white-space: nowrap;
    will-change: box-shadow, transform;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: .50rem;
  }
}

.button-29:focus {
  box-shadow: #3c4fe0 0 0 0 1.5px inset, rgba(45, 35, 66, .4) 0 2px 4px, rgba(45, 35, 66, .3) 0 7px 13px -3px, #3c4fe0 0 -3px 0 inset;
}

.button-29:hover {
  box-shadow: rgba(45, 35, 66, .4) 0 4px 8px, rgba(45, 35, 66, .3) 0 7px 13px -3px, #3c4fe0 0 -3px 0 inset;
  transform: translateY(-2px);
}

.button-29:active {
  box-shadow: #3c4fe0 0 3px 7px inset;
  transform: translateY(2px);
}

#mts-clear2 {
    font-size: 12pt;
}

/* Grouped inputs using same styles */
#mts-contribute-from,
#mts-contribute-to,
#mts-withdrawal-from,
#mts-withdrawal-to,
#mts-year-range select,
#mts-year-range input[type="number"],
#mts-fixed-rate input[type="text"],
#mts-standard-annual-fee input[type="text"],
#mts-annual-fee input[type="text"],
#mts-participation input[type="text"] {
    border-left: none;
    border-radius: 0 .25rem .25rem 0;
    background-color: #f0f8ff;
    border: 1px solid #ced4da;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Add width/padding where needed */
#mts-contribute-from,
#mts-contribute-to,
#mts-withdrawal-from,
#mts-withdrawal-to,
#mts-year-range select {
    padding: 0.58rem 1rem;
    border-radius: .25rem !important;
}

#mts-year-range input[type="number"] {
    width: 4em;
    margin-right: 1rem;
    padding: 0.375rem 0.75rem;
}

#mts-year-range {
    opacity: 1;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 0.5em;
    font-size: 1rem;
    font-weight: 400;
    color: #000;
}

/* Shared .input-group-text styles */
#mts-fixed-rate .input-group-text,
#mts-standard-annual-fee .input-group-text,
#mts-annual-fee .input-group-text,
#mts-participation .input-group-text,
#mts-initial-balance .input-group-text,
#mts-contribute-symbol,
#mts-withdrawal-symbol {
    font-weight: bold;
    color: #495057;
    background-color: #e9ecef;
    padding: 0.375rem 0.5rem;
    border-right: none;
}

/* Bold labels */
#mts-fixed-rate > div > label,
#mts-standard-annual-fee > div > label,
#mts-annual-fee > div > label,
#mts-participation > div > label,
#mts-initial-balance > label {
    margin-bottom: 0.5rem;
    font-weight: bold;
    display: inline-block;
    font-size: 1rem;
    color: #000;
}

#mts-initial-balance {
    flex: 1;
    min-width: 10em;

    .input-group-text {
        border: 1px solid #ced4da;
        border-right: none;
        border-radius: 0.25rem 0 0 0.25rem;
    }

    .form-control {
        padding: 0.375rem 0.75rem;
        border-left: none;
        border-radius: 0 0.25rem 0.25rem 0;
        background-color: aliceblue;
        border: 1px solid #ced4da;
    }
}

#mts-contribute-amount-wrapper,
#mts-withdrawal-amount-wrapper {
    display: flex;
    border-radius: 0.25rem;
    overflow: hidden;
    border: 1px solid #ced4da;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);

    #mts-contribute-symbol,
    #mts-withdrawal-symbol {
        border-right: 1px solid #ced4da;
    }

    input {
        padding: 0.375rem 0.75rem;
        background-color: aliceblue;
        border: none;
    }
}
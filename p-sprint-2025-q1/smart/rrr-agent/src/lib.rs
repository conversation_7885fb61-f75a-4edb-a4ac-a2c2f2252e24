pub mod core;
pub mod web;

pub trait App: approck::App + auth_fence::App + approck_redis::App + approck_postgres::App {}

pub trait Identity: approck::Identity + rrr_zero::Identity + auth_fence::Identity {
    fn web_usage(&self) -> bool;
    fn api_usage(&self) -> bool;
}
pub trait Document: bux::document::<PERSON><PERSON> {}
pub trait DocumentRRR: bux::document::Base {}

pub fn ml_agent(agent_uuid: granite::Uuid) -> String {
    format!("/agent/{agent_uuid}/realrr/")
}

pub fn ml_agent_realrr(agent_uuid: granite::Uuid) -> String {
    format!("/agent/{agent_uuid}/realrr/")
}

pub fn ml_agent_resources(agent_uuid: granite::Uuid) -> String {
    format!("/agent/{agent_uuid}/resources")
}

#[approck::http(GET /terms; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, doc: DocumentPlain) -> Result<Response> {
        use maud::html;

        // Look up the doc or fail
        let legal_plane_document = match legal_plane::core::load_active_by_psid(
            &app.postgres_dbcx().await?,
            "AdvisorAgreement",
        )
        .await?
        {
            Some(d) => d,
            None => {
                return Err(granite::Error::data_not_found()
                    .add_context("Terms document `AdvisorAgreement` not found.")
                    .set_external_message(
                        "Unable to find current terms document. Please contact support."
                            .to_string(),
                    ));
            }
        };

        doc.set_title(&legal_plane_document.name);

        doc.add_body(html!(
            div.smart-terms {
                header {
                    h1 { (legal_plane_document.name) }
                    x-revision {
                        "Revision: " (legal_plane_document.revision)
                        " | Last Updated: " (legal_plane_document.create_ts.format("%B %d, %Y"))
                    }
                }
                (maud::PreEscaped(legal_plane_document.body_html))
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

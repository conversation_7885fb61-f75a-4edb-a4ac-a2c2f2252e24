//-------------------------------------------------------------------------------------------------
// 1. Import Components
import "./billing.mcss";
import "@bux/input/text/string.mts";
import "@bux/component/form_wizard.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code
import { SE_nullable, SEC } from "@granite/lib.mts";
import { signup_advisor_stripe_get } from "@crate/api/signup/advisorλ.mts";
// -------------------------------------------------------------------------------------------------

// 3. Find Elements
const $form = SEC(HTMLFormElement, document, "#billing-form");
const signup_advisor_uuid = SEC(HTMLInputElement, $form, "[name=signup_advisor_uuid]").value;

// Entire functionality of page conditional on this element existing.
if ($form.querySelector("content")) {
    const $call_stripe: HTMLAnchorElement | null = SE_nullable<HTMLAnchorElement>(
        $form,
        "[id=call-stripe]",
    );
    // -------------------------------------------------------------------------------------------------
    // 4. Bind Event Handlers

    // -------------------------------------------------------------------------------------------------
    // 5. Write Code
    if ($call_stripe) {
        $call_stripe.addEventListener("click", (event) => {
            event.preventDefault();
            call_stripe();
        });
    }
}

async function call_stripe() {
    const response = await signup_advisor_stripe_get.api.call({
        signup_advisor_uuid: signup_advisor_uuid,
    });

    if ("ValidationError" in response) {
        const errors = response.ValidationError[0];

        if ("Inner" in errors) {
            const inner_errors = errors.Inner as any;
            // TODO: Display errors
            console.log(inner_errors);
        }
        if ("Outer" in errors) {
            alert(errors.Outer);
        }
    }
    if ("Output" in response) {
        const output = response.Output[0];

        if ("Errors" in output) {
            // Handle errors
            const errors = output.Errors.errors;
            alert(`Error: ${errors.join(", ")}`);
        } else if ("NotPaid" in output) {
            // Redirect to Stripe checkout URL
            window.location.href = output.NotPaid.stripe_checkout_url;
        } else if ("Paid" in output) {
            // Already paid, redirect to success page
            alert("Your subscription is already active!");
            window.location.reload(); // Reload to show the success message
        }
    }
}

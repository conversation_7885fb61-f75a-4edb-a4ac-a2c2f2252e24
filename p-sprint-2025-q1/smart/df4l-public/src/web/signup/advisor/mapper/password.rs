#[approck::http(GET /signup/advisor/{signup_advisor_uuid:Uuid}/password; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        doc: Document,
        identity: Identity,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use crate::api::signup::advisor::signup_advisor_wizard_data;
        use approck::html;

        let wizard_data = signup_advisor_wizard_data::call(
            app,
            identity,
            signup_advisor_wizard_data::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;

        let mut wizard = bux::component::form_wizard::new(WizardStep::Password, wizard_data)?;
        wizard.set_id("password-form");
        wizard.set_hidden("signup_advisor_uuid", path.signup_advisor_uuid);
        wizard.next_label = "Complete Signup".to_string();
        wizard.next_icon = "fas fa-check".to_string();
        wizard.set_extra_button(bux::button::submit::label_icon_class(
            "Complete Signup",
            "fas fa-check",
            "primary",
        ));

        wizard.add_heading("Setup your Password");
        wizard.add_description("Please safely record your password in a password manager.  You will need your email address and password from time to time to access this site.");

        wizard.add_body(html! {
            div.password-wrap {
                (bux::component::password_creator::password_creator())
            }
        });

        doc.set_title("Create Password");
        doc.hide_page_nav();
        doc.add_body(html! {
            (wizard)
        });
        Ok(Response::HTML(doc.into()))
    }
}

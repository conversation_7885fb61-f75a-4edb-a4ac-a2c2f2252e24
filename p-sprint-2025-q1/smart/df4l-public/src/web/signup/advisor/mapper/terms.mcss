.dancing-script-400 {
  font-family: "Dancing Script", cursive;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
}

#terms-form {
    content {
        .terms {
            border: 1px solid #e1e5e9;
            padding: 2rem;
            border-radius: 0.5rem;
            background-color: #ffffff;
            color: #2c3e50;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            margin-bottom: 1.5rem;

            h1 {
                font-size: 1.5rem;
                font-weight: 600;
                margin-bottom: 1.5rem;
                color: #1a202c;
                text-align: center;
                border-bottom: 2px solid #e2e8f0;
                padding-bottom: 0.75rem;
            }

            h2 {
                font-size: 1.25rem;
                font-weight: 600;
                margin: 1.5rem 0 1rem 0;
                color: #2d3748;
            }

            h3 {
                font-size: 1.125rem;
                font-weight: 600;
                margin: 1.25rem 0 0.75rem 0;
                color: #4a5568;
            }

            p {
                margin-bottom: 1rem;
                font-size: 0.95rem;
                text-align: justify;
            }

            strong {
                font-weight: 600;
                color: #2d3748;
            }

            em {
                color: #718096;
                font-style: italic;
            }

            ul, ol {
                margin: 1rem 0;
                padding-left: 1.5rem;

                li {
                    margin-bottom: 0.5rem;
                    font-size: 0.95rem;
                }
            }

            ul {
                list-style-type: disc;
            }

            ol {
                list-style-type: decimal;
            }
        }

        .x-sign {
            margin: 2rem auto;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            background-color: #f8fafc;
            max-width: 650px;

            .x-sign-instructions {
                margin-bottom: 1.5rem;

                p {
                    margin-bottom: 1rem;
                    font-size: 0.95rem;
                    line-height: 1.6;
                    color: #4a5568;
                    text-align: left;
                }
            }

            bux-input-text-string label {
                font-weight: 600;
                font-size: 1rem;
                color: #2d3748;
                margin-bottom: 0.5rem;
                display: block;
            }

            bux-input-text-string .help-text {
                margin-top: 0.5rem;
                font-size: 0.875rem;
                color: #718096;
            }

            input {
                font-family: "Dancing Script", cursive;
                font-optical-sizing: auto;
                font-weight: 400;
                font-size: 1.5rem;
                text-align: center;
                letter-spacing: 0.5px;
                padding: 0.75rem;
                border: 2px solid #e2e8f0;
                border-radius: 0.375rem;
                width: 100%;
                background-color: #ffffff;
                transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

                &:focus {
                    outline: none;
                    border-color: #3182ce;
                    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
                }
            }

            .x-error:not(:empty) {
                background-color: #fed7d7;
                color: #c53030;
                padding: 0.75rem;
                border: 1px solid #feb2b2;
                margin-bottom: 1rem;
                border-radius: 0.375rem;
                font-weight: 500;
                font-size: 0.875rem;
            }
        }


    }
}
#[approck::http(GET /signup/advisor/{signup_advisor_uuid:Uuid}/billing; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        doc: Document,
        identity: Identity,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use crate::api::signup::advisor::signup_advisor_stripe_get;
        use crate::api::signup::advisor::signup_advisor_wizard_data;
        use approck::html;

        let wizard_data = signup_advisor_wizard_data::call(
            app,
            identity,
            signup_advisor_wizard_data::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;

        let stripe_get = signup_advisor_stripe_get::call(
            app,
            identity,
            signup_advisor_stripe_get::Input {
                signup_advisor_uuid: path.signup_advisor_uuid,
            },
        )
        .await?;

        let mut wizard = bux::component::form_wizard::new(WizardStep::Billing, wizard_data)?;
        wizard.set_id("billing-form");
        wizard.set_hidden("action", "billing_info");
        wizard.set_hidden("signup_advisor_uuid", path.signup_advisor_uuid);

        // Directly extract the URL if it's NotPaid and override the next href
        if let signup_advisor_stripe_get::Output::NotPaid {
            stripe_checkout_url,
        } = &stripe_get
        {
            wizard.override_next_href(stripe_checkout_url);
        }
        wizard.add_body(html! {
            @match stripe_get {
                signup_advisor_stripe_get::Output::Paid { stripe_subscription_id } => {
                    div.x-success-wrapper {
                        p { "Payment information successfully saved. Please click Continue to proceed." }
                        p.small { "Subscription ID: " (stripe_subscription_id) }
                    }
                }
                signup_advisor_stripe_get::Output::NotPaid { .. } => {
                    div.credit-card-form {
                        div {
                            p { "Debt2Capital™ uses Stripe Billing to simplify payment." }
                            p { b { "Please click Continue to go to Stripe Billing. " } }
                            p { "Once completed, you will be returned to this page." }
                            br;
                        }
                    }
                }
                signup_advisor_stripe_get::Output::Errors { errors } => {
                    div.x-error-wrapper {
                        p { "We encountered the following issues with your billing setup:" }
                        ul {
                            @for error in errors {
                                li { (error) }
                            }
                        }
                        p { "Please try again or contact support if the problem persists." }
                        a.btn.warning id="retry-billing" {
                            i.fas.fa-sync aria-hidden="true" {}
                            " "
                            span { "Retry Billing Setup" }
                        }
                    }
                }
            }
        });
        doc.set_title("Billing Information");
        doc.hide_page_nav();
        doc.add_body(html! {
            (wizard)
        });

        Ok(Response::HTML(doc.into()))
    }
}

//TODO:DF4L: CSRF issue on this page
#[approck::http(GET|POST /signup/?agency=Option<String>; AUTH None; return HTML|Redirect;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        req: Request,
        doc: Document,
        qs: QueryString,
    ) -> Result<Response> {
        use crate::api::signup::advisor::signup_advisor_create;
        use maud::html;

        if req.is_post() {
            let output = signup_advisor_create::call(
                app,
                identity,
                signup_advisor_create::Input {
                    agency: qs.agency.clone(),
                },
            )
            .await?;
            return Ok(Response::Redirect(Redirect::see_other(output.signup_url)));
        }

        doc.set_title("Get Started");

        // Note: we do not want CSRF protection on this form because of needing to post from another domain.
        doc.add_body(html!(
            d2c-sign-up {
                panel {
                    content {
                        img."d2c-logo" src="https://asset7.net/Zagula/Smart/Debt2Capital/debt2capital_logo_trademark.svg" {}
                        h1 { "Get Started Today" }
                        p { "Join financial professionals who are transforming their practice and their clients' financial futures." }

                        form id="signup-form" method="post" {
                            (bux::button::submit::label("Sign Up Now"))
                        }
                    }
                }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

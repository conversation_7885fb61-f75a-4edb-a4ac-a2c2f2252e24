.smart-terms {
    border: 1px solid #e1e5e9;
    padding: 2rem;
    border-radius: .5rem;
    background-color: #fff;
    color: #2c3e50;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    max-width: 1200px;
    margin: 2rem auto;

    header {
        text-align: center;
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 2px solid #e2e8f0;

        h1 {
            font-size: 2rem;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 0;
            border-bottom: none;
        }

        x-revision {
            font-size: 0.95rem;
            color: #718096;
            margin: 0;
            text-align: center;
            display: block;
        }
    }

    /* Hide the first h1 in the content (duplicate) */
    h1:first-of-type:not(header h1) {
        display: none;
    }

    h1 {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        color: #1a202c;
        text-align: center;
        border-bottom: 2px solid #e2e8f0;
        padding-bottom: 0.75rem;
    }

    h2 {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 1.5rem 0 1rem 0;
        color: #2d3748;
    }

    h3 {
        font-size: 1.125rem;
        font-weight: 600;
        margin: 1.25rem 0 0.75rem 0;
        color: #4a5568;
    }

    p {
        margin-bottom: 1rem;
        font-size: 0.95rem;
        text-align: justify;
    }

    strong {
        font-weight: 600;
        color: #2d3748;
    }

    em {
        color: #718096;
        font-style: italic;
    }

    ul, ol {
        margin: 1rem 0;
        padding-left: 1.5rem;

        li {
            margin-bottom: 0.5rem;
            font-size: 0.95rem;
        }
    }

    ul {
        list-style-type: disc;
    }

    ol {
        list-style-type: decimal;
    }
}

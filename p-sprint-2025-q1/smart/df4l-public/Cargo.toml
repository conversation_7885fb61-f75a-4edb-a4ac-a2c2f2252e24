[package]
name = "df4l-public"
version = "0.1.0"
edition = "2024"

[package.metadata.approck.mod]
extends = [
    "df4l-zero",
    "approck",
    "bux",
    "granite",
    "auth-fence",
    "api-stripe",
    "api-sendgrid",
    "api-twilio",
]


[dependencies]
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
auth-fence = { workspace = true }
approck-redis = { workspace = true }
approck-postgres = { workspace = true }
api-stripe = { workspace = true }
api-sendgrid = { workspace = true }
api-twilio = { workspace = true }

maud = { workspace = true }
pulldown-cmark = { workspace = true }

df4l-zero = { path = "../df4l-zero" }
legal-plane = { workspace = true }

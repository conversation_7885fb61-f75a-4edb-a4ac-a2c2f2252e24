// Manages the multi-step signup process for an RRR agent. This file contains
// a series of API endpoints that guide the user through the necessary stages of
// creating an agent account. The entire process is designed as a wizard, where
// each step must be completed before proceeding to the next.
//
// The flow is as follows:
// 1. `signup_agent_create`: Initiates the process, creating a temporary record.
// 2. `signup_agent_contact_set/get`: Collects and retrieves the user's contact details.
// 3. `signup_agent_verify_get/email/phone`: Handles two-factor verification by sending
//    and confirming codes sent to the user's email and phone.
// 4. `signup_agent_terms_get/set`: Displays the terms of service and records the user's
//    acceptance.
// 5. `signup_agent_password_set`: Allows the user to set a secure password for their account.
//
// The `signup_agent_wizard_data` endpoint provides a snapshot of the user's progress
// at any point, enabling the frontend to display the correct state of the wizard.

/// ### Creates or resumes the agent signup process.
/// * Checks for an existing active signup record with the current session token.
/// * If found, returns the URL to continue that signup process.
/// * If not found, creates a new signup record and returns its URL.
#[approck::api]
pub mod signup_agent_create_or_resume {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {}

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub signup_agent_uuid: Uuid,
        pub signup_url: String,
    }

    pub async fn call(app: App, identity: Identity) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.signup_agent_create() {
            return_authorization_error!("signup_agent_create");
        }

        let session_token = identity.session_token();
        let remote_addr = identity.get_address();

        // First, check for an existing active signup record with this session token
        let existing_signup = granite::pg_row_option!(
            db = dbcx;
            args = {
                $session_token: &session_token,
            };
            row = {
                signup_agent_uuid: Uuid,
            };
            SELECT signup_agent_uuid
            FROM rrr.signup_agent
            WHERE session_token = $session_token
            AND signup_completed_ts IS NULL
            ORDER BY create_ts DESC
            LIMIT 1
        )
        .await?;

        let signup_agent_uuid = match existing_signup {
            Some(row) => {
                // Found an existing active signup, use it
                row.signup_agent_uuid
            }
            None => {
                // No existing signup found, create a new one
                let row = granite::pg_row!(
                    db = dbcx;
                    args = {
                        $session_token: &session_token,
                        $remote_addr: &remote_addr,
                    };
                    row = {
                        signup_agent_uuid: Uuid,
                    };
                    INSERT INTO rrr.signup_agent (session_token, create_addr)
                    VALUES ($session_token, $remote_addr::text::cidr)
                    RETURNING signup_agent_uuid
                )
                .await?;
                row.signup_agent_uuid
            }
        };

        let signup_url = crate::ml_signup_agent(signup_agent_uuid);

        Ok(Output {
            signup_agent_uuid,
            signup_url,
        })
    }
}

/// ### Initiates the agent signup process.
/// * This is the first step in the agent signup process.
/// * It generates a unique UUID for the signup and a URL to continue the process.
#[approck::api]
pub mod signup_agent_create {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {}

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub signup_agent_uuid: Uuid,
        pub signup_url: String,
    }

    pub async fn call(app: App, identity: Identity) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.signup_agent_create() {
            return_authorization_error!("signup_agent_create");
        }

        let session_token = identity.session_token();
        let remote_addr = identity.get_address();

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $session_token: &session_token,
                $remote_addr: &remote_addr,
            };
            row = {
                signup_agent_uuid: Uuid,
            };
            INSERT INTO rrr.signup_agent (session_token, create_addr)
            VALUES ($session_token, $remote_addr::text::cidr)
            RETURNING signup_agent_uuid
        )
        .await?;

        let signup_url = crate::ml_signup_agent(row.signup_agent_uuid);

        Ok(Output {
            signup_agent_uuid: row.signup_agent_uuid,
            signup_url,
        })
    }
}

/// ### Retrieves the current state of the signup wizard.
/// * This endpoint is used to determine which steps of the signup process are complete, pending, or have errors.
/// * It provides a snapshot of the entire signup flow's status.
#[approck::api]
pub mod signup_agent_wizard_data {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub signup_agent_uuid: Uuid,
        pub step_contact_enabled: bool,
        pub step_contact_complete: bool,
        pub step_contact_error: Option<String>,
        pub step_verify_enabled: bool,
        pub step_verify_complete: bool,
        pub step_verify_error: Option<String>,
        pub step_terms_enabled: bool,
        pub step_terms_complete: bool,
        pub step_terms_error: Option<String>,
        pub step_password_enabled: bool,
        pub step_password_complete: bool,
        pub step_password_error: Option<String>,
        pub done: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_agent_read(&dbcx, input.signup_agent_uuid)
            .await
        {
            return_authorization_error!("signup_agent_read");
        }

        let wizard_data =
            crate::core::signup::agent::Signup::load(&dbcx, input.signup_agent_uuid).await?;

        Ok(Output {
            signup_agent_uuid: input.signup_agent_uuid,
            step_contact_enabled: true,
            step_contact_complete: wizard_data.contact.is_ok(),
            step_contact_error: wizard_data
                .contact
                .err()
                .map(|_| "Contact Information Missing".to_string()),
            step_verify_enabled: true,
            step_verify_complete: wizard_data.verify.is_ok(),
            step_verify_error: wizard_data
                .verify
                .err()
                .map(|_| "Verification Incomplete".to_string()),
            step_terms_enabled: true,
            step_terms_complete: wizard_data.terms.is_ok(),
            step_terms_error: wizard_data
                .terms
                .err()
                .map(|_| "Terms & Conditions Not Accepted".to_string()),
            step_password_enabled: true,
            step_password_complete: false, // Password is now handled separately
            step_password_error: None,
            done: wizard_data.done.is_some(),
        })
    }
}

/// ### Retrieves the contact information for a signup.
/// * Used to populate the contact information form when the user returns to this step.
#[approck::api]
pub mod signup_agent_contact_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub signup_agent_uuid: Uuid,
        pub first_name: Option<String>,
        pub last_name: Option<String>,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub gbu_sales_rep_uuid: Option<Uuid>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub gbu_sales_reps: Vec<GbuSalesRep>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct GbuSalesRep {
        pub gbu_sales_rep_uuid: Uuid,
        pub name: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_agent_read(&dbcx, input.signup_agent_uuid)
            .await
        {
            return_authorization_error!("signup_agent_read");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
            };
            row = {
                first_name: Option<String>,
                last_name: Option<String>,
                email: Option<String>,
                phone: Option<String>,
                gbu_sales_rep_uuid: Option<Uuid>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,
            };
            SELECT
                first_name,
                last_name,
                email,
                phone,
                gbu_sales_rep_uuid,
                address1,
                address2,
                city,
                state,
                zip
            FROM
                rrr.signup_agent
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        let gbu_sales_reps = granite::pg_row_vec!(
            db = dbcx;
            row = {
                gbu_sales_rep_uuid: Uuid,
                name: String,
            };
            SELECT
                "00000000-0000-0000-0000-000000000000"::uuid AS gbu_sales_rep_uuid,
                "N/A - I don't know" AS name,
                -1 AS sort_order

            UNION

            SELECT
                gbu_sales_rep_uuid,
                name,
                sort_order
            FROM
                rrr.gbu_sales_rep
            WHERE
                active = true

            ORDER BY
                sort_order, name
        )
        .await?
        .into_iter()
        .map(|row| GbuSalesRep {
            gbu_sales_rep_uuid: row.gbu_sales_rep_uuid,
            name: row.name,
        })
        .collect();

        Ok(Output {
            signup_agent_uuid: input.signup_agent_uuid,
            first_name: row.first_name,
            last_name: row.last_name,
            email: row.email,
            phone: row.phone,
            gbu_sales_rep_uuid: row.gbu_sales_rep_uuid,
            address1: row.address1,
            address2: row.address2,
            city: row.city,
            state: row.state,
            zip: row.zip,
            gbu_sales_reps,
        })
    }
}

/// ### Sets or updates the contact information for the signup.
/// * Validates the provided contact details.
/// * If the email or phone number changes, it resets the corresponding verification status, requiring the user to verify the new information.
#[approck::api]
pub mod signup_agent_contact_set {
    use granite::{NestedError, ResultExt, return_authorization_error};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub email: String,
        pub phone: String,
        pub gbu_sales_rep_uuid: Option<Uuid>,
        pub address1: String,
        pub address2: Option<String>,
        pub city: String,
        pub state: String,
        pub zip: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    #[granite::gtype(ApiInput)]
    pub struct ContactInputError {
        pub signup_agent_uuid: Option<String>,
        pub first_name: Option<String>,
        pub last_name: Option<String>,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub gbu_sales_rep_uuid: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let mut dbcx = app.postgres_dbcx().await?;

        // start transaction block
        {
            let dbtx = dbcx.transaction().await?;

            if !identity
                .signup_agent_write(&dbtx, input.signup_agent_uuid)
                .await
            {
                return_authorization_error!("signup_agent_write");
            }

            // Validate input using the core signup validation (only validates required contact fields)
            let contact_partial = crate::core::signup::agent::Contact_Partial {
                first_name: Some(input.first_name.clone()),
                last_name: Some(input.last_name.clone()),
                email: Some(input.email.clone()),
                phone: Some(input.phone.clone()),
            };

            let contact = match contact_partial.validate() {
                Ok(contact) => contact,
                Err(_errors) => {
                    return Ok(Response::ValidationError(NestedError {
                        outer: "Validation Error".to_string(),
                        inner: None,
                    }));
                }
            };

            // Convert all-zero UUID to NULL for database storage
            let gbu_sales_rep_uuid_for_db = match &input.gbu_sales_rep_uuid {
                Some(uuid) if uuid.to_string() == "00000000-0000-0000-0000-000000000000" => None,
                Some(uuid) => Some(uuid),
                None => {
                    return Ok(Response::ValidationError(NestedError {
                        outer: "Validation Error".to_string(),
                        inner: Some(Input_Error {
                            signup_agent_uuid: None,
                            first_name: None,
                            last_name: None,
                            email: None,
                            phone: None,
                            gbu_sales_rep_uuid: Some("Selection is required.".to_string()),
                            address1: None,
                            address2: None,
                            city: None,
                            state: None,
                            zip: None,
                        }),
                    }));
                }
            };

            // Check if email already exists in identity or agent tables
            let email_exists = granite::pg_row!(
                db = dbtx;
                args = {
                    $email: &contact.email,
                };
                row = {
                    email_exists: bool,
                };
                SELECT
                    EXISTS (
                        SELECT FROM auth_fence.identity
                        WHERE LOWER(email) = LOWER($email::text)
                    )
                    OR
                    EXISTS (
                        SELECT FROM rrr.agent
                        WHERE LOWER(email) = LOWER($email::text)
                        AND email IS NOT NULL
                    )
                    AS email_exists
            )
            .await?;

            if email_exists.email_exists {
                return Ok(Response::ValidationError(NestedError {
                    outer: format!(
                        "The email address {} is already registered in the system. Please contact support to get access to your account.",
                        contact.email
                    ),
                    inner: Some(Input_Error {
                        signup_agent_uuid: None,
                        first_name: None,
                        last_name: None,
                        email: Some("This email address is already registered".to_string()),
                        phone: None,
                        gbu_sales_rep_uuid: None,
                        address1: None,
                        address2: None,
                        city: None,
                        state: None,
                        zip: None,
                    }),
                }));
            }

            // Find data that changed.
            let changed = granite::pg_row!(
                db = dbtx;
                args = {
                    $signup_agent_uuid: &input.signup_agent_uuid,
                    $email: &input.email,
                    $phone: &input.phone,
                };
                row = {
                    email: bool,
                    phone: bool,
                };
                SELECT
                    email IS DISTINCT FROM $email AS email,
                    phone IS DISTINCT FROM $phone AS phone
                FROM
                    rrr.signup_agent
                WHERE
                    signup_agent_uuid = $signup_agent_uuid
            )
            .await?;

            // if email changed, clear verification fields for email
            if changed.email {
                granite::pg_execute!(
                    db = dbtx;
                    args = {
                        $signup_agent_uuid: &input.signup_agent_uuid,
                    };
                    UPDATE
                        rrr.signup_agent
                    SET
                        email_code = NULL,
                        email_code_sent_ts = NULL,
                        email_code_expire_ts = NULL,
                        email_code_attempts = 0,
                        email_verified_ts = NULL
                    WHERE
                        signup_agent_uuid = $signup_agent_uuid
                )
                .await?;
            }

            // if phone changed, clear verification fields for phone
            if changed.phone {
                granite::pg_execute!(
                    db = dbtx;
                    args = {
                        $signup_agent_uuid: &input.signup_agent_uuid,
                    };
                    UPDATE
                        rrr.signup_agent
                    SET
                        phone_code = NULL,
                        phone_code_sent_ts = NULL,
                        phone_code_expire_ts = NULL,
                        phone_code_attempts = 0,
                        phone_verified_ts = NULL
                    WHERE
                        signup_agent_uuid = $signup_agent_uuid
                )
                .await?;
            }

            granite::pg_execute!(
                db = dbtx;
                args = {
                    $signup_agent_uuid: &input.signup_agent_uuid,
                    $first_name: &input.first_name,
                    $last_name: &input.last_name,
                    $email: &input.email,
                    $phone: &input.phone,
                    $gbu_sales_rep_uuid: &gbu_sales_rep_uuid_for_db,
                    $address1: &input.address1,
                    $address2: &input.address2,
                    $city: &input.city,
                    $state: &input.state,
                    $zip: &input.zip,
                };
                UPDATE
                    rrr.signup_agent
                SET
                    first_name = $first_name,
                    last_name = $last_name,
                    email = $email,
                    phone = $phone,
                    gbu_sales_rep_uuid = $gbu_sales_rep_uuid,
                    address1 = $address1,
                    address2 = $address2,
                    city = $city,
                    state = $state,
                    zip = $zip
                WHERE
                    signup_agent_uuid = $signup_agent_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;
        }
        // end transaction block

        Ok(Response::Output(Output {}))
    }
}

/// ### Gets verification status and sends codes if necessary.
/// * This endpoint is called when the user enters the verification step.
/// * If verification codes have not been sent, it triggers sending them via email (SendGrid) and SMS (Twilio).
/// * It returns the timestamps when codes were sent and whether the email/phone have been verified.
/// * Fails if contact information (email, phone) is missing.
#[approck::api]
pub mod signup_agent_verify_get {
    use crate::api::signup::agent::{signup_agent_resend_email, signup_agent_resend_phone};
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub email: String,
        pub phone: String,
        pub email_verified: bool,
        pub phone_verified: bool,
        pub email_code_sent_ts: Option<DateTimeTz>,
        pub phone_code_sent_ts: Option<DateTimeTz>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_agent_read(&dbcx, input.signup_agent_uuid)
            .await
        {
            return_authorization_error!("signup_agent_read");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
            };
            row = {
                email: Option<String>,
                phone: Option<String>,
                email_code_sent_ts: Option<DateTimeTz>,
                email_verified_ts: Option<DateTimeTz>,
                phone_code_sent_ts: Option<DateTimeTz>,
                phone_verified_ts: Option<DateTimeTz>,
            };
            SELECT
                email,
                phone,
                email_code_sent_ts,
                email_verified_ts,
                phone_code_sent_ts,
                phone_verified_ts
            FROM
                rrr.signup_agent
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        let email = row.email.ok_or_else(|| {
            granite::Error::process_error("Email is required for verification".to_string())
        })?;

        let phone = row.phone.ok_or_else(|| {
            granite::Error::process_error("Phone is required for verification".to_string())
        })?;

        let email_code_sent_ts = match row.email_code_sent_ts {
            Some(ts) => ts,
            None => {
                // Send email verification code using the resend API
                signup_agent_resend_email::call(
                    app,
                    identity,
                    signup_agent_resend_email::Input {
                        signup_agent_uuid: input.signup_agent_uuid,
                    },
                )
                .await?;

                granite::Utc::now().fixed_offset()
            }
        };

        let phone_code_sent_ts = match row.phone_code_sent_ts {
            Some(ts) => ts,
            None => {
                // Send phone verification code using the resend API
                signup_agent_resend_phone::call(
                    app,
                    identity,
                    signup_agent_resend_phone::Input {
                        signup_agent_uuid: input.signup_agent_uuid,
                    },
                )
                .await?;

                granite::Utc::now().fixed_offset()
            }
        };

        Ok(Output {
            email,
            phone,
            email_verified: row.email_verified_ts.is_some(),
            phone_verified: row.phone_verified_ts.is_some(),
            email_code_sent_ts: Some(email_code_sent_ts),
            phone_code_sent_ts: Some(phone_code_sent_ts),
        })
    }
}

/// ### Verifies the phone number using a user-provided code.
/// * Checks the code against the one stored in the database.
/// * Handles code expiration and limits the number of attempts.
/// * Marks the phone number as verified upon successful code entry.
#[approck::api]
pub mod signup_agent_verify_phone {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,
        pub phone_code: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub phone: String,
        pub status: Status,
    }

    #[granite::gtype(ApiOutput)]
    pub enum Status {
        Verified,
        Error(String),
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_agent_write(&dbcx, input.signup_agent_uuid)
            .await
        {
            return_authorization_error!("signup_agent_write");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
            };
            row = {
                phone: Option<String>,
                phone_code: Option<String>,
                phone_code_expire_ts: Option<DateTimeTz>,
                phone_code_attempts: i32,
                phone_verified_ts: Option<DateTimeTz>,
            };
            SELECT
                phone,
                phone_code,
                phone_code_expire_ts,
                phone_code_attempts,
                phone_verified_ts
            FROM
                rrr.signup_agent
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        let phone = row.phone.ok_or_else(|| {
            granite::Error::process_error("Phone is required for verification".to_string())
        })?;

        // Check if already verified
        if row.phone_verified_ts.is_some() {
            return Ok(Output {
                phone,
                status: Status::Verified,
            });
        }

        // Check if too many attempts
        if row.phone_code_attempts >= 5 {
            return Ok(Output {
                phone,
                status: Status::Error(
                    "Too many verification attempts. Please request a new code.".to_string(),
                ),
            });
        }

        // Check if code exists
        let stored_code = match row.phone_code {
            Some(code) => code,
            None => {
                return Ok(Output {
                    phone,
                    status: Status::Error(
                        "No verification code found. Please request a new code.".to_string(),
                    ),
                });
            }
        };

        // Check if code expired
        if let Some(expire_ts) = row.phone_code_expire_ts {
            if granite::Utc::now() > expire_ts {
                return Ok(Output {
                    phone,
                    status: Status::Error(
                        "Verification code has expired. Please request a new code.".to_string(),
                    ),
                });
            }
        }

        // Increment attempts
        granite::pg_execute!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
            };
            UPDATE
                rrr.signup_agent
            SET
                phone_code_attempts = phone_code_attempts + 1
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        // Check if code matches
        if input.phone_code != stored_code {
            return Ok(Output {
                phone,
                status: Status::Error("Invalid verification code. Please try again.".to_string()),
            });
        }

        // Mark as verified
        let now = granite::Utc::now().fixed_offset();
        granite::pg_execute!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
                $now: &now,
            };
            UPDATE
                rrr.signup_agent
            SET
                phone_verified_ts = $now::timestamptz
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        Ok(Output {
            phone,
            status: Status::Verified,
        })
    }
}

/// ### Verifies the email address using a user-provided code.
/// * Checks the code against the one stored in the database.
/// * Handles code expiration and limits the number of attempts.
/// * Marks the email as verified upon successful code entry.
#[approck::api]
pub mod signup_agent_verify_email {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,
        pub email_code: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub email: String,
        pub status: Status,
    }

    #[granite::gtype(ApiOutput)]
    pub enum Status {
        Verified,
        Error(String),
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_agent_write(&dbcx, input.signup_agent_uuid)
            .await
        {
            return_authorization_error!("signup_agent_write");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
            };
            row = {
                email: Option<String>,
                email_code: Option<String>,
                email_code_expire_ts: Option<DateTimeTz>,
                email_code_attempts: i32,
                email_verified_ts: Option<DateTimeTz>,
            };
            SELECT
                email,
                email_code,
                email_code_expire_ts,
                email_code_attempts,
                email_verified_ts
            FROM
                rrr.signup_agent
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        let email = row.email.ok_or_else(|| {
            granite::Error::process_error("Email is required for verification".to_string())
        })?;

        // Check if already verified
        if row.email_verified_ts.is_some() {
            return Ok(Output {
                email,
                status: Status::Verified,
            });
        }

        // Check if too many attempts
        if row.email_code_attempts >= 5 {
            return Ok(Output {
                email,
                status: Status::Error(
                    "Too many verification attempts. Please request a new code.".to_string(),
                ),
            });
        }

        // Check if code exists
        let stored_code = match row.email_code {
            Some(code) => code,
            None => {
                return Ok(Output {
                    email,
                    status: Status::Error(
                        "No verification code found. Please request a new code.".to_string(),
                    ),
                });
            }
        };

        // Check if code expired
        if let Some(expire_ts) = row.email_code_expire_ts {
            if granite::Utc::now() > expire_ts {
                return Ok(Output {
                    email,
                    status: Status::Error(
                        "Verification code has expired. Please request a new code.".to_string(),
                    ),
                });
            }
        }

        // Increment attempts
        granite::pg_execute!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
            };
            UPDATE
                rrr.signup_agent
            SET
                email_code_attempts = email_code_attempts + 1
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        // Check if code matches
        if input.email_code != stored_code {
            return Ok(Output {
                email,
                status: Status::Error("Invalid verification code. Please try again.".to_string()),
            });
        }

        // Mark as verified
        let now = granite::Utc::now().fixed_offset();
        granite::pg_execute!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
                $now: &now,
            };
            UPDATE
                rrr.signup_agent
            SET
                email_verified_ts = $now::timestamptz
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        Ok(Output {
            email,
            status: Status::Verified,
        })
    }
}

/// ### Retrieves the legal terms and conditions for acceptance.
/// * Fetches the active "AgentAgreement" document from the `legal_plane` module.
/// * Returns the document content (HTML), UUID, revision, and whether the terms have already been accepted.
#[approck::api]
pub mod signup_agent_terms_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub document_uuid: Uuid,
        pub revision: String,
        pub terms_html: String,
        pub terms_accepted_name: Option<String>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_agent_read(&dbcx, input.signup_agent_uuid)
            .await
        {
            return_authorization_error!("signup_agent_read");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
            };
            row = {
                terms_accepted_name: Option<String>,
            };
            SELECT
                terms_accepted_name
            FROM
                rrr.signup_agent
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        let legal_plane_document =
            match legal_plane::core::load_active_by_psid(&dbcx, "AgentAgreement").await? {
                Some(document) => document,
                None => {
                    return Err(granite::Error::process_error(
                        "Unable to find current terms document. Please contact support."
                            .to_string(),
                    ));
                }
            };

        Ok(Output {
            document_uuid: legal_plane_document.document_uuid,
            revision: legal_plane_document.revision,
            terms_html: legal_plane_document.body_html,
            terms_accepted_name: row.terms_accepted_name,
        })
    }
}

/// ### Records the user's acceptance of the terms.
/// * Validates that the document version being accepted is still the current one.
/// * Stores the user's name, remote address, and timestamp of acceptance.
#[approck::api]
pub mod signup_agent_terms_set {
    use granite::{NestedError, return_authorization_error};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,

        // for confirmation that nothing changed during the user's review
        pub document_uuid: Uuid,

        // for confirmation that nothing changed during the user's review
        pub revision: String,

        /// Users first and last name
        pub terms_accepted_name: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_agent_write(&dbcx, input.signup_agent_uuid)
            .await
        {
            return_authorization_error!("signup_agent_write");
        }

        // Get current contact info for validation
        let contact_row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
            };
            row = {
                first_name: Option<String>,
                last_name: Option<String>,
            };
            SELECT first_name, last_name FROM rrr.signup_agent WHERE signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        // get the current terms document from the legal plane module
        let legal_plane_document =
            match legal_plane::core::load_active_by_psid(&dbcx, "AgentAgreement").await? {
                Some(document) => document,
                None => {
                    return Err(granite::Error::process_error(
                        "Unable to find current terms document. Please contact support."
                            .to_string(),
                    ));
                }
            };

        // Validate that the document version being accepted is still current
        if input.document_uuid != legal_plane_document.document_uuid
            || input.revision != legal_plane_document.revision
        {
            return Err(granite::Error::process_error(
                "The terms document has been updated. Please review the current terms.".to_string(),
            ));
        }

        let remote_addr = identity
            .get_address()
            .parse()
            .unwrap_or("127.0.0.1".parse().unwrap());

        // Validate using core signup validation
        let terms_partial = crate::core::signup::agent::Terms_Partial {
            first_name: contact_row.first_name,
            last_name: contact_row.last_name,
            terms_accepted_name: Some(input.terms_accepted_name.clone()),
            document_uuid: Some(legal_plane_document.document_uuid),
            revision: Some(legal_plane_document.revision.clone()),
            remote_addr: Some(remote_addr),
        };

        let _terms = match terms_partial.validate() {
            Ok(terms) => terms,
            Err(errors) => {
                return Ok(Response::ValidationError(NestedError {
                    outer: "Validation Error".to_string(),
                    inner: Some(Input_Error {
                        signup_agent_uuid: None,
                        document_uuid: None,
                        revision: None,
                        terms_accepted_name: errors.terms_accepted_name,
                    }),
                }));
            }
        };

        // Update terms acceptance
        granite::pg_execute!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
                $terms_document_uuid: &legal_plane_document.document_uuid,
                $terms_revision: &legal_plane_document.revision,
                $terms_accepted_name: &input.terms_accepted_name,
                $terms_accepted_addr: &identity.get_address(),
            };
            UPDATE rrr.signup_agent SET
                terms_document_uuid = $terms_document_uuid,
                terms_revision = $terms_revision,
                terms_accepted_name = $terms_accepted_name,
                terms_accepted_addr = $terms_accepted_addr::text::cidr
            WHERE signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        Ok(Response::Output(Output {}))
    }
}

/// ### Creates the identity and login records for the new agent account.
/// * Validates the password strength and confirmation.
/// * Creates records in auth_fence.identity and auth_fence.login tables.
/// * Creates the rrr.agent record and links it to the identity.
/// * Completes the signup process without storing passwords in signup_agent.
#[approck::api]
pub mod signup_agent_complete {
    use granite::ResultExt;
    use granite::return_authorization_error;
    use granite::{NestedError, Uuid};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,
        pub password: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub agent_uuid: Uuid,
        pub next_url: String,
    }

    /// TRANSACTION-BLOCK
    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let mut dbcx = app.postgres_dbcx().await?;
        let dbtx = dbcx.transaction().await?;

        if !identity
            .signup_agent_write(&dbtx, input.signup_agent_uuid)
            .await
        {
            return_authorization_error!("signup_agent_write");
        }

        let password_strength = granite::password::password_analysis(&input.password);
        if !password_strength.acceptable {
            let input_error = Input_Error {
                signup_agent_uuid: None,
                password: Some("Password does not meet strength requirements".into()),
            };
            return Ok(Response::ValidationError(NestedError {
                outer: "Validation Error".to_string(),
                inner: Some(input_error),
            }));
        }

        // Get signup data
        let signup_row = granite::pg_row!(
            db = dbtx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
            };
            row = {
                first_name: String,
                last_name: String,
                email: String,
                phone: String,
                gbu_sales_rep_uuid: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,
                terms_document_uuid: Option<Uuid>,
                terms_revision: Option<String>,
                terms_accepted_name: Option<String>,
                terms_accepted_addr: Option<IpAddr>,
            };
            SELECT
                first_name,
                last_name,
                email,
                phone,
                gbu_sales_rep_uuid::text,
                address1,
                address2,
                city,
                state,
                zip,
                terms_document_uuid,
                terms_revision,
                terms_accepted_name,
                terms_accepted_addr::inet AS terms_accepted_addr
            FROM
                rrr.signup_agent
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        // Call auth-fence core to create identity and login records
        let create_data = auth_fence::core::signup::CreateIdentityData {
            first_name: signup_row.first_name.clone(),
            last_name: signup_row.last_name.clone(),
            email: signup_row.email.clone(),
            phone: Some(signup_row.phone.clone()),
            password: input.password,
        };

        let create_identity_result = create_data.create_identity_with_login(app).await?;

        let identity_uuid = create_identity_result.identity_uuid;

        // Convert all-zero UUID to NULL for database storage
        let gbu_sales_rep_uuid_for_agent = match &signup_row.gbu_sales_rep_uuid {
            Some(uuid_str) if uuid_str == "00000000-0000-0000-0000-000000000000" => None,
            Some(uuid_str) => Some(uuid_str.parse::<Uuid>().map_err(|_| {
                granite::Error::process_error(
                    "Invalid UUID format for gbu_sales_rep_uuid".to_string(),
                )
            })?),
            None => None,
        };

        // Create agent record
        let agent_result = granite::pg_row!(
            db = dbtx;
            args = {
                $identity_uuid: &identity_uuid,
                $first_name: &signup_row.first_name,
                $last_name: &signup_row.last_name,
                $email: &signup_row.email,
                $phone: &signup_row.phone,
                $gbu_sales_rep_uuid: &gbu_sales_rep_uuid_for_agent,
                $address1: &signup_row.address1,
                $address2: &signup_row.address2,
                $city: &signup_row.city,
                $state: &signup_row.state,
                $zip: &signup_row.zip,
            };
            row = {
                agent_uuid: Uuid,
            };
            INSERT INTO
                rrr.agent (
                    identity_uuid,
                    first_name,
                    last_name,
                    email,
                    phone,
                    gbu_sales_rep_uuid,
                    address1,
                    address2,
                    city,
                    state,
                    zip
                )
            VALUES (
                $identity_uuid,
                $first_name,
                $last_name,
                $email,
                $phone,
                $gbu_sales_rep_uuid,
                $address1,
                $address2,
                $city,
                $state,
                $zip
            )
            RETURNING
                agent_uuid
        )
        .await?;

        let agent_uuid = agent_result.agent_uuid;

        // Create agreement record for the terms acceptance if terms were accepted
        if let (Some(document_uuid), Some(revision), Some(accepted_name)) = (
            signup_row.terms_document_uuid,
            signup_row.terms_revision.as_ref(),
            signup_row.terms_accepted_name.as_ref(),
        ) {
            // Get the document body_markdown for the agreement record
            if let Some(document) =
                legal_plane::core::load_active_by_psid(&dbtx, "AgentAgreement").await?
            {
                if document.document_uuid == document_uuid && document.revision == *revision {
                    let _agreement_uuid = legal_plane::core::create_agreement(
                        &dbtx,
                        &legal_plane::core::AgreementParams {
                            document_uuid,
                            identity_uuid: Some(identity_uuid),
                            uuid_entity: None, // uuid_entity not used for individual signups
                            create_addr: signup_row
                                .terms_accepted_addr
                                .map(|addr| addr.to_string())
                                .unwrap_or_else(|| identity.get_address()),
                            create_name: accepted_name.clone(),
                            name: format!("{} {}", signup_row.first_name, signup_row.last_name),
                            body_markdown: document.body_markdown.clone(),
                            revision: revision.clone(),
                        },
                    )
                    .await
                    .amend(|e| e.add_context("creating agreement record"))?;
                }
            }
        }

        // Create session for the newly created user to automatically log them in
        let mut redis = app.redis_dbcx().await?;
        let session_token = identity.session_token();

        let identity_details = auth_fence::api::identity::Identity {
            identity_uuid,
            identity_type: auth_fence::api::identity::IdentityType::User,
            name: format!("{} {}", signup_row.first_name, signup_row.last_name),
            email: Some(signup_row.email.clone()),
            note: None,
            avatar_uri: None,
            active: true,
        };

        // Store identity info in redis to log the user in
        auth_fence::api::identity::set_redis_session(&mut redis, &session_token, &identity_details)
            .await?;

        // Log the authentication event
        let remote_address = identity.remote_address().to_string();
        auth_fence::postgres::log::auth_log(
            &dbtx,
            auth_fence::postgres::log::AuthLogData {
                create_addr: remote_address,
                session_token: session_token.clone(),
                identity_uuid: Some(identity_uuid),
                user_esid: None,
                user_email: Some(signup_row.email.clone()),
                auth_type: "Signup".to_string(),
                auth_action: "SignupComplete".to_string(),
                auth_provider: Some("AgentSignup".to_string()),
                success: true,
                blocked: false,
                data: None,
            },
        )
        .await?;

        // Update signup_agent record with the new agent_uuid
        granite::pg_execute!(
            db = dbtx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
                $agent_uuid: &agent_uuid,
            };
            UPDATE
                rrr.signup_agent
            SET
                agent_uuid_created = $agent_uuid,
                signup_completed_ts = now()
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        // commit
        dbtx.commit().await?;

        Ok(Response::Output(Output {
            agent_uuid,
            next_url: "/dashboard".to_string(),
        }))
    }
}

/// ### Resends the email verification code.
/// * Generates a new verification code and sends it via email.
/// * Updates the database with the new code and timestamp.
/// * Returns the email address for component rendering.
#[approck::api]
pub mod signup_agent_resend_email {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub email: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_agent_write(&dbcx, input.signup_agent_uuid)
            .await
        {
            return_authorization_error!("signup_agent_write");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
            };
            row = {
                email: Option<String>,
            };
            SELECT
                email
            FROM
                rrr.signup_agent
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        let email = row.email.ok_or_else(|| {
            granite::Error::process_error("Email is required for resend".to_string())
        })?;

        let email_code = granite::random_code_4();

        // Send email with verification code using SendGrid
        let email_message =
            format!("Your RRR verification code is: {email_code}.  Expires in 60 minutes.");

        app.sendgrid()
            .send_email(
                app,
                "default",
                &email,
                "RRR Verification Code",
                &email_message,
            )
            .await?;

        let now = granite::Utc::now().fixed_offset();

        granite::pg_execute!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
                $email_code: &email_code,
                $email_code_sent_ts: &now,
                $email_code_expire_ts: &(now + granite::Duration::minutes(60)),
            };
            UPDATE
                rrr.signup_agent
            SET
                email_code = $email_code,
                email_code_sent_ts = $email_code_sent_ts,
                email_code_expire_ts = $email_code_expire_ts,
                email_code_attempts = 0
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        Ok(Output { email })
    }
}

/// ### Resends the phone verification code.
/// * Generates a new verification code and sends it via SMS.
/// * Updates the database with the new code and timestamp.
/// * Returns the phone number for component rendering.
#[approck::api]
pub mod signup_agent_resend_phone {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub phone: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity
            .signup_agent_write(&dbcx, input.signup_agent_uuid)
            .await
        {
            return_authorization_error!("signup_agent_write");
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
            };
            row = {
                phone: Option<String>,
            };
            SELECT
                phone
            FROM
                rrr.signup_agent
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        let phone = row.phone.ok_or_else(|| {
            granite::Error::process_error("Phone is required for resend".to_string())
        })?;

        let phone_code = granite::random_code_4();

        // Send SMS with verification code using Twilio
        let sms_message =
            format!("Your RRR verification code is: {phone_code}.  Expires in 60 minutes.");

        app.twilio()
            .send_sms(app, "default", &phone, &sms_message)
            .await?;

        let now = granite::Utc::now().fixed_offset();

        granite::pg_execute!(
            db = dbcx;
            args = {
                $signup_agent_uuid: &input.signup_agent_uuid,
                $phone_code: &phone_code,
                $phone_code_sent_ts: &now,
                $phone_code_expire_ts: &(now + granite::Duration::minutes(60)),
            };
            UPDATE
                rrr.signup_agent
            SET
                phone_code = $phone_code,
                phone_code_sent_ts = $phone_code_sent_ts,
                phone_code_expire_ts = $phone_code_expire_ts,
                phone_code_attempts = 0
            WHERE
                signup_agent_uuid = $signup_agent_uuid
        )
        .await?;

        Ok(Output { phone })
    }
}

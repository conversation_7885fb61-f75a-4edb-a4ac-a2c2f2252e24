#[approck::http(GET /signup/agent/{signup_agent_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        doc: DocumentRRR,
        identity: Identity,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use crate::api::signup::agent::{signup_agent_contact_get, signup_agent_wizard_data};
        use approck::html;

        doc.add_css("./index.mcss");
        doc.add_js("./index.ts");

        let wizard_data = signup_agent_wizard_data::call(
            app,
            identity,
            signup_agent_wizard_data::Input {
                signup_agent_uuid: path.signup_agent_uuid,
            },
        )
        .await?;

        let personal_data = signup_agent_contact_get::call(
            app,
            identity,
            signup_agent_contact_get::Input {
                signup_agent_uuid: path.signup_agent_uuid,
            },
        )
        .await?;

        let mut wizard = bux::component::form_wizard::new(WizardStep::Index, wizard_data)?;
        wizard.set_id("personal-info-form");
        wizard.set_hidden("action", "signup");
        wizard.set_hidden("signup_agent_uuid", path.signup_agent_uuid);
        wizard.add_heading("Personal Information");
        wizard.add_description(
            "Please fill out your personal details. All fields are required to continue.",
        );

        let gbu_sales_reps_options: Vec<(String, String)> = personal_data
            .gbu_sales_reps
            .into_iter()
            .map(|rep| (rep.gbu_sales_rep_uuid.to_string(), rep.name))
            .collect::<Vec<_>>();

        let gbu_sales_reps_options_refs: Vec<(&str, &str)> = gbu_sales_reps_options
            .iter()
            .map(|(id, name)| (id.as_str(), name.as_str()))
            .collect();

        #[rustfmt::skip]
        wizard.add_body(maud::html!(
            grid-2 #left-right {
                div {
                    grid-2 {
                        (bux::input::text::string::name_label_value("first_name", "First Name", personal_data.first_name.as_deref()))
                        (bux::input::text::string::name_label_value("last_name", "Last Name", personal_data.last_name.as_deref()))
                    }
                    grid-2 {
                        (bux::input::text::string::name_label_value("email", "Email", personal_data.email.as_deref()))
                        (bux::input::text::string::name_label_value("phone", "Phone", personal_data.phone.as_deref()))
                    }
                    {
                        (bux::input::select::nilla::nilla_select(
                            "gbu_sales_rep_uuid",
                            "GBU Regional Sales Representative",
                            &gbu_sales_reps_options_refs,
                            personal_data.gbu_sales_rep_uuid.map(|uuid| uuid.to_string()).as_deref()
                        ))
                    }
                }
                div {
                    (bux::input::text::string::name_label_value("address1", "Address Line 1", personal_data.address1.as_deref()))
                    (bux::input::text::string::name_label_value("address2", "Address Line 2", personal_data.address2.as_deref()))

                    grid-3 {
                        (bux::input::text::string::name_label_value("city", "City", personal_data.city.as_deref()))
                        (addr_iso::input::address_us_select::us_state_select_with_help(app, "state", "State", personal_data.state.as_deref(), "").await?)
                        (bux::input::text::string::name_label_value("zip", "ZIP", personal_data.zip.as_deref()))
                    }
                }
            }
        ));

        doc.set_title("Personal Information");
        doc.add_body(html! {
            (wizard)
        });

        Ok(Response::HTML(doc.into()))
    }
}

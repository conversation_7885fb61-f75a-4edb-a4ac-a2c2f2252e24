// -------------------------------------------------------------------------------------------------
// 1. Import Components
import "@bux/component/verification_code.mts";
import "@bux/component/form_wizard.mts";
import "@bux/input/checkbox.mts";
import "./verify.mcss";
import "@bux/input/text/string.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code

import { SEC } from "@granite/lib.mts";
import { VerificationCodeEvent } from "@bux/component/verification_code.mts";
import {
    signup_agent_resend_email,
    signup_agent_resend_phone,
    signup_agent_verify_email,
    signup_agent_verify_phone,
} from "./verifyλ.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements

const $form = SEC(HTMLFormElement, document, "#verification-form");

// Entire functionality of page conditional on this element existing.
if ($form.querySelector("content")) {
    const signup_agent_uuid = SEC(HTMLInputElement, $form, "[name=signup_agent_uuid]").value;

    const $phone_wrapper = SEC(HTMLDivElement, $form, ".x-phone-wrapper");
    const $email_wrapper = SEC(HTMLDivElement, $form, ".x-email-wrapper");

    const $next_button = SEC(HTMLAnchorElement, $form, "a.x-next-button");

    // -------------------------------------------------------------------------------------------------
    // 4. Bind Event Handlers

    $next_button.addEventListener("click", (event) => {
        event.preventDefault();
        // alert("todo");
        window.location.href = $next_button.href;
    });

    $form.addEventListener("on_code_entered", (event: Event) => {
        event.preventDefault();
        const custom_event = event as CustomEvent<VerificationCodeEvent>;
        const verification_code = custom_event.detail.code;
        const contact_type = custom_event.detail.type;

        if (contact_type === "Phone") {
            signup_agent_verify_phone.call({
                signup_agent_uuid,
                phone_code: verification_code,
            }).then((response) => {
                if ("Output" in response) {
                    const output = response.Output[0];
                    $phone_wrapper.innerHTML = output.inner_html;
                }
            });
        }

        if (contact_type === "Email") {
            signup_agent_verify_email.call({
                signup_agent_uuid,
                email_code: verification_code,
            }).then((response) => {
                if ("Output" in response) {
                    const output = response.Output[0];
                    $email_wrapper.innerHTML = output.inner_html;
                }
            });
        }
    });

    // Handle phone resend requests
    $form.addEventListener("PhoneResendRequested", (event: Event) => {
        event.preventDefault();
        signup_agent_resend_phone.call({
            signup_agent_uuid,
        }).then((response) => {
            if ("Output" in response) {
                const output = response.Output[0];
                $phone_wrapper.innerHTML = output.inner_html;
            }
        }).catch((error) => {
            console.error("Error resending phone code:", error);
        });
    });

    // Handle email resend requests
    $form.addEventListener("EmailResendRequested", (event: Event) => {
        event.preventDefault();
        signup_agent_resend_email.call({
            signup_agent_uuid,
        }).then((response) => {
            if ("Output" in response) {
                const output = response.Output[0];
                $email_wrapper.innerHTML = output.inner_html;
            }
        }).catch((error) => {
            console.error("Error resending email code:", error);
        });
    });

    //-------------------------------------------------------------------------------------------------
    // 5. Write Code
}

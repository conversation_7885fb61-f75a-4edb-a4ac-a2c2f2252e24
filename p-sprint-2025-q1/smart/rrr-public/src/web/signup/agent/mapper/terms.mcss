
.dancing-script-400 {
  font-family: "Dancing Script", cursive;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
}

#terms-form {
    content {
        .terms {
            border: 1px solid #dee2e6;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.4);
            padding: 3rem 4rem;
            border-radius: 1rem;
            background-color: #fcf9e9;
            color: #333;

            font-family: "Merriweather Sans", sans-serif;
            font-optical-sizing: auto;
            font-style: 1rem;

            p {
                margin-bottom: 1.25rem;
            }
            strong {
                font-weight: 600;
                color: #2c3e50;
            }
            em {
                color: #6c757d;
                font-style: italic;
            }
        }

        .x-sign {
            text-align: center;
            margin: 2rem auto;
            display: block;
            border-radius: 1rem;
            background-color: #fdffcf;
            border: 2px dashed #000;
            padding: 2rem;
            max-width: 500pt;

            .x-sign-instructions {
                margin-bottom: 1.5rem;
                text-align: left;

                p {
                    margin-bottom: 1rem;
                    font-size: 0.95rem;
                    line-height: 1.5;
                    color: #2c3e50;
                }

                .x-sign-example {
                    background-color: #f8f9fa;
                    padding: 0.75rem;
                    border-radius: 0.25rem;
                    border-left: 4px solid #007bff;
                    font-size: 0.9rem;

                    strong {
                        color: #007bff;
                        font-weight: 600;
                    }
                }
            }

            input {
                font-family: "Dancing Script", cursive;
                font-optical-sizing: auto;
                font-weight: 400;
                font-size: 32pt;
                text-align: center;
                letter-spacing: 0.5px;
            }

            .x-error:not(:empty) {
                background-color: #ffebee;
                color: #d32f2f;
                padding: 0.75rem;
                border: 1px solid #d32f2f;
                margin-bottom: 1rem;
                border-radius: 0.25rem;
                font-weight: 500;
                text-align: left;
            }
        }


    }
}

//-------------------------------------------------------------------------------------------------
// 1. Import Components

import "./password.mcss";
import "@bux/component/password_creator.mts";
import "@bux/component/form_wizard.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code

import { SEC } from "@granite/lib.mts";
import { signup_agent_complete } from "@crate/api/signup/agentλ.mts";
import BuxComponentPasswordCreator from "@bux/component/password_creator.mts";
import FormWizard from "@bux/component/form_wizard.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements
const $form = SEC(HTMLFormElement, document, "#password-form");

// Entire functionality of page conditional on this element existing.
if ($form.querySelector("content")) {
    const signup_agent_uuid = SEC(HTMLInputElement, $form, "[name=signup_agent_uuid]").value;

    const $password_creator = SEC(
        BuxComponentPasswordCreator,
        $form,
        "bux-component-password-creator",
    );

    // -------------------------------------------------------------------------------------------------
    // 4. Bind Event Handlers

    $form.addEventListener("submit", (event) => {
        if (!$password_creator.validate()) {
            event.preventDefault();
        }
    });

    // -------------------------------------------------------------------------------------------------
    // 5. Form Wizard
    // -------------------------------------------------------------------------------------------------

    new FormWizard({
        $form,
        api: signup_agent_complete.api,

        err: (errors) => {
            $password_creator.set_e(errors.password);
        },

        get: () => {
            return {
                signup_agent_uuid: signup_agent_uuid,
                password: $password_creator.value,
            };
        },

        set: (_value) => {
        },

        out: (output) => {
            window.location.href = output.next_url;
        },
    });
}

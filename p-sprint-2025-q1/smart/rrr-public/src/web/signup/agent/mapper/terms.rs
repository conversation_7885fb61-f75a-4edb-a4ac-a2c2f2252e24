#[approck::http(GET /signup/agent/{signup_agent_uuid:Uuid}/terms; AUTH None; return HTML|Redirect;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: DocumentRRR,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        use crate::api::signup::agent::signup_agent_terms_get;
        use crate::api::signup::agent::signup_agent_wizard_data;

        let terms_data = signup_agent_terms_get::call(
            app,
            identity,
            signup_agent_terms_get::Input {
                signup_agent_uuid: path.signup_agent_uuid,
            },
        )
        .await?;

        let wizard_data = signup_agent_wizard_data::call(
            app,
            identity,
            signup_agent_wizard_data::Input {
                signup_agent_uuid: path.signup_agent_uuid,
            },
        )
        .await?;

        let mut wizard = bux::component::form_wizard::new(WizardStep::Terms, wizard_data)?;

        wizard.set_id("terms-form");
        wizard.set_hidden("action", "accept_terms");
        wizard.set_hidden("signup_agent_uuid", path.signup_agent_uuid);
        wizard.set_hidden("document_uuid", terms_data.document_uuid.to_string());
        wizard.set_hidden("revision", terms_data.revision);
        wizard.add_heading("📄 Terms and Conditions");
        wizard.add_description("Please review and accept the terms and conditions.  At the bottom, you will be asked to provide your electronic signature.");
        wizard.add_body(html! {
            div.terms {
                (maud::PreEscaped(terms_data.terms_html))
            }
            div.x-sign {
                div.x-sign-instructions {
                    p {
                        "To accept these terms and conditions, please type your full name exactly as it appears in your contact information. "
                        "This serves as your electronic signature and forms a legally binding agreement."
                    }
                }
                div.x-error {}
                (bux::input::text::string::name_label_value_help(
                    "terms_accepted_name",
                    "Electronic Signature",
                    terms_data.terms_accepted_name.as_deref(),
                    "Type your first and last name exactly as shown in your contact information"
                ))
            }
        });

        doc.set_title("Terms and Conditions");
        doc.add_css("https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400..700&family=Merriweather+Sans:ital,wght@0,300..800;1,300..800&display=swap");
        doc.add_body(html! {
            (wizard)
        });

        Ok(Response::HTML(doc.into()))
    }
}

pub mod index;
pub mod password;
pub mod terms;
pub mod verify;

#[approck::prefix(/signup/agent/{signup_agent_uuid:Uuid}/)]
pub mod prefix {
    pub fn menu(menu: Menu, signup_agent_uuid: Uuid) {
        menu.set_label_uri(
            "Agent Signup",
            &format!("/signup/agent/{signup_agent_uuid}/"),
        );
    }
}

use bux::component::form_wizard::FormWizardImpl;

#[derive(PartialEq, PartialOrd)]
pub enum WizardStep {
    Index,
    Terms,
    Verify,
    Password,
}

#[allow(refining_impl_trait)]
impl FormWizardImpl for WizardStep {
    type Context = crate::api::signup::agent::signup_agent_wizard_data::Output;

    fn all_variants() -> Vec<Self> {
        vec![
            WizardStep::Index,
            WizardStep::Terms,
            WizardStep::Verify,
            WizardStep::Password,
        ]
    }

    fn step(&self, ctx: &Self::Context) -> bux::component::form_wizard::FormWizardStep {
        let label = match self {
            WizardStep::Index => "Contact Information".to_string(),
            WizardStep::Verify => "Verification".to_string(),
            WizardStep::Terms => "Terms & Conditions".to_string(),
            WizardStep::Password => "Create Password".to_string(),
        };

        let complete = match self {
            WizardStep::Index => ctx.step_contact_complete,
            WizardStep::Verify => ctx.step_verify_complete,
            WizardStep::Terms => ctx.step_terms_complete,
            WizardStep::Password => ctx.step_password_complete,
        };

        let enabled = match self {
            // Index is always enabled
            WizardStep::Index => !ctx.done,
            WizardStep::Verify => ctx.step_contact_complete && !ctx.done,
            WizardStep::Terms => ctx.step_contact_complete && !ctx.done,
            WizardStep::Password => {
                ctx.step_contact_complete
                    && ctx.step_verify_complete
                    && ctx.step_terms_complete
                    && !ctx.done
            }
        };

        if enabled {
            bux::component::form_wizard::FormWizardStep::Enabled { label, complete }
        } else {
            bux::component::form_wizard::FormWizardStep::Disabled { label, complete }
        }
    }

    fn href(&self, context: &Self::Context) -> String {
        let uuid = context.signup_agent_uuid;
        match self {
            WizardStep::Index => format!("/signup/agent/{uuid}/"),
            WizardStep::Verify => format!("/signup/agent/{uuid}/verify"),
            WizardStep::Terms => format!("/signup/agent/{uuid}/terms"),
            WizardStep::Password => format!("/signup/agent/{uuid}/password"),
        }
    }

    fn error_message(&self, step: &WizardStep, ctx: &Self::Context) -> Option<String> {
        // Don't show errors on current or future steps
        if step <= self {
            return None;
        }

        match self {
            WizardStep::Index => ctx.step_contact_error.clone(),
            WizardStep::Verify => None,
            WizardStep::Terms => ctx.step_terms_error.clone(),
            WizardStep::Password => ctx.step_password_error.clone(),
        }
    }
}

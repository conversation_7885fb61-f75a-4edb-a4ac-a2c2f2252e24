#[approck::http(GET /signup/agent/{signup_agent_uuid:Uuid}/verify; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        doc: DocumentRRR,
        identity: Identity,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use crate::api::signup::agent::{signup_agent_verify_get, signup_agent_wizard_data};
        use approck::html;

        let wizard_data = signup_agent_wizard_data::call(
            app,
            identity,
            signup_agent_wizard_data::Input {
                signup_agent_uuid: path.signup_agent_uuid,
            },
        )
        .await?;

        // this is where the verification codes get sent out.
        let verify_data = signup_agent_verify_get::call(
            app,
            identity,
            signup_agent_verify_get::Input {
                signup_agent_uuid: path.signup_agent_uuid,
            },
        )
        .await?;

        // Resend functionality will be handled via JavaScript API calls
        let phone_resend_url = "#";
        let email_resend_url = "#";

        let phone_component = {
            let mut p = bux::component::verification_code::new_phone_4(&verify_data.phone);
            p.set_verified(verify_data.phone_verified);
            p.set_resend_href(phone_resend_url);
            p
        };

        let email_component = {
            let mut e = bux::component::verification_code::new_email_4(&verify_data.email);
            e.set_verified(verify_data.email_verified);
            e.set_resend_href(email_resend_url);
            e
        };

        #[rustfmt::skip]
        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::Verify, wizard_data)?;
            wizard.set_id("verification-form");
            wizard.set_hidden("signup_agent_uuid", path.signup_agent_uuid);
            wizard.add_heading("Communication Verification");
            wizard.add_description("We only want to work with humans that we can reach out to from time to time. Please check your text messages and email for your verification codes, and enter them below.");
            wizard.add_body(html! {
                grid-2 {
                    div.x-phone-wrapper {
                        (phone_component)
                    }
                    div.x-email-wrapper {
                        (email_component)
                    }
                }
            });
            wizard
        };

        doc.set_title("Verification");
        doc.add_body(html!((wizard)));
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod signup_agent_verify_phone {
    use maud::Render;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,
        pub phone_code: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub inner_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        use crate::api::signup::agent::signup_agent_verify_phone;

        // SECON PASSTHROUGH
        let output = signup_agent_verify_phone::call(
            app,
            identity,
            signup_agent_verify_phone::Input {
                signup_agent_uuid: input.signup_agent_uuid,
                phone_code: input.phone_code.clone(),
            },
        )
        .await?;

        let phone_component = {
            let mut p = bux::component::verification_code::new_phone_4(&output.phone);
            match output.status {
                signup_agent_verify_phone::Status::Verified => {
                    p.set_verified(true);
                }
                signup_agent_verify_phone::Status::Error(ref message) => {
                    p.set_user_error(message);
                }
            }
            p
        };

        Ok(Output {
            inner_html: phone_component.render().into_string(),
        })
    }
}

#[approck::api]
pub mod signup_agent_verify_email {
    use crate::api::signup::agent::signup_agent_verify_email;
    use maud::Render;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,
        pub email_code: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub inner_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // SECON PASSTHROUGH
        let output = signup_agent_verify_email::call(
            app,
            identity,
            signup_agent_verify_email::Input {
                signup_agent_uuid: input.signup_agent_uuid,
                email_code: input.email_code.clone(),
            },
        )
        .await?;

        let email_component = {
            let mut e = bux::component::verification_code::new_email_4(&output.email);
            match output.status {
                signup_agent_verify_email::Status::Verified => {
                    e.set_verified(true);
                }
                signup_agent_verify_email::Status::Error(ref message) => {
                    e.set_user_error(message);
                }
            }
            e
        };

        Ok(Output {
            inner_html: email_component.render().into_string(),
        })
    }
}

#[approck::api]
pub mod signup_agent_resend_email {
    use crate::api::signup::agent::signup_agent_resend_email;
    use maud::Render;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub inner_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Call the core resend email API
        let output = signup_agent_resend_email::call(
            app,
            identity,
            signup_agent_resend_email::Input {
                signup_agent_uuid: input.signup_agent_uuid,
            },
        )
        .await?;

        let email_component = {
            let mut e = bux::component::verification_code::new_email_4(&output.email);
            e.set_resent(true);
            e
        };

        Ok(Output {
            inner_html: email_component.render().into_string(),
        })
    }
}

#[approck::api]
pub mod signup_agent_resend_phone {
    use crate::api::signup::agent::signup_agent_resend_phone;
    use maud::Render;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub signup_agent_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub inner_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Call the core resend phone API
        let output = signup_agent_resend_phone::call(
            app,
            identity,
            signup_agent_resend_phone::Input {
                signup_agent_uuid: input.signup_agent_uuid,
            },
        )
        .await?;

        let phone_component = {
            let mut p = bux::component::verification_code::new_phone_4(&output.phone);
            p.set_resent(true);
            p
        };

        Ok(Output {
            inner_html: phone_component.render().into_string(),
        })
    }
}

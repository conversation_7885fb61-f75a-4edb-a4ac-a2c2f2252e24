[package]
name = "rrr-public"
version = "0.1.0"
edition = "2024"

[package.metadata.approck.mod]
extends = [
    "rrr-zero",
    "approck",
    "bux",
    "granite",
    "auth-fence",
    "api-sendgrid",
    "api-twilio",
    "addr-iso",
]


[dependencies]
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
auth-fence = { workspace = true }
approck-redis = { workspace = true }
approck-postgres = { workspace = true }
api-sendgrid = { workspace = true }
api-twilio = { workspace = true }

maud = { workspace = true }

rrr-zero = { path = "../rrr-zero" }
legal-plane = { workspace = true }
addr-iso = { workspace = true }

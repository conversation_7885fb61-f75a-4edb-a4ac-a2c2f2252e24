#[approck::http(GET /admin/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("RRR Admin Dashboard");

        use crate::api::admin::dashboard::admin_dashboard;
        let dashboard_data = admin_dashboard::call(app).await?;

        // Get user name for welcome message
        let user_name = "Admin".to_string();

        doc.add_body(html!(
            rrr-admin-dashboard {
                panel {
                    content {
                        header {
                            h1 { "Welcome, " (user_name) "!" }
                            p { "Here's a quick overview of your RRR system metrics." }
                        }
                        grid-2 {
                            admin-metric {
                                i.fas.fa-user-tie aria-hidden="true" {}
                                dl {
                                    dt {
                                        a href="/admin/agent/" { "Active Agents" }
                                    }
                                    dd { "(" (dashboard_data.agent_count) ")" }
                                }
                            }
                            admin-metric {
                                i.fas.fa-user-shield  aria-hidden="true" {}
                                dl {
                                    dt {
                                        a href="/admin/auth/role/" { "Roles" }
                                    }
                                    dd { "(" (dashboard_data.role_count) ")" }
                                }
                            }
                            admin-metric {
                                i.fas.fa-key  aria-hidden="true" {}
                                dl {
                                    dt {
                                        a href="/admin/auth/permission/" { "Permissions" }
                                    }
                                    dd { "(" (dashboard_data.permission_count) ")" }
                                }
                            }
                            admin-metric {
                                i.fas.fa-users  aria-hidden="true" {}
                                dl {
                                    dt {
                                        a href="/admin/auth/identity/" { "Identities" }
                                    }
                                    dd { "(" (dashboard_data.identity_count) ")" }
                                }
                            }
                        }
                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}

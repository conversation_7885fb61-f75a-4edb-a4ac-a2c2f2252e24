rrr-admin-dashboard {

    panel {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: none;
        border-radius: 10px;
        max-width: 960px;
        margin: 0 auto 1rem auto;

        header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 10px;
            color: white;

            p {
                margin: 0;
            }
        }

        admin-metric {
            display: flex;
            align-items: center;
            gap: 1rem;
            background-color: #fff;
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid #ddd;
            border-left: 4px solid #28a745;
            position: relative;

            @media (min-width: 1200px) {
                padding: 2rem;
            }

            > i:first-child {
                height: 3rem;
                width: 3rem;
                background-color: #f1f1f1;
                border-radius: 10px;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #28a745;

                @media (min-width:  1200px) {
                    width: 4rem;
                    height: 4rem;
                    font-size: 1.33333em;
                }
            }

            dl {
                margin-bottom: 0;
                display: flex;
                gap: .5rem;

                dt {

                    a {
                        font-weight: 500;
                        color: #000;

                        @media (min-width: 1200px) {
                            font-size: 14pt;
                        }

                        &:hover {
                            color: #000;
                        }

                        &:after {
                            position: absolute;
                            top: 0;
                            right: 0;
                            bottom: 0;
                            left: 0;
                            z-index: 1;
                            content: "";
                        }
                    }
                }

                dd {
                    margin-bottom: 0;
                    font-weight: 500;

                    @media (min-width: 1200px) {
                        font-size: 14pt;
                    }
                }
            }

            > a:last-child {
                margin-left: auto;
                color: #28a745;
            }
        }
    }
}
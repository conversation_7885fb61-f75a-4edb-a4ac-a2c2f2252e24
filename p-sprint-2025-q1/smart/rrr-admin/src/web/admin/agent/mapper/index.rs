#[approck::http(GET /admin/agent/{agent_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::agent::detail::admin_agent_detail;
        use maud::html;

        let agent = admin_agent_detail::call(
            app,
            identity,
            admin_agent_detail::Input {
                agent_uuid: path.agent_uuid,
            },
        )
        .await?;

        doc.set_title("Agent Details");
        doc.page_nav_edit_record("Edit Agent", &crate::ml_agent_edit(agent.agent_uuid));

        // Prepare display values
        let display_email = agent.email.as_deref().unwrap_or("Email not available");
        let display_phone = agent.phone.as_deref().unwrap_or("Phone not available");

        // Agent Information
        let agent_info = {
            let mut agent_info =
                bux::component::insight_deck::InsightDeck::new("Agent Information");
            agent_info.description("An overview of your contact details.");

            agent_info.add_basic_row(
                "fas fa-user",
                "Full Name",
                html!((format!("{} {}", agent.first_name, agent.last_name))),
            );

            agent_info.add_basic_row(
                "fas fa-calendar-plus",
                "Created On",
                html!((agent.create_ts.format("%B %d, %Y at %I:%M %p"))),
            );

            agent_info.add_basic_row(
                "fas fa-toggle-on",
                "Status",
                html! {
                    @if agent.active {
                        label-tag.success { "Active" }
                    } @else {
                        label-tag.warning { "Inactive" }
                    }
                },
            );

            agent_info.add_basic_row(
                "fas fa-envelope",
                "Email",
                html! {
                    @if let Some(email) = &agent.email {
                        a href=(format!("mailto:{}", email)) { (email) }
                    } @else {
                        span.text-muted { "Not provided" }
                    }
                },
            );

            agent_info.add_basic_row(
                "fas fa-phone",
                "Phone",
                html! {
                    @if let Some(phone) = &agent.phone {
                        (phone)
                    } @else {
                        span.text-muted { "Not provided" }
                    }
                },
            );

            if let Some(address1) = &agent.address1 {
                let full_address = if let Some(address2) = &agent.address2 {
                    format!("{address1}\n{address2}")
                } else {
                    address1.clone()
                };
                agent_info.add_basic_row(
                    "fas fa-map-marker-alt",
                    "Address",
                    html!((full_address.replace('\n', ", "))),
                );
            }

            if let Some(city) = &agent.city {
                agent_info.add_basic_row("fas fa-building", "City", html!((city)));
            }

            if let Some(state) = &agent.state {
                agent_info.add_basic_row("fas fa-flag", "State", html!((state)));
            }

            if let Some(zip) = &agent.zip {
                agent_info.add_basic_row("fas fa-mail-bulk", "ZIP Code", html!((zip)));
            }

            if let Some(admin_note) = &agent.admin_note {
                if !admin_note.trim().is_empty() {
                    agent_info.add_basic_row(
                        "fas fa-sticky-note",
                        "Admin Note",
                        html!((admin_note)),
                    );
                }
            }

            agent_info
        };

        doc.add_body(html!(
            insight-deck #admin-agent-detail {
                grid-12 {
                    cell-3 {
                        panel {
                            content {
                                contact-info {
                                    h1 { (agent.name) }
                                    p.phone.mb-0 {
                                        (display_phone)
                                    }
                                    p.email {
                                        @if let Some(email) = &agent.email {
                                            a href=(format!("mailto:{}", email)) { (email) }
                                        } @else {
                                            (display_email)
                                        }
                                    }
                                    hr;
                                    @if agent.active {
                                        label-tag.success { "Active Agent" }
                                    } @else {
                                        label-tag.warning { "Inactive Agent" }
                                    }
                                }
                            }
                        }
                    }
                    cell-9 {
                        (agent_info)
                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::http(GET /admin/agent/{agent_uuid:Uuid}/edit; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;

        use crate::api::admin::agent::detail::admin_agent_detail;

        let agent = admin_agent_detail::call(
            app,
            identity,
            admin_agent_detail::Input {
                agent_uuid: path.agent_uuid,
            },
        )
        .await?;

        doc.set_title("Edit Agent");

        let mut form_panel = bux::component::save_cancel_form_panel(
            &format!("Edit Agent Details For {}", agent.name),
            &crate::ml_agent(agent.agent_uuid),
        );

        form_panel.set_hidden("agent_uuid", path.agent_uuid);

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            grid-12 {
                cell-6 {
                    (bux::input::text::string::name_label_value("first_name", "First Name", Some(&agent.first_name)))
                    (bux::input::text::string::name_label_value("last_name", "Last Name", Some(&agent.last_name)))
                    (bux::input::text::string::name_label_value("email", "Email:", agent.email.as_deref()))
                    (bux::input::text::string::name_label_value("phone", "Phone:", agent.phone.as_deref()))
                }
                cell-6 {
                    (bux::input::text::string::name_label_value("address1", "Address Line 1", agent.address1.as_deref()))
                    (bux::input::text::string::name_label_value("address2", "Address Line 2", agent.address2.as_deref()))
                    grid-3 {
                        (bux::input::text::string::name_label_value("city", "City", agent.city.as_deref()))
                        (addr_iso::input::address_us_select::us_state_select_with_help(app, "state", "State", agent.state.as_deref(), "").await?)
                        (bux::input::text::string::name_label_value("zip", "ZIP", agent.zip.as_deref()))
                    }
                    (bux::input::textarea::string::name_label_value("admin_note", "Admin Note", agent.admin_note.as_deref()))
                }
            }
        ));
        doc.add_body(html! {
            section {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}

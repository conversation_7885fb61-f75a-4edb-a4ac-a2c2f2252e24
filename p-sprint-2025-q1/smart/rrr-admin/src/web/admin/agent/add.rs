#[approck::http(GET /admin/agent/add; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("Add Agent");

        let dbcx = app.postgres_dbcx().await?;

        // Fetch GBU sales reps for dropdown
        let gbu_sales_reps = granite::pg_row_vec!(
            db = dbcx;
            row = {
                gbu_sales_rep_uuid: Uuid,
                name: String,
            };
            SELECT
                "00000000-0000-0000-0000-000000000000"::uuid AS gbu_sales_rep_uuid,
                "N/A - I don't know" AS name,
                -1 AS sort_order

            UNION

            SELECT
                gbu_sales_rep_uuid,
                name,
                sort_order
            FROM
                rrr.gbu_sales_rep
            WHERE
                active = true

            ORDER BY
                sort_order, name
        )
        .await?;

        let gbu_sales_reps_options: Vec<(String, String)> = gbu_sales_reps
            .into_iter()
            .map(|rep| (rep.gbu_sales_rep_uuid.to_string(), rep.name))
            .collect::<Vec<_>>();

        let gbu_sales_reps_options_refs: Vec<(&str, &str)> = gbu_sales_reps_options
            .iter()
            .map(|(id, name)| (id.as_str(), name.as_str()))
            .collect();

        let mut form_panel =
            bux::component::add_cancel_form_panel("Agent Information", "/admin/agent/");

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            grid-12 {
                cell-6 {
                    grid-2 {
                        (bux::input::text::string::name_label_value("first_name", "First Name", None))
                        (bux::input::text::string::name_label_value("last_name", "Last Name", None))
                    }
                    (bux::input::text::string::name_label_value("address1", "Address Line 1", None))
                    (bux::input::text::string::name_label_value("address2", "Address Line 2", None))
                    grid-3 {
                        (bux::input::text::string::name_label_value("city", "City", None))
                        (addr_iso::input::address_us_select::us_state_select_with_help(app, "state", "State", None, "").await?)
                        (bux::input::text::string::name_label_value("zip", "ZIP", None))
                    }
                }
                cell-6 {
                    (bux::input::text::string::name_label_value("email", "Email:", None))
                    (bux::input::text::string::name_label_value("phone", "Phone:", None))
                    (bux::input::select::nilla::nilla_select(
                        "gbu_sales_rep_uuid",
                        "GBU Regional Sales Representative",
                        &gbu_sales_reps_options_refs,
                        None
                    ))
                    (bux::input::textarea::string::name_label_value("admin_note", "Admin Note", None))
                }
            }
        ));
        doc.add_body(html! {
            section {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}

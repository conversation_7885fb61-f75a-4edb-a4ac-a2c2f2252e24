pub mod api;
pub mod web;

pub trait App: approck::App + approck_postgres::App + auth_fence::App {}

pub trait Identity: approck::Identity + auth_fence::Identity {
    fn web_usage(&self) -> bool;
    fn api_usage(&self) -> bool;

    fn agent_list(&self) -> bool;
    fn agent_add(&self) -> bool;
    fn agent_read(&self, agent_uuid: granite::Uuid) -> bool;
    fn agent_write(&self, agent_uuid: granite::Uuid) -> bool;
}

pub trait Document: bux::document::Cliffy {}
pub trait DocumentRRR: bux::document::Base {}

pub fn ml_agent(agent_uuid: granite::Uuid) -> String {
    format!("/admin/agent/{agent_uuid}/")
}

pub fn ml_agent_list() -> String {
    "/admin/agent/".to_string()
}

pub fn ml_agent_add() -> String {
    "/admin/agent/add".to_string()
}

pub fn ml_agent_edit(agent_uuid: granite::Uuid) -> String {
    format!("/admin/agent/{agent_uuid}/edit")
}

pub fn ml_agent_delete(agent_uuid: granite::Uuid) -> String {
    format!("/admin/agent/{agent_uuid}/delete")
}

#[approck::api]
pub mod admin_agent_add {
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        #[gtype(trim=both; max=128; no_empty;)]
        pub first_name: String,
        #[gtype(trim=both; max=128; no_empty;)]
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub gbu_sales_rep_uuid: Option<Uuid>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub admin_note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub agent_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.agent_add() {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to agent add".to_string(),
            ));
        }

        let mut dbcx = app.postgres_dbcx().await?;

        // Convert all-zero UUID to NULL for database storage
        let gbu_sales_rep_uuid_for_agent = match &input.gbu_sales_rep_uuid {
            Some(uuid)
                if *uuid
                    == granite::Uuid::parse_str("00000000-0000-0000-0000-000000000000")
                        .unwrap() =>
            {
                None
            }
            Some(uuid) => Some(*uuid),
            None => None,
        };

        // Always create identity for the agent
        let identity_input = auth_fence::api::admin::identity::add::add::Input {
            identity_type: "User".to_string(),
            name: format!("{} {}", input.first_name, input.last_name),
            email: input.email.clone(),
            note: Some("Created alongside agent".to_string()),
            avatar_uri: None,
            active: true,
        };

        let identity_result =
            auth_fence::api::admin::identity::add::add::call(app, identity, identity_input).await?;

        let identity_uuid = Some(identity_result.identity_uuid);

        // Transaction block
        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $identity_uuid: &identity_uuid,
                    $first_name: &input.first_name,
                    $last_name: &input.last_name,
                    $email: &input.email,
                    $phone: &input.phone,
                    $gbu_sales_rep_uuid: &gbu_sales_rep_uuid_for_agent,
                    $address1: &input.address1,
                    $address2: &input.address2,
                    $city: &input.city,
                    $state: &input.state,
                    $zip: &input.zip,
                    $admin_note: &input.admin_note
                };
                row = {
                    agent_uuid: Uuid,
                };

                INSERT INTO
                    rrr.agent
                    (
                        identity_uuid,
                        first_name,
                        last_name,
                        email,
                        phone,
                        gbu_sales_rep_uuid,
                        address1,
                        address2,
                        city,
                        state,
                        zip,
                        admin_note
                    )
                VALUES
                    (
                        $identity_uuid,
                        $first_name,
                        $last_name,
                        $email,
                        $phone,
                        $gbu_sales_rep_uuid,
                        $address1,
                        $address2,
                        $city,
                        $state,
                        $zip,
                        $admin_note
                    )
                RETURNING
                    agent_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;
            row
        };

        Ok(Response::Output(Output {
            agent_uuid: row.agent_uuid,
            detail_url: crate::ml_agent(row.agent_uuid),
        }))
    }
}

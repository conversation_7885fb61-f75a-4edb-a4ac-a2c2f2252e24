#[approck::api]
pub mod admin_agent_list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
        pub active: Option<bool>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub agent_list: Vec<Agent>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Agent {
        pub agent_uuid: Uuid,
        pub create_ts: DateTimeUtc,
        pub first_name: String,
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub admin_note: Option<String>,
        pub active: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.agent_list() {
            return_authorization_error!("insufficient permissions to agent list");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $keyword: &input.keyword,
                $active: &input.active,
            };
            row = {
                agent_uuid: Uuid,
                create_ts: DateTimeUtc,
                first_name: String,
                last_name: String,
                email: Option<String>,
                phone: Option<String>,
                admin_note: Option<String>,
                active: bool,
            };
            SELECT
                agent_uuid,
                create_ts,
                first_name,
                last_name,
                email,
                phone,
                admin_note,
                active
            FROM
                rrr.agent
            WHERE true
                AND ($keyword::text IS NULL OR (first_name ILIKE "%" || $keyword::text || "%" OR last_name ILIKE "%" || $keyword::text || "%"))
                AND ($active::bool IS NULL OR active = $active::bool)
            ORDER BY
                first_name, last_name
        )
        .await?;

        Ok(Output {
            agent_list: rows
                .into_iter()
                .map(|r| Agent {
                    agent_uuid: r.agent_uuid,
                    create_ts: r.create_ts,
                    first_name: r.first_name,
                    last_name: r.last_name,
                    email: r.email,
                    phone: r.phone,
                    admin_note: r.admin_note,
                    active: r.active,
                })
                .collect(),
        })
    }
}

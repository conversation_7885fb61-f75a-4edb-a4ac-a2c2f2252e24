#[approck::api]
pub mod admin_agent_detail {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub agent_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub agent_uuid: Uuid,
        pub create_ts: DateTimeUtc,
        pub first_name: String,
        pub last_name: String,
        pub name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub active: bool,
        pub admin_note: Option<String>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.agent_read(input.agent_uuid) {
            return_authorization_error!("insufficient permissions to agent {}", input.agent_uuid);
        }

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $agent_uuid: &input.agent_uuid,
            };
            row = {
                agent_uuid: Uuid,
                create_ts: DateTimeUtc,
                first_name: String,
                last_name: String,
                name: String,
                email: Option<String>,
                phone: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,
                active: bool,
                admin_note: Option<String>,
            };
            SELECT
                agent_uuid,
                create_ts,
                first_name,
                last_name,
                first_name || " " || last_name AS name,
                email,
                phone,
                address1,
                address2,
                city,
                state,
                zip,
                active,
                admin_note

            FROM
                rrr.agent
            WHERE true
                AND agent_uuid = $agent_uuid::uuid
        )
        .await?;

        Ok(Output {
            agent_uuid: row.agent_uuid,
            create_ts: row.create_ts,
            first_name: row.first_name,
            last_name: row.last_name,
            name: row.name,
            email: row.email,
            phone: row.phone,
            address1: row.address1,
            address2: row.address2,
            city: row.city,
            state: row.state,
            zip: row.zip,
            active: row.active,
            admin_note: row.admin_note,
        })
    }
}

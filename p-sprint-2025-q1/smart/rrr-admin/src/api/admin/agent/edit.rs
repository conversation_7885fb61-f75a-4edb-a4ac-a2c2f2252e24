#[approck::api]
pub mod admin_agent_edit {

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub agent_uuid: Uuid,
        pub gbu_agent_esid: Option<String>,
        #[gtype(trim=both; max=128; no_empty;)]
        pub first_name: String,
        #[gtype(trim=both; max=128; no_empty;)]
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,
        pub admin_note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        if !identity.agent_write(input.agent_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to edit agent".to_string(),
            ));
        }

        let dbcx = app.postgres_dbcx().await?;

        granite::pg_execute!(
            db = dbcx;
            args = {
                $agent_uuid: &input.agent_uuid,
                $gbu_agent_esid: &input.gbu_agent_esid,
                $first_name: &input.first_name,
                $last_name: &input.last_name,
                $email: &input.email,
                $phone: &input.phone,
                $address1: &input.address1,
                $address2: &input.address2,
                $city: &input.city,
                $state: &input.state,
                $zip: &input.zip,
                $admin_note: &input.admin_note
            };
            UPDATE
                rrr.agent
            SET
                gbu_agent_esid = $gbu_agent_esid,
                first_name = $first_name,
                last_name = $last_name,
                email = $email,
                phone = $phone,
                address1 = $address1,
                address2 = $address2,
                city = $city,
                state = $state,
                zip = $zip,
                admin_note = $admin_note
            WHERE
                agent_uuid = $agent_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: crate::ml_agent(input.agent_uuid),
            message: "Agent updated".into(),
        }))
    }
}

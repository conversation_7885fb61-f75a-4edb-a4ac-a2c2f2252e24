#[approck::api]
pub mod admin_dashboard {

    #[granite::gtype(ApiInput)]
    pub struct Input {}

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub agent_count: i64,
        pub identity_count: i64,
        pub role_count: i64,
        pub permission_count: i64,
    }

    pub async fn call(app: App) -> Result<Output> {
        // Auth check is handled automatically by the api macro

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            row = {
                agent_count: i64,
                identity_count: i64,
                role_count: i64,
                permission_count: i64,
            };
            SELECT
                (SELECT COUNT(*) FROM rrr.agent WHERE active = true) AS agent_count,
                (SELECT COUNT(*) FROM auth_fence.identity) AS identity_count,
                (SELECT COUNT(*) FROM auth_fence.role) AS role_count,
                (SELECT COUNT(*) FROM auth_fence.permission) AS permission_count
        )
        .await?;

        Ok(Output {
            agent_count: row.agent_count,
            identity_count: row.identity_count,
            role_count: row.role_count,
            permission_count: row.permission_count,
        })
    }
}

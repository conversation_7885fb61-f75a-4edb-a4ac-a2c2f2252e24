impl df4l_icover::App for crate::AppStruct {
    fn auth_fence_provider(&self) -> &auth_fence_provider::ModuleStruct {
        &self.auth_fence_provider
    }
}

impl df4l_icover::Identity for crate::IdentityStruct {
    fn web_usage(&self) -> bool {
        true
    }

    fn api_usage(&self) -> bool {
        true
    }

    async fn client_read(
        &self,
        dbcx: &impl approck_postgres::DB,
        client_uuid: granite::Uuid,
    ) -> bool {
        // If this is coming from a provider (e.g. a token), then we need to confirm the scope
        // is part of the token
        if let Some(auth_fence_provider) = &self.auth_fence_provider {
            if !auth_fence_provider.scope_d2c_read {
                return false;
            }
        }

        // The remainder of this will work fine on either provider/local based identities.

        // translate client to advisor for permission check
        let advisor_uuid =
            match super::df4l_advisor::client_uuid_to_advisor_uuid(dbcx, &client_uuid).await {
                Ok(advisor_uuid) => advisor_uuid,
                Err(e) => {
                    approck::error!("Error looking up advisor_uuid for client_uuid: {:?}", e);
                    return false;
                }
            };

        approck::info!(
            "df4l_icover::Identity::client_read: Found advisor_uuid: {advisor_uuid} for client_uuid: {client_uuid}"
        );

        // make sure the advisor has read permission
        if let Some(df4l_advisor) = &self.df4l_advisor {
            if df4l_advisor.contains_key(&advisor_uuid) {
                return true;
            }
        }

        false
    }
}

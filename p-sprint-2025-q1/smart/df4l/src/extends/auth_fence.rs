use crate::extends::df4l_gbu::OpenIDGBUProvider;
use auth_fence::types::AuthProviderType;

impl auth_fence::App for crate::AppStruct {
    fn after_login_next_url<'a>(&self, next_uri: Option<&'a str>) -> &'a str {
        match next_uri {
            Some(next_uri) => next_uri,
            None => "/dashboard",
        }
    }

    fn auth_fence_system(&self) -> &auth_fence::types::ModuleStruct {
        &self.auth_fence
    }

    fn register_custom_providers(
        &self,
        system: &mut auth_fence::types::ModuleStruct,
        config: &auth_fence::types::ModuleConfig,
    ) {
        for provider in config.openid.values() {
            if let auth_fence::types::OpenIDConfig::Custom(base_config) = provider {
                if base_config.key == "gbu" {
                    system.register_provider(AuthProviderType::OpenID(Box::new(
                        OpenIDGBUProvider {
                            config: base_config.clone(),
                        },
                    )));
                }
            }
        }
    }
}

impl auth_fence::Identity for crate::IdentityStruct {
    fn is_logged_in(&self) -> bool {
        self.auth_fence.is_some()
    }
    fn identity_uuid(&self) -> Option<granite::Uuid> {
        match &self.auth_fence {
            Some(auth_fence) => Some(auth_fence.identity_uuid),
            None => None,
        }
    }
    fn remote_address(&self) -> std::net::IpAddr {
        self.request.remote_address
    }
    fn session_token(&self) -> String {
        self.request.session_token.clone()
    }
}

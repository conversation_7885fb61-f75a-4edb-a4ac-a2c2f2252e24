use crate::{AppStruct, IdentityStruct};

impl auth_fence_provider::Identity for IdentityStruct {
    fn scope_email(&self) -> bool {
        match &self.auth_fence_provider {
            Some(auth_fence_provider) => auth_fence_provider.scope_email,
            None => false,
        }
    }

    fn scope_profile(&self) -> bool {
        match &self.auth_fence_provider {
            Some(auth_fence_provider) => auth_fence_provider.scope_profile,
            None => false,
        }
    }
}

impl auth_fence_provider::App for AppStruct {
    fn auth_fence_provider(&self) -> &auth_fence_provider::ModuleStruct {
        &self.auth_fence_provider
    }
}

use auth_fence::openid::{OpenIDProvider, OpenIDProviderType};
use auth_fence::types::BaseOpenIDConfig;

pub struct OpenIDGBUProvider {
    pub config: BaseOpenIDConfig,
}

#[async_trait::async_trait]
impl OpenIDProvider for OpenIDGBUProvider {
    fn key(&self) -> String {
        self.config.key.clone()
    }
    fn provider(&self) -> OpenIDProviderType {
        OpenIDProviderType::Custom("gbu".to_string())
    }
    fn client_id(&self) -> String {
        self.config.client_id.clone()
    }
    fn client_secret(&self) -> String {
        self.config.client_secret.clone()
    }
    fn get_scopes(&self) -> Vec<String> {
        vec![
            "openid".to_string(),
            "profile".to_string(),
            "email".to_string(),
        ]
    }
    fn get_base_uri(&self) -> String {
        "https://sauth-uat.gbu.org".to_string()
    }
    fn get_issuer_url(&self) -> String {
        format!("{}/auth/realms/gbu", self.get_base_uri())
    }
    fn get_user_info_uri(&self) -> String {
        format!(
            "{}/auth/realms/gbu/protocol/openid-connect/userinfo",
            self.get_base_uri()
        )
    }
    fn get_redirect_uri(&self, app_url: String) -> String {
        format!("{}/oauth2/{}/redirect", app_url, self.key())
    }
    fn get_auth_uri(&self) -> String {
        format!(
            "{}/auth/realms/gbu/protocol/openid-connect/auth",
            self.get_base_uri()
        )
    }
    fn get_token_uri(&self) -> String {
        format!(
            "{}/auth/realms/gbu/protocol/openid-connect/token",
            self.get_base_uri()
        )
    }
    fn get_well_known_uri(&self) -> String {
        format!(
            "{}/auth/realms/gbu/.well-known/openid-configuration",
            self.get_base_uri()
        )
    }
    fn get_jwks_uri(&self) -> String {
        format!(
            "{}/auth/realms/gbu/protocol/openid-connect/certs",
            self.get_base_uri()
        )
    }
    fn get_button_html(&self, next_uri: Option<String>) -> maud::PreEscaped<String> {
        maud::html!(
            a href=(self.get_button_uri(next_uri)) class="btn btn-outline-custom social-btn" {
                "GBU"
            }
        )
    }
}

impl df4l_gbu::App for crate::AppStruct {}

impl df4l_gbu::Identity for crate::IdentityStruct {
    fn web_usage(&self) -> bool {
        true
    }

    fn api_usage(&self) -> bool {
        true
    }

    async fn client_read(
        &self,
        dbcx: &impl approck_postgres::DB,
        client_uuid: granite::Uuid,
    ) -> bool {
        // If this is coming from a provider (e.g. a token), then we need to confirm the scope
        // is part of the token
        if let Some(auth_fence_provider) = &self.auth_fence_provider {
            if !auth_fence_provider.scope_d2c_read {
                return false;
            }
        }

        // The remainder of this will work fine on either provider/local based identities.

        // translate client to advisor for permission check
        let advisor_uuid =
            match super::df4l_advisor::client_uuid_to_advisor_uuid(dbcx, &client_uuid).await {
                Ok(advisor_uuid) => advisor_uuid,
                Err(e) => {
                    approck::error!("Error looking up advisor_uuid for client_uuid: {:?}", e);
                    return false;
                }
            };

        approck::info!(
            "df4l_gbu::Identity::client_read: Found advisor_uuid: {advisor_uuid} for client_uuid: {client_uuid}"
        );

        // make sure the advisor has read permission
        if let Some(df4l_advisor) = &self.df4l_advisor {
            if df4l_advisor.contains_key(&advisor_uuid) {
                return true;
            }
        }

        false
    }
}

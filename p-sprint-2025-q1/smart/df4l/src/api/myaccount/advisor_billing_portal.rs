#[approck::api]
pub mod advisor_billing_portal {
    use granite::NestedError;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
        pub advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub stripe_portal_link: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.advisor_read(input.advisor_uuid) {
            return_authorization_error!("identity.advisor_read({})", input.advisor_uuid);
        }

        //Get billing info
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                stripe_customer_id: Option<String>,
                stripe_subscription_id: Option<String>,
            };
            SELECT
                (
                    SELECT c.customer_id
                    FROM api_stripe.customer c
                    WHERE c.api_stripe_customer_uuid = a.api_stripe_customer_uuid
                ) AS stripe_customer_id,
                (
                    SELECT s.subscription_id
                    FROM api_stripe.subscription s
                    WHERE s.api_stripe_customer_uuid = a.api_stripe_customer_uuid
                    LIMIT 1
                ) AS stripe_subscription_id
            FROM
                df4l.advisor a
            WHERE
                a.advisor_uuid = $advisor_uuid::uuid
        )
        .await?;

        let (stripe_customer_id, _stripe_subscription_id) = match (
            row.stripe_customer_id,
            row.stripe_subscription_id,
        ) {
            (Some(customer_id), Some(subscription_id)) => (customer_id, subscription_id),
            _ => {
                return Ok(Response::ValidationError(NestedError {
                    outer:
                        "Please contact customer service for information on your subscription or payments."
                            .to_string(),
                    inner: None,
                }));
            }
        };

        let return_url = format!(
            "{}/myaccount/{}/", //TODO update URL
            approck::server::App::webserver_system(app).url(),
            input.identity_uuid
        );

        let stripe_portal_link = app
            .stripe()
            .create_billing_portal_link2(
                &api_stripe::core::stripeapi::create_billing_portal_link::CreateBillingPortalLink {
                    customer_id: stripe_customer_id,
                    return_url,
                    configuration_id: None,
                },
            )
            .await
            .into_result();

        match stripe_portal_link {
            Ok(stripe_portal_link) => Ok(Response::Output(Output {
                stripe_portal_link: stripe_portal_link.url,
            })),
            Err(e) => {
                approck::error!("Error creating billing portal link: {:?}", e);
                Ok(Response::ValidationError(NestedError {
                    outer:
                        "Error generating link to Stripe Billing Portal. Please contact customer service for information on your subscription or payments."
                            .to_string(),
                    inner: None,
                }))
            }
        }
    }
}

#[approck::http(GET /dashboard; AUTH None; return HTML|Redirect;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("Dashboard");

        use crate::api::dashboard::entity_picker;
        let output = entity_picker::call(app, identity).await?;

        let rows = match output.rows {
            Some(rows) => rows,
            None => {
                return Ok(Response::Redirect(Redirect::see_other("/".to_string())));
            }
        };

        match rows.len() {
            0 => {
                doc.add_body(html!(
                    bux-action-panel {
                        panel {
                            content {
                                p { "You are not linked to any accounts. Please contact support for assistance." }
                                a href="/" { "Return to Home Page" }
                            }
                        }
                    }
                ));
                Ok(Response::HTML(doc.into()))
            }
            1 => {
                return Ok(Response::Redirect(Redirect::see_other(
                    rows[0].link.clone(),
                )));
            }
            _ => {
                let mut entity_picker = bux::component::entity_picker::new();
                entity_picker.set_heading("Please select a role to continue.");

                let uuids: Vec<String> =
                    rows.iter().map(|entity| entity.uuid.to_string()).collect();
                for (i, entity) in rows.iter().enumerate() {
                    entity_picker.append(
                        &entity.icon,
                        &entity.name,
                        &entity.link,
                        &entity.role,
                        &uuids[i],
                    );
                }

                doc.add_body(html!(
                    bux-action-panel {
                        panel {
                            content {
                                (entity_picker)
                            }
                        }
                    }
                ));
                Ok(Response::HTML(doc.into()))
            }
        }
    }
}

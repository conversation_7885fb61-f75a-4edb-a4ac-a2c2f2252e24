#[approck::http(GET /help; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Help");

        doc.add_body(html!(
            section {
                grid-row {
                    col-offset-eight {
                        panel.smart-faq {
                            content {
                                header {
                                    h1 { "Get Debt2Capital™ Help" }
                                }
                                p {
                                    "Phone Support: "
                                    a href="tel:************" { "************" }
                                }
                                p {
                                    "Email Support: "
                                    a href="mailto:<EMAIL>" { "<EMAIL>" }
                                }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

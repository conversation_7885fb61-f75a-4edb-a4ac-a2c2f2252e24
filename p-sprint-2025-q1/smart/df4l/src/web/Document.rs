bux::document! {

    pub struct Document {}

    impl Document {
        pub fn new(
            app: &'static crate::AppStruct,
            identity: &crate::IdentityStruct,
            req: &approck::server::Request,
        ) -> Self {
            use auth_fence::App;
            use bux::document::Cliffy;
            use bux::document::{Base, Nav2, Nav2Dropdown, Nav2Logo};

            let mut this = Self {
                ..Default::default()
            };

            // Base setup
            this.set_uri(req.path());
            this.set_title("Debt2Capital™"); // default title
            this.set_site_name("Debt2Capital™");


            this.set_owner("Smart Retirement Corporation");
            this.add_logo("/dashboard", "https://asset7.net/Zagula/Smart/Debt2Capital/debt2capital_logo_trademark_white.svg");

            // Nav2 setup
            this.set_identity(identity);

            if auth_fence::Identity::is_logged_in(identity) {
                this.add_nav2_menu_link("Help", "/help", "");
            }

            this.add_nav2_menu_link("FAQ", "/faq", "");

            match auth_fence::Identity::is_logged_in(identity) {
                true => {
                    if let Some(name) = bux::Identity::name(identity) {
                        let user_menu: &mut Nav2Dropdown;
                        if let Some(avatar_uri) = bux::Identity::avatar_uri(identity) {
                            let user_logo = Nav2Logo { url: avatar_uri, alt: None };
                            user_menu = this.add_nav2_menu_dropdown(&name, "user-menu", Some(user_logo), None);
                        } else {
                            user_menu = this.add_nav2_menu_dropdown(&name, "user-menu", None, None);
                        }
                        user_menu.add_link("Dashboard", "/dashboard", None);
                        user_menu.add_link("My Account", "/myaccount/", None);
                        user_menu.add_span(format!("Logged in as {name}").as_str());
                        user_menu.add_link("Logout", auth_fence::App::logout_url(app), None);
                    };
                }
                false => {
                    this.add_nav2_menu_link("Login", app.login_url(), "");
                }
            }

            this
        }
    }

    impl bux::document::Base for Document {
        fn render_body(&self) -> maud::Markup {
            bux::document::Cliffy::render_body(self)
        }
    }
    impl bux::document::Nav1 for Document {}
    impl bux::document::Nav2 for Document {}
    impl bux::document::PageNav for Document {}

    impl df4l_zero::Document for Document {}
    impl df4l_admin::Document for Document {}
    impl df4l_public::Document for Document {}
    impl df4l_advisor::Document for Document {}
    impl bux::document::FooterSocial for Document {}

    impl bux::document::Cliffy for Document {}
    impl bux::document::FooterLinks for Document {}
    impl auth_fence::Document for Document {}
    impl auth_fence::DocumentPublic for Document {}
    impl auth_fence_provider::Document for Document {}

    impl api_twilio::Document for Document {}
    impl api_sendgrid::Document for Document {}
    impl api_stripe::Document for Document {}

}

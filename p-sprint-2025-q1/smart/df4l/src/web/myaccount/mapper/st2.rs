#[approck::http(GET|POST /myaccount/{identity_uuid:Uuid}/st2; AUTH None; return HTML|Redirect;)]
pub mod page {
    pub async fn request(app: App, req: Request, doc: Document, path: Path) -> Result<Response> {
        use maud::html;

        doc.set_title("Stripe Test- Create Invoice Item Without Subscription");

        // Use pg_row_vec instead of pg_row to handle multiple rows
        let mut dbcx = app.postgres_dbcx().await?;
        let advisors = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $identity_uuid: &path.identity_uuid,
            };
            row = {
                advisor_uuid: Uuid,
                first_name: String,
                last_name: String,
                api_stripe_customer_uuid: Option<Uuid>,
                stripe_customer_id: Option<String>,
                api_stripe_subscription_uuid: Option<Uuid>,
                stripe_subscription_id: Option<String>,
            };
            SELECT
                advisor_uuid,
                first_name,
                last_name,
                df4l.advisor.api_stripe_customer_uuid,
                api_stripe.customer.customer_id as stripe_customer_id,
                df4l.advisor.api_stripe_subscription_uuid,
                (SELECT subscription_id FROM api_stripe.subscription WHERE api_stripe_subscription_uuid = df4l.advisor.api_stripe_subscription_uuid) AS stripe_subscription_id
            FROM
                df4l.advisor
            LEFT JOIN
                api_stripe.customer ON api_stripe.customer.api_stripe_customer_uuid = df4l.advisor.api_stripe_customer_uuid
            WHERE
                identity_uuid = $identity_uuid::uuid
        )
        .await?;

        // Check if we have any advisors
        if advisors.is_empty() {
            return Err(granite::process_error!(
                "No advisor found for this identity"
            ));
        }
        let settings = granite::pg_row!(
            db = dbcx;
            args = {
                $signup_advisor_setting_msid: &"d2c_software",
            };
            row = {
                stripe_product_monthly_d2c_subscription_esid: String,
                stripe_price_monthly_d2c_subscription_esid: String,
                stripe_product_tech_service_fee_esid: String,
                stripe_price_tech_service_fee_esid: String,
                stripe_product_credit_report_charge_esid: String,
                stripe_price_credit_report_charge_esid: String,
            };
            SELECT
                stripe_product_monthly_d2c_subscription_esid,
                stripe_price_monthly_d2c_subscription_esid,
                stripe_product_tech_service_fee_esid,
                stripe_price_tech_service_fee_esid,
                stripe_product_credit_report_charge_esid,
                stripe_price_credit_report_charge_esid
            FROM
                df4l.signup_advisor_setting
            WHERE
                signup_advisor_setting_msid = $signup_advisor_setting_msid
        )
        .await?;

        // Handle form submission
        if req.method() == "POST" {
            approck::info!("Form submitted");
            approck::info!("Calling stripe api");

            // Get the form data
            let form_data = req.read_body_query_pairs().await;
            approck::info!("Form data: {form_data:?}");

            // Get the customer_id from form data
            let customer_id = form_data
                .iter()
                .find(|(k, _)| k == "customer_id")
                .map(|(_, v)| v.to_string())
                .ok_or_else(|| granite::process_error!("Missing customer_id in form data"))?;

            // Get the Stripe secret key
            let secret_key = app.stripe().secret_key.clone();

            // Create first invoice item (Tech & Service Fee)
            let result1 = api_stripe::core::invoice_item::create_invoice_item(
                &mut dbcx,
                &secret_key,
                customer_id.clone(),
                Some("".to_string()), // Empty subscription ID since we don't want to attach to a subscription
                settings.stripe_price_tech_service_fee_esid,
                "Tech & Service Fee (one time charge)".to_string(),
                Some(1), // Add quantity parameter
            )
            .await;

            // Create second invoice item (Report Charge)
            let result2 = api_stripe::core::invoice_item::create_invoice_item(
                &mut dbcx,
                &secret_key,
                customer_id,
                Some("".to_string()), // Empty subscription ID since we don't want to attach to a subscription
                settings.stripe_price_credit_report_charge_esid,
                "Report Charge (one time charge)".to_string(),
                Some(1), // Add quantity parameter
            )
            .await;

            // Check if both operations were successful
            match (result1, result2) {
                (Ok(item1), Ok(item2)) => {
                    approck::info!("Invoice items created successfully");
                    approck::info!("Item 1: {item1:?}");
                    approck::info!("Item 2: {item2:?}");

                    // Redirect to success page
                    return Ok(Response::Redirect(
                        format!("/myaccount/{}/st2?success=true", path.identity_uuid)
                            .as_str()
                            .into(),
                    ));
                }
                (Err(e), _) => {
                    approck::error!("Error creating first invoice item: {e:?}");
                    return Err(e);
                }
                (_, Err(e)) => {
                    approck::error!("Error creating second invoice item: {e:?}");
                    return Err(e);
                }
            }
        }

        // Build the HTML with all advisors
        doc.add_body(html!(
            d2c-sign-up {
                panel {
                    content {
                        p { "This is a test page to create an invoice item." }
                        @for (i, advisor) in advisors.iter().enumerate() {
                            div .advisor-info {
                                h3 { "Advisor " (i + 1) ": " (advisor.first_name) " " (advisor.last_name) }
                                p { "Advisor UUID: " (advisor.advisor_uuid) }
                                p { "Stripe Customer ID: " (advisor.stripe_customer_id.as_deref().unwrap_or("None")) }
                                p { "Stripe Subscription ID: " (advisor.stripe_subscription_id.as_deref().unwrap_or("None")) }
                                form id=(format!("stripe_test-form-{}", i)) method="post" {
                                    input type="hidden" name="advisor_uuid" value=(advisor.advisor_uuid);
                                    input type="hidden" name="customer_id" value=(advisor.stripe_customer_id.clone().unwrap_or_default());
                                    (bux::button::submit::label("Create Invoice Item Without Subscription"))
                                }
                            }
                        }
                    }
                }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

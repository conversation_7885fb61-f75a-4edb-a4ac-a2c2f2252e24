//-------------------------------------------------------------------------------------------------
// 1. Import Components

import "@bux/component/insight_deck.mts";
import "./index.mcss";

// -------------------------------------------------------------------------------------------------
// 2. Import Code
import { advisor_billing_portal } from "@crate/api/myaccount/advisor_billing_portalλ.mts";
import { SA, SEC, SEC_array } from "@granite/lib.mts";

// -------------------------------------------------------------------------------------------------

// find ALL elements with class=stripe-portal-link
const $stripe_wrapper_list = SEC_array(HTMLDivElement, document, ".x-stripe-wrapper");

$stripe_wrapper_list.forEach(($wrapper) => {
    const identity_uuid = SA(String, $wrapper, "identity_uuid");
    const advisor_uuid = SA(String, $wrapper, "advisor_uuid");
    const $error = SEC(HTMLElement, $wrapper, "error");
    const $link = SEC(HTMLAnchorElement, $wrapper, "a");

    $link.addEventListener("click", (event) => {
        event.preventDefault();
        call_stripe(identity_uuid, advisor_uuid, $error);
    });
});

async function call_stripe(
    identity_uuid: string,
    advisor_uuid: string,
    $message_container: HTMLElement,
) {
    $message_container.innerHTML = "";

    const response = await advisor_billing_portal.api.call({
        identity_uuid: identity_uuid,
        advisor_uuid: advisor_uuid,
    });

    if ("ValidationError" in response) {
        const errors = response.ValidationError[0];

        if ("Outer" in errors) {
            $message_container.textContent = errors.Outer;
        }
    }

    if ("Output" in response) {
        const output = response.Output[0];
        // Redirect to Stripe  URL
        window.location.href = output.stripe_portal_link;
    }
}

pub mod index;
pub mod security;
pub mod st1;
pub mod st2;

#[approck::prefix(/myaccount/{identity_uuid:Uuid}/)]
pub mod prefix {
    pub fn menu(menu: Menu, identity: Identity, identity_uuid: Uuid) {
        menu.set_label_name_uri(
            "My Account",
            identity.name(),
            &crate::ml_myaccount(identity_uuid),
        );
        menu.add_link(
            "Security Settings",
            &auth_fence::ml_myaccount_security(identity_uuid),
        );
        menu.add_link("Back To Dashboard", "/dashboard");
    }
    pub fn auth() {}
}

#[approck::http(GET /myaccount/{identity_uuid:Uuid}/security/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Security Settings");

        // Fetch all account data using our consolidated API
        use crate::api::myaccount::summary::get_myaccount_summary;

        let account_data = get_myaccount_summary::call(
            app,
            identity,
            get_myaccount_summary::Input {
                identity_uuid: path.identity_uuid,
            },
        )
        .await?;

        // Prepare display values
        let display_email = account_data
            .email
            .as_deref()
            .unwrap_or("Email not available");

        let html_content = html!(
            d2c-security {
                grid-12 {
                    cell-3 {
                        panel {
                            content {
                                contact-info {
                                    h1 { (account_data.name) }
                                    p.phone.mb-0 {
                                        (account_data.mobile_phone.as_deref().unwrap_or("Phone not available"))
                                    }
                                    p.email {
                                        @if let Some(email) = &account_data.email {
                                            a href=(format!("mailto:{}", email)) { (email) }
                                        } @else {
                                            (display_email)
                                        }
                                    }
                                }
                            }
                        }
                    }
                    cell-9 {
                        panel.login-info {
                            header {
                                div {
                                    h5 { "Login With Username and Password" }
                                    p { "Manage your login credentials and review recent login activity with username and password." }
                                }
                            }
                            content {
                                ul.data-list {
                                    li {
                                        hbox {
                                            i.fas.fa-fingerprint aria-hidden="true" {}
                                            dl {
                                                dt { "Username" }
                                                dd {
                                                    @if account_data.username.is_empty() {
                                                        "Username not set"
                                                    } @else {
                                                        (account_data.username)
                                                    }
                                                }
                                            }
                                            @if !account_data.username.is_empty() {
                                                a href=(auth_fence::ml_myaccount_security_username(path.identity_uuid)) { i.fas.fa-chevron-right {} }
                                            } @else {
                                                a href=(auth_fence::ml_myaccount_security_login(path.identity_uuid)) { i.fas.fa-chevron-right {} }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-key aria-hidden="true" {}
                                            dl {
                                                dt { "Password Last Changed" }
                                                dd {
                                                    @if let Some(password_changed) = &account_data.password_last_changed {
                                                        (password_changed)
                                                    } @else {
                                                        "Password not set"
                                                    }
                                                }
                                            }
                                            @if account_data.password_last_changed.is_some() {
                                            a href=(auth_fence::ml_myaccount_security_password(path.identity_uuid)) { i.fas.fa-chevron-right {} }
                                            } @else {
                                                a href=(auth_fence::ml_myaccount_security_login(path.identity_uuid)) { i.fas.fa-chevron-right {} }
                                            }

                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-sign-in-alt aria-hidden="true" {}
                                            dl {
                                                dt { "Last Login" }
                                                dd {
                                                    @if account_data.last_login_with_email_password.is_some() {
                                                        label-tag.success {
                                                            (account_data.last_login_with_email_password.as_deref().unwrap_or("Unknown"))
                                                        }
                                                    } @else {
                                                        label-tag.default { "N/A" }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-calendar-alt aria-hidden="true" {}
                                            dl {
                                                dt { "Failed Login Attempts In 24 Hours" }
                                                dd {
                                                    (format!("{}", account_data.failed_login_attempts_in_last_24_hours))
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        panel.google-login-info {
                            header {
                                div {
                                    h5 { "Login With Google" }
                                }
                            }
                            content {
                                ul.data-list {
                                    li {
                                        hbox {
                                            i.fab.fa-google style="color: #DB4437;" aria-hidden="true" {}
                                            dl {
                                                dt { "Login Enabled" }
                                                dd {
                                                    @if account_data.login_with_google_enabled {
                                                        label-tag.success { "Yes" }
                                                    } @else {
                                                        label-tag.warning { "No" }
                                                    }
                                                }
                                            }
                                            a href=(auth_fence::ml_myaccount_security_sso_google(path.identity_uuid)) { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                }
                            }
                        }
                        //show this panel if microsoft is connected
                        panel.microsoft-login-info {
                            header {
                                div {
                                    h5 { "Login With Microsoft" }
                                }
                            }
                            content {
                                ul.data-list {
                                    li {
                                        hbox {
                                            i.fab.fa-microsoft style="color: #00A4EF;" aria-hidden="true" {}
                                            dl {
                                                dt { "Login Enabled" }
                                                dd {
                                                    @if account_data.login_with_microsoft_enabled {
                                                        label-tag.success { "Yes" }
                                                    } @else {
                                                        label-tag.warning { "No" }
                                                    }
                                                }
                                            }
                                            a href=(auth_fence::ml_myaccount_security_sso_microsoft(path.identity_uuid)) { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                }
                            }
                        }
                        panel.security-settings {
                            header {
                                div {
                                    h5 { "Security Settings" }
                                    p { "Manage your security settings." }
                                }
                            }
                            content {
                                ul.data-list {
                                    li {
                                        hbox {
                                            i.fas.fa-mobile-alt aria-hidden="true" {}
                                            dl {
                                                dt { "Mobile Number" }
                                                dd {
                                                    small { (account_data.mobile_phone.as_deref().unwrap_or("Phone not available")) }
                                                    br;
                                                    label-tag.success { "Verified on 06/06/2025 TODO" }
                                                }
                                            }
                                            a href=(auth_fence::ml_myaccount_security_mobile(path.identity_uuid)) { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-envelope-open-text aria-hidden="true" {}
                                            dl {
                                                dt { "Email Address" }
                                                dd {
                                                    small { (account_data.email.as_deref().unwrap_or("Email not available")) }
                                                    br;
                                                    label-tag.warning { "Not Verified TODO" }
                                                }
                                            }
                                            a href=(auth_fence::ml_myaccount_security_email(path.identity_uuid)) { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                }
                            }
                        }
                        panel.mfa-info {
                            header {
                                div {
                                    h5 { "2-Step Verification TODO" }
                                    p { "Manage your 2-step verification settings and verification status." }
                                }
                            }
                            content {
                                ul.data-list {
                                    li {
                                        hbox {
                                            i.fas.fa-sms aria-hidden="true" {}
                                            dl {
                                                dt { "MFA by SMS" }
                                                dd {
                                                    label-tag.success { "MFA SMS Enabled" }
                                                    br;
                                                    small { "Messages sent to +18148888888" }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-envelope aria-hidden="true" {}
                                            dl {
                                                dt { "MFA by Email" }
                                                dd {
                                                    label-tag.default { "MFA Email Not Enabled" }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-mobile-alt aria-hidden="true" {}
                                            dl {
                                                dt { "MFA by Authenticator App" }
                                                dd {
                                                    label-tag.default { "Authenticator App Not Enabled" }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                    li {
                                        hbox {
                                            @if account_data.sign_in_with_microsoft {
                                                i.fab.fa-microsoft style="color: #00A4EF;" aria-hidden="true" {}
                                                dl {
                                                    dt { "Microsoft Login" }
                                                    dd {
                                                        label-tag.success { "Enabled" }
                                                    }
                                                }
                                            } @else {
                                                i.fab.fa-microsoft style="color: #00A4EF;" aria-hidden="true" {}
                                                dl {
                                                    dt { "Microsoft Login" }
                                                    dd {
                                                        label-tag.warning { "Not Enabled" }
                                                    }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                    li {
                                        hbox {
                                            i.fas.fa-key aria-hidden="true" {}
                                            dl {
                                                dt { "Backup Codes" }
                                                dd {
                                                    label-tag.success { "Backup Codes Enabled" }
                                                    br;
                                                    small { "20 Codes Unused" }
                                                }
                                            }
                                            a href="#" { i.fas.fa-chevron-right {} }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        );

        doc.add_body(html_content);

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::http(GET /myaccount/{identity_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("My Account");

        // Fetch account data block
        let account_data = {
            use crate::api::myaccount::summary::get_myaccount_summary;

            get_myaccount_summary::call(
                app,
                identity,
                get_myaccount_summary::Input {
                    identity_uuid: path.identity_uuid,
                },
            )
            .await?
        };

        // Display values block
        let display_values = {
            #[rustfmt::skip]
            let display_email = account_data
                .email
                .as_deref()
                .unwrap_or("Email not available");

            display_email
        };

        // Advisors block
        let advisors = {
            let mut advisors = Vec::new();
            for advisor in account_data.advisors.iter() {
                let mut advisor_info =
                    bux::component::insight_deck::InsightDeck::new("Advisor Info");
                advisor_info.description(
                    "An overview of your advisor identifiers, contact details, and state licensures.",
                );
                advisor_info.add_css_class("advisor-info");
                advisor_info.add_basic_tile(
                    "fas fa-id-badge",
                    "Advisor ID",
                    html!((advisor.advisor_esid)),
                );

                advisor_info.add_edit_tile(
                    "fas fa-id-badge",
                    "GBU Agent ID",
                    match &advisor.gbu_advisor_esid {
                        Some(id) => html!((id)),
                        None => html!("None"),
                    },
                    df4l_zero::ml_myaccount_advisor_gbu(advisor.advisor_uuid),
                );

                //advisor name
                advisor_info.add_basic_row(
                    "fas fa-user",
                    "Name",
                    html! {
                        (format!("{} {}", advisor.first_name, advisor.last_name))
                        br;
                        small.text-muted {
                            a href=(df4l_zero::ml_help()) { "Please visit customer support if you want to change your name." }
                        }
                    },
                );

                advisor_info.add_edit_row_phone(
                    advisor.phone.as_deref(),
                    df4l_zero::ml_myaccount_advisor_phone(advisor.advisor_uuid),
                );
                advisor_info.add_edit_row_email(
                    advisor.email.as_deref(),
                    df4l_zero::ml_myaccount_advisor_email(advisor.advisor_uuid),
                );
                advisor_info.add_edit_row_address(
                    advisor.address.as_deref(),
                    df4l_zero::ml_myaccount_advisor_address(advisor.advisor_uuid),
                );

                // states of licensure
                advisor_info.add_edit_row(
                    "fas fa-id-badge",
                    "States of Licensure",
                    html! {
                        @if advisor.states_of_licensure.is_empty() {
                            label-tag.danger { "None" }
                        } @else {
                            @for (i, state) in advisor.states_of_licensure.iter().enumerate() {
                                @if i > 0 {
                                    " "
                                }
                                label-tag.primary { (state) }
                            }
                        }
                    },
                    df4l_zero::ml_myaccount_advisor_statelic(advisor.advisor_uuid),
                );
                //billing info
                advisor_info.add_basic_row(
                    "fas fa-credit-card",
                    "Billing",
                    html! {
                        div .x-stripe-wrapper advisor_uuid=(advisor.advisor_uuid) identity_uuid=(path.identity_uuid) {
                            error {}
                            "To review or update your billing information, "
                            a href="#" { 
                                "visit stripe billing" 
                            }
                            "."
                        }

                    },
                );

                advisors.push(advisor_info);
            }
            advisors
        };

        // Login info block TODO REMOVE
        let _login_info = {
            let mut login_info = bux::component::insight_deck::InsightDeck::new("Login Info");
            login_info
                .description("A summary of your login credentials and recent login activity.");
            login_info.add_css_class("login-info");

            // Add Details button
            login_info.add_button(bux::button::link::label_icon_class(
                "Details",
                "fas fa-arrow-right",
                &format!("/myaccount/{}/security/", path.identity_uuid),
                "sm primary",
            ));

            // Add login info tiles
            //if account_data.username.is_empty() {
            if account_data.username.is_empty() {
                login_info.add_basic_row(
                    "fas fa-fingerprint",
                    "Username",
                    html!(label-tag.warning { "Not set" }),
                );
            } else {
                login_info.add_basic_row(
                    "fas fa-fingerprint",
                    "Username",
                    html!((account_data.username)),
                );
            }

            let password_last_changed = account_data
                .password_last_changed
                .as_deref()
                .unwrap_or("Password not set");
            login_info.add_basic_row(
                "fas fa-key",
                "Password last updated",
                html! {
                    @if account_data.password_last_changed.is_some() {
                        (password_last_changed)
                    } @else {
                        label-tag.warning { "Password not set" }
                    }
                },
            );

            let last_login_with_email_password = account_data
                .last_login_with_email_password
                .as_deref()
                .unwrap_or("Never");
            login_info.add_basic_row(
                "fas fa-sign-in-alt",
                "Last login with email and password",
                html!((last_login_with_email_password)),
            );

            let login_attempts_with_email_password =
                if account_data.login_attempts_with_email_password == 0 {
                    "0 attempts".to_string()
                } else {
                    format!(
                        "{} attempts",
                        account_data.login_attempts_with_email_password
                    )
                };
            login_info.add_basic_row(
                "fas fa-calendar-alt",
                "Login attempts today with email and password",
                html!((login_attempts_with_email_password)),
            );

            let last_login_with_google = account_data
                .last_login_with_google
                .as_deref()
                .unwrap_or("Never");
            login_info.add_basic_row(
                "fab fa-google",
                "Last login with Google",
                html!((last_login_with_google)),
            );

            let login_attempts_with_google = if account_data.login_attempts_with_google == 0 {
                "0 attempts".to_string()
            } else {
                format!("{} attempts", account_data.login_attempts_with_google)
            };
            login_info.add_basic_row(
                "fab fa-google",
                "Login attempts today with Google",
                html!((login_attempts_with_google)),
            );

            let last_login_with_microsoft = account_data
                .last_login_with_microsoft
                .as_deref()
                .unwrap_or("Never");
            login_info.add_basic_row(
                "fab fa-microsoft",
                "Last login with Microsoft",
                html!((last_login_with_microsoft)),
            );

            let login_attempts_with_microsoft = if account_data.login_attempts_with_microsoft == 0 {
                "0 attempts".to_string()
            } else {
                format!("{} attempts", account_data.login_attempts_with_microsoft)
            };
            login_info.add_basic_row(
                "fab fa-microsoft",
                "Login attempts today with Microsoft",
                html!((login_attempts_with_microsoft)),
            );

            login_info
        };

        // Security settings block TODO REMOVE
        let _security_settings = {
            let mut security_settings =
                bux::component::insight_deck::InsightDeck::new("Security Settings");
            security_settings.description(
                "An overview of your current security settings and verification status",
            );
            security_settings.add_css_class("security-settings");

            // Add Details button
            security_settings.add_button(bux::button::link::label_icon_class(
                "Details",
                "fas fa-arrow-right",
                &format!("/myaccount/{}/security/", path.identity_uuid),
                "sm primary",
            ));

            // Add security settings rows
            //TODO:DF4L - Replace with real MFA and security data from database
            security_settings.add_basic_row(
                "fas fa-shield-alt",
                "2-Step Verification TODO",
                html!(label-tag.success { "Enabled" }),
            );

            let mfa_email_status = if account_data.mfa_email_enabled {
                html!(label-tag.success { "MFA Email Enabled" })
            } else {
                html!(label-tag.warning { "MFA Email Disabled" })
            };
            security_settings.add_basic_row(
                "fas fa-envelope-open-text",
                "MFA By Email TODO",
                mfa_email_status,
            );

            security_settings.add_basic_row(
                "fas fa-cog",
                "MFA By Authenticator App TODO",
                html!(label-tag.success { "Authenticator App Enabled" }),
            );

            // add google login sso status
            if account_data.sign_in_with_google {
                security_settings.add_basic_row(
                    "fab fa-google",
                    "Google Login",
                    html!(label-tag.success { "Enabled" }),
                );
            } else {
                security_settings.add_basic_row(
                    "fab fa-google",
                    "Google Login",
                    html!(label-tag.warning { "Not Enabled" }),
                );
            }

            // add microsoft login sso status
            if account_data.sign_in_with_microsoft {
                security_settings.add_basic_row(
                    "fab fa-microsoft",
                    "Microsoft Login",
                    html!(label-tag.success { "Enabled" }),
                );
            } else {
                security_settings.add_basic_row(
                    "fab fa-microsoft",
                    "Microsoft Login",
                    html!(label-tag.warning { "Not Enabled" }),
                );
            }

            security_settings.add_basic_row(
                "fas fa-mobile-alt",
                "Mobile Number TODO",
                html!(label-tag.success { "Verified" }),
            );

            security_settings.add_basic_row(
                "fas fa-envelope-open-text",
                "Email Address TODO",
                html!(label-tag.success { "Verified" }),
            );

            security_settings
        };

        // HTML rendering block
        let html_content = {
            html!(
                insight-deck #myaccount {
                    grid-12 {
                        cell-3 {
                            panel {
                                content {
                                    contact-info {
                                        h1 { (account_data.name) }
                                        p.phone.mb-0 {
                                            (account_data.mobile_phone.as_deref().unwrap_or("Phone not available"))
                                        }
                                        p.email {
                                            @if let Some(email) = &account_data.email {
                                                a href=(format!("mailto:{}", email)) { (email) }
                                            } @else {
                                                (display_values)
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        cell-9 {
                            // Advisors using InsightDeck component
                            @for advisor in &advisors {
                                (advisor)
                            }
                        }
                    }
                }
            )
        };

        doc.add_body(html_content);
        Ok(Response::HTML(doc.into()))
    }
}

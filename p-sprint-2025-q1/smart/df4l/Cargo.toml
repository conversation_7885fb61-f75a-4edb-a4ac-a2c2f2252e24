[package]
name = "df4l"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.approck.app]
port = 3014
docmap.auth-fence.DocumentPublic = "Document"
extends = [
    "addr-iso",
    "api-crs",
    "api-sendgrid",
    "api-sentry",
    "api-stripe",
    "api-twilio",
    "approck",
    "appstruct",
    "auth-fence-provider",
    "auth-fence",
    "bux",
    "df4l-admin",
    "df4l-advisor",
    "df4l-gbu",
    "df4l-icover",
    "df4l-public",
    "df4l-zero",
    "granite",
    "legal-plane",
    "msg-io-sms",
]

[package.metadata.approck.mod]
extends = ["addr-iso", "approck-postgres", "auth-fence", "api-stripe"]

[dependencies]
addr-iso = { workspace = true }
api-crs = { workspace = true }
api-sendgrid = { workspace = true }
api-sentry = { workspace = true }
api-stripe = { workspace = true }
api-twilio = { workspace = true }
approck = { workspace = true }
approck-postgres = { workspace = true }
approck-redis = { workspace = true }
appstruct = { workspace = true }
auth-fence = { workspace = true }
auth-fence-provider = { workspace = true }
bux = { workspace = true }
df4l-admin = { path = "../df4l-admin" }
df4l-advisor = { path = "../df4l-advisor" }
df4l-gbu = { path = "../df4l-gbu" }
df4l-icover = { path = "../df4l-icover" }
df4l-public = { path = "../df4l-public" }
df4l-zero = { path = "../df4l-zero" }
granite = { workspace = true }
legal-plane = { workspace = true }
msg-io-sms = { workspace = true }

async-trait = { workspace = true }
clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
toml = { workspace = true }
chrono = { workspace = true, features = ["serde"] }
serde_json = { workspace = true }
rust_decimal = { workspace = true }
rust_decimal_macros = { workspace = true }

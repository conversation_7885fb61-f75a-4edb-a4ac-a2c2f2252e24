schema df4l {

    table "client" {
        primary_key = ["client_uuid"]
        
        column "client_uuid" {
            type = "Uuid"
        }

        column "advisor_uuid" {
            type = "Uuid7"
            fkrr = "advisor.advisor_uuid"
        }

        column "first_name" {
            type = "FirstName"
        }

        column "last_name" {
            type = "LastName"
        }

        column "active" {
            type = "Active"
        }
    }


}












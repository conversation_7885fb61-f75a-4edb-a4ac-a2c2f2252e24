def get_valid_state_codes(db_connection):
    """
    Retrieves valid state codes from addr_iso.us_state table
    """
    cursor = db_connection.cursor()
    cursor.execute("SELECT state_code, name FROM addr_iso.us_state")
    state_rows = cursor.fetchall()
    
    # Create mappings for both codes and names
    valid_codes = {}
    for row in state_rows:
        code = row[0].strip()  # state_code
        name = row[1].strip()  # name
        
        # Add code -> code mapping
        valid_codes[code] = code
        
        # Add name -> code mapping (lowercase for case-insensitive matching)
        valid_codes[name.lower()] = code
    
    # Add common variations
    variations = {
        'wa - washington': 'WA',
        'string:oh': 'OH',
        'boise': 'ID',  # City in Idaho
        'ri': 'RI',
        'ms': 'MS',
        'nj': 'NJ',
        'tn': 'TN',
        'va': 'VA',
        'ct': 'CT',
        'wi': 'WI',
        'il': 'IL',
        'pa': 'PA',
        'la': 'LA',
        'fl': 'FL',
        'ne': 'NE',
        'ca': 'CA',
        'co': 'CO',
        'ky': 'KY',
    }
    
    # Add variations to the valid codes
    for variation, code in variations.items():
        valid_codes[variation.lower()] = code
    
    return valid_codes

def map_state_code(state_value, valid_state_codes):
    """
    Maps various state value formats to standard two-letter state codes.
    Returns None if the state cannot be mapped.
    
    Args:
        state_value: The state value to map
        valid_state_codes: Dictionary of valid state codes from get_valid_state_codes()
    """
    # Handle None, empty strings, etc.
    if not state_value:
        return None
    
    # Skip known invalid values
    invalid_values = [
        'united states (+1)', 'shanghai', 'john', 
        'myname', 'testuser', 'alice', 'hello', 
        'aadass', 'og', 'iin'
    ]
    
    if isinstance(state_value, str):
        normalized = state_value.strip()
        
        if normalized.lower() in [inv.lower() for inv in invalid_values]:
            return None
        
        # Try direct lookup first (case-insensitive)
        if normalized.lower() in valid_state_codes:
            return valid_state_codes[normalized.lower()]
        
        # Try without spaces
        normalized_no_spaces = normalized.lower().replace(' ', '')
        for key, value in valid_state_codes.items():
            if normalized_no_spaces == key.lower().replace(' ', ''):
                return value
    
    # Return None if no match found
    return None
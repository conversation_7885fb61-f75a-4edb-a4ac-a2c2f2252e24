import hashlib
import secrets
import time
import base64
from faker import Faker
import json
#from database import get_source_db, get_target_db
import psycopg2



fake = Faker('en_US')

import argparse

# Parse command line arguments
parser = argparse.ArgumentParser(description='Generate test data for agencies')
parser.add_argument('--seed', type=str, default="DEFAULT", help='Optional seed for random generation')
args = parser.parse_args()

# Use provided seed or generate a random one
SEED = args.seed

# Hardcode the db connection
DB = psycopg2.connect(
    host="127.0.0.1",
    port=20000,
    database="df4l",
    user="app",
    password="pass"
)
DB.autocommit = True
CURSOR = DB.cursor()

fake.seed_instance(SEED)


def gen():
    for identity in identity_table:
        print(f"Processing identity: {identity.name}")

def pick_one_of(seed, some_list):
    hash = hashlib.sha256(seed.encode()).hexdigest()
    index = int(hash, 16) % len(some_list)
    return some_list[index]

def generate_uuidv7(seed) -> str:
    # generate a deterministic UUID from seed
    hash = hashlib.sha256(seed.encode()).hexdigest()
    
    # Force first 5 characters to be 0
    modified_hash = "00000" + hash[5:]
    
    # Format as UUIDv7
    return f"{modified_hash[:8]}-{modified_hash[8:12]}-7{modified_hash[12:15]}-{modified_hash[15:19]}-{modified_hash[19:31]}"

def generate_agency_esid(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return f"{hash[:2]}-{hash[2:6]}"

def generate_advisor_esid(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return f"{hash[:3]}-{hash[3:6]}"

def generate_timestamp(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    # Use modulo to limit to reasonable range (e.g., 2010-2030)
    base_timestamp = 1262304000  # 2010-01-01 00:00:00
    range_seconds = 631152000    # ~20 years in seconds
    timestamp = base_timestamp + (int(hash[:8], 16) % range_seconds)
    return time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(timestamp))

def generate_date(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    # Use modulo to limit to reasonable range (e.g., 2010-2030)
    base_timestamp = 1262304000  # 2010-01-01 00:00:00
    range_seconds = 631152000    # ~20 years in seconds
    timestamp = base_timestamp + (int(hash[8:16], 16) % range_seconds)
    return time.strftime('%Y-%m-%d', time.gmtime(timestamp))

def generate_birth_date(seed) -> str:
    """Generate a plausible birth date for adults (ages 18-80)"""
    hash = hashlib.sha256(seed.encode()).hexdigest()

    # Calculate date range for adults aged 18-80
    current_year = 2025
    min_birth_year = current_year - 80  # 1945
    max_birth_year = current_year - 18  # 2007

    # Use hash to determine birth year within range
    year_range = max_birth_year - min_birth_year
    birth_year = min_birth_year + (int(hash[:4], 16) % year_range)

    # Use different parts of hash for month and day
    month = 1 + (int(hash[4:6], 16) % 12)

    # Days in each month (non-leap year for simplicity)
    days_in_month = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
    max_day = days_in_month[month - 1]

    # Handle leap year for February
    if month == 2 and birth_year % 4 == 0 and (birth_year % 100 != 0 or birth_year % 400 == 0):
        max_day = 29

    day = 1 + (int(hash[6:8], 16) % max_day)

    return f"{birth_year:04d}-{month:02d}-{day:02d}"

def active_or_not(seed) -> bool:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return int(hash[:1], 16) % 10 > 0

def generate_note(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()

    if int(hash[:1], 16) % 2 == 0:
        return None
    else:
        return f"Note for {hash[:10]}"

def determine_gender(first_name) -> str:
    """Determine gender based on first name"""
    # Remove titles and extra parts for analysis
    name_to_check = first_name.replace("Dr. ", "").replace("Rev. ", "").replace("Hon. ", "").replace("Prof. ", "").replace("Capt. ", "")
    name_to_check = name_to_check.split()[0]  # Take first part of compound names

    # Male names
    male_names = {
        "Al", "Bo", "Ty", "Ed", "Mo",
        "Christopher", "Maximilian", "Sebastian",
        "James", "David", "Michael",
        "Jean-Pierre", "André", "François",
        "Wei", "Jung-Ho",
        "John", "Jean", "Billy",  # From compound names
        "Robert", "William",  # From titles
        "Prince", "Bono", "Seal"
    }

    # Female names
    female_names = {
        "Alexandria", "Evangelina",
        "Maria", "Sarah",
        "María-José", "Zoë",
        "Yuki", "Priya", "Olga",
        "Mary", "Anna",  # From compound names
        "Elizabeth",  # From titles
        "Madonna", "Cher"
    }

    if name_to_check in male_names:
        return "Male"
    elif name_to_check in female_names:
        return "Female"
    else:
        # For ambiguous names, use a deterministic approach based on the name
        hash_val = hashlib.sha256(first_name.encode()).hexdigest()
        return "Male" if int(hash_val[:1], 16) % 2 == 0 else "Female"

def generate_client_type(seed) -> str:
    """80% DebtManagement, 20% PolicyOnly distribution"""
    hash_val = hashlib.sha256(seed.encode()).hexdigest()
    distribution_value = int(hash_val[:2], 16) % 100

    if distribution_value < 80:
        return "DebtManagement"
    else:
        return "PolicyOnly"

def generate_client_note(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return f"Note for {hash[:10]}"
    
def generate_phone(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    fake.seed_instance(int(hash[:8], 16))
    
    # Use a custom phone number format that's guaranteed to be under 20 chars
    phone_number = fake.numerify(text="###-###-####")
    
    return phone_number

def generate_email(seed, first_name) -> str:
    domain = pick_one_of(seed, DOMAIN_LIST)
    name = first_name.lower().replace(" ", "")
    return f"{name}@{domain}"

def generate_monthly_budget(seed) -> float:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return int(hash[7:15], 16) % 1000000 / 100.0

def generate_insurance_premium(seed) -> float:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return int(hash[:8], 16) % 1000000 / 100.0

def generate_insurance_pua(seed) -> float:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return int(hash[:8], 16) % 100000 / 100.0

def generate_net_cash_at_end(seed) -> float:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return int(hash[8:16], 16) % 1000000 / 100.0

def generate_balance(seed) -> float:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    fake.seed_instance(int(hash[:8], 16))
    return fake.random_int(min=10000, max=1000000)

def generate_interest_rate() -> float:
    return fake.pyfloat(min_value=0, max_value=10, right_digits=2)

def generate_monthly_payment(balance, seed) -> float:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    fake.seed_instance(int(hash[:8], 16))
    return round(balance / fake.random_int(min=10, max=100), 2)

def generate_crs_debt_esid(seed) -> str:
    """Generate CRS debt ESID in format: crs-[8-digit]-[7-digit]-[7-digit]"""
    hash = hashlib.sha256(seed.encode()).hexdigest()

    # Extract numbers from hash and format them
    part1 = str(int(hash[:8], 16))[-8:].zfill(8)
    part2 = str(int(hash[8:16], 16))[-7:].zfill(7)
    part3 = str(int(hash[16:24], 16))[-7:].zfill(7)

    return f"crs-{part1}-{part2}-{part3}"

def is_crs_debt(seed) -> bool:
    """Determine if debt should be CRS (85%) or manual (15%)"""
    hash = hashlib.sha256(seed.encode()).hexdigest()
    # Use modulo 100 to get percentage, 85% should be CRS
    return (int(hash[:2], 16) % 100) < 85

def create_advisor_state_licensure(advisor_uuid, client_states, seed):
    """Create state licensure records for an advisor based on their clients' states"""
    # Insert licensure records for each state where advisor has clients
    for state_code in client_states:
        if state_code:  # Skip None/empty states
            create_ts = generate_timestamp(f"{seed}-{state_code}")

            CURSOR.execute("""
                INSERT INTO df4l.advisor_statelic (
                    advisor_uuid,
                    state_code,
                    create_ts,
                    data
                ) VALUES (
                    %(advisor_uuid)s,
                    %(state_code)s,
                    %(create_ts)s,
                    %(data)s::jsonb
                )
                ON CONFLICT (advisor_uuid, state_code) DO NOTHING
            """, {
                'advisor_uuid': advisor_uuid,
                'state_code': state_code,
                'create_ts': create_ts,
                'data': json.dumps({})
            })

    DB.commit()
    return client_states
 
ADVISOR_PART_OF_AGENCY = [1, 0, 0]

AGENCY_COUNT = [3,5,6,8,4,34]

AGENCY_NAME_LIST = [
    "Admin Group",
    "Brokers' Choice of America",
    "Cincy SMART",
    "Equis Financial",
    "FFL",
    "GBU",
    "Provisor",
    "River Rock Advisors",
    "RRR",
    "Rick Law Agency",
    "SMART Advisor Network News: KD",
    "SMART Retirement",
    "SOS",
    "Trustworthy",
    "Summit Financial Group",
    "Legacy Wealth Partners",
    "Cornerstone Advisory",
    "Elite Benefits Team",
    "Guardian Financial",
    "Horizon Planning Group",
    "Integrity First Advisors",
    "KeyPoint Solutions",
    "Liberty Financial Services",
    "Meridian Wealth Management",
    "North Star Advisors",
    "Oakmont Strategic Group",
    "Pacific Coast Planning",
    "Quantum Financial",
    "Reliable Insurance Group",
    "Secure Path Advisors",
    "Steadfast Solutions",
    "True North Financial",
    "Unity Insurance Partners",
    "Venture Wealth Advisors"
]

NAME_LIST = [
    # Short names
    dict(first_name="Al", last_name="Wu"),
    dict(first_name="Bo", last_name="Li"),
    dict(first_name="Ty", last_name="Ng"),
    dict(first_name="Ed", last_name="Ko"),
    dict(first_name="Mo", last_name="Yu"),
    
    # Long names
    dict(first_name="Christopher", last_name="Montgomery-Wells"),
    dict(first_name="Alexandria", last_name="Van der Woodsen"),
    dict(first_name="Maximilian", last_name="Worthington III"),
    dict(first_name="Evangelina", last_name="Rodriguez-Martinez"),
    dict(first_name="Sebastian", last_name="Thorne-Blackwood"),
    
    # Normal names
    dict(first_name="James", last_name="Smith"),
    dict(first_name="Maria", last_name="Garcia"),
    dict(first_name="David", last_name="Johnson"),
    dict(first_name="Sarah", last_name="Williams"),
    dict(first_name="Michael", last_name="Brown"),
    
    # Special characters
    dict(first_name="Jean-Pierre", last_name="D'Arcy"),
    dict(first_name="María-José", last_name="O'Connor"),
    dict(first_name="André", last_name="St. James"),
    dict(first_name="François", last_name="Le'Blanc"),
    dict(first_name="Zoë", last_name="McKenzie"),
    
    # International
    dict(first_name="Wei", last_name="Zhang"),
    dict(first_name="Yuki", last_name="Tanaka"),
    dict(first_name="Priya", last_name="Patel"),
    dict(first_name="Jung-Ho", last_name="Kim"),
    dict(first_name="Olga", last_name="Ivanova"),
    
    # Compound
    dict(first_name="Mary Jane", last_name="Watson-Parker"),
    dict(first_name="John Paul", last_name="Jones-Smith"),
    dict(first_name="Anna Marie", last_name="von Trapp"),
    dict(first_name="Jean Claude", last_name="Van Damme"),
    dict(first_name="Billy Bob", last_name="O'Reilly"),
    
    # Professional titles
    dict(first_name="Dr. Robert", last_name="Wilson"),
    dict(first_name="Rev. Michael", last_name="Thomas"),
    dict(first_name="Hon. Elizabeth", last_name="Warren"),
    dict(first_name="Prof. William", last_name="White"),
    dict(first_name="Capt. James", last_name="Kirk"),
    
    # Single name
    dict(first_name="Madonna", last_name=""),
    dict(first_name="Cher", last_name=""),
    dict(first_name="Prince", last_name=""),
    dict(first_name="Bono", last_name=""),
    dict(first_name="Seal", last_name=""),
]

FIRST_NAME_LIST = [
    # Short names
    "Al", "Bo", "Ty", "Ed", "Mo",
    
    # Long names
    "Christopher", "Alexandria", "Maximilian", "Evangelina", "Sebastian",
    
    # Normal names
    "James", "Maria", "David", "Sarah", "Michael",
    
    # Special characters
    "Jean-Pierre", "María-José", "André", "François", "Zoë",
    
    # International
    "Wei", "Yuki", "Priya", "Jung-Ho", "Olga",
    
    # Compound
    "Mary Jane", "John Paul", "Anna Marie", "Jean Claude", "Billy Bob",
    
    # Professional titles
    "Dr. Robert", "Rev. Michael", "Hon. Elizabeth", "Prof. William", "Capt. James",
    
    # Single names
    "Madonna", "Cher", "Prince", "Bono", "Seal"
]

LAST_NAME_LIST = [
    # Short names
    "Wu", "Li", "Ng", "Ko", "Yu",
    
    # Long names
    "Montgomery-Wells", "Van der Woodsen", "Worthington III", "Rodriguez-Martinez", "Thorne-Blackwood",
    
    # Normal names
    "Smith", "Garcia", "Johnson", "Williams", "Brown",
    
    # Special characters
    "D'Arcy", "O'Connor", "St. James", "Le'Blanc", "McKenzie",
    
    # International
    "Zhang", "Tanaka", "Patel", "Kim", "Ivanova",
    
    # Compound
    "Watson-Parker", "Jones-Smith", "von Trapp", "Van Damme", "O'Reilly",
    
    # Professional
    "Wilson", "Thomas", "Warren", "White", "Kirk",
    
    # Empty for single names
    ""
]

ADDRESS_LIST = [
    # Short
    dict(line1="123 Main", line2=None, city="Anytown", state="CA", zip="12345", country="USA"),
    dict(line1="45 Oak", line2=None, city="Troy", state="NY", zip="12180", country="USA"),
    dict(line1="7 Pine", line2=None, city="Dover", state="DE", zip="19901", country="USA"),
    dict(line1="9 Elm", line2=None, city="Salem", state="MA", zip="01970", country="USA"),
    dict(line1="12 Cedar", line2=None, city="Rome", state="GA", zip="30161", country="USA"),
    # Long
    dict(line1="4567 NorthWest Oak Avenue", line2="Apartment 12B, Building C, Floor 3", city="Springfield", state="IL", zip="62701", country="USA"),
    dict(line1="7890 South Pine Road Extension", line2="Suite 204, The Grand Pines Complex", city="Smallville", state="KS", zip="66002", country="USA"),
    dict(line1="1011 East Elm Street Boulevard", line2="Unit B-17, Lower Level, Garden View", city="Boulder", state="CO", zip="80301", country="USA"),
    dict(line1="3210 Cedar Lane Drive Northeast", line2="Penthouse 9, Tower II, Historic District", city="Savannah", state="GA", zip="31401", country="USA"),
    dict(line1="6543 Birch Drive West", line2="Corporate Suite 300, Birch Business Park", city="Portland", state="OR", zip="97201", country="USA"),
    # Normal
    dict(line1="987 Maple Ct", line2=None, city="Asheville", state="NC", zip="28801", country="USA"),
    dict(line1="147 Willow Way", line2="Apt 4C", city="Miami", state="FL", zip="33101", country="USA"),
    dict(line1="258 Spruce St", line2=None, city=" Boise", state="ID", zip="83702", country="USA"),
    dict(line1="369 Laurel Dr", line2="Unit 8", city="Austin", state="TX", zip="78701", country="USA"),
    dict(line1="741 Sycamore Ave", line2=None, city="Madison", state="WI", zip="53703", country="USA"),
    # Abnormal
    dict(line1="13 Moonbeam Way", line2="Shack 3", city="Roswell", state="NM", zip="88201", country="USA"),
    dict(line1="1 Gator Swamp Rd", line2="Trailer 9", city="Gainesville", state="FL", zip="32601", country="USA"),
    dict(line1="666 Haunted Hollow", line2=None, city="Salem", state="OR", zip="97301", country="USA"),
    dict(line1="0 Nowhere Ln", line2="Box 42", city="Eureka", state="NV", zip="89316", country="USA"),
    dict(line1="9999 Unicorn Trail", line2="Glitter Barn", city="Bozeman", state="MT", zip="59715", country="USA"),
    # Short
    dict(line1="22 Birch", line2=None, city="Fargo", state="ND", zip="58102", country="USA"),
    dict(line1="8 Maple", line2=None, city="Bangor", state="ME", zip="04401", country="USA"),
    dict(line1="15 Elm", line2=None, city="Provo", state="UT", zip="84601", country="USA"),
    dict(line1="3 Oak", line2=None, city="Tulsa", state="OK", zip="74103", country="USA"),
    dict(line1="77 Pine", line2=None, city="Mobile", state="AL", zip="36602", country="USA"),
    # Long
    dict(line1="12345 West Magnolia Boulevard", line2="Condominium 56A, Tower 1, Upper Deck", city="Charleston", state="SC", zip="29401", country="USA"),
    dict(line1="67890 Northern Starlight Avenue", line2="Estate 12, Gated Community, Lot 89", city="Anchorage", state="AK", zip="99501", country="USA"),
    dict(line1="23456 Riverfront Parkway South", line2="Office 401, Riverside Corporate Center", city="Memphis", state="TN", zip="38103", country="USA"),
    dict(line1="78901 Coastal Highway East", line2="Beachfront Villa 7, Second Floor, Oceanview", city="Virginia Beach", state="VA", zip="23451", country="USA"),
    dict(line1="34567 Mountain Ridge Trail", line2="Cabin 23, Alpine Meadows Subdivision", city="Flagstaff", state="AZ", zip="86001", country="USA"),
    # Normal
    dict(line1="852 Chestnut St", line2=None, city="Lincoln", state="NE", zip="68508", country="USA"),
    dict(line1="963 Poplar Ave", line2="Apt 202", city="Columbus", state="OH", zip="43215", country="USA"),
    dict(line1="174 Aspen Dr", line2=None, city="Reno", state="NV", zip="89501", country="USA"),
    dict(line1="285 Magnolia Ln", line2="Unit 5B", city="Baton Rouge", state="LA", zip="70801", country="USA"),
    dict(line1="396 Walnut St", line2=None, city="Des Moines", state="IA", zip="50309", country="USA"),
    # Abnormal
    dict(line1="4 Dragonfly Alley", line2="Hut 2", city="Asheville", state="NC", zip="28802", country="USA"),
    dict(line1="13 Sasquatch Crossing", line2=None, city="Eureka", state="CA", zip="95501", country="USA"),
    dict(line1="7 Mermaid Cove", line2="Boat Slip 8", city="Key West", state="FL", zip="33040", country="USA"),
    dict(line1="0 Zero Point Rd", line2="Tent 6", city="Moab", state="UT", zip="84532", country="USA"),
    dict(line1="1111 Stardust Loop", line2="Rocket Pad 4", city="Houston", state="TX", zip="77002", country="USA"),
    # Short
    dict(line1="5 Laurel", line2=None, city="Sioux Falls", state="SD", zip="57103", country="USA"),
    dict(line1="9 Spruce", line2=None, city="Concord", state="NH", zip="03301", country="USA"),
    dict(line1="11 Cedar", line2=None, city="Cheyenne", state="WY", zip="82001", country="USA"),
    dict(line1="33 Pine", line2=None, city="Montpelier", state="VT", zip="05602", country="USA"),
    dict(line1="2 Oak", line2=None, city="Huntington", state="WV", zip="25701", country="USA"),
    # Long
    dict(line1="45678 Central Park Avenue Northwest", line2="Penthouse 12, Executive Towers, Suite 900", city="Oklahoma City", state="OK", zip="73102", country="USA"),
    dict(line1="89012 Eastern Shoreline Drive", line2="Mansion 3, Historic Coastal Estates", city="Newport", state="RI", zip="02840", country="USA"),
    dict(line1="56789 Downtown Plaza Boulevard", line2="Loft 45, Urban Center, Skyline View", city="Phoenix", state="AZ", zip="85003", country="USA"),
    dict(line1="123456 Golden Gate Parkway West", line2="Apartment 78C, Bridgeview Condominiums", city="San Francisco", state="CA", zip="94102", country="USA"),
    dict(line1="678901 Southern Hills Lane", line2="Farmhouse 5, Rolling Acres Subdivision", city="Lexington", state="KY", zip="40502", country="USA"),
    # Null addresses
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
]

DOMAIN_LIST = [
    "gmail.com",
    "yahoo.com",
    "hotmail.com",
    "outlook.com",
    "aol.com",
    "icloud.com",
    "protonmail.com",
    "zoho.com",
    "mail.com",
    "gmx.com",
    "yandex.com",
    "mail.ru",
    "inbox.com",
    "fastmail.com",
    "tutanota.com",
    "riseup.net",
]

TIMEZONE_LIST = [
    "GMT +0"
    "GMT +1",
    "GMT +2",
    "GMT +3",
    "GMT +4",
    "GMT +5",
    "GMT +6",
    "GMT +7",
    "GMT +8",
    "GMT +9",
    "GMT +10",
    "GMT +11",
    "GMT +12",
    "GMT -1",
    "GMT -2",
    "GMT -3",
    "GMT -4",
    "GMT -5",
    "GMT -6",
    "GMT -7",
    "GMT -8",
    "GMT -9",
    "GMT -10",
    "GMT -11",
    "GMT -12",
]

DEBT_NAME_LIST = [
    "Credit Card",
    "Car Loan",
    "Mortgage",
    "Student Loan",
    "Personal Loan",
    "Medical Debt",
    "Child Support",
    "Tax Debt",
    "Court Judgment",
    "Debt Consolidation Loan",
    "Home Equity Loan",
    "Business Loan",
    "Personal Guarantee",
    "Secured Loan",
    "Unsecured Loan",
    "Title Loan",
]

NUMBER_ADVISORS_FOR_IDENTITY = [1,1,1,2,2]
NUMBER_OF_CLIENTS_PER_ADVISOR = [8,9,10,9,8,9,10,14,15,20,25,30,10,8,9]
NUMBER_OF_DEBTS_PER_CLIENT = [5,6,7,8,9,10, 10, 10, 11, 12, 20, 18]

class Identity:
    @classmethod
    def list(cls):
        cursor = DB.cursor()  # Use dictionary cursor to get column names
        cursor.execute("""
            SELECT 
                identity_uuid, 
                identity_type, 
                name, 
                email, 
                note, 
                avatar_uri, 
                active,
                create_ts
            FROM 
                auth_fence.identity
            ORDER BY 
                name
        """)

        identities = []
        for row in cursor.fetchall():
            identities.append(Identity(row))

        return identities
        
    def __init__(self, tuple):
        self.uuid = tuple[0]
        self.type = tuple[1]
        self.name = tuple[2]
        self.email = tuple[3]
        self.note = tuple[4]
        self.avatar_uri = tuple[5]
        self.active = tuple[6]
        self.create_ts = tuple[7]



AGENCY_LIST = []
class Agency:
    def __init__(self, *, seed):
        self.uuid = generate_uuidv7(seed)
        self.esid = generate_agency_esid(seed)
        self.create_ts = generate_timestamp(seed)
        self.name = pick_one_of(seed, AGENCY_NAME_LIST)
        address = pick_one_of(seed, ADDRESS_LIST)
        self.line1 = address['line1']
        self.line2 = address['line2']
        self.city = address['city']
        self.state = address['state']
        self.zip = address['zip']
        self.active = active_or_not(seed)
        self.admin_note = generate_note(seed)
        self.data = {}

        # insert into the AGENCY_LIST
        AGENCY_LIST.append(self)

        # Insert into the database
        CURSOR.execute("""
            INSERT INTO df4l.agency (
                agency_uuid,
                agency_esid,
                create_ts,
                name,
                active,
                admin_note,
                data
            ) VALUES (
                %(agency_uuid)s,
                %(agency_esid)s,
                %(create_ts)s,
                %(name)s,
                %(active)s,
                %(admin_note)s,
                %(data)s::jsonb
            )
        """, {
            'agency_uuid': self.uuid,
            'agency_esid': self.esid,
            'create_ts': self.create_ts,
            'name': self.name,
            'active': self.active,
            'admin_note': self.admin_note,
            'data': json.dumps(self.data)
        })
        DB.commit()
    
    def __repr__(self):
        return f"Name: {self.name} uuid: {self.uuid} esid: {self.esid} create_ts: {self.create_ts} address: {self.line1} {self.line2} {self.city} {self.state} {self.zip} active: {self.active} admin_note: {self.admin_note} data: {self.data}"

ADVISOR_LIST = []
class Advisor:
    def __init__(self, *, seed, agency_uuid, identity_uuid):
        self.uuid = generate_uuidv7(seed)
        self.identity_uuid = identity_uuid
        self.agency_uuid = agency_uuid
        self.create_ts = generate_timestamp(seed)
        self.esid = generate_advisor_esid(seed)
        self.name = pick_one_of(seed, NAME_LIST)
        self.email = generate_email(seed, self.name['first_name'].lower())
        self.phone = generate_phone(seed)
        self.address = pick_one_of(seed, ADDRESS_LIST)
        self.active = active_or_not(seed)
        self.admin_note = generate_note(seed)
        self.data = {}

        # insert into the ADVISOR_LIST
        ADVISOR_LIST.append(self)

        # Insert into the database
        CURSOR.execute("""
            INSERT INTO df4l.advisor (
                advisor_uuid,
                agency_uuid,
                identity_uuid,
                create_ts,
                advisor_esid,
                first_name,
                last_name,
                email,
                phone,
                address1,
                address2,
                city,
                state,
                zip,
                active,
                admin_note,
                data
            ) VALUES (
                %(advisor_uuid)s,
                %(agency_uuid)s,
                %(identity_uuid)s,
                %(create_ts)s,
                %(advisor_esid)s,
                %(first_name)s,
                %(last_name)s,
                %(email)s,
                %(phone)s,
                %(address1)s,
                %(address2)s,
                %(city)s,
                %(state)s,
                %(zip)s,
                %(active)s,
                %(admin_note)s,
                %(data)s::jsonb
            )
        """, {
            'advisor_uuid': self.uuid,
            'agency_uuid': self.agency_uuid,
            'identity_uuid': self.identity_uuid,
            'create_ts': self.create_ts,
            'advisor_esid': self.esid,
            'first_name': self.name['first_name'],
            'last_name': self.name['last_name'],
            'email': self.email,
            'phone': self.phone,
            'address1': self.address['line1'],
            'address2': self.address['line2'],
            'city': self.address['city'],
            'state': self.address['state'],
            'zip': self.address['zip'],
            'active': self.active,
            'admin_note': self.admin_note,
            'data': json.dumps(self.data)
        })
        DB.commit()

        # Generate Client0 records (no state collection for licensure)
        for i in range(pick_one_of(seed, NUMBER_OF_CLIENTS_PER_ADVISOR)):
            Client0(advisor_uuid=self.uuid, seed=f'{seed}-(0){i}')

        # Collect client states during Client generation (for licensure)
        client_states = set()

        # Generate Client records and collect their states
        num_client = pick_one_of(seed, NUMBER_OF_CLIENTS_PER_ADVISOR)
        for i in range(num_client):
            client_seed = f'{seed}-(1){i}'
            # Determine the state this client will have
            client_address = pick_one_of(client_seed, ADDRESS_LIST)
            #if client_address['state'] in valid_state_codes and state is not New York + Oregon + California
            if client_address['state'] and client_address['state'] not in ['NY', 'OR', 'CA']:
                client_states.add(client_address['state'])
            # Create the actual client
            Client(advisor_uuid=self.uuid, seed=client_seed)

        # Generate state licensure records based on Client states only
        if client_states:
            licensed_states = create_advisor_state_licensure(self.uuid, client_states, seed)
            print(f"Created licensure for advisor {self.name['first_name']} {self.name['last_name']} in states: {sorted(licensed_states)}")
        else:
            print(f"No client states found for advisor {self.name['first_name']} {self.name['last_name']}")
        
    def __repr__(self):
        return f'Advisor {self.name} ({self.email})'


class Client0:
    def __init__(self, *, advisor_uuid, seed):
        self.uuid = generate_uuidv7(seed)
        self.advisor_uuid = advisor_uuid
        self.create_ts = generate_timestamp(seed)
        self.first_name = pick_one_of(seed, FIRST_NAME_LIST)
        self.last_name = pick_one_of(seed, LAST_NAME_LIST)
        self.email = generate_email(seed, self.first_name.lower())
        self.phone = generate_phone(seed)
        self.phone2 = generate_phone(seed)
        self.address = pick_one_of(seed, ADDRESS_LIST)
        self.timezone = pick_one_of(seed, TIMEZONE_LIST)
        self.monthly_budget = generate_monthly_budget(seed)
        #TODO: self.file_znid_doc_to_append = None
        self.active = active_or_not(seed)
        self.note = generate_note(seed)
        self.annual_insurance_premium = generate_insurance_premium(seed)
        self.annual_insurance_pua = generate_insurance_pua(seed)
        self.net_cash_at_end = generate_net_cash_at_end(seed)
        self.debt_free_start_date = generate_date(seed)
        self.debt_free_active = active_or_not(seed)
        #TODO: self.debt_free_extra_pua_list = []
        self.data = {}

        # Insert into the database
        CURSOR.execute("""
            INSERT INTO df4l.client0 (
                client_uuid,
                advisor_uuid,
                create_ts,
                first_name,
                last_name,
                email,
                phone,
                phone2,
                address1,
                address2,
                city,
                state,
                zip,
                time_zone,
                monthly_budget,
                active,
                note,
                annual_insurance_premium,
                annual_insurance_pua,
                net_cash_at_end,
                debt_free_start_date,
                debt_free_active,
                data
            ) VALUES (
                %(client_uuid)s,
                %(advisor_uuid)s,
                %(create_ts)s,
                %(first_name)s,
                %(last_name)s,
                %(email)s,
                %(phone)s,
                %(phone2)s,
                %(address1)s,
                %(address2)s,
                %(city)s,
                %(state)s,
                %(zip)s,
                %(time_zone)s,
                %(monthly_budget)s,
                %(active)s,
                %(note)s,
                %(annual_insurance_premium)s,
                %(annual_insurance_pua)s,
                %(net_cash_at_end)s,
                %(debt_free_start_date)s,
                %(debt_free_active)s,
                %(data)s::jsonb
            )
        """, {
            'client_uuid': self.uuid,
            'advisor_uuid': self.advisor_uuid,
            'create_ts': self.create_ts,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'email': self.email,
            'phone': self.phone,
            'phone2': self.phone2,
            'address1': self.address['line1'],
            'address2': self.address['line2'],
            'city': self.address['city'],
            'state': self.address['state'],
            'zip': self.address['zip'],
            'time_zone': self.timezone,
            'monthly_budget': self.monthly_budget,
            'active': self.active,
            'note': self.note,
            'annual_insurance_premium': self.annual_insurance_premium,
            'annual_insurance_pua': self.annual_insurance_pua,
            'net_cash_at_end': self.net_cash_at_end,
            'debt_free_start_date': self.debt_free_start_date,
            'debt_free_active': self.debt_free_active,
            'data': json.dumps(self.data)
        })
        DB.commit()

        # Generate debts for this client
        for i in range(pick_one_of(seed, NUMBER_OF_DEBTS_PER_CLIENT)):
            Client0Debt(client_uuid=self.uuid, seed=f'{seed}-{i}')

"""
CREATE TABLE df4l.client (
    client_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    advisor_uuid uuid NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    first_name varchar(64) NOT NULL,
    last_name varchar(64) NOT NULL,
    email varchar(255),
    phone varchar(20),
    address1 varchar(128),
    address2 varchar(128),
    city varchar(32),
    state varchar(16),
    zip varchar(16),
    country varchar(32),
    active bool NOT NULL DEFAULT true,
    time_zone varchar(32),
    gender varchar(8),
    birth_date date,
    note varchar(4096),
    CONSTRAINT "client_pkey" PRIMARY KEY ("client_uuid"),
    CONSTRAINT fk_advisor FOREIGN KEY (advisor_uuid) REFERENCES df4l.advisor(advisor_uuid) ON UPDATE RESTRICT ON DELETE RESTRICT
);
"""

class Client:
    def __init__(self, *, advisor_uuid, seed):
        self.client_uuid = generate_uuidv7(seed)
        self.advisor_uuid = advisor_uuid
        self.create_ts = generate_timestamp(seed)
        self.first_name = pick_one_of(seed, FIRST_NAME_LIST)
        self.last_name = pick_one_of(seed, LAST_NAME_LIST)
        self.gender = determine_gender(self.first_name)
        self.birth_date = generate_birth_date(seed)
        self.email = generate_email(seed, self.first_name.lower())
        self.phone = generate_phone(seed)
        self.address = pick_one_of(seed, ADDRESS_LIST)
        self.active = active_or_not(seed)
        self.timezone = pick_one_of(seed, TIMEZONE_LIST)
        self.note = generate_note(seed)
        self.client_type = generate_client_type(seed)
        self.budget_pua_split = "Max" if self.client_type == "DebtManagement" else None
        
        # Insert into the database
        CURSOR.execute("""
            INSERT INTO df4l.client (
                client_uuid,
                advisor_uuid,
                create_ts,
                first_name,
                last_name,
                email,
                phone,
                address1,
                address2,
                city,
                state,
                zip,
                active,
                time_zone,
                gender,
                birth_date,
                note,
                client_type,
                budget_pua_split
            ) VALUES (
                %(client_uuid)s,
                %(advisor_uuid)s,
                %(create_ts)s,
                %(first_name)s,
                %(last_name)s,
                %(email)s,
                %(phone)s,
                %(address1)s,
                %(address2)s,
                %(city)s,
                %(state)s,
                %(zip)s,
                %(active)s,
                %(time_zone)s,
                %(gender)s,
                %(birth_date)s,
                %(note)s,
                %(client_type)s,
                %(budget_pua_split)s
            )
        """, {
            'client_uuid': self.client_uuid,
            'advisor_uuid': self.advisor_uuid,
            'create_ts': self.create_ts,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'email': self.email,
            'phone': self.phone,
            'address1': self.address['line1'],
            'address2': self.address['line2'],
            'city': self.address['city'],
            'state': self.address['state'],
            'zip': self.address['zip'],
            'active': self.active,
            'time_zone': self.timezone,
            'gender': self.gender,
            'birth_date': self.birth_date,
            'note': self.note,
            'client_type': self.client_type,
            'budget_pua_split': self.budget_pua_split
        })
        DB.commit()

        # Generate client_crs records based on deterministic distribution
        self._generate_client_crs_records(seed)

        # Generate debts for this client
        for i in range(pick_one_of(seed, NUMBER_OF_DEBTS_PER_CLIENT)):
            ClientDebt(client_uuid=self.client_uuid, seed=f'{seed}-debt-{i}')

    def _generate_client_crs_records(self, seed):
        """Generate client_crs records with deterministic distribution:
        70% - exactly one record (single applicant)
        25% - exactly two records (applicant + spouse pair)
        5% - no records
        """
        hash_val = hashlib.sha256(seed.encode()).hexdigest()
        distribution_value = int(hash_val[:2], 16) % 100
        
        if distribution_value < 5:
            # 5% - No client_crs records
            return
        elif distribution_value < 30:
            # 25% - Two records (applicant + spouse pair)
            self._create_spouse_pair(seed)
        else:
            # 70% - Single applicant record
            self._create_single_applicant(seed)
    
    def _create_single_applicant(self, seed):
        """Create a single client_crs record for the applicant"""
        ClientCrs(
            client_uuid=self.client_uuid,
            client_person_type='Applicant',
            first_name=self.first_name,
            last_name=self.last_name,
            email=self.email,
            phone=self.phone,
            gender=self.gender,
            birth_date=self.birth_date,
            address=self.address,
            seed=f'{seed}-crs-applicant'
        )
    
    def _create_spouse_pair(self, seed):
        """Create two client_crs records for applicant + spouse pair"""
        # Create applicant record
        ClientCrs(
            client_uuid=self.client_uuid,
            client_person_type='Applicant',
            first_name=self.first_name,
            last_name=self.last_name,
            email=self.email,
            phone=self.phone,
            gender=self.gender,
            birth_date=self.birth_date,
            address=self.address,
            seed=f'{seed}-crs-applicant'
        )
        
        # Generate spouse with same last name and address
        spouse_first_name = self._generate_spouse_first_name(seed, self.gender)
        spouse_gender = "Female" if self.gender == "Male" else "Male"
        
        ClientCrs(
            client_uuid=self.client_uuid,
            client_person_type='Spouse',
            first_name=spouse_first_name,
            last_name=self.last_name,  # Same last name
            email=generate_email(f'{seed}-spouse', spouse_first_name.lower()),
            phone=generate_phone(f'{seed}-spouse'),
            gender=spouse_gender,
            birth_date=generate_birth_date(f'{seed}-spouse'),
            address=self.address,  # Same address
            seed=f'{seed}-crs-spouse'
        )
    
    def _generate_spouse_first_name(self, seed, primary_gender):
        """Generate a spouse first name of opposite gender"""
        # Filter names by gender for spouse
        if primary_gender == "Male":
            # Primary is male, spouse should be female
            female_names = [
                "Alexandria", "Evangelina", "Maria", "Sarah", "María-José", "Zoë",
                "Yuki", "Priya", "Olga", "Mary Jane", "Anna Marie", "Hon. Elizabeth",
                "Madonna", "Cher"
            ]
            return pick_one_of(f'{seed}-spouse-name', female_names)
        else:
            # Primary is female, spouse should be male
            male_names = [
                "Al", "Bo", "Ty", "Ed", "Mo", "Christopher", "Maximilian", "Sebastian",
                "James", "David", "Michael", "Jean-Pierre", "André", "François",
                "Wei", "Jung-Ho", "John Paul", "Jean Claude", "Billy Bob",
                "Dr. Robert", "Rev. Michael", "Prof. William", "Capt. James",
                "Prince", "Bono", "Seal"
            ]
            return pick_one_of(f'{seed}-spouse-name', male_names)
    

class ClientCrs:
    def __init__(self, *, client_uuid, client_person_type, first_name, last_name, email, phone, gender, birth_date, address, seed):
        self.client_crs_uuid = generate_uuidv7(seed)
        self.client_uuid = client_uuid
        self.client_person_type = client_person_type
        self.create_ts = generate_timestamp(seed)
        self.first_name = first_name
        self.last_name = last_name
        self.email = email
        self.phone = phone
        self.gender = gender
        self.birth_date = birth_date
        self.address = address
        
        # Generate additional CRS-specific fields
        self.ssn = self._generate_ssn(seed)
        self.crs_user_uuid = self._generate_crs_user_uuid(seed)
        self.crs_user_verified_ts = self._generate_crs_user_verified_ts(seed)
        self.crs_latest_report_esid = self._generate_report_esid(seed)
        self.crs_latest_report_date = generate_date(f'{seed}-report')
        
        # Insert into the database
        #CURSOR.execute("""
        #    INSERT INTO df4l.client_crs (
        #        client_crs_uuid,
        #        client_uuid,
        #        client_person_type,
        #        create_ts,
        #        first_name,
        #        last_name,
        #        email,
        #        phone,
        #        ssn,
        #        birth_date,
        #        gender,
        #        address1,
        #        address2,
        #        city,
        #        state,
        #        zip,
        #        crs_user_uuid,
        #        crs_user_verified_ts,
        #        crs_latest_report_esid,
        #        crs_latest_report_date
        #    ) VALUES (
        #        %(client_crs_uuid)s,
        #        %(client_uuid)s,
        #        %(client_person_type)s,
        #        %(create_ts)s,
        #        %(first_name)s,
        #        %(last_name)s,
        #        %(email)s,
        #        %(phone)s,
        #        %(ssn)s,
        #        %(birth_date)s,
        #        %(gender)s,
        #        %(address1)s,
        #        %(address2)s,
        #        %(city)s,
        #        %(state)s,
        #        %(zip)s,
        #        %(crs_user_uuid)s,
        #        %(crs_user_verified_ts)s,
        #        %(crs_latest_report_esid)s,
        #        %(crs_latest_report_date)s
        #    )
        #""", {
        #    'client_crs_uuid': self.client_crs_uuid,
        #    'client_uuid': self.client_uuid,
        #    'client_person_type': self.client_person_type,
        #    'create_ts': self.create_ts,
        #    'first_name': self.first_name,
        #    'last_name': self.last_name,
        #    'email': self.email,
        #    'phone': self.phone,
        #    'ssn': self.ssn,
        #    'birth_date': self.birth_date,
        #    'gender': self.gender,
        #    'address1': self.address['line1'],
        #    'address2': self.address['line2'],
        #    'city': self.address['city'],
        #    'state': self.address['state'],
        #    'zip': self.address['zip'],
        #    'crs_user_uuid': self.crs_user_uuid,
        #    'crs_user_verified_ts': self.crs_user_verified_ts,
        #    'crs_latest_report_esid': self.crs_latest_report_esid,
        #    'crs_latest_report_date': self.crs_latest_report_date
        #})
        DB.commit()
    
    def _generate_ssn(self, seed):
        """Generate a fake SSN for testing purposes"""
        hash_val = hashlib.sha256(seed.encode()).hexdigest()
        # Generate in XXX-XX-XXXX format, avoiding real SSN patterns
        area = str(int(hash_val[:3], 16) % 900 + 100)  # 100-999
        group = str(int(hash_val[3:5], 16) % 99 + 1).zfill(2)  # 01-99
        serial = str(int(hash_val[5:9], 16) % 9999 + 1).zfill(4)  # 0001-9999
        return f"{area}-{group}-{serial}"
    
    def _generate_crs_user_uuid(self, seed):
        """Generate CRS user ESID"""
        hash_val = hashlib.sha256(seed.encode()).hexdigest()
        return f"usr-{hash_val[:8]}-{hash_val[8:16]}"
    
    def _generate_crs_user_verified_ts(self, seed):
        """Generate user verification timestamp (80% verified, 20% None)"""
        hash_val = hashlib.sha256(seed.encode()).hexdigest()
        if int(hash_val[:1], 16) % 5 == 0:  # 20% chance
            return None
        return generate_timestamp(f'{seed}-verified')
    
    def _generate_report_esid(self, seed):
        """Generate CRS report ESID (90% have reports, 10% None)"""
        hash_val = hashlib.sha256(seed.encode()).hexdigest()
        if int(hash_val[:1], 16) % 10 == 0:  # 10% chance
            return None
        return f"rpt-{hash_val[:12]}-{hash_val[12:24]}"


CLIENT_DEBT_LIST = []
class Client0Debt:
    def __init__(self, *, client_uuid, seed):
        self.uuid = generate_uuidv7(seed)
        self.client_uuid = client_uuid
        self.create_ts = generate_timestamp(seed)
        self.name = pick_one_of(seed, DEBT_NAME_LIST)
        self.balance = generate_balance(seed)
        self.balance_date = generate_date(seed)
        self.interest_rate = generate_interest_rate()
        self.monthly_payment = generate_monthly_payment(self.balance, seed)
        self.active = active_or_not(seed)
        self.note = generate_note(seed)
        self.data = {}

        # Insert into the database
        CURSOR.execute("""
            INSERT INTO df4l.client0_debt (
                client_debt_uuid,
                client_uuid,
                create_ts,
                name,
                balance,
                balance_date,
                interest_rate,
                monthly_payment,
                active,
                note,
                data
            ) VALUES (
                %(client_debt_uuid)s,
                %(client_uuid)s,
                %(create_ts)s,
                %(name)s,
                %(balance)s,
                %(balance_date)s,
                %(interest_rate)s,
                %(monthly_payment)s,
                %(active)s,
                %(note)s,
                %(data)s::jsonb
            )
        """, {
            'client_debt_uuid': self.uuid,
            'client_uuid': self.client_uuid,
            'create_ts': self.create_ts,
            'name': self.name,
            'balance': self.balance,
            'balance_date': self.balance_date,
            'interest_rate': self.interest_rate,
            'monthly_payment': self.monthly_payment,
            'active': self.active,
            'note': self.note,
            'data': json.dumps(self.data)
        })
        DB.commit()

    

CLIENT_NOTE_LIST = []
class Client0Note:
    def __init__(self, *, client_uuid, seed):
        self.uuid = generate_uuidv7(seed)
        self.client_uuid = client_uuid
        self.create_ts = generate_timestamp(seed)
        self.note = generate_client_note(seed)
        self.data = {}

    def __repr__(self):
        return f"""CLIENT NOTE OBJECT:
        uuid: {self.uuid} 
        client_uuid: {self.client_uuid} 
        create_ts: {self.create_ts} 
        note: {self.note}
        data: {self.data}
        """

'''
#Generate 10 agencies
for i in range(10):
    AGENCY_LIST.append(Agency(seed=SEED + str(i)))

#Generate 100 advisors
#for i in range(100):
#    if pick_one_of(SEED + str(i), ADVISOR_PART_OF_AGENCY):
#        agency_uuid = pick_one_of(SEED + str(i), AGENCY_LIST).uuid
#    else:
#        agency_uuid = None
#
#    ADVISOR_LIST.append(Advisor(agency_uuid=agency_uuid, seed=SEED + str(i)))

#Generate 100 clients
for i in range(100):
    advisor_uuid = pick_one_of(SEED + str(i), ADVISOR_LIST).uuid
    CLIENT_LIST.append(Client(advisor_uuid=advisor_uuid, seed=SEED + str(i)))

#Generate 3 debts for each client
for client in CLIENT_LIST:
    for i in range(3):
        CLIENT_DEBT_LIST.append(ClientDebt(client_uuid=client.uuid, seed=SEED + str(client.uuid) + str(i)))

#Generate 25 client notes
for i in range(25):
    client_uuid = pick_one_of(SEED + str(i), CLIENT_LIST).uuid
    CLIENT_NOTE_LIST.append(ClientNote(client_uuid=client.uuid, seed=SEED + str(client_uuid) + str(i)))

'''

##########################################################################################################################


class ClientDebt:
    def __init__(self, *, client_uuid, seed):
        self.client_debt_uuid = generate_uuidv7(seed)
        self.client_uuid = client_uuid
        self.create_ts = generate_timestamp(seed)
        self.active = active_or_not(seed)

        # Determine if this is a CRS debt (85%) or manual debt (15%)
        if is_crs_debt(seed):
            return
        else:
            # Manual debt - no debt_esid, can have manual fields
            self.client_debt_esid = None
            self.name = pick_one_of(seed, DEBT_NAME_LIST)
            self.balance = generate_balance(seed) / 100.0  # More reasonable balance
            self.balance_date = generate_date(seed)
            self.annual_interest_percentage = generate_interest_rate()
            self.monthly_payment_amount = generate_monthly_payment(self.balance, seed)
            self.note = generate_note(seed)

        # Insert into the database
        CURSOR.execute("""
            INSERT INTO df4l.client_debt (
                client_debt_uuid,
                client_debt_esid,
                client_uuid,
                active,
                create_ts,
                name,
                balance,
                balance_date,
                annual_interest_percentage,
                monthly_payment_amount,
                note
            ) VALUES (
                %(client_debt_uuid)s,
                %(client_debt_esid)s,
                %(client_uuid)s,
                %(active)s,
                %(create_ts)s,
                %(name)s,
                %(balance)s,
                %(balance_date)s,
                %(annual_interest_percentage)s,
                %(monthly_payment_amount)s,
                %(note)s
            )
        """, {
            'client_debt_uuid': self.client_debt_uuid,
            'client_debt_esid': self.client_debt_esid,
            'client_uuid': self.client_uuid,
            'active': self.active,
            'create_ts': self.create_ts,
            'name': self.name,
            'balance': self.balance,
            'balance_date': self.balance_date,
            'annual_interest_percentage': self.annual_interest_percentage,
            'monthly_payment_amount': self.monthly_payment_amount,
            'note': self.note
        })
        DB.commit()




##########################################################################################################################

def get_identities():

    """Retrieve all identities from auth_fence.identity table"""
    cursor = DB.cursor()  # Use dictionary cursor to get column names
    cursor.execute("""
        SELECT 
            identity_uuid, 
            identity_type, 
            name, 
            email, 
            note, 
            avatar_uri, 
            active,
            create_ts
        FROM 
            auth_fence.identity
        ORDER BY 
            name
    """)
    
    identities = cursor.fetchall()
    cursor.close()
    
    return identities
    
    
def generate_advisors():
    # Clear existing advisor list
    ADVISOR_LIST.clear()

    #generate 3 advisors for each identity
    for identity in identities:
        for i in range(3):
            # Create a unique seed for this identity
            identity_seed = f"{SEED}-identity-{identity['identity_uuid']}-{i}"
            
            # Determine if advisor belongs to an agency
            if pick_one_of(identity_seed, ADVISOR_PART_OF_AGENCY):
                agency_uuid = pick_one_of(identity_seed, AGENCY_LIST).uuid
            else:
                agency_uuid = None
            
            # Create advisor
            advisor = Advisor(agency_uuid=agency_uuid, seed=identity_seed)
            
            # Override email with identity email
            advisor.email = identity['email']
            
            # Store identity_uuid in advisor data
            advisor.data['identity_uuid'] = str(identity['identity_uuid'])
            
            # Add to advisor list
            ADVISOR_LIST.append(advisor)
            print(f"Generated advisor for identity: {identity['name']} ({identity['email']})")



identities = get_identities()


##########################################################################################################################

def transfer_agencies(target_db):
    AgencyMap = {} #map between agency_znid and agency_uuid 
    try:
        # Insert into target database
        cursor = target_db.cursor()
        for agency in AGENCY_LIST:
            print(f"Processing agency: {agency.name}")
            try:
                # Convert data to JSON string if it's a dict

                cursor.execute("""
                    INSERT INTO df4l.agency (
                        agency_uuid,
                        agency_esid,
                        create_ts,
                        name,
                        active,
                        admin_note,
                        data
                    ) VALUES (
                        %(agency_uuid)s,
                        %(agency_esid)s,
                        %(create_ts)s,
                        %(name)s,
                        %(active)s,
                        %(admin_note)s,
                        %(data)s::jsonb
                    )
                """, {
                    'agency_uuid': agency.uuid,
                    'agency_esid': agency.esid,
                    'create_ts': agency.create_ts,
                    'name': agency.name,
                    'active': agency.active,
                    'admin_note': agency.admin_note,
                    'data': json.dumps(agency.data)
                })
                target_db.commit()
            except Exception as e:
                target_db.rollback()
                print(f"Error processing agency {agency['name']}: {e}")
                print(f"Data type: {type(agency['data'])}")
                print(f"Data content: {agency['data']}")
                print("##############################################################################################")
                continue
        
        cursor.close()
        print("Transfer completed successfully")
        print("")
        print("")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during transfer: {e}")
        print("##############################################################################################")
        raise
    return AgencyMap

def transfer_advisors(target_db, AgencyMap):
    try:
        # Insert into target database
        cursor = target_db.cursor()
        for advisor in ADVISOR_LIST:
            advisor_name = f"{advisor.name['first_name']} {advisor.name['last_name']}"
            print(f"Processing advisor: {advisor_name}")
            try:
                # Handle None agency_uuid properly
                agency_uuid = None
                if advisor.agency_uuid is not None:
                    agency_uuid = AgencyMap.get(advisor.agency_uuid)
                                
                cursor.execute("""
                    INSERT INTO df4l.advisor (
                        advisor_uuid,
                        agency_uuid,
                        create_ts,
                        advisor_esid,
                        first_name,
                        last_name,
                        email,
                        phone,
                        address1,
                        address2,
                        city,
                        zip,
                        active,
                        admin_note,
                        data
                    ) VALUES (
                        %(advisor_uuid)s,
                        %(agency_uuid)s,
                        %(create_ts)s,
                        %(advisor_esid)s,
                        %(first_name)s,
                        %(last_name)s,
                        %(email)s,
                        %(phone)s,
                        %(address1)s,
                        %(address2)s,
                        %(city)s,
                        %(zip)s,
                        %(active)s,
                        %(admin_note)s,
                        %(data)s::jsonb
                    )
                """, {
                    'advisor_uuid': advisor.uuid,
                    'agency_uuid': agency_uuid,
                    'create_ts': advisor.create_ts,
                    'advisor_esid': advisor.esid,
                    'first_name': advisor.name['first_name'],
                    'last_name': advisor.name['last_name'],
                    'email': advisor.email,
                    'phone': advisor.phone,
                    'address1': advisor.address['line1'],
                    'address2': advisor.address['line2'],
                    'city': advisor.address['city'],
                    'zip': advisor.address['zip'],
                    'active': advisor.active,
                    'admin_note': advisor.admin_note,
                    'data': json.dumps(advisor.data)
                })
                

                target_db.commit()
                print(f"Successfully inserted advisor: {advisor_name}")
            except Exception as e:
                target_db.rollback()
                print(f"Error processing advisor {advisor_name}: {e}")
                print("##############################################################################################")
                continue

        cursor.close()
        print("Advisor transfer completed successfully")
        print("")
        print("")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during advisor transfer: {e}")
        print("##############################################################################################")
        raise
    
    return

def transfer_clients(target_db):
    try:
        # Insert into target database
        cursor = target_db.cursor()
        for client in CLIENT_LIST:
            client_name = f"{client.name['first_name']} {client.name['last_name']}"
            print(f"Processing client: {client_name}")
            try:

                cursor.execute("""
                    INSERT INTO df4l.client0 (
                        client_uuid,
                        advisor_uuid,
                        create_ts,
                        first_name,
                        last_name,
                        email,
                        phone,
                        phone2,
                        address1,
                        address2,
                        city,
                        state,
                        zip,
                        time_zone,
                        monthly_budget,

                        active,
                        note,
                        annual_insurance_premium,
                        annual_insurance_pua,
                        net_cash_at_end,
                        debt_free_start_date,
                        debt_free_active,

                        data
                    ) VALUES (
                        %(client_uuid)s,
                        %(advisor_uuid)s,
                        %(create_ts)s,
                        %(first_name)s,
                        %(last_name)s,
                        %(email)s,
                        %(phone)s,
                        %(phone2)s,
                        %(address1)s,
                        %(address2)s,
                        %(city)s,
                        %(state)s,
                        %(zip)s,
                        %(time_zone)s,
                        %(monthly_budget)s,

                        %(active)s,
                        %(note)s,
                        %(annual_insurance_premium)s,
                        %(annual_insurance_pua)s,
                        %(net_cash_at_end)s,
                        %(debt_free_start_date)s,
                        %(debt_free_active)s,

                        %(data)s::jsonb
                    )
                """, {
                    'client_uuid': client.uuid,
                    'advisor_uuid': client.advisor_uuid,
                    'create_ts': client.create_ts,
                    'first_name': client.name['first_name'],
                    'last_name': client.name['last_name'],
                    'email': client.email,
                    'phone': client.phone,
                    'phone2': client.phone2,
                    'address1': client.address['line1'],
                    'address2': client.address['line2'],
                    'city': client.address['city'],
                    'state': client.address['state'],
                    'zip': client.address['zip'],
                    'time_zone': client.timezone,
                    'monthly_budget': client.monthly_budget,
                    #'file_znid_doc_to_append': client.file_znid_doc_to_append,
                    'active': client.active,
                    'note': client.note,
                    'annual_insurance_premium': client.annual_insurance_premium,
                    'annual_insurance_pua': client.annual_insurance_pua,
                    'net_cash_at_end': client.net_cash_at_end,
                    'debt_free_start_date': client.debt_free_start_date,
                    'debt_free_active': client.debt_free_active,
                    #'debt_free_extra_pua_list': json.dumps(client.debt_free_extra_pua_list),
                    'data': json.dumps(client.data)
                })
                

                target_db.commit()
                print(f"Successfully inserted client: {client_name}")
            except Exception as e:
                target_db.rollback()
                print(f"Error processing client {client_name}: {e}")
                print("##############################################################################################")
                continue

        cursor.close()
        print("Client transfer completed successfully")
        print("")
        print("")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during client transfer: {e}")
        print("##############################################################################################")
        raise

    return

def transfer_client_debts(target_db):
    try:
        # Insert into target database
        cursor = target_db.cursor()
        for client_debt in CLIENT_DEBT_LIST:
            print(f"Processing client debt: {client_debt.name}")
            try:

                cursor.execute("""
                    INSERT INTO df4l.client0_debt (
                        client_debt_uuid,
                        client_uuid,
                        create_ts,
                        name,
                        balance,
                        balance_date,
                        interest_rate,
                        monthly_payment,
                        active,
                        note,
                        data
                    ) VALUES (
                        %(client_debt_uuid)s,
                        %(client_uuid)s,
                        %(create_ts)s,
                        %(name)s,
                        %(balance)s,
                        %(balance_date)s,
                        %(interest_rate)s,
                        %(monthly_payment)s,
                        %(active)s,
                        %(note)s,
                        %(data)s::jsonb
                    )
                """, {
                    'client_debt_uuid': client_debt.uuid,
                    'client_uuid': client_debt.client_uuid,
                    'create_ts': client_debt.create_ts,
                    'name': client_debt.name,
                    'balance': client_debt.balance,
                    'balance_date': client_debt.balance_date,
                    'interest_rate': client_debt.interest_rate,
                    'monthly_payment': client_debt.monthly_payment,
                    'active': client_debt.active,
                    'note': client_debt.note,
                    'data': json.dumps(client_debt.data)
                })

                target_db.commit()
                print(f"Successfully inserted client debt: {client_debt.name}")
            except Exception as e:
                target_db.rollback()
                print(f"Error processing client debt {client_debt.name}: {e}")
                print("##############################################################################################")
                continue

        cursor.close()
        print("Client debt transfer completed successfully")
        print("")
        print("")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during client debt transfer: {e}")
        print("##############################################################################################")
        raise
    
    return

def transfer_client_notes(target_db):
    try:
        # Insert into target database
        cursor = target_db.cursor()
        for client_note in CLIENT_NOTE_LIST:
            print(f"Processing client note: {client_note.note}")
            try:

                cursor.execute("""
                    INSERT INTO df4l.client0_note (
                        client_note_uuid,
                        client_uuid,
                        create_ts,
                        note,
                        data
                    ) VALUES (
                        %(client_note_uuid)s,
                        %(client_uuid)s,
                        %(create_ts)s,
                        %(note)s,
                        %(data)s::jsonb
                    )
                """, {
                    'client_note_uuid': client_note.uuid,
                    'client_uuid': client_note.client_uuid,
                    'create_ts': client_note.create_ts,
                    'note': client_note.note,
                    'data': json.dumps(client_note.data)
                })

                target_db.commit()
                print(f"Successfully inserted client note: {client_note.note}")
            except Exception as e:
                target_db.rollback()
                print(f"Error processing client note {client_note.note}: {e}")
                print("##############################################################################################")
                continue

        cursor.close()
        print("Client note transfer completed successfully")
        print("")
        print("")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during client note transfer: {e}")
        print("##############################################################################################")
        raise
    
    return
def generate_password_hash(password, salt):
    """Generate PBKDF2 SHA256 hash for password"""
    import hashlib
    # Use PBKDF2 with SHA256, 100000 iterations (standard for auth systems)
    key = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
    return base64.b64encode(key).decode('utf-8')

def generate_random_password(seed):
    """Generate a random password using 4 simple English words with spaces"""
    simple_words = [
        "apple", "beach", "chair", "dance", "eagle", "flame", "grape", "house", "seven", "juice",
        "kiten", "lemon", "mouse", "night", "ocean", "piano", "queen", "river", "stone", "table",
        "under", "voice", "water", "zebra", "bread", "cloud", "dream", "earth", "field", "green",
        "happy", "light", "magic", "peace", "quick", "smile", "trust", "unity", "world", "young",
        "brave", "clear", "fresh", "giant", "heart", "laugh", "music", "power", "quiet", "sweet",
        "tiger", "urban", "value", "white", "extra", "youth", "zesty", "amber", "bloom", "craft",
        "drive", "enjoy", "focus", "grace", "honor", "ideal", "jolly", "known", "loyal", "merry",
        "noble", "order", "pride", "quest", "royal", "solid", "truth", "ultra", "vital", "worth"
    ]
    
    # Use the seed to deterministically select 4 words
    hash_val = hashlib.sha256(seed.encode()).hexdigest()
    words = []
    for i in range(3):
        # Use different parts of the hash for each word
        word_index = int(hash_val[i*8:(i+1)*8], 16) % len(simple_words)
        words.append(simple_words[word_index])
    
    return " ".join(words)

def insert_identity_and_advisor(first_name, last_name, email, identity_uuid, advisor_uuid, password=None):
    """Insert an identity and corresponding advisor with hardcoded UUIDs and login credentials"""
    
    # Generate random password if not provided
    if password is None:
        password = generate_random_password(f"{email}-{identity_uuid}")
    
    # Generate salt for password
    salt = secrets.token_hex(16)
    password_hash = generate_password_hash(password, salt)
    
    # Create full name
    full_name = f"{first_name} {last_name}".strip()
    
    # First, insert the identity into auth_fence.identity
    CURSOR.execute("""
        INSERT INTO auth_fence.identity (
            identity_uuid,
            identity_type,
            name,
            email,
            active,
            create_ts
        ) VALUES (
            %(identity_uuid)s,
            'User',
            %(name)s,
            %(email)s,
            true,
            now()
        )
        ON CONFLICT (identity_uuid) DO UPDATE SET
            name = EXCLUDED.name,
            email = EXCLUDED.email
    """, {
        'identity_uuid': identity_uuid,
        'name': full_name,
        'email': email
    })
    
    # Insert login credentials into auth_fence.login
    CURSOR.execute("""
        INSERT INTO auth_fence.login (
            identity_uuid,
            username,
            password_base64_pbkdf2_sha256_hash,
            password_salt,
            active,
            create_ts
        ) VALUES (
            %(identity_uuid)s,
            %(username)s,
            %(password_hash)s,
            %(salt)s,
            true,
            now()
        )
        ON CONFLICT (identity_uuid) DO UPDATE SET
            username = EXCLUDED.username,
            password_base64_pbkdf2_sha256_hash = EXCLUDED.password_base64_pbkdf2_sha256_hash,
            password_salt = EXCLUDED.password_salt
    """, {
        'identity_uuid': identity_uuid,
        'username': email,  # Use email as username
        'password_hash': password_hash,
        'salt': salt
    })
    
    # Generate advisor ESID from the UUID for consistency using NNN-NNN
    advisor_esid = generate_advisor_esid(f"advisor-{advisor_uuid}")
    
    # Insert the advisor into df4l.advisor
    CURSOR.execute("""
        INSERT INTO df4l.advisor (
            advisor_uuid,
            identity_uuid,
            agency_uuid,
            create_ts,
            advisor_esid,
            first_name,
            last_name,
            email,
            phone,
            address1,
            address2,
            city,
            state,
            zip,
            active,
            admin_note,
            data
        ) VALUES (
            %(advisor_uuid)s,
            %(identity_uuid)s,
            NULL,
            now(),
            %(advisor_esid)s,
            %(first_name)s,
            %(last_name)s,
            %(email)s,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            true,
            'Auto-generated test advisor',
            '{}'::jsonb
        )
    """, {
        'advisor_uuid': advisor_uuid,
        'identity_uuid': identity_uuid,
        'advisor_esid': advisor_esid,
        'first_name': first_name,
        'last_name': last_name,
        'email': email
    })
    
    DB.commit()
    print(f"Inserted identity and advisor for: {full_name} ({email}) with password: {password}")
    
    # Return the account details for summary
    return {
        'name': full_name,
        'email': email,
        'password': password
    }

def insert_stripe_product_and_price(cursor, product_id, product_name, product_description, 
                                   price_id, price_name, price_amount):
    """Insert a Stripe product and its associated price"""
    # Insert product
    cursor.execute("""
        INSERT INTO api_stripe.product (
            product_id,
            name,
            description,
            active,
            metadata
        ) VALUES (
            %(product_id)s,
            %(product_name)s,
            %(product_description)s,
            true,
            '{}'::jsonb
        )
        ON CONFLICT (product_id) DO UPDATE SET
            name = EXCLUDED.name,
            description = EXCLUDED.description,
            active = EXCLUDED.active
        RETURNING api_stripe_product_uuid
    """, {
        'product_id': product_id,
        'product_name': product_name,
        'product_description': product_description
    })
    product_uuid = cursor.fetchone()[0]
    
    # Insert price
    cursor.execute("""
        INSERT INTO api_stripe.price (
            price_id,
            api_stripe_product_uuid,
            name,
            active,
            unit_amount,
            metadata
        ) VALUES (
            %(price_id)s,
            %(product_uuid)s,
            %(price_name)s,
            true,
            %(price_amount)s,
            '{}'::jsonb
        )
        ON CONFLICT (price_id) DO NOTHING
    """, {
        'price_id': price_id,
        'product_uuid': product_uuid,
        'price_name': price_name,
        'price_amount': price_amount
    })
    
    return product_uuid

def main():
    try:
        #print("Connecting to source database...")
        #db1 = get_source_db()
        #print("Connected to source database successfully")
        
        print("Connecting to target database...")
        print("Connected to target database successfully")
        
        # Create a cursor and execute truncate statements
        CURSOR.execute("TRUNCATE TABLE df4l.agency CASCADE")
        CURSOR.execute("TRUNCATE TABLE df4l.advisor CASCADE")
        CURSOR.execute("TRUNCATE TABLE df4l.advisor_statelic CASCADE")
        CURSOR.execute("TRUNCATE TABLE df4l.client CASCADE")
        CURSOR.execute("TRUNCATE TABLE df4l.client_debt CASCADE")
        CURSOR.execute("TRUNCATE TABLE df4l.client0 CASCADE")
        CURSOR.execute("TRUNCATE TABLE df4l.client0_debt CASCADE")
        CURSOR.execute("TRUNCATE TABLE df4l.client0_note CASCADE")

        # Delete test accounts (preserve appcove.com accounts)
        CURSOR.execute("DELETE FROM auth_fence.login WHERE identity_uuid IN (SELECT identity_uuid FROM auth_fence.identity WHERE email NOT LIKE '%appcove.com')")
        CURSOR.execute("DELETE FROM auth_fence.identity WHERE email NOT LIKE '%appcove.com'")
        DB.commit()
        

    except Exception as e:
        print(f"Error during truncate: {e}")
        raise


    # Create a few agencies
    for i in range(pick_one_of(SEED, AGENCY_COUNT)):
        Agency(seed=f'{SEED}-{i}')

    identities = Identity.list()

    for i, identity in enumerate(identities):
        for n in range(pick_one_of(f'{SEED}-{i}', NUMBER_ADVISORS_FOR_IDENTITY)):
            agency_uuid = None
            Advisor(seed=f'{SEED}-{i}-{n}', agency_uuid=agency_uuid, identity_uuid=identity.uuid)
    
    
    # Insert specific identities and advisors with hardcoded UUIDs at the end
    print("\n=== Inserting specific identities and advisors ===")
    
    # Insert signup advisor settings
    print("\n=== Inserting signup advisor settings ===")
    CURSOR.execute("""
        INSERT INTO "df4l"."signup_advisor_setting" (
            "signup_advisor_setting_msid", 
            "create_ts", 
            "name", 
            "note", 
            "active", 
            "stripe_product_monthly_d2c_subscription_esid", 
            "stripe_price_monthly_d2c_subscription_esid", 
            "stripe_product_tech_service_fee_esid", 
            "stripe_price_tech_service_fee_esid", 
            "stripe_product_credit_report_charge_esid", 
            "stripe_price_credit_report_charge_esid"
        ) VALUES (
            'd2c_software',
            now(),
            'D2C Monthly subscription', 
            NULL, 
            't', 
            'prod_SVgwqmwUVlPUd3', 
            'price_1RafY8H1mpXpEHoKeoFHvoKc', 
            'prod_SZqoESl8ID5kgt', 
            'price_1Reh7GH1mpXpEHoKkXANJXI4', 
            'prod_SZqprh64se9eUw', 
            'price_1Reh7wH1mpXpEHoKqtT8Wx5G'
            )
        ON CONFLICT (signup_advisor_setting_msid) DO NOTHING
    """)
    DB.commit()
    print("Inserted default signup advisor settings")

    # Insert api_stripe.product data
    print("\n=== Inserting api_stripe.product data ===")
    CURSOR.execute("""
        INSERT INTO api_stripe.product (
            product_id,
            name,
            description,
            active,
            metadata
        ) VALUES (
            'prod_SVgwqmwUVlPUd3',
            'D2C Subscription',
            'Monthly subscription for D2C software is $197/mo beginning 30 days after initial sign up.',
            true,
            '{}'::jsonb
        ),
        (
            'prod_SZqoESl8ID5kgt',
            'Tech & Service Fee',
            'Tech & Service fee per client',
            true,
            '{}'::jsonb
        ),
        (
            'prod_SZqprh64se9eUw',
            'Credit Report',
            'Credit Report per client',
            true,
            '{}'::jsonb
        )
        ON CONFLICT (product_id) DO NOTHING
    """)
    DB.commit()
    print("Inserted api_stripe.product data")

    # Insert Stripe products and prices
    print("\n=== Inserting Stripe products and prices ===")

    # D2C Subscription
    d2c_uuid = insert_stripe_product_and_price(
        CURSOR,
        'prod_SVgwqmwUVlPUd3',
        'D2C Subscription',
        'Monthly subscription for D2C software is $197/mo beginning 30 days after initial sign up.',
        'price_1RafY8H1mpXpEHoKeoFHvoKc',
        'Price for D2C Subscription',
        197
    )

    # Tech & Service Fee
    tech_uuid = insert_stripe_product_and_price(
        CURSOR,
        'prod_SZqoESl8ID5kgt',
        'Tech & Service Fee',
        'Tech & Service fee per client',
        'price_1Reh7GH1mpXpEHoKkXANJXI4',
        'Price for Tech & Service Fee',
        1
    )

    # Credit Report
    credit_uuid = insert_stripe_product_and_price(
        CURSOR,
        'prod_SZqprh64se9eUw',
        'Soft Credit Pull',
        'Soft Credit Pull per client',
        'price_1Reh7wH1mpXpEHoKqtT8Wx5G',
        'Price for Credit Report',
        0.65
    )

    DB.commit()
    print("Inserted Stripe products and prices")

    # Define account data for all users to be created
    account_data = [
        # iCover Insurance team
        {"first_name": "Pranav", "last_name": "", "email": "<EMAIL>"},
        {"first_name": "Mahesh", "last_name": "", "email": "<EMAIL>"},
        {"first_name": "Shivam", "last_name": "", "email": "<EMAIL>"},
        {"first_name": "Sakshi", "last_name": "", "email": "<EMAIL>"},
        {"first_name": "Dhilip", "last_name": "", "email": "<EMAIL>"},
        {"first_name": "Aakash", "last_name": "", "email": "<EMAIL>"},
        {"first_name": "Nancy", "last_name": "", "email": "<EMAIL>"},
        {"first_name": "Priti", "last_name": "", "email": "<EMAIL>"},
        {"first_name": "Vignesh", "last_name": "", "email": "<EMAIL>"},
        {"first_name": "Nicole", "last_name": "", "email": "<EMAIL>"},

        # GBU team
        {"first_name": "Sucharitha", "last_name": "Gadem", "email": "<EMAIL>"},
        {"first_name": "Rajinikanth", "last_name": "Bheema", "email": "<EMAIL>"},
        {"first_name": "Preetika", "last_name": "Gaur", "email": "<EMAIL>"},
        {"first_name": "Bharadwaj", "last_name": "Koda", "email": "<EMAIL>"},
        {"first_name": "Sathishkumar", "last_name": "Viswanathan", "email": "<EMAIL>"},
        {"first_name": "Sonomi", "last_name": "Morgan", "email": "<EMAIL>"},
        {"first_name": "Kirk", "last_name": "Dietrich", "email": "<EMAIL>"},
        {"first_name": "Katy", "last_name": "Hylkema", "email": "<EMAIL>"},
        {"first_name": "Amanda", "last_name": "Feist", "email": "<EMAIL>"},
        {"first_name": "Kyle", "last_name": "Fulton", "email": "<EMAIL>"},
        {"first_name": "Karen", "last_name": "Potkul", "email": "<EMAIL>"},
        {"first_name": "Rick", "last_name": "Rodriguez", "email": "<EMAIL>"},
        {"first_name": "Tiffany", "last_name": "Gazza", "email": "<EMAIL>"},
        {"first_name": "Jessica", "last_name": "Ferman", "email": "<EMAIL>"},
        {"first_name": "Felicia", "last_name": "Altenburg", "email": "<EMAIL>"},
        {"first_name": "Brad", "last_name": "Ryba", "email": "<EMAIL>"},
        {"first_name": "Michael", "last_name": "Staar", "email": "<EMAIL>"},
        {"first_name": "Andrew", "last_name": "Barnosky", "email": "<EMAIL>"},
        {"first_name": "Janine", "last_name": "Brickner", "email": "<EMAIL>"},
        {"first_name": "Chris", "last_name": "Russen", "email": "<EMAIL>"},
        {"first_name": "Ravi", "last_name": "Sharma", "email": "<EMAIL>"},
        {"first_name": "Sakina", "last_name": "Hussain", "email": "<EMAIL>"},
        {"first_name": "Krista", "last_name": "Storjohann", "email": "<EMAIL>"},
        
        # SMART Advisor Network team
        {"first_name": "Lesli", "last_name": "Yingling", "email": "<EMAIL>"},
        {"first_name": "Matt", "last_name": "Zagula", "email": "<EMAIL>"},
    ]

    # Create accounts using the data list with auto-generated UUIDs
    created_accounts = []
    for i, account in enumerate(account_data):
        # Generate deterministic UUIDs based on email and index
        identity_seed = f"identity-{account['email']}-{i}"
        advisor_seed = f"advisor-{account['email']}-{i}"

        identity_uuid = generate_uuidv7(identity_seed)
        advisor_uuid = generate_uuidv7(advisor_seed)

        created_accounts.append(insert_identity_and_advisor(
            account["first_name"],
            account["last_name"],
            account["email"],
            identity_uuid,
            advisor_uuid
        ))

    print("=== Completed inserting specific identities and advisors ===\n")

    # Print summary of all created accounts
    print("=" * 80)
    print("ACCOUNT SUMMARY - LOGIN CREDENTIALS")
    print("=" * 80)
    print(f"{'Name':<25} | {'Email':<35} | {'Password'}")
    print("-" * 80)
    for account in created_accounts:
        print(f"{account['name']:<25} | {account['email']:<35} | {account['password']}")
    print("=" * 80)
    print(f"Total accounts created: {len(created_accounts)}")
    print("=" * 80)

        



if __name__ == "__main__":    
    main()


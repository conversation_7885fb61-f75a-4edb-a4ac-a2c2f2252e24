import json
import time
import secrets
import uuid
from database import get_source_db, get_target_db
from psycopg2 import Error
from state_mapping import get_valid_state_codes, map_state_code

def generate_uuidv7() -> str:
    # Get current time in milliseconds since epoch
    timestamp_ms = int(time.time() * 1000)
    
    # Convert timestamp to 48-bit hex (12 characters)
    timestamp_hex = format(timestamp_ms, '012x')
    
    # Generate random bytes for the remaining parts
    random_bytes = secrets.token_bytes(10)
    random_hex = random_bytes.hex()
    
    # Set version 7
    version_hex = '7'
    
    # Set variant (8-b) for the first character of the third group
    variant_hex = format((random_bytes[0] & 0x3F) | 0x80, '02x')
    remaining_hex = random_hex[2:]
    
    # Assemble the UUID in canonical format
    return f"{timestamp_hex[:8]}-{timestamp_hex[8:12]}-{version_hex}{random_hex[:3]}-{variant_hex}{random_hex[4:6]}-{random_hex[6:18]}"

    
'''
SQL For Agency Table
CREATE TABLE df4l.agency (
   agency_uuid uuid DEFAULT public.uuidv7() NOT NULL,
   agency_esid varchar(20) NOT NULL,
   create_ts timestamptz(6) NOT NULL DEFAULT now(),
   name varchar(128) NOT NULL,
   active boolean NOT NULL DEFAULT true,
   admin_note text,
   data jsonb NOT NULL DEFAULT '{}'::jsonb,
   CONSTRAINT "agency_pkey" PRIMARY KEY (agency_uuid),
   CONSTRAINT "agency.agency_esid.uniq" UNIQUE (agency_esid)
);

'''
def transfer_agencies(source_db, target_db):
    AgencyMap = {} #map between agency_znid and agency_uuid 
    try:
        # Get source data with the specified mapping
        source_agencies = source_db.RowList("""
            SELECT
                "Agency_ZNID" AS agency_znid,
                "AgencyID" AS agency_esid,
                "CreateDate" as create_ts,
                "Name" AS name,
                "Active" AS active,
                "AdminNote" as admin_note,
                jsonb_build_object('Agency_ZNID', "Agency_ZNID") as data
            FROM
                "Main"."Agency"            
        """)
        
        print(f"\nFound {len(source_agencies)} agencies to transfer")

        # Insert into target database
        cursor = target_db.cursor()
        for agency in source_agencies:
            print(f"Processing agency: {agency['name']}")
            try:
                # Convert data to JSON string if it's a dict
                AgencyMap[agency['agency_znid']] = generate_uuidv7()

                cursor.execute("""
                    INSERT INTO df4l.agency (
                        agency_uuid,
                        agency_esid,
                        create_ts,
                        name,
                        active,
                        admin_note,
                        data
                    ) VALUES (
                        %(agency_uuid)s,
                        %(agency_esid)s,
                        %(create_ts)s,
                        %(name)s,
                        %(active)s,
                        %(admin_note)s,
                        %(data)s::jsonb
                    )
                """, {
                    'agency_uuid': AgencyMap[agency['agency_znid']],
                    'agency_esid': agency['agency_esid'],
                    'create_ts': agency['create_ts'],
                    'name': agency['name'],
                    'active': agency['active'],
                    'admin_note': agency['admin_note'],
                    'data': json.dumps(agency['data'])
                })
                target_db.commit()
            except Exception as e:
                target_db.rollback()
                print(f"Error processing agency {agency['name']}: {e}")
                print(f"Data type: {type(agency['data'])}")
                print(f"Data content: {agency['data']}")
                continue
        
        cursor.close()
        print("Transfer completed successfully")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during transfer: {e}")
        raise
    return AgencyMap

'''
SQL For Advisor Table
CREATE TABLE df4l.advisor (
   advisor_uuid uuid NOT NULL DEFAULT uuidv7(),
   agency_uuid uuid,
   create_ts timestamptz(6) NOT NULL DEFAULT now(),
   advisor_esid varchar(7) NOT NULL,
   gbu_advisor_esid varchar(20),
   first_name varchar(128) NOT NULL,
   last_name varchar(128) NOT NULL,
   email varchar(256), --we have advisor records with no emails
   phone varchar(64),
   address1 varchar(128),
   address2 varchar(128),
   city varchar(128),
   state varchar(16),
   zip varchar(20),
   country varchar(128),
   active boolean NOT NULL DEFAULT true,
   admin_note text,
   data jsonb NOT NULL DEFAULT '{}'::jsonb,
   CONSTRAINT "advisor_pkey" PRIMARY KEY ("advisor_uuid"),
   CONSTRAINT fk_agency FOREIGN KEY (agency_uuid) REFERENCES df4l.agency(agency_uuid) ON DELETE RESTRICT,
   CONSTRAINT fk_state FOREIGN KEY (state) REFERENCES addr_iso.us_state(state_code) ON DELETE RESTRICT,
   CONSTRAINT advisor_esid_unique UNIQUE (advisor_esid)
);
'''
def transfer_advisors(source_db, target_db, AgencyMap):
    AdvisorMap = {}  # map between advisor_znid and advisor_uuid
    try:
        # Get valid state codes from the database
        valid_state_codes = get_valid_state_codes(target_db)
        
        # Get source data with the specified mapping
        source_advisors = source_db.RowList("""
            SELECT
                a."Advisor_ZNID" as advisor_znid,
                a."Agency_ZNID" as agency_znid,
                a."CreateDate" as create_ts,
                a."AdvisorID" as advisor_esid,
                c."FirstName" as first_name,
                c."LastName" as last_name,
                c."Email" as email,
                a."Active" as active,
                c."ContactInfo"->'Phone1'->>'Number' as phone,
                COALESCE(NULLIF(c."ContactInfo"->'Address1'->>'Line1', ''), c."ContactInfo"->'Address2'->>'Line1') as address1,
                COALESCE(NULLIF(c."ContactInfo"->'Address2'->>'Line2', ''), c."ContactInfo"->'Address1'->>'Line2') as address2,
                COALESCE(NULLIF(c."ContactInfo"->'Address1'->>'City', ''), c."ContactInfo"->'Address2'->>'City') as city,
                COALESCE(NULLIF(c."ContactInfo"->'Address1'->>'State', ''), c."ContactInfo"->'Address2'->>'State') as state,
                COALESCE(NULLIF(c."ContactInfo"->'Address2'->>'Zip', ''), c."ContactInfo"->'Address1'->>'Zip') as zip,
                COALESCE(NULLIF(c."ContactInfo"->'Address2'->>'Country', ''), c."ContactInfo"->'Address1'->>'Country') as country,
                a."AdminNote" as admin_note,
                jsonb_build_object(
                    'Advisor_ZNID', a."Advisor_ZNID",
                    'Agency_ZNID', a."Agency_ZNID",
                    'original_state', COALESCE(NULLIF(c."ContactInfo"->'Address1'->>'State', ''), c."ContactInfo"->'Address2'->>'State')
                ) as data
            FROM
                "Main"."Advisor" a
            LEFT JOIN
                "ACRM"."Contact" c ON c."Contact_ZNID" = a."Contact_ZNID"
        """)
        
        print(f"\nFound {len(source_advisors)} advisors to transfer")

        cursor = target_db.cursor()
        for advisor in source_advisors:
            print(f"Processing advisor: {advisor['first_name']} {advisor['last_name']}")
            try:
                AdvisorMap[advisor['advisor_znid']] = generate_uuidv7()
                
                # Map state code to standard format
                mapped_state = map_state_code(advisor['state'], valid_state_codes)
                
                # Store original state in data if it was mapped
                data = advisor['data']
                if mapped_state and mapped_state != advisor['state']:
                    data['mapped_from_state'] = advisor['state']
                
                cursor.execute("""
                    INSERT INTO df4l.advisor (
                        advisor_uuid,
                        agency_uuid,
                        create_ts,
                        advisor_esid,
                        first_name,
                        last_name,
                        email,
                        phone,
                        address1,
                        address2,
                        city,
                        state,
                        zip,
                        active,
                        admin_note,
                        data
                    ) VALUES (
                        %(advisor_uuid)s,
                        %(agency_uuid)s,
                        %(create_ts)s,
                        %(advisor_esid)s,
                        %(first_name)s,
                        %(last_name)s,
                        %(email)s,
                        %(phone)s,
                        %(address1)s,
                        %(address2)s,
                        %(city)s,
                        %(state)s,
                        %(zip)s,
                        %(active)s,
                        %(admin_note)s,
                        %(data)s::jsonb
                    )
                """, {
                    'advisor_uuid': AdvisorMap[advisor['advisor_znid']],
                    'agency_uuid': AgencyMap.get(advisor['agency_znid'], None),
                    'create_ts': advisor['create_ts'],
                    'advisor_esid': advisor['advisor_esid'],
                    'first_name': advisor['first_name'],
                    'last_name': advisor['last_name'],
                    'email': advisor['email'],
                    'phone': advisor['phone'],
                    'address1': advisor['address1'],
                    'address2': advisor['address2'],
                    'city': advisor['city'],
                    'state': mapped_state,  # Use mapped state code
                    'zip': advisor['zip'],
                    'active': advisor['active'],
                    'admin_note': advisor['admin_note'],
                    'data': json.dumps(data)
                })
                target_db.commit()
            except Exception as e:
                target_db.rollback()
                print(f"Error processing advisor {advisor['first_name']} {advisor['last_name']}: {e}")
                print(f"State value: '{advisor['state']}', Mapped to: '{mapped_state}'")
                print(f"Data content: {advisor['data']}")
                continue
        
        cursor.close()
        print("Advisor transfer completed successfully")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during advisor transfer: {e}")
        raise
    
    return AdvisorMap    

'''
CREATE TABLE df4l.client0 (
   client_uuid uuid DEFAULT public.uuidv7() NOT NULL,
   advisor_uuid uuid,
   create_ts timestamptz(6) NOT NULL DEFAULT now(),
   name varchar(128) NOT NULL,
   email varchar(255),
   phone varchar(20),
   phone2 varchar(20),
   address1 varchar(128),
   address2 varchar(128),
   city varchar(32),
   state varchar(16),
   zip varchar(16),
   country varchar(32),
   time_zone varchar(32),
   monthly_budget numeric(10,2),
   file_znid_doc_to_append int4,
   active bool NOT NULL DEFAULT true,
   note varchar(4096),
   annual_insurance_premium numeric(10,2),
   annual_insurance_pua numeric(10,2),
   net_cash_at_end numeric(10,2),
   debt_free_start_date date,
   debt_free_active bool NOT NULL DEFAULT false,
   debt_free_extra_pua_list jsonb NOT NULL DEFAULT '[]'::jsonb,
   data jsonb NOT NULL DEFAULT '{}'::jsonb,
   CONSTRAINT "client_pkey" PRIMARY KEY ("client_uuid"),
   CONSTRAINT fk_advisor FOREIGN KEY (advisor_uuid) REFERENCES df4l.advisor(advisor_uuid) ON DELETE RESTRICT
);
'''
def transfer_clients(source_db, target_db, AdvisorMap):
    ClientMap = {}  # map between client_znid and client_uuid
    try:
        # Get valid state codes from the database
        valid_state_codes = get_valid_state_codes(target_db)
        
        # Get source data with the specified mapping
        source_clients = source_db.RowList("""
            SELECT
                "AdvisorClient_ZNID" as client_znid,
                "Advisor_ZNID" as advisor_znid,
                "CreateDate" as create_ts,
                "Name" as full_name,
                "Email" as email,
                "Phone" as phone,
                "Phone2" as phone2,
                "Line1" as address1,
                "Line2" as address2,
                "City" as city,
                "State" as state,
                "Zip" as zip,
                "Country" as country,
                "TimeZone" as time_zone,
                "MonthlyBudget" as monthly_budget,
                "File_ZNID_DocToAppend" as file_znid_doc_to_append,
                "Active" as active,
                "Note" as note,
                "AnnualInsurancePremium" as annual_insurance_premium,
                "AnnualInsurancePUA" as annual_insurance_pua,
                "NetCashAtEnd" as net_cash_at_end,
                "DebtFree_StartDate" as debt_free_start_date,
                "DebtFree_Active" as debt_free_active,
                "DebtFree_ExtraPUA_List" as debt_free_extra_pua_list,
                jsonb_build_object(
                    'AdvisorClient_ZNID', "AdvisorClient_ZNID",
                    'Advisor_ZNID', COALESCE("Advisor_ZNID", NULL),
                    'original_state', "State"
                ) as data
            FROM
                "Main"."AdvisorClient"
            WHERE 
                "Advisor_ZNID" is not NULL  -- only process clients with an advisor
                AND "Name" is not NULL  -- we don't use clients without names
            """)
        
        print(f"\nFound {len(source_clients)} clients to transfer")

        cursor = target_db.cursor()
        for client in source_clients:
            print(f"Processing client: {client['full_name']}")
            try:
                ClientMap[client['client_znid']] = generate_uuidv7()
                
                # Parse full_name into first_name and last_name
                name_parts = client['full_name'].split(None, 1)
                first_name = name_parts[0] if len(name_parts) > 0 else ""
                last_name = name_parts[1] if len(name_parts) > 1 else ""
                
                # Map state code to standard format
                mapped_state = map_state_code(client['state'], valid_state_codes)
                
                # Store original state in data if it was mapped
                data = client['data']
                if mapped_state and mapped_state != client['state']:
                    data['mapped_from_state'] = client['state']

                cursor.execute("""
                    INSERT INTO df4l.client0 (
                        client_uuid,
                        advisor_uuid,
                        create_ts,
                        first_name,
                        last_name,
                        email,
                        phone,
                        phone2,
                        address1,
                        address2,
                        city,
                        state,
                        zip,
                        time_zone,
                        monthly_budget,
                        file_znid_doc_to_append,
                        active,
                        note,
                        annual_insurance_premium,
                        annual_insurance_pua,
                        net_cash_at_end,
                        debt_free_start_date,
                        debt_free_active,
                        debt_free_extra_pua_list,
                        data
                    ) VALUES (
                        %(client_uuid)s,
                        %(advisor_uuid)s,
                        %(create_ts)s,
                        %(first_name)s,
                        %(last_name)s,
                        %(email)s,
                        %(phone)s,
                        %(phone2)s,
                        %(address1)s,
                        %(address2)s,
                        %(city)s,
                        %(state)s,
                        %(zip)s,
                        %(time_zone)s,
                        %(monthly_budget)s,
                        %(file_znid_doc_to_append)s,
                        %(active)s,
                        %(note)s,
                        %(annual_insurance_premium)s,
                        %(annual_insurance_pua)s,
                        %(net_cash_at_end)s,
                        %(debt_free_start_date)s,
                        %(debt_free_active)s,
                        %(debt_free_extra_pua_list)s,
                        %(data)s::jsonb
                    )
                """, {
                    'client_uuid': ClientMap[client['client_znid']],
                    'advisor_uuid': AdvisorMap.get(client['advisor_znid'], None),
                    'create_ts': client['create_ts'],
                    'first_name': first_name,
                    'last_name': last_name,
                    'email': client['email'],
                    'phone': client['phone'],
                    'phone2': client['phone2'],
                    'address1': client['address1'],
                    'address2': client['address2'],
                    'city': client['city'],
                    'state': mapped_state,  # Use mapped state code
                    'zip': client['zip'],
                    'time_zone': client['time_zone'],
                    'monthly_budget': client['monthly_budget'],
                    'file_znid_doc_to_append': client['file_znid_doc_to_append'],
                    'active': client['active'],
                    'note': client['note'],
                    'annual_insurance_premium': client['annual_insurance_premium'],
                    'annual_insurance_pua': client['annual_insurance_pua'],
                    'net_cash_at_end': client['net_cash_at_end'],
                    'debt_free_start_date': client['debt_free_start_date'],
                    'debt_free_active': client['debt_free_active'],
                    'debt_free_extra_pua_list': json.dumps(client['debt_free_extra_pua_list']),
                    'data': json.dumps(data)
                })
                target_db.commit()
            except Exception as e:
                target_db.rollback()
                print(f"Error processing client {client['full_name']}: {e}")
                print(f"State value: '{client['state']}', Mapped to: '{mapped_state}'")
                print(f"Data content: {client['data']}")
                continue
        
        cursor.close()
        print("Client transfer completed successfully")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during client transfer: {e}")
        raise
    
    return ClientMap


'''
CREATE TABLE df4l.client0_debt (
    client_debt_uuid uuid NOT NULL DEFAULT uuidv7(),
    client_uuid uuid NOT NULL,
    debttype_uuid uuid,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    balance numeric,
    balance_date date,
    interest_rate numeric,
    monthly_payment numeric,
    active boolean NOT NULL DEFAULT true,
    note varchar(1024),
    name varchar(32),
    data jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT client_debt_pkey PRIMARY KEY (client_debt_uuid),
    CONSTRAINT fk_client FOREIGN KEY (client_uuid) REFERENCES df4l.client0(client_uuid) ON DELETE RESTRICT,
    CONSTRAINT fk_debttype FOREIGN KEY (debttype_uuid) REFERENCES df4l.debttype(debttype_uuid) ON DELETE RESTRICT
);
'''

def transfer_client_debts(source_db, target_db, ClientMap):
    Client_DebtMap = {}  # map between AdvisorClient_Debt_ZNID and client_debt_uuid
    
    try:
        # Get source data with the specified mapping
        source_client_debts = source_db.RowList("""
            SELECT
                d."AdvisorClient_Debt_ZNID" as client_debt_znid,
                d."AdvisorClient_ZNID" as client_znid,
                d."CreateDate" as create_ts,
                d."Balance" as balance,
                d."BalanceDate" as balance_date,
                d."InterestRate" as interest_rate,
                d."MonthlyPayment" as monthly_payment,
                d."Active" as active,
                d."Note" as note,
                COALESCE(d."Name", (SELECT dt."Name" FROM "Main"."DebtType" dt WHERE dt."DebtType_ZNID" = d."DebtType_ZNID")) as name,
                jsonb_build_object(
                    'AdvisorClient_Debt_ZNID', d."AdvisorClient_Debt_ZNID",
                    'AdvisorClient_ZNID', d."AdvisorClient_ZNID", 
                    'DebtType_ZNID', d."DebtType_ZNID"
                ) as data
            FROM
                "Main"."AdvisorClient_Debt" d
            INNER JOIN
                "Main"."AdvisorClient" c ON c."AdvisorClient_ZNID" = d."AdvisorClient_ZNID"
            WHERE
                d."Active" = true
                AND c."Name" IS NOT NULL
        """)
        
        print(f"\nFound {len(source_client_debts)} client debts to transfer")

        cursor = target_db.cursor()
        for client_debt in source_client_debts:
            print(f"Processing client debt: {client_debt['name']}")
            try:
                # Skip if client_znid doesn't exist in ClientMap
                if client_debt['client_znid'] not in ClientMap:
                    print(f"Warning: Skipping debt '{client_debt['name']}' because client ZNID {client_debt['client_znid']} not found in ClientMap")
                    continue
                    
                Client_DebtMap[client_debt['client_debt_znid']] = generate_uuidv7()

                cursor.execute("""
                    INSERT INTO df4l.client0_debt (
                        client_debt_uuid,
                        client_uuid,
                        create_ts,
                        balance,
                        balance_date,
                        interest_rate,
                        monthly_payment,
                        active,
                        note,
                        name,
                        data
                    ) VALUES (
                        %(client_debt_uuid)s,
                        %(client_uuid)s,
                        %(create_ts)s,
                        %(balance)s,
                        %(balance_date)s,
                        %(interest_rate)s,
                        %(monthly_payment)s,
                        %(active)s,
                        %(note)s,
                        %(name)s,
                        %(data)s::jsonb
                    )
                """, {
                    'client_debt_uuid': Client_DebtMap[client_debt['client_debt_znid']],
                    'client_uuid': ClientMap[client_debt['client_znid']],  # This was failing
                    'create_ts': client_debt['create_ts'],
                    'balance': client_debt['balance'],
                    'balance_date': client_debt['balance_date'],
                    'interest_rate': client_debt['interest_rate'],
                    'monthly_payment': client_debt['monthly_payment'],
                    'active': client_debt['active'],
                    'note': client_debt['note'],
                    'name': client_debt['name'],
                    'data':  json.dumps(client_debt['data'])
                })
                target_db.commit()
            except Exception as e:
                target_db.rollback()
                print(f"Error processing client debt {client_debt['name']}: {e}")
                print(f"Data type: {type(client_debt['data'])}")
                print(f"Data content: {client_debt['data']}")
                continue
        
        cursor.close()
        print("Client debt transfer completed successfully")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during client debt transfer: {e}")
        raise
    
    return Client_DebtMap

'''
CREATE TABLE df4l.client0_event (
    client_event_uuid uuid NOT NULL DEFAULT uuidv7(),
    client_uuid uuid NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    session_token varchar(64),
    create_addr varchar(64),
    create_auth jsonb NOT NULL DEFAULT '[]'::jsonb,
    create_name varchar(64),
    create_source varchar(64),
    event_type varchar(32) NOT NULL,
    event_date date NOT NULL DEFAULT now(),
    note varchar(1024),
    amount numeric(12,2),
    data jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT client_event_pkey PRIMARY KEY (client_event_uuid),
    CONSTRAINT fk_client FOREIGN KEY (client_uuid) REFERENCES df4l.client0(client_uuid) ON DELETE RESTRICT
);
'''

def transfer_client_events(source_db, target_db, ClientMap):
    ClientEventMap = {}  # map between AdvisorClient_Event_ZNID and client_event_uuid
    try:
        # Get source data with the specified mapping
        source_client_events = source_db.RowList("""
            SELECT
                e."AdvisorClient_Event_ZNID" as client_event_znid,
                e."AdvisorClient_ZNID" as client_znid,
                e."CreateTS" as create_ts,
                e."SessionToken" as session_token,
                e."CreateAddr" as create_addr,
                e."CreateAuth" as create_auth,
                e."CreateName" as create_name,
                e."CreateSource" as create_source,
                e."EventType" as event_type,
                e."Date" as event_date,
                e."Note" as note,
                e."Amount" as amount,
                jsonb_build_object(
                    'AdvisorClient_Event_ZNID', e."AdvisorClient_Event_ZNID",
                    'AdvisorClient_ZNID', e."AdvisorClient_ZNID",
                    'FKEY', e."FKEY"
                ) as data
            FROM
                "Main"."AdvisorClient_Event" e
            INNER JOIN
                "Main"."AdvisorClient" c ON c."AdvisorClient_ZNID" = e."AdvisorClient_ZNID"
            WHERE
                c."Name" IS NOT NULL
        """)
        
        print(f"\nFound {len(source_client_events)} client events to transfer")

        cursor = target_db.cursor()
        for client_event in source_client_events:
            print(f"Processing client event: {client_event['event_type']}")
            try:
                # Skip if client_znid doesn't exist in ClientMap
                if client_event['client_znid'] not in ClientMap:
                    print(f"Warning: Skipping event '{client_event['event_type']}' because client ZNID {client_event['client_znid']} not found in ClientMap")
                    continue
                    
                ClientEventMap[client_event['client_event_znid']] = generate_uuidv7()

                cursor.execute("""
                    INSERT INTO df4l.client0_event (
                        client_event_uuid,
                        client_uuid,
                        create_ts,
                        session_token,
                        create_addr,
                        create_auth,
                        create_name,
                        create_source,
                        event_type,
                        event_date,
                        note,
                        amount,
                        data
                    ) VALUES (
                        %(client_event_uuid)s,
                        %(client_uuid)s,
                        %(create_ts)s,
                        %(session_token)s,
                        %(create_addr)s,
                        %(create_auth)s,
                        %(create_name)s,
                        %(create_source)s,
                        %(event_type)s,
                        %(event_date)s,
                        %(note)s,
                        %(amount)s,
                        %(data)s::jsonb
                    )
                """, {
                    'client_event_uuid': ClientEventMap[client_event['client_event_znid']],
                    'client_uuid': ClientMap[client_event['client_znid']],  # Now safe to use direct lookup
                    'create_ts': client_event['create_ts'],
                    'session_token': client_event['session_token'],
                    'create_addr': client_event['create_addr'],
                    'create_auth': json.dumps(client_event['create_auth']),
                    'create_name': client_event['create_name'],
                    'create_source': client_event['create_source'],
                    'event_type': client_event['event_type'],
                    'event_date': client_event['event_date'],
                    'note': client_event['note'],
                    'amount': client_event['amount'],
                    'data': json.dumps(client_event['data'])
                })
                target_db.commit()
            except Exception as e:
                target_db.rollback()
                print(f"Error processing client event {client_event['event_type']}: {e}")
                print(f"Data type: {type(client_event['data'])}")
                print(f"Data content: {client_event['data']}")
                continue
        
        cursor.close()
        print("Client event transfer completed successfully")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during client event transfer: {e}")
        raise
    
    return ClientEventMap
'''
CREATE TABLE df4l.client0_note (
    client_note_uuid uuid NOT NULL DEFAULT uuidv7(),
    client_uuid uuid NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    note varchar(4096) NOT NULL,
    data jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT client_note_pkey PRIMARY KEY (client_note_uuid),
    CONSTRAINT fk_client FOREIGN KEY (client_uuid) REFERENCES df4l.client0(client_uuid) ON DELETE RESTRICT
);
'''

def transfer_client_notes(source_db, target_db, ClientMap):
    ClientNoteMap = {}  # map between AdvisorClient_Note_ZNID and client_note_uuid
    try:
        # Get source data with the specified mapping
        source_client_notes = source_db.RowList("""
            SELECT
                n."AdvisorClient_Note_ZNID" as client_note_znid,
                n."AdvisorClient_ZNID" as client_znid,
                n."CreateTS" as create_ts,
                n."Note" as note,
                jsonb_build_object(
                    'AdvisorClient_Note_ZNID', n."AdvisorClient_Note_ZNID",
                    'AdvisorClient_ZNID', n."AdvisorClient_ZNID"
                ) as data
            FROM
                "Main"."AdvisorClient_Note" n
            INNER JOIN
                "Main"."AdvisorClient" c ON c."AdvisorClient_ZNID" = n."AdvisorClient_ZNID"
            WHERE
                c."Name" IS NOT NULL
        """)
        
        print(f"\nFound {len(source_client_notes)} client notes to transfer")

        cursor = target_db.cursor()
        for client_note in source_client_notes:
            print(f"Processing client note for client_znid: {client_note['client_znid']}")
            try:
                ClientNoteMap[client_note['client_note_znid']] = generate_uuidv7()

                cursor.execute("""
                    INSERT INTO df4l.client0_note (
                        client_note_uuid,
                        client_uuid,
                        create_ts,
                        note,
                        data
                    ) VALUES (
                        %(client_note_uuid)s,
                        %(client_uuid)s,
                        %(create_ts)s,
                        %(note)s,
                        %(data)s::jsonb
                    )
                """, {
                    'client_note_uuid': ClientNoteMap[client_note['client_note_znid']],
                    'client_uuid': ClientMap.get(client_note['client_znid']),
                    'create_ts': client_note['create_ts'],
                    'note': client_note['note'],
                    'data': json.dumps(client_note['data'])
                })
                target_db.commit()
            except Exception as e:
                target_db.rollback()
                print(f"Error processing client note {client_note['client_znid']}: {e}")
                print(f"Data type: {type(client_note['data'])}")
                print(f"Data content: {client_note['data']}")
                continue
        
        cursor.close()
        print("Client note transfer completed successfully")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during client note transfer: {e}")
        raise
    
    return ClientNoteMap

def transfer_advisor_statelics(source_db, target_db, AdvisorMap):
    try:
        # Get valid state codes from the database
        valid_state_codes = get_valid_state_codes(target_db)
        
        # Get source data with the specified mapping
        source_advisor_statelics = source_db.RowList("""
            SELECT
                a."Advisor_ZNID" as advisor_znid,
                c."Setting"->'StatesOfLicensure' as states_of_licensure
            FROM
                "ACRM"."Contact" c
            INNER JOIN 
                "Main"."Advisor" a ON c."Contact_ZNID" = a."Contact_ZNID"
            WHERE 
                c."Setting"->'StatesOfLicensure' IS NOT NULL
        """)
        
        print(f"\nFound {len(source_advisor_statelics)} advisor state licenses to transfer")

        cursor = target_db.cursor()
        for advisor_statelic in source_advisor_statelics:
            advisor_uuid = AdvisorMap.get(advisor_statelic['advisor_znid'])
            if not advisor_uuid:
                print(f"Skipping advisor_znid {advisor_statelic['advisor_znid']}: No matching UUID found")
                continue
                
            states = advisor_statelic['states_of_licensure']
            if not isinstance(states, list):
                print(f"Skipping advisor_znid {advisor_statelic['advisor_znid']}: States not in list format")
                continue
                
            print(f"Processing state licenses for advisor_znid: {advisor_statelic['advisor_znid']}")
            
            for state_code in states:
                try:
                    # Extract state code from format like "US-FL" to "FL"
                    if state_code and isinstance(state_code, str) and "-" in state_code:
                        state_code = state_code.split("-")[1]
                    
                    # Map state code to standard format
                    mapped_state = map_state_code(state_code, valid_state_codes)
                    if not mapped_state:
                        print(f"Skipping invalid state code: {state_code}")
                        continue
                        
                    # Store original state in data if it was mapped
                    data = {'original_state': state_code}
                    
                    cursor.execute("""
                        INSERT INTO df4l.advisor_statelic (
                            advisor_uuid,
                            state_code,
                            data
                        ) VALUES (
                            %(advisor_uuid)s,
                            %(state_code)s,
                            %(data)s::jsonb
                        ) ON CONFLICT (advisor_uuid, state_code) DO NOTHING
                    """, {
                        'advisor_uuid': advisor_uuid,
                        'state_code': mapped_state,
                        'data': json.dumps(data)
                    })
                    target_db.commit()
                except Exception as e:
                    target_db.rollback()
                    print(f"Error processing state license {state_code} for advisor {advisor_statelic['advisor_znid']}: {e}")
                    continue
        
        cursor.close()
        print("Advisor state license transfer completed successfully")
        
    except Exception as e:
        if 'target_db' in locals():
            target_db.rollback()
        print(f"Error during advisor state license transfer: {e}")
        raise

def main():
    try:
        print("Connecting to source database...")
        db1 = get_source_db()
        print("Connected to source database successfully")
        
        print("Connecting to target database...")
        db2 = get_target_db()
        print("Connected to target database successfully")
        
        # Create a cursor and execute truncate statements
        cursor = db2.cursor()
        cursor.execute("TRUNCATE TABLE df4l.client0_event CASCADE")
        cursor.execute("TRUNCATE TABLE df4l.client0_debt CASCADE")
        cursor.execute("TRUNCATE TABLE df4l.client0 CASCADE")
        cursor.execute("TRUNCATE TABLE df4l.advisor_statelic CASCADE")
        cursor.execute("TRUNCATE TABLE df4l.advisor CASCADE")
        cursor.execute("TRUNCATE TABLE df4l.agency CASCADE")
        db2.commit()
        cursor.close()
        
        AgencyMap = transfer_agencies(db1, db2)
        AdvisorMap = transfer_advisors(db1, db2, AgencyMap)
        transfer_advisor_statelics(db1, db2, AdvisorMap)
        ClientMap = transfer_clients(db1, db2, AdvisorMap)
        Client_DebtMap = transfer_client_debts(db1, db2, ClientMap)
        ClientEventMap = transfer_client_events(db1, db2, ClientMap)
        ClientNoteMap = transfer_client_notes(db1, db2, ClientMap)
        
        # Query to list all tables from all schemas using RowList
        tables = db1.RowList("""
            SELECT 
                schemaname || '.' || tablename as table_name,
                pg_size_pretty(pg_total_relation_size(quote_ident(schemaname) || '.' || quote_ident(tablename))) as size
            FROM 
                pg_tables 
            WHERE 
                schemaname NOT IN ('pg_catalog', 'information_schema')
            ORDER BY 
                schemaname, tablename
        """)

        # Query to list all tables from all schemas using RowList for db2
        tables_db2 = db2.RowList("""
            SELECT 
                schemaname || '.' || tablename as table_name,
                pg_size_pretty(pg_total_relation_size(quote_ident(schemaname) || '.' || quote_ident(tablename))) as size
            FROM 
                pg_tables 
            WHERE 
                schemaname NOT IN ('pg_catalog', 'information_schema')
            ORDER BY 
                schemaname, tablename
        """)

    except Error as e:
        print(f"Database Error: {e}")
    except Exception as e:
        print(f"General Error: {e}")
    finally:
        if 'db1' in locals():
            db1.close()
        if 'db2' in locals():
            db2.close()

if __name__ == "__main__":
    main()

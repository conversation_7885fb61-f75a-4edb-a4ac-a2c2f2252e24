import hashlib
import secrets
import time
from faker import Faker
import json
import psycopg2
import argparse

fake = Faker('en_US')

# Parse command line arguments
parser = argparse.ArgumentParser(description='Generate test data for RRR')
parser.add_argument('--seed', type=str, default="DEFAULT", help='Optional seed for random generation')
args = parser.parse_args()

# Use provided seed or generate a random one
SEED = args.seed

# Hardcode the db connection for RRR
DB = psycopg2.connect(
    host="127.0.0.1",
    port=20000,
    database="rrr",
    user="app",
    password="pass"
)
DB.autocommit = True
CURSOR = DB.cursor()

fake.seed_instance(SEED)

def pick_one_of(seed, some_list):
    hash = hashlib.sha256(seed.encode()).hexdigest()
    index = int(hash, 16) % len(some_list)
    return some_list[index]

def generate_uuidv7(seed) -> str:
    # generate a deterministic UUID from seed
    hash = hashlib.sha256(seed.encode()).hexdigest()
    
    # Force first 5 characters to be 0
    modified_hash = "00000" + hash[5:]
    
    # Format as UUIDv7
    return f"{modified_hash[:8]}-{modified_hash[8:12]}-7{modified_hash[12:15]}-{modified_hash[15:19]}-{modified_hash[19:31]}"

def generate_timestamp(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    # Use modulo to limit to reasonable range (e.g., 2010-2030)
    base_timestamp = 1262304000  # 2010-01-01 00:00:00
    range_seconds = 631152000    # ~20 years in seconds
    timestamp = base_timestamp + (int(hash[:8], 16) % range_seconds)
    return time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(timestamp))

def active_or_not(seed) -> bool:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    return int(hash[:1], 16) % 10 > 0

def generate_note(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    
    if int(hash[:1], 16) % 2 == 0:
        return None
    else:
        return f"Note for {hash[:10]}"

def generate_phone(seed) -> str:
    hash = hashlib.sha256(seed.encode()).hexdigest()
    fake.seed_instance(int(hash[:8], 16))
    
    # Use a custom phone number format that's guaranteed to be under 20 chars
    phone_number = fake.numerify(text="###-###-####")
    
    return phone_number

def generate_email(seed, first_name) -> str:
    domain = pick_one_of(seed, DOMAIN_LIST)
    name = first_name.lower().replace(" ", "")
    return f"{name}@{domain}"

NAME_LIST = [
    # Short names
    dict(first_name="Al", last_name="Wu"),
    dict(first_name="Bo", last_name="Li"),
    dict(first_name="Ty", last_name="Ng"),
    dict(first_name="Ed", last_name="Ko"),
    dict(first_name="Mo", last_name="Yu"),
    
    # Long names
    dict(first_name="Christopher", last_name="Montgomery-Wells"),
    dict(first_name="Alexandria", last_name="Van der Woodsen"),
    dict(first_name="Maximilian", last_name="Worthington III"),
    dict(first_name="Evangelina", last_name="Rodriguez-Martinez"),
    dict(first_name="Sebastian", last_name="Thorne-Blackwood"),
    
    # Normal names
    dict(first_name="James", last_name="Smith"),
    dict(first_name="Maria", last_name="Garcia"),
    dict(first_name="David", last_name="Johnson"),
    dict(first_name="Sarah", last_name="Williams"),
    dict(first_name="Michael", last_name="Brown"),
    
    # Special characters
    dict(first_name="Jean-Pierre", last_name="D'Arcy"),
    dict(first_name="María-José", last_name="O'Connor"),
    dict(first_name="André", last_name="St. James"),
    dict(first_name="François", last_name="Le'Blanc"),
    dict(first_name="Zoë", last_name="McKenzie"),
    
    # International
    dict(first_name="Wei", last_name="Zhang"),
    dict(first_name="Yuki", last_name="Tanaka"),
    dict(first_name="Priya", last_name="Patel"),
    dict(first_name="Jung-Ho", last_name="Kim"),
    dict(first_name="Olga", last_name="Ivanova"),
    
    # Compound
    dict(first_name="Mary Jane", last_name="Watson-Parker"),
    dict(first_name="John Paul", last_name="Jones-Smith"),
    dict(first_name="Anna Marie", last_name="von Trapp"),
    dict(first_name="Jean Claude", last_name="Van Damme"),
    dict(first_name="Billy Bob", last_name="O'Reilly"),
    
    # Professional titles
    dict(first_name="Dr. Robert", last_name="Wilson"),
    dict(first_name="Rev. Michael", last_name="Thomas"),
    dict(first_name="Hon. Elizabeth", last_name="Warren"),
    dict(first_name="Prof. William", last_name="White"),
    dict(first_name="Capt. James", last_name="Kirk"),
    
    # Single name
    dict(first_name="Madonna", last_name=""),
    dict(first_name="Cher", last_name=""),
    dict(first_name="Prince", last_name=""),
    dict(first_name="Bono", last_name=""),
    dict(first_name="Seal", last_name=""),
]

ADDRESS_LIST = [
    # Short
    dict(line1="123 Main", line2=None, city="Anytown", state="CA", zip="12345", country="USA"),
    dict(line1="45 Oak", line2=None, city="Troy", state="NY", zip="12180", country="USA"),
    dict(line1="7 Pine", line2=None, city="Dover", state="DE", zip="19901", country="USA"),
    dict(line1="9 Elm", line2=None, city="Salem", state="MA", zip="01970", country="USA"),
    dict(line1="12 Cedar", line2=None, city="Rome", state="GA", zip="30161", country="USA"),
    # Long
    dict(line1="4567 NorthWest Oak Avenue", line2="Apartment 12B, Building C, Floor 3", city="Springfield", state="IL", zip="62701", country="USA"),
    dict(line1="7890 South Pine Road Extension", line2="Suite 204, The Grand Pines Complex", city="Smallville", state="KS", zip="66002", country="USA"),
    dict(line1="1011 East Elm Street Boulevard", line2="Unit B-17, Lower Level, Garden View", city="Boulder", state="CO", zip="80301", country="USA"),
    dict(line1="3210 Cedar Lane Drive Northeast", line2="Penthouse 9, Tower II, Historic District", city="Savannah", state="GA", zip="31401", country="USA"),
    dict(line1="6543 Birch Drive West", line2="Corporate Suite 300, Birch Business Park", city="Portland", state="OR", zip="97201", country="USA"),
    # Normal
    dict(line1="987 Maple Ct", line2=None, city="Asheville", state="NC", zip="28801", country="USA"),
    dict(line1="147 Willow Way", line2="Apt 4C", city="Miami", state="FL", zip="33101", country="USA"),
    dict(line1="258 Spruce St", line2=None, city=" Boise", state="ID", zip="83702", country="USA"),
    dict(line1="369 Laurel Dr", line2="Unit 8", city="Austin", state="TX", zip="78701", country="USA"),
    dict(line1="741 Sycamore Ave", line2=None, city="Madison", state="WI", zip="53703", country="USA"),
    # Abnormal
    dict(line1="13 Moonbeam Way", line2="Shack 3", city="Roswell", state="NM", zip="88201", country="USA"),
    dict(line1="1 Gator Swamp Rd", line2="Trailer 9", city="Gainesville", state="FL", zip="32601", country="USA"),
    dict(line1="666 Haunted Hollow", line2=None, city="Salem", state="OR", zip="97301", country="USA"),
    dict(line1="0 Nowhere Ln", line2="Box 42", city="Eureka", state="NV", zip="89316", country="USA"),
    dict(line1="9999 Unicorn Trail", line2="Glitter Barn", city="Bozeman", state="MT", zip="59715", country="USA"),
    # Null addresses
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
    dict(line1=None, line2=None, city=None, state=None, zip=None, country=None),
]

DOMAIN_LIST = [
    "gmail.com",
    "yahoo.com",
    "hotmail.com",
    "outlook.com",
    "aol.com",
    "icloud.com",
    "protonmail.com",
    "zoho.com",
    "mail.com",
    "gmx.com",
    "yandex.com",
    "mail.ru",
    "inbox.com",
    "fastmail.com",
    "tutanota.com",
    "riseup.net",
]

# Configuration for test data generation
NUMBER_AGENTS_FOR_IDENTITY = [1, 1, 1, 2, 2]

class Identity:
    @classmethod
    def list(cls):
        cursor = DB.cursor()
        cursor.execute("""
            SELECT
                identity_uuid,
                identity_type,
                name,
                email,
                note,
                avatar_uri,
                active,
                create_ts
            FROM
                auth_fence.identity
            ORDER BY
                name
        """)

        identities = []
        for row in cursor.fetchall():
            identities.append(Identity(row))

        return identities

    def __init__(self, tuple):
        self.uuid = tuple[0]
        self.type = tuple[1]
        self.name = tuple[2]
        self.email = tuple[3]
        self.note = tuple[4]
        self.avatar_uri = tuple[5]
        self.active = tuple[6]
        self.create_ts = tuple[7]


class RrrAdminIdentity:
    def __init__(self, *, identity_uuid, seed):
        self.identity_uuid = identity_uuid
        self.create_ts = generate_timestamp(seed)

        # All permissions should be true
        self.perm_api_usage = True
        self.perm_web_usage = True
        self.perm_agent_read = True
        self.perm_agent_write = True

        # Insert into the database
        CURSOR.execute("""
            INSERT INTO rrr_admin.identity (
                identity_uuid,
                create_ts,
                perm_api_usage,
                perm_web_usage,
                perm_agent_read,
                perm_agent_write
            ) VALUES (
                %(identity_uuid)s,
                %(create_ts)s,
                %(perm_api_usage)s,
                %(perm_web_usage)s,
                %(perm_agent_read)s,
                %(perm_agent_write)s
            )
        """, {
            'identity_uuid': self.identity_uuid,
            'create_ts': self.create_ts,
            'perm_api_usage': self.perm_api_usage,
            'perm_web_usage': self.perm_web_usage,
            'perm_agent_read': self.perm_agent_read,
            'perm_agent_write': self.perm_agent_write
        })
        DB.commit()

    def __repr__(self):
        return f'RrrAdminIdentity {self.identity_uuid} (read: {self.perm_agent_read}, write: {self.perm_agent_write})'


class RrrAgent:
    def __init__(self, *, identity_uuid, seed):
        self.agent_uuid = generate_uuidv7(seed)
        self.identity_uuid = identity_uuid
        self.create_ts = generate_timestamp(seed)

        # Generate personal information
        name = pick_one_of(seed, NAME_LIST)
        self.first_name = name['first_name']
        self.last_name = name['last_name']
        self.email = generate_email(seed, self.first_name.lower())
        self.phone = generate_phone(seed)

        # Generate address
        address = pick_one_of(seed, ADDRESS_LIST)
        self.address1 = address['line1']
        self.address2 = address['line2']
        self.city = address['city']
        self.state = address['state']
        self.zip = address['zip']
        self.country = address['country']

        self.active = active_or_not(seed)
        self.admin_note = generate_note(seed)
        self.data = {}

        # Insert into the database
        CURSOR.execute("""
            INSERT INTO rrr.agent (
                agent_uuid,
                identity_uuid,
                create_ts,
                first_name,
                last_name,
                email,
                phone,
                address1,
                address2,
                city,
                state,
                zip,
                active,
                admin_note,
                data
            ) VALUES (
                %(agent_uuid)s,
                %(identity_uuid)s,
                %(create_ts)s,
                %(first_name)s,
                %(last_name)s,
                %(email)s,
                %(phone)s,
                %(address1)s,
                %(address2)s,
                %(city)s,
                %(state)s,
                %(zip)s,
                %(active)s,
                %(admin_note)s,
                %(data)s::jsonb
            )
        """, {
            'agent_uuid': self.agent_uuid,
            'identity_uuid': self.identity_uuid,
            'create_ts': self.create_ts,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'email': self.email,
            'phone': self.phone,
            'address1': self.address1,
            'address2': self.address2,
            'city': self.city,
            'state': self.state,
            'zip': self.zip,
            'active': self.active,
            'admin_note': self.admin_note,
            'data': json.dumps(self.data)
        })
        DB.commit()

    def __repr__(self):
        return f'RrrAgent {self.first_name} {self.last_name}'


def main():
    try:
        print("Connecting to RRR database...")
        print("Connected to RRR database successfully")

        # Create a cursor and execute truncate statements
        CURSOR.execute("TRUNCATE TABLE rrr.agent CASCADE")
        CURSOR.execute("TRUNCATE TABLE rrr_admin.identity CASCADE")
        DB.commit()

        print("Truncated existing test data")

    except Exception as e:
        print(f"Error during truncate: {e}")
        raise

    # Get all identities from auth_fence
    identities = Identity.list()
    print(f"Found {len(identities)} identities")

    # Create admin permissions for all identities
    for i, identity in enumerate(identities):
        RrrAdminIdentity(identity_uuid=identity.uuid, seed=f'{SEED}-admin-{i}')

    # Create agents for some identities
    for i, identity in enumerate(identities):
        for n in range(pick_one_of(f'{SEED}-{i}', NUMBER_AGENTS_FOR_IDENTITY)):
            RrrAgent(identity_uuid=identity.uuid, seed=f'{SEED}-agent-{i}-{n}')

    print("RRR test data generation completed successfully")


if __name__ == "__main__":
    main()

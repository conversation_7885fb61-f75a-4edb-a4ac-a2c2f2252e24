#[approck::api]
pub mod client_dashboard {
    use granite::return_authorization_error;
    use granite::{Datelike, Decimal};
    use num_traits::cast::ToPrimitive;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Client {
        pub client_uuid: Uuid,
        pub name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub annual_insurance_premium: Option<Decimal>,
        pub annual_insurance_base: Option<Decimal>,
        pub annual_insurance_pua: Option<Decimal>,
        pub active: bool,
        pub is_demo: bool,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client: Client,
        pub debt_info: DebtInfo,
        // Legacy fields for compatibility with edit page
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub name: String,
        pub create_ts: DateTimeUtc,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,

        pub note: Option<String>,
        pub active: bool,
    }

    impl Output {
        pub fn address(&self) -> String {
            let mut parts = Vec::new();

            if let Some(addr1) = &self.address1 {
                parts.push(addr1.clone());
            }
            if let Some(addr2) = &self.address2 {
                parts.push(addr2.clone());
            }
            if let Some(city) = &self.city {
                parts.push(city.clone());
            }
            if let Some(state) = &self.state {
                parts.push(state.clone());
            }
            if let Some(zip) = &self.zip {
                parts.push(zip.clone());
            }

            parts.join(" ")
        }
    }

    #[granite::gtype(ApiOutput)]
    pub struct DebtInfoDebtItem {
        pub client_debt_uuid: Uuid,
        pub debt_name: String,
        pub full_name: String,
        pub balance: Decimal,
        pub interest_rate: Decimal,
        pub monthly_payment: Decimal,
        pub effective_interest_cost_percent: Decimal,
        pub is_paid_off_as_of_today: bool,
        pub payoff_date_formatted: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct DebtInfo {
        pub start_year: u32,
        pub start_month: u32,
        pub debt_balance: Option<Decimal>,
        pub annual_budget: Option<Decimal>,
        pub minimum_monthly_payment: Option<Decimal>,
        pub total_paid: Decimal,
        pub total_unpaid: Decimal,
        pub esimated_cash_value: Decimal,
        pub debt_list: Vec<DebtInfoDebtItem>,
    }

    // Helper function to format date as "YY, Mon"
    pub fn date_formatted_yy_mon(year: u32, month: u32) -> String {
        let month_names = [
            "", "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec",
        ];
        let month_name = month_names.get(month as usize).unwrap_or(&"");
        format!("'{:02}, {}", year % 100, month_name)
    }

    // Helper function to format decimal with fallback
    pub fn format_decimal_optional(value: Option<Decimal>, fallback: &str) -> String {
        match value {
            Some(v) => v.to_string(),
            None => fallback.to_string(),
        }
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        // Load wizard data which includes all client, debt, and budget information
        let wizard = df4l_zero::client::wizard::Wizard::load(app, &dbcx, input.client_uuid).await?;

        // Get basic client info from database
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_uuid: Uuid,
                advisor_uuid: Uuid,
                first_name: String,
                last_name: String,
                name: String,
                create_ts: DateTimeUtc,
                email: Option<String>,
                phone: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,
                note: Option<String>,
                active: bool,
                is_demo: bool,
            };
            SELECT
                client_uuid,
                advisor_uuid,
                first_name,
                last_name,
                first_name || " " || last_name AS name,
                create_ts,
                email,
                phone,
                address1,
                address2,
                city,
                state,
                zip,
                note,
                active,
                is_demo
            FROM
                df4l.client
            WHERE true
                AND client_uuid = $client_uuid::uuid
        )
        .await?;

        // Store the recent client views per identity and advisor
        if let Some(identity_uuid) = identity.identity_uuid() {
            // get the current timestamp as an integer
            let current_ts = granite::Utc::now().timestamp_millis() as f64;
            let key = format!("RecentClient:{}:{}", identity_uuid, row.advisor_uuid);
            let mut rediscx = app.redis_dbcx().await?;
            rediscx
                .zadd(&key, row.client_uuid.to_string(), current_ts)
                .await?;
        }

        // Get debt data from wizard (includes merged CRS and manual debts)
        let debt_rows = &wizard.debts_input.unwrap_or_default();

        // Calculate insurance premiums from wizard PUA data
        let annual_insurance_premium = wizard
            .pua_input
            .monthly_total_premium
            .map(|p| p * Decimal::new(12, 0));
        let annual_insurance_pua = wizard
            .budget_input
            .budget_pua_contribution
            .map(|p| p * Decimal::new(12, 0));
        let annual_insurance_base = match (annual_insurance_premium, annual_insurance_pua) {
            (Some(premium), Some(pua)) => Some(premium - pua),
            (Some(premium), None) => Some(premium),
            _ => None,
        };

        // Create client data with real insurance values
        let client = Client {
            client_uuid: row.client_uuid,
            name: row.name.clone(),
            email: row.email.clone(),
            phone: row.phone.clone(),
            annual_insurance_premium,
            annual_insurance_base,
            annual_insurance_pua,
            active: row.active,
            is_demo: row.is_demo,
        };

        // Create debt items from wizard debt data (includes merged CRS and manual debts)
        let mut debt_list = Vec::new();
        for debt_row in debt_rows {
            let balance = debt_row.balance.unwrap_or(Decimal::ZERO);
            let interest_rate = debt_row.annual_interest_percentage.unwrap_or(Decimal::ZERO);
            let monthly_payment = debt_row.monthly_payment_amount.unwrap_or(Decimal::ZERO);

            // Skip empty/meaningless debt records
            // Only include debts that have either a meaningful name or a positive balance
            if debt_row.name.is_none() && balance <= Decimal::ZERO {
                continue;
            }

            let debt_name = debt_row
                .name
                .clone()
                .unwrap_or_else(|| "Unnamed Debt".to_string());

            // Calculate effective interest cost percent (same as interest rate for now)
            let effective_interest_cost_percent = interest_rate;

            // Determine if debt is paid off (balance is zero or negative)
            let is_paid_off_as_of_today = balance <= Decimal::ZERO;

            // Calculate simple payoff date (this is a simplified calculation)
            let payoff_date_formatted = if is_paid_off_as_of_today {
                Some("Paid Off".to_string())
            } else if monthly_payment > Decimal::ZERO && balance > Decimal::ZERO {
                // Simple calculation: balance / monthly_payment = months to payoff
                let months_to_payoff =
                    (balance / monthly_payment).to_f64().unwrap_or(0.0).ceil() as u32;
                let current_date = granite::Utc::now().date_naive();
                current_date
                    .checked_add_months(chrono::Months::new(months_to_payoff))
                    .map(|payoff_date| {
                        date_formatted_yy_mon(payoff_date.year_ce().1, payoff_date.month0() + 1)
                    })
            } else {
                None
            };

            debt_list.push(DebtInfoDebtItem {
                client_debt_uuid: debt_row.client_debt_uuid,
                debt_name: debt_name.clone(),
                full_name: debt_name,
                balance,
                interest_rate,
                monthly_payment,
                effective_interest_cost_percent,
                is_paid_off_as_of_today,
                payoff_date_formatted,
            });
        }

        // Calculate totals from real data
        let total_balance = debt_list.iter().map(|d| d.balance).sum::<Decimal>();
        let minimum_monthly_payment = debt_list.iter().map(|d| d.monthly_payment).sum::<Decimal>();

        // Calculate annual budget from wizard's budget data
        let monthly_budget = wizard.budget_input.budget_extra_debts.unwrap_or_default()
            + wizard.budget_input.budget_extra_savings.unwrap_or_default()
            + wizard
                .budget_input
                .budget_extra_retirement
                .unwrap_or_default()
            + wizard.budget_input.budget_extra_surplus.unwrap_or_default();
        let annual_budget = if monthly_budget > Decimal::ZERO {
            Some(monthly_budget * Decimal::new(12, 0))
        } else {
            None
        };

        let total_paid = Decimal::ZERO;
        let total_unpaid = total_balance;
        let estimated_cash_value = Decimal::ZERO;

        // Get current date for start year/month
        let current_date = granite::Utc::now().date_naive();

        // Create debt info with real data
        let debt_info = DebtInfo {
            start_year: current_date.year_ce().1,
            start_month: current_date.month0() + 1,
            debt_balance: Some(total_balance),
            annual_budget,
            minimum_monthly_payment: Some(minimum_monthly_payment),
            total_paid,
            total_unpaid,
            esimated_cash_value: estimated_cash_value,
            debt_list,
        };

        Ok(Output {
            client,
            debt_info,
            // Legacy fields for compatibility
            client_uuid: row.client_uuid,
            advisor_uuid: row.advisor_uuid,
            first_name: row.first_name,
            last_name: row.last_name,
            name: row.name,
            create_ts: row.create_ts,
            email: row.email,
            phone: row.phone,
            address1: row.address1,
            address2: row.address2,
            city: row.city,
            state: row.state,
            zip: row.zip,

            note: row.note,
            active: row.active,
        })
    }
}

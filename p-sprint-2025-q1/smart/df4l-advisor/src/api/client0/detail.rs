#[approck::api]
pub mod detail {
    use granite::Decimal;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_uuid: Uuid,
        pub advisor_uuid: Option<Uuid>,
        pub first_name: String,
        pub last_name: String,
        pub name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub phone2: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,

        pub active: bool,
        pub annual_insurance_premium: Option<Decimal>,
        pub annual_insurance_pua: Option<Decimal>,
        pub net_cash_at_end: Option<Decimal>,
        pub debt_free_start_date: Option<DateUtc>,
    }

    // Format decimal optional value as $100/mo $1,200/yr or $100.50/mo $1,200.50/yr if cents exist
    fn format_decimal_optional(value: Option<Decimal>) -> String {
        if let Some(value) = value {
            let monthly = value / Decimal::from(12);
            let yearly = value;

            // Check if monthly has cents
            let monthly_str = if monthly.fract().is_zero() {
                format!("${}/mo", monthly.trunc())
            } else {
                format!("${monthly:.2}/mo")
            };

            // Check if yearly has cents
            let yearly_str = if yearly.fract().is_zero() {
                format!("${}/yr", yearly.trunc())
            } else {
                format!("${yearly:.2}/yr")
            };

            format!("{monthly_str} {yearly_str}")
        } else {
            "".to_string()
        }
    }

    impl Output {
        pub fn advisor_name(&self) -> String {
            if let Some(advisor_uuid) = self.advisor_uuid {
                format!("Advisor {advisor_uuid}")
            } else {
                "No Advisor".to_string()
            }
        }

        pub fn address(&self) -> String {
            let mut parts = Vec::new();

            if let Some(addr1) = &self.address1 {
                parts.push(addr1.clone());
            }
            if let Some(addr2) = &self.address2 {
                parts.push(addr2.clone());
            }
            if let Some(city) = &self.city {
                parts.push(city.clone());
            }
            if let Some(state) = &self.state {
                parts.push(state.clone());
            }
            if let Some(zip) = &self.zip {
                parts.push(zip.clone());
            }

            parts.join(" ")
        }

        pub fn annual_insurance_premium_formatted(&self) -> String {
            format_decimal_optional(self.annual_insurance_premium)
        }

        pub fn annual_insurance_pua_formatted(&self) -> String {
            format_decimal_optional(self.annual_insurance_pua)
        }

        pub fn total_insurance_premium_formatted(&self) -> String {
            // if both are Some, add them together and format
            // if either is None, return empty string
            if let (Some(annual_insurance_premium), Some(annual_insurance_pua)) =
                (self.annual_insurance_premium, self.annual_insurance_pua)
            {
                format_decimal_optional(Some(annual_insurance_premium + annual_insurance_pua))
            } else {
                "".to_string()
            }
        }
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_uuid: Uuid,
                advisor_uuid: Option<Uuid>,
                first_name: String,
                last_name: String,
                name: String,
                email: Option<String>,
                phone: Option<String>,
                phone2: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,

                active: bool,
                annual_insurance_premium: Option<Decimal>,
                annual_insurance_pua: Option<Decimal>,
                net_cash_at_end: Option<Decimal>,
                debt_free_start_date: Option<DateUtc>,
            };
            SELECT
                client_uuid,
                advisor_uuid,
                first_name,
                last_name,
                first_name || " " || last_name AS name,
                email,
                phone,
                phone2,
                address1,
                address2,
                city,
                state,
                zip,

                active,
                annual_insurance_premium,
                annual_insurance_pua,
                net_cash_at_end,
                debt_free_start_date

            FROM
                df4l.client0
            WHERE true
                AND client_uuid = $client_uuid::uuid
        )
        .await?;

        Ok(Output {
            client_uuid: row.client_uuid,
            advisor_uuid: row.advisor_uuid,
            first_name: row.first_name,
            last_name: row.last_name,
            name: row.name,
            email: row.email,
            phone: row.phone,
            phone2: row.phone2,
            address1: row.address1,
            address2: row.address2,
            city: row.city,
            state: row.state,
            zip: row.zip,

            active: row.active,
            annual_insurance_premium: row.annual_insurance_premium,
            annual_insurance_pua: row.annual_insurance_pua,
            net_cash_at_end: row.net_cash_at_end,
            debt_free_start_date: row.debt_free_start_date,
        })
    }
}

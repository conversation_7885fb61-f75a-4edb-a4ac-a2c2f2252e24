#[approck::api]
pub mod edit {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub client_uuid: Uuid,
        pub name: Option<String>,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub phone2: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
                $name: &input.name,
                $email: &input.email,
                $phone: &input.phone,
                $phone2: &input.phone2,
            };
            UPDATE
                df4l.client0
            SET
                name = $name,
                email = $email,
                phone = $phone,
                phone2 = $phone2

            WHERE
                client_uuid = $client_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: df4l_zero::ml_advisor_client0_details(
                input.advisor_uuid,
                input.client_uuid,
            ),
            message: "Aadvisor updated".into(),
        }))
    }
}

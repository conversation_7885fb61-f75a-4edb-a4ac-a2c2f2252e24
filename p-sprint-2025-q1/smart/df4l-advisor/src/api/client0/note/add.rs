#[approck::api]
pub mod add {
    use granite::{ResultExt, return_authorization_error};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub note: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_note_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let mut dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        // Transaction block
        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $client_uuid: &input.client_uuid,
                    $note: &input.note,
                };
                row = {
                    client_note_uuid: Uuid,
                    advisor_uuid: Uuid,
                };

                INSERT INTO
                    df4l.client0_note
                    (
                        client_uuid,
                        note
                    )
                VALUES
                    (
                        $client_uuid,
                        $note
                    )
                // We need the advisor_uuid to power redirect
                RETURNING
                    client_note_uuid,
                    (
                        SELECT advisor_uuid
                        FROM df4l.client0
                        WHERE client_uuid = $client_uuid::uuid
                    ) AS advisor_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;

            row
        };

        Ok(Response::Output(Output {
            client_note_uuid: row.client_note_uuid,
            detail_url: df4l_zero::ml_advisor_client0_note_list(
                row.advisor_uuid,
                input.client_uuid,
            ),
        }))
    }
}

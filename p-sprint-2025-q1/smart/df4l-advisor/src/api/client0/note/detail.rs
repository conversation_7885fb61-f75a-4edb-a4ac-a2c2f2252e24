#[approck::api]
pub mod detail {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub client_note_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_note_uuid: Uuid,
        pub client_uuid: Uuid,
        pub create_ts: DateTimeUtc,
        pub note: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_note_uuid: &input.client_note_uuid,
            };
            row = {
                client_note_uuid: Uuid,
                client_uuid: Uuid,
                create_ts: DateTimeUtc,
                note: String,
            };
            SELECT
                client_note_uuid,
                client_uuid,
                create_ts,
                note
            FROM
                df4l.client0_note
            WHERE true
                AND client_note_uuid = $client_note_uuid::uuid
        )
        .await?;

        Ok(Output {
            client_note_uuid: row.client_note_uuid,
            client_uuid: row.client_uuid,
            create_ts: row.create_ts,
            note: row.note,
        })
    }
}

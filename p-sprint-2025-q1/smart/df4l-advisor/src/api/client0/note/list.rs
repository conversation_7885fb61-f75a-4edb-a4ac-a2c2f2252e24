#[approck::api]
pub mod list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub note_list: Vec<Note>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Note {
        pub client_note_uuid: Uuid,
        pub client_uuid: Uuid,
        pub create_ts: DateTimeUtc,
        pub note: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_note_uuid: Uuid,
                client_uuid: Uuid,
                create_ts: DateTimeUtc,
                note: String,
            };
            SELECT
                client_note_uuid,
                client_uuid,
                create_ts,
                note
            FROM
                df4l.client0_note
            WHERE true
                AND client_uuid = $client_uuid::uuid // SECON
            ORDER BY
                create_ts DESC
        )
        .await?;

        Ok(Output {
            note_list: rows
                .into_iter()
                .map(|r| Note {
                    client_note_uuid: r.client_note_uuid,
                    client_uuid: r.client_uuid,
                    create_ts: r.create_ts,
                    note: r.note,
                })
                .collect(),
        })
    }
}

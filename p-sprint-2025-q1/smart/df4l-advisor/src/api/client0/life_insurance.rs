#[approck::api]
pub mod life_insurance {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub client_uuid: Uuid,
        pub net_cash_at_end: Option<Decimal>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
                $net_cash_at_end: &input.net_cash_at_end,
            };
            UPDATE
                df4l.client0
            SET
                net_cash_at_end = $net_cash_at_end

            WHERE
                client_uuid = $client_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: df4l_zero::ml_advisor_client0_lfe_insurance(
                input.advisor_uuid,
                input.client_uuid,
            ),
            message: "Aadvisor updated".into(),
        }))
    }
}

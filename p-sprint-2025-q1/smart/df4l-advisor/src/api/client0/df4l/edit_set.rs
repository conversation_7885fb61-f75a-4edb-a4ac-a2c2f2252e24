#[approck::api]
pub mod edit_set {

    use granite::return_authorization_error;
    use serde::{Deserialize, Serialize};

    //  {"Month": 48, "Amount": "21739"}
    #[granite::gtype(ApiInput)]
    #[derive(Serialize, Deserialize)]
    pub struct ExmExa {
        pub month: i32,
        pub amount: Decimal,
    }

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub client_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub phone2: Option<String>,
        pub annual_insurance_premium: Option<Decimal>,
        pub annual_insurance_pua: Option<Decimal>,
        pub monthly_budget: Option<Decimal>,
        pub debt_free_extra_pua_list: Vec<ExmExa>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
                $first_name: &input.first_name,
                $last_name: &input.last_name,
                $email: &input.email,
                $phone: &input.phone,
                $phone2: &input.phone2,
                $annual_insurance_premium: &input.annual_insurance_premium,
                $annual_insurance_pua: &input.annual_insurance_pua,
                $monthly_budget: &input.monthly_budget,
                // json encode exm_exa_list
                $debt_free_extra_pua_list: &granite::json!(&input.debt_free_extra_pua_list),
            };
            UPDATE
                df4l.client0
            SET
                first_name = $first_name,
                last_name = $last_name,
                email = $email,
                phone = $phone,
                phone2 = $phone2,
                annual_insurance_premium = $annual_insurance_premium,
                annual_insurance_pua = $annual_insurance_pua,
                monthly_budget = $monthly_budget,
                debt_free_extra_pua_list = $debt_free_extra_pua_list::jsonb
            WHERE
                client_uuid = $client_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: df4l_zero::ml_advisor_client0_details(
                input.advisor_uuid,
                input.client_uuid,
            ),
            message: "Aadvisor updated".into(),
        }))
    }
}

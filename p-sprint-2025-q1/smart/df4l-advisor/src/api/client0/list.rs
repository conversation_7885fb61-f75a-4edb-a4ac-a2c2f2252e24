#[approck::api]
pub mod advisor_client_list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub keyword: Option<String>,
        pub active: Option<bool>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_list: Vec<Client>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Client {
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
        pub advisor_esid: Option<String>,
        pub create_ts: DateTimeUtc,
        pub name: String,
        pub active: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.client_list(input.advisor_uuid) {
            return_authorization_error!("insufficient permissions to client list");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
                $keyword: &input.keyword,
                $active: &input.active,
            };
            row = {
                client_uuid: Uuid,
                advisor_uuid: Uuid,
                advisor_esid: Option<String>,
                create_ts: DateTimeUtc,
                name: String,
                active: bool,
            };
            SELECT
                c.client_uuid,
                c.advisor_uuid,
                a.advisor_esid,
                c.create_ts,
                c.first_name || " " || c.last_name AS name,
                c.active
            FROM
                df4l.client0 c
            INNER JOIN
                df4l.advisor as a USING(advisor_uuid)
            WHERE true
                AND c.advisor_uuid = $advisor_uuid
                AND (
                    $keyword::text IS NULL
                    OR
                    c.first_name ILIKE "%" || $keyword::text || "%"
                    OR
                    c.last_name ILIKE "%" || $keyword::text || "%"
                    OR
                    (c.first_name || " " || c.last_name) ILIKE "%" || $keyword::text || "%"
                )
                AND ($active::bool IS NULL OR c.active = $active::bool)
            ORDER BY
                c.first_name, c.last_name
        )
        .await?;

        Ok(Output {
            client_list: rows
                .into_iter()
                .map(|r| Client {
                    client_uuid: r.client_uuid,
                    advisor_uuid: r.advisor_uuid,
                    advisor_esid: r.advisor_esid,
                    create_ts: r.create_ts,
                    name: r.name,
                    active: r.active,
                })
                .collect(),
        })
    }
}

#[approck::api]
pub mod detail {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub client_debt_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_debt_uuid: Uuid,
        pub client_uuid: Uuid,
        pub name: String,
        pub balance: Option<Decimal>,
        pub balance_date: Option<DateUtc>,
        pub interest_rate: Option<Decimal>,
        pub monthly_payment: Option<Decimal>,
        pub active: bool,
        pub note: Option<String>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_debt_uuid: &input.client_debt_uuid,
            };
            row = {
                client_debt_uuid: Uuid,
                client_uuid: Uuid,
                name: String,
                balance: Option<Decimal>,
                balance_date: Option<DateUtc>,
                interest_rate: Option<Decimal>,
                monthly_payment: Option<Decimal>,
                active: bool,
                note: Option<String>,
            };
            SELECT
                cd.client_debt_uuid,
                cd.client_uuid,
                cd.name,
                cd.balance,
                cd.balance_date,
                cd.interest_rate,
                cd.monthly_payment,
                cd.active,
                cd.note
            FROM
                df4l.client0_debt AS cd
            WHERE
                cd.client_debt_uuid = $client_debt_uuid
        )
        .await?;

        let output = Output {
            client_debt_uuid: row.client_debt_uuid,
            client_uuid: row.client_uuid,
            name: row.name,
            balance: row.balance,
            balance_date: row.balance_date,
            interest_rate: row.interest_rate,
            monthly_payment: row.monthly_payment,
            active: row.active,
            note: row.note,
        };

        Ok(output)
    }
}

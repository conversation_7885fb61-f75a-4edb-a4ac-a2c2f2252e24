#[approck::api]
pub mod edit {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_debt_uuid: Uuid,
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
        pub name: String,
        pub balance: Option<Decimal>,
        pub interest_rate: Option<Decimal>,
        pub monthly_payment: Option<Decimal>,
        pub note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_debt_uuid: &input.client_debt_uuid,
                $client_uuid: &input.client_uuid,
                $name: &input.name,
                $balance: &input.balance,
                $interest_rate: &input.interest_rate,
                $monthly_payment: &input.monthly_payment,
                $note: &input.note,
            };
            UPDATE
                df4l.client0_debt
            SET
                name = $name,
                balance = $balance,
                interest_rate = $interest_rate,
                monthly_payment = $monthly_payment,
                note = $note
            WHERE
                client_uuid = $client_uuid
                AND client_debt_uuid = $client_debt_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: df4l_zero::ml_advisor_client0_debt_details(
                input.advisor_uuid,
                input.client_uuid,
                input.client_debt_uuid,
            ),
            message: "Client debt updated".into(),
        }))
    }
}

#[approck::api]
pub mod del {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub client_uuid: Uuid,
        pub client_debt_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_debt_uuid: &input.client_debt_uuid,
                $client_uuid: &input.client_uuid,
            };
            DELETE FROM
                df4l.client0_debt
            WHERE
                client_uuid = $client_uuid
                AND client_debt_uuid = $client_debt_uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: df4l_zero::ml_advisor_client0_debt_list(
                input.advisor_uuid,
                input.client_uuid,
            ),
            message: "Client debt deleted".into(),
        }))
    }
}

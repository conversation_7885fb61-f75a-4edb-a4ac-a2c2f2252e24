#[approck::api]
pub mod client_debt_free_info {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Client {
        pub client_uuid: Uuid,
        pub name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub phone2: Option<String>,
        pub monthly_budget: Option<Decimal>,
        pub annual_insurance_premium: Option<Decimal>,
        pub annual_insurance_base: Option<Decimal>,
        pub annual_insurance_pua: Option<Decimal>,
        pub net_cash_at_end: Option<Decimal>,
        pub debt_free_start_date: Option<DateUtc>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct ClientDebtItem {
        pub client_debt_uuid: Uuid,
        pub name: String,
        pub balance: Option<Decimal>,
        pub interest_rate: Option<Decimal>,
        pub monthly_payment: Option<Decimal>,
        pub active: bool,
        pub note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client: Client,
        pub debt_list: Vec<ClientDebtItem>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_uuid: Uuid,
                advisor_uuid: Option<Uuid>,
                name: String,
                email: Option<String>,
                phone: Option<String>,
                phone2: Option<String>,
                monthly_budget: Option<Decimal>,
                annual_insurance_premium: Option<Decimal>,
                annual_insurance_pua: Option<Decimal>,
                net_cash_at_end: Option<Decimal>,
                debt_free_start_date: Option<DateUtc>,
            };
            SELECT
                client_uuid,
                advisor_uuid,
                first_name || " " || last_name AS name,
                email,
                phone,
                phone2,
                monthly_budget,
                annual_insurance_premium,
                annual_insurance_pua,
                net_cash_at_end,
                debt_free_start_date
            FROM
                df4l.client0
            WHERE true
            AND advisor_uuid = $advisor_uuid::uuid
            AND client_uuid = $client_uuid::uuid
        )
        .await?;

        let rows = granite::pg_row_vec!(
        db = dbcx;
        args = {
            $client_uuid: &input.client_uuid,
        };
        row = {
            client_debt_uuid: Uuid,
            name: String,
            balance: Option<Decimal>,
            interest_rate: Option<Decimal>,
            monthly_payment: Option<Decimal>,
            active: bool,
            note: Option<String>,
        };
        SELECT
            cd.client_debt_uuid,
            cd.name,
            cd.balance,
            cd.interest_rate,
            cd.monthly_payment,
            cd.active,
            cd.note
        FROM
            df4l.client0_debt AS cd
        WHERE
            cd.client_uuid = $client_uuid::uuid
            AND cd.active = true
        )
        .await?;

        Ok(Output {
            client: Client {
                client_uuid: row.client_uuid,
                name: row.name,
                email: row.email,
                phone: row.phone,
                phone2: row.phone2,
                monthly_budget: row.monthly_budget,
                net_cash_at_end: row.net_cash_at_end,
                annual_insurance_premium: row.annual_insurance_premium,
                annual_insurance_pua: row.annual_insurance_pua,
                annual_insurance_base: if let (Some(premium), Some(pua)) =
                    (row.annual_insurance_premium, row.annual_insurance_pua)
                {
                    Some(premium - pua)
                } else {
                    None
                },
                debt_free_start_date: row.debt_free_start_date,
            },
            debt_list: rows
                .into_iter()
                .map(|r| ClientDebtItem {
                    client_debt_uuid: r.client_debt_uuid,
                    name: r.name,
                    balance: r.balance,
                    interest_rate: r.interest_rate,
                    monthly_payment: r.monthly_payment,
                    active: r.active,
                    note: r.note,
                })
                .collect(),
        })
    }
}

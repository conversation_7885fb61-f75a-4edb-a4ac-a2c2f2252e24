#[approck::api]
pub mod advisor_setup_checklist {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct SetupItem {
        pub name: String,
        pub complete: bool,
        pub action_url: String,
    }

    #[granite::gtype(ApiOutput)]
    pub enum Output {
        HasItems(Vec<SetupItem>),
        NoItems,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.advisor_read(input.advisor_uuid) {
            return_authorization_error!("Insufficient permissions to advisor read");
        }

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                gbu_complete: bool,
                email_complete: bool,
                phone_complete: bool,
                address_complete: bool,
                statelic_complete: bool,
            };
            SELECT
                gbu_advisor_esid IS NOT NULL as gbu_complete,
                email IS NOT NULL as email_complete,
                phone IS NOT NULL as phone_complete,
                address1 IS NOT NULL AND city IS NOT NULL AND state IS NOT NULL AND zip IS NOT NULL as address_complete,
                EXISTS (
                    SELECT
                    FROM df4l.advisor_statelic
                    WHERE advisor_uuid = $advisor_uuid
                ) as statelic_complete
            FROM
                df4l.advisor
            WHERE
                advisor_uuid = $advisor_uuid
        )
        .await?;

        let mut setup_items = vec![];

        // Only add incomplete items to the setup list
        setup_items.push(SetupItem {
            name: "Email Address".to_string(),
            complete: row.email_complete,
            action_url: df4l_zero::ml_myaccount_advisor_email(input.advisor_uuid),
        });

        setup_items.push(SetupItem {
            name: "Mobile Number".to_string(),
            complete: row.phone_complete,
            action_url: df4l_zero::ml_myaccount_advisor_phone(input.advisor_uuid),
        });

        setup_items.push(SetupItem {
            name: "Address".to_string(),
            complete: row.address_complete,
            action_url: df4l_zero::ml_myaccount_advisor_address(input.advisor_uuid),
        });

        setup_items.push(SetupItem {
            name: "GBU Agent ID".to_string(),
            complete: row.gbu_complete,
            action_url: df4l_zero::ml_myaccount_advisor_gbu(input.advisor_uuid),
        });

        setup_items.push(SetupItem {
            name: "State Licenses".to_string(),
            complete: row.statelic_complete,
            action_url: df4l_zero::ml_myaccount_advisor_statelic(input.advisor_uuid),
        });

        // if any are incomplete, send HasItems
        if setup_items.iter().any(|item| !item.complete) {
            Ok(Output::HasItems(setup_items))
        } else {
            Ok(Output::NoItems)
        }
    }
}

#[approck::api]
pub mod dashboard {
    use granite::Uuid;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub active_legacy_clients: i64,
        pub active_clients: i64,
        pub pending_clients_count: i64,
        pub name: String,
        pub gbu_agent_id: Option<String>,
        pub statelic: Vec<String>,
        pub identity_uuid: Uuid,
        pub recent_clients: Vec<RecentClient>,
        pub pending_clients: Vec<PendingClient>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct RecentClient {
        pub client_uuid: Uuid,
        pub name: String,
        pub client_type: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct PendingClient {
        pub client_uuid: Uuid,
        pub name: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.advisor_read(input.advisor_uuid) {
            return_authorization_error!(
                "insufficient permissions to advisor {}",
                input.advisor_uuid
            );
        }

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                first_name: String,
                last_name: String,
                gbu_advisor_esid: Option<String>,
                active_legacy_clients: i64,
                active_clients: i64,
                pending_clients_count: i64,
                statelic: Vec<String>,
                identity_uuid: Uuid,
            };
            SELECT
                a.first_name,
                a.last_name,
                a.gbu_advisor_esid,
                (SELECT count(*) FROM df4l.client0 WHERE advisor_uuid = a.advisor_uuid AND active ) AS active_legacy_clients,
                (SELECT count(*) FROM df4l.client_active ca WHERE ca.advisor_uuid = a.advisor_uuid) AS active_clients,
                (SELECT count(*) FROM df4l.client_pending cp INNER JOIN df4l.client c USING(client_uuid) WHERE c.advisor_uuid = a.advisor_uuid) AS pending_clients_count,
                COALESCE(
                    (
                        SELECT array_agg(sl.label ORDER BY als.state_code)
                        FROM df4l.advisor_statelic als
                        INNER JOIN df4l.statelic sl ON als.state_code = sl.state_code
                        WHERE als.advisor_uuid = a.advisor_uuid
                    ),
                    ARRAY[]::text[]
                ) AS statelic,
                a.identity_uuid
            FROM
                df4l.advisor a
            WHERE
                a.advisor_uuid = $advisor_uuid::uuid
        )
        .await?;

        let full_name = format!("{} {}", row.first_name, row.last_name);

        // Get recent clients from Redis
        let recent_clients = if let Some(identity_uuid) = identity.identity_uuid() {
            let key = format!("RecentClient:{}:{}", identity_uuid, input.advisor_uuid);
            let mut rediscx = app.redis_dbcx().await?;

            let all_client_uuids: Vec<String> = rediscx
                .zrangebyscore_limit(&key, 0.0, f64::MAX, 0, 50)
                .await
                .unwrap_or_default();

            // Parse UUIDs and collect valid ones, preserving order
            let mut valid_client_uuids = Vec::new();
            for client_uuid_str in all_client_uuids.iter().rev() {
                if let Ok(client_uuid) = client_uuid_str.parse::<Uuid>() {
                    valid_client_uuids.push(client_uuid);
                }
            }

            // Fetch all client data in a single query if we have valid UUIDs
            if !valid_client_uuids.is_empty() {
                let client_rows = granite::pg_row_vec!(
                    db = dbcx;
                    args = {
                        $client_uuids: &valid_client_uuids,
                    };
                    row = {
                        client_uuid: Uuid,
                        name: String,
                        client_type: String,
                    };

                    SELECT
                        client_uuid,
                        first_name || " " || last_name AS name,
                        client_type::text AS client_type
                    FROM
                        df4l.client
                    WHERE
                        client_uuid = ANY($client_uuids::uuid[])
                        AND active
                    ORDER BY
                        array_position($client_uuids::uuid[], client_uuid)
                )
                .await?;

                // Directly map to RecentClient
                client_rows
                    .into_iter()
                    .map(|row| RecentClient {
                        client_uuid: row.client_uuid,
                        name: row.name,
                        client_type: row.client_type,
                    })
                    .collect()
            } else {
                // No valid UUIDs found in Redis
                Vec::new()
            }
        } else {
            // Not logged in
            Vec::new()
        };

        let pending_clients = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                client_uuid: Uuid,
                name: String,
            };
            SELECT
                c.client_uuid,
                c.first_name || " " || c.last_name AS name
            FROM
                df4l.client_pending cp
            INNER JOIN
                df4l.client c USING(client_uuid)
            WHERE true
                AND c.advisor_uuid = $advisor_uuid
            ORDER BY
                c.create_ts DESC
            LIMIT 5
        )
        .await?
        .into_iter()
        .map(|row| PendingClient {
            client_uuid: row.client_uuid,
            name: row.name,
        })
        .collect();

        Ok(Output {
            active_legacy_clients: row.active_legacy_clients,
            active_clients: row.active_clients,
            pending_clients_count: row.pending_clients_count,
            name: full_name,
            gbu_agent_id: row.gbu_advisor_esid.filter(|s| !s.trim().is_empty()),
            statelic: row.statelic,
            identity_uuid: row.identity_uuid,
            recent_clients,
            pending_clients,
        })
    }
}

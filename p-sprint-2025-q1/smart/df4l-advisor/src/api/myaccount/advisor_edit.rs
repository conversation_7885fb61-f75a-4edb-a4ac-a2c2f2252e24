#[approck::api]

//TODO:DF4L: this whole function needs refactored.  See notes within.
pub mod edit_advisor {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub first_name: Option<String>,
        pub last_name: Option<String>,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,

        pub gbu_agent_id: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        //TODO:DF4L: advisor should not have advisor_write - they should have advisor_myaccount_write
        if !identity.advisor_write(input.advisor_uuid) {
            return_authorization_error!("identity.advisor_write({})", input.advisor_uuid);
        }

        let mut error = Input_Error {
            advisor_uuid: None,
            first_name: None,
            last_name: None,
            email: None,
            phone: None,
            address1: None,
            address2: None,
            city: None,
            state: None,
            zip: None,
            gbu_agent_id: None,
        };

        //TODO:DF4L: Do not allow for edit of first name, last name here
        //TODO:DF4L: changing email or mobile needs to be separate process
        //TODO:DF4L: Validation needed

        let gbu_agent_id = match input.gbu_agent_id {
            Some(gbu_agent_id) => {
                let gbu_agent_id = gbu_agent_id.trim().to_string();
                if !df4l_zero::gbu_agent::validate(&dbcx, &gbu_agent_id).await? {
                    error.gbu_agent_id = Some("Invalid GBU Agent ID".to_string());
                    return Ok(Response::ValidationError(granite::NestedError {
                        outer: "Invalid GBU Agent ID".to_string(),
                        inner: Some(error),
                    }));
                }
                Some(gbu_agent_id)
            }
            None => None,
        };

        // validate gbu_agent_id

        granite::pg_execute!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
                $first_name: &input.first_name,
                $last_name: &input.last_name,
                $email: &input.email,
                $phone: &input.phone,
                $address1: &input.address1,
                $address2: &input.address2,
                $city: &input.city,
                $state: &input.state,
                $zip: &input.zip,
                $gbu_agent_id: &gbu_agent_id,
            };
            UPDATE
                df4l.advisor
            SET
                first_name = COALESCE($first_name, first_name),
                last_name = COALESCE($last_name, last_name),
                email = COALESCE($email, email),
                phone = COALESCE($phone, phone),
                address1 = COALESCE($address1, address1),
                address2 = COALESCE($address2, address2),
                city = COALESCE($city, city),
                state = COALESCE($state, state),
                zip = COALESCE($zip, zip),
                gbu_advisor_esid = COALESCE($gbu_agent_id, gbu_advisor_esid)
            WHERE
                advisor_uuid = $advisor_uuid::uuid
        )
        .await?;

        let identity_row = granite::pg_row!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
            };
            row = {
                identity_uuid: Uuid,
            };
            SELECT
                identity_uuid
            FROM
                df4l.advisor
            WHERE
                advisor_uuid = $advisor_uuid::uuid
        )
        .await?;

        Ok(Response::Output(Output {
            detail_url: format!("/myaccount/{}/", identity_row.identity_uuid),
            message: "Advisor updated".to_string(),
        }))
    }
}

import "./index.mcss";
import "@bux/input/text/currency.mts";

import { SEC } from "@granite/lib.mts";
import { life_insurance } from "@crate/api/client0/life_insuranceλ.mts";

import BuxInputCurrency from "@bux/input/text/currency.mts";

const $client_uuid = SEC(HTMLInputElement, document, "[name=client_uuid]");
const $advisor_uuid = SEC(HTMLInputElement, document, "[name=advisor_uuid]");
const $net_cash_at_end = SEC(BuxInputCurrency, document, "[name=net_cash_at_end]");
// Add on-change event to save
$net_cash_at_end.addEventListener("change", (_event) => {
    life_insurance
        .call({
            client_uuid: $client_uuid.value,
            advisor_uuid: $advisor_uuid.value,
            net_cash_at_end: $net_cash_at_end.value_option,
        })
        .then((response) => {
            if ("Output" in response) {
                // Saved successfully
            } else {
                console.error(response);
            }
        })
        .catch((error) => {
            console.error(error);
        });
});

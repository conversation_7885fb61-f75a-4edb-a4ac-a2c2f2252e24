import "./delete.mcss";

import { SE } from "@granite/lib.mts";
import { del } from "@crate/api/client0/debt/deleteλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $client_uuid = SE($form, "[name=client_uuid]") as HTMLInputElement;
const $advisor_uuid = SE($form, "[name=advisor_uuid]") as HTMLInputElement;
const $client_debt_uuid = SE($form, "[name=client_debt_uuid]") as HTMLInputElement;

new FormPanel({
    $form,
    api: del.api,
    on_cancel: go_back,

    err: (errors) => {
        console.log("errors: ", errors);
    },

    get: () => {
        return {
            client_debt_uuid: $client_debt_uuid.value,
            client_uuid: $client_uuid.value,
            advisor_uuid: $advisor_uuid.value,
        };
    },

    set: (_value) => {
        // console.log(value);
    },
    out: (output) => {
        go_next(output.detail_url);
    },
});

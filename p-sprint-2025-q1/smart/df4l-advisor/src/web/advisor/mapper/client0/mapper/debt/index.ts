import "@bux/component/detail_table.mts";
import "./index.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/select/nilla.mts";

// Targeting the td elements in the table to have background color
addEventListener("DOMContentLoaded", () => {
    document.querySelectorAll<HTMLTableRowElement>("table tbody tr").forEach((tr) => {
        const cells: string[] = Array.from(
            tr.querySelectorAll<HTMLTableCellElement>("td"),
        ).map((td) => td.textContent?.trim().replace(/\u00A0/g, " ") || "");

        const firstCell = cells[0];

        const isSpecialRow = firstCell === "Totals:" ||
            firstCell === "100% Paid Off:";

        if (isSpecialRow) {
            tr.classList.add("highlight-special");
        }
    });
});

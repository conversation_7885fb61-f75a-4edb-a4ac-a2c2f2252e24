panel {
    border-color: #6c757d;
}

d2c-client-info {

    h1 {
        text-align: center;
        background-color: #cfe2ff;
        padding: 1rem;
        border-radius: .25rem;
        border: 1px solid #b6d4fe;
        color: #084298;
        font-weight: 500;
        font-size: 18pt;
    }

    p.phone, p.email, p.status {
        display: flex;
        gap: 1rem;
        align-items: center;
    
        i {
            width: 1.5rem;
            text-align: center;
        }
    }
}

d2c-program-overview {
    
    panel {

        content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;

            div:first-child {

                h5 {
                    font-size: 11pt;
                    margin-bottom: 0;
                    color: dimgray;
                    font-weight: normal;
                }

                p {
                    margin-bottom: 0;
                    font-size: 18pt;
                    font-weight: 500;
                }
            }

            i {
                font-size: 20pt;
            }
        }
    }
}

d2c-debt-list {

    table {

        tr {

            th, td {
                text-align: right;
            }

            th:first-child, td:first-child {
                text-align: left;
            }

            &.paid {

                td {
                    text-decoration: line-through;
                }
            }
        }

        &.striped {

            > tbody {

                tr:nth-of-type(odd) {
                    background-color: rgba(0, 0, 0, .05);
                }
            }
        }
    }
}

d2c-program-timeline {

    panel {
        border-color: #6c757d;

        content {
            padding: 0;
            display: flex;

            div:nth-child(1) {
                padding: 1rem;
                width: 25%;
                text-align: right;

                p {
                    margin-bottom: 0;
                    font-size: .875rem;
                    font-weight: 500;
                }
            }

            div:nth-child(2) {
                position: relative;
                padding-top: 1rem;

                i {
                    color: #6c757d;
                    position: relative;
                    z-index: 100;
                    background-color: #fff;
                    display: block;
                    border-top: 5px solid #fff;
                    border-bottom: 5px solid #fff;
                }

                &::after {
                    content: "";
                    display: block;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: 50%;
                    width: 1px;
                    background-color: #6c757d;
                    transform: translateX(-50%);
                }
            }

            div:nth-child(3) {
                padding: 1rem;

                h5 {
                    font-size: 1rem;
                }

                p {
                    margin-bottom: 0;
                    font-size: 1rem;
                    font-weight: 500;
                }
            }
        }

        /* Program Start */
        &:first-child {
            
            div:nth-child(2) {

                i {
                    border-top: none;
                }
                
                &::after {
                    top: 1rem;
                }
            }
        }

        /* Program End */
        &:last-child {

            div:nth-child(2) {
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                padding-top: 0;
                padding-bottom: 1rem;

                i {
                    border-bottom: none;
                }

                &::after {
                    bottom: 1rem;
                }
            }
        }

        &.complete {
            border-color: #198754;
            background-color: #dff0d8;

            div:nth-child(2) {

                i {
                    color: #198754;
                    background-color: #dff0d8;
                    border-color: #dff0d8;
                }

                &::after {
                    background-color: #198754;
                }
            }

            div:nth-child(3) {

                p {
                    text-decoration: line-through;
                }
            }
        }
    }
}
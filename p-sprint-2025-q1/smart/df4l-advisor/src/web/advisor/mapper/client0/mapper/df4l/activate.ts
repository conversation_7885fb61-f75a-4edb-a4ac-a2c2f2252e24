import "./activate.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/date.mjs";

import { SE } from "@granite/lib.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";
import { activate } from "@crate/api/client0/df4l/activateλ.mts";

import FormPanel from "@bux/component/form_panel.mts";
import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputDate from "@bux/input/date.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $advisor_uuid: HTMLInputElement = SE($form, "[name=advisor_uuid]");
const $client_uuid: HTMLInputElement = SE($form, "[name=client_uuid]");
const $phone: BuxInputTextString = SE($form, "[name=phone]");
const $phone2: BuxInputTextString = SE($form, "[name=phone2]");
const $debt_free_start_date: BuxInputDate = SE($form, "[name=debt_free_start_date]");

new FormPanel({
    $form,
    api: activate.api,
    on_cancel: go_back,

    err: (errors) => {
        $phone.set_e(errors.phone);
        $phone2.set_e(errors.phone2);
        $debt_free_start_date.set_e(errors.debt_free_start_date);
    },

    get: () => {
        return {
            client_uuid: $client_uuid.value,
            advisor_uuid: $advisor_uuid.value,
            phone: $phone.value_option,
            phone2: $phone2.value_option,
            debt_free_start_date: $debt_free_start_date.value || undefined,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});

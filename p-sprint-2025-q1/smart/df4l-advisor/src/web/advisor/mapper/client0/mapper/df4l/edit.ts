import "./edit.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/text/currency.mts";
import "@bux/input/select/nilla.mts";

import { SE } from "@granite/lib.mts";
import { edit_set as edit } from "@crate/api/client0/df4l/edit_setλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputCurrency from "@bux/input/text/currency.mts";
import FormPanel from "@bux/component/form_panel.mts";
import BuxInputSelectNilla from "@bux/input/select/nilla.mts";
//import {type Decimal } from "@granite/lib.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $advisor_uuid: HTMLInputElement = SE($form, "[name=advisor_uuid]");
const $client_uuid: HTMLInputElement = SE($form, "[name=client_uuid]");
const $first_name: BuxInputTextString = SE($form, "[name=first_name]");
const $last_name: BuxInputTextString = SE($form, "[name=last_name]");
const $email: BuxInputTextString = SE($form, "[name=email]");
const $phone: BuxInputTextString = SE($form, "[name=phone]");
const $phone2: BuxInputTextString = SE($form, "[name=phone2]");
const $monthly_budget: BuxInputCurrency = SE($form, "[name=monthly_budget]");
const $annual_insurance_premium: BuxInputCurrency = SE(
    $form,
    "[name=annual_insurance_premium]",
);
const $annual_insurance_pua: BuxInputCurrency = SE($form, "[name=annual_insurance_pua]");

// in a loop 0..5, create lists of $exm and $exa elements
const $exm: BuxInputSelectNilla<string>[] = [];
const $exa: BuxInputCurrency[] = [];
const ex_month_count = 5;
for (let i = 0; i < ex_month_count; i++) {
    $exm.push(SE($form, `[name=exm_${i}]`) as BuxInputSelectNilla<string>);
    $exa.push(SE($form, `[name=exa_${i}]`));
}

new FormPanel({
    $form,
    api: edit.api,
    on_cancel: go_back,

    err: (errors) => {
        $first_name.set_e(errors.first_name);
        $last_name.set_e(errors.last_name);
        $email.set_e(errors.email);
        $phone.set_e(errors.phone);
        $phone2.set_e(errors.phone2);
        $monthly_budget.set_e(errors.monthly_budget);
        $annual_insurance_premium.set_e(errors.annual_insurance_premium);
        $annual_insurance_pua.set_e(errors.annual_insurance_pua);
    },

    get: () => {
        //
        // in a loop 0..ex_month_count, collect exm_*, exa_* values, composing list in the following format [{"Month": 48, "Amount": "21739"}, {"Month": 60, "Amount": "7171"}].
        // Use only non-zero values.
        const debt_free_extra_pua_list = [];
        for (let i = 0; i < ex_month_count; i++) {
            if ($exm[i] && $exm[i]?.value && $exa[i] && $exa[i]?.value) {
                // parse month as int
                const m = Number.parseInt($exm[i]?.value || "");
                const a = $exa[i]?.value;
                if (m && a) {
                    debt_free_extra_pua_list.push({ month: m, amount: a });
                }
            }
        }
        console.log("debt_free_extra_pua_list", debt_free_extra_pua_list);

        return {
            advisor_uuid: $advisor_uuid.value,
            client_uuid: $client_uuid.value,
            first_name: $first_name.value,
            last_name: $last_name.value,
            email: $email.value_option,
            phone: $phone.value_option,
            phone2: $phone2.value_option,
            monthly_budget: $monthly_budget.value_option,
            annual_insurance_premium: $annual_insurance_premium.value_option,
            annual_insurance_pua: $annual_insurance_pua.value_option,
            debt_free_extra_pua_list,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});

#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/life-insurance/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;

        // Generate report
        use df4l_zero::api::client_debt_free_info::client_debt_free_info;
        // Use df4l_zero API to return all the data, including plan
        let client_debt_info = client_debt_free_info::call(
            app,
            identity,
            client_debt_free_info::Input {
                advisor_uuid: path.advisor_uuid,
                client_uuid: path.client_uuid,
                generate_plan: true,
            },
        )
        .await?;

        let client = &client_debt_info.client;

        doc.set_title("Life Insurance Document");

        doc.add_body(html!(
            input type="hidden" name="client_uuid" value=(path.client_uuid.to_string()) {}
            input type="hidden" name="advisor_uuid" value=(path.advisor_uuid.to_string()) {}

            panel {
                content {
                    grid-2 {
                        (
                            bux::input::text::currency::currency_input(
                                "net_cash_at_end",
                                &format!("Enter NET CASH value from this dcument for YEAR {}:", client_debt_info.debt_info.max_payoff_month / 12 + 1), 
                                client.net_cash_at_end
                            )
                        )
                        img src="https://invoicemaker.com/uploads/2022/11/Debt-Collection-Invoice-Template.png" alt="Document" style="max-width: 30%; margin-top: 1rem; display: block;" {}
                    }
                }
                footer {
                    (bux::button::link::label_icon_class("View", "", "#", "success"))
                    " "
                    (bux::button::link::label_icon_class("Upload New Document", "", "/advisor/00000000-0000-0000-0000-000000000000/client0/00000000-0000-0000-0000-000000000000/life-insurance/upload-life-illustration", "success"))
                }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/life-insurance/upload-life-illustration; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Upload Life Illustration Document for Adrian Gallinal");

        doc.add_body(html!(
            panel {
                content {
                    p { "Upload a PDF file containing a whole life illustration which will be appended to the end of the debt snowball report." }
                    (bux::button::link::label_icon_class("Browse File", "", "#", "primary"))
                }
                footer {
                    (bux::button::link::label_icon_class("Continue", "", "/advisor/00000000-0000-0000-0000-000000000000/client0/00000000-0000-0000-0000-000000000000/life-insurance/", "primary"))
                    " "
                    (bux::button::link::cancel("/advisor/00000000-0000-0000-0000-000000000000/client0/00000000-0000-0000-0000-000000000000/life-insurance/"))
                }
            }
        ));
        Response::HTML(doc.into())
    }
}

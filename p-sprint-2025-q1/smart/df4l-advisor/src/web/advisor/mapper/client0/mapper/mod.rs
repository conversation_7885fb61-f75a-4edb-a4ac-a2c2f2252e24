pub mod debt;
pub mod df4l;
pub mod edit;
pub mod index;
pub mod life_insurance;
pub mod note;
pub mod remove;

#[approck::prefix(/advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/)]
pub mod prefix {
    pub fn menu(app: App, menu: Menu, advisor_uuid: Uuid, client_uuid: Uuid) {
        menu.set_label_name_uri(
            "Client Details",
            app.uuid_to_label(client_uuid),
            &df4l_zero::ml_advisor_client0_details(advisor_uuid, client_uuid),
        );

        menu.add_link(
            "Edit Program",
            &df4l_zero::ml_advisor_client0_df4l_edit(advisor_uuid, client_uuid),
        );
        menu.add_link(
            "Activate Program",
            &df4l_zero::ml_advisor_client0_df4l_activate(advisor_uuid, client_uuid),
        );
        menu.add_link(
            "List of Debts",
            &df4l_zero::ml_advisor_client0_debt_list(advisor_uuid, client_uuid),
        );
        menu.add_link(
            "Notes",
            &df4l_zero::ml_advisor_client0_note_list(advisor_uuid, client_uuid),
        );
        menu.add_link(
            "Life Insurance Document",
            &df4l_zero::ml_advisor_client0_life_insurance(advisor_uuid, client_uuid),
        );
        menu.add_link(
            "View Debt Plan",
            &df4l_zero::ml_advisor_client0_df4l_dump(advisor_uuid, client_uuid),
        );
        menu.add_link(
            "View Debt Report",
            &df4l_zero::ml_advisor_client0_df4l_report(advisor_uuid, client_uuid),
        );
    }
    pub fn auth() {}
}

#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/debt/{client_debt_uuid:Uuid}/delete; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;
        use bux::format_currency_us_0;
        use bux::format_percentage_us_2;

        use crate::api::client0::debt::detail::detail;

        let debt = detail::call(
            app,
            identity,
            detail::Input {
                client_uuid: path.client_uuid,
                client_debt_uuid: path.client_debt_uuid,
            },
        )
        .await?;

        doc.set_title("Delete Debt Account");

        let mut form_panel = bux::component::delete_cancel_form_panel(
            "Are you sure you want to remove this debt account?",
            &df4l_zero::ml_advisor_client0_debt_list(path.advisor_uuid, path.client_uuid),
        );
        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            input type="hidden" name="client_uuid" value=(path.client_uuid.to_string()) {}
            input type="hidden" name="advisor_uuid" value=(path.advisor_uuid.to_string()) {}
            input type="hidden" name="client_debt_uuid" value=(path.client_debt_uuid.to_string()) {}

            dl {
                dt { "Debt:" }
                dd { (debt.name) }
                dt { "Current Balance:" }
                dd { (debt.balance.map(format_currency_us_0).unwrap_or_else(|| "N/A".to_string())) }
                dt { "Minimum Payment:" }
                dd { (debt.monthly_payment.map(format_currency_us_0).unwrap_or_else(|| "N/A".to_string())) }
                dt { "Interest Rate (%):" }
                dd { (debt.interest_rate.map(format_percentage_us_2).unwrap_or_else(|| "N/A".to_string())) }
            }
            (bux::input::checkbox::name_label_checked("yes", "Yes, Delete This Debt Account", false))
        ));

        doc.add_body(html!(
            section  {
                grid-12 {
                    cell-3 {}
                    cell-6 {
                        (form_panel)
                    }
                    cell-3 {}
                }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/df4l/report?keyword=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;
        use bux::Render;
        use bux::format_currency_us_0;
        use bux::format_decimal_us_2;
        use bux::format_percentage_us_2;
        use granite::Decimal;
        use std::collections::HashMap;
        /*
        DebtTypeIcons = {
            'American Express': 'cc-amex.png',
            'Boat': 'ship.png',
            'Car': 'car-side.png',
            'Credit Card': 'credit-card.png',
            'Department Store Card': 'credit-card.png',
            'Discover': 'cc-discover.png',
            'Home Improvement Loan': 'home.png',
            'Line Of Credit': 'credit-card.png',
            'MasterCard': 'cc-mastercard.png',
            'Medical Debt': 'clinic-medical.png',
            'Mortgage': 'home.png',
            'Motorcycle': 'motorcycle.png',
            'Other Loan': 'money-check-alt.png',
            'Personal Loan': 'piggy-bank.png',
            'Student Loan': 'graduation-cap.png',
            'Truck': 'truck-pickup.png',
            'Vacation Debt': 'plane-departure.png',
            'Vacation Home': 'home.png',
            'Van': 'shuttle-van.png',
            'Visa': 'credit-card.png',
            'Wedding Debt': 'ring.png',
            }
        */
        let debt_type_icons = HashMap::from([
            ("American Express", "cc-amex.png"),
            ("Boat", "ship.png"),
            ("Car", "car-side.png"),
            ("Credit Card", "credit-card.png"),
            ("Department Store Card", "credit-card.png"),
            ("Discover", "cc-discover.png"),
            ("Home Improvement Loan", "home.png"),
            ("Line Of Credit", "credit-card.png"),
            ("MasterCard", "cc-mastercard.png"),
            ("Medical Debt", "clinic-medical.png"),
            ("Mortgage", "home.png"),
            ("Motorcycle", "motorcycle.png"),
            ("Other Loan", "money-check-alt.png"),
            ("Personal Loan", "piggy-bank.png"),
            ("Student Loan", "graduation-cap.png"),
            ("Truck", "truck-pickup.png"),
            ("Vacation Debt", "plane-departure.png"),
            ("Vacation Home", "home.png"),
            ("Van", "shuttle-van.png"),
            ("Visa", "credit-card.png"),
            ("Wedding Debt", "ring.png"),
        ]);

        // Create a closure to get debt type icon based on debt name
        let get_debt_icon = |debt_name: &str| -> String {
            // use format!("https://smartadvisortools-com.smart-1.us-east-2.acp7.link/secure/client/debtfree/report/{}", icon_name)
            let icon_name = debt_type_icons.get(debt_name).unwrap_or(&"default.png");
            format!(
                "https://smartadvisortools-com.smart-1.us-east-2.acp7.link/secure/client/debtfree/report/{icon_name}"
            )
        };

        // Generate report
        use df4l_zero::api::client_debt_free_info::client_debt_free_info;
        // Use df4l_zero API to return all the data, including plan
        let client_debt_info = client_debt_free_info::call(
            app,
            identity,
            client_debt_free_info::Input {
                advisor_uuid: path.advisor_uuid,
                client_uuid: path.client_uuid,
                generate_plan: true,
            },
        )
        .await?;

        doc.set_title("Debt Report");

        // Table 1: Basic Info
        let mut dt1 = bux::component::detail_table(&client_debt_info.debt_info.debt_list);
        dt1.add_column("Debt", |a| html! { (a.full_name) });
        dt1.add_column("Balance", |a| html! { (format_currency_us_0(a.balance)) });
        dt1.add_column(
            "Interest Rate",
            |a| html! { (format_percentage_us_2(a.interest_rate)) },
        );
        dt1.add_column(
            "Payment",
            |a| html! { (format_currency_us_0(a.monthly_payment)) },
        );
        dt1.add_column(
            "Monthly Interest",
            |a| html! { (format_currency_us_0(a.monthly_interest.unwrap_or_default())) },
        );
        dt1.add_column(
            "Effective Interest Cost",
            |a| html! { (format_percentage_us_2(a.monthly_interest_percent.unwrap_or_default())) },
        );

        // Section 3
        let section3_debt_html = section3_debt_table(&client_debt_info.debt_info);

        doc.add_body(html!(
            // //////////////////////////////////////////////////////////////////////////////////////////
            // PAGE 1 - Cover Page
            page {
                page-header {
                    div.blue-header {}
                }
                page-body.text-center {
                    img src="https://asset7.net/Zagula/Smart/Debt2Capital/debt2capital_logo_trademark.svg" style="max-width:600px;height:auto; margin-bottom: 8%;" {}
                    h2 { i { "Prepared for:" } }
                    p style="font-size:24pt;" { (client_debt_info.client.name) }
                    h2 { i { "Prepared By:" } }
                    p style="font-size:22pt;" { (client_debt_info.advisor.name) }
                    p style="font-size:14pt;" { (client_debt_info.advisor.email.as_deref().unwrap_or("")) }
                }
                page-footer {
                    "This illustration is for educational purposes only." br;
                    "Debt2Capital™ is a product of SMART Retirement Corporation." br;
                    "Copyright © 2025 by SMART Retirement Corporation. All rights reserved."
                }
            }
            // //////////////////////////////////////////////////////////////////////////////////////////
            // PAGE 2 - Disclaimer and Analysis
            page {
                page-header {
                    div.blue-header {}
                }
                page-body {
                    h2 { "Scope of Analysis Disclaimer:" }
                    p { "The analysis contained herein is conceptual in nature and provided to offer you an alternate plan to eliminate your current debts. The Debt2Capital™ approach uses a specific type of whole life insurance policy heavily funded with paid up additions which are accessible via policy loans to pay off your outstanding debts." }
                    p { "Debt2Capital™ is created for people with the sufficient incomes and the financial means to make regular payments against their existing debt obligations, who also have some extra money in their budget, and want a better way to pay-off their existing debt using a modified approach to snowballing their debt." }
                    p { "Debt2Capital™ is not a debt relief or debt settlement program.  Debt2Capital™ does not renegotiate, settle, or in any way alter the terms of payment, interest rates, or other terms of your debt. DF4L™ does not make any payments for you or combine your loans into one or more new payment obligations." }
                    p { "You will be required to pay regular, ongoing premiums toward a whole life insurance policy with a paid-up additions feature that will compound interest inside your policy’s cash value.  Premiums paid to whole life insurance policies are typically higher than those paid to term life insurance policies; but, term insurance has no cash value, just a death benefit.  Some of the money you pay into the policy will be applied to a savings component of the policy, referred to as the cash value of your policy. Some of the money will go to insurance benefit expenses." }
                    p { "If you borrow money from the cash value portion of your policy (for example, to pay off a debt), the policy issuer will charge interest on the outstanding principal of that loan.  However, these loans typically have lower interest rates than personal loans." }
                    p { "The report does not provide tax, legal, accounting or investment advice. The rates of return are hypothetical and cannot project future performance. The whole life product representations are estimates based on current economic conditions and policy dividend rates which are subject to change by the carrier without notice. The assumptions used to generate this report are estimates and not product specific. You should consult your tax advisor regarding taxation both federally and state specific." }
                    p { "The results of this analysis are heavily reliant on the data you provide concerning your outstanding debts. As time progresses, you may take additional loans which will significantly change the outcome. In addition, the whole life company may change their dividend rates and loan rates which will also change the outcome of this analysis. This report is conceptual and is for educational purposes only." }
                }
                page-footer {
                    "This illustration is for educational purposes only." br;
                    "Debt2Capital™ is a product of SMART Retirement Corporation." br;
                    "Copyright © 2025 by SMART Retirement Corporation. All rights reserved."
                }
            }
            // //////////////////////////////////////////////////////////////////////////////////////////
            // PAGE 3 - Section One and Two
            page {
                page-header {
                    div.blue-header {}
                }
                page-body {
                    h2 { b {"Section 1:"} " " u {"Effective"} " Interest Cost On Your Next Payment" }
                    (dt1.render())
                    table {
                        tfoot {
                            tr {
                                th{}
                                th { "Balance" }
                                th { "Interest Rate" }
                                th { "Payment" }
                                th { "Monthly Interest" }
                                th { "Effective Interest Cost" }
                            }
                            tr {
                                th {
                                    b { "Totals:" }
                                }
                                th {
                                    (format_currency_us_0(client_debt_info.debt_info.debt_balance.unwrap_or_default()))
                                }
                                th {
                                    (format_percentage_us_2(client_debt_info.debt_info.average_interest_rate.unwrap_or_default()))
                                }
                                th {
                                    (format_currency_us_0(client_debt_info.debt_info.minimum_monthly_payment.unwrap_or_default()))
                                }
                                th {
                                    (format_currency_us_0(client_debt_info.debt_info.total_monthly_interest.unwrap_or_default()))
                                }
                                th {
                                    (format_percentage_us_2(client_debt_info.debt_info.total_monthly_interest_percent.unwrap_or_default()))
                                }
                            }
                        }
                    }


                    h2 {
                        b { "Section 2:" } " " u { "Current Balance" } " Due vs. " u { "Balance Plus Interest" } " Due"
                    }
                    div style="display: flex; justify-content: center; align-items: flex-start; gap: 2rem; text-align: center; margin-top: 1rem;" {
                        div {
                            div style="height: 1in; width: 1.75in; background: white; margin: 10px auto 0;" {}
                            div style="height: 2in; background-color: #11294f; color: white; font-size: 16pt; display: flex; align-items: center; justify-content: center;" {
                                (format_currency_us_0(client_debt_info.debt_info.debt_balance.unwrap_or_default()))
                            }
                            div style="width: 1.75in; text-align: center; font-weight: bold; font-size: 16pt; padding-top: 10px; border-top: 1px solid black; margin: 0 auto;" {
                                "Current Loan" br; "Balance"
                            }
                        }
                        div style="display: flex; flex-direction: column; align-items: center; justify-content: center;" {
                            div style="font-weight: bold; font-size: 13pt;" {
                                (format_currency_us_0(client_debt_info.debt_info.standard_payoff_interest))
                                br; "Interest"
                            }
                            img src="https://smartadvisortools-com.smart-1.us-east-2.acp7.link/secure/client/debtfree/report/arrow.png" style="height: 20px; margin-top: 10px;" {}
                        }
                        div {
                            div style="height: 1in; background: repeating-linear-gradient(130deg, #C65358, #C65358 5px, #ca171e 5px, #ca171e 10px);" {}
                            div style="height: 2.1in; background-color: #ca171e; color: white; font-size: 16pt; display: flex; align-items: center; justify-content: center;" {
                                (format_currency_us_0(client_debt_info.debt_info.debt_balance.unwrap_or_default() + client_debt_info.debt_info.standard_payoff_interest))
                            }
                            div style="width: 1.75in; text-align: center; font-weight: bold; font-size: 16pt; padding-top: 10px; border-top: 1px solid black; margin: 0 auto;" {
                                "Current Loan" br; "Balance + Interest" br; "at minimum payments"
                            }
                        }
                    }

                }
                page-footer {
                    "This illustration is for educational purposes only." br;
                    "Debt2Capital™ is a product of SMART Retirement Corporation." br;
                    "Copyright © 2025 by SMART Retirement Corporation. All rights reserved."
                }
            }
            // //////////////////////////////////////////////////////////////////////////////////////////
            // PAGE 4 - Section Three
            page {
                page-header {
                    div.blue-header {}
                }
                page-body {
                    h2 { b { "Section 3:" } " Debt Free When?" }
                    (section3_debt_html)
                    br;
                    p {
                        "The illustration above is based on the minimum monthly payment of " 
                            (format_currency_us_0(client_debt_info.debt_info.minimum_monthly_payment.unwrap_or_default()))
                        "/mo "
                        "which does not include the additional monthly premium of "
                        @if client_debt_info.debt_info.monthly_insurance_premium.is_some() {
                            (format_currency_us_0(client_debt_info.debt_info.monthly_insurance_premium.unwrap_or_default()))
                            "/mo"
                        }
                        @else {
                            "-"
                        }
                        " "
                        "of which is paid into the whole life policy, "
                            (format_percentage_us_2(client_debt_info.debt_info.percent_to_paid_up_additions.unwrap_or_default()))
                        " "
                        "is directed to paid up additions. 
                        Results will vary depending on the policy performance and results may vary." 
                    }
                    h3.text-center style="color: red;" {
                        "With Debt2Capital™, you will pay off your debt" 
                        br;
                        b {
                            (client_debt_info.debt_info.max_standard_payoff_month - client_debt_info.debt_info.max_payoff_month)
                            " months "
                            /*
                            ''' + ('''
                                /''' + HS(PLUR(round((DebtFreeInfo.MaxStandardPayoffMonth-DebtFreeInfo.MaxPayoffMonth)/12,2), 'year')) + '''
                            ''' if (DebtFreeInfo.MaxStandardPayoffMonth-DebtFreeInfo.MaxPayoffMonth)/12 > 1 else '') + '''
                            */
                            @if (client_debt_info.debt_info.max_standard_payoff_month - client_debt_info.debt_info.max_payoff_month) / 12 > 1 {
                                " / "
                                (format_decimal_us_2(Decimal::from(client_debt_info.debt_info.max_standard_payoff_month - client_debt_info.debt_info.max_payoff_month) / Decimal::from(12)))
                                " years"
                            }
                            br;
                            "faster saving you " 
                            b {
                                (format_currency_us_0(client_debt_info.debt_info.interest_saved))
                                "!" 
                            }
                        }
                    }
                    @if !client_debt_info.debt_info.debt_list.is_empty() {
                        h2.text-center { "Your Debt2Capital™ Action Plan And Timeline" }
                        table {
                            @for (i, debt) in client_debt_info.debt_info.debt_list.iter().enumerate() {
                                @if i % 2 != 0 {
                                    tr {
                                        td {}
                                        td {}
                                        td style="text-align: center; padding: 0 .2in;" {
                                            img src="https://smartadvisortools-com.smart-1.us-east-2.acp7.link/secure/client/debtfree/report/time-line.png" style="height: 0.4in;";
                                        }
                                        td {
                                            img
                                            src=(get_debt_icon(&debt.debt_name))
                                            style="height: .375in;";
                                        }
                                        td style="text-align: right; width: 48%;" {
                                            div style="font-size: 10pt; display: inline-block; vertical-align: middle;" {
                                                (debt.full_name)
                                                br;
                                                b { "PAID" } 
                                                br;
                                                "in "
                                                (debt.payoff_month.unwrap_or_default())
                                                " Months ("
                                                    (debt.payoff_date.clone().unwrap_or_default())
                                                ")"
                                            }
                                        }
                                    }
                                }
                                @else {
                                    tr {
                                        td style="text-align: right; width: 48%;" {
                                            div style="font-size: 10pt; display: inline-block; vertical-align: middle;" {
                                                (debt.full_name)
                                                br;
                                                b { "PAID" } 
                                                br;
                                                "in "
                                                (debt.payoff_month.unwrap_or_default())
                                                " Months ("
                                                    (debt.payoff_date.clone().unwrap_or_default())
                                                ")"
                                            }
                                        }
                                        td style="text-align: center; padding: 0 .2in;" {
                                            img src="https://smartadvisortools-com.smart-1.us-east-2.acp7.link/secure/client/debtfree/report/time-line.png" style="height: 0.4in;";
                                        }
                                        td {
                                            img
                                            src=(get_debt_icon(&debt.debt_name))
                                            style="height: .375in;";
                                        }
                                        td {}
                                        td {}
                                    }
                                }
                            }
                        }
                    }
                }
                page-footer {
                    "This illustration is for educational purposes only." br;
                    "Debt2Capital™ is a product of SMART Retirement Corporation." br;
                    "Copyright © 2025 by SMART Retirement Corporation. All rights reserved."
                }
            }
            // //////////////////////////////////////////////////////////////////////////////////////////
            // PAGE 5 - Section Four
            page {
                page-header {
                    div.blue-header {}
                }
                page-body {
                    h2 { b { "Section 4" } ": Estimated Capital After Your Debts Are Paid" }
                    p.page5 { "Debt Snowballing can be a very effective method to eliminate debt. However, with the traditional debt snowball you will always need to return to the banks, auto financiers, and credit card companies for your next purchase. Once a dollar is spent you don't have it to make your next purchase." }
                    p.page5 { "With Debt2Capital™, your debts get paid off plus there is an accumulated capital that is available to you that you then \"loan\" to yourself. By making yourself the only banker you will need in the future after you pay off your current debts, you will save significant dollars otherwise lost to the interest expense of future mortgages, student loans, auto loans, credit cards, and other forms of borrowed money." }
                    p.page5 { "The most important aspect of Debt2Capital™ is to be HONEST with yourself. The key to Debt2Capital™ success is that you make sure you pay back your whole life policy the dollar amount a commercial bank would have charged you. Over time, this accumulated capital will help you make future major purchases and keep you out of debt for life. Eventually when you are in retirement, the capital you’ve accumulated can be used for major purchases." }
                    p.page5 { "Your Debt2Capital™ advisor will continue to guide you through the process and help you focus on compounding interest for yourself and not for the lending institutions lending you money at your expense. The advisor providing this information to you is an independent advisor. SMART Retirement Corporation is not responsible for the recommendations made by the independent advisor providing you this analysis." }
                }
                page-footer {
                    "This illustration is for educational purposes only." br;
                    "Debt2Capital™ is a product of SMART Retirement Corporation." br;
                    "Copyright © 2025 by SMART Retirement Corporation. All rights reserved."
                }
            }

            // //////////////////////////////////////////////////////////////////////////////////////////
            // PAGE 6 - Section Five
            page {
                page-header {
                    div.blue-header {}
                }
                page-body {
                    h2 { b { "Section 5:" } " Summary of Benefits" }
                    div."highlight-box highlight-blue" {
                        label { "Total Accumulated Net Cash Plus Estimated Interest Savings" } br;
                        big { b {
                            (format_currency_us_0(client_debt_info.debt_info.interest_saved + client_debt_info.debt_info.net_cash_at_end.unwrap_or_default()))
                        } }
                    }
                    hr;
                    div."highlight-box highlight-green" {
                        label { "Accumulated Net Cash Value" } br;
                        big {
                            (format_currency_us_0(client_debt_info.debt_info.net_cash_at_end.unwrap_or_default()))
                        }
                    }
                    div."highlight-box highlight-beige" {
                        label { "Estimated Interest Savings" } br;
                        big {
                            (format_currency_us_0(client_debt_info.debt_info.interest_saved))
                        }
                    }
                    p { "Based on the illustrated values of the policy ledger attached below, which are subject to change due to varying dividend rates, interest rates and other market conditions, the Debt2Capital™ advisor guiding you through the Debt2Capital™ process, Michael Berdin, estimates a cash value of $0 by the end of policy year 1 solely on information provided in the attached illustration." }
                    p { "*This estimate is for educational purposes only and likely inaccurate due to additional purchases you make, new debts you incur, by the modification of your payment schedules or changes in the dividend and/or loan rates made by the Insurance Policy Carrier over this extended period of time." }
                    p { "This report is free of any charge. No fee can be charged for this report. Insurance policies pay compensation to the agent." }
                }
                page-footer {
                    "This illustration is for educational purposes only." br;
                    "Debt2Capital™ is a product of SMART Retirement Corporation." br;
                    "Copyright © 2025 by SMART Retirement Corporation. All rights reserved."
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }

    /// Generate client debt table from real client debt data
    /// Note: This function displays actual client debt records from the database
    fn section3_debt_table(
        debt_info: &df4l_zero::api::client_debt_free_info::client_debt_free_info::DebtInfo,
    ) -> approck::Markup {
        use approck::html;
        use bux::format_currency_us_0;

        html! {
            table.table.table-striped {
                thead {
                    tr {
                        th { "Debt" }
                        th {
                            "Minimum Payments"
                            br;
                            (format_currency_us_0(debt_info.minimum_monthly_payment.unwrap_or_default()))
                            "/mo"
                        }
                        th {
                            "Minimum Payments"
                            br;
                            "+"
                            @if debt_info.monthly_insurance_premium.is_some() {
                                (format_currency_us_0(debt_info.monthly_insurance_premium.unwrap_or_default()))
                                "/mo"
                            }
                            @else {
                                "-"
                            }
                            br;
                            "Total "
                            @if debt_info.monthly_budget.is_some() {
                                (format_currency_us_0(debt_info.monthly_budget.unwrap_or_default()))
                                "/mo"
                            }
                            @else {
                                "-"
                            }
                         }
                        th { "Interest Saved With Debt Free 4 Life™" }
                    }
                }
                tbody {
                    @for debt in &debt_info.debt_list {
                        tr class=(if debt.active { "" } else { "text-muted" }) {
                            td {
                                (debt.full_name)
                            }
                            /*
              <td style="white-space: nowrap;">
                ''' + HS(PLUR(row.StandardPayoffMonth, 'month')) + '''
                ''' + ('''
                  /''' + HS(PLUR(round(row.StandardPayoffMonth/12, 2), 'year')) + '''
                '''if (row.StandardPayoffMonth/12) > 1 else '') + '''
                <br>''' + HS(CUR0(row.StandardPayoffInterest)) + ''' interest
              </td>

                             */
                            td {
                                (debt.standard_payoff_month)
                                " "
                                @if debt.standard_payoff_month > 1 {
                                    "months"
                                } @else {
                                    "month"
                                }

                                @let payoff_year = debt.standard_payoff_month as f64 / 12.0;
                                @if payoff_year > 1.0 {
                                    " /"
                                    (format!("{:.2}", payoff_year))
                                    " "
                                    @if payoff_year > 1.0 {
                                        "years"
                                    } @else {
                                        "year"
                                    }

                                } @else {
                                    ""
                                }
                                br;
                                (format_currency_us_0(debt.standard_payoff_interest))
                                " interest"
                            }
                            /*

              <td style="white-space: nowrap;">
                ''' + HS(PLUR(row.PayoffMonth, 'month')) + '''
                ''' + ('''
                  /''' + HS(PLUR(round(row.PayoffMonth/12,2), 'year')) + '''
                ''' if (row.PayoffMonth/12) > 1 else '') + '''
                <br>''' + HS(CUR0(row.PayoffInterest)) + ''' interest
              </td>
                             */
                            td {

                                (debt.payoff_month.unwrap_or(0))
                                " "
                                @if debt.payoff_month.unwrap_or(0) > 1 {
                                    "months"
                                } @else {
                                    "month"
                                }

                                @let payoff_year = debt.payoff_month.unwrap_or(0) as f64 / 12.0;
                                @if payoff_year > 1.0 {
                                    " /"
                                    (format!("{:.2}", debt.payoff_month.unwrap_or(0) as f64 / 12.0))
                                    " "
                                    @if payoff_year > 1.0 {
                                        "years"
                                    } @else {
                                        "year"
                                    }
                                } @else {
                                    ""
                                }
                                br;
                                (format_currency_us_0(debt.payoff_interest))
                            }
                            /*
                            <td class="text-right">''' + HS(CUR0(row.StandardPayoffInterest - row.PayoffInterest)) + '''</td>
                             */
                            td {
                                @ let interest = debt.standard_payoff_interest - debt.payoff_interest; {
                                  (format_currency_us_0(interest))
                                }
                            }
                        }
                    }
                }
                tfoot {
                    tr {
                        /*
                        <td>100% Paid Off:</td>
                        <td>''' + HS(DebtFreeInfo.MaxStandardPayoffDate) + '''</td>
                        <td>''' + HS(DebtFreeInfo.MaxPayoffDate) + '''</td>
                        <td class="text-right">Saved ''' + HS(CUR0(DebtFreeInfo.InterestSaved)) + '''</td>
                        */
                        td {
                            "100% Paid Off:"
                        }
                        td {
                            (debt_info.max_standard_payoff_date)
                        }
                        td {
                            (debt_info.max_payoff_date)
                        }
                        td {
                            "Saved " (format_currency_us_0(debt_info.interest_saved))
                        }
                    }
                }
            }
        }
    }
}

#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;
        use bux::format_currency_us_0;
        //use df4l_zero::calc::format_decimal_optional;
        //use df4l_zero::calc::DebtInfo;
        //use crate::calc::DebtInfoDebtInfo;
        //use crate::calc::format_decimal_optional;

        //use crate::api::client0::client_debt_free_info::client_debt_free_info;
        use df4l_zero::api::client_debt_free_info::client_debt_free_info;
        use df4l_zero::api::client_debt_free_info::client_debt_free_info::date_formatted_yy_mon;
        use df4l_zero::api::client_debt_free_info::client_debt_free_info::format_decimal_optional;

        // Use df4l_zero API to return all the data, excluding plan
        let client_debt_info = client_debt_free_info::call(
            app,
            identity,
            client_debt_free_info::Input {
                advisor_uuid: path.advisor_uuid,
                client_uuid: path.client_uuid,
                generate_plan: false,
            },
        )
        .await?;

        /*
        // Generate plan
        let mut debt_info = DebtInfo::new(
            _app,
            client_debt_info
        );

        // Generate the debt payoff plan
        match debt_info.generate_plan(None) {
            Ok(_) => {
                println!("Plan generated successfully!");
                // use Debug trait to print the plan
                println!("{:?}", debt_info);
            }
            Err(e) => {
                println!("Error generating debt plan: {}", e);
                doc.add_error(&granite::Error::validation(e));
            }
        }
        */
        ////////////////////////////////////////////////////////////////////////////

        doc.page_nav_edit_record(
            "Edit Client",
            &df4l_zero::ml_advisor_client0_edit(path.advisor_uuid, path.client_uuid),
        );
        doc.set_title("Debt Free Program Details");
        doc.set_body_display_fluid();

        doc.add_body(html!(
            grid-12 {
                cell-3 {
                    panel {
                        content {
                            d2c-client-info {
                                h1 { (client_debt_info.client.name) }
                                hr;
                                p.status {
                                    i.fas.fa-bars {}
                                    @if client_debt_info.client.active {
                                        label-tag.success { "Active" }
                                    } @else {
                                        label-tag.danger { "Inactive" }
                                    }
                                }
                                @if let Some(phone) = &client_debt_info.client.phone {
                                    p.phone {
                                        i.fas.fa-phone-alt {}
                                        (phone)
                                    }
                                }
                                @if let Some(phone2) = &client_debt_info.client.phone2 {
                                    p.phone {
                                        i.fas.fa-phone-alt {}
                                        (phone2)
                                    }
                                }
                                @if let Some(email) = &client_debt_info.client.email {
                                    p.email {
                                        i.far.fa-envelope {}
                                        a href=(format!("mailto:{}", email)) { (email) }
                                    }
                                }
                            }
                            hr;
                            p {
                                small { b{"Minimum Monthly Debt Payments:"} }
                                br;
                                span { (format_currency_us_0(client_debt_info.debt_info.minimum_monthly_payment.unwrap_or_default())) "/mo" }
                            }
                            hr;
                            p {
                                small { b{"Combined Premium And Debt Snowball Budget:"} }
                                br;
                                annual_budget
                                span { (format_decimal_optional(client_debt_info.debt_info.annual_budget, "-"))  }
                            }
                            hr;
                            p {
                                small { b{"Total Insurance Premium:"} }
                                br;
                                span { (format_decimal_optional(client_debt_info.client.annual_insurance_premium,  "-")) }
                            }
                            hr;
                            p {
                                small { b{"Insurance Premium Base:"} }
                                br;
                                span { (format_decimal_optional(client_debt_info.client.annual_insurance_base,  "-")) }
                            }
                            hr;
                            p style="margin-bottom: 0;" {
                                small { b {"Insurance Premium PUA:"} }
                                br;
                                span { (format_decimal_optional(client_debt_info.client.annual_insurance_pua,  "-")) }
                            }

                            @if !client_debt_info.client.debt_free_extra_pua_list.is_empty() {
                                hr;
                                p {
                                    small { b {"Extra PUA Contributions:"} }
                                    br;
                                    @for extra_pua in &client_debt_info.client.debt_free_extra_pua_list {
                                        span { "Month " (extra_pua.month) 
                                        " " i .fas.fa-arrow-right {} " "
                                        (format_currency_us_0(extra_pua.amount)) }
                                        br;
                                    }
                                }
                            }
                        }
                    }

                }
                cell-6 {
                    d2c-program-overview {
                        grid-3 {
                            panel {
                                content {
                                    div {
                                        h5 { "Paid Debts" }
                                        p { (format_currency_us_0(client_debt_info.debt_info.total_paid)) }
                                    }
                                    i.fas.fa-arrow-up style="color: #198754;" {}
                                }
                            }
                            panel {
                                content {
                                    div {
                                        h5 { "Unpaid Debts" }
                                        p { (format_currency_us_0(client_debt_info.debt_info.total_unpaid)) }
                                    }
                                    i.fas.fa-arrow-down style="color: #ffc107;" {}
                                }
                            }
                            panel {
                                content {
                                    div {
                                        h5 { "Est. Cash Value" }
                                        p { (format_currency_us_0(client_debt_info.debt_info.esimated_cash_value))}
                                    }
                                    i.fas.fa-dollar-sign style="color: #0d6efd;" {}
                                }
                            }
                        }
                    }
                    d2c-debt-list {
                        table.striped {
                            thead {
                                tr {
                                    th { "Debt" }
                                    th { "Balance" }
                                    th { "Mo. Payment"}
                                    th { "Interest" }
                                    th { "EIR" }
                                }
                            }
                            tbody {
                                @for debt in &client_debt_info.debt_info.debt_list {
                                    @let class_name = if debt.is_paid_off_as_of_today { "paid" } else { "" };
                                    tr class=(class_name) {
                                        td { (debt.full_name) }
                                        td { (format_currency_us_0(debt.balance)) }
                                        td { (format_currency_us_0(debt.monthly_payment)) }
                                        td { (debt.interest_rate) "%" }
                                        td { (debt.effective_interest_cost_percent) "%" }
                                    }
                                }
                            }
                        }
                    }
                }
                cell-3 {
                    d2c-program-timeline {
                        panel.complete {
                            content {
                                div {
                                    p { (date_formatted_yy_mon(client_debt_info.debt_info.start_year, client_debt_info.debt_info.start_month))  }
                                }
                                div {
                                    i.fas.fa-circle {}
                                }
                                div {
                                    h5 {
                                        "Program Started"
                                        br;
                                        small { "Debt Balance" }
                                    }
                                    label-tag.danger { (format_currency_us_0(client_debt_info.debt_info.debt_balance.unwrap_or_default())) }
                                }
                            }
                        }

                        //@for debt in &debt_info.paid_off_debts() {
                        @for debt in client_debt_info.debt_info.debt_list {
                            @let panel_class = if debt.is_paid_off_as_of_today { "complete" } else { "" };
                            @let icon_class = if debt.is_paid_off_as_of_today { "fas fa-circle" } else { "fas fa-spinner" };
                            panel class=(panel_class) {
                                content {
                                    div {
                                        p {(debt.payoff_date_formatted_yy_mm.clone().unwrap_or_default())}
                                    }
                                    div {
                                        i class=(icon_class) {}
                                    }
                                    div {
                                        h5 {
                                            (debt.full_name)
                                            @if debt.is_paid_off_as_of_today {
                                                br;
                                                small { "Paid Off" }
                                            }
                                        }
                                        p { (format_currency_us_0(debt.balance)) }
                                    }
                                }
                            }
                        }

                    }
                }
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}

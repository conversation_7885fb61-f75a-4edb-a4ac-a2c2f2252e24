/* For legal landscape printing */
@media print {

    /* 1. Expand the default content container */
    content-container {
      width: 100% !important;
      max-width: 100% !important;
      padding: 0 !important;
      margin: 0 auto !important;
      overflow: visible !important;
    }
  
    /* 2. Hide UI clutter (navs, buttons, layout shells) */
    split-button,
    .btn, .btn-wrapper, .dropdown-wrapper,
    .breadcrumb, page-nav-wrapper, nav-wrapper, nav, footer {
      display: none !important;
    }

    @page {
        size: legal landscape;
    }

    table-wrapper.data-list {
        overflow: visible !important;
    }

    table {
        width: max-content !important;
        min-width: 100% !important;
    }
}


/*********************************************************/
/* Stylized data table */
/*********************************************************/

table-wrapper.data-list {
    
    table {
        border: none;

        thead {

            tr {
                
                th {
                    border: 1px solid #333;
                    padding: 2px 4px;
                    font-size: 10pt;
                    position: sticky;
                    background-color: white;
                    vertical-align: middle;
                }
            }

            tr:nth-child(1) {

                th {
                    top: 0px;
                    height: 25px;
                    text-align: center;

                    &:last-child {
                        text-align: left;
                    }
                }
            }

            tr:nth-child(2) {

                th {
                    top: 25px;
                    height: 100px;
                    text-align: left;

                    &:last-child {
                        text-align: left;
                    }
                }
            }

            tr:nth-child(3) {

                th {
                    height: 2px;
                    background-color: black;
                    top: 125px;
                }
            }
        }

        tbody {

            tr {

                td {
                    border: 1px solid #333;
                    padding: 2px 4px;
                    font-size: 10pt;
                    vertical-align: middle;

                    &.cp-month {
                        color: white;
                        font-weight: bold;
                        background-color: #999;
                    }

                    &.cp-budget { 
                        background-color: aquamarine; 
                    }

                    &.cp-insurance { 
                        background-color: beige; 
                    }
                    
                    &.cp-pua { 
                        background-color: lavender; 
                    }

                    &.cp-loan { 
                        background-color: lightgreen; 
                    }

                    &.cp-debt { 
                        background-color: pink; 
                    }

                    &.cp-cash { 
                        background-color: lightblue; 
                    }

                    &.cp-message { 
                        background-color: #ffccee;
                        text-align: left;
                        white-space: nowrap;
                    }

                    &.cp-debt-0 { background-color: #E0FFE0; }
                    &.cp-debt-1 { background-color: #E0FFFB; }
                    &.cp-debt-2 { background-color: #E0EBFF; }
                    &.cp-debt-3 { background-color: #E6E0FF; }
                    &.cp-debt-4 { background-color: #F9E0FF; }
                    &.cp-debt-5 { background-color: #FFE0EF; }
                    &.cp-debt-6 { background-color: #FFEDE0; }
                    &.cp-debt-7 { background-color: #F6FFE0; }
                    &.cp-debt-8 { background-color: #FFF9E0; }
                    &.cp-debt-9 { background-color: #FFE0E2; }
                    &.cp-debt-10 { background-color: #FFE0FB; }
                    &.cp-debt-11 { background-color: #F0E0FF; }
                    &.cp-debt-12 { background-color: #E0E2FF; }
                    &.cp-debt-13 { background-color: #E0F6FF; }
                    &.cp-debt-14 { background-color: #E0FFEE; }
                }
            }
        }
    }
}

/*********************************************************/

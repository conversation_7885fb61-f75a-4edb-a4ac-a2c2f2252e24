/* For legal printing */
@media print {

    /* 1. Expand the default content container */
    content-container {
      width: 100% !important;
      max-width: 100% !important;
      padding: 0 !important;
      margin: 0 auto !important;
      overflow: visible !important;
    }
  
    /* 2. Hide UI clutter (navs, buttons, layout shells) */
    split-button,
    .btn, .btn-wrapper, .dropdown-wrapper,
    .breadcrumb, page-nav-wrapper, nav-wrapper, nav, footer {
      display: none !important;
    }

    @page {
        size: legal portrait; /* US Legal size */
    }
}
  

page {
    page-break-after: always;
    padding: 0;
    margin: 1rem auto;
    background: white;
    max-width: 8.5in;
    height: 12in;
    outline: 1px dotted black;
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    page-header {
        height: 1in;
        background-color: silver;
        box-sizing: border-box;

        .blue-header {
            background-color: #11294f;
            width: 1015px;
            height: 49px;
        }
    }


    page-body {
        flex: 1;
        margin-top: 1rem;
        padding-left: 2rem;
        padding-right: 2rem;
        box-sizing: border-box;

        hr {
            margin: 1rem auto;
            width: 80%;
            border: none;
            border-top: 2px solid black;
        }
    }

    page-footer {
        text-align: center;
        font-size: 10pt;
        border-top: 1px solid #ccc;
        background-color: #eee;
        padding-top: 10px;
        padding-bottom: 15px;
        width: 100%;
    }
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

h2 {
    color: #11294f;
    font-size: 17pt;
    font-weight: bold;
    margin: 15pt 5pt;
    text-align: center;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;

    th, td {
        padding: 0.5em;
    }

    th {
        background-color: #eee;
    }
}

.highlight-box {
    padding: .2in .25in;
    margin: .25in .75in;
    font-size: 125%;
    text-align: center;
    border: 2px solid #000;
    border-radius: .5in;

    &.highlight-blue {
        background-color: #E0FFFB;
        font-size: 150%;
    }

    &.highlight-green {
        background-color: #E0FFE0;
    }

    &.highlight-beige {
        background-color: beige;
    }
}

.page5 {
    margin-bottom: 1rem;
}

img {
    &.page1-heading-image {
        max-width: 100%;
    }
}

tr.highlight-special {
    td {
        background-color: wheat;
        font-weight: bold;
        border-bottom: 1px solid #00000020;
    }
}

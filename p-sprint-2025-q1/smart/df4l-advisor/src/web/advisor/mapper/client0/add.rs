#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/add; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        _app: App,
        _identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        //use crate::api::client::add::add;

        use approck::html;

        //load advisor
        use crate::api::advisor::detail::advisor_detail;
        let advisor_response = advisor_detail::call(
            _app,
            _identity,
            advisor_detail::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        // Extract the output from the response
        let advisor = match advisor_response {
            crate::api::advisor::detail::advisor_detail::Response::Output(output) => output,
            _ => return Err(granite::process_error!("Failed to get advisor details")),
        };

        doc.set_title(&format!("Add New Client For Advisor {}", advisor.name()));

        let mut form_panel = bux::component::add_cancel_form_panel(
            &format!("Add New Client For Advisor {}", advisor.name()),
            &df4l_zero::ml_advisor_client0_list(path.advisor_uuid),
        );

        form_panel.set_hidden("advisor_uuid", path.advisor_uuid);

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            grid-12 {
                cell-6 {
                    (bux::input::text::string::name_label_value("first_name", "Client First Name:", None))
                    (bux::input::text::string::name_label_value("last_name", "Client Last Name:", None))
                    (bux::input::text::string::name_label_value("email", "Email:", None))
                    (bux::input::text::string::name_label_value_help("phone", "Send Text Messages To Phone Number", None, "Do not include country code."))
                    (bux::input::text::string::name_label_value_help("phone2", "Send Text Messages To Additional Phone Number", None, "Do not include country code."))
                }
                cell-6 {
                    (bux::input::text::currency::currency_input_with_help("monthly_budget", "Monthly Premium And Debt Snowball Budget", None, "The total combined budget for both Insurance Premium and Debt Snowball. The more the better, but keeping it realistic is best."))
                    (bux::input::text::currency::currency_input_with_help("annual_insurance_premium", "Annual Insurance Premium", None, "The total annual premium of the Life Insurance Policy (includes PUA and Base)."))
                    (bux::input::text::currency::currency_input_with_help("annual_insurance_pua", "Annual Insurance PUA", None, "The amount of the total premium which is Paid Up Additions (PUA)."))
                }
            }
        ));
        doc.add_body(html! {
            section {
                (form_panel)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}

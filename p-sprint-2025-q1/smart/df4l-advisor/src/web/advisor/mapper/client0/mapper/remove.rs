#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client0/{client_uuid:Uuid}/remove; AUTH None; return HTML;)]
pub mod page {
    use crate::web::status::AdvisorStatusSelection;
    use bux::component::set_status_panel::StatusPanel;
    pub async fn request(doc: Document) -> Response {
        use maud::{Render, html};

        doc.set_title("Remove Client");

        let status_panel = StatusPanel::with_defaults(
            "",
            html! {
                p { "Select the status to remove the Advisor." }
            },
            vec![
                AdvisorStatusSelection::Active,
                AdvisorStatusSelection::Inactive,
            ],
            "/advisor/00000000-0000-0000-0000-000000000000/client0/",
        );

        doc.add_body(html!(
            bux-action-panel {
                (status_panel.render())
            }
        ));
        Response::HTML(doc.into())
    }
}

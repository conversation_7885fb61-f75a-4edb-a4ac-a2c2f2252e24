//-------------------------------------------------------------------------------------------------
// 1. Import Components
import "./policy.mcss";
import "@bux/component/form_wizard.mts";
import "@bux/input/text/currency.mts";
import "@bux/input/select/nilla.mts";

//-------------------------------------------------------------------------------------------------
// 2. Import Code
import { SEC, SEC_nullable } from "@granite/lib.mts";
import { client_policy_set } from "./policyλ.mts";
import BuxInputTextCurrency from "@bux/input/text/currency.mts";
import BuxInputSelectNilla from "@bux/input/select/nilla.mts";
import FormWizard from "@bux/component/form_wizard.mts";

//-------------------------------------------------------------------------------------------------
// 4. Select Elements

const $form = SEC(HTMLFormElement, document, "#policy-editor");

// Entire functionality of page conditional on this element existing.
if (SEC_nullable(HTMLElement, $form, "content")) {
    const client_uuid = SEC(HTMLInputElement, $form, "[name=client_uuid]").value;
    const $policy_total_premium = SEC(BuxInputTextCurrency, $form, "[name=policy_total_premium]");
    const $pua_split = SEC(BuxInputSelectNilla, $form, "[name=pua_split]");
    const $pua_contribution = SEC(BuxInputTextCurrency, $form, "[name=pua_contribution]");
    const $next_button = SEC(HTMLAnchorElement, $form, "a.x-next-button");

    $next_button.addEventListener("click", (event) => {
        event.preventDefault();
        $form.requestSubmit();
    });

    // Demo data button handler
    const $demo_link = SEC_nullable(HTMLAnchorElement, $form, "#demo-notice a");
    if ($demo_link) {
        $demo_link.addEventListener("click", (event: Event) => {
            event.preventDefault();

            // Prefill demo policy values
            $policy_total_premium.set_text("500");
            $pua_split.value = "Max";
        });
    }

    //-------------------------------------------------------------------------------------------------
    // 5. Form Wizard

    new FormWizard({
        $form,
        api: client_policy_set.api,

        err: (errors) => {
            $policy_total_premium.set_e(errors.policy_total_premium);
            $pua_split.set_e(errors.budget_pua_split);
            $pua_contribution.set_e(errors.budget_pua_contribution);
        },

        get: () => {
            return {
                client_uuid: client_uuid,
                policy_total_premium: $policy_total_premium.value,
                budget_pua_split: client_policy_set.PuaSplit_Partial_map[$pua_split.value ?? ""],
                budget_pua_contribution: $pua_contribution.value,
            };
        },

        set: (_value) => {
        },

        out: (_output) => {
            window.location.href = $next_button.href;
        },
    });
}

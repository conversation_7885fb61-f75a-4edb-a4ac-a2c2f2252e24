@import "./mod.mcss";

#icover-wizard {
    content {
        text-align: center;

        h2 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        p {
            margin-bottom: 1.5rem;
            color: #555;
            line-height: 1.6;
        }

        a#icover-link {
            display: inline-block;
            background-color: #27ae60;
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            text-decoration: none;
            font-size: 1.2rem;
            font-weight: bold;
            transition: background-color 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin: 4rem 0;

            &:hover {
                background-color: #229954;
                text-decoration: none;
                color: white;
            }

            i {
                margin-right: 0.5rem;
            }
        }

        #errors {
            background-color: #f8d7da;
            border: 1px solid #dc3545;
            border-radius: 0.25rem;
            padding: 1rem;
            margin: 1rem 0;
            color: #dc3545;

            p {
                color: inherit;
                margin-bottom: 1rem;
                font-weight: 600;
            }

            ul {
                list-style-type: disc;
                padding-left: 1.5rem;
                text-align: left;

                li {
                    margin-bottom: 0.5rem;
                    color: inherit;
                }
            }
        }

    }
}

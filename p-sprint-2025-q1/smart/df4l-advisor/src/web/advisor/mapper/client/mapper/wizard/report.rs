#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/report; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        // imports and dependencies
        use super::super::WizardStep;
        use super::super::client_wizard_context;
        use approck::html;

        // document configuration
        doc.add_page_nav_action(
            "Print",
            "print-button",
            Some("primary"),
            Some("fas fa-print"),
        );

        doc.add_page_nav_link_targeted(
            "View Calculations",
            &df4l_zero::ml_advisor_client_wizard_dump(path.advisor_uuid, path.client_uuid),
            None,
            Some("fas fa-list"),
            "_blank",
        );

        // data fetching
        let wizard_data = client_wizard_context::call(
            app,
            identity,
            client_wizard_context::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        // Read demo status from the database via wizard_data
        let is_demo = wizard_data.demo;

        // TODO: if this is a pattern we want to use, then it needs to have an identity and security check
        let wizard_core = df4l_zero::client::wizard::Wizard::load(
            app,
            &app.postgres_dbcx().await?,
            path.client_uuid,
        )
        .await?;

        // Calculate plan output for payoff scenarios
        let plan_output = wizard_core.to_plan_output().map_err(|e| {
            granite::Error::new(granite::ErrorType::Unexpected)
                .set_external_message(format!("Plan calculation error: {e}"))
        })?;

        let debts_data = &plan_output.debts;

        // wizard setup and configuration
        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::Report, wizard_data)?;

            wizard.set_id("report");
            wizard.set_hidden("client_uuid", path.client_uuid);

            wizard.add_heading("Don't Forget the Problem");
            let interest_percentage = format!(
                "{}%",
                plan_output
                    .monthly_payment_percent_that_is_interest
                    .round_dp(0)
            );
            let description_text = format!(
                "{interest_percentage} of your monthly payment goes to interest! You keep working, but the bank keeps most of it. That's the trap we solve."
            );
            wizard.add_description(&description_text);

            // main layout structure
            wizard.add_body(html!(
                    @if is_demo {
                        (super::super::demo_notice_html(false))
                    }

                    df4l-advisor-debt-free {
                        // Summary cards
                        x-section {
                            h2 {
                                "Section 1: Effective Interest Cost on Your Next Payment"
                                label-tag.default { "How much of your next payment is interest?" }
                            }
                            x-summary-cards {
                                x-card.payment-mo {
                                    x-label { "Payment/Mo." }
                                    x-value { (bux::format_currency_us_0(plan_output.monthly_payment_total)) }
                                }
                                x-card.interest-portion {
                                    x-label { "Interest Portion" }
                                    x-value { (bux::format_currency_us_0(plan_output.monthly_interest_total)) }
                                }
                                x-card.principal-portion {
                                    x-label { "Principal Portion" }
                                    x-value { (bux::format_currency_us_0(plan_output.monthly_principal_total)) }
                                }
                            }

                            // Top progress bar showing overall interest/principal split
                            div.progress-bar.mb-3 {
                                @let percent_interest = plan_output.monthly_payment_percent_that_is_interest;
                                @let principal_percent = plan_output.monthly_payment_percent_that_is_principal;
                                progress-bar {
                                    progress-remaining style=(format!("width: {}%;", principal_percent)) {}
                                    progress-used style=(format!("width: {}%;", percent_interest)) {}
                                }
                                progress-details {
                                    span { (format!("{}% Principal", principal_percent.round_dp(1))) }
                                    span { (format!("{}% Interest", percent_interest.round_dp(1))) }
                                }
                            }

                            table-wrapper.data-list {
                                table {
                                    thead {
                                        tr {
                                            th { "Debt Name" }
                                            th { "Balance" }
                                            th { "Interest Rate" }
                                            th { "Payment/Mo." }
                                            th { "Interest vs. Principal" }
                                        }
                                    }
                                    tbody {
                                        @for debt in debts_data {
                                            tr {
                                                td.debt-name { (debt.name) }
                                                td.debt-balance { (bux::format_currency_us_0(debt.balance)) }
                                                td { (bux::format_percentage_us_0(debt.annual_interest_percentage)) }
                                                td { (bux::format_currency_us_0(debt.monthly_payment_amount)) }
                                                td.interest-vs-principal {
                                                    @let percent_that_is_interest = debt.percent_that_is_interest.unwrap_or_default();
                                                    @let principal_percent = granite::Decimal::new(100,0) - percent_that_is_interest;
                                                    progress-bar {
                                                        progress-remaining style=(format!("width: {}%;", principal_percent)) {}
                                                        progress-used style=(format!("width: {}%;", percent_that_is_interest)) {}
                                                    }
                                                    progress-details {
                                                        span { (format!("{}% Principal", principal_percent.round_dp(0))) }
                                                        span { (format!("{}% Interest", percent_that_is_interest.round_dp(0))) }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Section 2: Current Balance vs Balance Plus Interest
                    x-section {
                        h2 {
                            "Section 2: Current Balance Due vs. Balance Plus Interest Due"
                            label-tag.default { "Comparing minimum payments vs. Debt2Capital™" }
                        }
                        div.balance-comparison {
                            @let total_balance = plan_output.debt_principal_total;
                            @let min_interest = plan_output.min_interest_total;
                            @let min_total_with_interest = plan_output.min_payments_total;

                            @let d2c_interest = plan_output.d2c_interest_total;
                            @let d2c_total_with_interest = plan_output.d2c_payments_total;

                            // Calculate percentages based on the largest total (min payments) to keep principal constant
                            @let principal_percentage = (total_balance / min_total_with_interest * granite::Decimal::new(100, 0)).round_dp(1);
                            @let min_interest_percentage = (min_interest / min_total_with_interest * granite::Decimal::new(100, 0)).round_dp(1);
                            @let d2c_interest_percentage = (d2c_interest / min_total_with_interest * granite::Decimal::new(100, 0)).round_dp(1);

                            @let cash_value = plan_output.benefit_20year_cash_value;
                            @let cash_value_percentage = (cash_value / min_total_with_interest * granite::Decimal::new(100, 0)).round_dp(1);

                            div.balance-bar {
                                div.bar-label {
                                    span { "Current Loan Balance + Interest at minimum payments " }
                                    span.amount { (bux::format_currency_us_0(min_total_with_interest)) }
                                }
                                div.progress-track.stacked {
                                    div.segment-principal style=(format!("width: {}%;", principal_percentage)) {}
                                    div.segment-interest style=(format!("width: {}%;", min_interest_percentage)) {}
                                }
                                div.bar-details {
                                    span.red { (bux::format_currency_us_0(min_interest)) " Interest" }
                                    " + "
                                    span { (bux::format_currency_us_0(total_balance)) " Principal" }
                                }
                            }

                            div.balance-bar {
                                div.bar-label {
                                    span { "Current Loan Balance + Interest with Debt2Capital™ " }
                                    span.amount { (bux::format_currency_us_0(d2c_total_with_interest)) }
                                }
                                div.progress-track.stacked {
                                    div.segment-principal style=(format!("width: {}%;", principal_percentage)) {}
                                    div.segment-interest.d2c style=(format!("width: {}%;", d2c_interest_percentage)) {}
                                }
                                div.bar-details {
                                    span.green { (bux::format_currency_us_0(d2c_interest)) " Interest" }
                                    " + "
                                    span { (bux::format_currency_us_0(total_balance)) " Principal" }
                                }
                            }

                            div.balance-bar {
                                div.bar-label {
                                    span { "Plus: Accumulated Cash Value with Debt2Capital™ " }
                                    span.amount { (bux::format_currency_us_0(cash_value)) }
                                }
                                div.progress-track.stacked {
                                    div.segment-cash-value style=(format!("width: {}%;", cash_value_percentage)) {}
                                }
                                div.bar-details {
                                    span.blue { (bux::format_currency_us_0(cash_value)) " Cash Value Available" }
                                }
                            }

                            div.savings-highlight {
                                div.savings-amount {
                                    "Interest Savings: "
                                    span.highlight-green { (bux::format_currency_us_0(min_interest - d2c_interest)) }
                                }
                                div.savings-description {
                                    "By using Debt2Capital™, you save "
                                    strong { (bux::format_currency_us_0(min_interest - d2c_interest)) }
                                    " in interest payments compared to minimum payments alone."
                                }
                            }
                        }
                    }

                    // Section 3: Debt Free When?
                    x-section {
                        h2 {
                            "Section 3: Debt Free When?"
                            label-tag.default { "Comparing minimum payments vs. minimum + $1,000/mo" }
                        }

                        p.disclaimer {
                            "The illustration above is based on the minimum monthly payment of "
                            (bux::format_currency_us_0(plan_output.monthly_payment_total))
                            "/mo which does not include the additional monthly premium of $1,000 of which is paid into the whole life policy, 50.00% is directed to paid up additions. Results will vary depending on the policy performance and results may vary."
                        }

                        div.highlight {
                            p {
                                span.highlight-red { "With Debt2Capital™, you will pay off your debt " }
                                span.highlight-red {
                                    strong {
                                        @let first_item = &plan_output.payoff_items[0];
                                        (format!("{} months / {:.1} years", first_item.time_savings_months, first_item.time_savings_months as f64 / 12.0))
                                    }
                                    " faster saving you "
                                    strong {
                                        (bux::format_currency_us_0(plan_output.min_interest_total - plan_output.d2c_interest_total))
                                    }
                                    "!"
                                }
                            }
                        }

                        div.timeline {
                            h3 { "Your Debt2Capital™ Action Plan And Timeline" }
                            div.timeline-container {
                                div.timeline-line {}
                                @for (index, payoff_item) in plan_output.payoff_items.iter().enumerate() {
                                    @let is_left = index % 2 == 0;
                                    @if is_left {
                                        div.timeline-item.left {
                                            div.timeline-content {
                                                strong { (payoff_item.debt_name) " " } strong { "PAID" } br {}
                                                (payoff_item.payoff_date_d2c.format("%b %Y"))
                                            }
                                            div.timeline-summary {
                                                @if payoff_item.interest_savings > granite::Decimal::ZERO {
                                                    div.summary-item {
                                                        span.summary-label { "Interest Saved: " }
                                                        span.summary-value { (bux::format_currency_us_0(payoff_item.interest_savings)) }
                                                    }
                                                }
                                                @if payoff_item.time_savings_months > 0 {
                                                    div.summary-item {
                                                        span.summary-label { "Time Saved: " }
                                                        span.summary-value { (format!("{} months", payoff_item.time_savings_months)) }
                                                    }
                                                }
                                                div.summary-item {
                                                    span.summary-label { "Balance: " }
                                                    span.summary-value { (bux::format_currency_us_0(payoff_item.total_balance)) }
                                                }
                                            }
                                            span.timeline-dot {}
                                        }
                                    } @else {
                                        div.timeline-item.right {
                                            span.timeline-dot {}
                                            div.timeline-summary {
                                                @if payoff_item.interest_savings > granite::Decimal::ZERO {
                                                    div.summary-item {
                                                        span.summary-label { "Interest Saved: " }
                                                        span.summary-value { (bux::format_currency_us_0(payoff_item.interest_savings)) }
                                                    }
                                                }
                                                @if payoff_item.time_savings_months > 0 {
                                                    div.summary-item {
                                                        span.summary-label { "Time Saved: " }
                                                        span.summary-value { (format!("{} months", payoff_item.time_savings_months)) }
                                                    }
                                                }
                                                div.summary-item {
                                                    span.summary-label { "Balance: " }
                                                    span.summary-value { (bux::format_currency_us_0(payoff_item.total_balance)) }
                                                }
                                            }
                                            div.timeline-content {
                                                strong { (payoff_item.debt_name) " " } strong { "PAID" } br {}
                                                (payoff_item.payoff_date_d2c.format("%b %Y"))
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Section 4: Estimated Capital After Your Debts Are Paid
                    x-section {
                        h2 {
                            "Section 4: Estimated Capital After Your Debts Are Paid"
                            label-tag.default { "Why building your own \"bank\" matters?" }
                        }

                        p { "Debt Snowballing can be a very effective method to eliminate debt. However, with the traditional debt snowball you will always need to return to the banks, auto financiers, and credit card companies for your next purchase. Once a dollar is spent you don't have it to make your next purchase." }
                        p { "With Debt2Capital™, your debts get paid off plus there is an accumulated capital that is available to you that you then \"loan\" to yourself. By making yourself the only banker you will need in the future after you pay off your current debts, you will save significant dollars otherwise lost to the interest expense of future mortgages, student loans, auto loans, credit cards, and other forms of borrowed money." }
                        p { "The most important aspect of Debt2Capital™ is to be HONEST with yourself. The key to Debt2Capital™ success is that you make sure you pay back your whole life policy the dollar amount a commercial bank would have charged you. Over time, this accumulated capital will help you make future major purchases and keep you out of debt for life. Eventually when you are in retirement, the capital you’ve accumulated can be used for major purchases." }
                        p { "Your Debt2Capital™ advisor will continue to guide you through the process and help you focus on compounding interest for yourself and not for the lending institutions lending you money at your expense. The advisor providing this information to you is an independent advisor. SMART Retirement Corporation is not responsible for the recommendations made by the independent advisor providing you this analysis." }
                    }



                    // Section 5: Summary of Benefits
                    x-section {
                        h2 {
                            "Section 5: Summary of Benefits"
                            label-tag.default { "Illustrative policy values and interest savings" }
                        }

                        div.metrics {
                            div.metric {
                                div.metric-value {
                                    (bux::format_currency_us_0(plan_output.benefit_total))
                                }
                                p.metric-label { "Total Accumulated Net Cash Plus Estimated Interest Savings" }
                            }
                            div.metric {
                                div.metric-value {
                                    @let final_policy_month = plan_output.policy.month_list.last().unwrap();
                                    (bux::format_currency_us_0(final_policy_month.cash_value_total))
                                }
                                p.metric-label { "Accumulated Net Cash Value" }
                            }
                            div.metric {
                                div.metric-value {
                                    (bux::format_currency_us_0(plan_output.min_interest_total - plan_output.d2c_interest_total))
                                }
                                p.metric-label { "Estimated Interest Savings" }
                            }
                        }

                        div.disclaimer-note {
                            p {
                                "Based on the illustrated values of the policy ledger attached below, which are subject to change due to varying dividend rates, interest rates and other market conditions, the Debt2Capital™ advisor guiding you through the Debt2Capital™ process, we estimate a cash value of "
                                @let final_policy_month = plan_output.policy.month_list.last().unwrap();
                                (bux::format_currency_us_0(final_policy_month.cash_value_total))
                                " by the end of policy year 20 solely on information provided in the attached illustration."
                            }
                            p { "*This estimate is for educational purposes only and likely inaccurate due to additional purchases you make, new debts you incur, by the modification of your payment schedules or changes in the dividend and/or loan rates made by the Insurance Policy Carrier over this extended period of time." }
                            p { "This report is free of any charge. No fee can be charged for this report. Insurance policies pay compensation to the agent." }
                        }
                    }

                    // Scope of Analysis Disclaimer
                    x-section {
                        h2 {
                            "Scope of Analysis Disclaimer"
                            label-tag.default { "Read first" }
                        }
                        div.disclaimer-box {
                            p { "The analysis contained herein is conceptual in nature and provided to offer you an alternate plan to eliminate your current debts. The Debt2Capital™ approach uses a specific type of whole life insurance policy heavily funded with paid up additions which are accessible via policy loans to pay off your outstanding debts." }
                            p { "Debt2Capital™ is created for people with the sufficient incomes and the financial means to make regular payments against their existing debt obligations, who also have some extra money in their budget, and want a better way to pay-off their existing debt using a modified approach to snowballing their debt." }
                            p { "Debt2Capital™ is not a debt relief or debt settlement program. Debt2Capital™ does not renegotiate, settle, or in any way alter the terms of payment, interest rates, or other terms of your debt. DF4L™ does not make any payments for you or combine your loans into one or more new payment obligations." }
                            p { "You will be required to pay regular, ongoing premiums toward a whole life insurance policy with a paid-up additions feature that will compound interest inside your policy’s cash value. Premiums paid to whole life insurance policies are typically higher than those paid to term life insurance policies; but, term insurance has no cash value, just a death benefit. Some of the money you pay into the policy will be applied to a savings component of the policy, referred to as the cash value of your policy. Some of the money will go to insurance benefit expenses." }
                            p { "If you borrow money from the cash value portion of your policy (for example, to pay off a debt), the policy issuer will charge interest on the outstanding principal of that loan. However, these loans typically have lower interest rates than personal loans." }
                            p { "The report does not provide tax, legal, accounting or investment advice. The rates of return are hypothetical and cannot project future performance. The whole life product representations are estimates based on current economic conditions and policy dividend rates which are subject to change by the carrier without notice. The assumptions used to generate this report are estimates and not product specific. You should consult your tax advisor regarding taxation both federally and state specific." }
                            p { "The results of this analysis are heavily reliant on the data you provide concerning your outstanding debts. As time progresses, you may take additional loans which will significantly change the outcome. In addition, the whole life company may change their dividend rates and loan rates which will also change the outcome of this analysis. This report is conceptual and is for educational purposes only." }
                        }
                    }

                ));
            wizard
        };

        // document finalization
        doc.set_title("Debt Free When?");
        doc.add_body(html!((wizard)));
        Ok(Response::HTML(doc.into()))
    }
}

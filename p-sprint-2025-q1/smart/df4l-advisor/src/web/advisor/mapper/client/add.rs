#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/add?demo; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document, path: Path, qs: QueryString) -> Response {
        use df4l_zero::types::ClientType;
        use maud::html;

        let title = "Add a New Client";

        doc.set_title(title);

        let mut form_panel = bux::component::add_cancel_form_panel(
            title,
            &df4l_zero::ml_advisor_client_list(path.advisor_uuid),
        );

        form_panel.set_id("client-add");
        form_panel.set_hidden("advisor_uuid", path.advisor_uuid);
        form_panel.set_hidden("is_demo", qs.demo.to_string());

        if qs.demo {
            form_panel.add_body(html! {
                section id="demo-notice" {
                    "This is a demo client. It will be deleted after 72 hours. You may "
                    a href="#prefill" { "click here to prefill the data below." }
                }
            });
        }

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            #split {
                div {
                    grid-2 {
                        (bux::input::text::string::name_label_value("first_name", "First Name:", None))
                        (bux::input::text::string::name_label_value("last_name", "Last Name:", None))
                        (bux::input::text::string::name_label_value("phone", "Mobile Phone:", None))
                        (bux::input::text::string::name_label_value("email", "Personal Email:", None))
                    }

                    hr;

                    fieldset {
                        legend { "Client Type:" }
                        ul {
                            li {
                                label for="r1" { 
                                    input id="r1" type="radio" name="client_type" value="DebtManagement" checked;
                                    (ClientType::DebtManagement.label())
                                }
                            }
                            li {
                                label for="r2" { 
                                    input id="r2" type="radio" name="client_type" value="PolicyOnly";
                                    (ClientType::PolicyOnly.label())
                                }
                            }
                        }
                    }

                }
                div {
                    h2 { "About Adding a Client" }
                    p { "Providing the primary applicant's information is the first step to onboarding a new client on the Debt2Capital™ platform." }
                    p { "If the primary applicant is married, you will be able to enter spouse information on a subsequent step." }
                    hr;
                    p { "There are two types of clients you can add:" }
                    p { "1. " b { (ClientType::DebtManagement.label()) } " - This is a client who will be using the Debt2Capital™ platform to purchase an insurance policy and pay down their debt, using soft credit pulls to keep the plan on track each month." }
                    p { "2. " b { (ClientType::PolicyOnly.label()) } " - This is a client who will only be purchasing a Debt2Capital™ insurance policy." }
                }
            }
        ));
        doc.add_body(html! {
            (form_panel)
        });
        Response::HTML(doc.into())
    }
}

#[approck::api]
pub mod client_add {
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub client_type: ClientType,
        pub first_name: String,
        pub last_name: String,
        pub email: String,
        pub phone: String,
        pub is_demo: bool,
    }

    #[granite::gtype(ApiInput, ts_from = "@df4l-zero/typesλ.mts")]
    pub type ClientType = ::df4l_zero::types::ClientType;

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_uuid: Uuid,
        pub onboarding_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let mut dbcx = app.postgres_dbcx().await?;

        if !identity.client_add(input.advisor_uuid) {
            return Ok(Response::AuthorizationError(
                "insufficient permissions to client add".to_string(),
            ));
        }

        let mut error = Input_Error {
            advisor_uuid: None,
            first_name: None,
            last_name: None,
            email: None,
            phone: None,
            client_type: None,
            is_demo: None,
        };

        // Validate input
        let first_name = input.first_name.trim().to_string();
        let last_name = input.last_name.trim().to_string();
        let email = input.email.trim().to_string();
        let phone = input.phone.trim().to_string();

        if first_name.is_empty() {
            error.first_name = Some("First name is required.".to_string());
        }
        if last_name.is_empty() {
            error.last_name = Some("Last name is required.".to_string());
        }

        if email.is_empty() {
            error.email = Some("Email is required.".to_string());
        }
        if !granite::validate_email(&email) {
            error.email = Some("Invalid email address.".to_string());
        }

        if phone.is_empty() {
            error.phone = Some("Phone is required.".to_string());
        }
        if !granite::validate_phone_us(&phone) {
            error.phone = Some("Invalid United States phone number.".to_string());
        }

        if error.first_name.is_some()
            || error.last_name.is_some()
            || error.email.is_some()
            || error.phone.is_some()
        {
            return Ok(Response::ValidationError(granite::NestedError {
                outer: "Validation Error".to_string(),
                inner: Some(error),
            }));
        }

        // For insurance only clients, we let the user decide, otherwise, we set it to Max
        let budget_pua_split = match input.client_type {
            ClientType::PolicyOnly => None,
            ClientType::DebtManagement => Some("Max".to_string()),
        };

        // For insurance only clients, we don't enable CRS
        // For debt management clients, we enable CRS for the applicant
        let api_crs_user_enabled_applicant = match input.client_type {
            ClientType::PolicyOnly => None,
            ClientType::DebtManagement => Some(true),
        };

        // For insurance only clients, we don't enable CRS
        // For debt management clients, we don't enable CRS for the spouse
        let api_crs_user_enabled_spouse = match input.client_type {
            ClientType::PolicyOnly => None,
            ClientType::DebtManagement => Some(false),
        };

        // Transaction block
        let row = {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            let row = granite::pg_row!(
                db = dbtx;
                args = {
                    $advisor_uuid: &input.advisor_uuid,
                    $first_name: &first_name,
                    $last_name: &last_name,
                    $email: &email,
                    $phone: &phone,
                    $client_type: &input.client_type,
                    $is_demo: &input.is_demo,
                    $budget_pua_split: &budget_pua_split,
                    $api_crs_user_enabled_applicant: &api_crs_user_enabled_applicant,
                    $api_crs_user_enabled_spouse: &api_crs_user_enabled_spouse,
                };
                row = {
                    client_uuid: Uuid,
                };

                INSERT INTO
                    df4l.client
                    (
                        advisor_uuid,
                        first_name,
                        last_name,
                        email,
                        phone,
                        client_type,
                        is_demo,
                        budget_pua_split,
                        api_crs_user_enabled_applicant,
                        api_crs_user_enabled_spouse
                    )
                VALUES
                    (
                        $advisor_uuid,
                        $first_name,
                        $last_name,
                        $email,
                        $phone,
                        $client_type,
                        $is_demo,
                        $budget_pua_split,
                        $api_crs_user_enabled_applicant,
                        $api_crs_user_enabled_spouse
                    )
                RETURNING
                    client_uuid
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction for client add"))?;
            row
        };

        Ok(Response::Output(Output {
            client_uuid: row.client_uuid,
            onboarding_url: df4l_zero::ml_advisor_client_wizard(
                input.advisor_uuid,
                row.client_uuid,
            ),
        }))
    }
}

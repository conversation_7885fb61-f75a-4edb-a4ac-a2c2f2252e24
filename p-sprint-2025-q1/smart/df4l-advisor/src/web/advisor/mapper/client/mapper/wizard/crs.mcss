/* Credit Report Wizard - <PERSON><PERSON>tic Nested CSS */
.df4l-advisor-onboarding-crs {
    text-align: center;

    #Applicant, #Spouse {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1rem;

        h3 {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 24px;
        }

        label {
            color: #6c757d;
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        button {
            background: #0d6efd;
            border: none;
            color: white;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;

            &:hover {
                background-color: #0b5ed7;
            }
        }
    }

    a[href="#activate"], a[href="#deactivate"] {
        color: #0d6efd;
        text-decoration: none;
        margin-top: 1rem;
        display: block;
        font-size: 80%;
    }

    /* Special styling for Get Started state */
    #Applicant:has(button:contains("Get Started")) {
        border-left: 4px solid #28a745;
    }
}

form.bux-form-wizard {
    > footer {
        margin-top: 0 !important;
    }
}

//! # Client Onboarding Wizard - Introduction Page
//!
//! This module provides the introduction/landing page for the Debt2Capital™ client onboarding wizard.
//! The wizard guides financial advisors through a comprehensive 7-step process to onboard new clients
//! for debt elimination and wealth building strategies.
//!
//! ## Wizard Flow Overview
//!
//! The complete onboarding process consists of the following steps:
//!
//! 1. **Introduction** (this page) - Welcome and overview of the onboarding process
//! 2. **Client Info** - Collect basic client contact information and demographics
//! 3. **Credit Reports** - Pull and review client's credit report data
//! 4. **Review Debts** - Analyze and manage client's debt portfolio from credit reports and manual entries
//! 5. **Make Budget** - Set client's financial budget including extra payments and surplus
//! 6. **View Report** - Generate comprehensive Debt2Capital™ analysis and recommendations
//! 7. **Setup Policy** - Integration with iCover for whole life insurance policy setup
//!
//! ## Purpose
//!
//! This wizard enables financial advisors to:
//! - Systematically collect all necessary client financial information
//! - Pull and analyze credit report data for debt assessment
//! - Create personalized debt elimination strategies using the Debt2Capital™ methodology
//! - Generate professional reports showing potential savings and timelines
//! - Seamlessly transition clients to insurance policy setup through iCover integration
//!
//! The Debt2Capital™ approach uses whole life insurance policies with paid-up additions
//! to create a systematic debt elimination strategy that builds wealth while paying off debts.

#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use maud::html;

        doc.set_title("Client Onboarding Wizard");

        use super::super::client_wizard_context;

        let wizard_data = client_wizard_context::call(
            app,
            identity,
            client_wizard_context::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        // Read demo status from the database via wizard_data
        let is_demo = wizard_data.demo;

        // This is moved on the next line, but we need it after.
        let client_type = wizard_data.client_type.clone();

        let mut wizard = bux::component::form_wizard::new(WizardStep::Index, wizard_data)?;

        wizard.add_heading("Welcome to Debt2Capital™ Client Onboarding");

        match client_type {
            df4l_zero::types::ClientType::DebtManagement => {
                wizard.set_id("wizard-debtmanagement");
                wizard.add_description("Transform your debts today into capital for tomorrow.");

                wizard.add_body(html!(
                    @if is_demo {
                        (super::super::demo_notice_html(false))
                    }

                    div #wizard-intro-content {
                        div #benefits-section {
                            div #benefits-grid {
                                div {
                                    h4 { "🏦 Debt2Capital™" }
                                    p { "Turning Today's Debts Into Tomorrow's Capital" }
                                }
                                div {
                                    h4 { "💰 Debt Elimination" }
                                    p { "Pay off debts faster" }
                                }
                                div {
                                    h4 { "📈 Build Capital" }
                                    p { "Continuous Compounding growth" }
                                }
                                div {
                                    h4 { "🛡️ Cash Accumulation + Benefits" }
                                    p { "Financial Security" }
                                }
                            }
                        }

                        grid-2 {
                            div #intro-section {
                                h3 { "What is Debt2Capital™?" }
                                p { "Debt2Capital™ is an innovative approach to the debt snowball method using life insurance cash values and paid up additions to eliminate debt and build capital for inevitable future purchases." }
                                button."d2c-gold".lg id="slides-open" { "Show Presentation" }

                                dialog #slides {
                                    dialog-inner {
                                        header {
                                            h5 { "What is Debt2Capital™?" }
                                            button.sm.secondary id="slides-close" { 
                                                i.fas.fa-times {}
                                                " Close" 
                                            }
                                        }
                                        content {
                                            iframe src="https://docs.google.com/presentation/d/e/2PACX-1vSRChtNgKrA6NUpFvmdWm0dKu6xw52MHtQXUGf_Qkh58wAcNoZA1WBCYYC4_ZuUhnCE2JsefpHIPPa4/pubembed?start=false&loop=false&delayms=10000" frameborder="0" width="960" height="569" allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true" {}
                                        }
                                    }
                                }
                            }

                            div #process-overview {
                                h3 { "Process Steps" }
                                ol #wizard-steps {
                                    li { strong { "Client Info" } " - Contact details" }
                                    li { strong { "Credit Report" } " - Pull credit profile" }
                                    li { strong { "Review Debts" } " - Analyze obligations" }
                                    li { strong { "Budget" } " - Set available funds" }
                                    li { strong { "Report" } " - Generate analysis" }
                                    li { strong { "Policy Design & App" } " - Connect to initiate E-App" }
                                }
                            }
                        }
                    }
                ));
            }
            df4l_zero::types::ClientType::PolicyOnly => {
                wizard.set_id("wizard-policyonly");
                wizard.add_description("Insurance Policy Only wording here.");

                wizard.add_body(html!(
                    @if is_demo {
                        (super::super::demo_notice_html(false))
                    }
                ));
            }
        }

        doc.hide_page_nav();
        doc.add_body(html!((wizard)));

        Ok(Response::HTML(doc.into()))
    }
}

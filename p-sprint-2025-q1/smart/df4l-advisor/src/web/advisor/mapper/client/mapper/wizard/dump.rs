#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/dump?download; AUTH None; return HTML|Bytes;)]
pub mod page {
    use granite::return_authorization_error;

    pub async fn request(
        app: App,
        identity: Identity,
        doc: DocumentPlain,
        path: Path,
        qs: QueryString,
    ) -> Result<Response> {
        use approck::html;

        let dbcx = app.postgres_dbcx().await?;
        if !identity.client_read(&dbcx, path.client_uuid).await {
            return_authorization_error!("identity.client_read({})", path.client_uuid);
        }

        let wizard = df4l_zero::client::wizard::Wizard::load(app, &dbcx, path.client_uuid).await?;

        let plan_output = wizard.to_plan_output().map_err(|e| {
            granite::Error::new(granite::ErrorType::Unexpected)
                .set_external_message(format!("Plan Error: {e}"))
        })?;

        if qs.download {
            let excel_data = plan_output.export_excel()?;
            return Ok(Response::Bytes(Bytes::download_file(
                excel_data,
                "plan.xlsx",
            )?));
        }

        // Add CSS file
        doc.add_css("./dump.mcss");

        // Generate both grids if plan result is successful
        let d2c_grid = plan_output.generate_d2c_grid();
        let min_grid = plan_output.generate_min_grid();

        doc.add_body(html!(
            h1 id="plan-header" { "D2C Plan Analysis" }

            div id="download-section" {
                p { "Download your complete plan analysis as an Excel spreadsheet:" }
                a id="download-link" href="?download" {
                    "📊 Download Excel Report"
                }
            }

            h2 class="section-header section-d2c" { "Debt-to-Cash (D2C) Plan" }
            (render_grid(&d2c_grid))

            h2 class="section-header section-min" { "Minimum Payment Plan" }
            (render_grid(&min_grid))
        ));
        Ok(Response::HTML(doc.into()))
    }

    fn render_grid(grid: &df4l_zero::plan::generate_grid::Grid) -> maud::Markup {
        use approck::html;
        use bux::{format_currency_us_0, format_date_us};
        use df4l_zero::plan::generate_grid::CellValue;

        html! {
            table class="data-table" {
                thead {
                    @for header_row in &grid.header_rows {
                        tr {
                            @for header_cell in &header_row.cells {
                                th colspan=(header_cell.colspan) style=(format!("background-color: {};", header_cell.background_color)) {
                                    (header_cell.label)
                                }
                            }
                        }
                    }
                }
                tbody {
                    @for row in &grid.data_rows {
                        tr {
                            @for cell in &row.cells {
                                @if let Some(value) = &cell.value {
                                    @match value {
                                        CellValue::Number(num) => {
                                            td class="cell-number" style=(format!("background-color: {};", cell.background_color)) { (num) }
                                        }
                                        CellValue::Date(date) => {
                                            td class="cell-date" style=(format!("background-color: {};", cell.background_color)) { (format_date_us(*date)) }
                                        }
                                        CellValue::Currency(amount) => {
                                            @if amount.is_zero() {
                                                td class="cell-zero" style=(format!("background-color: {};", cell.background_color)) { "-" }
                                            } @else {
                                                td class="cell-currency" style=(format!("background-color: {};", cell.background_color)) { (format_currency_us_0(*amount)) }
                                            }
                                        }
                                    }
                                } @else {
                                    td class="cell-na" style=(format!("background-color: {};", cell.background_color)) { "N/A" }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

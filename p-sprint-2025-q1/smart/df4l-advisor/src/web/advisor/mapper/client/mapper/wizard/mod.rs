pub mod budget;
pub mod contact;
pub mod crs;
pub mod debt;
pub mod dump;
pub mod icover;
pub mod icover_demo;
pub mod index;
pub mod policy;
pub mod report;
#[approck::prefix(/advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/)]
pub mod prefix {
    pub fn auth() {}
}

use bux::component::form_wizard::FormWizardImpl;

pub type WizardData = client_wizard_context::Output;

/// Generate demo notice HTML with prefill functionality
fn demo_notice_html(show_prefill_link: bool) -> maud::Markup {
    use maud::html;

    html! {
        section id="demo-notice" {
            "This is a demo client. It will be deleted after 72 hours."
            @if show_prefill_link {
                " You may "
                a href="#prefill" { "click here to prefill the data below." }
            }
        }
    }
}

pub type ClientType = ::df4l_zero::types::ClientType;

#[derive(PartialEq)]
pub enum WizardStep {
    Index,
    Contact,
    Crs,
    Debt,
    Budget,
    Policy,
    Report,
    ICover,
}

#[allow(refining_impl_trait)]
impl FormWizardImpl for WizardStep {
    type Context = WizardData;
    fn all_variants() -> Vec<Self> {
        vec![
            WizardStep::Index,
            WizardStep::Contact,
            WizardStep::Crs,
            WizardStep::Debt,
            WizardStep::Budget,
            WizardStep::Policy,
            WizardStep::Report,
            WizardStep::ICover,
        ]
    }

    fn step(&self, ctx: &WizardData) -> bux::component::form_wizard::FormWizardStep {
        use bux::component::form_wizard::FormWizardStep::{Disabled, Enabled, Hidden};

        match self {
            // Index is always enabled
            WizardStep::Index => Enabled {
                label: "Intro".to_string(),
                complete: true,
            },

            // Contact is always enabled, complete depends on context
            WizardStep::Contact => Enabled {
                label: "Client Info".to_string(),
                complete: ctx.contact_complete,
            },

            // Crs is only enabled for DebtManagement clients
            WizardStep::Crs => match ctx.client_type {
                ClientType::DebtManagement => {
                    if ctx.contact_complete {
                        Enabled {
                            label: "Credit Reports".to_string(),
                            complete: ctx.crs_complete,
                        }
                    } else {
                        Disabled {
                            label: "Credit Reports".to_string(),
                            complete: false,
                        }
                    }
                }
                ClientType::PolicyOnly => Hidden,
            },

            // Debt is only enabled for DebtManagement clients
            WizardStep::Debt => match ctx.client_type {
                ClientType::DebtManagement => {
                    if ctx.contact_complete && ctx.crs_complete {
                        Enabled {
                            label: "Review Debts".to_string(),
                            complete: ctx.debt_complete,
                        }
                    } else {
                        Disabled {
                            label: "Review Debts".to_string(),
                            complete: false,
                        }
                    }
                }
                ClientType::PolicyOnly => Hidden,
            },

            // Budget is only enabled for DebtManagement clients
            WizardStep::Budget => match ctx.client_type {
                ClientType::DebtManagement => {
                    if ctx.contact_complete && ctx.crs_complete && ctx.debt_complete {
                        Enabled {
                            label: "Make Budget".to_string(),
                            complete: ctx.budget_complete,
                        }
                    } else {
                        Disabled {
                            label: "Make Budget".to_string(),
                            complete: false,
                        }
                    }
                }
                ClientType::PolicyOnly => Hidden,
            },

            // Policy is only enabled for PolicyOnly clients
            WizardStep::Policy => match ctx.client_type {
                ClientType::DebtManagement => Hidden,
                ClientType::PolicyOnly => {
                    if ctx.contact_complete {
                        Enabled {
                            label: "Policy Details".to_string(),
                            complete: ctx.policy_complete,
                        }
                    } else {
                        Disabled {
                            label: "Policy Details".to_string(),
                            complete: false,
                        }
                    }
                }
            },

            // Report is only enabled for DebtManagement clients
            WizardStep::Report => match ctx.client_type {
                ClientType::DebtManagement => {
                    if ctx.contact_complete
                        && ctx.crs_complete
                        && ctx.debt_complete
                        && ctx.budget_complete
                    {
                        Enabled {
                            label: "View Report".to_string(),
                            complete: true,
                        }
                    } else {
                        Disabled {
                            label: "View Report".to_string(),
                            complete: false,
                        }
                    }
                }
                ClientType::PolicyOnly => Hidden,
            },

            // ICover is enabled for both client types
            WizardStep::ICover => match ctx.client_type {
                ClientType::DebtManagement => {
                    if ctx.contact_complete
                        && ctx.crs_complete
                        && ctx.debt_complete
                        && ctx.budget_complete
                    {
                        Enabled {
                            label: "Setup Policy".to_string(),
                            complete: ctx.icover_complete,
                        }
                    } else {
                        Disabled {
                            label: "Setup Policy".to_string(),
                            complete: false,
                        }
                    }
                }
                ClientType::PolicyOnly => {
                    if ctx.contact_complete && ctx.policy_complete {
                        Enabled {
                            label: "Setup Policy".to_string(),
                            complete: ctx.icover_complete,
                        }
                    } else {
                        Disabled {
                            label: "Setup Policy".to_string(),
                            complete: false,
                        }
                    }
                }
            },
        }
    }

    fn href(&self, ctx: &WizardData) -> String {
        match self {
            WizardStep::Index => {
                df4l_zero::ml_advisor_client_wizard(ctx.advisor_uuid, ctx.client_uuid)
            }
            WizardStep::Contact => {
                df4l_zero::ml_advisor_client_wizard_contact(ctx.advisor_uuid, ctx.client_uuid)
            }
            WizardStep::Crs => {
                df4l_zero::ml_advisor_client_wizard_crs(ctx.advisor_uuid, ctx.client_uuid)
            }
            WizardStep::Debt => {
                df4l_zero::ml_advisor_client_wizard_debt(ctx.advisor_uuid, ctx.client_uuid)
            }
            WizardStep::Budget => {
                df4l_zero::ml_advisor_client_wizard_budget(ctx.advisor_uuid, ctx.client_uuid)
            }
            WizardStep::Policy => {
                df4l_zero::ml_advisor_client_wizard_policy(ctx.advisor_uuid, ctx.client_uuid)
            }
            WizardStep::Report => {
                df4l_zero::ml_advisor_client_wizard_report(ctx.advisor_uuid, ctx.client_uuid)
            }
            WizardStep::ICover => {
                df4l_zero::ml_advisor_client_wizard_icover(ctx.advisor_uuid, ctx.client_uuid)
            }
        }
    }
}

#[approck::function]
pub mod client_wizard_context {
    use granite::return_authorization_error;

    pub type ClientType = ::df4l_zero::types::ClientType;

    #[granite::gtype]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype]
    pub struct Output {
        pub client_uuid: Uuid,
        pub advisor_uuid: Uuid,
        pub client_type: ClientType,
        pub contact_complete: bool,
        pub crs_complete: bool,
        pub debt_complete: bool,
        pub budget_complete: bool,
        pub policy_complete: bool,
        pub icover_complete: bool,
        pub demo: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let wizard = df4l_zero::client::wizard::Wizard::load(app, &dbcx, input.client_uuid).await?;

        Ok(Output {
            client_uuid: wizard.client_uuid,
            advisor_uuid: wizard.advisor_uuid,
            client_type: wizard.client_type,
            contact_complete: wizard.contact_result.is_ok(),
            crs_complete: wizard.crs_result.is_ok(),
            debt_complete: wizard.debts_result.is_ok(),
            budget_complete: wizard.budget_result.is_ok(),
            policy_complete: wizard.pua_result.is_ok(),
            icover_complete: wizard.icover_result.is_ok(),
            demo: wizard.is_demo,
        })
    }
}

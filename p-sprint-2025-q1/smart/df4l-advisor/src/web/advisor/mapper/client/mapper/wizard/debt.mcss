@import "./mod.mcss";

#debt-editor {
    table {
        /* overrides when active is not checked */
        tr:has(input[type="checkbox"][name="active"]:not(:checked)) {
            td.x-name, td.x-balance, td.x-balance-date, td.x-annual-interest-percentage, td.x-monthly-payment-amount {
                opacity: 0.3;
                text-decoration: line-through;
            }

            td.x-active {
                button.x-delete {
                    display: inline-block;
                    background: none;
                    border: none;
                    margin: 0;
                    padding: 0;
                    font-size: 1.5em;
                }
            }
        }

        error {
            color: red;
            display: block;
        }
        error:has(:empty) {
            display: none;
        }
        
        td.x-active, th.x-active {
            width: 1px;
            white-space: nowrap;
            text-align: center;
        }

        td.x-active {
            bux-input-checkbox {
                display: inline-block;
            }
            button.x-delete {
                display: none;
            }
        }

        th.x-name {
            text-align: left;
        }
        td.x-name {
            font-weight: bold;
            color: rgb(65, 0, 0);

            input {
                font-weight: bold;
                width: 100%;
                color: rgb(65, 0, 0);
            }
        }

        th.x-balance {
            text-align: right;
        }
        td.x-balance {
            width: 10em;
            text-align: right;
            > span {
                margin-right: .75rem;
            }
            input {
                text-align: right;
            }
        }

        th.x-balance-date {
            text-align: right;
        }
        td.x-balance-date {
            width: 7em;
            text-align: right;
            > span {
                margin-right: .75rem;
            }
            input {
                text-align: right;
            }
        }

        th.x-annual-interest-percentage {
            text-align: right;
        }
        td.x-annual-interest-percentage {
            width: 7em;
            text-align: right;
            > span {
                margin-right: .75rem;
            }
            input {
                text-align: right;
            }
        }

        th.x-monthly-payment-amount {
            text-align: right;
        }
        td.x-monthly-payment-amount {
            width: 9em;
            text-align: right;
            > span {
                margin-right: .75rem;
            }
            input {
                text-align: right;
            }
        }

        bux-input-text-currency, bux-input-checkbox, bux-input-text-percentage, bux-input-text-string, bux-input-date {
            margin: 0; 
            
        }
    }
}
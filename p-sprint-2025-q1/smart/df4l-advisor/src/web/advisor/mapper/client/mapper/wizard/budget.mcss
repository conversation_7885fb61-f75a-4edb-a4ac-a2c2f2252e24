@import "./mod.mcss";

/* Minimal styling - let bux framework handle component styling */
/* Use semantic nested selectors instead of utility classes */

#budget-entry {
    #splitter {
        background-color: #f3f4f6;
        padding: 1.5rem;
        gap: 1.5rem;
        border-radius: 1rem;

        bux-input-text-currency {
            margin-bottom: 0;
        }

        panel {
            border-radius: 1rem;
            box-shadow: 2px 4px 12px #00000014;
            border: none;
            margin-bottom: 0;

            content {
                padding: 1.5rem;
            }
        }

        #question-list {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;

            panel {
                border-left: 3px solid #3498db;
            }
        }

        #total-container {
            background-color: #fff;
            border-radius: 1rem;
            box-shadow: 2px 4px 12px #00000014;
            border: none;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;

            p {
                text-align: center;
                margin-bottom: 0;
            }
            
            > div:first-child {
                background-image: linear-gradient(0deg, #084298 0%, #2155a2 50%, #3968ad 100%);
                border-radius: 1rem;
                color: #fff;
                padding: 1.5rem;
                text-align: center;

                h3 {
                    font-size: 24pt;
                    margin: 0;
                }

                hr {
                    border-color: #fff;
                }
            }

            div.gray-border {
                border: 5px solid #eee;
                padding: 1.5rem;
                border-radius: 1rem;

                bux-input-select-nilla {
                    margin-bottom: 0.5rem;
                }
            }
        }
    }
}
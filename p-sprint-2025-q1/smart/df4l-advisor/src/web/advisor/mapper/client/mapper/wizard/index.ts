// -------------------------------------------------------------------------------------------------
// 1. Import Components

import "./index.mcss";
import "@bux/component/form_wizard.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code

import { SEC } from "@granite/lib.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements

if (document.getElementById("wizard-debtmanagement")) {
    const $slides_dialog = SEC(HTMLDialogElement, document, "#slides");
    const $open_button = SEC(HTMLButtonElement, document, "#slides-open");
    const $close_button = SEC(HTMLButtonElement, document, "#slides-close");
    const $google_slides_iframe = SEC(HTMLIFrameElement, document, "iframe");

    const original_iframe_src = $google_slides_iframe.src;

    // -------------------------------------------------------------------------------------------------
    // 4. Bind Event Handlers

    $open_button.addEventListener("click", (event) => {
        event.preventDefault();
        event.stopPropagation();
        $google_slides_iframe.src = original_iframe_src;
        $slides_dialog.showModal();

        // Wait for iframe to actually load before focusing
        $google_slides_iframe.addEventListener("load", () => {
            $google_slides_iframe.focus();
        }, { once: true });
    });

    $close_button.addEventListener("click", (event) => {
        event.preventDefault();
        event.stopPropagation();
        $slides_dialog.close();
        $google_slides_iframe.src = "";
    });

    $slides_dialog.addEventListener("cancel", () => {
        $google_slides_iframe.src = "";
    });
}

#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/icover; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        doc.set_title("ICover");

        use super::super::client_wizard_context;
        use super::client_icover_get;

        let wizard_data = client_wizard_context::call(
            app,
            identity,
            client_wizard_context::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        // Read demo status from the database via wizard_data
        let is_demo = wizard_data.demo;

        let icover_output = client_icover_get::call(
            app,
            identity,
            client_icover_get::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::ICover, wizard_data)?;

            wizard.set_id("icover-wizard");
            wizard.set_hidden("client_uuid", path.client_uuid);
            wizard.add_heading("Start The Journey");
            wizard.add_body(html!(
                @if is_demo {
                    (super::super::demo_notice_html(false))
                }

                @match icover_output {
                    client_icover_get::Output::Ready { icover_url } => {
                        @if is_demo {
                            a #icover-link href=(format!("/advisor/{}/client/{}/wizard/icover_demo", path.advisor_uuid, path.client_uuid)) target="_blank" rel="noopener noreferrer" {
                                i."fas fa-external-link-alt" {}
                                " Design Your Debt2Capital™ Policy"
                            }
                        } @else {
                            a #icover-link href=(icover_url) target="_blank" rel="noopener noreferrer" {
                                i."fas fa-external-link-alt" {}
                                " Design Your Debt2Capital™ Policy"
                            }
                        }
                        p {
                            "Staging Site: "
                            a target="api" href=(format!("/api/icover/client/{}/", path.client_uuid)) { "View Data" }
                        }
                    }
                    client_icover_get::Output::NotReady { messages } => {
                        #errors {
                            p {
                                "You cannot proceed to iCover until the following errors are resolved:"
                            }
                            ul {
                                @for message in messages {
                                    li {
                                        @match &message.url {
                                            Some(url) => {
                                                (message.message) " " a href=(url) { "Complete setup" }
                                            }
                                            None => {
                                                (message.message)
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            ));
            wizard
        };

        doc.add_body(html!(
            div.constrain-width-md {
                (wizard)
            }
        ));
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::function]
pub mod client_icover_get {
    use granite::return_authorization_error;

    #[granite::gtype]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype]
    pub type ICoverErrorMessage = ::df4l_zero::client::wizard::ICoverErrorMessage;

    #[granite::gtype]
    pub enum Output {
        Ready { icover_url: String },
        NotReady { messages: Vec<ICoverErrorMessage> },
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let wizard = df4l_zero::client::wizard::Wizard::load(app, &dbcx, input.client_uuid).await?;

        match wizard.icover_result {
            Ok(icover_output) => Ok(Output::Ready {
                icover_url: icover_output.icover_url,
            }),
            Err(icover_error) => Ok(Output::NotReady {
                messages: icover_error,
            }),
        }
    }
}

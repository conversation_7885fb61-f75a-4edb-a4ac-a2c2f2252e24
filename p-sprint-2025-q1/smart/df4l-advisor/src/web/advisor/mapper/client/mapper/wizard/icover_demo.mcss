body {
    overflow-x: hidden;
}

/* Flash animation keyframes */
@keyframes flash-button {
    0% { background: rgba(39, 174, 96, 0.9); }
    50% { background: rgba(255, 255, 255, 0.9); color: #333; }
    100% { background: rgba(39, 174, 96, 0.9); }
}

/* Main slideshow container - full screen with scrollable content */
#icover-demo-slideshow {
    width: 100vw;
    /* height: 100vh; */
    background: #ffffff;
    overflow: auto;
    position: relative;

    /* Container for individual slide content */
    .slide-content {
        width: 100%;
        min-height: 100vh;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        padding: 0;

        /* Individual slide image - sized to avoid overlapping navigation buttons */
        .slide-image {
            width: calc(100vw - 12rem);
            height: auto;
            display: block;
            object-fit: cover;
            object-position: top center;
            margin: 2rem auto;
        }
    }

    /* Navigation buttons (previous/next) - fixed position, always visible */
    .x-nav-btn {
        position: fixed;
        top: 50vh;
        transform: translateY(-50%);
        background: rgba(39, 174, 96, 0.9);
        color: white;
        border: none;
        width: 120px;
        height: 50px;
        border-radius: 25px;
        cursor: pointer;
        font-size: 1rem;
        font-weight: bold;
        z-index: 1000;
        backdrop-filter: blur(4px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;

        /* Hover effect for navigation buttons */
        &:hover:not(:disabled) {
            background: rgba(34, 153, 84, 0.95);
            transform: translateY(-50%) scale(1.05);
            transition: all 0.2s ease;
        }

        /* Disabled state for navigation buttons */
        &:disabled {
            background: rgba(204, 204, 204, 0.7);
            cursor: not-allowed;
        }

        /* Flash animation for button feedback */
        &.flash {
            animation: flash-button 0.3s ease-in-out;
        }

        /* Previous button positioning */
        &.prev-btn {
            left: 1rem;
        }

        /* Next button positioning */
        &.next-btn {
            right: 1rem;
        }
    }



/* Step counter display (e.g., "Step 1 of 45") - fixed to viewport bottom */
.slide-counter {
    position: fixed !important;
    bottom: 2rem !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: rgba(0, 0, 0, 0.85);
    color: white;
    padding: 0.8rem .5rem;
    border-radius: 30px;
    font-weight: 500;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    letter-spacing: 0.5px;
    z-index: 9999 !important;
    backdrop-filter: blur(6px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    font-size: 1rem;
    line-height: 1.2;
    text-align: center;
    min-width: 140px;
    margin: 0 !important;
    top: auto !important;
    right: auto !important;
}

    /* Tablet responsive design - smaller buttons and reduced margins */
    @media (max-width: 768px) {
        /* Adjust image width for smaller navigation buttons */
        .slide-content .slide-image {
            width: calc(100vw - 16rem);
            margin: 1rem auto;
        }

        /* Smaller navigation buttons for tablet screens */
        .x-nav-btn {
            width: 100px;
            height: 45px;
            font-size: 0.9rem;

            /* Closer to screen edges on tablets */
            &.prev-btn {
                left: 0.5rem;
            }

            &.next-btn {
                right: 0.5rem;
            }
        }

        /* Compact step counter for tablet screens */
        .slide-counter {
            bottom: 1rem !important;
            padding: 0.6rem 1.4rem;
            font-size: 0.9rem;
            min-width: 120px;
        }
    }

    /* Mobile responsive design - smallest buttons and minimal margins */
    @media (max-width: 480px) {
        /* Maximum image width for mobile screens */
        .slide-content .slide-image {
            width: calc(100vw - 12rem);
            margin: 0.5rem auto;
        }

        /* Smallest navigation buttons for mobile screens */
        .x-nav-btn {
            width: 80px;
            height: 40px;
            font-size: 0.8rem;
        }

        /* Most compact step counter for mobile screens */
        .slide-counter {
            bottom: 0.5rem !important;
            padding: 0.5rem 1rem;
            font-size: 0.8rem;
            min-width: 100px;
        }
    }
}

#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/icover_demo; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        _app: App,
        _identity: Identity,
        doc: DocumentPlain,
        _path: Path,
    ) -> Result<Response> {
        use approck::html;

        doc.set_title("iCover Demo");

        doc.add_body(html!(
            div #icover-demo-slideshow {
                button.x-nav-btn.prev-btn type="button" {
                    i."fas fa-chevron-left" {}
                    "Previous"
                }

                div.slide-content {
                    img.slide-image src="" alt="" {}
                }

                button.x-nav-btn.next-btn type="button" {
                    i."fas fa-chevron-right" {}
                    "Next"
                }

                div.slide-counter {}
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

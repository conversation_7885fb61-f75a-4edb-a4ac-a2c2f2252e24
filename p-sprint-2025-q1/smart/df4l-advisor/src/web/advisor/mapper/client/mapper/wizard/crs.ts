// -------------------------------------------------------------------------------------------------
// 1. Import Components
import "./index.mcss";
import "./crs.mcss";

// -------------------------------------------------------------------------------------------------
// 2. Import Code

import "@bux/component/form_wizard.mts";

import { SEC } from "@granite/lib.mts";
//import { CrsDialogType, CrsDialogType_map } from "@api-crs/core/typesλ.mts";
import { client_toggle, get_api_crs_user_uuid, refresh_panel, validate } from "./crsλ.mts";
import { CreditReportSubject } from "@df4l-zero/typesλ.mts";
// new stuff
import { Dialog } from "@api-crs/web/dialog2.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements

const $form = SEC(HTMLFormElement, document, "form.bux-form-wizard");
const $client_uuid = SEC(HTMLInputElement, $form, "[name=client_uuid]");
const $error = SEC(HTMLElement, $form, "error");

const $Applicant = SEC(HTMLDivElement, $form, "#Applicant");
const $Spouse = SEC(HTMLDivElement, $form, "#Spouse");

const $next_button = SEC(HTMLAnchorElement, $form, "a.x-next-button");

// -------------------------------------------------------------------------------------------------
// 4. Bind Event Hnadlers

// This is bound to the container so the innerHTML can be replaced
$Applicant.addEventListener("click", (event) => {
    on_click(event, $Applicant, { "Applicant": true });
});

$Spouse.addEventListener("click", (event) => {
    on_click(event, $Spouse, { "Spouse": true });
});

$next_button.addEventListener("click", (event) => {
    event.preventDefault();

    $error.textContent = "";

    // hit validation service
    validate
        .call({
            client_uuid: $client_uuid.value,
        })
        .then((response) => {
            if ("Output" in response) {
                const output = response.Output[0];
                if ("Ok" in output) {
                    window.location.href = $next_button.href;
                } else {
                    const errors = output.Error;
                    const $ul = document.createElement("ul");

                    if ("Some" in errors.applicant_error) {
                        const $li = document.createElement("li");
                        $li.textContent = errors.applicant_error.Some;
                        $ul.appendChild($li);
                    }
                    if ("Some" in errors.spouse_error) {
                        const $li = document.createElement("li");
                        $li.textContent = errors.spouse_error.Some;
                        $ul.appendChild($li);
                    }

                    $error.innerHTML = "<b>Please correct these issues before continuing:</b>";
                    $error.appendChild($ul);
                }
            }
        });
});

function on_click(
    event: Event,
    $element: HTMLDivElement,
    credit_report_subject: CreditReportSubject,
) {
    if (event.target instanceof HTMLButtonElement) {
        event.preventDefault();
        $error.textContent = "";
        get_api_crs_user_uuid
            .call({
                client_uuid: $client_uuid.value,
                credit_report_subject,
            })
            .then((response) => {
                if ("Output" in response) {
                    const output = response.Output[0];
                    $element.innerHTML = output.inner_html;
                    const dlg = new Dialog(output.api_crs_user_uuid);
                    dlg.on_state_change = () => {
                        refresh_panel
                            .call({
                                client_uuid: $client_uuid.value,
                                credit_report_subject,
                            })
                            .then((response) => {
                                if ("Output" in response) {
                                    const output = response.Output[0];
                                    $element.innerHTML = output.inner_html;
                                }
                            });
                    };
                }
            });
    }

    if (event.target instanceof HTMLAnchorElement && event.target.href.includes("#activate")) {
        event.preventDefault();
        $error.textContent = "";
        client_toggle
            .call({
                client_uuid: $client_uuid.value,
                credit_report_subject,
                active: true,
            })
            .then((response) => {
                if ("Output" in response) {
                    const output = response.Output[0];
                    $element.innerHTML = output.inner_html;
                }
            });
    }

    if (event.target instanceof HTMLAnchorElement && event.target.href.includes("#deactivate")) {
        event.preventDefault();
        $error.textContent = "";
        client_toggle
            .call({
                client_uuid: $client_uuid.value,
                credit_report_subject,
                active: false,
            })
            .then((response) => {
                if ("Output" in response) {
                    const output = response.Output[0];
                    $element.innerHTML = output.inner_html;
                }
            });
    }
}

// -------------------------------------------------------------------------------------------------
// 5. Write Code

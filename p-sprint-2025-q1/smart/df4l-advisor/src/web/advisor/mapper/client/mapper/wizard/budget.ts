//-------------------------------------------------------------------------------------------------
// 1. Import Components
import "./budget.mcss";
import "@bux/component/form_wizard.mts";
import "@bux/input/text/currency.mts";
import "@bux/input/radio/boolean.mts";
import "@bux/input/select/nilla.mts";

//-------------------------------------------------------------------------------------------------
// 2. Import Code
import { CUR0, Decimal, SEC, SEC_nullable, unwrap_or_undefined } from "@granite/lib.mts";
import { client_budget_set, client_budget_validate } from "./budgetλ.mts";
import BuxInputTextCurrency from "@bux/input/text/currency.mts";
import FormWizard from "@bux/component/form_wizard.mts";

//-------------------------------------------------------------------------------------------------
// 3. Select Elements

const $form = SEC(HTMLFormElement, document, "#budget-entry");

// Entire functionality of page conditional on this element existing.
if ($form.querySelector("content")) {
    const $client_uuid = SEC(HTMLInputElement, $form, "[name=client_uuid]").value;
    const $budget_extra_debts = SEC(BuxInputTextCurrency, $form, "[name=budget_extra_debts]");
    const $budget_extra_savings = SEC(BuxInputTextCurrency, $form, "[name=budget_extra_savings]");
    const $budget_extra_retirement = SEC(
        BuxInputTextCurrency,
        $form,
        "[name=budget_extra_retirement]",
    );
    const $budget_extra_surplus = SEC(BuxInputTextCurrency, $form, "[name=budget_extra_surplus]");
    const $pua_contribution = SEC(BuxInputTextCurrency, $form, "[name=pua_contribution]");
    const $next_button = SEC(HTMLAnchorElement, $form, "a.x-next-button");
    const $error = SEC(HTMLElement, $form, "error");

    const $total = SEC(HTMLSpanElement, $form, "#total");

    // Helper function for trusted form submission
    const submitForm = () => {
        $form.requestSubmit();
    };

    // Demo data button handler
    const $demo_link = SEC_nullable(HTMLAnchorElement, $form, "#demo-notice a");
    if ($demo_link) {
        $demo_link.addEventListener("click", (event: Event) => {
            event.preventDefault();

            // Prefill demo budget values
            $budget_extra_debts.set_text("400");
            $budget_extra_savings.set_text("125");
            $budget_extra_retirement.set_text("125");
            $budget_extra_surplus.set_text("100");
            submitForm();
        });
    }

    // cause input events to save
    $budget_extra_debts.addEventListener("change", (_event) => {
        submitForm();
    });
    $budget_extra_savings.addEventListener("change", (_event) => {
        submitForm();
    });
    $budget_extra_retirement.addEventListener("change", (_event) => {
        submitForm();
    });
    $budget_extra_surplus.addEventListener("change", (_event) => {
        submitForm();
    });
    $pua_contribution.addEventListener("change", (_event) => {
        submitForm();
    });

    const refresh = function () {
        const budget_extra_debts = $budget_extra_debts.value ?? Decimal.zero();
        const budget_extra_savings = $budget_extra_savings.value ?? Decimal.zero();
        const budget_extra_retirement = $budget_extra_retirement.value ?? Decimal.zero();
        const budget_extra_surplus = $budget_extra_surplus.value ?? Decimal.zero();

        const total = budget_extra_debts.add(budget_extra_savings).add(budget_extra_retirement).add(
            budget_extra_surplus,
        );
        $total.textContent = CUR0(total);
    };

    new FormWizard({
        $form,
        api: client_budget_set.api,

        err: (errors) => {
            $budget_extra_debts.set_e(errors.budget_extra_debts);
            $budget_extra_savings.set_e(errors.budget_extra_savings);
            $budget_extra_retirement.set_e(errors.budget_extra_retirement);
            $budget_extra_surplus.set_e(errors.budget_extra_surplus);
            $pua_contribution.set_e(errors.budget_pua_contribution);
        },

        get: () => {
            return {
                client_uuid: $client_uuid,
                budget_extra_debts: $budget_extra_debts.value_option,
                budget_extra_savings: $budget_extra_savings.value_option,
                budget_extra_retirement: $budget_extra_retirement.value_option,
                budget_extra_surplus: $budget_extra_surplus.value_option,
                budget_pua_contribution: $pua_contribution.value_option,
            };
        },

        set: (_value) => {
        },

        out: (_output) => {
            refresh();
        },
    });

    $next_button.addEventListener("click", (event) => {
        event.preventDefault();

        $error.textContent = "";
        $budget_extra_debts.set_e(undefined);
        $budget_extra_savings.set_e(undefined);
        $budget_extra_retirement.set_e(undefined);
        $budget_extra_surplus.set_e(undefined);
        $pua_contribution.set_e(undefined);

        client_budget_validate
            .call({
                client_uuid: $client_uuid,
            })
            .then((response) => {
                if ("Output" in response) {
                    const output = response.Output[0];
                    if ("Valid" in output) {
                        window.location.href = $next_button.href;
                    } else {
                        const invalid = output.Invalid;
                        $error.textContent = invalid.message;
                        $budget_extra_debts.set_e(unwrap_or_undefined(invalid.budget_extra_debts));
                        $budget_extra_savings.set_e(
                            unwrap_or_undefined(invalid.budget_extra_savings),
                        );
                        $budget_extra_retirement.set_e(
                            unwrap_or_undefined(invalid.budget_extra_retirement),
                        );
                        $budget_extra_surplus.set_e(
                            unwrap_or_undefined(invalid.budget_extra_surplus),
                        );
                        $pua_contribution.set_e(
                            unwrap_or_undefined(invalid.budget_pua_contribution),
                        );
                        $form.reportValidity();
                    }
                } else {
                    console.error(response);
                }
            })
            .catch((error) => {
                console.error(error);
            });
    });

    refresh();
}

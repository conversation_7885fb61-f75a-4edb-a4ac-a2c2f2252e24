use granite::Decimal;
use rand::Rng;

#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/debt; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        use super::super::client_wizard_context;
        use super::refresh_tbody;

        let wizard_data = client_wizard_context::call(
            app,
            identity,
            client_wizard_context::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        // Read demo status from the database via wizard_data
        let is_demo = wizard_data.demo;

        let tbody_inner_html = refresh_tbody::call(
            app,
            identity,
            refresh_tbody::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?
        .tbody_inner_html;

        let wizard = {
            let mut wizard = bux::component::form_wizard::new(WizardStep::Debt, wizard_data)?;

            wizard.auto_complete = Some(false);
            wizard.set_id("debt-editor");
            wizard.add_heading("Review Your Client's Debts");
            wizard.add_description("This is a combined list of any debts pulled from the credit report and any debts you have manually entered.  Any debt may be marked as inactive by unchecking the checkbox.  Inactive debts that you have entered manually may be deleted.  Debts that have been pulled from the credit report may not be deleted because they are subject to automatic change as credit reports are updated.");
            wizard.set_hidden("client_uuid", path.client_uuid);
            wizard.add_body(html!(
                @if is_demo {
                    (super::super::demo_notice_html(true))
                }

                table {
                    thead {
                        tr {
                            th.x-active { "Included" }
                            th.x-name { "Debt Name" }
                            th.x-balance { "Balance" }
                            th.x-balance-date { "As Of" }
                            th.x-annual-interest-percentage { "Interest Rate" }
                            th.x-monthly-payment-amount { "Monthly Payment" }
                        }
                    }
                    tbody {
                        (maud::PreEscaped(tbody_inner_html))
                    }
                }

                div.text-center {
                    a href="#" class="x-add-debt" { "Add New Debt" }
                }
            ));
            wizard
        };

        doc.hide_page_nav();
        doc.set_title("Client Debts");
        doc.add_body(html!((wizard)));
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod refresh_tbody {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub tbody_inner_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        use approck::html;

        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let wizard = df4l_zero::client::wizard::Wizard::load(app, &dbcx, input.client_uuid).await?;

        let tbody = html! {
            @for debt in &wizard.debts_input.unwrap_or_default() {
                (super::make_row(debt))
            }
        };

        Ok(Output {
            tbody_inner_html: tbody.into_string(),
        })
    }
}

fn calculate_demo_interest_rate(
    balance: Option<Decimal>,
    monthly_payment: Option<Decimal>,
) -> Option<Decimal> {
    if let (Some(balance), Some(payment)) = (balance, monthly_payment)
        && balance > Decimal::ZERO
        && payment > Decimal::ZERO
    {
        // Calculate base annual interest rate: (0.90 * monthly_payment * 12) / balance
        let base_annual_rate = (Decimal::new(90, 2) * payment * Decimal::new(12, 0)) / balance;

        // Cap at 30% (0.30)
        let capped_rate = if base_annual_rate > Decimal::new(30, 2) {
            Decimal::new(30, 2)
        } else {
            base_annual_rate
        };

        // Apply random factor between 0.70 and 1.00
        let mut rng = rand::rng();
        let random_factor: f64 = rng.random_range(0.70..1.00);
        let random_decimal = Decimal::try_from(random_factor).unwrap_or(Decimal::new(85, 2));
        let final_rate = capped_rate * random_decimal;

        // Convert to percentage and round to 2 decimal places
        let rate_percentage = (final_rate * Decimal::new(100, 0)).round_dp(2);

        Some(rate_percentage)
    } else {
        None
    }
}

fn make_row(debt: &::df4l_zero::debt::DebtRow) -> approck::Markup {
    use approck::html;

    // External debts are not editable other than {active, annual_interest_percentage}
    if debt.client_debt_esid.is_some() {
        let demo_interest_rate =
            calculate_demo_interest_rate(debt.balance, debt.monthly_payment_amount);

        html! {
            tr client_debt_uuid=(debt.client_debt_uuid.to_string())
                data-demo-interest-rate=(demo_interest_rate.map(|r| r.to_string()).unwrap_or_default()) {
                td.x-active {
                    (bux::input::checkbox::name_value("active", debt.active))
                }
                td.x-name {
                    span {
                        (debt.name.as_ref().unwrap_or(&"-".to_string()))
                    }
                    error {}
                }
                td.x-balance {
                    span {
                        (bux::format_currency_us_0_option(debt.balance))
                    }
                }
                td.x-balance-date {
                    span {
                        (bux::format_date_us_option(debt.balance_date))
                    }
                }
                td.x-annual-interest-percentage {
                    (bux::input::text::percentage::name_value("annual_interest_percentage", debt.annual_interest_percentage))
                }
                td.x-monthly-payment-amount {
                    span {
                        (bux::format_currency_us_0_option(debt.monthly_payment_amount))
                    }
                }
            }
        }
    }
    // Editable debts get an editor for all fields
    else {
        html!(
            tr client_debt_uuid=(debt.client_debt_uuid.to_string()) editable {
                td.x-active {
                    (bux::input::checkbox::name_value("active", debt.active))
                    button.x-delete type="button" title="Delete this debt" {
                        "🗑"
                    }
                }
                td.x-name {
                    (bux::input::text::string::name_value("name", debt.name.as_deref()))
                }
                td.x-balance {
                    (bux::input::text::currency::name_value_dec0("balance", debt.balance))
                }
                td.x-balance-date {
                    (bux::input::date::name_value("balance_date", debt.balance_date))
                }
                td.x-annual-interest-percentage {
                    (bux::input::text::percentage::name_value("annual_interest_percentage", debt.annual_interest_percentage))
                }
                td.x-monthly-payment-amount {
                    (bux::input::text::currency::name_value_dec0("monthly_payment_amount", debt.monthly_payment_amount))
                }
            }
        )
    }
}

#[approck::api]
pub mod client_debt_add {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub tr_html: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_debt_uuid: Uuid,
                active: bool,
                balance_date: DateUtc,
            };
            INSERT INTO df4l.client_debt (
                client_uuid,
                active,
                balance_date
            )
            VALUES (
                $client_uuid,
                true,
                CURRENT_DATE
            )
            RETURNING
                client_debt_uuid,
                active,
                balance_date
        )
        .await?;

        let debt = ::df4l_zero::debt::DebtRow {
            client_debt_uuid: row.client_debt_uuid,
            client_uuid: input.client_uuid,
            client_debt_esid: None,
            name: None,
            balance: None,
            balance_date: Some(row.balance_date),
            annual_interest_percentage: None,
            monthly_payment_amount: None,
            active: row.active,
        };

        Ok(Output {
            tr_html: super::make_row(&debt).into_string(),
        })
    }
}

#[approck::api]
pub mod client_debt_delete {
    use granite::{return_authorization_error, return_invalid_operation};

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub client_debt_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        // validate that the debt is inactive
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_debt_uuid: &input.client_debt_uuid,
                $client_uuid: &input.client_uuid,
            };
            row = {
                active: bool,
                is_external: bool,
            };
            SELECT
                active,
                client_debt_esid IS NOT NULL AS is_external
            FROM
                df4l.client_debt
            WHERE
                // SECON: restrict to client_uuid
                client_uuid = $client_uuid
                AND client_debt_uuid = $client_debt_uuid

        )
        .await?;

        if row.active {
            return_invalid_operation!("Cannot delete an active debt");
        }

        if row.is_external {
            return_invalid_operation!("Cannot delete an external debt");
        }

        // Delete the debt
        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_debt_uuid: &input.client_debt_uuid,
                $client_uuid: &input.client_uuid,
            };
            DELETE FROM
                df4l.client_debt
            WHERE
                // SECON: restrict to client_uuid
                client_uuid = $client_uuid

                AND client_debt_uuid = $client_debt_uuid
        )
        .await?;

        Ok(Output {})
    }
}

#[approck::api]
pub mod client_debt_save_status {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub client_debt_uuid: Uuid,
        pub active: bool,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        // Update the debt's active status
        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_debt_uuid: &input.client_debt_uuid,
                $client_uuid: &input.client_uuid,
                $active: &input.active,
            };
            UPDATE
                df4l.client_debt
            SET
                active = $active

            WHERE
                // SECON: restrict to client_uuid
                client_uuid = $client_uuid

                AND client_debt_uuid = $client_debt_uuid
        )
        .await?;

        Ok(Output {})
    }
}

/// To be used to update manually entered debts (where client_debt_esid IS NULL)
#[approck::api]
pub mod client_debt_save {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub client_debt_uuid: Uuid,
        pub name: Option<String>,
        pub balance: Option<Decimal>,
        pub balance_date: Option<DateUtc>,
        pub annual_interest_percentage: Option<Decimal>,
        pub monthly_payment_amount: Option<Decimal>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        // Update the debt
        // SECON: restrict to client_uuid so above security check is sufficient
        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_debt_uuid: &input.client_debt_uuid,
                $client_uuid: &input.client_uuid,
                $name: &input.name,
                $balance: &input.balance,
                $balance_date: &input.balance_date,
                $annual_interest_percentage: &input.annual_interest_percentage,
                $monthly_payment_amount: &input.monthly_payment_amount,
            };
            UPDATE
                df4l.client_debt
            SET
                name = $name,
                balance = $balance,
                balance_date = $balance_date,
                annual_interest_percentage = $annual_interest_percentage,
                monthly_payment_amount = $monthly_payment_amount
            WHERE true
                AND client_uuid = $client_uuid // SECON: restrict to client_uuid
                AND client_debt_uuid = $client_debt_uuid
                AND client_debt_esid IS NULL
        )
        .await?;

        Ok(Output {})
    }
}

/// To be used to update external debts (where client_debt_esid IS NOT NULL)
#[approck::api]
pub mod client_debt_save_external {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub client_debt_uuid: Uuid,
        pub annual_interest_percentage: Option<Decimal>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {}

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        // Update the debt
        // SECON: restrict to client_uuid so above security check is sufficient
        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_debt_uuid: &input.client_debt_uuid,
                $client_uuid: &input.client_uuid,
                $annual_interest_percentage: &input.annual_interest_percentage,
            };
            UPDATE
                df4l.client_debt
            SET
                annual_interest_percentage = $annual_interest_percentage
            WHERE true
                AND client_uuid = $client_uuid // SECON: restrict to client_uuid
                AND client_debt_uuid = $client_debt_uuid
                AND client_debt_esid IS NOT NULL
        )
        .await?;

        Ok(Output {})
    }
}

#[approck::api]
pub mod client_debt_validate_all {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub enum Output {
        Valid,
        Invalid(DebtsError),
    }

    #[granite::gtype(ApiOutput, ts_from = "@df4l-zero/debt/modλ.mts")]
    pub type DebtsError = ::df4l_zero::debt::DebtsError;

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let wizard = df4l_zero::client::wizard::Wizard::load(app, &dbcx, input.client_uuid).await?;

        match wizard.debts_result {
            Ok(_) => Ok(Output::Valid),
            Err(debts_error) => Ok(Output::Invalid(debts_error)),
        }
    }
}

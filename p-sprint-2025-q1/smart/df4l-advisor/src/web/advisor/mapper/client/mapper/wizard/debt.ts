//-------------------------------------------------------------------------------------------------
// 1. Import Components
import "./debt.mcss";
import "@bux/component/form_wizard.mts";
import "@bux/input/text/currency.mts";
import "@bux/input/text/percentage.mts";
import "@bux/input/text/string.mts";
import "@bux/input/checkbox.mts";
import "@bux/input/date.mts";

//-------------------------------------------------------------------------------------------------
// 2. Import Code
import { SA, SEC, SEC_nullable, unwrap_or_undefined, Uuid } from "@granite/lib.mts";

import {
    client_debt_add,
    client_debt_delete,
    client_debt_save,
    client_debt_save_external,
    client_debt_save_status,
    client_debt_validate_all,
    refresh_tbody,
} from "./debtλ.mts";

import { DebtError } from "@df4l-zero/debt/modλ.mts";

import BuxInputCheckbox from "@bux/input/checkbox.mts";
import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputCurrency from "@bux/input/text/currency.mts";
import BuxInputPercentage from "@bux/input/text/percentage.mts";
import BuxInputDate from "@bux/input/date.mjs";

//-------------------------------------------------------------------------------------------------
// 3. Find Elements

const $form = SEC(HTMLFormElement, document, "#debt-editor");

//-------------------------------------------------------------------------------------------------
// Entire functionality of page conditional on this element existing.

if ($form.querySelector("content")) {
    const SET_E_MAP = new Map<Uuid, (error: DebtError) => void>();
    const CLR_E_MAP = new Map<Uuid, () => void>();
    const DEMO_LIST: (() => void)[] = [];

    const refresh = function () {
        SET_E_MAP.clear();
        CLR_E_MAP.clear();
        DEMO_LIST.length = 0;

        refresh_tbody.api
            .call({
                client_uuid,
            })
            .then((response) => {
                if ("Output" in response) {
                    $tbody.innerHTML = response.Output[0].tbody_inner_html;

                    for (
                        const tr of Array.from($tbody.querySelectorAll<HTMLTableRowElement>("tr"))
                    ) {
                        wrap_tr(tr);
                    }
                } else {
                    console.error(response);
                }
            })
            .catch((error) => {
                console.error(error);
            });
    };

    const wrap_tr = function (tr: string | HTMLTableRowElement): HTMLTableRowElement {
        let $tr: HTMLTableRowElement;
        if (typeof tr === "string") {
            const $tbody = document.createElement("tbody");
            $tbody.innerHTML = tr;
            $tr = $tbody.querySelector("tr") as HTMLTableRowElement;
        } else {
            $tr = tr;
        }

        const client_debt_uuid = SA(String, $tr, "client_debt_uuid");
        const editable = $tr.hasAttribute("editable");
        const $active = SEC(BuxInputCheckbox, $tr, "bux-input-checkbox[name=active]");

        $active.on_change = save_status;

        function save_status() {
            client_debt_save_status
                .call({
                    client_uuid,
                    client_debt_uuid,
                    active: $active.value ?? false,
                })
                .then((response) => {
                    if ("Output" in response) {
                        // Status saved successfully
                    } else {
                        console.error(response);
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        }

        if (editable) {
            const $name = SEC(BuxInputTextString, $tr, "[name=name]");
            const $balance = SEC(BuxInputCurrency, $tr, "[name=balance]");
            const $balance_date = SEC(BuxInputDate, $tr, "[name=balance_date]");
            const $annual_interest_percentage = SEC(
                BuxInputPercentage,
                $tr,
                "[name=annual_interest_percentage]",
            );
            const $monthly_payment_amount = SEC(
                BuxInputCurrency,
                $tr,
                "[name=monthly_payment_amount]",
            );
            const $delete_button = SEC(HTMLButtonElement, $tr, "button.x-delete");

            const save = () => {
                client_debt_save
                    .call({
                        client_uuid,
                        client_debt_uuid,
                        name: $name.value_option,
                        balance: $balance.value_option,
                        balance_date: $balance_date.value_option,
                        annual_interest_percentage: $annual_interest_percentage.value_option,
                        monthly_payment_amount: $monthly_payment_amount.value_option,
                    })
                    .then((response) => {
                        if ("Output" in response) {
                            // Saved successfully
                        } else {
                            throw `Error saving debt: ${response}`;
                        }
                    });
            };

            const delete_debt = () => {
                client_debt_delete
                    .call({
                        client_uuid,
                        client_debt_uuid,
                    })
                    .then((response) => {
                        if ("Output" in response) {
                            // Successfully deleted, refresh the table
                            refresh();
                        } else {
                            throw `Error deleting debt: ${response}`;
                        }
                    });
            };

            $name.on_change = save;
            $balance.on_change = save;
            $balance_date.on_change = save;
            $annual_interest_percentage.on_change = save;
            $monthly_payment_amount.on_change = save;
            $delete_button.addEventListener("click", (event) => {
                event.preventDefault();
                delete_debt();
            });

            SET_E_MAP.set(client_debt_uuid, (error: DebtError) => {
                $name.set_e(unwrap_or_undefined(error.name));
                $balance.set_e(unwrap_or_undefined(error.balance));
                $balance_date.set_e(unwrap_or_undefined(error.balance_date));
                $annual_interest_percentage.set_e(
                    unwrap_or_undefined(error.annual_interest_percentage),
                );
                $monthly_payment_amount.set_e(unwrap_or_undefined(error.monthly_payment_amount));
            });
            CLR_E_MAP.set(client_debt_uuid, () => {
                $name.set_e(undefined);
                $balance.set_e(undefined);
                $balance_date.set_e(undefined);
                $annual_interest_percentage.set_e(undefined);
                $monthly_payment_amount.set_e(undefined);
            });
        } // Not editable (e.g. external debt from credit report -- only annual_interest_percentage is editable)
        else {
            const $annual_interest_percentage = SEC(
                BuxInputPercentage,
                $tr,
                "[name=annual_interest_percentage]",
            );

            const $error = SEC(HTMLElement, $tr, "error");

            const save = () => {
                client_debt_save_external
                    .call({
                        client_uuid,
                        client_debt_uuid,
                        annual_interest_percentage: $annual_interest_percentage.value_option,
                    })
                    .then((response) => {
                        if ("Output" in response) {
                            // Saved successfully
                        } else {
                            throw `Error saving debt: ${response}`;
                        }
                    });
            };

            $annual_interest_percentage.on_change = save;

            SET_E_MAP.set(client_debt_uuid, (error: DebtError) => {
                $annual_interest_percentage.set_e(
                    unwrap_or_undefined(error.annual_interest_percentage),
                );

                const $ul = document.createElement("ul");
                if ("Some" in error.name) {
                    const $li = document.createElement("li");
                    $li.textContent = error.name.Some;
                    $ul.appendChild($li);
                }
                if ("Some" in error.balance) {
                    const $li = document.createElement("li");
                    $li.textContent = error.balance.Some;
                    $ul.appendChild($li);
                }
                if ("Some" in error.balance_date) {
                    const $li = document.createElement("li");
                    $li.textContent = error.balance_date.Some;
                    $ul.appendChild($li);
                }
                if ("Some" in error.monthly_payment_amount) {
                    const $li = document.createElement("li");
                    $li.textContent = error.monthly_payment_amount.Some;
                    $ul.appendChild($li);
                }

                if ($ul.childElementCount > 0) {
                    $error.replaceChildren($ul);
                } else {
                    $error.innerHTML = "";
                }
            });
            CLR_E_MAP.set(client_debt_uuid, () => {
                $annual_interest_percentage.set_e(undefined);
                $error.innerHTML = "";
            });

            // Demo data closure for external debts (only interest rate is editable)
            DEMO_LIST.push(() => {
                const demo_rate = SA(String, $tr, "data-demo-interest-rate");
                if (demo_rate) { // Only set if not empty
                    $annual_interest_percentage.set_text(demo_rate);
                }
            });
        }

        return $tr;
    };

    /// call the new_row api to get the new row html, then inject it into the tbody
    const add_debt = function () {
        client_debt_add
            .call({
                client_uuid,
            })
            .then((response) => {
                if ("Output" in response) {
                    const $tr = wrap_tr(response.Output[0].tr_html);
                    $tbody.appendChild($tr);
                    $tr.querySelector<HTMLInputElement>("input[name=name]")?.focus();
                } else {
                    console.error(response);
                }
            })
            .catch((error) => {
                console.error(error);
            });
    };

    // -------------------------------------------------------------------------------------------------
    // 3. Find elements
    const $table = SEC(HTMLTableElement, $form, "table");
    const $tbody = SEC(HTMLTableSectionElement, $table, "tbody");
    const client_uuid = (SEC(HTMLInputElement, $form, "[name=client_uuid]")).value;

    const $x_add_link = SEC(HTMLAnchorElement, $form, ".x-add-debt");

    const $next_button = SEC(HTMLAnchorElement, $form, "a.x-next-button");
    const $error = SEC(HTMLElement, $form, "error");

    //-------------------------------------------------------------------------------------------------
    // 4. Bind event handlers

    $x_add_link.addEventListener("click", (event) => {
        event.preventDefault();
        add_debt();
    });

    // Demo data button handler
    const $demo_link = SEC_nullable(HTMLAnchorElement, $form, "#demo-notice a");
    if ($demo_link) {
        $demo_link.addEventListener("click", (event: Event) => {
            event.preventDefault();

            // Iterate over all demo closures and call them
            DEMO_LIST.forEach((demo_fn) => {
                demo_fn();
            });
        });
    }

    // iterate over all the tr tags in tbody and wrap them
    for (const tr of Array.from($tbody.querySelectorAll<HTMLTableRowElement>("tr"))) {
        wrap_tr(tr);
    }

    // validate before continue
    $next_button.addEventListener("click", (event) => {
        event.preventDefault();

        client_debt_validate_all
            .call({
                client_uuid,
            })
            .then((response) => {
                // iterate over clr_e_map and call the function
                CLR_E_MAP.forEach((value) => {
                    value();
                });

                if ("Output" in response) {
                    const output = response.Output[0];
                    if ("Valid" in output) {
                        window.location.href = $next_button.href;
                    } else {
                        const debts_error = output.Invalid[0];
                        $error.textContent = debts_error.outer;
                        for (const error of debts_error.inner) {
                            const set_e = SET_E_MAP.get(error.client_debt_uuid);
                            if (set_e) {
                                set_e(error);
                            }
                        }
                        $form.reportValidity();
                    }
                } else {
                    throw `Error validating debts: ${response}`;
                }
            });
    });

    //-------------------------------------------------------------------------------------------------
    // 5. All the other funcitonality
}

//-------------------------------------------------------------------------------------------------
// 1. Import Components

import "./contact.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/textarea/string.mts";
import "@bux/component/form_wizard.mts";
import "@bux/input/select/nilla.mts";
import "@bux/input/select/gender.mts";
import "@bux/input/date.mts";

//-------------------------------------------------------------------------------------------------
// 2. Import Code

import { SEC, SEC_nullable } from "@granite/lib.mts";
import { client_wizard_contact_set } from "./contactλ.mts";
import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import FormWizard from "@bux/component/form_wizard.mts";
import BuxInputSelectNilla from "@bux/input/select/nilla.mts";
import BuxInputSelectGender from "@bux/input/select/gender.mts";
import BuxInputDate from "@bux/input/date.mts";

const $form = SEC(HTMLFormElement, document, "#contact-editor");

// Entire functionality of page conditional on this element existing.
if (document.getElementById("contact-editor")) {
    //-------------------------------------------------------------------------------------------------
    // 3. Find Elements

    const $client_uuid = SEC(HTMLInputElement, $form, "[name=client_uuid]").value;
    const $first_name = SEC(BuxInputTextString, $form, "bux-input-text-string[name=first_name]");
    const $last_name = SEC(BuxInputTextString, $form, "bux-input-text-string[name=last_name]");
    const $address1 = SEC(BuxInputTextString, $form, "bux-input-text-string[name=address1]");
    const $address2 = SEC(BuxInputTextString, $form, "bux-input-text-string[name=address2]");
    const $city = SEC(BuxInputTextString, $form, "bux-input-text-string[name=city]");
    const $state = SEC(BuxInputSelectNilla, $form, "bux-input-select-nilla[name=state]");
    const $zip = SEC(BuxInputTextString, $form, "bux-input-text-string[name=zip]");
    const $email = SEC(BuxInputTextString, $form, "bux-input-text-string[name=email]");
    const $phone = SEC(BuxInputTextString, $form, "bux-input-text-string[name=phone]");
    const $gender = SEC(BuxInputSelectGender, $form, "bux-input-select-gender[name=gender]");
    const $birth_date = SEC(BuxInputDate, $form, "bux-input-date[name=birth_date]");
    const $note = SEC(BuxInputTextareaString, $form, "bux-input-textarea-string[name=note]");
    const $next_button = SEC(HTMLAnchorElement, $form, "a.x-next-button");

    //-------------------------------------------------------------------------------------------------
    // 4. Bind Event Handlers

    // Load Demo Client button handler
    const $load_demo_link = SEC_nullable(
        HTMLAnchorElement,
        $form,
        "#demo-notice a[href='#prefill']",
    );
    if ($load_demo_link) {
        $load_demo_link.addEventListener("click", (event) => {
            event.preventDefault();

            $first_name.value = "John";
            $last_name.value = "Doe";
            $email.value = "<EMAIL>";
            $phone.value = "(*************";
            $address1.value = "123 Main Street";
            $address2.value = "Apt 4B";
            $city.value = "Anytown";
            $state.value = "PA";
            $zip.value = "12345";
            $gender.value = "Male";
            $birth_date.value = new Date("1985-06-15");
            $note.value = "Demo client for testing purposes.";
        });
    }

    $next_button.addEventListener("click", (event) => {
        event.preventDefault();
        $form.requestSubmit();
    });

    //-------------------------------------------------------------------------------------------------
    // 5. Write Code

    new FormWizard({
        $form,
        api: client_wizard_contact_set.api,

        err: (errors) => {
            $first_name.set_e(errors.first_name);
            $last_name.set_e(errors.last_name);
            $address1.set_e(errors.address1);
            $address2.set_e(errors.address2);
            $city.set_e(errors.city);
            $state.set_e(errors.state);
            $zip.set_e(errors.zip);
            $email.set_e(errors.email);
            $phone.set_e(errors.phone);
            $gender.set_e(errors.gender);
            $birth_date.set_e(errors.birth_date);
            $note.set_e(errors.note);
        },

        get: () => {
            return {
                client_uuid: $client_uuid,
                first_name: $first_name.value,
                last_name: $last_name.value,
                address1: $address1.value,
                address2: $address2.value_option,
                city: $city.value,
                state: $state.value,
                zip: $zip.value,
                email: $email.value,
                phone: $phone.value,
                gender: $gender.value,
                birth_date: $birth_date.value ?? undefined,
                note: $note.value_option,
            };
        },

        set: (_value) => {
        },

        out: (_output) => {
            window.location.href = $next_button.href;
        },
    });
}

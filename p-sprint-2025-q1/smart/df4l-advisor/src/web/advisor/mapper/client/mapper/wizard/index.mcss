@import "./mod.mcss";

#wizard-debtmanagement {
    #wizard-intro-content {
        margin: 0 auto;
        padding: 1.5rem;
        line-height: 1.5;
        color: #333;

        h3 {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.4rem;
        }

        p {
            margin-bottom: 1rem;
            font-size: 0.95rem;
            color: #555;
        }
    }

    #benefits-section {
        margin-bottom: 2rem;

        #benefits-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;

            > div {
                background: #fff;
                border-radius: 8px;
                padding: 1rem;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                text-align: center;
                border-top: 3px solid #3498db;

                h4 {
                    color: #2c3e50;
                    font-size: 0.9rem;
                    font-weight: 600;
                    margin-bottom: 0.5rem;
                }

                p {
                    color: #666;
                    font-size: 0.8rem;
                    margin-bottom: 0;
                    line-height: 1.3;
                }
            }
        }
    }

    #intro-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

        h3 {
            color: #2980b9;
            margin-top: 0;
            border-bottom: none;
        }

        p {
            color: #2c3e50;
            margin-bottom: 1rem;
        }
    }

    #process-overview {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

        h3 {
            margin-top: 0;
            border-bottom: none;
        }

        #wizard-steps {
            background: transparent;
            border-radius: 0;
            padding: 0;
            box-shadow: none;
            counter-reset: step-counter;
            list-style: none;
            margin: 1rem 0 0 0;

            li {
                position: relative;
                padding: 0.6rem 0 0.6rem 2.5rem;
                border-bottom: 1px solid rgba(255, 255, 255, 0.5);
                counter-increment: step-counter;
                font-size: 0.9rem;

                &:last-child {
                    border-bottom: none;
                }

                &::before {
                    content: counter(step-counter);
                    position: absolute;
                    left: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    width: 1.5rem;
                    height: 1.5rem;
                    background: linear-gradient(135deg, #3498db, #2980b9);
                    color: white;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: bold;
                    font-size: 0.75rem;
                }

                strong {
                    color: #2c3e50;
                    font-weight: 600;
                }
            }
        }
    }
}

button.d2c-gold {
    border-color: #ac8950;
    background-color: #ac8950;
    background-image: linear-gradient(0deg, #ac8950 0%, #bda173 50%, #cdb896 100%);
    color: #000;

    &:hover {
        border-color: #ac8950;
        background-color: #ac8950;
        background-image: linear-gradient(0deg, #ac8950 0%, #bda173 50%, #cdb896 100%);
        color: #000;
    }
}

dialog#slides {
    background-color: #fff;
    outline: 0;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: .3rem;
    padding: 0;
    width: 100%;

    @media (min-width: 1200px) {
        height: 100%;
    }

    &::backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }

    dialog-inner {
        display: flex;
        flex-direction: column;

        @media (min-width: 1200px) {
            height: 100%;
        }

        header {
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            border-top-left-radius: calc(.3rem - 1px);
            border-top-right-radius: calc(.3rem - 1px);
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 1rem;

            h5 {
                font-size: 1.25rem;
                margin: 0;
            }
        }

        content {
            padding: 1rem;

            @media (min-width: 1200px) {
                flex-grow: 1;
            }

            iframe {
                width: 100%;
                height: auto;
                aspect-ratio: 16 / 9;

                @media (min-width: 1200px) {
                    height: 100%;
                    object-fit: contain;
                    aspect-ratio: auto;
                }
            }
        }
    }
}
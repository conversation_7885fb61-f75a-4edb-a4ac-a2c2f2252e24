* {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
}

/* Page Structure */
#plan-header {
    margin: 0;
    padding: 1rem;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
    position: sticky;
    top: 0;
    z-index: 100;
    font-weight: 600;
}

#download-section {
    margin: 1rem 0;
    padding: 1rem;
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    text-align: center;
}

#download-link {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 500;
    transition: background-color 0.2s;
}

#download-link:hover {
    background: #0056b3;
}

/* Section Headers */
.section-header {
    margin: 1rem 0 0.5rem 0;
    padding: 0.5rem;
    border-left: 4px solid;
}

.section-d2c {
    background: #e3f2fd;
    border-left-color: #2196f3;
}

.section-min {
    background: #fff9c4;
    border-left-color: #ffc107;
}

/* Tables */
.data-table {
    border-collapse: collapse;
    width: 100%;
    min-width: 1500px;
    font-size: 0.75rem;
    margin-bottom: 2rem;
    font-variant-numeric: tabular-nums;
}

.data-table th {
    border: 1px solid #ddd;
    padding: 6px;
    text-align: center;
    font-weight: 600;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
    font-size: 0.7rem;
}

.data-table td {
    border: 1px solid #ddd;
    padding: 4px;
    font-size: 0.7rem;
}

.data-table tr:nth-child(even) {
    background-color: #fafafa;
}

/* Cell Types */
.cell-number {
    text-align: center;
    font-weight: 600;
    font-variant-numeric: tabular-nums;
}

.cell-date {
    text-align: center;
    font-size: 0.65rem;
}

.cell-currency {
    text-align: right;
    font-variant-numeric: tabular-nums;
}

.cell-zero {
    text-align: right;
    color: #999;
    font-variant-numeric: tabular-nums;
}

.cell-na {
    text-align: right;
    color: #ccc;
}
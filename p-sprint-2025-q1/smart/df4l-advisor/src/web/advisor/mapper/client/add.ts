//-------------------------------------------------------------------------------------------------
// 1. Import Components
import "./add.mcss";
import "@bux/input/text/string.mts";

//-------------------------------------------------------------------------------------------------
// 2. Import Code
import { SEC, SEC_array, SEC_nullable } from "@granite/lib.mts";
import { client_add } from "./addλ.mts";
import { ClientType_Partial_map } from "@df4l-zero/typesλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import FormPanel from "@bux/component/form_panel.mts";

//-------------------------------------------------------------------------------------------------
// 3. Find Elements
const $form = SEC(HTMLFormElement, document, "form.form-panel");
const $advisor_uuid: HTMLInputElement = SEC(HTMLInputElement, $form, "[name=advisor_uuid]");
const $is_demo: HTMLInputElement = SEC(HTMLInputElement, $form, "[name=is_demo]");
const is_demo: boolean = $is_demo.value === "true";
const $first_name: BuxInputTextString = SEC(BuxInputTextString, $form, "[name=first_name]");
const $last_name: BuxInputTextString = SEC(BuxInputTextString, $form, "[name=last_name]");
const $email: BuxInputTextString = SEC(BuxInputTextString, $form, "[name=email]");
const $phone: BuxInputTextString = SEC(BuxInputTextString, $form, "[name=phone]");
const $client_type_radios: HTMLInputElement[] = SEC_array(
    HTMLInputElement,
    $form,
    "[name=client_type]",
);

//-------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

// Load Demo Client link handler
const $load_demo_link = SEC_nullable(
    HTMLAnchorElement,
    $form,
    "#demo-notice a[href='#prefill']",
);
if ($load_demo_link) {
    $load_demo_link.addEventListener("click", (event) => {
        event.preventDefault();

        $first_name.value = "John";
        $last_name.value = "Doe";
        $email.value = "<EMAIL>";
        $phone.value = "(*************";

        const debt_management_radio = $client_type_radios.find((radio) =>
            radio.value === "DebtManagement"
        );
        if (debt_management_radio) {
            debt_management_radio.checked = true;
        }
    });
}

//-------------------------------------------------------------------------------------------------
// 5. Write Code
new FormPanel({
    $form,
    api: client_add.api,
    on_cancel: go_back,

    err: (errors) => {
        $first_name.set_e(errors.first_name);
        $last_name.set_e(errors.last_name);
        $email.set_e(errors.email);
        $phone.set_e(errors.phone);
        $client_type_radios.forEach((radio) => {
            radio.setCustomValidity(errors.client_type?.Outer ?? "");
        });
        $form.reportValidity();
    },

    get: () => {
        const selected_client_type_value = Array.from($client_type_radios).find((radio) =>
            radio.checked
        )?.value ?? "";

        // Convert string to ClientType enum format using the generated map
        const client_type = ClientType_Partial_map[selected_client_type_value];

        return {
            advisor_uuid: $advisor_uuid.value,
            client_type: client_type,
            first_name: $first_name.value,
            last_name: $last_name.value,
            email: $email.value,
            phone: $phone.value,
            is_demo: is_demo,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        go_next(output.onboarding_url);
    },
});

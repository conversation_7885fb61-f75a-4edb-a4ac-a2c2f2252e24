@import "./mod.mcss";

form.bux-form-wizard {
    & > header {
        margin-bottom: 0 !important;

        & h1 {
            color: red;
        }
    }

    /* Main report layout container */
    df4l-advisor-debt-free {

        /* Summary cards styling */
        x-summary-cards {
            display: grid;
            grid-template-columns: repeat(3, minmax(160px, 260px));
            gap: 1.5rem;
            margin-bottom: 2rem;
            margin-top: 2rem;
            justify-content: center;

            x-card {
                padding: .75rem;
                border-radius: 0.5rem;
                text-align: center;
                max-width: 220px;

                x-label {
                    display: block;
                    font-size: 1rem;
                    color: #000;
                    font-weight: normal;
                }

                x-value {
                    display: block;
                    font-size: 1.5rem;
                    font-weight: bold;
                }

                &.payment-mo,
                &.interest-portion,
                &.principal-portion {
                    background-color: #e9ecef;
                }
            }
        }

        /* Table styling with alternating rows */
        table-wrapper.data-list {
            table {
                border: none;

                tbody {
                    tr {
                        border: none;

                        td {
                            padding-top: 1rem;
                            padding-bottom: 1rem;
                            border: none;
                        }
                    }

                    tr:nth-child(odd) {
                        background-color: #f5f5f5;
                    }

                    tr:nth-child(even) {
                        background-color: white;
                    }



                    /* Right align data cells for Balance column and columns 3-4 */
                    td:nth-child(2),
                    td:nth-child(3),
                    td:nth-child(4) {
                        text-align: right !important;
                    }

                    /* Add gap after Payment/Mo. column */
                    td:nth-child(4) {
                        padding-right: 1rem !important;
                    }

                    /* Interest vs. Principal column should be centered and vertically aligned */
                    td:nth-child(5) {
                        text-align: center !important;
                        vertical-align: middle !important;
                        padding-left: 1rem;
                        padding-right: 1rem;
                    }

                    /* Progress bars in Interest vs. Principal column */
                    td.interest-vs-principal {
                        vertical-align: middle;

                        progress-bar {
                            margin: 0;
                            height: 20px;
                            border-radius: 10px;
                        }
                    }
                }

                /* Header styling - no gray background, right align except Debt Name */
                thead {
                    background-color: white !important;

                    tr {
                        background-color: white !important;
                        text-align: right;

                        th {
                            background-color: white !important;
                        }

                        th:nth-child(1) {
                            text-align: left;
                        }

                        th:nth-child(2),
                        th:nth-child(3),
                        th:nth-child(4) {
                            text-align: right;
                        }

                        /* Add gap after Payment/Mo. header */
                        th:nth-child(4) {
                            padding-right: 1rem !important;
                        }

                        th:nth-child(5) {
                            text-align: center;
                        }
                    }
                }
            }
        }

        /* Responsive design for summary cards */
        @media (max-width: 768px) {
            x-summary-cards {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            x-summary-cards {
                grid-template-columns: 1fr;
            }
        }
    }

    /* Progress bar styling with red and green gradients */
    progress-bar {
        display: flex;
        width: 100%;
        height: 28px;
        border-radius: 14px;
        overflow: hidden;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        margin: 6px 0;
        box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);

        progress-used {
            background: #f8265c;
            display: block;
            height: 100%;
            transition: width 0.3s ease;
            box-shadow: inset 0 1px 0 rgba(255,255,255,0.2);
        }

        progress-remaining {
            background: #f4aa29;
            display: block;
            height: 100%;
            transition: width 0.3s ease;
            box-shadow: inset 0 1px 0 rgba(255,255,255,0.2);
        }
    }

    /* Progress details styling */
    progress-details {
        display: flex;
        justify-content: space-between;

        color: #000000;
        margin-top: 4px;

        span:first-child {
            color: #000000; /* Black for interest label */
        }

        span:last-child {
            color: #000000; /* Black for principal label */
        }
    }

    /* Progress column specific styling */
    td.progress-column {
        vertical-align: middle;

        progress-bar {
            margin: 0;
            height: 20px;
        }
    }
}

/* Debt Free 4 Life Report Styling */

/* Report Header */
.report-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem 0;

    .logo {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #5462c2, #7f8cf0);
        border-radius: 8px;
        flex-shrink: 0;
    }

    .title-group {
        h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
        }

        p {
            margin: 0;
            
            color: #6b7280;
        }
    }
}

/* Section Styling */
x-section {
    margin-bottom: 2rem !important;
    display: block;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 20px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08), 0 4px 10px rgba(0, 0, 0, 0.03);
    padding: 24px;

    h2 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #0f172a;
        margin-bottom: 0.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 2px solid #6366f1;
        padding-bottom: 0.75rem;
    }

    .subtitle {
        color: #64748b;
        font-size: 0.9rem;
        margin-bottom: 1.5rem;
    }
}

/* Label tag default styling */
label-tag.default {
    background-color: #e5e5e5;
    color: black;
}

/* Disclaimer text */
p.disclaimer {
    margin: 1.5rem 0;
    line-height: 1.5;
    color: #374151;
}

/* Highlight section with red text */
.highlight {
    text-align: center;
    margin: 2rem 0;
    padding: 1rem;
}

.highlight-red {
    color: #dc2626;
    font-weight: 600;
}

/* Timeline styling */
.timeline {
    margin: 3rem 0;

    h3 {
        text-align: center;
        color: #4a5568;
        margin-bottom: 3rem;
        font-weight: 600;
    }
}

.timeline-container {
    position: relative;
    max-width: 850px;
    margin: 0 auto;
}

.timeline-line {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, #6366f1, #8b5cf6);
    transform: translateX(-50%);
    border-radius: 2px;
}

.timeline-item {
    display: flex;
    align-items: center;
    margin: 2rem 0;
    position: relative;

    &.left {
        justify-content: flex-end;
    }

    &.right {
        justify-content: flex-start;
    }
}

.timeline-content {
    background: white;
    padding: 1.25rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 280px;
    text-align: left;
    font-size: 0.9rem;
    line-height: 1.3;

    strong {
        color: #2d3748;
    }
}

.timeline-dot {
    width: 14px;
    height: 14px;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-radius: 50%;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

/* Table Styling */
.red {
    color: #dc2626;
    font-weight: 500;
}

/* Table styling */
table-wrapper.data-list table {
    border: none;

    tbody tr {
        border: none;

        td {
            padding-top: 1rem;
            padding-bottom: 1rem;
            border: none;
            text-align: right;
        }

        &:nth-child(odd) {
            background-color: #f5f5f5;
        }

        &:nth-child(even) {
            background-color: white;
        }
    }

    /* Header styling - white background, no borders, with padding */
    thead {
        background-color: white !important;

        tr {
            background-color: white !important;
            text-align: right;
            border-bottom: 1px solid #6b6b6b;

            th {
                background-color: white !important;
                padding-top: 1rem;
                padding-bottom: 1rem;
                border: none;
            }
        }
    }

    /* Footer styling */
    tfoot tr {
        background-color: white;
        font-weight: 600;

        td {
            padding-top: 1rem;
            padding-bottom: 1rem;
            border: none;
            text-align: right;
        }
    }

    /* First column (Debt) should be left-aligned */
    thead tr th:first-child,
    tbody tr td:first-child,
    tfoot tr td:first-child {
        text-align: left;
    }

    /* Make small elements in table cells display as block to create line breaks */
    tbody tr td small {
        display: block;
        margin-top: 0.25rem;
    }
}

/* Section 1 table - Monthly Interest and Eff. Int. Cost columns should be red */
x-section:nth-of-type(2) table-wrapper.data-list table {
    thead tr th:nth-child(5),
    thead tr th:nth-child(6),
    tbody tr td:nth-child(5),
    tbody tr td:nth-child(6),
    tfoot tr td:nth-child(5),
    tfoot tr td:nth-child(6) {
        color: #dc2626 !important;
        font-weight: 500;
    }
}

/* Balance Comparison */
.balance-comparison {
    margin: 2rem 0;

    .balance-bar {
        margin-bottom: 1rem;

        .bar-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;

            .amount {
                font-weight: 600;
                color: #1f2937;
            }
        }

        .progress-track {
            height: 24px;
            background: #f3f4f6;
            border-radius: 12px;
            overflow: hidden;
            position: relative;

            .progress-fill {
                height: 100%;
                background: #f4aa29;
                transition: width 0.3s ease;
            }

            &.stacked {
                display: flex;

                .segment-principal {
                    background: #f4aa29;
                }

                .segment-interest {
                    background: #f8265c;
                }

                .segment-cash-value {
                    background: #22b945;
                }
            }
        }

        .bar-details {
            margin-top: 0.5rem;
            font-size: 0.75rem;
            color: #6b7280;
        }
    }
}

/* Metrics Grid */
x-section .metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin: 2rem 0;

    .metric {
        text-align: center;
        padding: 1.5rem 1rem;
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .metric-label {
            color: #6b7280;
            margin: 0;
        }
    }
}

/* Highlight Box */
.highlight {
    background: #ecfdf5;
    border: 1px solid #a7f3d0;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1.5rem 0;

    p {
        margin: 0;
        color: #065f46;
        font-weight: 500;
    }
}

/* Timeline */
.timeline {
    margin: 2rem 0;

    h3 {
        font-size: 1rem;
        color: #6b7280;
        margin-bottom: 1rem;
    }

    .timeline-item {
        display: flex;
        gap: 1rem;
        margin-bottom: 0.75rem;

        .timeline-date {
            font-family: monospace;
            color: #5462c2;
            font-weight: 600;
            min-width: 80px;
        }

        .timeline-content {
            flex: 1;
            color: #4b5563;
        }
    }
}

/* Benefits Summary */
.benefits-summary {
    display: grid;
    gap: 1rem;
    margin: 2rem 0;

    .benefit-card {
        text-align: center;
        padding: 2rem;
        border: 2px solid #1f2937;
        border-radius: 1rem;
        background: #f9fafb;

        .benefit-amount {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .benefit-label {
            color: #6b7280;
            margin: 0;
            line-height: 1.4;
        }
    }
}

/* Section 4 specific metrics styling */
x-section:has(h2:contains("Section 4")) .metrics {
    border-left: 4px solid #5462c2;
    padding-left: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .metrics {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .report-header {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .balance-comparison .bar-label {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

/* Print Report Functionality - Consolidated print styles */
@media print {
    /* 1. Expand the default content container */
    content-container {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 !important;
        margin: 0 auto !important;
        overflow: visible !important;
    }

    /* 2. Hide UI clutter (navs, buttons, layout shells) */
    split-button,
    .btn, .btn-wrapper, .dropdown-wrapper,
    .breadcrumb, page-nav-wrapper, nav-wrapper, nav, footer,
    button[onclick="window.print()"] {
        display: none !important;
    }

    /* Hide form wizard header when printing */
    form.bux-form-wizard > header {
        display: none !important;
    }

    /* Panel page break rules for long content */
    panel {
        page-break-inside: avoid;
        page-break-after: auto;
    }

    /* Ensure panels don't break awkwardly */
    content {
        page-break-inside: avoid;
    }

    /* Ensure all report content is visible and printable */
    df4l-advisor-debt-free {
        display: block !important;
        visibility: visible !important;
    }

    /* Prevent tables from breaking awkwardly */
    table {
        page-break-inside: avoid;
    }

    /* Preserve all colors as-is when printing */
    * {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
}

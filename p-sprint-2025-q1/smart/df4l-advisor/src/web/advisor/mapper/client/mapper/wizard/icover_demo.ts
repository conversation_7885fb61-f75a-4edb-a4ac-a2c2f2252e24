import "./icover_demo.mcss";
import { SE } from "@granite/lib.mts";

const slides = [
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_000.png",
        title: "Slide 1",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_001.png",
        title: "Slide 2",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_002.png",
        title: "Slide 3",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_003.png",
        title: "Slide 4",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_004.png",
        title: "Slide 5",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_005.png",
        title: "Slide 6",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_006.png",
        title: "Slide 7",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_007.png",
        title: "Slide 8",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_008.png",
        title: "Slide 9",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_009.png",
        title: "Slide 10",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_010.png",
        title: "Slide 11",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_011.png",
        title: "Slide 12",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_012.png",
        title: "Slide 13",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_013.png",
        title: "Slide 14",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_014.png",
        title: "Slide 15",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_015.png",
        title: "Slide 16",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_016.png",
        title: "Slide 17",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_017.png",
        title: "Slide 18",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_018.png",
        title: "Slide 19",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_019.png",
        title: "Slide 20",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_020.png",
        title: "Slide 21",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_021.png",
        title: "Slide 22",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_022.png",
        title: "Slide 23",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_023.png",
        title: "Slide 24",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_024.png",
        title: "Slide 25",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_025.png",
        title: "Slide 26",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_026.png",
        title: "Slide 27",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_027.png",
        title: "Slide 28",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_028.png",
        title: "Slide 29",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_029.png",
        title: "Slide 30",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_030.png",
        title: "Slide 31",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_031.png",
        title: "Slide 32",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_032.png",
        title: "Slide 33",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_033.png",
        title: "Slide 34",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_034.png",
        title: "Slide 35",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_035.png",
        title: "Slide 36",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_036.png",
        title: "Slide 37",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_037.png",
        title: "Slide 38",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_038.png",
        title: "Slide 39",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_039.png",
        title: "Slide 40",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_040.png",
        title: "Slide 41",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_041.png",
        title: "Slide 42",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_042.png",
        title: "Slide 43",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_043.png",
        title: "Slide 44",
    },
    {
        image: "https://asset7.net/debt2capital/icover-demo/d2c_044.png",
        title: "Slide 45",
    },
];

// preload all the slides
slides.forEach((slide) => {
    const img = new Image();
    img.src = slide.image;
});

let current_slide_index = 0;

const $slide_image: HTMLImageElement = SE(document, ".slide-image");
const $prev_button: HTMLButtonElement = SE(document, ".prev-btn");
const $next_button: HTMLButtonElement = SE(document, ".next-btn");
const $slide_counter: HTMLElement = SE(document, ".slide-counter");

function update_slide() {
    const slide = slides[current_slide_index];
    if (!slide) return;

    $slide_image.src = slide.image;
    $slide_image.alt = slide.title;
    $slide_counter.textContent = `Step ${current_slide_index + 1} of ${slides.length}`;
    $prev_button.disabled = current_slide_index === 0;
    $next_button.disabled = current_slide_index === slides.length - 1;

    // scroll to top
    window.scrollTo(0, 0);
}

function prev_slide() {
    if (current_slide_index > 0) {
        current_slide_index--;
        update_slide();
    }
}

function next_slide() {
    if (current_slide_index < slides.length - 1) {
        current_slide_index++;
        update_slide();
    }
}

function flash_button(button: HTMLButtonElement) {
    button.classList.add("flash");
    setTimeout(() => {
        button.classList.remove("flash");
    }, 300);
}

$prev_button.addEventListener("click", prev_slide);
$next_button.addEventListener("click", next_slide);

// Click on image to flash the next button (guide user to use button)
$slide_image.addEventListener("click", () => {
    if (current_slide_index < slides.length - 1) {
        flash_button($next_button);
    }
});

document.addEventListener("keydown", (event) => {
    if (event.key === "ArrowLeft") prev_slide();
    if (event.key === "ArrowRight") next_slide();
    if (event.key === "Escape") self.close();
});

update_slide();

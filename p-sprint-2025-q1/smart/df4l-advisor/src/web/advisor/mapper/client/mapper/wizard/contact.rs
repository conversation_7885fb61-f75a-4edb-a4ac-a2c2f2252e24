#[approck::http(GET /advisor/{advisor_uuid:Uuid}/client/{client_uuid:Uuid}/wizard/contact; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use super::super::WizardStep;
        use approck::html;

        use super::super::client_wizard_context;
        use super::client_wizard_contact_get;

        let wizard_data = client_wizard_context::call(
            app,
            identity,
            client_wizard_context::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;

        // Read demo status from the database via wizard_data
        let is_demo = wizard_data.demo;

        let client = client_wizard_contact_get::call(
            app,
            identity,
            client_wizard_contact_get::Input {
                client_uuid: path.client_uuid,
            },
        )
        .await?;
        doc.set_title("Edit Client");

        let mut form_wizard = bux::component::form_wizard::new(WizardStep::Contact, wizard_data)?;
        form_wizard.disable_auto_complete();

        form_wizard.add_heading("Edit Client Info");
        form_wizard.add_description("This is the information of the insured.  Spouse information, if required, will be entered on a subsequent step.");
        form_wizard.set_id("contact-editor");
        form_wizard.set_hidden("advisor_uuid", path.advisor_uuid);
        form_wizard.set_hidden("client_uuid", path.client_uuid);

        #[rustfmt::skip]
        form_wizard.add_body(maud::html!(
            @if is_demo {
                (super::super::demo_notice_html(true))
            }

            grid-2 {
                div {
                    grid-2 {
                        (bux::input::text::string::name_label_value("first_name", "First Name", Some(&client.first_name)))
                        (bux::input::text::string::name_label_value("last_name", "Last Name", Some(&client.last_name)))
                    }
                    (bux::input::text::string::name_label_value("address1", "Address Line 1", client.address1.as_deref()))
                    (bux::input::text::string::name_label_value("address2", "Address Line 2", client.address2.as_deref()))

                    grid-3 {
                        (bux::input::text::string::name_label_value("city", "City", client.city.as_deref()))
                        (addr_iso::input::address_us_select::us_state_select_with_help(app, "state", "State", client.state.as_deref(), "").await?)
                        (bux::input::text::string::name_label_value("zip", "ZIP", client.zip.as_deref()))
                    }
                }
                div {
                    (bux::input::text::string::name_label_value("email", "Email", client.email.as_deref()))
                    (bux::input::text::string::name_label_value("phone", "Phone", client.phone.as_deref()))
                    grid-2 {
                        (bux::input::date::bux_input_date("birth_date", "Birth Date", client.birth_date))
                        (bux::input::select::gender::gender_select("gender", "Gender", client.gender.as_deref()))
                    }
                    (bux::input::textarea::string::name_label_value("note", "Note", client.note.as_deref()))
                }
            }
        ));

        doc.hide_page_nav();
        doc.add_body(html! {
            div.constrain-width-md {
                (form_wizard)
            }
        });
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod client_wizard_contact_get {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub client_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub address1: Option<String>,
        pub address2: Option<String>,
        pub city: Option<String>,
        pub state: Option<String>,
        pub zip: Option<String>,

        pub note: Option<String>,
        pub gender: Option<String>,
        pub birth_date: Option<DateUtc>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_read(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_read({})", input.client_uuid);
        }

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
            };
            row = {
                client_uuid: Uuid,
                first_name: String,
                last_name: String,
                email: Option<String>,
                phone: Option<String>,
                address1: Option<String>,
                address2: Option<String>,
                city: Option<String>,
                state: Option<String>,
                zip: Option<String>,

                gender: Option<String>,
                birth_date: Option<DateUtc>,
                note: Option<String>,
            };
            SELECT
                client_uuid,
                first_name,
                last_name,
                email,
                phone,
                address1,
                address2,
                city,
                state,
                zip,

                gender,
                birth_date,
                note
            FROM
                df4l.client
            WHERE
                client_uuid = $client_uuid
        )
        .await?;

        Ok(Output {
            client_uuid: row.client_uuid,
            first_name: row.first_name,
            last_name: row.last_name,
            email: row.email,
            phone: row.phone,
            address1: row.address1,
            address2: row.address2,
            city: row.city,
            state: row.state,
            zip: row.zip,

            gender: row.gender,
            birth_date: row.birth_date,
            note: row.note,
        })
    }
}

#[approck::api]
pub mod client_wizard_contact_set {
    use granite::NestedError;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub client_uuid: Uuid,
        pub first_name: String,
        pub last_name: String,
        pub email: String,
        pub phone: String,
        pub address1: String,
        pub address2: Option<String>,
        pub city: String,
        pub state: String,
        pub zip: String,
        pub gender: String,
        pub birth_date: DateUtc,
        pub note: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output;

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.client_write(&dbcx, input.client_uuid).await {
            return_authorization_error!("identity.client_write({})", input.client_uuid);
        }

        // load up a Client_Partial object
        let contact_partial = df4l_zero::client::wizard::ContactInput {
            first_name: Some(input.first_name.clone()),
            last_name: Some(input.last_name.clone()),
            email: Some(input.email.clone()),
            phone: Some(input.phone.clone()),
            address1: Some(input.address1.clone()),
            address2: input.address2.clone(),
            city: Some(input.city.clone()),
            state: Some(input.state.clone()),
            zip: Some(input.zip.clone()),
            gender: Some(input.gender.clone()),
            birth_date: Some(input.birth_date),
        };

        let contact = match contact_partial.validate() {
            Ok(contact) => contact,
            Err(errors) => {
                return Ok(Response::ValidationError(NestedError {
                    outer: "There was some errors in your input.  Please correct the errors and try again.".to_string(),
                    inner: Some(Input_Error {
                        client_uuid: None,
                        first_name: errors.first_name,
                        last_name: errors.last_name,
                        email: errors.email,
                        phone: errors.phone,
                        address1: errors.address1,
                        address2: errors.address2,
                        city: errors.city,
                        state: errors.state,
                        zip: errors.zip,
                        gender: errors.gender,
                        birth_date: errors.birth_date,
                        note: None,
                    }),
                }));
            }
        };

        granite::pg_execute!(
            db = dbcx;
            args = {
                $client_uuid: &input.client_uuid,
                $first_name: &contact.first_name,
                $last_name: &contact.last_name,
                $email: &contact.email,
                $phone: &contact.phone,
                $address1: &contact.address1,
                $address2: &contact.address2,
                $city: &contact.city,
                $state: &contact.state,
                $zip: &contact.zip,
                $gender: &contact.gender.to_string(),
                $birth_date: &contact.birth_date,
            };
            UPDATE
                df4l.client
            SET
                first_name = $first_name,
                last_name = $last_name,
                email = $email,
                phone = $phone,
                address1 = $address1,
                address2 = $address2,
                city = $city,
                state = $state,
                zip = $zip,
                gender = $gender,
                birth_date = $birth_date
            WHERE
                client_uuid = $client_uuid
        )
        .await?;

        Ok(Response::Output(Output))
    }
}

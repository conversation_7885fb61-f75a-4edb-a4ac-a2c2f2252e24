#client-add {

    #demo-notice {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 12px;
        margin-bottom: 16px;
        text-align: center;

        a {
            color: #d63384;
            font-weight: bold;
            text-decoration: underline;
        }
    }
    
    #split {
        display: grid;
        grid-template-columns: 3fr 2fr;
        gap: 4rem;                
    }

    fieldset {
        border: none;
        padding: 0;
        margin: 2rem 0;

        legend {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 1rem;
            padding: 0;
        }

        ul {
            list-style: none;
            padding: 0;
            margin: 0;

            li {
                margin-bottom: 1rem;
                padding: 1rem;
                border: 2px solid #ddd;
                border-radius: 8px;
                transition: all 0.2s ease;

                &:has(input[type="radio"]:checked) {
                    border-color: #007bff;
                    background-color: #f8f9fa;
                }

                input[type="radio"] {
                    margin-right: 0.75rem;
                    transform: scale(1.5);
                }

                label {
                    cursor: pointer;
                    font-size: 1.1rem;
                    font-weight: bold;
                }
            }
        }
 
    }

    fieldset:invalid {
        ul.client-type-options {
            border: 2px solid #dc3545;
            border-radius: 8px;
            padding: 0.5rem;
            background-color: #f8d7da;
        }

        &::after {
            content: attr(data-error);
            display: block;
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.5rem;
        }
    }
}
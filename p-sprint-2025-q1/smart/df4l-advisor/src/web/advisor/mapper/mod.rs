pub mod client;
pub mod client0;
pub mod index;
pub mod onboarding;

#[approck::prefix(/advisor/{advisor_uuid:Uuid}/)]
pub mod prefix {
    pub fn menu(app: App, menu: Menu, advisor_uuid: Uuid) {
        menu.set_label_name_uri(
            "Advisor Dashboard",
            app.uuid_to_label(advisor_uuid),
            &df4l_zero::ml_advisor(advisor_uuid),
        );
        //TODO UPDATE LINKING, implement ml_advisor_client_list
        menu.add_link_icon(
            "D2C Client List",
            &df4l_zero::ml_advisor_client_list(advisor_uuid),
            approck::Icon::emoji_list(),
        );
        menu.add_link_icon(
            "Add New Client",
            &df4l_zero::ml_advisor_client_add(advisor_uuid),
            approck::Icon::emoji_plus(),
        );
        menu.add_link_icon(
            "Add Demo Client",
            &df4l_zero::ml_advisor_client_add_demo(advisor_uuid),
            approck::Icon::emoji_wizard(),
        );
        menu.add_link_icon(
            "Legacy Client List",
            &df4l_zero::ml_advisor_client0_list(advisor_uuid),
            approck::Icon::emoji_scroll(),
        );

        menu.add_link_icon("My Account", "/myaccount/", approck::Icon::emoji_person());
    }
    pub fn auth() {}
}

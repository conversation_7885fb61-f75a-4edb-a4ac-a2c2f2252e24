#[approck::http(GET /myaccount/advisor/{advisor_uuid:Uuid}/address; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;

        doc.set_title("Edit Address");

        use crate::api::myaccount::advisor_detail::detail_advisor;

        let output = detail_advisor::call(
            app,
            identity,
            detail_advisor::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        let panel = {
            let mut panel = bux::component::save_cancel_form_panel("Edit Address", "/myaccount/");
            panel.set_hidden("advisor_uuid", path.advisor_uuid);
            panel.add_body(html!(
                (bux::input::text::string::name_label_value("address1", "Address Line 1:", output.address1.as_deref()))
                (bux::input::text::string::name_label_value("address2", "Address Line 2:", output.address2.as_deref()))
                grid-3 {
                    (bux::input::text::string::name_label_value("city", "City:", output.city.as_deref()))
                    (addr_iso::input::address_us_select::us_state_select_with_help(app, "state", "State:", output.state.as_deref(), "").await?)
                    (bux::input::text::string::name_label_value("zip", "ZIP Code:", output.zip.as_deref()))
                }

            ));
            panel
        };

        doc.set_body_display_narrow();
        doc.add_body(html!((panel)));
        Ok(Response::HTML(doc.into()))
    }
}

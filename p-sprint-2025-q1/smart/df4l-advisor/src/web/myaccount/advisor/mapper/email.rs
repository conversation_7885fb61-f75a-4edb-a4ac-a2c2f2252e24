#[approck::http(GET /myaccount/advisor/{advisor_uuid:Uuid}/email; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;

        doc.set_title("Edit Email Address");

        use crate::api::myaccount::advisor_detail::detail_advisor;

        let output = detail_advisor::call(
            app,
            identity,
            detail_advisor::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        let form_panel = {
            let mut panel =
                bux::component::save_cancel_form_panel("Edit Email Address", "/myaccount/");
            panel.set_hidden("advisor_uuid", path.advisor_uuid);
            panel.add_body(html!(
                (bux::input::text::string::name_label_value(
                    "email",
                    "Email Address:",
                    output.email.as_deref()
                ))
            ));
            panel
        };

        doc.set_body_display_narrow();
        doc.add_body(html!((form_panel)));
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod save_email {
    use granite::NestedError;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub email: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output;

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.advisor_write(input.advisor_uuid) {
            return_authorization_error!("identity.advisor_write({})", input.advisor_uuid);
        }

        let mut error = Input_Error {
            advisor_uuid: None,
            email: None,
        };

        if input.email.is_empty() {
            error.email = Some("Email is required.".to_string());
        } else if !granite::validate_email(&input.email) {
            error.email = Some("Invalid email address.".to_string());
        }

        if error.email.is_some() {
            return Ok(Response::ValidationError(NestedError {
                outer: "Validation Error".to_string(),
                inner: Some(error),
            }));
        }

        granite::pg_execute!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
                $email: &input.email,
            };
            UPDATE
                df4l.advisor
            SET
                email = $email
            WHERE
                advisor_uuid = $advisor_uuid
        )
        .await?;

        Ok(Response::Output(Output {}))
    }
}

// -------------------------------------------------------------------------------------------------
// 1. Import Components
import "./email.mcss";
import "@bux/input/text/string.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code

import { SEC } from "@granite/lib.mts";
import { go_back } from "@bux/singleton/nav_stack.mts";
import { save_email } from "./emailλ.mjs";

import BuxInputTextString from "@bux/input/text/string.mjs";
import FormPanel from "@bux/component/form_panel.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements

const $form = SEC(HTMLFormElement, document, "form.form-panel");
const advisor_uuid = SEC(HTMLInputElement, $form, "[name=advisor_uuid]").value;

const $email = SEC(BuxInputTextString, $form, "[name=email]");

// -------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

// -------------------------------------------------------------------------------------------------
// 5. Form Panel

new FormPanel({
    $form,
    api: save_email.api,
    on_cancel: go_back,

    err: (errors) => {
        $email.set_e(errors.email);
        $form.reportValidity();
    },

    get: () => {
        return {
            advisor_uuid: advisor_uuid,
            email: $email.value,
        };
    },

    set: (_value) => {
    },

    out: (_output) => {
        go_back();
    },
});

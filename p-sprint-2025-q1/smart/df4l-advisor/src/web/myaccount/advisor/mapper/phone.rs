#[approck::http(GET /myaccount/advisor/{advisor_uuid:Uuid}/phone; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use approck::html;

        doc.set_title("Edit Phone Number");

        use crate::api::myaccount::advisor_detail::detail_advisor;

        let output = detail_advisor::call(
            app,
            identity,
            detail_advisor::Input {
                advisor_uuid: path.advisor_uuid,
            },
        )
        .await?;

        let form_panel = {
            let mut panel =
                bux::component::save_cancel_form_panel("Edit Phone Number", "/myaccount/");
            panel.set_hidden("advisor_uuid", path.advisor_uuid);
            panel.add_body(html!(
                (bux::input::text::string::name_label_value(
                    "phone",
                    "Phone Number:",
                    output.phone.as_deref()
                ))
            ));
            panel
        };

        doc.set_body_display_narrow();
        doc.add_body(html!((form_panel)));
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod save_phone {
    use granite::NestedError;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub advisor_uuid: Uuid,
        pub phone: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output;

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        if !identity.advisor_write(input.advisor_uuid) {
            return_authorization_error!("identity.advisor_write({})", input.advisor_uuid);
        }

        let mut error = Input_Error {
            advisor_uuid: None,
            phone: None,
        };

        if input.phone.is_empty() {
            error.phone = Some("Phone number is required.".to_string());
        } else if !granite::validate_phone_us(&input.phone) {
            error.phone = Some("Invalid phone number.".to_string());
        }

        if error.phone.is_some() {
            return Ok(Response::ValidationError(NestedError {
                outer: "Validation Error".to_string(),
                inner: Some(error),
            }));
        }

        granite::pg_execute!(
            db = dbcx;
            args = {
                $advisor_uuid: &input.advisor_uuid,
                $phone: &input.phone,
            };
            UPDATE
                df4l.advisor
            SET
                phone = $phone
            WHERE
                advisor_uuid = $advisor_uuid
        )
        .await?;

        Ok(Response::Output(Output {}))
    }
}

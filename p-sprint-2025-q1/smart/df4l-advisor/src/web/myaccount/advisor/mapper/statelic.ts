import "./statelic.mcss";
import "@bux/input/checkbox.mts";

import FormPanel from "@bux/component/form_panel.mts";
import BuxInputCheckbox from "@bux/input/checkbox.mts";
import { SE } from "@granite/lib.mts";

import { go_back } from "@bux/singleton/nav_stack.mts";
import { advisor_onboarding_checklist_statelic_set } from "@crate/api/myaccount/statelicλ.mjs";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $advisor_uuid: HTMLInputElement = SE($form, "[name=advisor_uuid]");
const advisor_uuid = $advisor_uuid.value;

const $checkboxes: NodeListOf<BuxInputCheckbox> = $form.querySelectorAll(
    'bux-input-checkbox[name="state_code"]',
);

// Select All / Deselect All functionality
const $selectAllBtn = SE(document, "#select-all-btn") as HTMLButtonElement;
const $deselectAllBtn = SE(document, "#deselect-all-btn") as HTMLButtonElement;

function get_selected_state_codes(): string[] {
    return Array.from($checkboxes)
        .filter((checkbox) => {
            const result = checkbox.get();
            if ("Ok" in result) {
                return result.Ok;
            } else {
                return false;
            }
        })
        .map((checkbox) => checkbox.get_value() || "");
}

function select_all_states(): void {
    Array.from($checkboxes).forEach((checkbox) => {
        checkbox.set_p(true);
    });

    // Visual feedback: highlight the select all button briefly
    provide_button_feedback($selectAllBtn);
}

function deselect_all_states(): void {
    Array.from($checkboxes).forEach((checkbox) => {
        checkbox.set_p(false);
    });

    // Visual feedback: highlight the deselect all button briefly
    provide_button_feedback($deselectAllBtn);
}

function provide_button_feedback(button: HTMLButtonElement): void {
    // Add a temporary class for visual feedback
    button.classList.add("button-clicked");

    // Remove the class after a short delay
    setTimeout(() => {
        button.classList.remove("button-clicked");
    }, 300);
}

// Event listeners for select all/deselect all buttons
$selectAllBtn.addEventListener("click", select_all_states);
$deselectAllBtn.addEventListener("click", deselect_all_states);

new FormPanel({
    $form,
    api: advisor_onboarding_checklist_statelic_set.api,
    on_cancel: go_back,

    err: (_errors) => {
        // string error will suffice
    },

    get: () => {
        return {
            advisor_uuid: advisor_uuid,
            state_codes: get_selected_state_codes(),
        };
    },

    set: (_value) => {
        // No prefill required for this screen
    },

    out: (_output) => {
        go_back();
    },
});

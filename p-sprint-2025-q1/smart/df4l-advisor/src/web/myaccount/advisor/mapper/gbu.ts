import "./gbu.mcss";
import "@bux/input/text/string.mts";

import { SE } from "@granite/lib.mts";
import { go_back } from "@bux/singleton/nav_stack.mts";
import { edit_advisor } from "@crate/api/myaccount/advisor_editλ.mjs";

import BuxInputTextString from "@bux/input/text/string.mts";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $advisor_uuid: HTMLInputElement = SE($form, "[name=advisor_uuid]");
const $gbu_agent_id: BuxInputTextString = SE($form, "[name=gbu_agent_id]");

new FormPanel({
    $form,
    api: edit_advisor.api,
    on_cancel: go_back,

    err: (errors) => {
        $gbu_agent_id.set_e(errors.gbu_agent_id);
    },

    get: () => {
        return {
            advisor_uuid: $advisor_uuid.value,
            first_name: undefined, // Not updating contact info from GBU form
            last_name: undefined,
            email: undefined,
            phone: undefined,
            address1: undefined,
            address2: undefined,
            city: undefined,
            state: undefined,
            zip: undefined,

            gbu_agent_id: $gbu_agent_id.value_option,
        };
    },

    set: (_value) => {
    },

    out: (_output) => {
        go_back();
    },
});

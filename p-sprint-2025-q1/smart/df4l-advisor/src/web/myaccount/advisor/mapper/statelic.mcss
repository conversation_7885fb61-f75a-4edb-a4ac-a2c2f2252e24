.text-center {
    text-align: center;
}

panel {
    content {
        .select-all-controls {
            text-align: center;
            margin: 1rem 0;

            button {
                margin: 0 0.5rem;
                font-size: 0.875rem;

                &:active {
                    background-color: #3b82f6;
                    color: #ffffff;
                    border-color: #2563eb;
                }

                &.button-clicked {
                    background-color: #10b981;
                    color: #ffffff;
                    border-color: #059669;
                }

                i {
                    margin-right: 0.25rem;
                }
            }
        }

        .state-checkboxes {
            column-width: 175px;
        }
    }
}
 
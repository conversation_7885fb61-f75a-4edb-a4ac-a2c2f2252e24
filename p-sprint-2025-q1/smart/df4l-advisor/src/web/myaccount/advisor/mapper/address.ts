import "./address.mcss";
import "@bux/input/text/string.mts";
import "@addr-iso/input/address_us_select.mts";

import FormPanel from "@bux/component/form_panel.mts";
import BuxInputTextString from "@bux/input/text/string.mts";
import { AddressUsSelect } from "@addr-iso/input/address_us_select.mts";
import { SE } from "@granite/lib.mts";

import { go_back } from "@bux/singleton/nav_stack.mts";
import { edit_advisor } from "@crate/api/myaccount/advisor_editλ.mjs";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $advisor_uuid: HTMLInputElement = SE($form, "[name=advisor_uuid]");
const advisor_uuid = $advisor_uuid.value;

const $address1: BuxInputTextString = SE($form, "[name=address1]");
const $address2: BuxInputTextString = SE($form, "[name=address2]");
const $city: BuxInputTextString = SE($form, "[name=city]");
const $state: AddressUsSelect = SE($form, "[name=state]");
const $zip: BuxInputTextString = SE($form, "[name=zip]");

new FormPanel({
    $form,
    api: edit_advisor.api,
    on_cancel: go_back,

    err: (errors: any) => {
        $address1.set_e(errors.address1);
        $address2.set_e(errors.address2);
        $city.set_e(errors.city);
        $state.set_e(errors.state);
        $zip.set_e(errors.zip);
    },

    get: () => {
        return {
            advisor_uuid: advisor_uuid,
            address1: $address1.value_option,
            address2: $address2.value_option,
            city: $city.value_option,
            state: $state.value_option,
            zip: $zip.value_option,
        };
    },

    set: (_value) => {
        // No prefill required for this screen as data is already loaded in the form
    },

    out: (_output) => {
        go_back();
    },
});

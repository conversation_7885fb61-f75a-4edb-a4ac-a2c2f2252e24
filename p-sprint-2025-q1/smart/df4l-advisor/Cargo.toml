[package]
name = "df4l-advisor"
version = "0.1.0"
edition = "2024"

[package.metadata.approck.mod]
extends = [
    "df4l-zero",
    "df4l-icover",
    "approck",
    "auth-fence",
    "bux",
    "granite",
    "addr-iso",
    "api-crs",
]


[dependencies]
approck = { workspace = true }
approck-postgres = { workspace = true }
auth-fence = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
chrono = { workspace = true, features = ["serde"] }
num-traits = { workspace = true }
maud = { workspace = true }
postgres-types = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
rand = { workspace = true }
itertools = { workspace = true }

df4l-zero = { path = "../df4l-zero" }
df4l-icover = { path = "../df4l-icover" }
addr-iso = { workspace = true }
api-crs = { workspace = true }

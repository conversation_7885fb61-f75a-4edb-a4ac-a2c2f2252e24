[[audits]]
type = "dependency_version"
warn_on_version = true

[[audits]]
type = "dependency_extends_match"

[[audits]]
type = "module_files"
warn_on_extra = true

# [[audits]]
# type = "file_existence"
# expected_files = ["", ""]

# [[audits]]
# type = "item_presence"
# file = "src/module/mod.rs"
# expected_items = ["impl extends::App for crate::AppStruct", "impl extends::Identity for crate::IdentityStruct"]
# warn_on_extra = true
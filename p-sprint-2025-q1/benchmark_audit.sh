#!/bin/bash

# Benchmark script for audit performance
echo "🚀 Audit Performance Benchmarking"
echo "=================================="

# Function to run audit and extract timing
run_audit_benchmark() {
    local description="$1"
    echo ""
    echo "📊 $description"
    echo "---"
    
    # Run audit and capture output
    output=$(./acp audit 2>&1)
    exit_code=$?
    
    # Extract timing information
    echo "$output" | grep -A 20 "🕐 Audit Performance Summary:"
    
    return $exit_code
}

# Test 1: Normal run
run_audit_benchmark "Normal Audit Run"

# Test 2: Multiple runs for consistency
echo ""
echo "🔄 Running 3 consecutive audits for consistency check:"
for i in {1..3}; do
    echo "Run $i:"
    ACP_AUDIT_TIMING=1 ./acp audit 2>&1 | grep -E "(Total audit time:|Audit completed)" | sed 's/^/  /'
done

# Test 3: Using hyperfine if available
if command -v hyperfine &> /dev/null; then
    echo ""
    echo "📈 Statistical analysis with hyperfine:"
    hyperfine './acp audit' --warmup 1 --runs 5 --ignore-failure
else
    echo ""
    echo "💡 Install hyperfine for statistical benchmarking:"
    echo "   cargo install hyperfine"
fi

echo ""
echo "✅ Benchmarking complete!"

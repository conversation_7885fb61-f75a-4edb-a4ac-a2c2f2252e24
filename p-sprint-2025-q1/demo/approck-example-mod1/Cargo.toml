[package]
name = "approck-example-mod1"
version = "0.1.1"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.approck.mod]
extends = ["approck-example-mod2"]


[dependencies]
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-postgres = { workspace = true }
approck-redis = { workspace = true }

serde = { workspace = true, features = ["derive"] }
maud = { workspace = true }


[package]
name = "approck-example"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.approck.app]
port = 3020
extends = ["approck", "approck-example-mod1", "bux", "granite"]

[dependencies]
approck-example-mod1 = { path = "../approck-example-mod1" }
approck-example-mod2 = { path = "../approck-example-mod2" }

approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-postgres = { workspace = true }
approck-redis = { workspace = true }

clap = { workspace = true, features = ["derive"] }
error-stack = { workspace = true }
futures = { workspace = true }
tokio = { workspace = true, features = ["full"] }
maud.workspace = true
serde = { workspace = true, features = ["derive"] }
serde_json.workspace = true
rand = { workspace = true }
toml = { workspace = true }
reqwest = { workspace = true }

[build-dependencies]
approck-compiler = { workspace = true }


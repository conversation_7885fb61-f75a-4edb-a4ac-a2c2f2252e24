#[approck::http(GET /example/video; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(req: Request, ui: Document) -> Response {
        #[rustfmt::skip]
        ui.add_body(maud::html! {
            div.container.bg-white {
                a href="/example/" { "◂ Back to Example List" }
                h1 { code {  (req.path()) } }

                hr;

                div #video-screen {
                    .video-demo {
                        h2 { "Camera Access and Canvas Video Demo" }

                        p { "This example demonstrates accessing the user's camera and displaying the video feed on a canvas." }

                        div.controls {
                            button #start-camera { "Start Camera" }
                            button #stop-camera disabled { "Stop Camera" }
                        }

                        div.video-container {
                            video #video autoplay muted style="display: none;" {}
                            canvas #canvas width="1280" height="720" style="border: 1px solid #ccc; max-width: 100%; height: auto;" {}
                        }

                        div #status { "Click 'Start Camera' to begin" }
                    }
                }
            }
        });

        Response::HTML(ui.into())
    }
}

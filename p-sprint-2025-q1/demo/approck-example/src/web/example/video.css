#video-screen {
    .video-demo {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
    }

    .video-demo h2 {
        color: #333;
        margin-bottom: 10px;
    }

    .video-demo p {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.5;
    }

    .controls {
        margin-bottom: 20px;
    }

    .controls button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        margin-right: 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s;
    }

    .controls button:hover:not(:disabled) {
        background-color: #0056b3;
    }

    .controls button:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
    }

    .video-container {
        display: flex;
        justify-content: center;
        margin-bottom: 20px;
    }

    #canvas {
        border: 2px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    #status {
        text-align: center;
        padding: 10px;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        color: #495057;
        font-weight: 500;
    }
}

// -------------------------------------------------------------------------------------------------
// 1. Import types

import "./example18.mcss";
import "@bux/input/text/string.mts";
import "@bux/component/form_panel.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code

import { go_back } from "@bux/singleton/nav_stack.mts";
import BuxInputTextString from "@bux/input/text/string.mts";
import { BuxFormDirect } from "@bux/form/direct.mts";
import { SEC } from "@granite/lib.mts";
import { save } from "./example18λ.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find elements

const $form = SEC(HTMLFormElement, document, "#example18");
const $name = SEC(BuxInputTextString, $form, "[name=name]");
const $email = SEC(BuxInputTextString, $form, "[name=email]");
const $phone = SEC(BuxInputTextString, $form, "[name=phone]");
const $age = SEC(BuxInputTextString, $form, "[name=age]");

// -------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

// -------------------------------------------------------------------------------------------------
// 5. Form Panel
new BuxFormDirect({
    $form,
    api: save.api,
    on_cancel: go_back,

    get: () => {
        return {
            name: $name.value_option,
            email: $email.value_option,
            phone: $phone.value_option,
            age: $age.value_option,
        };
    },

    out: (output) => {
        if ("InputErr" in output) {
            const e = output.InputErr[0];
            $name.set_e(e.name);
            $email.set_e(e.email);
            $phone.set_e(e.phone);
            $age.set_e(e.age);
            $form.reportValidity();
            return;
        }

        const o = output.Ok;

        alert(o.next_url);
    },
});

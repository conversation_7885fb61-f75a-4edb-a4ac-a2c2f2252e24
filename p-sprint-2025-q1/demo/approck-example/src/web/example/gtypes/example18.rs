#[approck::http(GET /example/gtypes/example18; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(req: Request, doc: Document) -> Response {
        let mut form_panel = bux::form::direct::save_cancel("Example 18", "/example/");
        form_panel.set_id("example18");
        form_panel.add_body(maud::html! {
            (bux::input::text::string::name_label_value("name", "Name", None))
            (bux::input::text::string::name_label_value("email", "Email", None))
            (bux::input::text::string::name_label_value("phone", "Phone", None))
            (bux::input::text::string::name_label_value("age", "Age", None))
        });

        #[rustfmt::skip]
        doc.add_body(maud::html! {
            div.container.bg-white {
                a href="/example/" { "◂ Back to Example List" } 
                h1 { code {  (req.path()) } }

                hr;

                p { "This is an example of using a GType with a custom validation function." }

                (form_panel)
            }
        });

        Response::HTML(doc.into())
    }
}

#[approck::api2]
pub mod save {

    #[granite::gtype(Api2Input)]
    pub struct Input {
        pub name: Option<String>,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub age: Option<String>,
    }

    #[granite::gtype(Api2Output)]
    pub struct InputErr {
        pub name: Option<String>,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub age: Option<String>,
    }

    #[granite::gtype]
    pub struct InputOk {
        pub name: String,
        pub email: String,
        pub phone: Option<String>,
        pub birth_year: i32,
    }

    pub trait _InputValidate {
        fn validate(value: Input) -> Result<InputOk, InputErr>;
    }

    #[granite::gtype(Api2Output)]
    pub enum Output {
        Ok { next_url: String },
        InputErr(InputErr),
    }

    pub async fn call(input: Input) -> Result<Output> {
        approck::info!("input: {:?}", input);

        Ok(Output::Ok {
            next_url: "/example/".to_string(),
        })
    }
}

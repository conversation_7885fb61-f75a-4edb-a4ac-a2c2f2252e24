#[path = "libλ.rs"]
pub mod libλ;

mod extends;
mod user;
mod web;

#[derive(serde::Deserialize)]
pub struct AppConfig {
    pub webserver: approck::server::ModuleConfig,
    pub redis: approck_redis::ModuleConfig,
    pub postgres: approck_postgres::ModuleConfig,
}

pub struct AppStruct {
    pub webserver: approck::server::Module,
    pub redis: approck_redis::ModuleStruct,
    pub postgres: approck_postgres::ModuleStruct,
}

#[derive(Debug)]
pub struct IdentityStruct {}

// It makes sense to define documents in the web dir, but they should be referred to from here.
pub use crate::web::Document::Document as DocumentStruct;

impl approck::App for AppStruct {
    type Config = AppConfig;
    type Identity = IdentityStruct;
    fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            webserver: approck::server::Module::new(config.webserver)?,
            redis: approck_redis::ModuleStruct::new(config.redis)?,
            postgres: approck_postgres::ModuleStruct::new(config.postgres)?,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        // get the crate name using the env! macro
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {crate_name}");
        Ok(())
    }

    async fn auth(&self, _req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        Ok(IdentityStruct {})
    }
}

impl approck::Identity for crate::IdentityStruct {}

pub trait App:
    approck::App + approck::server::App + approck_redis::App + approck_postgres::App
{
}
pub trait Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}
pub trait Document: bux::document::Base {}

impl App for AppStruct {}
impl Identity for crate::IdentityStruct {}
impl Document for crate::DocumentStruct {}

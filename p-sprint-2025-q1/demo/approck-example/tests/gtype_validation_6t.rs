/*
Input struct is different from the output struct.
Input needs to be type converted (e.g. Option<T> becomes T) and validated in a failable way.
We expect it to pass, and it's an application error if it doesn't.

Rule: any field is failable if it has a function to validate it.
Rule: any failable fields must have a spot in the error struct.

*/

granite::gvalidate! {
    pub struct Person {
        pub person_uuid: granite::Uuid,
        pub title: Option<String>,  // Note this is not part of the output
        pub name: Option<String>,
        pub age: Option<i32>,
    }

    pub struct PersonOk {
        pub person_uuid: granite::Uuid,
        pub name: String,
        pub age: i32,
        pub is_doctor: bool,    // Note this is not part of the input
        pub balance: granite::Decimal,
    }

    pub struct PersonErr {
        pub person_uuid: granite::Uuid,
        pub title: Option<String>,
        pub name: Option<String>,
        pub age: Option<String>,
        pub balance: Option<String>,
    }

    pub trait PersonValidate {
        fn validate(self, balance: granite::Decimal) -> Result<PersonOk, PersonErr>;
        fn finalize(ok: PersonOk) -> Result<PersonOk, PersonErr> {
            Ok(ok)
        }

        fn title(title: Option<String>) -> Result<String, VError> {
            let title = title.v_unwrap()?.v_trim()?.v_min(1)?.v_max(20)?;
            Ok(title)
        }

        fn name(name: Option<String>, is_doctor: &bool) -> Result<String, VError> {
            let name = name.v_unwrap()?.v_trim()?.v_min(1)?.v_max(20)?;

            // Doctor's cannot be called Bob or bob
            if *is_doctor && name.to_lowercase() == "bob" {
                return Err("Doctors cannot be called Bob".into())
            }

            Ok(name)
        }

        fn age(age: Option<i32>) -> Result<i32, VError> {
            age.v_unwrap()?.v_min(21)?.v_max(64)
        }

        fn is_doctor(title: &String) -> Result<bool, VError> {
            Ok(title == "Dr" || title == "Doctor")
        }

        fn balance(balance: granite::Decimal) -> Result<granite::Decimal, VError> {
            balance.v_zero_plus()
        }

        fn has_positive_balance(balance: granite::Decimal) -> bool {
            balance > 0.into()
        }
    }





















    impl PersonValidate for Person {
        #[allow(non_snake_case, unused_parens, unused_variables)]
        fn validate(self, balance: granite::Decimal) -> Result<PersonOk, PersonErr> {
            let person_uuid = self.person_uuid;

            let balance__result = Self::balance(balance);

            let has_positive_balance = if let Ok(balance) = balance__result {
                Ok(Self::has_positive_balance(balance))
            }
            else {
                Err(VError::Dependency)
            };

            // this is destructuring and type converting all input fields
            // These functions must convert from Struct to StructOk
            let title__result = Self::title(self.title);
            let age__result = Self::age(self.age);

            // is_doctor function requires some local variables to run.
            // We can use a topological sort to determine the order in which to run them.
            // We can emit a compiler error if you have cyclic dependencies.
            let is_doctor__result = if let Ok(title) = &title__result {
                Self::is_doctor(&title)
            }
            else {
                Err(VError::Dependency)
            };

            // name function requires some local variables to run.
            let name__result = if let Ok(is_doctor) = is_doctor__result {
                Self::name(self.name, &is_doctor)
            }
            else {
                Err(VError::Dependency)
            };


            // All fields belong here
            match (title__result, name__result, age__result, is_doctor__result, balance__result) {
                // We're only going to use fields in the output struct here
                (Ok(title), Ok(name), Ok(age), Ok(is_doctor), Ok(balance)) => {
                    Self::finalize(PersonOk {
                        person_uuid,
                        name,
                        age,
                        is_doctor,
                        balance,
                    })
                },

                // handle errors
                (name__result, age__result, title__result, is_doctor__result, balance__result) => {
                    Err(PersonErr {
                        person_uuid,
                        title: match title__result {
                            Ok(_) => None,
                            Err(VError::Static(s)) => Some(s.to_string()),
                            Err(VError::Dynamic(s)) => Some(s),
                            Err(VError::Dependency) => None,
                        },
                        name: match name__result {
                            Ok(_) => None,
                            Err(VError::Static(s)) => Some(s.to_string()),
                            Err(VError::Dynamic(s)) => Some(s),
                            Err(VError::Dependency) => None,
                        },
                        age: match age__result {
                            Ok(_) => None,
                            Err(VError::Static(s)) => Some(s.to_string()),
                            Err(VError::Dynamic(s)) => Some(s),
                            Err(VError::Dependency) => None,
                        },
                        balance: match balance__result {
                            Ok(_) => None,
                            Err(VError::Static(s)) => Some(s.to_string()),
                            Err(VError::Dynamic(s)) => Some(s),
                            Err(VError::Dependency) => None,
                        },
                    })
                }
            }
        }
    }

}

#[test]
fn gtype_validate_6t() {
    use std::str::FromStr;
    let person_uuid = granite::Uuid::from_str("00000000-0000-0000-0000-000000000000").unwrap();
    let person = Person {
        person_uuid,
        title: Some("Dr".to_string()),
        name: Some("John".to_string()),
        age: Some(30),
    };

    let balance = granite::Decimal::new(-100, 0);

    match person.validate(balance) {
        Ok(_) => panic!("Expected error"),
        Err(e) => {
            assert_eq!(e.person_uuid, person_uuid);
            assert_eq!(e.title, None);
            assert_eq!(e.name, None);
            assert_eq!(e.age, None);
            assert_eq!(e.balance, Some("Value must be 0 or greater.".to_string()));
        }
    }
}

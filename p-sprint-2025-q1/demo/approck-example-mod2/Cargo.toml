[package]
name = "approck-example-mod2"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[package.metadata.approck.mod]
extends = ["bux"]

[dependencies]
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }

serde = { workspace = true, features = ["derive"] }

[build-dependencies]
approck-compiler = { workspace = true }


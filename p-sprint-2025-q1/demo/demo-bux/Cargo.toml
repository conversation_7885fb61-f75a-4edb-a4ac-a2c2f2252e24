[package]
name = "demo-bux"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.approck.app]
port = 3902

extends = ["bux", "granite"]


[dependencies]
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-redis = { workspace = true }

clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
toml = { workspace = true }
# Gemini Workspace Configuration

This document outlines the project's structure, conventions, and core technologies to ensure consistent and efficient development.

## Core Principles

1. **Consistency is Key:** We adhere to established patterns and conventions.
2. **Minimize Boilerplate:** We value concise and expressive code.
3. **Convention over Configuration:** We favor sensible defaults and established patterns.
4. **Avoid Unnecessary Complexity:** We strive for simple and maintainable solutions.

## Project Overview

`acp7r` is a monorepo containing a compiler ecosystem and a runtime ecosystem for building web applications.

### Crate Types

- **Regular Crates:** Standard Rust crates.
- **Application Crates:** Defined by a `[package.metadata.acp]` section with an `app` key in `Cargo.toml`.
- **Module Crates:** Defined by a `[package.metadata.acp]` section with a `module` key in `Cargo.toml`.

### Naming Conventions

- **Rust:**
  - `snake_case` for variables and functions.
  - `CamelCase` for types.
  - `ALL_CAPS` for constants.
- **TypeScript:**
  - `snake_case` for variables and methods.
  - `CamelCase` for classes.
  - `ALL_CAPS` for `const` variables.

## Architecture

The project is built on a foundation of core frameworks and a modular application structure.

### Core Frameworks

- **`approck`**: The main application framework.
- **`bux`**: A user interface framework for building web applications.
- **`granite`**: A foundational library for utilities and error handling.

### Application Structure

Applications are composed of a core application crate and several module crates.

- Each application resides in its own subdirectory (e.g., `appcove/aplay`).
- The main application crate (e.g., `aplay`) contains the primary application logic and configuration.
- Sub-crates (e.g., `aplay-zero`, `aplay-admin`) provide specific features and are extended by the main application.
- A `{app}-zero` crate is used for shared types and utilities within an application family.

### Key Design Patterns

- **`Cargo.toml` Metadata:** The `[package.metadata.acp]` section in `Cargo.toml` is used to configure applications and modules, including application ports and module extensions.
- **Application Entry Point:** The `main.rs` file of an application should contain only the `approck::main!` macro.
- **Application `lib.rs`:** The `lib.rs` file defines the `AppConfig`, `AppStruct`, and `IdentityStruct` structs, and implements the `approck::App` and `approck::Identity` traits.
- **Module Implementation:** Modules are implemented in the `src/module` directory of an application.
- **Web Interface:** The web interface is defined in the `src/web` directory, with a `Document.rs` file for the main HTML structure and separate files for page handlers.

## Technology Stack

### Core Technologies

- **Rust (Edition 2024):** The primary backend language.
- **TypeScript:** Used for frontend code.
- **CSS:** For styling.
- **HTML:** Generated using the Maud templating system.

### Prohibited Technologies

- `bootstrap`
- `react`
- `anyhow`
- `error_stack`
- `this_error`

**Note:** Do not add new dependencies without approval.

### Build and Tooling

- **`./acp`**: The custom build system for the monorepo.
- **`cargo`**: Rust's package manager.
- **`esbuild`**: TypeScript/JavaScript bundler.
- **`rg` (ripgrep):** For code searching.
- **`fd`**: For finding files.

### Database and Caching

- **PostgreSQL:** The primary relational database.
- **Redis:** Used for caching and session management.

## Development Workflow

### Common Commands

- `./acp build`: Build the entire project.
- `./acp build -p <app>`: Build a specific application.
- `./acp check`: Check syntax and run static analysis.
- `./acp format`: Format the codebase.
- `./acp test`: Run all tests.
- `./acp test -p <crate>`: Run tests for a specific crate.
- `./acp workspace`: Display information about the workspace.

### Adding a New Application

To add a new application, follow the detailed steps outlined in the `task-app-create.md` document. The general process is as follows:

1. Create a new directory for the application.
2. Create the main application crate with a `Cargo.toml` file, `main.rs`, and `lib.rs`.
3. Implement the necessary modules in the `src/module` directory.
4. Implement the web interface in the `src/web` directory.
5. Add the new crate to the workspace `Cargo.toml`.
6. Build and verify the new application using `./acp build -p <app_name>` and `./acp check`.

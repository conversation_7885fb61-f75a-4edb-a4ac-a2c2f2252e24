# Technology Stack

## Core Technologies

### Programming Languages

- **Rust (Edition 2024)**: Primary backend language with latest async trait features
- **TypeScript**: Generated frontend code for type-safe client-side development
- **CSS**: Generated and compiled styles with optimization
- **HTML**: Generated via Maud templating system

### What NOT to use

- DO NOT use `bootstrap`
- DO NOT use `react`
- DO NOT use `anyhow`
- DO NOT use `error_stack`
- DO NOT use `this_error`
- DO NOT add new dependencies without approval

### Tool usage

- run `./acp build` and `./acp check` in that order

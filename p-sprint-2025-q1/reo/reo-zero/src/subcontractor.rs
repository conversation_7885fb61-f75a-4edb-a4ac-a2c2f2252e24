pub struct Subcontractor {
    pub name: String,
    pub position: String,
    pub total_paid: String,
    pub qre_percentage: Option<String>,
    pub job_description: String,
    pub status: String,
    pub has_proof: bool,
}

pub fn list() -> Vec<Subcontractor> {
    vec![
        Subcontractor {
            name: "<PERSON>".to_string(),
            position: "Freelancer".to_string(),
            total_paid: "$20,000".to_string(),
            qre_percentage: Some("85%".to_string()),
            job_description: "Website Development".to_string(),
            status: "Complete".to_string(),
            has_proof: true,
        },
        Subcontractor {
            name: "<PERSON>".to_string(),
            position: "Consultant".to_string(),
            total_paid: "$40,000".to_string(),
            qre_percentage: Some("90%".to_string()),
            job_description: "Financial Advisory".to_string(),
            status: "Complete".to_string(),
            has_proof: true,
        },
        Subcontractor {
            name: "<PERSON>".to_string(),
            position: "Developer".to_string(),
            total_paid: "$35,000".to_string(),
            qre_percentage: Some("80%".to_string()),
            job_description: "Software Development".to_string(),
            status: "In Progress".to_string(),
            has_proof: false,
        },
        Subcontractor {
            name: "Tech Solutions LLC".to_string(),
            position: "Contractor".to_string(),
            total_paid: "$75,000".to_string(),
            qre_percentage: Some("95%".to_string()),
            job_description: "System Integration".to_string(),
            status: "In Progress".to_string(),
            has_proof: true,
        },
        Subcontractor {
            name: "David Chen".to_string(),
            position: "Analyst".to_string(),
            total_paid: "$25,000".to_string(),
            qre_percentage: Some("75%".to_string()),
            job_description: "Data Analysis".to_string(),
            status: "Complete".to_string(),
            has_proof: true,
        },
        Subcontractor {
            name: "Innovation Labs Inc".to_string(),
            position: "Research Partner".to_string(),
            total_paid: "$60,000".to_string(),
            qre_percentage: Some("88%".to_string()),
            job_description: "R&D Research".to_string(),
            status: "In Progress".to_string(),
            has_proof: true,
        },
        Subcontractor {
            name: "Lisa Rodriguez".to_string(),
            position: "Designer".to_string(),
            total_paid: "$18,000".to_string(),
            qre_percentage: Some("70%".to_string()),
            job_description: "UI/UX Design".to_string(),
            status: "Complete".to_string(),
            has_proof: false,
        },
        Subcontractor {
            name: "Michael Thompson".to_string(),
            position: "Project Manager".to_string(),
            total_paid: "$45,000".to_string(),
            qre_percentage: Some("92%".to_string()),
            job_description: "Project Coordination".to_string(),
            status: "In Progress".to_string(),
            has_proof: true,
        },
    ]
}

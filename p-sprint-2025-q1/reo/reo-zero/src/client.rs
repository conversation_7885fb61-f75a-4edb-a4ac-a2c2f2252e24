pub struct Client {
    pub name: String,
    pub packets: String,
    pub status: String,
    pub associated_with: String,
}

pub fn list() -> Vec<Client> {
    vec![
        Client {
            name: "ABC Corp".to_string(),
            packets: "2023 R&D Tax Credit - Active".to_string(),
            status: "Active".to_string(),
            associated_with: "<PERSON><PERSON><PERSON>".to_string(),
        },
        Client {
            name: "Greenfield Enterprises".to_string(),
            packets: "2020 R&D Tax Credit - Inactive".to_string(),
            status: "Inactive".to_string(),
            associated_with: "Finnegan Wells".to_string(),
        },
        Client {
            name: "Green Valley Farms".to_string(),
            packets: "2023 R&D Tax Credit - Active".to_string(),
            status: "Active".to_string(),
            associated_with: "<PERSON><PERSON>".to_string(),
        },
        Client {
            name: "<PERSON>".to_string(),
            packets: "2022 R&D Tax Credit - Completed".to_string(),
            status: "Active".to_string(),
            associated_with: "<PERSON>asher".to_string(),
        },
        Client {
            name: "<PERSON>".to_string(),
            packets: "2022 R&D Tax Credit - Completed".to_string(),
            status: "Active".to_string(),
            associated_with: "Isolde <PERSON>".to_string(),
        },
        Client {
            name: "XYZ Industries".to_string(),
            packets: "2021 R&D Tax Credit - Late".to_string(),
            status: "Inactive".to_string(),
            associated_with: "Jasper Cruz".to_string(),
        },
        Client {
            name: "Sunrise Innovations".to_string(),
            packets: "2023 R&D Tax Credit - Active".to_string(),
            status: "Active".to_string(),
            associated_with: "Elara Vance".to_string(),
        },
        Client {
            name: "Oak Ridge Labs".to_string(),
            packets: "2021 R&D Tax Credit - Late".to_string(),
            status: "Inactive".to_string(),
            associated_with: "Theo Ramirez".to_string(),
        },
        Client {
            name: "Global Tech Solutions".to_string(),
            packets: "2024 R&D Tax Credit - Completed".to_string(),
            status: "Inactive".to_string(),
            associated_with: "Aurelia Harper".to_string(),
        },
        Client {
            name: "Innovative Designs Inc.".to_string(),
            packets: "2024 R&D Tax Credit - Completed".to_string(),
            status: "Inactive".to_string(),
            associated_with: "Finnegan Wells".to_string(),
        },
        Client {
            name: "Beacon Analytics".to_string(),
            packets: "2023 R&D Tax Credit - Active".to_string(),
            status: "Active".to_string(),
            associated_with: "Livia Bennett".to_string(),
        },
        Client {
            name: "NextWave Consulting".to_string(),
            packets: "2021 R&D Tax Credit - Late".to_string(),
            status: "Inactive".to_string(),
            associated_with: "Callum Frasher".to_string(),
        },
        Client {
            name: "Quantum Solutions LLC".to_string(),
            packets: "2022 R&D Tax Credit - Completed".to_string(),
            status: "Active".to_string(),
            associated_with: "Isolde Sinclair".to_string(),
        },
        Client {
            name: "NovaTech Manufacturing".to_string(),
            packets: "2024 R&D Tax Credit - Completed".to_string(),
            status: "Inactive".to_string(),
            associated_with: "Jasper Cruz".to_string(),
        },
        Client {
            name: "Bright Future Group".to_string(),
            packets: "2020 R&D Tax Credit - Inactive".to_string(),
            status: "Inactive".to_string(),
            associated_with: "Elara Vance".to_string(),
        },
        Client {
            name: "Summit Logistics".to_string(),
            packets: "2023 R&D Tax Credit - Active".to_string(),
            status: "Active".to_string(),
            associated_with: "Theo Ramirez".to_string(),
        },
        Client {
            name: "Lunar Labs".to_string(),
            packets: "2022 R&D Tax Credit - Completed".to_string(),
            status: "Active".to_string(),
            associated_with: "Aurelia Harper".to_string(),
        },
        Client {
            name: "Vista Dynamics".to_string(),
            packets: "2023 R&D Tax Credit - Active".to_string(),
            status: "Active".to_string(),
            associated_with: "Finnegan Wells".to_string(),
        },
        Client {
            name: "EchoWare Systems".to_string(),
            packets: "2021 R&D Tax Credit - Late".to_string(),
            status: "Inactive".to_string(),
            associated_with: "Livia Bennett".to_string(),
        },
        Client {
            name: "Phoenix Rising Inc.".to_string(),
            packets: "2020 R&D Tax Credit - Inactive".to_string(),
            status: "Inactive".to_string(),
            associated_with: "Callum Frasher".to_string(),
        },
    ]
}

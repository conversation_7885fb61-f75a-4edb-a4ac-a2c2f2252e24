pub struct Admin {
    pub packet_name: String,
    pub year: String,
    pub status: String,
}

pub fn list() -> Vec<Admin> {
    vec![
        Admin {
            packet_name: "2023 R&D Tax Credit".to_string(),
            year: "2023".to_string(),
            status: "Open".to_string(),
        },
        Admin {
            packet_name: "2024 R&D Tax Credit".to_string(),
            year: "2024".to_string(),
            status: "Open".to_string(),
        },
        Admin {
            packet_name: "2025 R&D Tax Credit".to_string(),
            year: "2025".to_string(),
            status: "Open".to_string(),
        },
    ]
}

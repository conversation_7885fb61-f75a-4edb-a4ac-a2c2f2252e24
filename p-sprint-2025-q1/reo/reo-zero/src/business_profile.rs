pub struct BusinessProfile {
    pub business_name: String,
    pub phone: String,
    pub address: String,
    pub login_email: String,
}

pub fn list() -> Vec<BusinessProfile> {
    vec![BusinessProfile {
        business_name: "ABC Corp".to_string(),
        phone: "******-567-890".to_string(),
        address: "123 Main St, Apt 4B, New York, NY, 10001".to_string(),
        login_email: "<EMAIL>".to_string(),
    }]
}

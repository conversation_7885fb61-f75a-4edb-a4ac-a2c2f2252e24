pub struct User {
    pub name: String,
    pub associated_with: String,
    pub status: String,
    pub email_address: String,
    pub address: String,
}

pub fn list() -> Vec<User> {
    vec![
        User {
            name: "<PERSON>".to_string(),
            associated_with: "Green Valley Farms (Company)".to_string(),
            status: "7/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "123 Harvest Road, Fresno, CA 93722".to_string(),
        },
        User {
            name: "<PERSON>".to_string(),
            associated_with: "<PERSON> (Individual)".to_string(),
            status: "7/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "458 Maple Street, Phoenix, AZ 85004".to_string(),
        },
        User {
            name: "<PERSON><PERSON> Monte".to_string(),
            associated_with: "ABC Corp (Company)".to_string(),
            status: "9/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "789 Commerce Blvd, Dallas, TX 75201".to_string(),
        },
        User {
            name: "<PERSON>".to_string(),
            associated_with: "XYZ Corp (Company)".to_string(),
            status: "9/9 items submitted - Complete".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "1010 Innovation Dr, San Jose, CA 95112".to_string(),
        },
        User {
            name: "Sophie Adams".to_string(),
            associated_with: "ABC Corp (Company)".to_string(),
            status: "7/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "241 Oak Lane, Denver, CO 80220".to_string(),
        },
        User {
            name: "Carlos Rivera".to_string(),
            associated_with: "Smart Biz Solutions (Company)".to_string(),
            status: "6/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "133 Elm Street, Austin, TX 78701".to_string(),
        },
        User {
            name: "Amanda Clark".to_string(),
            associated_with: "Bright Future LLC (Company)".to_string(),
            status: "9/9 items submitted - Complete".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "99 Sunset Blvd, Orlando, FL 32801".to_string(),
        },
        User {
            name: "Derek White".to_string(),
            associated_with: "Noble Ventures (Company)".to_string(),
            status: "5/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "876 River Rd, Portland, OR 97205".to_string(),
        },
        User {
            name: "Emily Harris".to_string(),
            associated_with: "Taylor Group (Company)".to_string(),
            status: "8/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "443 Mountain Ave, Salt Lake City, UT 84101".to_string(),
        },
        User {
            name: "Nathan Brooks".to_string(),
            associated_with: "Olivia Lee (Individual)".to_string(),
            status: "9/9 items submitted - Complete".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "1500 Ocean Drive, Miami Beach, FL 33139".to_string(),
        },
        User {
            name: "Rachel Turner".to_string(),
            associated_with: "Sunrise Solutions (Company)".to_string(),
            status: "7/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "2055 Sunrise Way, San Diego, CA 92101".to_string(),
        },
        User {
            name: "Brandon Lee".to_string(),
            associated_with: "Global Synergy (Company)".to_string(),
            status: "6/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "88 Skyline Blvd, Seattle, WA 98101".to_string(),
        },
        User {
            name: "Hailey Watson".to_string(),
            associated_with: "LunaTech (Company)".to_string(),
            status: "9/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "602 Tech Park, Palo Alto, CA 94301".to_string(),
        },
        User {
            name: "Jason Kim".to_string(),
            associated_with: "Family First Inc. (Company)".to_string(),
            status: "4/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "77 Maple Grove, Minneapolis, MN 55401".to_string(),
        },
        User {
            name: "Ava Murphy".to_string(),
            associated_with: "Archer & Co. (Company)".to_string(),
            status: "8/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "1600 Lakeview Dr, Chicago, IL 60614".to_string(),
        },
        User {
            name: "Isaac Reed".to_string(),
            associated_with: "Horizon Ltd. (Company)".to_string(),
            status: "9/9 items submitted - Complete".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "501 Pioneer Trail, Boise, ID 83702".to_string(),
        },
        User {
            name: "Natalie Cox".to_string(),
            associated_with: "Eco Green (Company)".to_string(),
            status: "7/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "444 Forest Hills Dr, Asheville, NC 28801".to_string(),
        },
        User {
            name: "Tyler Scott".to_string(),
            associated_with: "NextWave Technologies (Company)".to_string(),
            status: "6/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "333 Future Ln, Raleigh, NC 27601".to_string(),
        },
        User {
            name: "Grace Bennett".to_string(),
            associated_with: "Bennett Holdings (Company)".to_string(),
            status: "9/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "720 Heritage Blvd, Philadelphia, PA 19107".to_string(),
        },
        User {
            name: "Ethan Moore".to_string(),
            associated_with: "Moore & Partners (Company)".to_string(),
            status: "5/9 items submitted".to_string(),
            email_address: "<EMAIL>".to_string(),
            address: "100 Constitution Ave NW, Washington, DC 20001".to_string(),
        },
    ]
}

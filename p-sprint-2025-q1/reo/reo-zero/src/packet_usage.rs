pub struct PacketUsage {
    pub packet_name: String,
    pub assigned_to: String,
    pub status: String,
}

pub struct GroupedPacketUsage {
    pub packet_name: String,
    pub assigned_to_list: Vec<String>,
    pub status: String,
}

pub fn list() -> Vec<PacketUsage> {
    vec![
        PacketUsage {
            packet_name: "2023 R&D Tax Credit".to_string(),
            assigned_to: "<PERSON> Do<PERSON>".to_string(),
            status: "Active".to_string(),
        },
        PacketUsage {
            packet_name: "2023 R&D Tax Credit".to_string(),
            assigned_to: "<PERSON>".to_string(),
            status: "Active".to_string(),
        },
        PacketUsage {
            packet_name: "2023 R&D Tax Credit".to_string(),
            assigned_to: "<PERSON>".to_string(),
            status: "Active".to_string(),
        },
        PacketUsage {
            packet_name: "2023 R&D Tax Credit".to_string(),
            assigned_to: "ABC Corp".to_string(),
            status: "Active".to_string(),
        },
        PacketUsage {
            packet_name: "2024 R&D Tax Credit".to_string(),
            assigned_to: "<PERSON>".to_string(),
            status: "Active".to_string(),
        },
        PacketUsage {
            packet_name: "2024 R&D Tax Credit".to_string(),
            assigned_to: "<PERSON>".to_string(),
            status: "Active".to_string(),
        },
        PacketUsage {
            packet_name: "2024 R&D Tax Credit".to_string(),
            assigned_to: "Olivia Brown".to_string(),
            status: "Active".to_string(),
        },
        PacketUsage {
            packet_name: "2024 R&D Tax Credit".to_string(),
            assigned_to: "Green Valley Farms".to_string(),
            status: "Active".to_string(),
        },
        PacketUsage {
            packet_name: "2025 R&D Tax Credit".to_string(),
            assigned_to: "James Wilson".to_string(),
            status: "Active".to_string(),
        },
        PacketUsage {
            packet_name: "2025 R&D Tax Credit".to_string(),
            assigned_to: "Sarah Taylor".to_string(),
            status: "Active".to_string(),
        },
        PacketUsage {
            packet_name: "2025 R&D Tax Credit".to_string(),
            assigned_to: "Daniel Lee".to_string(),
            status: "Active".to_string(),
        },
        PacketUsage {
            packet_name: "2025 R&D Tax Credit".to_string(),
            assigned_to: "XYZ Industries".to_string(),
            status: "Active".to_string(),
        },
    ]
}

pub fn grouped_list() -> Vec<GroupedPacketUsage> {
    use std::collections::HashMap;

    let packet_usage = list();
    let mut grouped: HashMap<String, (Vec<String>, String)> = HashMap::new();

    // Group assignments by packet name
    for usage in packet_usage {
        let entry = grouped
            .entry(usage.packet_name.clone())
            .or_insert((Vec::new(), usage.status.clone()));
        entry.0.push(usage.assigned_to);
        // Keep the status from the first entry (they should all be the same for a packet)
    }

    // Convert HashMap to Vec<GroupedPacketUsage>
    let mut result: Vec<GroupedPacketUsage> = grouped
        .into_iter()
        .map(
            |(packet_name, (assigned_to_list, status))| GroupedPacketUsage {
                packet_name,
                assigned_to_list,
                status,
            },
        )
        .collect();

    // Sort by packet name for consistent ordering
    result.sort_by(|a, b| a.packet_name.cmp(&b.packet_name));

    result
}

pub mod accountinfo;
pub mod admin;
pub mod association;
pub mod business_profile;
pub mod client;
pub mod packet_usage;
pub mod packets;
pub mod subcontractor;
pub mod supplies;
pub mod user;
pub mod wages;
pub mod web;

pub trait App: approck::server::App {}

pub trait Identity: approck::Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}

pub trait Document: bux::document::Base {}

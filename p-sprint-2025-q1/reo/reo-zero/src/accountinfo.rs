pub struct User {
    pub first_name: String,
    pub last_name: String,
    pub phone: String,
    pub address: String,
    pub line_1: String,
    pub line_2: String,
    pub city: String,
    pub zip_code: String,
    pub country: String,
    pub login_email: String,
}

pub fn list() -> Vec<User> {
    vec![User {
        first_name: "<PERSON>".to_string(),
        last_name: "<PERSON><PERSON>".to_string(),
        phone: "******-567-890".to_string(),
        address: "123 Main St, Apt 4B".to_string(),
        line_1: "123 Main St".to_string(),
        line_2: "Apt 4B".to_string(),
        city: "New York".to_string(),
        zip_code: "10001".to_string(),
        country: "USA".to_string(),
        login_email: "<EMAIL>".to_string(),
    }]
}

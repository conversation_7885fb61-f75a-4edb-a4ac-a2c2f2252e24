pub struct Association {
    pub name: String,
    pub company: String,
    pub status: String,
}

pub fn list() -> Vec<Association> {
    vec![
        Association {
            name: "Aurelia Harper".to_string(),
            company: "HarperTech Solutions".to_string(),
            status: "Active".to_string(),
        },
        Association {
            name: "Finnegan Wells".to_string(),
            company: "Wells Enterprises".to_string(),
            status: "Inactive".to_string(),
        },
        Association {
            name: "<PERSON><PERSON>".to_string(),
            company: "Bennett Financial".to_string(),
            status: "Active".to_string(),
        },
        Association {
            name: "<PERSON>asher".to_string(),
            company: "Banco De Oro".to_string(),
            status: "Inactive".to_string(),
        },
        Association {
            name: "<PERSON><PERSON><PERSON>".to_string(),
            company: "Sinclair Consulting".to_string(),
            status: "Active".to_string(),
        },
        Association {
            name: "Noah <PERSON>gado".to_string(),
            company: "Delgado Group".to_string(),
            status: "Active".to_string(),
        },
        Association {
            name: "<PERSON>la Davidson".to_string(),
            company: "Davidson Realty".to_string(),
            status: "Inactive".to_string(),
        },
        Association {
            name: "<PERSON>".to_string(),
            company: "Blake Security Inc.".to_string(),
            status: "Active".to_string(),
        },
        Association {
            name: "Sienna Cruz".to_string(),
            company: "Cruz Construction".to_string(),
            status: "Inactive".to_string(),
        },
        Association {
            name: "Ezra Navarro".to_string(),
            company: "Navarro Textiles".to_string(),
            status: "Active".to_string(),
        },
        Association {
            name: "Vivienne Reed".to_string(),
            company: "Reed & Co.".to_string(),
            status: "Active".to_string(),
        },
        Association {
            name: "Jonas Beck".to_string(),
            company: "Beck Technologies".to_string(),
            status: "Inactive".to_string(),
        },
        Association {
            name: "Freya Moore".to_string(),
            company: "Moore Marketing Group".to_string(),
            status: "Active".to_string(),
        },
        Association {
            name: "Kai Turner".to_string(),
            company: "Turner Legal Services".to_string(),
            status: "Active".to_string(),
        },
        Association {
            name: "Zara Quinn".to_string(),
            company: "Quinn Imports".to_string(),
            status: "Inactive".to_string(),
        },
        Association {
            name: "Leo Santiago".to_string(),
            company: "Santiago & Partners".to_string(),
            status: "Active".to_string(),
        },
        Association {
            name: "Camila Flores".to_string(),
            company: "Flores Design Studio".to_string(),
            status: "Inactive".to_string(),
        },
        Association {
            name: "Jasper Cruz".to_string(),
            company: "Cruz & Sons Holdings".to_string(),
            status: "Active".to_string(),
        },
        Association {
            name: "Elara Vance".to_string(),
            company: "Vance Innovations".to_string(),
            status: "Active".to_string(),
        },
        Association {
            name: "Theo Ramirez".to_string(),
            company: "Ramirez Logistics".to_string(),
            status: "Inactive".to_string(),
        },
    ]
}

pub struct Supply {
    pub name: String,
    pub purpose: String,
    pub amount: String,
    pub status: String,
    pub has_proof: bool,
}

pub fn list() -> Vec<Supply> {
    vec![
        Supply {
            name: "Table and Chair".to_string(),
            purpose: "Office Supplies".to_string(),
            amount: "$60,000".to_string(),
            status: "Complete".to_string(),
            has_proof: true,
        },
        Supply {
            name: "Power Drill".to_string(),
            purpose: "Construction Supplies".to_string(),
            amount: "$40,000".to_string(),
            status: "Complete".to_string(),
            has_proof: true,
        },
        Supply {
            name: "Computer Equipment".to_string(),
            purpose: "Technology Supplies".to_string(),
            amount: "$25,000".to_string(),
            status: "In Progress".to_string(),
            has_proof: false,
        },
        Supply {
            name: "Laboratory Equipment".to_string(),
            purpose: "Research Supplies".to_string(),
            amount: "$80,000".to_string(),
            status: "In Progress".to_string(),
            has_proof: true,
        },
        Supply {
            name: "Software Licenses".to_string(),
            purpose: "Technology Supplies".to_string(),
            amount: "$15,000".to_string(),
            status: "Complete".to_string(),
            has_proof: true,
        },
        Supply {
            name: "Testing Materials".to_string(),
            purpose: "Research Supplies".to_string(),
            amount: "$30,000".to_string(),
            status: "In Progress".to_string(),
            has_proof: false,
        },
    ]
}

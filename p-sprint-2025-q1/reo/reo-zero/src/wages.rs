pub struct Wage {
    pub name: String,
    pub position: String,
    pub salary: String,
    pub qre_percentage: Option<String>,
    pub job_description: String,
    pub status: String,
    pub has_proof: bool,
}

pub fn list() -> Vec<Wage> {
    vec![
        Wage {
            name: "<PERSON>".to_string(),
            position: "Freelancer".to_string(),
            salary: "$20,000".to_string(),
            qre_percentage: Some("85%".to_string()),
            job_description: "Website Development".to_string(),
            status: "Complete".to_string(),
            has_proof: true,
        },
        Wage {
            name: "<PERSON>".to_string(),
            position: "Consultant".to_string(),
            salary: "$40,000".to_string(),
            qre_percentage: Some("90%".to_string()),
            job_description: "Financial Advisory".to_string(),
            status: "Complete".to_string(),
            has_proof: true,
        },
        Wage {
            name: "<PERSON>".to_string(),
            position: "Developer".to_string(),
            salary: "$35,000".to_string(),
            qre_percentage: Some("80%".to_string()),
            job_description: "Software Development".to_string(),
            status: "In Progress".to_string(),
            has_proof: false,
        },
        Wage {
            name: "<PERSON>".to_string(),
            position: "Analyst".to_string(),
            salary: "$25,000".to_string(),
            qre_percentage: Some("75%".to_string()),
            job_description: "Data Analysis".to_string(),
            status: "Complete".to_string(),
            has_proof: true,
        },
        Wage {
            name: "Lisa Rodriguez".to_string(),
            position: "Designer".to_string(),
            salary: "$18,000".to_string(),
            qre_percentage: Some("70%".to_string()),
            job_description: "UI/UX Design".to_string(),
            status: "In Progress".to_string(),
            has_proof: false,
        },
        Wage {
            name: "Michael Thompson".to_string(),
            position: "Project Manager".to_string(),
            salary: "$45,000".to_string(),
            qre_percentage: Some("92%".to_string()),
            job_description: "Project Coordination".to_string(),
            status: "In Progress".to_string(),
            has_proof: true,
        },
    ]
}

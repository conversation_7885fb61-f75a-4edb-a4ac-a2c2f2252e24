#[approck::http(GET /dashboard/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Dashboard");

        let mut entity_picker = bux::component::entity_picker::new();
        entity_picker.set_heading("Link to Navigation");
        entity_picker.append("🔗", "Client Dashboard", "/client/", "", "nav-001");

        entity_picker.append("🔗", "Admin Dashboard", "/admin/", "", "nav-002");

        entity_picker.append("🔗", "Packets", "/admin/packet-type/", "", "nav-003");

        entity_picker.append("🔗", "Users", "/admin/user/", "", "nav-004");

        entity_picker.append("🔗", "Clients", "/admin/client/", "", "nav-005");

        entity_picker.append("🔗", "My Account", "/myaccount/accountinfo/", "", "nav-006");

        doc.add_body(html!(
            panel {
                content {
                    (entity_picker)
                    p { i { "I don't know what comes next..." } }
                }
            }
        ));
        Response::HTML(doc.into())
    }
}

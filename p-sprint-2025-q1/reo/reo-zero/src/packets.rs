pub struct Packets {
    pub packets: String,
    pub status: String,
}

pub fn list() -> Vec<Packets> {
    vec![
        Packets {
            packets: "2025 R&D Tax Credit".to_string(),
            status: "Active".to_string(),
        },
        Packets {
            packets: "2023 R&D Tax Credit".to_string(),
            status: "Inactive".to_string(),
        },
        Packets {
            packets: "2022 R&D Tax Credit".to_string(),
            status: "Active".to_string(),
        },
        Packets {
            packets: "2024 R&D Tax Credit".to_string(),
            status: "Inactive".to_string(),
        },
        Packets {
            packets: "2028 R&D Tax Credit".to_string(),
            status: "Active".to_string(),
        },
        Packets {
            packets: "2020 R&D Tax Credit".to_string(),
            status: "Inactive".to_string(),
        },
        Packets {
            packets: "2021 R&D Tax Credit".to_string(),
            status: "Inactive".to_string(),
        },
        Packets {
            packets: "2026 R&D Tax Credit".to_string(),
            status: "Active".to_string(),
        },
        Packets {
            packets: "2027 R&D Tax Credit".to_string(),
            status: "Inactive".to_string(),
        },
        Packets {
            packets: "2029 R&D Tax Credit".to_string(),
            status: "Active".to_string(),
        },
        Packets {
            packets: "2025 Innovation Packet".to_string(),
            status: "Active".to_string(),
        },
        Packets {
            packets: "2023 Compliance Packet".to_string(),
            status: "Inactive".to_string(),
        },
        Packets {
            packets: "2022 Technical Dossier".to_string(),
            status: "Active".to_string(),
        },
        Packets {
            packets: "2021 Financial Report".to_string(),
            status: "Inactive".to_string(),
        },
        Packets {
            packets: "2024 Preliminary Filing".to_string(),
            status: "Active".to_string(),
        },
        Packets {
            packets: "2026 Grant Proposal".to_string(),
            status: "Inactive".to_string(),
        },
        Packets {
            packets: "2028 Development Packet".to_string(),
            status: "Active".to_string(),
        },
        Packets {
            packets: "2027 Engineering R&D File".to_string(),
            status: "Inactive".to_string(),
        },
        Packets {
            packets: "2030 R&D Tax Credit".to_string(),
            status: "Active".to_string(),
        },
        Packets {
            packets: "2029 R&D Documentation".to_string(),
            status: "Inactive".to_string(),
        },
    ]
}

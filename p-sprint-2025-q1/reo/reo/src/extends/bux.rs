use crate::IdentityStruct;

impl bux::Identity for IdentityStruct {
    fn role(&self) -> String {
        match self {
            IdentityStruct::User(_) => "user".to_string(),
            IdentityStruct::Anonymous => "anonymous".to_string(),
        }
    }
    fn name(&self) -> Option<String> {
        match self {
            IdentityStruct::User(user_info) => Some(user_info.name.to_string()),
            IdentityStruct::Anonymous => None,
        }
    }
    fn email(&self) -> Option<String> {
        match self {
            IdentityStruct::User(user_info) => user_info.email.clone(),
            IdentityStruct::Anonymous => None,
        }
    }
    fn avatar_uri(&self) -> Option<String> {
        match self {
            IdentityStruct::User(user_info) => user_info.avatar_uri.clone(),
            IdentityStruct::Anonymous => None,
        }
    }
}

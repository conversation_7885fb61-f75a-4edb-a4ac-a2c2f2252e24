use crate::{AppStruct, IdentityStruct};

impl auth_fence::Identity for IdentityStruct {
    fn is_logged_in(&self) -> bool {
        match self {
            IdentityStruct::Anonymous => false,
            IdentityStruct::User(_) => true,
        }
    }
    fn identity_uuid(&self) -> Option<granite::Uuid> {
        match self {
            IdentityStruct::Anonymous => None,
            IdentityStruct::User(user_info) => Some(user_info.identity_uuid),
        }
    }
    fn remote_address(&self) -> std::net::IpAddr {
        todo!()
    }
    fn session_token(&self) -> String {
        todo!()
    }
}

impl auth_fence::App for AppStruct {
    fn after_login_next_url<'a>(&self, next_uri: Option<&'a str>) -> &'a str {
        match next_uri {
            Some(next_uri) => next_uri,
            None => "/dashboard/",
        }
    }
    fn auth_fence_system(&self) -> &auth_fence::types::ModuleStruct {
        &self.auth_fence
    }
}

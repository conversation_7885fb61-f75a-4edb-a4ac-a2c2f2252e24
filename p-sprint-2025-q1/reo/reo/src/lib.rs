#[path = "libλ.rs"]
pub mod libλ;

mod extends;
mod web;

///////////////////////////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////////////////////////

#[derive(serde::Deserialize)]
pub struct AppConfig {
    pub redis: approck_redis::ModuleConfig,
    pub postgres: approck_postgres::ModuleConfig,
    pub webserver: approck::server::ModuleConfig,
    pub auth_fence: auth_fence::types::ModuleConfig,
}

pub struct AppStruct {
    pub webserver: approck::server::Module,
    pub postgres: approck_postgres::ModuleStruct,
    pub redis: approck_redis::ModuleStruct,
    pub auth_fence: auth_fence::types::ModuleStruct,
}

#[derive(Debug)]
pub enum IdentityStruct {
    Anonymous,
    User(auth_fence::api::identity::Identity),
}

pub use crate::web::Document::Document as DocumentStruct;

///////////////////////////////////////////////////////////////////////////////////////////////////

impl approck::App for AppStruct {
    type Config = AppConfig;
    type Identity = IdentityStruct;

    fn new(config: Self::Config) -> granite::Result<Self> {
        use approck::Module;
        Ok(Self {
            webserver: approck::server::Module::new(config.webserver)?,
            postgres: approck_postgres::ModuleStruct::new(config.postgres)?,
            redis: approck_redis::ModuleStruct::new(config.redis)?,
            auth_fence: auth_fence::types::ModuleStruct::new(config.auth_fence)?,
        })
    }

    async fn init(&self) -> granite::Result<()> {
        approck::Module::init(&self.redis).await?;
        approck::Module::init(&self.postgres).await?;
        approck::Module::init(&self.webserver).await?;
        approck::Module::init(&self.auth_fence).await?;

        // get the crate name using the env! macro
        let crate_name = env!("CARGO_PKG_NAME");
        println!("init: {crate_name}");
        Ok(())
    }

    async fn auth(&self, req: &approck::server::Request) -> granite::Result<IdentityStruct> {
        use auth_fence::App;
        let auth_fence = self.auth_fence_system();

        let mut redis = match self.redis.get_dbcx().await {
            Ok(redis) => redis,
            Err(e) => {
                return Err(granite::process_error!(
                    "Error getting Redis connection: {}",
                    e
                ));
            }
        };

        match auth_fence
            .get_user_identity(&req.session_token(), &mut redis)
            .await
        {
            Ok(user_info) => match user_info {
                Some(user_info) => Ok(IdentityStruct::User(user_info)),
                None => Ok(IdentityStruct::Anonymous),
            },
            Err(_e) => Ok(IdentityStruct::Anonymous),
        }
    }
}

impl approck::Identity for IdentityStruct {}

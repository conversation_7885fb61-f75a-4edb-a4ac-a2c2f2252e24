bux::document! {

    pub struct Document {}

    impl Document {
        pub fn new(
            app: &'static crate::AppStruct,
            identity: &crate::IdentityStruct,
            req: &approck::server::Request,
        ) -> Self {
            // needed for default login_url() and logout_url()
            use auth_fence::App;

            // traits must be in scope trait methods to be available, ex. set_identity(), nav2_menu_add(), add_logo_url()
            use bux::document::{Base, Nav2, Nav2Dropdown, Nav2Logo, Nav2Icon}; // Removed nav1 after eliminating this.add_nav1_menu_link from the left menu

            println!("Request {:?}", req.path());
            let mut this = Self {
                ..Default::default()
            };

            // Base setup
            this.set_title("REO"); // default title
            this.set_site_name("REO");




            this.add_logo("/dashboard/", "https://asset7.net/REO/reo-logo.svg");


            // Nav2 setup
            this.set_identity(identity);

            this.add_nav2_menu_link("FAQ", "/faq", "");

            match auth_fence::Identity::is_logged_in(identity) {
                true => {

                    if let Some(name) = bux::Identity::name(identity) {
                        let user_menu: &mut Nav2Dropdown;
                        if let Some(avatar_uri) = bux::Identity::avatar_uri(identity) {
                            let user_logo = Nav2Logo{ url: avatar_uri, alt: None };
                            user_menu = this.add_nav2_menu_dropdown(&name, "user-menu", Some(user_logo), None);
                        } else {
                            user_menu = this.add_nav2_menu_dropdown(&name, "user-menu", None, None);
                        }
                        user_menu.add_link("Client Dashboard", "/client/", None);
                        user_menu.add_link("My Account", "/myaccount/accountinfo/", None);
                        user_menu.add_span(format!("Logged in as {name}").as_str());
                        // TODO: add icon i.fas.fa-sign-out-alt
                        user_menu.add_link("Logout", app.logout_url(), None);

                        let admin_icon = Nav2Icon { class: "fas fa-cog".into(), id: Some("gear-icon".into()) };
                        let admin_menu = this.add_nav2_menu_dropdown("", "admin-tools", None, Some(admin_icon));
                        admin_menu.add_link("Admin Dashboard", "/admin/", None);
                        admin_menu.add_link("Staff Dashboard", "/staff/", None);
                        admin_menu.add_link("Packets", "/admin/packet-type/", None);
                        admin_menu.add_link("Users", "/admin/user/", None);
                        admin_menu.add_link("Clients", "/admin/client/", None);
                    };
                }
                false => {
                    this.add_nav2_menu_link("Login", app.login_url(), "");
                }
            }

            this
        }
    }

    impl bux::document::Base for Document {
        fn render_body(&self) -> maud::Markup {
            bux::document::Cliffy::render_body(self)
        }
    }
    impl bux::document::Nav1 for Document {}
    impl bux::document::Nav2 for Document {}
    impl bux::document::FooterSocial for Document {}
    impl bux::document::FooterLinks for Document {}
    impl bux::document::PageNav for Document {}
    impl bux::document::Cliffy for Document {}
    impl auth_fence::Document for Document {}
    impl auth_fence::DocumentPublic for Document {}

    impl reo_zero::Document for Document {}
    impl reo_admin::Document for Document {}
    impl reo_client::Document for Document {}
    impl reo_packet::Document for Document {}
    impl reo_public::Document for Document {}
    impl reo_staff::Document for Document {}
    impl reo_user::Document for Document {}
}

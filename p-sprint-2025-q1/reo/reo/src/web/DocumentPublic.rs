use maud::html;

bux::document! {

    pub struct DocumentPublic {}

    impl DocumentPublic {
        pub fn new(
            _app: &'static crate::AppSystem,
            _identity: &crate::Identity,
            _req: &approck::server::Request,
        ) -> Self {
            // trait Nav2 must be in scope for set_identity() and nav2_menu_add()
            use bux::document::Base;

            let mut this = Self {
                ..Default::default()
            };

            this.set_title("REO"); // default title

            // Base setup
            this
        }
    }

    impl bux::document::Base for DocumentPublic {
        fn render_body(&self) -> maud::Markup {
            use bux::document::{Base, Nav1, Nav2, FooterLinks};
            use maud::{html, PreEscaped};
            html!(
                layout-wrapper-outer {
                    layout-wrapper-inner {
                        header-bar.disabled id="header-bar" {}
                        nav-wrapper {
                            content-container {
                                nav-header id="horizontal-nav-header" {
                                    a href="/client/" {
                                        img src="https://i.imgur.com/8zNssOI.png" alt="REO Logo" class="img-fluid" style="width: 100px;";
                                    }
                                }
                                (Nav1::render_nav1(self))
                                (Nav2::render(self))
                            }
                        }
                        content-container {
                            (Base::render_body_inner(self))
                        }
                    }
                    footer {
                        (FooterLinks::render(self))
                        p id="footer-copyright" {
                            small {
                                (PreEscaped("&copy;"))
                                " REO 2024"
                            }
                        }
                    }
                }
            )
        }
    }
    impl bux::document::Nav1 for DocumentPublic {}
    impl bux::document::Nav2 for DocumentPublic {
        fn render(&self) -> maud::Markup {
            html! {
                nav aria-label="Secondary Navigation" id="secondary-navigation" {
                    ul.nav-menu {
                        li.menu-item-dropdown id="signin" {
                            a.menu-link href="/auth/" { "Sign in" }
                        }
                    }
                }
            }
        }
    }
    impl reo_public::DocumentPublic for DocumentPublic {}
    impl bux::document::FooterLinks for DocumentPublic {}


}

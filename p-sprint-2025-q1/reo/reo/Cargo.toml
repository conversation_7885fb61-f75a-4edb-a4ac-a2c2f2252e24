[package]
name = "reo"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.approck.app]
port = 3009
docmap.auth-fence.DocumentPublic = "Document"
extends = ["reo-zero", "reo-admin", "reo-client", "reo-packet", "reo-public", "reo-staff", "reo-user", "approck", "bux", "granite", "auth-fence", "appstruct"]


[dependencies]
appstruct = { workspace = true }
reo-zero = { path = "../reo-zero" }
reo-admin = { path = "../reo-admin" }
reo-client = { path = "../reo-client" }
reo-packet = { path = "../reo-packet" }
reo-public = { path = "../reo-public" }
reo-staff = { path = "../reo-staff" }
reo-user = { path = "../reo-user" }
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-redis = { workspace = true }
approck-postgres = { workspace = true }
auth-fence = { workspace = true }


clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
toml = { workspace = true }
chrono = { workspace = true, features = ["serde"] }

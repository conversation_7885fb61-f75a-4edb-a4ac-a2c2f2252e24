#[approck::http(GET /about/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("About tmtru");

        doc.add_body(html!(
            div.container {
                h1 { "About tmtru" }
                p { "This is a simple tmtru application. It is a work in progress." }
                br;
                p { "The goal is to do tmtru things in a tmtru way." }
            }
        ));

        Response::HTML(doc.into())
    }
}

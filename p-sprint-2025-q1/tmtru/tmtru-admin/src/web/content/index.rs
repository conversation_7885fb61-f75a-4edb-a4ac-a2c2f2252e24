#[approck::http(GET /content/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Content Curator Dashboard");

        doc.add_body(html!(
            main {
                h1 { "Content Management" }
                p.mb-4 { "Marketing content tracks, AI summaries, topic bucketing, QUE distribution." }

                grid-2 {
                    table-wrapper.data-list {
                        h4 { "Track Builder" }
                        table {
                            tr {
                                td { "Topic" }
                                td { label-tag.primary { "Digital Marketing Growth" } }
                            }
                            tr {
                                td { "Modules" }
                                td { label-tag.success { "8" } }
                            }
                            tr {
                                td { "Target Audience" }
                                td { "Technology Companies" }
                            }
                        }
                    }

                    table-wrapper.data-list {
                        h4 { "Auto Shorts" }
                        table {
                            tr {
                                td { "Summaries ready" }
                                td { label-tag.success { "5" } }
                            }
                            tr {
                                td { "Generate" }
                                td { i.fas.fa-magic {} }
                            }
                        }
                    }

                    table-wrapper.data-list {
                        h4 { "Bucket by Topic" }
                        table {
                            tr {
                                td { "Marketing Strategy" }
                                td { i.fas.fa-bullhorn {} }
                            }
                            tr {
                                td { "Sales Growth" }
                                td { i.fas.fa-chart-line {} }
                            }
                            tr {
                                td { "Customer Success" }
                                td { i.fas.fa-users {} }
                            }
                            tr {
                                td { "Technology Trends" }
                                td { i.fas.fa-microchip {} }
                            }
                        }
                    }

                    table-wrapper.data-list {
                        h4 { "Push to QUE" }
                        table {
                            tr {
                                td { "Target Audience" }
                                td { label-tag.warning { "Technology Leaders" } }
                            }
                            tr {
                                td { "Schedule Distribution" }
                                td { i.fas.fa-calendar {} }
                            }
                            tr {
                                td { "Personalization" }
                                td { "Based on Smart Numbers" }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

#[approck::http(GET /dashboard/; AUTH None; return HTML|Redirect;)]
pub mod page {
    pub async fn request(_app: App, _identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("Dashboard");

        // Hardcoded roles for TuePeer MSP Platform
        let mut entity_picker = bux::component::entity_picker::new();
        entity_picker.set_heading("Please select a role to continue.");

        entity_picker.append(
            "📈", // chart-line
            "Executive",
            "/executive/",
            "Executive Dashboard",
            "exec-001",
        );

        entity_picker.append(
            "⚙️", // cog
            "Administrator",
            "/admin/",
            "System Administration",
            "admin-001",
        );

        entity_picker.append(
            "👥", // users
            "Customer Success",
            "/csm/",
            "Customer Success Management",
            "csm-001",
        );

        entity_picker.append(
            "🎧", // headset
            "Support Team",
            "/support/",
            "Customer Support",
            "support-001",
        );

        entity_picker.append(
            "✍️", // edit
            "Content Management",
            "/content/",
            "Content & Documentation",
            "content-001",
        );

        entity_picker.append(
            "👤", // user
            "Member",
            "/member/",
            "Team Member Portal",
            "member-001",
        );

        doc.add_body(html!(
            h1 { "Welcome to tmtru" }
            bux-action-panel {
                panel {
                    content {
                        (entity_picker)
                    }
                }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

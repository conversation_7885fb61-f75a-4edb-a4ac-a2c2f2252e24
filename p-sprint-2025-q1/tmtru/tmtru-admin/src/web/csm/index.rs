#[approck::http(GET /csm/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("CSM Dashboard");
        doc.set_body_display_fluid();

        doc.add_body(html!(
            main {
                h1 { "Customer Success (CSM)" }

                grid-2 {

                    div {
                        h3 { "Sync Manager with Keap" }
                        img src="https://asset7.net/tmt-2025-demo/csm_sync_manager_with_keap.png" alt="CSM Sync Manager with Keap";
                    }

                    div {
                        h3 { "Manager List" }
                        img src="https://asset7.net/tmt-2025-demo/csm_manager-list.png" alt="CSM Manager List";
                    }

                    div {
                        h3 { "Manager Details" }
                        img src="https://asset7.net/tmt-2025-demo/csm_manager-details.png" alt="CSM Manager Details";
                    }

                    div {
                        h3 { "Add Manager" }
                        img src="https://asset7.net/tmt-2025-demo/csm_add_manager.png" alt="CSM Add Manager";
                    }

                    div {
                        h3 { "Edit Manager" }
                        img src="https://asset7.net/tmt-2025-demo/csm_edit_manager.png" alt="CSM Edit Manager";
                    }

                }
            }
        ));

        Response::HTML(doc.into())
    }
}

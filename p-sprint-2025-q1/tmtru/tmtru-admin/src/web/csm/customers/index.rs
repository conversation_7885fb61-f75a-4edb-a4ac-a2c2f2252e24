#[approck::http(GET /csm/customers/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Customer Management");

        doc.add_body(html!(
            main {
                h1 { "Customer Management" }
                p { "Welcome to the Customer Management section. Use the navigation menu to access different customer-related features." }

                div.feature-grid {
                    div.feature-card {
                        h3 { "Products" }
                        p { "Manage customer products and sales blueprints." }
                        a href="/csm/customers/product/sales-blueprint" { "View Products" }
                    }

                    div.feature-card {
                        h3 { "Search" }
                        p { "AI-powered search functionality for customer data." }
                        a href="/csm/customers/search/" { "Access Search" }
                    }

                    div.feature-card {
                        h3 { "Training" }
                        p { "Customer training materials and resources." }
                        a href="/csm/customers/training/" { "View Training" }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

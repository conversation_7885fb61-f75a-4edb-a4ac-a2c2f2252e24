#[approck::http(GET /csm/customers/training/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Training");
        doc.set_body_display_fluid();

        doc.add_body(html!(
            main {
                h1 { "Training" }

                grid-2 {
                    div {
                        h3 { "Training Page" }
                        img src="https://asset7.net/tmt-2025-demo/training_page.png" alt="Training Page";
                    }

                    div {
                        h3 { "Training Admin Details" }
                        img src="https://asset7.net/tmt-2025-demo/training_admin-details.png" alt="Training Admin Details";
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

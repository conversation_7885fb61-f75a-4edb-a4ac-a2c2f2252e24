#[approck::http(GET /csm/customers/search/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Search");

        doc.add_body(html!(
            main {
                h1 { "Search" }

                div {
                    h3 { "AI Search Interface" }
                    img src="https://asset7.net/tmt-2025-demo/ai_search.png" alt="AI Search Interface";
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

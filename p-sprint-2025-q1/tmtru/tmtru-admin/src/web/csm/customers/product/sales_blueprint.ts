import "./sales_blueprint.mcss";

// Sales Blueprint functionality
addEventListener("DOMContentLoaded", () => {
    console.log("Sales Blueprint page loaded");

    // Handle session navigation
    const sessionLinks = document.querySelectorAll("x-nav.sidebar ul.session-list li a");
    sessionLinks.forEach((link) => {
        link.addEventListener("click", (e) => {
            e.preventDefault();
            console.log("Session clicked:", link.textContent);
        });
    });
});

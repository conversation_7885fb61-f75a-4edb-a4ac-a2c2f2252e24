/* Sales Blueprint - Modern Layout */
main.sales-blueprint {
    background: #f7f7f7;
    min-height: 100vh;

    x-product {
        display: flex;
        margin: 0 auto;
        min-height: 100vh;
        gap: 0;

        x-nav.sidebar {
            width: 280px;
            background: #f0f0f0;
            padding: 1.5rem 1rem;
            border-right: 1px solid #ddd;

            h3 {
                margin: 0 0 1.5rem 0;
                color: #333;
                font-size: 1.1rem;
            }

            ul.session-list {
                list-style: none;
                padding: 0;
                margin: 0;

                li {
                    margin-bottom: 0.8rem;

                    a {
                        display: block;
                        text-decoration: none;
                        color: #0077cc;
                        padding: 0.75rem;
                        border-radius: 6px;
                        line-height: 1.4;
                        transition: all 0.2s ease;

                        &:hover {
                            background: #e2e2e2;
                        }
                    }

                    small {
                        display: block;
                        color: #666;
                        font-size: 0.85rem;
                        margin-top: 0.25rem;
                    }

                    &.active a {
                        font-weight: 600;
                        background: #fff;
                        border-left: 4px solid #0077cc;
                        color: #000;
                    }

                    &.upcoming {
                        span.disabled {
                            display: block;
                            color: #999;
                            padding: 0.75rem;
                            text-decoration: line-through;
                        }
                    }
                }
            }
        }

        content.main-content {
            flex: 1;
            padding: 2rem;
            background: #fff;

            section.video-section {
                header.session-header {
                    margin-bottom: 2rem;

                    h1 {
                        margin: 0 0 1rem 0;
                        color: #333;
                        font-size: 2rem;
                    }

                    p.session-meta {
                        display: flex;
                        gap: 2rem;
                        margin: 0;

                        span {
                            display: flex;
                            align-items: center;
                            gap: 0.5rem;
                            color: #666;
                            font-size: 0.9rem;

                            i {
                                color: #0077cc;
                            }
                        }

                        span.status {
                            color: #28a745;

                            i {
                                color: #28a745;
                            }
                        }
                    }
                }

                video-container {
                    display: block;
                    margin-bottom: 2rem;

                    video-placeholder {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        height: 360px;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        border-radius: 8px;
                        color: white;
                        text-align: center;
                        margin-bottom: 1rem;

                        i.fas.fa-play-circle {
                            font-size: 4rem;
                            margin-bottom: 1rem;
                            opacity: 0.9;
                        }

                        h3 {
                            margin: 0 0 0.5rem 0;
                            font-size: 1.5rem;
                        }

                        p {
                            margin: 0;
                            opacity: 0.9;
                        }
                    }

                    controls.video-controls {
                        display: flex;
                        align-items: center;
                        gap: 1rem;

                        button.play-btn {
                            background: #0077cc;
                            color: white;
                            border: none;
                            padding: 0.75rem 1.5rem;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: 500;
                            transition: background 0.2s ease;

                            &:hover {
                                background: #005fa3;
                            }

                            i {
                                margin-right: 0.5rem;
                            }
                        }

                        span.video-info {
                            color: #666;
                            font-size: 0.9rem;
                        }
                    }
                }

                description.session-description {
                    display: block;

                    h3 {
                        margin: 0 0 1rem 0;
                        color: #333;
                    }

                    ul.learning-objectives {
                        list-style: none;
                        padding: 0;
                        margin: 0;

                        li {
                            display: flex;
                            align-items: flex-start;
                            gap: 0.75rem;
                            margin-bottom: 0.75rem;
                            line-height: 1.5;

                            i.fas.fa-check {
                                color: #28a745;
                                margin-top: 0.25rem;
                                flex-shrink: 0;
                            }
                        }
                    }
                }
            }
        }

        aside.resources-sidebar {
            width: 300px;
            background: #fafafa;
            padding: 2rem 1.5rem;
            border-left: 1px solid #ddd;

            h3 {
                margin: 0 0 1.5rem 0;
                color: #333;
                font-size: 1.1rem;
            }

            .resource-section {
                margin-bottom: 2rem;

                h4 {
                    margin: 0 0 1rem 0;
                    color: #333;
                    font-size: 1rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;

                    i {
                        color: #0077cc;
                    }
                }

                ul.file-list {
                    list-style: none;
                    padding: 0;
                    margin: 0;

                    li.file-item {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 0.75rem 0;
                        border-bottom: 1px solid #e2e2e2;

                        file-info {
                            display: flex;
                            align-items: center;
                            gap: 0.75rem;

                            i {
                                color: #0077cc;
                                width: 16px;
                            }

                            span.file-name {
                                font-weight: 500;
                                color: #333;
                            }

                            span.file-type {
                                background: #e9ecef;
                                padding: 0.2rem 0.4rem;
                                border-radius: 3px;
                                font-size: 0.75rem;
                                color: #666;
                            }
                        }

                        span.file-size {
                            color: #666;
                            font-size: 0.85rem;
                        }
                    }
                }

                ul.tool-list {
                    list-style: none;
                    padding: 0;
                    margin: 0;

                    li {
                        margin-bottom: 1rem;

                        a {
                            color: #0077cc;
                            text-decoration: none;
                            font-weight: 500;

                            &:hover {
                                text-decoration: underline;
                            }
                        }

                        small {
                            display: block;
                            color: #666;
                            margin-top: 0.25rem;
                            font-size: 0.85rem;
                        }
                    }
                }

                progress-stats {
                    display: block;

                    stat {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 0.75rem;

                        span.label {
                            color: #666;
                        }

                        span.value {
                            font-weight: 600;
                            color: #333;
                        }
                    }
                }

                progress-bar {
                    display: block;
                    background: #e9ecef;
                    height: 8px;
                    border-radius: 4px;
                    margin-top: 1rem;

                    progress-fill {
                        display: block;
                        background: #28a745;
                        height: 100%;
                        border-radius: 4px;
                        transition: width 0.3s ease;
                    }
                }
            }
        }
    }

    /* Responsive Design */
    @media screen and (max-width: 1024px) {
        container {
            flex-direction: column;

            x-nav.sidebar,
            aside.resources-sidebar {
                width: 100%;
                border: none;
                border-bottom: 1px solid #ddd;
            }

            content.main-content {
                padding: 1rem;
            }

            aside.resources-sidebar {
                border-top: 1px solid #ddd;
                border-bottom: none;
            }
        }
    }
}

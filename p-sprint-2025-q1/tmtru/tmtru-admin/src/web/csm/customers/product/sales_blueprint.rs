#[approck::http(GET /csm/customers/product/sales-blueprint; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Sales Blueprint - Technology Marketing Toolkit");
        doc.set_body_display_fluid();

        doc.add_body(html!(
            main.sales-blueprint {
                x-product {
                    x-nav.sidebar {
                        h3 { "Sales Blueprint Sessions" }
                        ul.session-list {
                            li {
                                a href="#welcome" { "Welcome & Overview" }
                                small { "Getting Started Guide" }
                            }
                            li.active {
                                a href="#session1" { "Session 1: Foundation Building" }
                                small { "Presenter: <PERSON> Robins" }
                            }
                            li {
                                a href="#session2" { "Session 2: Overcoming Objections" }
                                small { "Presenter: Robin Robins" }
                            }
                            li {
                                a href="#session3" { "Session 3: Qualification Process" }
                                small { "Presenter: <PERSON> Robins" }
                            }
                            li {
                                a href="#session4" { "Session 4: Discovery Meeting" }
                                small { "Presenter: <PERSON> Robins" }
                            }
                            li {
                                a href="#session5" { "Session 5: Technical Assessment" }
                                small { "Presenter: <PERSON> Robins" }
                            }
                            li {
                                a href="#session6" { "Session 6: Prescribe & Close" }
                                small { "Presenter: Robin Robin<PERSON>" }
                            }
                            li.upcoming {
                                span.disabled { "Session 7: Follow-Up Strategies" }
                                small { "Available: August 13th, 1:00 PM CT" }
                            }
                        }
                    }

                    content.main-content {
                        section.video-section {
                            header.session-header {
                                h1 { "Session 1: Foundation Building" }
                                p.session-meta {
                                    span.duration { i.fas.fa-clock {} " 45 minutes" }
                                    span.presenter { i.fas.fa-user {} " Robin Robins" }
                                    span.status { i.fas.fa-check-circle {} " Completed" }
                                }
                            }

                            video-container {
                                video-placeholder {
                                    i.fas.fa-play-circle {}
                                    h3 { "Sales Foundation Masterclass" }
                                    p { "Learn the fundamental principles of MSP sales success" }
                                }
                                controls.video-controls {
                                    button.play-btn {
                                        i.fas.fa-play {}
                                        " Play Video"
                                    }
                                    span.video-info { "Foundation_Building_Session.mp4" }
                                }
                            }

                            description.session-description {
                                h3 { "What You'll Learn" }
                                ul.learning-objectives {
                                    li { i.fas.fa-check {} " Building trust with prospects from the first interaction" }
                                    li { i.fas.fa-check {} " Positioning yourself as the technology expert" }
                                    li { i.fas.fa-check {} " Creating urgency without being pushy" }
                                    li { i.fas.fa-check {} " Establishing your unique value proposition" }
                                }
                            }
                        }
                    }

                    aside.resources-sidebar {
                        h3 { "Session Resources" }

                        downloads.resource-section {
                            h4 { i.fas.fa-download {} " Downloads" }
                            ul.file-list {
                                li.file-item {
                                    file-info {
                                        i.fas.fa-file-pdf {}
                                        span.file-name { "Sales Foundation Slides" }
                                        span.file-type { "PDF" }
                                    }
                                    span.file-size { "2.1 MB" }
                                }
                                li.file-item {
                                    file-info {
                                        i.fas.fa-file-word {}
                                        span.file-name { "Prospect Qualification Checklist" }
                                        span.file-type { "DOCX" }
                                    }
                                    span.file-size { "156 KB" }
                                }
                                li.file-item {
                                    file-info {
                                        i.fas.fa-file-excel {}
                                        span.file-name { "Sales Pipeline Tracker" }
                                        span.file-type { "XLSX" }
                                    }
                                    span.file-size { "89 KB" }
                                }
                            }
                        }

                        tools.resource-section {
                            h4 { i.fas.fa-tools {} " Tools & Templates" }
                            ul.tool-list {
                                li {
                                    a href="#" { "Email Templates Library" }
                                    small { "Pre-written follow-up sequences" }
                                }
                                li {
                                    a href="#" { "Objection Handling Scripts" }
                                    small { "Proven responses to common objections" }
                                }
                                li {
                                    a href="#" { "ROI Calculator" }
                                    small { "Interactive tool for prospects" }
                                }
                            }
                        }

                        progress.resource-section {
                            h4 { i.fas.fa-chart-line {} " Your Progress" }
                            progress-stats {
                                stat {
                                    span.label { "Sessions Completed" }
                                    span.value { "1 of 7" }
                                }
                                stat {
                                    span.label { "Total Watch Time" }
                                    span.value { "45 minutes" }
                                }
                                stat {
                                    span.label { "Downloads" }
                                    span.value { "3 files" }
                                }
                            }
                            progress-bar {
                                progress-fill style="width: 14.3%" {}
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

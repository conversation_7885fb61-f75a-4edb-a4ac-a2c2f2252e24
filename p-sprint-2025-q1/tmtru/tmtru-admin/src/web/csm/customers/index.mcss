/* Customer Management Index - minimal styling */
main {
    padding: 1rem;

    h1 {
        margin-bottom: 1rem;
        color: #333;
    }

    p {
        margin-bottom: 2rem;
        color: #666;
    }

    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;

        .feature-card {
            background: #f9f9f9;
            padding: 1.5rem;
            border-radius: 8px;
            border: 1px solid #ddd;

            h3 {
                margin-bottom: 0.5rem;
                color: #333;
            }

            p {
                margin-bottom: 1rem;
                color: #666;
                font-size: 0.9rem;
            }

            a {
                color: #0d6efd;
                text-decoration: none;
                font-weight: 500;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
}

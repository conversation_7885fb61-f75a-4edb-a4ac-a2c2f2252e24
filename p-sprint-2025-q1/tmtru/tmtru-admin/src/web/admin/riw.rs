#[approck::http(GET /admin/riw/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("RIW - Rapid Implementation Workshop");
        doc.set_body_display_fluid();

        doc.add_body(html!(
            main {
                h1 { "RIW - Rapid Implementation Workshop" }

                grid-2 {
                    div {
                        h3 { "Home Page" }
                        img src="https://asset7.net/tmt-2025-demo/riw_home_page.png" alt="RIW Home Page";
                    }

                    div {
                        h3 { "Admin Overview" }
                        img src="https://asset7.net/tmt-2025-demo/riw_admin_overview.png" alt="RIW Admin Overview";
                    }

                    div {
                        h3 { "Admin Overview Checklist Report" }
                        img src="https://asset7.net/tmt-2025-demo/riw_admin_overview_checklist-report.png" alt="RIW Admin Overview Checklist Report";
                    }

                    div {
                        h3 { "Admin Checklist Details" }
                        img src="https://asset7.net/tmt-2025-demo/riw_admin_checklist_details.png" alt="RIW Admin Checklist Details";
                    }

                    div {
                        h3 { "Admin Add New RIW" }
                        img src="https://asset7.net/tmt-2025-demo/riw_admin_add_new_riw.png" alt="RIW Admin Add New RIW";
                    }

                    div {
                        h3 { "Admin Member List" }
                        img src="https://asset7.net/tmt-2025-demo/riw_admin_memberlist.png" alt="RIW Admin Member List";
                    }

                    div {
                        h3 { "Members" }
                        img src="https://asset7.net/tmt-2025-demo/riw_members.png" alt="RIW Members";
                    }

                    div {
                        h3 { "Admin Attendance Report" }
                        img src="https://asset7.net/tmt-2025-demo/riw_admin_attendance_report.png" alt="RIW Admin Attendance Report";
                    }

                    div {
                        h3 { "Attendance" }
                        img src="https://asset7.net/tmt-2025-demo/riw_attendance.png" alt="RIW Attendance";
                    }

                    div {
                        h3 { "Admin Manage RIW Content" }
                        img src="https://asset7.net/tmt-2025-demo/riw_admin_manage_riw_content.png" alt="RIW Admin Manage RIW Content";
                    }

                    div {
                        h3 { "Admin Content Home Page Details" }
                        img src="https://asset7.net/tmt-2025-demo/riw_admin_content-home_page_details.png" alt="RIW Admin Content Home Page Details";
                    }

                    div {
                        h3 { "Admin Content Phase Details" }
                        img src="https://asset7.net/tmt-2025-demo/riw_admin_content_phase_details.png" alt="RIW Admin Content Phase Details";
                    }

                    div {
                        h3 { "Admin Content Workshop Details" }
                        img src="https://asset7.net/tmt-2025-demo/riw_admin_content_workshop_details.png" alt="RIW Admin Content Workshop Details";
                    }

                    div {
                        h3 { "Workshop Details" }
                        img src="https://asset7.net/tmt-2025-demo/riw_workshop_details.png" alt="RIW Workshop Details";
                    }

                    div {
                        h3 { "Admin Events Details" }
                        img src="https://asset7.net/tmt-2025-demo/riw_admin_events_details.png" alt="RIW Admin Events Details";
                    }

                    div {
                        h3 { "Phase 1" }
                        img src="https://asset7.net/tmt-2025-demo/riw_phase1.png" alt="RIW Phase 1";
                    }

                    div {
                        h3 { "Phase 2" }
                        img src="https://asset7.net/tmt-2025-demo/riw_phase2.png" alt="RIW Phase 2";
                    }

                    div {
                        h3 { "Phase 3" }
                        img src="https://asset7.net/tmt-2025-demo/riw_phase3.png" alt="RIW Phase 3";
                    }

                    div {
                        h3 { "Rapid Starter" }
                        img src="https://asset7.net/tmt-2025-demo/riw_rapid_starter.png" alt="RIW Rapid Starter";
                    }

                    div {
                        h3 { "QUE" }
                        img src="https://asset7.net/tmt-2025-demo/riw_que.png" alt="RIW QUE";
                    }

                    div {
                        h3 { "Resources" }
                        img src="https://asset7.net/tmt-2025-demo/riw_resources.png" alt="RIW Resources";
                    }

                    div {
                        h3 { "FAQ" }
                        img src="https://asset7.net/tmt-2025-demo/riw_faq.png" alt="RIW FAQ";
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

#[approck::http(GET /admin/tickets/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Tickets");
        doc.set_body_display_fluid();

        doc.add_body(html!(
            main {
                h1 { "Tickets" }

                grid-2 {
                    div {
                        h3 { "Ticket List" }
                        img src="https://asset7.net/tmt-2025-demo/ticket_list.png" alt="Ticket List";
                    }

                    div {
                        h3 { "Ticket Details" }
                        img src="https://asset7.net/tmt-2025-demo/ticket_details.png" alt="Ticket Details";
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

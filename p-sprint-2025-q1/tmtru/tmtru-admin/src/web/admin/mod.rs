pub mod crm;
pub mod index;

// Admin modules
pub mod engagement;
pub mod gamification;
pub mod riw;
pub mod tickets;

#[approck::prefix(/admin/)]
pub mod prefix {
    pub fn menu(menu: Menu) {
        menu.set_label_uri("Admin Dashboard", "/admin/");
        menu.add_link("CRM", "/admin/crm");
        menu.add_link("Engagement", "/admin/engagement/");
        menu.add_link("RIW", "/admin/riw/");
        menu.add_link("Tickets", "/admin/tickets/");
        menu.add_link("Gamification", "/admin/gamification/");
    }
    pub fn auth() {}
}

#[approck::http(GET /admin/gamification/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Gamification");

        doc.add_body(html!(
            main {
                h1 { "Gamification" }

                div {
                    h3 { "Gamification Interface" }
                    img src="https://asset7.net/tmt-2025-demo/gamification.png" alt="Gamification Interface";
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

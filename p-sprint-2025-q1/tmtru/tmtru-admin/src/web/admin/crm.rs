#[approck::http(GET /admin/crm; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("CRM - Customer Relationship Management");

        doc.add_body(html!(
            main {
                h1 { "CRM Integration" }
                p.mb-4 { "HubSpot, Keap sync, lead scoring, automation." }

                grid-2 {
                    table-wrapper.data-list {
                        h4 { "HubSpot Status" }
                        table {
                            tr {
                                td { "Connection" }
                                td { i.fas.fa-check {} }
                            }
                            tr {
                                td { "Last Sync" }
                                td { label-tag.success { "2 min ago" } }
                            }
                            tr {
                                td { "Records Synced" }
                                td { label-tag.primary { "1,247" } }
                            }
                        }
                    }

                    table-wrapper.data-list {
                        h4 { "Keap (Infusionsoft)" }
                        table {
                            tr {
                                td { "Connection" }
                                td { i.far.fa-circle {} }
                            }
                            tr {
                                td { "Setup Required" }
                                td { label-tag.warning { "API Key" } }
                            }
                        }
                    }

                    table-wrapper.data-list {
                        h4 { "Lead Scoring" }
                        table {
                            tr {
                                td { "Hot Leads" }
                                td { label-tag.danger { "12" } }
                            }
                            tr {
                                td { "Warm Leads" }
                                td { label-tag.warning { "34" } }
                            }
                            tr {
                                td { "Cold Leads" }
                                td { label-tag.primary { "89" } }
                            }
                        }
                    }

                    table-wrapper.data-list {
                        h4 { "Automation Rules" }
                        table {
                            tr {
                                td { "Lead Assignment" }
                                td { i.fas.fa-robot {} }
                            }
                            tr {
                                td { "Follow-up Sequences" }
                                td { i.fas.fa-envelope {} }
                            }
                            tr {
                                td { "Score Updates" }
                                td { i.fas.fa-chart-line {} }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

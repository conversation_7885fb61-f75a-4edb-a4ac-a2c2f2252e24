#[approck::http(GET /admin/engagement/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Engagement Dashboard");
        doc.set_body_display_fluid();

        doc.add_body(html!(
            main {
                h1 { "Engagement" }

                grid-2 {
                    div {
                        h3 { "Company Engagement" }
                        img src="https://asset7.net/tmt-2025-demo/engagement_company.png" alt="Engagement Company";
                    }

                    div {
                        h3 { "Metric Settings" }
                        img src="https://asset7.net/tmt-2025-demo/engagement_metric_settings.png" alt="Engagement Metric Settings";
                    }

                    div {
                        h3 { "Daily Charts" }
                        img src="https://asset7.net/tmt-2025-demo/engagement_daily_charts.png" alt="Engagement Daily Charts";
                    }

                    div {
                        h3 { "Report Filters" }
                        img src="https://asset7.net/tmt-2025-demo/engagement_report_filters.png" alt="Engagement Report Filters";
                    }

                    div {
                        h3 { "Score Report" }
                        img src="https://asset7.net/tmt-2025-demo/engagement_score_report.png" alt="Engagement Score Report";
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

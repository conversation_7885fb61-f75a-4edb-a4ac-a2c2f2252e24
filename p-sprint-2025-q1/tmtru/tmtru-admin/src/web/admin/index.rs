#[approck::http(GET /admin/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Administrator Dashboard");

        doc.add_body(html!(
            main {
                h1 { "Administrator Dashboard" }
                p.mb-4 { "Identity, integrations, governance." }

                grid-2 {
                    table-wrapper.data-list {
                        h4 { "SSO Providers" }
                        table {
                            tr {
                                td { "Google" }
                                td { i.fas.fa-check {} }
                            }
                            tr {
                                td { "Microsoft" }
                                td { i.fas.fa-check {} }
                            }
                            tr {
                                td { "Apple" }
                                td { i.fas.fa-check {} }
                            }
                            tr {
                                td { "tmtru One" }
                                td { i.fas.fa-check {} }
                            }
                        }
                    }

                    table-wrapper.data-list {
                        h4 { "CRM Integrations" }
                        table {
                            tr {
                                td { "HubSpot" }
                                td { i.fas.fa-check {} }
                            }
                            tr {
                                td { "Keap (Infusionsoft)" }
                                td { i.far.fa-circle {} }
                            }
                        }
                    }

                    table-wrapper.data-list {
                        h4 { "Membership Levels & Tags" }
                        table {
                            tr {
                                td { "Gold, Silver" }
                                td { label-tag.primary { "Active" } }
                            }
                            tr {
                                td { "Suppression Tags" }
                                td { label-tag.primary { "Configured" } }
                            }
                        }
                    }

                    table-wrapper.data-list {
                        h4 { "Email Sender Profiles" }
                        table {
                            tr {
                                td { "<EMAIL>" }
                                td { label-tag.success { "Verified" } }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

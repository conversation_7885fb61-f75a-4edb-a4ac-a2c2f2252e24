/* Goals Page - Professional Layout */

/* Main goals container */
panel {
    content {

        /* Grid layout */
        grid-12 {
            max-width: 1400px;
            margin: 0 auto;
            gap: 3rem;

            @media (max-width: 992px) {
                gap: 2rem;
            }

            /* Main content area */
            cell-8 {
                display: flex;
                flex-direction: column;
                gap: 2rem;

                /* AI Question section */
                section.x-ai-question {
                    background: #fff;
                    padding: 2rem;
                    border-radius: 12px;
                    border: 2px solid #dfdfdf;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

                    label {
                        font-size: 1.3rem;
                        font-weight: 600;
                        color: #2c3e50;
                        margin-bottom: 1rem;
                        display: block;
                    }

                    input {
                        width: 100%;
                        padding: 1.2rem 1.5rem;
                        font-size: 1.1rem;
                        min-height: 5rem;
                        border: 2px solid #e9ecef;
                        border-radius: 8px;
                        transition: all 0.3s ease;
                        background: #fff;
                        box-sizing: border-box;

                        &:focus {
                            outline: none;
                            border-color: #007bff;
                            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
                            transform: translateY(-1px);
                        }

                        &::placeholder {
                            color: #6c757d;
                            font-style: italic;
                        }
                    }
                }

                /* Progress section */
                section.x-progress {
                    background: #fff;
                    padding: 1.5rem 2rem;
                    border-radius: 12px;
                    border: 2px solid #dfdfdf;
                    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);

                    label {
                        font-size: 1.1rem;
                        font-weight: 600;
                        color: #495057;
                        margin-bottom: 1rem;
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;

                        &::before {
                            content: '📊';
                            font-size: 1.2rem;
                        }
                    }

                    progress {
                        width: 100%;
                        height: 28px;
                        background: #f8f9fa;
                        border: 1px solid #dee2e6;
                        border-radius: 14px;
                        overflow: hidden;
                        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);

                        &::-webkit-progress-bar {
                            background: #f8f9fa;
                            border-radius: 14px;
                        }

                        &::-webkit-progress-value {
                            background: linear-gradient(90deg, #28a745, #20c997);
                            border-radius: 14px;
                            transition: all 0.3s ease;
                        }

                        &::-moz-progress-bar {
                            background: linear-gradient(90deg, #28a745, #20c997);
                            border-radius: 14px;
                        }
                    }
                }
            }

            /* Sidebar with suggestions */
            cell-4 {
                align-self: flex-start;

                h2 {
                    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
                    color: #fff;
                    margin: 0 0 1rem 0;
                    padding: 1.5rem 2rem;
                    font-size: 1.2rem;
                    font-weight: 600;
                    border-radius: 12px;
                    position: relative;
                    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
                }

                /* Suggestion list */
                x-suggestion-list {
                    display: flex;
                    flex-direction: column;
                    gap: 1rem;

                    x-suggestion-item {
                        display: block;

                        /* Goal tile buttons */
                        button.x-goal {
                            background: #fff;
                            border: 2px solid #dfdfdf;
                            border-radius: 12px;
                            padding: 1rem;
                            text-align: left;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
                            display: flex;
                            flex-direction: column;
                            gap: 0.75rem;
                            width: 100%;

                            &:hover {
                                transform: translateY(-4px);
                                box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
                                border-color: #007bff;
                                background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
                            }

                            &:active {
                                transform: translateY(-2px);
                            }

                            &:focus {
                                outline: none;
                                box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
                            }

                            /* Goal title */
                            div.button-title {
                                font-size: 1rem;
                                font-weight: 600;
                                color: #2c3e50;
                                line-height: 1.3;
                            }

                            /* Goal description */
                            div.button-description {
                                font-size: 0.75rem;
                                color: #6c757d;
                                line-height: 1.4;
                                font-weight: 400;
                                flex-grow: 1;
                                display: -webkit-box;
                                -webkit-line-clamp: 3;
                                line-clamp: 3;
                                -webkit-box-orient: vertical;
                                overflow: hidden;
                            }
                        }
                    }
                }
            }
        }

        h3 { margin-top: 3rem; }

        /* Table section */
        table-wrapper.data-list {
            background: #fff;
            border-radius: 0 0 12px 12px;
            border: 2px solid #dfdfdf;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;

            table {
                min-width: auto;
                table-layout: auto;

                th, td {
                    word-wrap: break-word;
                    white-space: normal;
                }

                tbody tr {
                    transition: all 0.2s ease;

                    &:hover {
                        background: #f8f9fa;
                        transform: translateX(2px);
                    }

                    td {
                        padding: 1rem 1.5rem;
                    }
                }
            }
        }
    }
}
#[approck::http(GET /goals/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Goals");

        doc.add_body(html!(
            panel {
                content {
                    grid-12 {

                        cell-8 {
                            // AI Question Section
                            section.x-ai-question {
                                label for="goal-input" { "What are your goals?" }
                                input #goal-input type="text" name="goal-input" placeholder="Tell me about your business goals";
                            }

                            // Progress Section
                            section.x-progress {
                                label { "Progress" }
                                progress value="40" max="100" { "40%" }
                            }
                        }

                        // Goals Table Section
                        cell-4 {
                            h2 { "Suggestions" }
                            x-suggestion-list {
                                x-suggestion-item {
                                    button.x-goal {
                                        div.button-title { "Personal Wealth Creation" }
                                        div.button-description { "Define how much net worth or investable assets you want the business to generate for you personally in 6 months and in 5 years." }
                                    }
                                }
                                x-suggestion-item {
                                    button.x-goal {
                                        div.button-title { "Owner's Time Freedom & Well-being" }
                                        div.button-description { "Set targets for average hours worked per week, number of vacation days taken, and stress level you want to maintain." }
                                    }
                                }
                                x-suggestion-item {
                                    button.x-goal {
                                        div.button-title { "Operational Autonomy & Scalability" }
                                        div.button-description { "Decide how independent the business should be from your daily involvement (e.g., % of core operations owner-independent)." }
                                    }
                                }
                                x-suggestion-item {
                                    button.x-goal {
                                        div.button-title { "Market Leadership & Brand Strength" }
                                        div.button-description { "Establish goals for your industry reputation, such as market share, recognition, or influence over industry conversations." }
                                    }
                                }
                            }
                        }
                    }
                    h3 { "Business Owner Success Profile" }
                    table-wrapper.data-list {
                        table {
                            thead {
                                tr {
                                    th { "Dimension" }
                                    th { "6-Month Goal" }
                                    th { "5-Year Goal" }
                                }
                            }
                            tbody {
                                tr {
                                    td { "Profitability & Cash Health" }
                                    td { "Reach $500K annualized revenue" }
                                    td { "Reach $1.2M annualized revenue" }
                                }
                                tr {
                                    td { "Team Health & Culture" }
                                    td { "Offer 3+ weeks PTO" }
                                    td { "6 weeks PTO + flight vouchers" }
                                }
                                tr {
                                    td { "Customer Loyalty & Value" }
                                    td { "Reduce churn below 5%" }
                                    td { "Clients receive more value than they pay" }
                                }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

#[approck::http(GET /support/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Support Dashboard");

        doc.add_body(html!(
            main {
                h1 { "Support Team" }
                p.mb-4 { "Live tickets, queues, SLA risk." }

                grid-2 {
                    table-wrapper.data-list {
                        h4 { "Tickets Queue" }
                        table {
                            tr {
                                td { "New" }
                                td { label-tag.primary { "7" } }
                            }
                            tr {
                                td { "In Progress" }
                                td { label-tag.warning { "22" } }
                            }
                            tr {
                                td { "Breach Risk" }
                                td { label-tag.danger { "3" } }
                            }
                        }
                    }

                    table-wrapper.data-list {
                        h4 { "Live Chat / Video" }
                        table {
                            tr {
                                td { "Waiting" }
                                td { label-tag.warning { "2" } }
                            }
                            tr {
                                td { "Join room" }
                                td { i.fas.fa-video {} }
                            }
                        }
                    }

                    table-wrapper.data-list {
                        h4 { "SLA Heatmap" }
                        table {
                            tr {
                                td { "Heatmap" }
                                td.trend-chart { "▒▒▒▓▓▓███" }
                            }
                            tr {
                                td { "Show segments" }
                                td { "by client" }
                            }
                        }
                    }

                    table-wrapper.data-list {
                        h4 { "Quick Actions" }
                        table {
                            tr {
                                td { "Escalate Priority" }
                                td { i.fas.fa-arrow-up {} }
                            }
                            tr {
                                td { "Bulk Update" }
                                td { i.fas.fa-edit {} }
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

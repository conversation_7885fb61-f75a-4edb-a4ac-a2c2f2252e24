/* Member Dashboard - minimal styling */

/* Responsive images for tmtru member dashboard */
img {
    max-width: 100%;
    height: auto;
}

.img-fluid {
    max-width: 100%;
    height: auto;
}

main {

    h1 {
        margin-bottom: 0.5rem;
        color: #333;
    }

    p.mb-4 {
        margin-bottom: 2rem;
        color: #666;
        font-size: 1.1rem;
    }
}

table-wrapper.data-list {
    margin: 0.25rem;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;

    /* Fix horizontal scrollbar issue for member dashboard */
    overflow-x: visible;

    table {
        min-width: auto; /* Override global min-width */
        table-layout: auto;

        th, td {
            word-wrap: break-word;
            white-space: normal;
        }
    }

    h2 {
        margin-top: 0;
        margin-bottom: 1rem;
        font-size: 1.2rem;
        color: #333;
    }

    footer {
        margin-top: 1rem;
        text-align: right;

        a {
            color: #0077cc;
            text-decoration: none;
            font-weight: 500;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}

/* KPI trend indicators */
.trend-up {
    color: #28a745;
    font-weight: bold;
}

.trend-down {
    color: #dc3545;
    font-weight: bold;
}

.trend-stable {
    color: #6c757d;
    font-weight: bold;
}

/* Quick Actions Section */
section.quick-actions {
    margin-top: 2rem;
    background: #fff;
    padding: 1.5rem;
    border-radius: 4px;
    border: 1px solid #ddd;

    h2 {
        margin-top: 0;
        margin-bottom: 1.5rem;
        color: #333;
    }

    .action-card {
        display: block;
        padding: 1.5rem;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        text-decoration: none;
        color: #333;
        transition: all 0.2s ease;
        text-align: center;

        &:hover {
            background: #e9ecef;
            border-color: #0077cc;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        i {
            font-size: 2rem;
            color: #0077cc;
            margin-bottom: 0.5rem;
            display: block;
        }

        h3 {
            margin: 0.5rem 0;
            font-size: 1.1rem;
            color: #333;
        }

        p {
            margin: 0;
            font-size: 0.9rem;
            color: #666;
        }
    }
}

/* Badge styles for event types */
label-tag {
    display: inline-block;
    white-space: nowrap;
    padding: .35em .65em;
    font-size: .75em;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    border-radius: .25rem;

    &.zoom { background-color: #ffcfc4; color: #000; }
    &.webinar { background-color: #b3d8fa; color: #000; }
    &.virtual { background-color: #bdecd6; color: #000; }
    
    &.success { background-color: #28a745; }
    &.warning { background-color: #ffc107; color: #000; }
    &.info { background-color: #17a2b8; }
    &.danger { background-color: #dc3545; }
}

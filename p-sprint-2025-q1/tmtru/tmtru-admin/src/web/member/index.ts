import "./index.mcss";

// Member Dashboard functionality
addEventListener("DOMContentLoaded", () => {
    // Handle quick action card clicks
    const actionCards = document.querySelectorAll("a.action-card");
    actionCards.forEach((card) => {
        card.addEventListener("click", () => {
            const href = card.getAttribute("href");
            const title = card.querySelector("h3")?.textContent;
            console.log(`Quick action clicked: ${title} -> ${href}`);
        });
    });

    // Handle table footer links
    const footerLinks = document.querySelectorAll("table-wrapper.data-list footer a");
    footerLinks.forEach((link) => {
        link.addEventListener("click", () => {
            const href = link.getAttribute("href");
            const text = link.textContent;
            console.log(`Footer link clicked: ${text} -> ${href}`);
        });
    });

    // Add hover effects to table rows
    const tableRows = document.querySelectorAll("table tbody tr");
    tableRows.forEach((row) => {
        const htmlRow = row as HTMLElement;
        row.addEventListener("mouseenter", () => {
            htmlRow.style.backgroundColor = "#f8f9fa";
        });

        row.addEventListener("mouseleave", () => {
            htmlRow.style.backgroundColor = "";
        });
    });

    // Initialize Font Awesome icons
    const icons = document.querySelectorAll("i.fas");
    icons.forEach((icon) => {
        // Ensure Font Awesome classes are properly applied
        if (!icon.classList.contains("fas")) {
            icon.classList.add("fas");
        }
    });

    console.log("Member Dashboard initialized");
});

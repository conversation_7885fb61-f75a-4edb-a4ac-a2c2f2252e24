#[approck::http(GET /member/calendar; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Event Calendar");
        doc.set_body_display_fluid();

        doc.add_body(html!(
            x-layout {
                x-legend {
                    h2 { "Event Types" }
                    ul {
                        li { span.dot.live {} "Live Event" }
                        li { span.dot.webinar {} "Webinar" }
                        li { span.dot.zoom {} "Zoom Meeting" }
                        li { span.dot.virtual {} "Live Virtual Event" }
                        li { span.dot.strategy {} "Strategy Session" }
                        li { span.dot.training {} "Training" }
                        li { span.dot.deadline {} "Deadline" }
                    }
                }

                x-calendar-wrapper {
                    x-view-toggle {
                        a.primary href="/member/calendar" { "Month View" }
                        a.secondary href="/member/agenda" { "Agenda View" }
                    }

                    x-controls {
                        x-nav-calendar {
                            button { "‹ Previous" }
                        }
                        h1 { "August 2025" }
                        x-nav-calendar {
                            button { "Next ›" }
                        }
                    }

                    x-weekdays {
                        div { "Sunday" }
                        div { "Monday" }
                        div { "Tuesday" }
                        div { "Wednesday" }
                        div { "Thursday" }
                        div { "Friday" }
                        div { "Saturday" }
                    }

                    x-calendar {
                        // Week 1
                        x-day {
                            header {
                                span.day-number { "27" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "28" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "29" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="webinar" {
                                label-tag.webinar { "Webinar" }
                                br;
                                span.title { "tmtru VSA Best Practices: "}
                                "Optimizing Remote Monitoring & Management"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "30" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="live" {
                                label-tag.live { "Live Event" }
                                br;
                                span.title { "tmtru One Platform Training: "}
                                "Live Implementation Workshop"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "31" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="live" {
                                label-tag.live { "Live Event" }
                                br;
                                span.title { "tmtru Automation Training: "}
                                "Advanced Scripting Workshop"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "1" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="live" {
                                label-tag.live { "Live Event" }
                                br;
                                span.title { "tmtru DattoCon 2025: "}
                                "Annual Partner Conference"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "2" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }

                        // Week 2
                        x-day {
                            header {
                                span.day-number { "3" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "4" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="zoom" {
                                label-tag.zoom { "Zoom Meeting" }
                                br;
                                span.title { "tmtru RMM Onboarding: "}
                                "New Partner Orientation"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "5" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="webinar" {
                                label-tag.webinar { "Webinar" }
                                br;
                                span.title { "tmtru Support Office Hours: "}
                                "Technical Q&A Session"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "6" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="virtual" {
                                label-tag.virtual { "Live Virtual Event" }
                                br;
                                span.title { "tmtru Compliance Manager: "}
                                "Automated Compliance Reporting"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "7" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="webinar" {
                                label-tag.webinar { "Webinar" }
                                br;
                                span.title { "tmtru Revenue Growth: "}
                                "Maximizing MSP Profitability"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "8" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "9" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }

                        // Week 3
                        x-day {
                            header {
                                span.day-number { "10" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "11" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="training" {
                                label-tag.training { "Training" }
                                br;
                                span.title { "tmtru BMS Training: "}
                                "Business Management Suite Certification"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "12" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "13" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="strategy" {
                                label-tag.strategy { "Strategy Session" }
                                br;
                                span.title { "tmtru Growth Strategy: "}
                                "Q4 2025 Partner Planning Session"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "14" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "15" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="deadline" {
                                label-tag.deadline { "Deadline" }
                                br;
                                span.title { "tmtru Certification Deadline: "}
                                "Complete Partner Requirements by 5PM"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "16" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }

                        // Week 4
                        x-day {
                            header {
                                span.day-number { "17" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "18" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "19" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="webinar" {
                                label-tag.webinar { "Webinar" }
                                br;
                                span.title { "tmtru AI Integration: "}
                                "Machine Learning for MSP Operations"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "20" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "21" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="virtual" {
                                label-tag.virtual { "Live Virtual Event" }
                                br;
                                span.title { "tmtru Client Onboarding: "}
                                "Best Practices Workshop"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "22" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "23" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }

                        // Week 5
                        x-day {
                            header {
                                span.day-number { "24" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "25" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "26" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "27" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="zoom" {
                                label-tag.zoom { "Zoom Meeting" }
                                br;
                                span.title { "tmtru Partner Briefing: "}
                                "Q4 Product Updates & Roadmap"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "28" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "29" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="webinar" {
                                label-tag.webinar { "Webinar" }
                                br;
                                span.title { "tmtru Workflow Optimization: "}
                                "Advanced Automation Strategies"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "30" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }

                        // Week 6 (partial)
                        x-day {
                            header {
                                span.day-number { "31" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "1" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "2" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "3" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "4" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                            x-event type="virtual" {
                                label-tag.virtual { "Live Virtual Event" }
                                br;
                                span.title { "tmtru Success Planning: "}
                                "Annual Partner Review & Growth Strategy"
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "5" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                        x-day {
                            header {
                                span.day-number { "6" }
                                button.add-event { i.fas.fa-plus {}}
                            }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

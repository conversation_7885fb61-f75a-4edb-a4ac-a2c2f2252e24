#[approck::http(GET /member/kpi; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("KPI Dashboard");
        doc.set_body_display_fluid();

        doc.add_body(html!(
            main {
                h1 { "KPI Dashboard" }

                grid-2 {
                    div {
                        h3 { "Performance Overview" }
                        img src="https://asset7.net/tmt-2025-demo/kpi_performance.png" alt="KPI Performance Dashboard";
                    }

                    div {
                        h3 { "Annual Goals Setup" }
                        img src="https://asset7.net/tmt-2025-demo/kpi_annual_goals.png" alt="KPI Annual Goals";
                    }

                    div {
                        h3 { "Goals Preview" }
                        img src="https://asset7.net/tmt-2025-demo/kpi_annual_goals_preview.png" alt="KPI Annual Goals Preview";
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

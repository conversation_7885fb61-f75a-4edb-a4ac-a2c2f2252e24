#[approck::http(GET /member/agenda; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Event Agenda");
        doc.set_body_display_fluid();

        doc.add_body(html!(
            x-layout {
                x-legend {
                    h2 { "Event Types" }
                    ul {
                        li { span.dot.live {} "Live Event" }
                        li { span.dot.webinar {} "Webinar" }
                        li { span.dot.zoom {} "Zoom Meeting" }
                        li { span.dot.virtual {} "Live Virtual Event" }
                        li { span.dot.strategy {} "Strategy Session" }
                        li { span.dot.training {} "Training" }
                        li { span.dot.deadline {} "Deadline" }
                    }
                }

                x-agenda-wrapper {
                    x-view-toggle {
                        a.secondary href="/member/calendar" { "Month View" }
                        a.primary href="/member/agenda" { "Agenda View" }
                    }

                    x-controls {
                        x-nav-calendar {
                            button { "‹ Previous" }
                        }
                        h1 { "August 2025" }
                        x-nav-calendar {
                            button { "Next ›" }
                        }
                    }

                    x-agenda-content {
                        section.event {
                            div.event-date { "Aug 04, 2025" }
                            a.event-title href="#" { "tmtru RMM Onboarding – New Partner Orientation" }
                            div.event-time {
                                "Start Date: 08/04/2025 10:00 AM US/Eastern   "
                                "End Date: 08/04/2025 11:00 AM US/Eastern"
                            }
                            label-tag.zoom { "Zoom Meeting" }

                            p { "Welcome to the tmtru family! In this comprehensive onboarding session, we'll walk new MSP partners through the essentials of tmtru RMM, helping you get up to speed quickly and start managing endpoints efficiently." }

                            p { strong { "What You'll Learn:" } }
                            ul.fa-checklist {
                                li { i.fas.fa-check {} " Navigating tmtru VSA: Key features & tools for endpoint management" }
                                li { i.fas.fa-check {} " Agent Deployment: Installing and configuring tmtru agents" }
                                li { i.fas.fa-check {} " Monitoring & Alerting: Setting up proactive system monitoring" }
                                li { i.fas.fa-check {} " Patch Management: Automated Windows and third-party patching" }
                                li { i.fas.fa-check {} " Live Q&A Session with tmtru Experts" }
                            }

                            p { "Whether you're new to tmtru or RMM platforms in general, this session will give you the confidence to hit the ground running with your new tmtru deployment." }
                            p { "See you there!" }

                            button.primary { "View Details" }
                        }

                        section.event {
                            div.event-date { "Aug 05, 2025" }
                            a.event-title href="#" { "tmtru Support Office Hours" }
                            div.event-time {
                                "Start Date: 08/05/2025 11:00 AM US/Eastern   "
                                "End Date: 08/05/2025 12:00 PM US/Eastern"
                            }
                            label-tag.webinar { "Webinar" }

                            p { "This session is your opportunity to connect directly with our tmtru technical experts and ask any questions about the VSA platform, best practices, or technical implementation challenges." }

                            p { strong { "Topics we may cover:" } }
                            ul.fa-checklist {
                                li { i.fas.fa-check {} " tmtru VSA configuration and optimization strategies" }
                                li { i.fas.fa-check {} " Advanced scripting and automation workflows" }
                                li { i.fas.fa-check {} " Integration with tmtru BMS and IT Glue" }
                                li { i.fas.fa-check {} " Troubleshooting common deployment issues" }
                                li { i.fas.fa-check {} " Security best practices and compliance reporting" }
                            }

                            p { "This open forum gives you the flexibility to bring your own tmtru questions or learn from other MSP partners' experiences." }

                            button.primary { "View Details" }
                        }

                        section.event {
                            div.event-date { "Aug 06, 2025" }
                            a.event-title href="#" { "tmtru Compliance Manager – Automated Compliance Reporting" }
                            div.event-time {
                                "Start Date: 08/06/2025 09:00 AM US/Eastern   "
                                "End Date: 08/06/2025 10:30 AM US/Eastern"
                            }
                            label-tag.virtual { "Live Virtual Event" }

                            p { "Learn how tmtru Compliance Manager can streamline your compliance reporting processes and help you meet industry standards like SOC 2, HIPAA, and PCI DSS with automated documentation and evidence collection." }

                            p { strong { "What You'll Learn:" } }
                            ul.fa-checklist {
                                li { i.fas.fa-check {} " Setting up automated compliance frameworks" }
                                li { i.fas.fa-check {} " Generating audit-ready reports and documentation" }
                                li { i.fas.fa-check {} " Integrating with existing tmtru VSA monitoring" }
                                li { i.fas.fa-check {} " Managing client compliance requirements efficiently" }
                                li { i.fas.fa-check {} " Best practices for ongoing compliance maintenance" }
                            }

                            p { "Perfect for MSPs looking to expand their compliance service offerings or streamline existing compliance processes." }

                            button.primary { "View Details" }
                        }

                        section.event {
                            div.event-date { "Aug 07, 2025" }
                            a.event-title href="#" { "tmtru Revenue Growth – Maximizing MSP Profitability" }
                            div.event-time {
                                "Start Date: 08/07/2025 01:00 PM US/Eastern   "
                                "End Date: 08/07/2025 02:30 PM US/Eastern"
                            }
                            label-tag.webinar { "Webinar" }

                            p { "Discover proven strategies for growing your MSP business using tmtru's comprehensive platform. Learn how successful partners are leveraging tmtru tools to increase recurring revenue and improve operational efficiency." }

                            p { strong { "Key Topics:" } }
                            ul.fa-checklist {
                                li { i.fas.fa-check {} " Upselling and cross-selling with tmtru BMS insights" }
                                li { i.fas.fa-check {} " Automating service delivery to improve margins" }
                                li { i.fas.fa-check {} " Using tmtru analytics for business intelligence" }
                                li { i.fas.fa-check {} " Scaling operations with tmtru automation" }
                                li { i.fas.fa-check {} " Partner success stories and case studies" }
                            }

                            p { "Join fellow MSP owners and learn actionable strategies you can implement immediately to drive growth." }

                            button.primary { "View Details" }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

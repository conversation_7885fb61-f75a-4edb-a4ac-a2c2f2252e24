pub mod agenda;
pub mod calendar;
pub mod chat;
pub mod group;
pub mod index;
pub mod kpi;
pub mod que;

#[approck::prefix(/member/)]
pub mod prefix {
    pub fn menu(menu: Menu) {
        menu.set_label_uri("Member Dashboard", "/member/");
        menu.add_link("Calendar", "/member/calendar");
        menu.add_link("Chat", "/member/chat");
        menu.add_link("Group", "/member/group");
        menu.add_link("KPI", "/member/kpi");
        menu.add_link("Que", "/member/que");
    }
    pub fn auth() {}
}

#[approck::http(GET /member/chat; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Chat");

        doc.add_body(html!(
            main {
                h1 { "Chat" }

                div {
                    h3 { "Main Chat Interface" }
                    img src="https://asset7.net/tmt-2025-demo/chat.png" alt="Chat Interface";
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

#[approck::http(GET /member/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Member Dashboard");

        doc.add_body(html!(
            main {
                h1 { "Member Dashboard" }
                p.mb-4 { "Your personal workspace for tmtru events, peer groups, KPI tracking, and communication." }

                grid-2 {
                    section {
                        table-wrapper.data-list {
                            h2 { "📅 Upcoming Events" }
                            table {
                                thead {
                                    tr {
                                        th { "Date" }
                                        th { "Event" }
                                        th { "Type" }
                                        th { "Status" }
                                    }
                                }
                                tbody {
                                    tr {
                                        td { "Aug 04" }
                                        td { "tmtru RMM Onboarding" }
                                        td { label-tag.zoom { "Zoom" } }
                                        td { label-tag.success { "Registered" } }
                                    }
                                    tr {
                                        td { "Aug 05" }
                                        td { "Support Office Hours" }
                                        td { label-tag.webinar { "Webinar" } }
                                        td { label-tag.warning { "Pending" } }
                                    }
                                    tr {
                                        td { "Aug 06" }
                                        td { "Compliance Manager Training" }
                                        td { label-tag.virtual { "Virtual" } }
                                        td { label-tag.info { "Available" } }
                                    }
                                    tr {
                                        td { "Aug 07" }
                                        td { "Revenue Growth Webinar" }
                                        td { label-tag.webinar { "Webinar" } }
                                        td { label-tag.info { "Available" } }
                                    }
                                }
                            }
                            footer {
                                a href="/member/calendar" { "View Full Calendar →" }
                            }
                        }
                    }

                    section {
                        table-wrapper.data-list {
                            h2 { "💬 Recent QUE Activity" }
                            table {
                                thead {
                                    tr {
                                        th { "Topic" }
                                        th { "Group" }
                                        th { "Activity" }
                                    }
                                }
                                tbody {
                                    tr {
                                        td { "tmtru VSA Best Practices" }
                                        td { "RMM Users" }
                                        td { "3 new replies" }
                                    }
                                    tr {
                                        td { "Client Onboarding Process" }
                                        td { "MSP Growth" }
                                        td { "5 new replies" }
                                    }
                                    tr {
                                        td { "Security Framework Updates" }
                                        td { "Compliance" }
                                        td { "2 new replies" }
                                    }
                                    tr {
                                        td { "Pricing Strategy Discussion" }
                                        td { "Business Dev" }
                                        td { "1 new reply" }
                                    }
                                }
                            }
                            footer {
                                a href="/member/que" { "View All Discussions →" }
                            }
                        }
                    }
                }

                grid-2 {
                    section {
                        table-wrapper.data-list {
                            h2 { "📊 Your KPI Summary" }
                            table {
                                thead {
                                    tr {
                                        th { "Metric" }
                                        th { "Current" }
                                        th { "Target" }
                                        th { "Trend" }
                                    }
                                }
                                tbody {
                                    tr {
                                        td { "Monthly Recurring Revenue" }
                                        td { "$45,200" }
                                        td { "$50,000" }
                                        td { span.trend-up { "↗ +8%" } }
                                    }
                                    tr {
                                        td { "Client Retention Rate" }
                                        td { "94%" }
                                        td { "95%" }
                                        td { span.trend-stable { "→ 0%" } }
                                    }
                                    tr {
                                        td { "Average Response Time" }
                                        td { "2.3 hrs" }
                                        td { "2.0 hrs" }
                                        td { span.trend-down { "↘ -5%" } }
                                    }
                                    tr {
                                        td { "New Clients This Month" }
                                        td { "7" }
                                        td { "10" }
                                        td { span.trend-up { "↗ +40%" } }
                                    }
                                }
                            }
                            footer {
                                a href="/member/kpi" { "View Detailed Analytics →" }
                            }
                        }
                    }

                    section {
                        table-wrapper.data-list {
                            h2 { "👥 Peer Group Activity" }
                            table {
                                thead {
                                    tr {
                                        th { "Group" }
                                        th { "Members" }
                                        th { "Last Activity" }
                                    }
                                }
                                tbody {
                                    tr {
                                        td { "MSP Growth Strategies" }
                                        td { "24 members" }
                                        td { "2 hours ago" }
                                    }
                                    tr {
                                        td { "tmtru Power Users" }
                                        td { "18 members" }
                                        td { "4 hours ago" }
                                    }
                                    tr {
                                        td { "Security Best Practices" }
                                        td { "31 members" }
                                        td { "1 day ago" }
                                    }
                                    tr {
                                        td { "Business Development" }
                                        td { "15 members" }
                                        td { "2 days ago" }
                                    }
                                }
                            }
                            footer {
                                a href="/member/group" { "Manage Groups →" }
                            }
                        }
                    }
                }

                section.quick-actions {
                    h2 { "🚀 Quick Actions" }
                    grid-4 {
                        a.action-card href="/member/calendar" {
                            i."fas fa-calendar" {}
                            h3 { "View Calendar" }
                            p { "See upcoming events and schedule" }
                        }
                        a.action-card href="/member/chat" {
                            i."fas fa-comments" {}
                            h3 { "Join Chat" }
                            p { "Connect with peers instantly" }
                        }
                        a.action-card href="/member/kpi" {
                            i."fas fa-chart-line" {}
                            h3 { "Track KPIs" }
                            p { "Monitor your business metrics" }
                        }
                        a.action-card href="/member/que" {
                            i."fas fa-question-circle" {}
                            h3 { "Browse QUE" }
                            p { "Find answers and discussions" }
                        }
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

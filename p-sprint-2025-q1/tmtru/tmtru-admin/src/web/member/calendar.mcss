/* Event Calendar - semantic nested CSS */
x-layout {
    display: grid;
    grid-template-columns: 240px 1fr;
    gap: 1rem;
}

x-legend {
    background: #fff;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #ddd;
    align-self: start;

    h2 {
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    ul {
        list-style: none;
        padding: 0;

        li {
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
    }

    span.dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;

        &.live { background: #ffe0a8; }
        &.webinar { background: #b3d8fa; }
        &.zoom { background: #ffcfc4; }
        &.virtual { background: #bdecd6; }
        &.strategy { background: #e7c7ec; }
        &.training { background: #c8e7a7; }
        &.deadline { background: #f8bcbc; }
    }
}

x-view-toggle {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-bottom: 1rem;

    a {
        background: #f8f9fa;
        border: 1px solid #ddd;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        text-decoration: none;
        font-size: 0.9rem;
        color: #666;
        transition: all 0.2s ease;

        &:hover {
            background: #e9ecef;
            color: #333;
        }

        &.primary {
            background: #0d6efd;
            color: white;
            border-color: #0077cc;
        }

        &.secondary {
            background: #f8f9fa;
            color: #666;
            border-color: #ddd;
        }
    }
}

x-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 6px;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.05);

    h1 {
        margin: 0;
        font-size: 1.5rem;
    }

    x-nav-calendar button {
        font-size: 1.2rem;
        background: none;
        border: none;
        cursor: pointer;
    }
}

x-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    font-weight: bold;
    background: #f0f0f0;
    padding: 0.75rem 0;
    border-bottom: 1px solid #ccc;
}

x-calendar {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #ccc;
}

x-day {
    background: #fff;
    padding: 0.75rem;
    font-size: 0.8rem;
    overflow-wrap: break-word;
    display: flex;
    flex-direction: column;

    header {
        font-weight: bold;
        margin-bottom: 0.5rem;
        display: flex;
        justify-content: space-between;
        align-items: center;

        span.day-number {
            font-size: 1rem;
        }

        button.add-event {
            background: none;
            border: none;
            font-size: 1rem;
            color: #ccc;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
            font-weight: normal;

            &:hover {
                background-color: #f0f0f0;
                color: #999;
            }

            &:active {
                background-color: #e0e0e0;
            }
        }
    }
}

x-event {
    background: #f9f9f9;
    padding: 0.5rem;
    margin-bottom: 0.4rem;
    border-left: 4px solid #5462c2;
    border-radius: 4px;
    white-space: normal;
    word-break: break-word;

    &[type="live"] { border-left-color: #ffe0a8; }
    &[type="webinar"] { border-left-color: #b3d8fa; }
    &[type="zoom"] { border-left-color: #ffcfc4; }
    &[type="virtual"] { border-left-color: #bdecd6; }
    &[type="strategy"] { border-left-color: #e7c7ec; }
    &[type="training"] { border-left-color: #c8e7a7; }
    &[type="deadline"] { border-left-color: #f8bcbc; }

    span.title {
        font-weight: 600;
    }
}

/* Badge styles targeting the global label-tag system */
label-tag {
    font-size: .75rem;
    
    &.live { background-color: #ffe0a8; color: #000; }
    &.webinar { background-color: #b3d8fa; color: #000; }
    &.zoom { background-color: #ffcfc4; color: #000; }
    &.virtual { background-color: #bdecd6; color: #000; }
    &.strategy { background-color: #e7c7ec; color: #000; }
    &.training { background-color: #c8e7a7; color: #000; }
    &.deadline { background-color: #f8bcbc; color: #000; }
}

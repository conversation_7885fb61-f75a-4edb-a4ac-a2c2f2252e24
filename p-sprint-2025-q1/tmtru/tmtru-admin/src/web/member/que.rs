#[approck::http(GET /member/que; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("QUE");

        doc.add_body(html!(
            main {
                h1 { "QUE" }

                grid-2 {
                    div {
                        h3 { "Main QUE Interface" }
                        img src="https://asset7.net/tmt-2025-demo/que.png" alt="QUE Main Interface";
                    }

                    div {
                        h3 { "QUE with Write New Post" }
                        img src="https://asset7.net/tmt-2025-demo/que_with_write_new_post.png" alt="QUE with Write New Post";
                    }

                    div {
                        h3 { "Write Post Interface" }
                        img src="https://asset7.net/tmt-2025-demo/que_write_post.png" alt="QUE Write Post";
                    }

                }


            }
        ));

        Response::HTML(doc.into())
    }
}

#[approck::http(GET /member/group; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Peer Groups");
        doc.set_body_display_fluid();

        doc.add_body(html!(
            main {
                h1 { "Peer Groups" }

                grid-2 {
                    div {
                        h3 { "PAC About" }
                        img src="https://asset7.net/tmt-2025-demo/pac_about.png" alt="PAC About";
                    }

                    div {
                        h3 { "PAC Home" }
                        img src="https://asset7.net/tmt-2025-demo/pac_home.png" alt="PAC Home";
                    }

                    div {
                        h3 { "KPI Dashboard" }
                        img src="https://asset7.net/tmt-2025-demo/pac_kpi_dashboard.png" alt="PAC KPI Dashboard";
                    }

                    div {
                        h3 { "KPI Summary" }
                        img src="https://asset7.net/tmt-2025-demo/pac_kpi_summary.png" alt="PAC KPI Summary";
                    }

                    div {
                        h3 { "Group Summary" }
                        img src="https://asset7.net/tmt-2025-demo/pac_group_summary.png" alt="PAC Group Summary";
                    }

                    div {
                        h3 { "Attendance" }
                        img src="https://asset7.net/tmt-2025-demo/pac_attendance.png" alt="PAC Attendance";
                    }

                    div {
                        h3 { "Members" }
                        img src="https://asset7.net/tmt-2025-demo/pac_members.png" alt="PAC Members";
                    }

                    div {
                        h3 { "Resources" }
                        img src="https://asset7.net/tmt-2025-demo/pac_resources.png" alt="PAC Resources";
                    }
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

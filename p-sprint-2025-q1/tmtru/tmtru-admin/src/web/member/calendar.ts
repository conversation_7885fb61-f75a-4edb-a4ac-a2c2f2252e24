import "./calendar.mcss";

// Calendar functionality
addEventListener("DOMContentLoaded", () => {
    // Add event functionality
    const addEventButtons = document.querySelectorAll("button.add-event");

    addEventButtons.forEach((button) => {
        button.addEventListener("click", (e) => {
            e.preventDefault();
            e.stopPropagation();

            const dayElement = button.closest("x-day");
            const dayNumber = button.parentElement?.querySelector("span.day-number")?.textContent;

            // Check if input already exists
            const existingInput = dayElement?.querySelector(
                "input.add-event-input",
            ) as HTMLInputElement;
            if (existingInput) {
                existingInput.focus();
                return;
            }

            // Create input element
            const input = document.createElement("input");
            input.type = "text";
            input.className = "add-event-input";
            input.placeholder = "Add Event";
            input.style.cssText = `
                width: 100%;
                padding: 0.25rem;
                border: 1px solid #ccc;
                border-radius: 4px;
                font-size: 0.75rem;
                margin-top: 0.25rem;
                background: #fff;
            `;

            // Add event listeners for input
            input.addEventListener("keydown", (e) => {
                if (e.key === "Enter") {
                    const eventText = input.value.trim();
                    if (eventText) {
                        console.log(`Adding event "${eventText}" to day ${dayNumber}`);
                        // Here you would typically make an API call to save the event
                    }
                    input.remove();
                } else if (e.key === "Escape") {
                    input.remove();
                }
            });

            input.addEventListener("blur", () => {
                setTimeout(() => input.remove(), 100);
            });

            // Add input to day and focus
            dayElement?.appendChild(input);
            input.focus();
        });
    });
});

/* Event Agenda - semantic nested CSS */
x-layout {
    display: grid;
    grid-template-columns: 240px 1fr;
    gap: 1rem;
}

x-legend {
    background: #fff;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #ddd;
    align-self: start;

    h2 {
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    ul {
        list-style: none;
        padding: 0;

        li {
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
    }

    span.dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;

        &.live { background: #ffe0a8; }
        &.webinar { background: #b3d8fa; }
        &.zoom { background: #ffcfc4; }
        &.virtual { background: #bdecd6; }
        &.strategy { background: #e7c7ec; }
        &.training { background: #c8e7a7; }
        &.deadline { background: #f8bcbc; }
    }
}

x-view-toggle {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-bottom: 1rem;

    a {
        background: #f8f9fa;
        border: 1px solid #ddd;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        text-decoration: none;
        font-size: 0.9rem;
        color: #666;
        transition: all 0.2s ease;

        &:hover {
            background: #e9ecef;
            color: #333;
        }

        &.primary {
            background: #0d6efd;
            color: white;
            border-color: #0077cc;
        }

        &.secondary {
            background: #f8f9fa;
            color: #666;
            border-color: #ddd;
        }
    }
}

x-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 6px;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.05);

    h1 {
        margin: 0;
        font-size: 1.5rem;
    }

    x-nav-calendar button {
        font-size: 1.2rem;
        background: none;
        border: none;
        cursor: pointer;
    }
}

x-agenda-content {
    flex: 1;
}

section.event {
    background: #fff;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 6px;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.05);

    .event-date {
        font-weight: bold;
        font-size: 1.1rem;
        color: #333;
        margin-bottom: 0.2rem;
    }

    a.event-title {
        color: #0077cc;
        text-decoration: none;
        font-weight: bold;
        display: inline-block;
        margin-bottom: 0.5rem;
        font-size: 1rem;

        &:hover {
            text-decoration: underline;
        }
    }

    .event-time {
        font-weight: bold;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
        color: #555;
    }

    p {
        margin-bottom: 1rem;
        line-height: 1.5;
        color: #333;

        strong {
            color: #333;
        }
    }

    ul.fa-checklist {
        list-style: none;
        padding-left: 0;
        margin: 0.8rem 0;

        li {
            margin-bottom: 0.6rem;
            font-size: 0.95rem;
            display: flex;
            align-items: flex-start;
            line-height: 1.4;

            i.fas.fa-check {
                color: #28a745;
                margin-right: 0.5rem;
                font-size: 1rem;
                flex-shrink: 0;
                margin-top: 0.1rem;
            }
        }
    }
}

/* Badge styles targeting the global label-tag system */
label-tag {
    font-size: .80rem;
    margin-bottom: .50rem;

    &.live { background-color: #ffe0a8; color: #000; }
    &.webinar { background-color: #b3d8fa; color: #000; }
    &.zoom { background-color: #ffcfc4; color: #000; }
    &.virtual { background-color: #bdecd6; color: #000; }
    &.strategy { background-color: #e7c7ec; color: #000; }
    &.training { background-color: #c8e7a7; color: #000; }
    &.deadline { background-color: #f8bcbc; color: #000; }
}

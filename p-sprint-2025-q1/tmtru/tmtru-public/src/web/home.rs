#[approck::http(GET /home/<USER>
pub mod page {
    pub async fn request(identity: Identity, doc: Document) -> Response {
        use maud::html;

        // If user is already logged in, redirect them to dashboard
        if identity.is_logged_in() {
            return Response::Redirect(Redirect::see_other("/dashboard/".to_string()));
        }

        doc.set_title("tmtru");

        doc.add_body(html!(
            div.bux-narrow-md {
                h1 { "Welcome to tmtru" }
                p { "This is a simple tmtru application." }
                hr;
                p {
                    (bux::button::link::label_class("Login", "/auth/", "primary"))
                }
            }
        ));

        Response::HTML(doc.into())
    }
}

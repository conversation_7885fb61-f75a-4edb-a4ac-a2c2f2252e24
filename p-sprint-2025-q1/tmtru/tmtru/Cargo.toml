[package]
name = "tmtru"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.approck.app]
port = 3016
docmap.auth-fence.DocumentPublic = "DocumentPublic"

extends = [
    "approck",
    "appstruct",
    "auth-fence",
    "bux",
    "granite",
    "tmtru-admin",
    "tmtru-public",
    "tmtru-zero",
    "api-openai",
]

[dependencies]
appstruct = { workspace = true }
tmtru-admin = { path = "../tmtru-admin" }
tmtru-public = { path = "../tmtru-public" }
tmtru-zero = { path = "../tmtru-zero" }
api-openai = { workspace = true }
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
auth-fence = { workspace = true }
approck-postgres = { workspace = true }
approck-redis = { workspace = true }

clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
maud = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
toml = { workspace = true }

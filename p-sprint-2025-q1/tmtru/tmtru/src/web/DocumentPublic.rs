bux::document! {

    pub struct DocumentPublic {}

    impl DocumentPublic {
        pub fn new(
            _app: &'static crate::AppStruct,
            _identity: &crate::IdentityStruct,
            req: &approck::server::Request,
        ) -> Self {
            use bux::document::Base;

            let mut this = Self {
                ..Default::default()
            };

            this.set_uri(req.path());
            this.set_title("tmtru");
            this.set_site_name("tmtru");

            // Add Google Fonts
            this.add_head(maud::html!(
                link href="https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&display=swap');" rel="stylesheet";
            ));

            this
        }
    }

    impl bux::document::Base for DocumentPublic {
        fn render_body(&self) -> maud::Markup {
            use bux::document::Base;
            use maud::html;
            html!(
                layout-wrapper-outer {
                    logo-wrapper {
                        img #tmt src="https://asset7.net/tmt-2025-demo/logos/tmt-white.png" {}
                        img #trupeer src="https://asset7.net/tmt-2025-demo/logos/trupeer-logo-white.png" {}
                    }
                    (Base::render_body_inner(self))
                }
            )
        }
    }

    impl bux::document::PageNav for DocumentPublic {}
    impl auth_fence::DocumentPublic for DocumentPublic {}
}

use crate::IdentityStruct;

impl bux::Identity for IdentityStruct {
    fn role(&self) -> String {
        match self {
            IdentityStruct::Anonymous => "anonymous".to_string(),
            IdentityStruct::User(_user_info) => "user".to_string(),
        }
    }
    fn name(&self) -> Option<String> {
        match self {
            IdentityStruct::Anonymous => None,
            IdentityStruct::User(user_info) => Some(user_info.name.clone()),
        }
    }
    fn email(&self) -> Option<String> {
        match self {
            IdentityStruct::Anonymous => None,
            IdentityStruct::User(user_info) => user_info.email.clone(),
        }
    }
    fn avatar_uri(&self) -> Option<String> {
        match self {
            IdentityStruct::Anonymous => None,
            IdentityStruct::User(user_info) => user_info.avatar_uri.clone(),
        }
    }
}

//! # Twilio SMS API Module
//!
//! This module provides SMS functionality through the Twilio API.
//!
//! ## Usage
//!
//! ```ignore
//! // In your application code:
//! let result = app.twilio().send_sms("support", "+15551234567", "Hello from <PERSON><PERSON><PERSON>!").await?;
//! ```
//!
//! ## Configuration
//!
//! Configure in your app's config file:
//! ```toml
//! [api_twilio]
//! key_sid = "AC9086bafa42b30433dbd64eb7f5db570a"
//! key_secret = "your_auth_token_here"
//!
//! [api_twilio.sender]
//! support = "+15551234567"
//! alerts = "+15559876543"
//! ```

pub mod api;
pub mod web;

use std::collections::HashMap;
use std::time::Instant;

pub trait App: approck::App + approck_postgres::App {
    fn twilio(&self) -> &ModuleStruct;
}

pub trait Identity: approck::Identity {
    fn web_usage(&self) -> bool {
        self.is_admin()
    }
    fn api_usage(&self) -> bool {
        self.is_admin()
    }
    fn is_admin(&self) -> bool;
}

pub trait Document: bux::document::Base {}

#[derive(serde::Deserialize)]
pub struct ModuleConfig {
    pub key_sid: String,
    pub key_secret: String,
    pub sender: HashMap<String, String>,
}

pub struct ModuleStruct {
    pub key_sid: String,
    pub key_secret: String,
    pub sender_map: HashMap<String, String>,
}

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;
    fn new(config: Self::Config) -> granite::Result<Self> {
        Ok(Self {
            key_sid: config.key_sid,
            key_secret: config.key_secret,
            sender_map: config.sender,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}

impl ModuleStruct {
    pub async fn send_sms<A: App>(
        &self,
        app: &A,
        sender_key: &str,
        to: &str,
        message: &str,
    ) -> granite::Result<()> {
        let from_number = self
            .sender_map
            .get(sender_key)
            .ok_or_else(|| granite::Error::validation(format!("Unknown sender: {sender_key}")))?;

        self.send_sms_with_logging(app, sender_key, from_number, to, message)
            .await
    }

    async fn send_sms_with_logging<A: App>(
        &self,
        app: &A,
        sender_key: &str,
        from_number: &str,
        to_number: &str,
        message: &str,
    ) -> granite::Result<()> {
        let request_start = Instant::now();
        let request_ts = chrono::Utc::now();

        let request_data = serde_json::json!({
            "From": from_number,
            "To": to_number,
            "Body": message
        });

        let result = send_sms(
            &self.key_sid,
            &self.key_secret,
            from_number,
            to_number,
            message,
        )
        .await;

        let response_ts = chrono::Utc::now();
        let duration_ms = request_start.elapsed().as_millis() as i32;

        let (success, response_status, response_text, response_error) = match &result {
            Ok(_) => (true, Some(200), Some("Success".to_string()), None),
            Err(e) => (false, None, None, Some(e.to_string())),
        };

        // Log the API call
        let dbcx = app.postgres_dbcx().await?;
        let _ = granite::pg_execute!(
            db = dbcx;
            args = {
                $request_ts: &request_ts,
                $response_ts: &response_ts,
                $sender_key: &sender_key,
                $from_number: &from_number,
                $to_number: &to_number,
                $message_body: &message,
                $request_data: &request_data,
                $response_status: &response_status,
                $response_text: &response_text,
                $response_error: &response_error,
                $duration_ms: &duration_ms,
                $success: &success,
            };
            INSERT INTO api_twilio.log_api_send (
                request_ts, response_ts, sender_key, from_number, to_number,
                message_body, request_data, response_status, response_text,
                response_error, duration_ms, success
            ) VALUES ($request_ts, $response_ts, $sender_key, $from_number, $to_number,
                     $message_body, $request_data, $response_status, $response_text,
                     $response_error, $duration_ms, $success)
        )
        .await;

        result
    }
}

async fn send_sms(
    key_sid: &str,
    key_secret: &str,
    from_number: &str,
    to_number: &str,
    message: &str,
) -> granite::Result<()> {
    let client = reqwest::Client::new();

    let url = format!("https://api.twilio.com/2010-04-01/Accounts/{key_sid}/Messages.json");

    let form_data = [("From", from_number), ("To", to_number), ("Body", message)];

    let response = client
        .post(&url)
        .basic_auth(key_sid, Some(key_secret))
        .form(&form_data)
        .send()
        .await
        .map_err(|e| granite::Error::api_request_error(format!("Failed to send SMS: {e}")))?;

    if response.status().is_success() {
        Ok(())
    } else {
        let status = response.status();
        let body = response
            .text()
            .await
            .unwrap_or_else(|_| "Unable to read response body".to_string());
        Err(granite::Error::api_request_error(format!(
            "Twilio API error {status}: {body}"
        )))
    }
}

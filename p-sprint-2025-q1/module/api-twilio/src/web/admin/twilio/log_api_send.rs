#[approck::http(GET /admin/twilio/log-api-send?limit=Option<i64>&offset=Option<i64>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        qs: QueryString,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Twilio Send Logs");
        doc.set_body_display_fluid();
        // doc.page_nav_enable_go_back("/admin/twilio/");

        let limit = qs.limit.unwrap_or(50);
        let offset = qs.offset.unwrap_or(0);

        let logs_output = crate::api::admin::twilio::log::list::call(
            app,
            identity,
            crate::api::admin::twilio::log::list::Input {
                limit: Some(limit),
                offset: Some(offset),
            },
        )
        .await?;
        let logs = logs_output.logs;

        let mut dt = bux::component::detail_table(logs);

        dt.add_column("Sent", |log| {
            html! {
                (log.create_ts.clone())
            }
        });

        dt.add_column("Sender", |log| html! { (log.sender_key) });

        dt.add_column("From", |log| html! { (log.from_number) });

        dt.add_column("To", |log| html! { (log.to_number) });

        dt.add_column("Message", |log| {
            html! {
                div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;" {
                    (if log.message_body.len() > 50 {
                        format!("{}...", &log.message_body[..50])
                    } else {
                        log.message_body.clone()
                    })
                }
            }
        });

        dt.add_column("Duration", |log| {
            html! {
                @if let Some(duration) = log.duration_ms {
                    (duration) "ms"
                } @else {
                    "-"
                }
            }
        });

        dt.add_column("Status", |log| {
            html! {
                @if log.success {
                    span class="badge bg-success" { "Success" }
                } @else {
                    span class="badge bg-danger" { "Failed" }
                }
            }
        });

        dt.add_column("Response", |log| {
            html! {
                @if let Some(status) = log.response_status {
                    "HTTP " (status)
                } @else if let Some(error) = &log.response_error {
                    span class="text-danger" title=(error) {
                        (if error.len() > 30 {
                            format!("{}...", &error[..30])
                        } else {
                            error.clone()
                        })
                    }
                } @else {
                    "-"
                }
            }
        });

        doc.add_body(html! {
            div class="row" {
                div class="col-md-12" {
                    h1 { "Twilio Send Logs" }
                    p { "View detailed logs of all SMS messages sent via Twilio API." }
                }
            }
            (dt)
        });

        Ok(Response::HTML(doc.into()))
    }
}

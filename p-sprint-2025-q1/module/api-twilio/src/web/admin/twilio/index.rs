#[approck::http(GET /admin/twilio/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("Twilio Admin Dashboard");

        let summary_output = crate::api::admin::twilio::log::summary::call(
            app,
            identity,
            crate::api::admin::twilio::log::summary::Input {},
        )
        .await?;
        let summary = summary_output.summary;

        doc.add_body(html! {
            panel {
                content {
                    h2 { "Twilio Admin Dashboard" }
                    p { "Manage and monitor Twilio SMS API usage." }
                }
            }

            grid-3 {
                panel {
                    header {
                        h5 { "Total Messages" }
                    }
                    content {
                        h2 { (summary.total_messages) }
                    }
                }
                panel {
                    header {
                        h5 { "Successful Messages" }
                    }
                    content {
                        h2 { (summary.successful_messages) }
                    }
                }
                panel {
                    header {
                        h5 { "Failed Messages" }
                    }
                    content {
                        h2 { (summary.failed_messages) }
                    }
                }
            }

            panel {
                header {
                    h5 { "Quick Actions" }
                }
                content {
                    a.btn.primary href="/admin/twilio/log-api-send" {
                        "View Send Logs"
                    }
                    " "
                    a.btn.secondary href="/admin/twilio/test" {
                        "Send Test SMS"
                    }
                }
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}

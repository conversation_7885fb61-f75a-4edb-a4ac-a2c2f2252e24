//-------------------------------------------------------------------------------------------------
// 1. Import Components
import "./test.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/textarea/string.mts";
import "@bux/input/select/nilla.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code
import { SE } from "@granite/lib.mts";
import { admin_twilio_test_send } from "@crate/api/admin/twilio/testλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import BuxInputSelectNilla from "@bux/input/select/nilla.mts";
import FormPanel from "@bux/component/form_panel.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $sender_key: BuxInputSelectNilla<string> = SE($form, "[name=sender_key]");
const $to_number: BuxInputTextString = SE($form, "[name=to_number]");
const $message: BuxInputTextareaString = SE($form, "[name=message]");

// -------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

new FormPanel({
    $form,
    api: admin_twilio_test_send.api,
    on_cancel: go_back,

    err: (errors) => {
        $sender_key.set_e(errors.sender_key);
        $to_number.set_e(errors.to_number);
        $message.set_e(errors.message);
    },

    get: () => {
        return {
            sender_key: $sender_key.value || "",
            to_number: $to_number.value,
            message: $message.value,
        };
    },

    set: (value) => {
        $sender_key.value = value.sender_key;
        $to_number.value = value.to_number;
        $message.value = value.message;
    },

    out: (output) => {
        if (output.success) {
            go_next("/admin/twilio/");
        } else {
            // Stay on same page and show error
            console.error(output.message);
        }
    },
});

// -------------------------------------------------------------------------------------------------
// 5. Write Code
// -------------------------------------------------------------------------------------------------

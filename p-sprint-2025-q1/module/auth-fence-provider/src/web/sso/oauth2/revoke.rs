#[approck::http(POST /sso/oauth2/revoke; AUTH None; return J<PERSON><PERSON>;)]
pub mod revoke {
    #[granite::gtype(RsType, RsTypeDecode, RsTypeEncode)]
    struct PostJson {
        token: String,
    }

    pub async fn request(post: Post<PERSON><PERSON>, req: Request) -> Result<Response> {
        approck::info!("OAuth2 token revocation request initiated");
        let ip_addr = req.remote_address().ip().to_string();
        approck::debug!("Client IP: {}", ip_addr);
        approck::debug!("Token to revoke: {}", post.token);

        // Revoke the token
        approck::debug!("Attempting to revoke access token");
        match crate::sso::oauth2::token::revoke_access_token() {
            Ok(_) => {
                approck::info!("OAuth2 token revocation successful");
                Ok(Response::JSON(
                    serde_json::json!({
                        "message": "Token revoked"
                    })
                    .into(),
                ))
            }
            Err(e) => {
                approck::info!("OAuth2 token revocation failed: {}", e);
                let mut headers = approck::HeaderMap::new();
                headers.append("Content-Type", "application/json".parse().unwrap());
                headers.append("Cache-Control", "no-store".parse().unwrap());
                headers.append("Pragma", "no-cache".parse().unwrap());

                Ok(Response::JSON(JSON {
                    headers,
                    status: approck::StatusCode::BAD_REQUEST,
                    content: serde_json::json!({
                        "error": "invalid_request",
                        "error_description": "Invalid token"
                    })
                    .to_string(),
                }))
            }
        }
    }
}

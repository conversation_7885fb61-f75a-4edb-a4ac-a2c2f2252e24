#[approck::http(GET /sso/oauth2/redirect?
    return_to=String;
    AUTH None; return Redirect;
)]
pub mod authorize {
    fn get_redis_key_returnto(key: &str) -> String {
        format!("oauth2:auth:original_uri:{key}")
    }

    pub async fn request(
        qs: QueryString,
        redis: Redis,
        req: Request,
        _db: Postgres,
        _identity: Identity,
    ) -> Result<Response> {
        //use crate::sso::oauth2::log::{log_oauth2_event, OAuth2LogAction, OAuth2LogResult};
        let ip_addr = req.remote_address().ip();
        let _ip_str = ip_addr.to_string();

        let return_to = qs.return_to;

        // we're coming back from a login redirect
        // pull it from redis and redirect
        approck::debug!("Redirecting back to original URI");
        // pull the original url from redis
        let original_uri = match redis
            .get_val::<String>(&get_redis_key_returnto(&return_to))
            .await
        {
            Ok(val) => val,
            Err(e) => {
                approck::info!(
                    "OAuth2 authorization failed: Error getting original URI from redis: {}",
                    e
                );
                return Err(e);
            }
        };
        // delete the key from redis
        redis.del(&return_to).await?;
        // redirect to the original url
        return Ok(Response::Redirect(original_uri.as_str().into()));
    }
}

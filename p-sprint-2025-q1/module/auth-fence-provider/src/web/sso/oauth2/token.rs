#[approck::http(POST /sso/oauth2/token; AUTH None; return JSON;)]
pub mod token {
    use crate::sso::oauth2::{auth, client, token::create_token};

    // Standard oauth2 token request comes in as `application/x-www-form-urlencoded`
    // with the client:secret in the Basic Auth header (base64 encoded)
    #[derive(Debug)]
    pub struct PostForm {
        grant_type: String,
        code: String,
        code_verifier: Option<String>,
        redirect_uri: String,
    }

    // TODO: Implement rate limiting
    pub async fn request(
        app: App,
        redis: Redis,
        db: Postgres,
        req: Request,
        post: PostForm,
    ) -> Result<Response> {
        approck::info!("OAuth2 token request initiated");
        approck::debug!("Request form data: {:?}", post);

        let ip_addr = req.remote_address().ip().to_string();
        approck::debug!("Client IP: {}", ip_addr);

        let (client_uuid, client_secret) = match req.auth_basic() {
            Some((id, secret)) => {
                approck::debug!("Processing Basic Auth credentials");
                let id = match granite::Uuid::parse_str(id.as_str()) {
                    Ok(uuid) => {
                        approck::debug!("Client UUID parsed: {}", uuid);
                        uuid
                    }
                    Err(_e) => {
                        approck::info!("OAuth2 token request failed: Invalid client id format");
                        return Err(granite::process_error!("Invalid client id"));
                    }
                };
                // let's replace the plus signs with spaces first to avoid replacing intentional plus signs
                let secret = urlencoding::decode(secret.replace("+", " ").as_str())
                    .expect("utf-8")
                    .to_string();
                approck::debug!("Client secret decoded successfully");
                (id, secret)
            }
            // TODO: return a 401 response if the grant_type is not "authorization_code"
            None => {
                approck::info!("OAuth2 token request failed: Missing client credentials");
                return Err(granite::process_error!("Missing client credentials"));
            }
        };

        let _grant_type = post.grant_type;
        let code = post.code;
        let code_verifier = post.code_verifier;
        let redirect_uri = post.redirect_uri;
        let code_verifier = match code_verifier {
            Some(verifier) => {
                approck::debug!("Code verifier provided {}", verifier);
                verifier
            }
            None => {
                approck::debug!("No code verifier provided");
                "".to_string()
            }
        };
        approck::info!("Grant type: {}", _grant_type);
        approck::debug!("Authorization code: {}", code);
        approck::info!("Redirect URI: {}", redirect_uri);

        // Load the client
        approck::debug!(
            "Loading client information for client_uuid: {}",
            client_uuid
        );
        let client = match client::load_client(&db, &client_uuid).await {
            Ok(client) => {
                approck::debug!("Client loaded successfully: {}", client.name);
                client
            }
            Err(e) => {
                approck::info!("OAuth2 token request failed: Error loading client: {}", e);
                return Err(e);
            }
        };

        // Validate the client secret
        approck::debug!("Validating client secret");
        match client.validate_secret(&client_secret) {
            Ok(_) => approck::debug!("Client secret validation successful"),
            Err(e) => {
                approck::info!("OAuth2 token request failed: Invalid client secret: {}", e);
                return Err(e);
            }
        };

        // Validate the IP address
        approck::debug!("Validating client IP address: {}", ip_addr);
        match client.validate_ip_address(ip_addr.as_str()) {
            Ok(_) => approck::debug!("IP address validation successful"),
            Err(e) => {
                approck::info!(
                    "OAuth2 token request failed: IP address validation failed: {}",
                    e
                );
                return Err(e);
            }
        };

        // Validate the redirect URI
        approck::debug!("Validating redirect URI: {}", redirect_uri);
        match client.validate_redirect_uri(&redirect_uri) {
            Ok(_) => approck::debug!("Redirect URI validation successful"),
            Err(e) => {
                approck::info!(
                    "OAuth2 token request failed: Redirect URI validation failed: {}",
                    e
                );
                return Err(e);
            }
        };

        // Validate that the code_verifier is provided and PKCE is enabled
        if client.pkce_enabled && code_verifier.is_empty() {
            approck::info!("OAuth2 token request failed: Code verifier is required");
            return Err(granite::Error::new(granite::ErrorType::Validation)
                .set_external_message("Code verifier is required".to_string()));
        }

        // Load the auth code
        approck::debug!("Loading authorization code from Redis");
        let auth_code = match auth::load_auth_code(&mut redis, &code).await {
            Ok(auth_code) => {
                approck::debug!("Authorization code loaded successfully");
                auth_code
            }
            Err(e) => {
                approck::info!(
                    "OAuth2 token request failed: Authorization code is invalid or expired: {}",
                    e
                );
                return Err(granite::Error::new(granite::ErrorType::Authentication)
                    .set_external_message(
                        "The authorization code is invalid or has expired".to_string(),
                    )
                    .add_context(e));
            }
        };

        // validate that the auth code belongs to this client
        approck::debug!("Validating authorization code belongs to client");
        if auth_code.client_uuid != client.client_uuid {
            approck::info!(
                "OAuth2 token request failed: Authorization code does not belong to this client"
            );
            // auth code does not belong to this client
            return Err(granite::Error::new(granite::ErrorType::Authentication)
                .set_external_message(
                    "The authorization code is corrupted, invalid or has expired".to_string(),
                ));
        }
        approck::debug!("Authorization code client validation successful");

        // validate the code challenge
        match (client.pkce_enabled, code_verifier.is_empty()) {
            (true, true) => {
                approck::info!("OAuth2 token request failed: Code verifier is required");
                return Err(granite::Error::new(granite::ErrorType::Validation)
                    .set_external_message("Code verifier is required".to_string()));
            }
            (true, false) => {
                approck::debug!("Validating code challenge with verifier");
                if !auth_code.verify_code_challenge(code_verifier) {
                    approck::info!(
                        "OAuth2 token request failed: Code challenge verification failed"
                    );
                    return Err(granite::Error::new(granite::ErrorType::Authentication)
                        .set_external_message(
                        "The authorization code's code verifier did not match the original code challenge"
                            .to_string(),
                    ));
                }
                approck::debug!("Code challenge verification successful");
            }
            (false, _) => {
                approck::debug!(
                    "OAuth2 token request skipping code challenge validation because PKCE is not enabled for client {}",
                    client.client_uuid
                );
            }
        }

        // create the access token
        approck::debug!("Creating access token");
        let auth_provider = app.auth_fence_provider();
        let aes_key = auth_provider.aes_key();
        let access_token = match create_token::call(
            &aes_key,
            create_token::Input {
                client_uuid: auth_code.client_uuid,
                identity_uuid: auth_code.identity_uuid,
                redirect_uri: auth_code.redirect_uri,
                scopes: auth_code.scopes.clone(),
                expires_in: auth_provider.access_token_ttl(),
                ip: ip_addr,
            },
        ) {
            Ok(code) => code,
            Err(e) => {
                approck::info!("OAuth2 authorization failed to create access token: {}", e);
                return Err(e);
            }
        };

        approck::debug!("Access token created with scopes: {:?}", auth_code.scopes);

        // store the access token
        approck::debug!("Storing access token in Redis");
        match access_token.store_access_token(&aes_key, &mut redis).await {
            Ok(_) => approck::debug!("Access token stored successfully"),
            Err(e) => {
                approck::info!(
                    "OAuth2 token request failed: Error storing access token: {}",
                    e
                );
                return Err(e);
            }
        };

        // create the proper headers for the access token response
        approck::debug!("Creating response headers");
        let mut headers = approck::HeaderMap::new();
        headers.append(
            "Content-Type",
            "application/json;charset=UTF-8".parse().unwrap(),
        );
        headers.append("Cache-Control", "no-store".parse().unwrap());
        headers.append("Pragma", "no-cache".parse().unwrap());

        // get the token response
        approck::debug!("Generating token response");
        let token_response = access_token.get_token_reponse(&aes_key);

        approck::info!("OAuth2 token request successful, returning access token");
        Ok(Response::JSON(JSON {
            headers,
            status: approck::StatusCode::OK,
            content: token_response,
        }))
    }
}

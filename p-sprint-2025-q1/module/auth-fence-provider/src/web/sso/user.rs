//  - POST /sso/user
//    - User info endpoint: Fetch basic user information
//    - Validate access token
//      - Validate scopes
//    - Validate session and consent
//    - Payload
//      ```json
//      {
//          "access_token": "access_token"
//      }
//      ```
//    - Response
//      ```json
//      {
//          "sub": "identity_uuid",
//          "name": "<PERSON>",
//          "email": "<EMAIL>"
//      }
//      ```
#[approck::http(POST /sso/user; AUTH None; return JSON;)]
pub mod userinfo {
    use crate::sso::user::load;

    #[derive(serde::Serialize)]
    pub struct BasicUserInfo {
        pub id: granite::Uuid,
        pub email: String,
        pub first_name: String,
        pub last_name: String,
        pub avatar_uri: Option<String>,
    }

    pub async fn request(app: App, identity: Identity, req: Request) -> Result<Response> {
        approck::info!("OAuth2 userinfo request initiated");

        if !identity.scope_profile() {
            approck::info!("OAuth2 userinfo request failed: Missing required scope");
            return Err(granite::Error::new(granite::ErrorType::Authentication)
                .set_external_message("Missing required scope".to_string()));
        }

        let ip_addr = req.remote_address().ip().to_string();
        approck::debug!("Client IP: {}", ip_addr);

        let identity_uuid = match identity.identity_uuid() {
            Some(identity_uuid) => identity_uuid,
            None => {
                approck::info!("OAuth2 userinfo request failed: User not logged in");
                return Err(granite::Error::new(granite::ErrorType::Authentication)
                    .set_external_message("User not logged in".to_string()));
            }
        };

        let userinfo = match load::execute(app, load::Input { identity_uuid }).await {
            Ok(userinfo) => {
                approck::debug!("User information loaded successfully");
                userinfo
            }
            Err(e) => {
                approck::info!(
                    "OAuth2 userinfo request failed: Error loading user information: {}",
                    e
                );
                return Err(granite::Error::new(granite::ErrorType::ProcessError).add_context(e));
            }
        };

        // Return the user info
        approck::info!("OAuth2 userinfo request successful");
        Ok(Response::JSON(
            serde_json::to_string(&userinfo).unwrap().into(),
        ))
    }
}

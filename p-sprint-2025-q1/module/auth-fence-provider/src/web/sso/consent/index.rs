//  - GET /identity/sso/consent/
//    - Shows grant consent screen for user
//    - Parameters:
//      ```rust
//      {
//          "client_uuid": "client_uuid",
//          "scope": ["openid", "email", "profile"],
//          "state": "optional_state",
//          "redirect_uri": "https://example.com/callback",
//          "response_type": "code"
//      }
//      ```
//    - Response: HTML consent page
#[approck::http(GET /sso/consent/?
    client_id=String // String instead of Uuid gives us a chance to return proper error
    &state=String
    &response_type=String
    &scope=String
    &code_challenge=String
    &code_challenge_method=String
    &redirect_uri=String; AUTH is_logged_in; return HTML|Redirect;
)]
pub mod index {
    use crate::sso::oauth2::consent;

    pub async fn request(
        doc: Document,
        qs: QueryString,
        app: App,
        db: Postgres,
        identity: Identity,
    ) -> Result<Response> {
        approck::info!("OAuth2 consent page request initiated");
        approck::debug!("Client ID: {}", qs.client_id);
        approck::debug!("Requested scopes: {}", qs.scope);
        approck::debug!("Redirect URI: {}", qs.redirect_uri);

        // we can expect this not to fail because of AUTH is_logged_in
        let identity_uuid = identity.identity_uuid().unwrap();

        let client_uuid = match granite::Uuid::parse_str(&qs.client_id) {
            Ok(uuid) => uuid,
            Err(e) => {
                approck::info!("OAuth2 consent page failed: Invalid client_id: {}", e);
                return Err(granite::process_error!("Invalid client_id provided"));
            }
        };

        let auth_params = [
            ("client_id", qs.client_id.as_str()),
            ("state", qs.state.as_str()),
            ("response_type", qs.response_type.as_str()),
            ("scope", qs.scope.as_str()),
            ("code_challenge", qs.code_challenge.as_str()),
            ("code_challenge_method", qs.code_challenge_method.as_str()),
            ("redirect_uri", qs.redirect_uri.as_str()),
        ];

        let scope_param = &qs.scope;
        let scope_set = scope_param
            .split(" ")
            .map(|s| s.to_string())
            .collect::<Vec<String>>();

        approck::debug!("Client UUID: {}", client_uuid);
        approck::debug!("Parsed scopes: {:?}", scope_set);

        approck::debug!("Loading client information for client_id: {}", qs.client_id);
        let client = match crate::sso::oauth2::client::load_client(
            &app.postgres_dbcx().await?,
            &client_uuid,
        )
        .await
        {
            Ok(client) => {
                approck::debug!("Client loaded successfully: {}", client.name);
                client
            }
            Err(e) => {
                approck::info!("OAuth2 consent page failed: Error loading client: {}", e);
                return Err(e);
            }
        };

        // TODO: Client needs full validation here too, no tricksies

        // Double check that the user has not already granted consent
        approck::debug!(
            "Checking for existing user consent for client ({}) and identity ({})",
            qs.client_id,
            identity_uuid
        );
        match consent::load_consent(app, &client_uuid, &identity_uuid).await {
            Ok(Some(consent)) => {
                approck::info!("OAuth2 consent found, validating against requested scopes");
                // Redirect back to authorization flow
                let origin_uri = granite::util::make_uri("/sso/oauth2/auth", &auth_params);

                match consent.validate_consent(&scope_set) {
                    Ok(true) => {
                        approck::debug!(
                            "Consent scope validation successful, redirecting to authorization flow"
                        );
                        return Ok(Response::Redirect(origin_uri.as_str().into()));
                    }
                    Ok(false) => {
                        // consent is invalid, continue with consent page (do nothing here)
                        approck::info!(
                            "OAuth2 consent scope validation did not pass, showing consent page"
                        );
                    }
                    Err(e) => {
                        approck::info!(
                            "OAuth2 consent page failed: error validating consent scopes: {}",
                            e
                        );
                        return Err(e);
                    }
                }
            }
            Ok(None) => {
                approck::debug!("No existing consent found, showing consent page");
            }
            Err(e) => {
                approck::info!("OAuth2 consent page failed: Error loading consent: {}", e);
                return Err(e);
            }
        }

        // TODO: VALIDATE ALL PARAMS! OR CREATE AN "AUTH REQUEST CACHE"
        // Add all of the query string parameters back
        approck::debug!("Creating accept URI for consent form");
        let accept_uri = granite::util::make_uri("/sso/consent/accept", &auth_params);
        approck::debug!("Accept URI: {}", accept_uri);

        // TODO: Should there be record of consent denial?
        approck::debug!("Creating deny URI for consent form");
        let deny_uri = granite::util::make_uri(
            qs.redirect_uri.as_str(),
            &[("error", "access_denied"), ("state", qs.state.as_str())],
        );
        approck::debug!("Deny URI: {}", deny_uri);

        // Load the scopes
        let scopes = crate::sso::oauth2::scope::load_exact_scope_list(&db, &scope_set).await?;

        // TODO: Find a better display name for the app
        // TODO: Add information on the page about managing consent and user policy
        let app_name = "Smart Retirement Corp.";

        doc.add_body(maud::html! {
            div.container {
                h1 { "Sign in to " (client.name) }
                p { "By continuing, you agree to give " strong { (app_name) } " permission to share the following information with " strong { (client.name) } ":" }
                ul {
                    @for scope in scopes {
                        li { (scope.name) " - " (scope.description) }
                    }
                }
                p { "Do you wish to continue?" }
                form method="post" {
                    button.btn.btn-secondary type="submit" name="deny" formaction=(deny_uri) { "Cancel" }
                    " "
                    button.btn.btn-primary type="submit" name="accept" formaction=(accept_uri) { "Yes, I agree" }
                }
            }
        });

        approck::info!("OAuth2 consent page rendered successfully");
        Ok(Response::HTML(doc.into()))
    }
}

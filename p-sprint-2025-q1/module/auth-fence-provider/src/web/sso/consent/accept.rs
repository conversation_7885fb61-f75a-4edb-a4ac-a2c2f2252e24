#[approck::http(POST /sso/consent/accept?
    client_id=String
    &state=String
    &response_type=String
    &scope=String
    &code_challenge=String
    &code_challenge_method=String
    &redirect_uri=String;
    AUTH is_logged_in;
    return Redirect;
)]
pub mod index {
    use crate::sso::oauth2::consent;

    pub async fn request(
        qs: QueryString,
        app: App,
        redis: Redis,
        req: Request,
    ) -> Result<Response> {
        approck::info!("OAuth2 consent acceptance request initiated");
        approck::debug!("Client ID: {}", qs.client_id);
        approck::debug!("Requested scopes: {}", qs.scope);
        approck::debug!("Redirect URI: {}", qs.redirect_uri);

        let auth_fence = app.auth_fence_system();

        approck::debug!("Getting user identity from session token");
        let identity_uuid = match auth_fence
            .get_user_identity(&req.session_token(), &mut redis)
            .await?
        {
            Some(identity) => {
                approck::debug!("User identity found: {}", identity.identity_uuid);
                identity.identity_uuid
            }
            None => {
                approck::info!("OAuth2 consent acceptance failed: User not logged in");
                return Err(granite::process_error!("User not logged in"));
            }
        };

        approck::debug!("Parsing client UUID: {}", qs.client_id);
        let client_uuid = match granite::Uuid::parse_str(&qs.client_id) {
            Ok(uuid) => {
                approck::debug!("Client UUID parsed successfully: {}", uuid);
                uuid
            }
            Err(e) => {
                approck::info!(
                    "OAuth2 consent acceptance failed: Invalid client UUID format: {}",
                    e
                );
                return Err(e.into());
            }
        };

        approck::debug!("Parsing scopes: {}", qs.scope);
        let scopes = qs
            .scope
            .split(" ")
            .map(|s| s.to_string())
            .collect::<Vec<String>>();

        // TODO: VALIDATE ALL PARAMS! OR CREATE AN "AUTH REQUEST CACHE"
        // Record the consent
        approck::debug!(
            "Granting consent for client: {} and user: {}",
            client_uuid,
            identity_uuid
        );
        approck::debug!("Granting consent for scopes: {:?}", scopes);

        match consent::grant_consent(app, &client_uuid, &identity_uuid, &scopes).await {
            Ok(_) => approck::debug!("Consent granted successfully"),
            Err(e) => {
                approck::info!(
                    "OAuth2 consent acceptance failed: Error granting consent: {}",
                    e
                );
                return Err(e);
            }
        };

        // Redirect back to authorization flow
        approck::debug!("Creating redirect URI back to authorization flow");
        let origin_uri = granite::util::make_uri(
            "/sso/oauth2/auth",
            &[
                ("client_id", qs.client_id.to_string().as_str()),
                ("state", qs.state.as_str()),
                ("response_type", qs.response_type.as_str()),
                ("scope", qs.scope.as_str()),
                ("code_challenge", qs.code_challenge.as_str()),
                ("code_challenge_method", qs.code_challenge_method.as_str()),
                ("redirect_uri", qs.redirect_uri.as_str()),
            ],
        );
        approck::debug!("Redirecting to: {}", origin_uri);

        approck::info!("OAuth2 consent acceptance successful, redirecting to authorization flow");
        Ok(Response::Redirect(origin_uri.as_str().into()))
    }
}

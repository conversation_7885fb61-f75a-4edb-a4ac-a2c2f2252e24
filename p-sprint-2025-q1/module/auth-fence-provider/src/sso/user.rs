pub mod load {
    pub struct Input {
        pub identity_uuid: granite::Uuid,
    }

    #[derive(serde::Serialize, serde::Deserialize)]
    pub struct Output {
        pub id: granite::Uuid,
        pub first_name: String,
        pub last_name: String,
        pub email: String,
        pub avatar_uri: Option<String>,
    }

    pub async fn execute(app: &impl crate::App, input: Input) -> granite::Result<Output> {
        approck::debug!(
            "Loading user information for identity: {}",
            input.identity_uuid
        );

        let db = app.postgres_dbcx().await?;

        match granite::pg_row!(
            db = db;
            args = {
                $identity_uuid: &input.identity_uuid,
            };
            row = {
                identity_uuid: Uuid,
                name: String,
                email: String,
                avatar_uri: Option<String>,
            };
            SELECT
                identity_uuid,
                name,
                email,
                avatar_uri
            FROM
                auth_fence.identity
            WHERE TRUE
                AND identity_uuid = $identity_uuid::uuid
        )
        .await
        {
            Ok(row) => {
                // split the name into first and last name
                let (first_name, last_name) = match row.name.split_once(" ") {
                    Some((first_name, last_name)) => {
                        (first_name.to_string(), last_name.to_string())
                    }
                    None => (row.name.clone(), "".to_string()),
                };

                Ok(Output {
                    id: row.identity_uuid,
                    first_name,
                    last_name,
                    email: row.email,
                    avatar_uri: row.avatar_uri,
                })
            }
            Err(e) => {
                println!("SSO: Error getting user info: {e:?}");
                Err(granite::process_error!("SSO: Error getting user info").add_context(e))
            }
        }
    }
}

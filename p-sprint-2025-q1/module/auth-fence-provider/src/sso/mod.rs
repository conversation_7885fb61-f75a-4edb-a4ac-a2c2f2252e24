use aes_gcm::aead::AeadMut;
use aes_gcm::{Aes256Gcm, Key, KeyInit, Nonce, aead::consts::U12};
use base64_light::base64_decode;
use rand::rng;

pub mod oauth2;
pub mod user;

const NONCE_LEN: usize = 12;
const TAG_LEN: usize = 16;

// create nonce from uuidv7 - high guarantee of uniqueness
fn generate_nonce_from_uuidv7() -> Nonce<U12> {
    let uuid = granite::uuid_v7();
    let uuid_bytes = uuid.as_bytes();
    *Nonce::from_slice(&uuid_bytes[..12])
}

pub fn generate_random_code() -> String {
    use rand::Rng;

    let mut rng = rng();
    let random_bytes: [u8; 32] = rng.random();
    base64_light::base64url_encode_bytes(random_bytes.as_slice())
}

pub fn encrypt_data(key: &[u8], auth_code: &str) -> granite::Result<String> {
    // create the key/cipher
    let key = Key::<Aes256Gcm>::from_slice(key);
    let mut cipher = Aes256Gcm::new(key);

    // create the nonce
    let nonce = &generate_nonce_from_uuidv7();

    // encrypt
    let cipher_text = match cipher.encrypt(nonce, auth_code.as_bytes()) {
        Ok(encrypted_code) => {
            approck::info!("Data encryption success");
            encrypted_code
        }
        Err(e) => {
            approck::info!("Failed to encrypt data: {}", e);
            return Err(granite::process_error!("Error encrypting data").add_context(e));
        }
    };

    // combine nonce and ciphertext for storage (ciphertext = encrypted code + tag)
    let mut combined_storage_bytes = Vec::with_capacity(12 + cipher_text.len());
    combined_storage_bytes.extend_from_slice(nonce);
    combined_storage_bytes.extend_from_slice(&cipher_text);

    let encoded_storage_bytes = base64_light::base64_encode_bytes(&combined_storage_bytes);

    Ok(encoded_storage_bytes)
}

pub fn decrypt_data(key: &[u8], encoded_data: &str) -> granite::Result<String> {
    // base64 decode
    let decoded_encrypted_data = base64_decode(encoded_data);

    // Must be at least 28 characters (nonce + tag) encrypted
    if decoded_encrypted_data.len() < NONCE_LEN + TAG_LEN {
        return Err(granite::process_error!(
            "Failure decrypting: insufficient encrypted data length"
        ));
    }

    // split the data, nonce is first 12 bytes
    let (nonce_bytes, cipher_bytes) = &decoded_encrypted_data.split_at(12);

    let nonce: &Nonce<U12> = Nonce::from_slice(nonce_bytes);

    // create the key/cipher
    let key = Key::<Aes256Gcm>::from_slice(key);
    let mut cipher = Aes256Gcm::new(key);

    let cipher_text = match cipher.decrypt(nonce, cipher_bytes.as_ref()) {
        Ok(text) => match String::from_utf8(text) {
            Ok(decrypted_text) => decrypted_text,
            Err(e) => {
                return Err(granite::process_error!(
                    "Failed to parse decrypted bytes into readable string"
                )
                .add_context(e));
            }
        },
        Err(e) => {
            approck::info!("Failed to decrypt value: {}", e);
            return Err(granite::process_error!("Failed to decrypt value").add_context(e));
        }
    };

    Ok(cipher_text)
}

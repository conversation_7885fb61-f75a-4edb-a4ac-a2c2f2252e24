use crate::sso::decrypt_data;

#[derive(serde::Serialize, serde::Deserialize)]
pub struct OAuth2AccessToken {
    access_token: String,
    pub client_uuid: granite::Uuid,
    pub identity_uuid: granite::Uuid,
    pub scopes: Vec<String>,
    pub create_ts: granite::DateTime<granite::Utc>,
    pub expires_in: i64,
    pub ip: String,
}

fn redis_key(token: &str) -> String {
    // get the hash of the access token
    let token_hash = granite::sha256_str(token);

    format!("oauth2:access_token:{token_hash}")
}

pub mod create_token {
    use crate::sso::{encrypt_data, generate_random_code};

    use super::OAuth2AccessToken;

    pub struct Input {
        pub client_uuid: granite::Uuid,
        pub identity_uuid: granite::Uuid,
        pub scopes: Vec<String>,
        pub redirect_uri: String,
        pub expires_in: i64,
        pub ip: String,
    }

    pub fn call(aes_key: &[u8], input: Input) -> granite::Result<OAuth2AccessToken> {
        approck::debug!(
            "Creating new access token for client: {}",
            input.client_uuid
        );
        let create_ts = granite::Utc::now();

        // TODO: Should TTL be per-client?
        let expires_in = input.expires_in;
        approck::debug!("Access token TTL: {} seconds", expires_in);

        let access_token = generate_random_code();

        let encrypted_access_token = match encrypt_data(aes_key, &access_token) {
            Ok(encrypted_bytes) => {
                approck::debug!("Encrypted access token successfully");
                encrypted_bytes
            }
            Err(e) => {
                approck::info!("Encrypting access token failed: {}", e);
                return Err(granite::process_error!("Error encrypting access token").add_context(e));
            }
        };

        let token = OAuth2AccessToken {
            access_token: encrypted_access_token,
            client_uuid: input.client_uuid,
            identity_uuid: input.identity_uuid,
            scopes: input.scopes.clone(),
            create_ts,
            expires_in,
            ip: input.ip.clone(),
        };

        approck::debug!("Access token created with scopes: {:?}", input.scopes);
        approck::debug!("Access token created for IP: {}", input.ip);

        Ok(token)
    }
}

impl std::fmt::Display for OAuth2AccessToken {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "OAuth2AccessToken {{ access_token: [REDACTED], client_uuid: {}, identity_uuid: {}, scopes: {:?}, create_ts: {}, expires_in: {}, ip: {} }}",
            self.client_uuid,
            self.identity_uuid,
            self.scopes,
            self.create_ts,
            self.expires_in,
            self.ip
        )
    }
}

impl std::fmt::Debug for OAuth2AccessToken {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{self}")
    }
}

impl OAuth2AccessToken {
    pub fn expire_ts(&self) -> granite::DateTime<granite::Utc> {
        self.create_ts + chrono::Duration::seconds(self.expires_in)
    }

    pub fn token(&self, key: &[u8]) -> granite::Result<String> {
        // token is stored encrypted
        let access_token = &self.access_token;

        let decrypted_token = match decrypt_data(key, access_token) {
            Ok(encrypted_bytes) => {
                approck::debug!("Decrypted access token successfully");
                encrypted_bytes
            }
            Err(e) => {
                approck::info!("Decrypting access token failed: {}", e);
                return Err(granite::process_error!("Error decrypting access token").add_context(e));
            }
        };

        Ok(decrypted_token)
    }

    pub async fn store_access_token(
        &self,
        aes_key: &[u8],
        redis: &mut approck_redis::RedisCX<'_>,
    ) -> granite::Result<()> {
        approck::debug!(
            "Storing access token in Redis for client: {}",
            self.client_uuid
        );

        let access_token = match self.token(aes_key) {
            Ok(code) => code,
            Err(e) => {
                approck::info!("Failed to grab access token: {}", e);
                return Err(e);
            }
        };

        let redis_key = redis_key(&access_token);
        approck::debug!("Redis key: {}", redis_key);

        match redis.set_json(&redis_key, self).await {
            Ok(_) => {
                approck::debug!("Access token stored successfully");

                redis.set_expire(&redis_key, self.expires_in).await?;

                Ok(())
            }
            Err(e) => {
                approck::info!("Failed to store access token: {}", e);
                Err(granite::process_error!("Error storing access token in redis").add_context(e))
            }
        }
    }

    pub fn get_token_reponse(&self, aes_key: &[u8]) -> String {
        approck::debug!("Generating token response for client: {}", self.client_uuid);
        let scope_str = self.scopes.join(" ");
        approck::debug!("Token response scopes: {}", scope_str);

        let access_token = match self.token(aes_key) {
            Ok(code) => code,
            Err(e) => {
                approck::info!("Failed to grab access token: {}", e);
                return e.to_string();
            }
        };

        let response = serde_json::json!({
            "access_token": access_token,
            "token_type": "Bearer",
            "expires_in": self.expires_in,
            "scope": scope_str // this is required if different than requested scopes
            // TODO: add refresh token support
        })
        .to_string();

        approck::debug!("Token response generated successfully");
        response
    }

    pub fn validate_scope(&self, scope: &str) -> bool {
        approck::debug!("Validating scope: {}", scope);
        approck::debug!("Token scopes: {:?}", self.scopes);
        self.scopes.contains(&scope.to_string())
    }
}

pub async fn load_access_token(
    redis: &mut approck_redis::RedisCX<'_>,
    access_token: &str,
) -> granite::Result<OAuth2AccessToken> {
    approck::debug!("Loading access token from Redis");
    // get the hash of the access token
    let token_hash = redis_key(access_token);
    approck::debug!("Redis key: {}", token_hash);

    // look up the access token object from redis
    match redis.get_json::<OAuth2AccessToken>(&token_hash).await {
        Ok(val) => {
            approck::debug!(
                "Access token loaded successfully for client: {}",
                val.client_uuid
            );

            // make sure if for some reason redis didn't expire the token, that we do it now
            if val.create_ts + chrono::Duration::seconds(val.expires_in) < granite::Utc::now() {
                approck::debug!("Access token expired, deleting from redis");
                redis.del(&token_hash).await?;

                return Err(granite::authorization_error!("Access token expired"));
            }

            Ok(val)
        }
        Err(e) => {
            approck::info!("Failed to load access token: {}", e);
            Err(granite::process_error!("Error getting access token from redis").add_context(e))
        }
    }
}

pub fn revoke_access_token() -> granite::Result<()> {
    approck::debug!("Revoking access token");
    // TODO: Implement token revocation
    approck::debug!("Access token revocation not yet implemented");
    Ok(())
}

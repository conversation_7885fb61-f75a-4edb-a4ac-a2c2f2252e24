pub struct OAuth2Consent {
    pub oauth2_consent_uuid: granite::Uuid,
    pub oauth2_client_uuid: granite::Uuid,
    pub identity_uuid: granite::Uuid,
    pub scope_ids: Vec<String>,
}

pub async fn load_consent(
    app: &impl approck_postgres::App,
    client_uuid: &granite::Uuid,
    identity_uuid: &granite::Uuid,
) -> granite::Result<Option<OAuth2Consent>> {
    approck::debug!(
        "Loading consent record for client: {} and identity: {}",
        client_uuid,
        identity_uuid
    );

    // load the consent record for a given client and identity
    let db = app.postgres_dbcx().await?;

    match granite::pg_row!(
        db = db;
        args = {
            $client_uuid: client_uuid,
            $identity_uuid: identity_uuid,
        };
        row = {
            oauth2_consent_uuid: Uuid,
            oauth2_client_uuid: Uuid,
            identity_uuid: Uuid,
            scope_ids: Vec<String>,
        };
        SELECT
            oauth2_consent_uuid,
            oauth2_client_uuid,
            identity_uuid,
            scope_ids
        FROM
            auth_fence_provider.oauth2_consent
        WHERE TRUE
            AND oauth2_client_uuid = $client_uuid::uuid
            AND identity_uuid = $identity_uuid::uuid
    )
    .await
    {
        Ok(row) => {
            approck::debug!("Consent record found with scopes: {:?}", row.scope_ids);
            Ok(Some(OAuth2Consent {
                oauth2_consent_uuid: row.oauth2_consent_uuid,
                oauth2_client_uuid: row.oauth2_client_uuid,
                identity_uuid: row.identity_uuid,
                scope_ids: row.scope_ids,
            }))
        }
        Err(e) => match e.get_error_type() {
            granite::ErrorType::DataNotFound => {
                approck::debug!("No consent record found");
                Ok(None)
            }
            _ => {
                approck::info!("Error loading consent record: {}", e);
                Err(granite::process_error!("Error loading consent record").add_context(e))
            }
        },
    }
}

pub async fn grant_consent(
    app: &impl approck_postgres::App,
    client_uuid: &granite::Uuid,
    identity_uuid: &granite::Uuid,
    scope_set: &Vec<String>,
) -> granite::Result<()> {
    approck::debug!(
        "Granting consent for client: {} and identity: {} with scopes: {:?}",
        client_uuid,
        identity_uuid,
        scope_set
    );

    let db = app.postgres_dbcx().await?;

    // create a new consent record
    match granite::pg_execute!(
        db = db;
        args = {
            $client_uuid: client_uuid,
            $identity_uuid: identity_uuid,
            $scope_set: scope_set,
        };
        MERGE INTO
            auth_fence_provider.oauth2_consent
        USING
            (VALUES ($client_uuid::uuid, $identity_uuid::uuid, $scope_set::text[])) AS source (oauth2_client_uuid, identity_uuid, scope_ids)
        ON
            auth_fence_provider.oauth2_consent.oauth2_client_uuid = source.oauth2_client_uuid
            AND auth_fence_provider.oauth2_consent.identity_uuid = source.identity_uuid
        WHEN MATCHED THEN
            UPDATE SET
                scope_ids = source.scope_ids
        WHEN NOT MATCHED THEN
            INSERT
                (oauth2_client_uuid, identity_uuid, scope_ids)
            VALUES
                (source.oauth2_client_uuid, source.identity_uuid, source.scope_ids)
    )
    .await {
        Ok(_) => {
            approck::debug!("Consent granted successfully");
            Ok(())
        },
        Err(e) => {
            approck::info!("Error granting consent: {}", e);
            Err(e)
        }
    }
}

impl OAuth2Consent {
    pub fn validate_consent(&self, scope_set: &Vec<String>) -> granite::Result<bool> {
        approck::debug!(
            "Validating consent scopes: {:?} against granted scopes: {:?}",
            scope_set,
            self.scope_ids
        );

        // check scopes against consent record
        for scope in scope_set {
            if !self.scope_ids.contains(scope) {
                approck::debug!("Consent validation failed: scope '{}' not granted", scope);
                return Ok(false);
            }
        }

        approck::debug!("Consent validation successful");
        Ok(true)
    }
}

pub fn revoke_consent(
    _client_uuid: &granite::Uuid,
    _identity_uuid: &granite::Uuid,
) -> granite::Result<()> {
    approck::debug!(
        "Revoking consent for client: {} and identity: {}",
        _client_uuid,
        _identity_uuid
    );
    // TODO: Implement consent revocation
    approck::debug!("Consent revocation not yet implemented");
    Ok(())
}

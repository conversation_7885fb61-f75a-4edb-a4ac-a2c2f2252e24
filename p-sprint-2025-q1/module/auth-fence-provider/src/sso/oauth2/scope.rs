pub(crate) struct OAuth2Scope {
    pub _scope_id: String,
    pub name: String,
    pub description: String,
}

pub(crate) async fn load_exact_scope_list(
    db: &impl approck_postgres::DB,
    scope_set: &Vec<String>,
) -> granite::Result<Vec<OAuth2Scope>> {
    approck::debug!("Loading scope list: {:?}", scope_set);

    // Lookup the scopes
    let rows = match granite::pg_row_vec!(
        db = db;
        args = {
            $scope_set: scope_set,
        };
        row = {
            scope_id: String,
            name: String,
            description: String,
        };
        SELECT
            scope_id,
            name,
            description
        FROM
            auth_fence_provider.oauth2_scope
        WHERE TRUE
            AND scope_id = ANY($scope_set::text[])
    )
    .await
    {
        Ok(rows) => {
            approck::debug!("Scope list loaded successfully: {:?}", rows);

            // Use set difference to find any scopes that were not found
            let mut missing_scopes = scope_set.clone();

            for row in &rows {
                missing_scopes.retain(|scope| scope != &row.scope_id);
            }

            if !missing_scopes.is_empty() {
                approck::info!("Scope list failed: scope(s) {:?} not found", missing_scopes);
                return Err(granite::Error::authentication("Invalid scope".to_string()));
            }

            rows
        }
        Err(e) => {
            approck::info!("Error loading scope list: {}", e);
            return Err(granite::process_error!("Error loading scope list").add_context(e));
        }
    };

    Ok(rows
        .into_iter()
        .map(|row| OAuth2Scope {
            _scope_id: row.scope_id,
            name: row.name,
            description: row.description,
        })
        .collect())
}

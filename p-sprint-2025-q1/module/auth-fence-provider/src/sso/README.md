# Auth Fence SSO

## Identity Provider

- Authorization Code Grant Flow
  - Client check
    - CLIENT_VALID
      - Continue
    - CLIENT_INVALID
      - Show error
  - Identity check
    - IDENTITY_PRESENT
      - Continue
    - IDENTITY_NOT_PRESENT
      - Stash auth parameters
      - Login
      - Continue to consent check
  - User consent check
    - CONSENT_PRESENT
      - Continue
    - CONSENT_NOT_PRESENT
      - Show consent form
      - CONSENT_GRANTED
        - Store consent
        - Continue
      - CONSENT_DENIED
        - Redirect to redirect_uri with error
  - Generate authorization code
    - Store auth code and metadata
    - Redirect to client with auth code

- Access Token Grant Flow
  - Client/Secret validation
    - CLIENT_VALID
      - SECRET_VALID
        - Continue
      - SECRET_INVALID
        - Return error
    - CLIENT_INVALID
      - Return error
  - Validate session
    - SESSION_VALID
      - Continue
    - SESSION_INVALID
      - Return error
  - Generate access token
    - Store access token and metadata
    - Return access token and metadata

- User Consent Management
  - List of approved clients
  - Details page for approved clients
    - Display client, scopes, and sign-in history
    - Revoke consent functionality

- Configuration Parameters
  - Token TTL settings
  - Security settings
  - SSL/TLS settings
  - CORS settings

### Implementation Checklist

- [x] Database schema
- [ ] Token management
  - [x] Access token generation
  - [x] Access token validation
  - [ ] Access token revocation
  - [x] Auth code generation
  - [x] Auth code validation
  - [ ] Auth code revocation
- [x] Redis token management
  - [x] Access token storage and TTL
  - [x] Auth code storage and TTL
- [ ] OAuth2 endpoints
  - [x] Authorization Code endpoint
  - [x] Token Generation endpoint
  - [ ] Token Revocation endpoint
  - [x] User consent endpoint
- [ ] Client validation
  - [x] Client load
  - [x] Client validation
  - [x] Redirect URI validation
  - [x] IP validation
  - [ ] CIDR validation
  - [x] Scope validation
- [ ] User consent
  - [x] User consent load/check
  - [x] User grant consent
  - [ ] User deny consent
- [ ] Consent management
  - [ ] Client list interface
  - [ ] Client details interface
  - [ ] Revoke consent interface
- [ ] Security implementation
  - [x] PKCE support (code_challenge)
  - [ ] Rate limiting
  - [ ] CORS configuration
  - [ ] SSL/TLS requirements
- [ ] Logging
  - [ ] Authorization code attempts
  - [ ] Access token attempts
  - [ ] API events
  - [ ] Token operations
  - [ ] Consent changes

### Token management

- Generate, validate, and revoke access tokens and authorization codes.
- Handle token expiration and introspection (later), access scopes.

### Web Interface

- Manages redirect flows for initiating and completing authentication through SSO with external clients.
- User consent management
  - Locked behind active session.
  - Allow users to manage their consent for client applications.
  - Allow users to grant or deny consent for specific client applications.
  - Display clear descriptions of scopes and their purposes.

### Database Integration

- Stores user credentials, authorization codes, access tokens, and session metadata.
- Stores client application metadata, including allowed IP/CIDR ranges and redirect URIs.
- Stores user consent for client applications and their associated scopes.
- Stores audit logs for authentication, authorization, and api events.

### Extensible API Framework

- Allows client applications to integrate custom endpoints secured by authentication methods provided by this module.
- Custom scopes can be defined and enforced on an a per application basis.

### oAuth2 compliant

- Auth code generation
- Token generation, introspection, revocation
- User consent
- PKCE support
- Scope validation
- Client authentication
- State parameter support
- Error response handling
- Rate limiting for authentication attempts

### Security Considerations

- Auth code TTL:
  - Dev/staging: 10 min
  - Production: 3 min
- Access Token TTL:
  - Dev/staging: 15 min
  - Production: 15 min
- Encrypted communications between IdP and RP
- Whitelist IP addresses
- PKCE (Proof Key for Code Exchange) support
  - Required for mobile and native applications
  - Recommended for all OAuth2 flows
- Rate limiting for authentication attempts
- Session management and timeout policies
- Secure token storage and handling
- Cross-Origin Resource Sharing (CORS) configuration
- SSL/TLS requirements
- CSRF protection for consent forms
- Clear scope descriptions
- Ability to revoke consent
- Audit logging of consent changes

### Configuration Parameter Traits

```rust
// TODO: Should parameters be configured per client or per application?
// Implement sensible defaults

// Token TTL settings
auth_code_ttl_dev() // 600 # 10 minutes
auth_code_ttl_prod() // 180 # 3 minutes
access_token_ttl_dev() // 900  # 15 minutes
access_token_ttl_prod() // 900 # 15 minutes

// Security settings
// Maximum number of authentication attempts allowed per client per IP address within the rate limit window
max_auth_attempts_per_client_ip() // 5

// Maximum number of authentication attempts allowed per identity within the rate limit window
max_auth_attempts_per_identity() // 3

// Maximum number of authentication attempts allowed globally within the rate limit window
max_auth_attempts_global() // 100
rate_limit_window() // 300  # 5 minutes

// SSL/TLS settings
require_ssl() // true
minimum_tls_version() // "1.2"

// CORS settings
allowed_origins() // ["https://example.com"]
allowed_methods() // ["GET", "POST"]
```

### Structs, Traits, and Functions

```rust
struct OAuth2Client {
    client_uuid: Uuid,
    name: String,
    client_secret_hash: String,
    client_secret_salt: String,
    redirect_uris: Vec<String>,
    scope_ids: Vec<String>,
    allowed_cidr: Vec<String>,
    active: bool,
}

fn load_client(client_uuid: &str) -> Result<OAuth2Client>;
fn validate_secret(client_secret: &str) -> Result<bool>;
fn validate_redirect_uri(redirect_uri: &str) -> Result<bool>;
fn validate_scope_set(scope_set: &Vec<String>) -> Result<bool>;
fn validate_ip_address(ip_address: &str) -> Result<bool>;

/////////////////////////////////////////////////////////////////

struct OAuth2Consent {
    oauth2_consent_uuid: Uuid,
    client_uuid: Uuid,
    identity_uuid: Uuid,
    scope_ids: Vec<String>,
}

fn load_consent(client_uuid: &Uuid, identity_uuid: &Uuid) -> Result<OAuth2Consent>;
fn validate_consent(client_uuid: &Uuid, identity_uuid: &Uuid, scope_set: &Vec<String>) -> Result<bool>;
fn grant_consent(client_uuid: &Uuid, identity_uuid: &Uuid, scope_set: &Vec<String>) -> Result<()>;
fn revoke_consent(client_uuid: &Uuid, identity_uuid: &Uuid) -> Result<()>;

/////////////////////////////////////////////////////////////////

struct OAuth2AuthCode {
    auth_code: String,
    client_uuid: Uuid,
    identity_uuid: Uuid,
    scopes: Vec<String>,
    code_challenge: String,
    code_challenge_method: String,
    redirect_uri: String,
    create_ts: DateTime<Utc>,
    expires_in: i64,
    state: String,
    ip: String,
}

fn generate_auth_code() -> String;

fn try_new( Input { ... } ) -> OAuth2AuthCode;
fn load_auth_code(client_uuid: &Uuid, identity_uuid: &Uuid, auth_code: &str) -> Result<OAuth2AuthCode>;
fn store_auth_code() -> Result<()>;
fn encrypt_auth_code() -> String;
fn decrypt_auth_code() -> String;
fn verify_code_challenge() -> Result<()>;

/////////////////////////////////////////////////////////////////

struct OAuth2AccessToken {
    access_token: String,
    client_uuid: Uuid,
    identity_uuid: Uuid,
    scopes: Vec<String>,
    create_ts: DateTime<Utc>,
    expires_in: i64,
    ip: String,
}

fn generate_access_token() -> String;

fn try_new( Input { ... } ) -> OAuth2AccessToken;
fn load_access_token(access_token: &str) -> Result<OAuth2AccessToken>;
fn store_access_token() -> Result<()>;
fn encrypt_access_token() -> String;
fn decrypt_access_token() -> String;
fn revoke_access_token() -> Result<()>;

/////////////////////////////////////////////////////////////////

struct OAuth2Scope {
    scope_id: String,
    name: String,
    description: String,
}

fn load_scope(scope_id: &str) -> Result<OAuth2Scope>;
fn load_scope_list() -> Result<Vec<OAuth2Scope>>;

/////////////////////////////////////////////////////////////////

struct OAuth2Log {
    oauth2_log_uuid: Uuid,
    client_uuid: Uuid,
    identity_uuid: Uuid,
    create_ts: DateTime<Utc>,
    create_addr: String,
    token_type: String,
    token_hash: String,
    token_expire_ts: DateTime<Utc>,
    action: String,
    result: String,
    data: String,
}

fn log_action( Input { ... } ) -> Result<()>;
fn check_rate_limit(client_uuid: &Uuid, identity_uuid: &Uuid, ip_address: &str) -> Result<()>;
```

### Error Handling

- OAuth2 error responses
  - invalid_request
  - invalid_client
  - access_denied
  - unsupported_response_type
  - invalid_scope
  - server_error
  - temporarily_unavailable
  - consent_required
  - consent_denied
  - invalid_scope

### Monitoring and Logging

- Authorization code attempts (success/failure)
- Access Token operations (generation, validation, revocation)
- API calls/activity
- Consent management actions
- Error logging

### Database

- auth_fence_provider.oauth2_client
  - oauth2_client_uuid
  - create_ts
  - update_ts
  - name
  - secret_hash
  - secret_salt
  - redirect_uris text[]
  - scope_ids text[]
  - allowed_cidr cidr[]
  - note
  - active

- auth_fence_provider.oauth2_consent
  - oauth2_consent_uuid PKEY
  - client_uuid
  - identity_uuid
  - create_ts
  - update_ts
  - scope_ids text[]
  - UNIQUE (client_uuid, identity_uuid)

- auth_fence_provider.token_type ENUM
  - auth_code
  - access_token

- auth_fence_provider.oauth2_log
  - oauth2_log_uuid PKEY
  - client_uuid
  - identity_uuid
  - create_ts
  - create_addr inet
  - token_type
  - token_hash
  - token_expire_ts
  - action
  - result
  - data

- auth_fence_provider.oauth2_scope
  - scope_id
  - create_ts
  - update_ts
  - name
  - description
  - active

- Add Δ logging tables

### Redis

Auth Code Storage:

- Key Pattern: `oauth2:authcode:{client_uuid}:{identity_uuid}`
- TTL: Configured by auth_code_ttl settings
- Value (JSON):
  ```json
  {
      "auth_code": "encrypted_auth_code",
      "client_uuid": "uuid",
      "identity_uuid": "uuid",
      "scopes": ["scope1", "scope2"],
      "code_challenge": "challenge",
      "code_challenge_method": "S256",
      "redirect_uri": "uri",
      "create_ts": "timestamp",
      "expires_in": "seconds",
      "state": "state",
      "ip": "ip"
  }
  ```

Token Storage:

- Key Pattern: `oauth2:token:{client_uuid}:{identity_uuid}`
- TTL: Configured by access_token_ttl settings
- Value (JSON):
  ```json
  {
      "access_token": "encrypted_access_token",
      "client_uuid": "uuid",
      "identity_uuid": "uuid",
      "scopes": ["scope1", "scope2"],
      "create_ts": "timestamp",
      "expires_in": "seconds",
      "ip": "ip"
  }
  ```

### Endpoints (API, UI)

- GET /identity/sso/consent/
  - Shows grant consent screen for user
  - Parameters:
    ```rust
    {
        "client_uuid": "client_uuid",
        "scope": ["openid", "email", "profile"],
        "state": "optional_state",
        "redirect_uri": "https://example.com/callback",
        "response_type": "code"
    }
    ```
  - Response: HTML consent page

- POST /identity/sso/consent/accept
  - Handles user decision to consent
  - Parameters:
    ```rust
    {
        "client_uuid": "client_uuid",
        "scope": ["openid", "email", "profile"],
    }
    ```
  - Response: Redirect to original auth flow

- POST /identity/sso/consent/deny
  - Handles user decision to dissent
  - Parameters:
    ```rust
    {
        "client_uuid": "client_uuid",
        "scope": ["openid", "email", "profile"],
    }
    ```
  - Response: Redirect to redirect_uri with error

- POST /sso/oauth2/authorize
  - Auth endpoint: Initiate SSO flow, login
  - Validate IP address of client, if applicable
  - Validate redirect URI
  - Validate scopes
  - Validate consent
  - Generate auth code, store auth code and associated metadata
  - Payload
    ```json
    {
        "response_type": "code",
        "client_uuid": "client_uuid",
        "state": "optional_state",
        "code_challenge": "code_challenge",
        "code_challenge_method": "S256",
        "redirect_uri": "https://example.com/callback",
        "scope": "user_id profile email"
    }
    ```
  - Response
    - Redirect to Payload.redirect_uri
    ```json
    {
        "code": "abc123",
        "state": "optional_state",
        "session_state": "session_state"
    }
    ```
  - Error
    - Redirect to Payload.redirect_uri
    ```json
    {
        "error": "request_error",
        "state": "optional_state",
        "error_description": "Invalid redirect URI"
    }
    ```

- POST /sso/oauth2/token
  - Token endpoint: Exchange auth code for access token
  - Validate client credentials
  - Validate IP address of client, if applicable
  - Validate auth code
    - Validate code
    - Validate code challenge
    - Validate scopes
  - Validate session and consent
  - Validate redirect URI
  - Generate access token, store access token and associated metadata
  - Payload
    ```json
    {
        "client_uuid": "client_uuid",
        "client_secret": "client_secret",
        "code": "auth_code",
        "redirect_uri": "https://example.com/callback"
    }
    ```
  - Response
    ```json
    {
        "access_token": "access_token",
        "token_type": "Bearer",
        "expires_in": 3600,
        "scope": "openid profile email"
    }
    ```

- POST /sso/oauth2/revoke
  - Revoke endpoint: Revoke access token
  - Validate client credentials
  - Validate IP address of client, if applicable
  - Validate access token
  - Revoke access token
  - Payload
    ```json
    {
        "client_uuid": "client_uuid",
        "client_secret": "client_secret",
        "token": "access_token"
    }
    ```

- POST /sso/user
  - User info endpoint: Fetch basic user information
  - Validate access token
    - Validate scopes
  - Validate session and consent
  - Payload
    ```json
    {
        "access_token": "access_token"
    }
    ```
  - Response
    ```json
    {
        "sub": "identity_uuid",
        "name": "John Doe",
        "email": "<EMAIL>"
    }
    ```

- GET /identity/sso/{mapper}/consent/
  - Consent Management: Lists approved clients for a given user/identity
  - Response: HTML consent management page

- GET /identity/sso/{mapper}/consent/revoke
  - Consent Revocation: Revoke consent
  - Revokes consent for a specific client, user must confirm
  - Parameters:
    ```rust
    {
        "client_uuid": "client_uuid"
    }
    ```
  - Response: HTML consent revocation page

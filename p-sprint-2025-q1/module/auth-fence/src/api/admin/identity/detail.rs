#[approck::api]
pub mod detail {
    use crate::api::admin::identity::ml_admin;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub identity_uuid: Uuid,
        pub identity_type: String,
        pub create_ts: String,
        pub name: String,
        pub email: Option<String>,
        pub note: Option<String>,
        pub avatar_uri: Option<String>,
        pub active: bool,
        pub roles: Vec<Role>,
        pub permissions: Vec<Permission>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Role {
        pub role_uuid: Uuid,
        pub name: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Permission {
        pub permission_uuid: Uuid,
        pub source: Source,
        pub name: String,
    }

    #[derive(granite::FromString)]
    #[granite::gtype(ApiOutput)]
    pub enum Source {
        Role,
        Permission,
        RolePermission,
    }

    impl Output {
        pub fn ml_admin(&self, uri: &str) -> String {
            ml_admin(&self.identity_uuid, uri)
        }
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Check if the user has permission to read this identity
        // TODO: implement proper permission check based on your system
        if identity.identity_uuid().is_none() {
            return_authorization_error!("insufficient permissions to read permissions");
        }

        let dbcx = app.postgres_dbcx().await?;

        // Query identity details directly
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
            };
            row = {
                identity_uuid: Uuid,
                identity_type: String,
                create_ts: Option<String>,
                name: String,
                email: Option<String>,
                note: Option<String>,
                avatar_uri: Option<String>,
                active: bool,
            };
            SELECT
                identity_uuid,
                identity_type::text AS identity_type,
                create_ts::timestamp::text,
                name,
                email,
                note,
                avatar_uri,
                active
            FROM auth_fence.identity
            WHERE identity_uuid = $identity_uuid::uuid
        )
        .await?;

        // load the roles using api
        let roles = match crate::api::admin::identity::role::list::call(
            app,
            identity,
            crate::api::admin::identity::role::list::Input {
                identity_uuid: input.identity_uuid,
            },
        )
        .await
        {
            Ok(output) => output
                .roles
                .into_iter()
                .map(|row| Role {
                    role_uuid: row.role_uuid,
                    name: row.name,
                })
                .collect(),
            Err(e) => {
                approck::error!("Failed to retrieve identity roles: {e}");
                vec![]
            }
        };

        // load the permissions using api
        let permissions = match crate::api::admin::identity::permission::list::call(
            app,
            identity,
            crate::api::admin::identity::permission::list::Input {
                identity_uuid: input.identity_uuid,
            },
        )
        .await
        {
            Ok(output) => output
                .permissions
                .into_iter()
                .map(|row| Permission {
                    permission_uuid: row.permission_uuid,
                    source: row.source.to_string().into(),
                    name: row.name,
                })
                .collect(),
            Err(e) => {
                approck::error!("Failed to retrieve identity permissions: {e}");
                vec![]
            }
        };

        Ok(Output {
            identity_uuid: row.identity_uuid,
            identity_type: row.identity_type,
            create_ts: row.create_ts.unwrap_or_default(),
            name: row.name,
            email: row.email,
            note: row.note,
            avatar_uri: row.avatar_uri,
            active: row.active,
            roles,
            permissions,
        })
    }
}

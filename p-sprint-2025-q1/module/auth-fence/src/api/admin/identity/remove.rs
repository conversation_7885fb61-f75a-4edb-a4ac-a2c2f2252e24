#[approck::api]
pub mod remove {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub detail_url: String,
        pub message: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Check if the user has permission to delete this identity
        // TODO: implement proper permission check based on your system
        if identity.identity_uuid().is_none() {
            return_authorization_error!("insufficient permissions to delete identity");
        }

        let dbcx = app.postgres_dbcx().await?;

        // Soft delete by setting active = false
        granite::pg_execute!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
            };
            UPDATE
                auth_fence.identity
            SET
                active = false
            WHERE
                identity_uuid = $identity_uuid
        )
        .await?;

        Ok(Output {
            detail_url: "/admin/auth/identity/".to_string(),
            message: "Identity deleted".into(),
        })
    }
}

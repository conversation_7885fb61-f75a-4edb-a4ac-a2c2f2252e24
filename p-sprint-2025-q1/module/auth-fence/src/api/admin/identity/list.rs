#[approck::api]
pub mod list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
        pub active: Option<bool>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub identities: Vec<Identity>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Identity {
        pub identity_uuid: Uuid,
        pub identity_type: String,
        pub create_ts: String,
        pub name: String,
        pub email: Option<String>,
        pub note: Option<String>,
        pub avatar_uri: Option<String>,
        pub active: bool,
    }

    impl Identity {
        pub fn ml_admin(&self, uri: &str) -> String {
            format!("/admin/auth/identity/{}/{}", self.identity_uuid, uri)
        }
    }

    pub async fn call(app: App, identity: Identity, _input: Input) -> Result<Output> {
        // Check if the user has permission to list identities
        // TODO: implement proper permission check based on your system
        if identity.identity_uuid().is_none() {
            return_authorization_error!("insufficient permissions to read permissions");
        }

        let dbcx = app.postgres_dbcx().await?;

        // Query all identities
        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {};
            row = {
                identity_uuid: Uuid,
                identity_type: String,
                create_ts: Option<String>,
                name: String,
                email: Option<String>,
                note: Option<String>,
                avatar_uri: Option<String>,
                active: bool,
            };
            SELECT
                identity_uuid,
                identity_type::text AS identity_type,
                create_ts::timestamp::text,
                name,
                email,
                note,
                avatar_uri,
                active
            FROM auth_fence.identity
        )
        .await?;

        let identities = rows
            .into_iter()
            .map(|row| Identity {
                identity_uuid: row.identity_uuid,
                identity_type: row.identity_type,
                create_ts: row.create_ts.unwrap_or_default(),
                name: row.name,
                email: row.email,
                note: row.note,
                avatar_uri: row.avatar_uri,
                active: row.active,
            })
            .collect();

        Ok(Output { identities })
    }
}

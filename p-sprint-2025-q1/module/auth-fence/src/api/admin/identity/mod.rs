pub mod add;
pub mod detail;
pub mod edit;
pub mod list;
pub mod login;
pub mod permission;
pub mod remove;
pub mod role;
pub mod types;

// Re-export common types for easier access
pub use types::{Identity, IdentityType, ml_admin};

// Import types from other modules for convenience
pub use crate::api::admin::permission::Permission;
pub use crate::api::admin::permission::PermissionSource;
pub use crate::api::admin::role::Role;

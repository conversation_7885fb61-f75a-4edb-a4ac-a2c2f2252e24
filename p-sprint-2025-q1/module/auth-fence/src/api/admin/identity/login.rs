#[approck::api]
pub mod detail {
    use granite::return_authorization_error;
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
    }

    #[derive(Clone)]
    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub login: Option<Login>,
    }

    #[derive(Clone)]
    #[granite::gtype(ApiOutput)]
    pub struct Login {
        pub username: String,
        pub active: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Check if the user has permission to view login details
        if identity.identity_uuid().is_none() {
            return_authorization_error!("insufficient permissions to view login details");
        }

        let dbcx = app.postgres_dbcx().await?;

        match granite::pg_row_option!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
            };
            row = {
                username: String,
                active: bool,
            };
            SELECT
                login.username,
                login.active
            FROM
                auth_fence.login
            WHERE
                login.identity_uuid = $identity_uuid::uuid
        )
        .await
        {
            Ok(Some(row)) => Ok(Output {
                login: Some(Login {
                    username: row.username,
                    active: row.active,
                }),
            }),
            Ok(None) => Ok(Output { login: None }),
            Err(e) => {
                approck::info!("Failed to retrieve login details: {}", e);
                Err(granite::process_error!("Failed to retrieve login details").add_context(e))
            }
        }
    }
}

#[approck::api]
pub mod save {
    use crate::api::admin::identity::ml_admin;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
        pub username: String,
        pub password: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub next_uri: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Check if the user has permission to set login details
        if identity.identity_uuid().is_none() {
            return_authorization_error!("insufficient permissions to set login details");
        }

        println!("save login: {input:?}");

        let dbcx = app.postgres_dbcx().await?;

        // Check if username already exists for a different identity
        let existing = granite::pg_row_option!(
            db = dbcx;
            args = {
                $username: &input.username,
                $identity_uuid: &input.identity_uuid,
            };
            row = {
                identity_uuid: Uuid,
            };
            SELECT
                identity_uuid
            FROM
                auth_fence.login
            WHERE
                LOWER(username) = LOWER($username::varchar)
                AND identity_uuid != $identity_uuid::uuid
        )
        .await?;

        if existing.is_some() {
            return Err(granite::Error::validation(
                "Username already exists".to_string(),
            ));
        }

        // Generate password hash
        let password_salt = crate::api::generate_salt();
        let password_hash = crate::api::hash_password(&input.password, &password_salt);

        println!("password_hash: {}", password_hash.len());

        let _ = granite::pg_execute!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
                $username: &input.username,
                $password_hash: &password_hash,
                $password_salt: &password_salt,
            };
            MERGE INTO
                auth_fence.login AS target
            USING
                (VALUES ($identity_uuid::uuid, $username::varchar, $password_hash::varchar, $password_salt::varchar)) AS source (identity_uuid, username, password_hash, password_salt)
            ON
                target.identity_uuid = source.identity_uuid
            WHEN MATCHED THEN
                UPDATE SET
                    username = source.username,
                    password_base64_pbkdf2_sha256_hash = source.password_hash,
                    password_salt = source.password_salt
            WHEN NOT MATCHED THEN
                INSERT (identity_uuid, username, password_base64_pbkdf2_sha256_hash, password_salt, active)
                VALUES
                    (source.identity_uuid, source.username, source.password_hash, source.password_salt, TRUE)
        ).await?;

        Ok(Output {
            next_uri: ml_admin(&input.identity_uuid, ""),
        })
    }
}

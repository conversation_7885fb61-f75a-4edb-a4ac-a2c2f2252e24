#[approck::api]
pub mod add {
    use crate::api::admin::identity::IdentityType;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_type: String,
        pub name: String,
        pub email: Option<String>,
        pub note: Option<String>,
        pub avatar_uri: Option<String>,
        pub active: bool,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub identity_uuid: Uuid,
        pub detail_url: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Check if the user has permission to add identities
        // This is a placeholder - implement proper permission check based on your system
        if identity.identity_uuid().is_none() {
            return_authorization_error!("insufficient permissions to add identities");
        }

        let dbcx = app.postgres_dbcx().await?;

        // Convert identity_type string to enum
        let identity_type: IdentityType = input.identity_type.into();

        // Insert new identity
        let result = granite::pg_row!(
            db = dbcx;
            args = {
                $identity_type: &identity_type,
                $name: &input.name,
                $email: &input.email,
                $note: &input.note,
                $avatar_uri: &input.avatar_uri,
                $active: &input.active,
            };
            row = {
                identity_uuid: Uuid,
            };
            INSERT INTO
                auth_fence.identity (
                    identity_type,
                    name,
                    email,
                    note,
                    avatar_uri,
                    active
                )
            VALUES (
                $identity_type,
                $name,
                $email,
                $note,
                $avatar_uri,
                $active
            )
            RETURNING
                identity_uuid
        )
        .await?;

        let identity_uuid = result.identity_uuid;
        let detail_url = format!("/admin/auth/identity/{identity_uuid}/");

        Ok(Output {
            identity_uuid,
            detail_url,
        })
    }
}

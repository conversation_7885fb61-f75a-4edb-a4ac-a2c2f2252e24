#[approck::api]
pub mod list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub permissions: Vec<Permission>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Permission {
        pub permission_uuid: Uuid,
        pub name: String,
        pub source: PermissionSource,
    }

    #[derive(granite::FromString, granite::FmtDisplay)]
    #[granite::gtype(ApiOutput)]
    pub enum PermissionSource {
        Role,
        Permission,
        RolePermission,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Check if the user has permission to read this identity
        // TODO: implement proper permission check based on your system
        if identity.identity_uuid().is_none() {
            return_authorization_error!("insufficient permissions to read permissions");
        }

        let dbcx = app.postgres_dbcx().await?;

        // Query permissions directly
        let permissions = match granite::pg_row_vec!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
            };
            row = {
                permission_uuid: Uuid,
                name: String,
                source: String,
            };
            WITH identity_perm (permission_uuid, name, source) AS (
                // select the permissions based on identity_permission table
                SELECT
                    ip.permission_uuid,
                    p.name,
                    1
                FROM
                    auth_fence.identity_permission ip
                JOIN
                    auth_fence.permission p ON ip.permission_uuid = p.permission_uuid
                WHERE
                    ip.identity_uuid = $identity_uuid::uuid

                UNION

                // select the permissions based on role_permission table
                SELECT
                    rp.permission_uuid,
                    p.name,
                    2
                FROM
                    auth_fence.role_permission rp
                JOIN
                    auth_fence.permission p ON rp.permission_uuid = p.permission_uuid
                WHERE
                    rp.role_uuid IN (
                        SELECT role_uuid FROM auth_fence.identity_role ir
                        WHERE ir.identity_uuid = $identity_uuid::uuid
                    )
            )
            SELECT
                permission_uuid,
                name,
                CASE source
                    WHEN 1 THEN "Permission"
                    WHEN 2 THEN "Role"
                    ELSE "RolePermission"
                END
            FROM
                (
                    SELECT
                        permission_uuid,
                        name,
                        SUM(source) AS source
                    FROM
                        identity_perm
                    GROUP BY
                        permission_uuid,
                        name
                )
            ORDER BY
                name
        )
        .await
        {
            Ok(rows) => rows
                .into_iter()
                .map(|row| Permission {
                    permission_uuid: row.permission_uuid,
                    name: row.name,
                    source: row.source.into(),
                })
                .collect(),
            Err(e) => {
                approck::error!("Failed to retrieve identity permissions: {e}");
                vec![]
            }
        };

        Ok(Output { permissions })
    }
}

#[approck::api]
pub mod update {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
        pub permissions: Vec<Uuid>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub success: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Check if the user has permission to write to this identity
        // TODO: implement proper permission check based on your system
        if identity.identity_uuid().is_none() {
            return_authorization_error!("insufficient permissions to read permissions");
        }

        let dbcx = app.postgres_dbcx().await?;

        // Execute the MERGE INTO query directly
        granite::pg_execute!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
                $permissions: &input.permissions,
            };
            // First, create a CTE to identify permissions granted through roles
            WITH role_permissions AS (
                SELECT DISTINCT rp.permission_uuid
                FROM auth_fence.role_permission rp
                JOIN auth_fence.identity_role ir ON rp.role_uuid = ir.role_uuid
                WHERE ir.identity_uuid = $identity_uuid::uuid
            ),
            // Create a table from the input permissions array
            input_permissions AS (
                SELECT unnest($permissions::uuid[]) as permission_uuid
            )
            MERGE INTO
                auth_fence.identity_permission AS target
            USING (
                // Only include permissions that aren't already granted through roles
                SELECT ip.permission_uuid
                FROM input_permissions ip
                WHERE NOT EXISTS (
                    SELECT 1 FROM role_permissions rp
                    WHERE rp.permission_uuid = ip.permission_uuid
                )
            ) AS source
            ON target.identity_uuid = $identity_uuid::uuid
                AND target.permission_uuid = source.permission_uuid
            WHEN NOT MATCHED BY TARGET THEN
                INSERT (identity_uuid, permission_uuid)
                VALUES ($identity_uuid::uuid, source.permission_uuid)
            WHEN NOT MATCHED BY SOURCE AND target.identity_uuid = $identity_uuid::uuid
                // Don't delete permissions that are granted through roles
                AND NOT EXISTS (
                    SELECT 1 FROM role_permissions rp
                    WHERE rp.permission_uuid = target.permission_uuid
                ) THEN
                DELETE
        )
        .await?;

        Ok(Output { success: true })
    }
}

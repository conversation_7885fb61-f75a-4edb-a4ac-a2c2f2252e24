use granite::Uuid;
use granite::{AsVec, FmtDisplay, FromString};
use postgres_types::{FromSql, ToSql};

/// Identity type enum used throughout the identity API
#[derive(Debug, ToSql, FromSql, AsVec, FmtDisplay, FromString)]
#[postgres(name = "identity_type")]
pub enum IdentityType {
    User,
    Service,
}

/// Core identity structure used throughout the identity API
#[derive(Debug)]
pub struct Identity {
    pub identity_uuid: Uuid,
    pub identity_type: IdentityType,
    pub create_ts: String,
    pub name: String,
    pub email: Option<String>,
    pub note: Option<String>,
    pub avatar_uri: Option<String>,
    pub active: bool,
}

impl Identity {
    pub fn ml_admin(&self, uri: &str) -> String {
        ml_admin(&self.identity_uuid, uri)
    }
}

/// Generate a URL for the identity admin interface
pub fn ml_admin(identity_uuid: &Uuid, uri: &str) -> String {
    format!("/admin/auth/identity/{identity_uuid}/{uri}")
}

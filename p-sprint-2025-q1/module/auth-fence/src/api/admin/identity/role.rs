#[approck::api]
pub mod list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub roles: Vec<Role>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Role {
        pub role_uuid: Uuid,
        pub name: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Check if the user has permission to read this identity
        // TODO: implement proper permission check based on your system
        if identity.identity_uuid().is_none() {
            return_authorization_error!("insufficient permissions to read permissions");
        }

        let dbcx = app.postgres_dbcx().await?;

        // Query roles directly
        let roles = match granite::pg_row_vec!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
            };
            row = {
                role_uuid: Uuid,
                name: String,
            };
            SELECT
                role_uuid,
                name
            FROM
                auth_fence.identity_role
            JOIN
                auth_fence.role USING (role_uuid)
            WHERE TRUE
                AND identity_uuid = $identity_uuid::uuid
            ORDER BY
                name
        )
        .await
        {
            Ok(output) => output
                .into_iter()
                .map(|row| Role {
                    role_uuid: row.role_uuid,
                    name: row.name,
                })
                .collect(),
            Err(e) => {
                approck::error!("Failed to retrieve identity roles: {e}");
                vec![]
            }
        };

        Ok(Output { roles })
    }
}

#[approck::api]
pub mod update {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
        pub roles: Vec<Uuid>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub success: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Check if the user has permission to write to this identity
        // TODO: implement proper permission check based on your system
        if identity.identity_uuid().is_none() {
            return_authorization_error!("insufficient permissions to read permissions");
        }

        let dbcx = app.postgres_dbcx().await?;

        // Execute the MERGE INTO query directly
        granite::pg_execute!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
                $roles: &input.roles,
            };
            MERGE INTO
                auth_fence.identity_role AS target
            USING (
                SELECT UNNEST($roles::uuid[]) AS role_uuid
            ) AS source
            ON target.identity_uuid = $identity_uuid::uuid
                AND target.role_uuid = source.role_uuid
            WHEN NOT MATCHED BY TARGET THEN
                INSERT (identity_uuid, role_uuid)
                VALUES ($identity_uuid::uuid, source.role_uuid)
            WHEN NOT MATCHED BY SOURCE AND target.identity_uuid = $identity_uuid::uuid THEN
                DELETE
        )
        .await?;

        Ok(Output { success: true })
    }
}

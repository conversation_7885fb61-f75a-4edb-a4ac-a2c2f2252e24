#[approck::api]
pub mod permission {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub role_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub permissions: Vec<ApiPermission>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct ApiPermission {
        pub permission_uuid: Uuid,
        pub name: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Check if the user has permission to read this role
        // TODO: implement proper permission check based on your system
        if identity.identity_uuid().is_none() {
            return_authorization_error!("insufficient permissions to role {}", input.role_uuid);
        }

        let dbcx = app.postgres_dbcx().await?;

        // Query permissions directly
        let permissions = match granite::pg_row_vec!(
            db = dbcx;
            args = {
                $role_uuid: &input.role_uuid,
            };
            row = {
                permission_uuid: Uuid,
                name: String,
            };
            SELECT
                p.permission_uuid,
                p.name
            FROM auth_fence.role_permission rp
            JOIN auth_fence.permission p USING (permission_uuid)
            WHERE rp.role_uuid = $role_uuid::uuid
            ORDER BY p.name
        )
        .await
        {
            Ok(rows) => rows
                .into_iter()
                .map(|row| ApiPermission {
                    permission_uuid: row.permission_uuid,
                    name: row.name,
                })
                .collect(),
            Err(e) => {
                approck::error!("Failed to retrieve role permissions: {e}");
                vec![]
            }
        };

        Ok(Output { permissions })
    }
}

#[approck::api]
pub mod update {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub role_uuid: Uuid,
        pub permissions: Vec<Uuid>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub success: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Check if the user has permission to update this role
        // TODO: implement proper permission check based on your system
        println!("role_uuid: {:?}", input.role_uuid);

        if identity.identity_uuid().is_none() {
            return_authorization_error!(
                "insufficient permissions to update role {}",
                input.role_uuid
            );
        }

        let dbcx = app.postgres_dbcx().await?;

        println!("permissions: {:?}", input.permissions);

        // Execute the MERGE INTO query directly
        granite::pg_execute!(
            db = dbcx;
            args = {
                $role_uuid: &input.role_uuid,
                $permissions: &input.permissions,
            };
            MERGE INTO
                auth_fence.role_permission AS target
            USING (
                SELECT unnest($permissions::uuid[]) as permission_uuid
            ) AS source
            ON target.role_uuid = $role_uuid::uuid
                AND target.permission_uuid = source.permission_uuid
            WHEN NOT MATCHED BY TARGET THEN
                INSERT (role_uuid, permission_uuid)
                VALUES ($role_uuid::uuid, source.permission_uuid)
            WHEN NOT MATCHED BY SOURCE AND target.role_uuid = $role_uuid::uuid THEN
                DELETE
        )
        .await?;

        Ok(Output { success: true })
    }
}

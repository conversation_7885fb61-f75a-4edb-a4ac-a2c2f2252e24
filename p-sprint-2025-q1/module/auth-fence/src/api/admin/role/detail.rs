#[approck::api]
pub mod detail {
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub role_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub role_uuid: Uuid,
        pub role_psid: Option<String>,
        pub name: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // TODO: implement proper permission check based on your system
        if identity.identity_uuid().is_none() {
            granite::return_authorization_error!("insufficient permissions to read permissions");
        }
        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $role_uuid: &input.role_uuid,
            };
            row = {
                role_uuid: Uuid,
                role_psid: Option<String>,
                name: String,
            };
            SELECT
                role_uuid,
                name,
                role_psid
            FROM
                auth_fence.role
            WHERE
                role_uuid = $role_uuid::uuid
        )
        .await?;

        Ok(Output {
            role_uuid: row.role_uuid,
            name: row.name,
            role_psid: row.role_psid,
        })
    }
}

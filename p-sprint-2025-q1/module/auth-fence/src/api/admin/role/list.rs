#[approck::api]
pub mod list {
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub roles: Vec<Role>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Role {
        pub role_uuid: Uuid,
        pub role_psid: Option<String>,
        pub name: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // TODO: implement proper permission check based on your system
        if identity.identity_uuid().is_none() {
            granite::return_authorization_error!("insufficient permissions to read permissions");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $keyword: &input.keyword,
            };
            row = {
                role_uuid: Uuid,
                role_psid: Option<String>,
                name: String,
            };
            SELECT
                role_uuid,
                role_psid,
                name
            FROM
                auth_fence.role
            WHERE TRUE
                AND ($keyword::text IS NULL OR name ILIKE "%" || $keyword::text || "%")
            ORDER BY
                name
        )
        .await?;

        Ok(Output {
            roles: rows
                .into_iter()
                .map(|row| Role {
                    role_uuid: row.role_uuid,
                    name: row.name,
                    role_psid: row.role_psid,
                })
                .collect(),
        })
    }
}

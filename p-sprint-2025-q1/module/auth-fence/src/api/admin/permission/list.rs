#[approck::api]
pub mod list {
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub permissions: Vec<Permission>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Permission {
        pub permission_uuid: Uuid,
        pub func: Option<String>,
        pub args: Vec<String>,
        pub create_ts: String,
        pub name: String,
        pub description: Option<String>,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        //Check if the user has permission to read this permission
        if identity.identity_uuid().is_none() {
            granite::return_authorization_error!("insufficient permissions to read permissions");
        }

        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $keyword: &input.keyword,
            };
            row = {
                permission_uuid: Uuid,
                func: Option<String>,
                args: Vec<String>,
                create_ts: Option<String>,
                name: String,
                description: Option<String>,
            };
            SELECT
                permission_uuid,
                func,
                args,
                create_ts::text AS create_ts,
                name,
                description
            FROM
                auth_fence.permission
            WHERE TRUE
                AND ($keyword::text IS NULL OR name ILIKE "%" || $keyword::text || "%")
            ORDER BY
                name
        )
        .await?;

        Ok(Output {
            permissions: rows
                .into_iter()
                .map(|row| Permission {
                    permission_uuid: row.permission_uuid,
                    func: row.func,
                    args: row.args,
                    create_ts: row.create_ts.unwrap_or_default(),
                    name: row.name,
                    description: row.description,
                })
                .collect(),
        })
    }
}

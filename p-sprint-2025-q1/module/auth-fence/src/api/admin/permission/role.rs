#[approck::api]
pub mod role {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub permission_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub roles: Vec<ApiRole>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct ApiRole {
        pub role_uuid: Uuid,
        pub name: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Check if the user has permission to read this permission
        // TODO: implement proper permission check based on your system
        if identity.identity_uuid().is_none() {
            return_authorization_error!(
                "insufficient permissions to permission {}",
                input.permission_uuid
            );
        }

        let dbcx = app.postgres_dbcx().await?;

        // Query roles directly
        let roles = match granite::pg_row_vec!(
            db = dbcx;
            args = {
                $permission_uuid: &input.permission_uuid,
            };
            row = {
                role_uuid: Uuid,
                name: String,
            };
            SELECT
                r.role_uuid,
                r.name
            FROM auth_fence.role_permission rp
            JOIN auth_fence.role r USING (role_uuid)
            WHERE rp.permission_uuid = $permission_uuid::uuid
            ORDER BY r.name
        )
        .await
        {
            Ok(rows) => rows
                .into_iter()
                .map(|row| ApiRole {
                    role_uuid: row.role_uuid,
                    name: row.name,
                })
                .collect(),
            Err(e) => {
                approck::error!("Failed to retrieve roles for permission: {e}");
                vec![]
            }
        };

        Ok(Output { roles })
    }
}

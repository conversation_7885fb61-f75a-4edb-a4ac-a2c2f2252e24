#[approck::api]
pub mod map {
    use granite::{Uuid, return_authorization_error};
    use std::collections::HashMap;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Option<Uuid>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub permissions: Vec<PermissionWithRoles>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct PermissionWithRoles {
        pub permission_uuid: Uuid,
        pub name: String,
        pub roles: Vec<ApiRole>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct ApiRole {
        pub role_uuid: Uuid,
        pub name: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        // Check if the user has permission to read permissions
        // TODO: implement proper permission check based on your system
        if identity.identity_uuid().is_none() {
            return_authorization_error!("insufficient permissions to read permissions");
        }

        let dbcx = app.postgres_dbcx().await?;

        // Query permission map directly
        let mut map: HashMap<Uuid, PermissionWithRoles> = HashMap::new();

        match granite::pg_row_vec!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
            };
            row = {
                permission_uuid: Uuid,
                permission_name: String,
                role_uuid: Uuid,
                role_name: String,
            };
            SELECT
                rp.permission_uuid,
                p.name AS permission_name,
                r.role_uuid,
                r.name AS role_name
            FROM
                auth_fence.role_permission rp
            JOIN
                auth_fence.permission p USING (permission_uuid)
            JOIN
                auth_fence.role r USING (role_uuid)
            WHERE TRUE
                AND (
                    $identity_uuid::uuid IS NULL
                    OR rp.role_uuid IN (
                        SELECT
                            role_uuid
                        FROM
                            auth_fence.identity_role
                        WHERE TRUE
                            AND identity_uuid = $identity_uuid::uuid
                    )
                )
            ORDER BY
                r.name
        )
        .await
        {
            Ok(rows) => {
                for row in rows {
                    let entry = map
                        .entry(row.permission_uuid)
                        .or_insert(PermissionWithRoles {
                            permission_uuid: row.permission_uuid,
                            name: row.permission_name,
                            roles: vec![],
                        });

                    entry.roles.push(ApiRole {
                        role_uuid: row.role_uuid,
                        name: row.role_name,
                    });
                }
            }
            Err(e) => {
                approck::error!("Failed to retrieve role permission map: {e}");
            }
        }

        // Convert the HashMap to a Vec for the output
        let permissions = map.into_values().collect();

        Ok(Output { permissions })
    }
}

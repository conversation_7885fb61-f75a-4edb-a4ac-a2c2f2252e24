#[approck::api]
pub mod detail {
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub permission_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub permission_uuid: Uuid,
        pub func: Option<String>,
        pub args: Vec<String>,
        pub create_ts: String,
        pub name: String,
        pub description: Option<String>,
        pub roles: Vec<Role>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Role {
        pub role_uuid: Uuid,
        pub name: String,
    }

    pub async fn call(app: App, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        let row = match granite::pg_row!(
            db = dbcx;
            args = {
                $permission_uuid: &input.permission_uuid,
            };
            row = {
                permission_uuid: Uuid,
                func: Option<String>,
                args: Vec<String>,
                create_ts: Option<String>,
                name: String,
                description: Option<String>,
            };
            SELECT
                permission_uuid,
                func,
                args,
                create_ts::text AS create_ts,
                name,
                description
            FROM
                auth_fence.permission
            WHERE
                permission_uuid = $permission_uuid::uuid
        )
        .await
        {
            Ok(row) => row,
            Err(e) => {
                return Err(
                    granite::process_error!("Failed to retrieve permission record").add_context(e),
                );
            }
        };

        let permission = Output {
            permission_uuid: row.permission_uuid,
            func: row.func,
            args: row.args,
            create_ts: row.create_ts.unwrap_or_default(),
            name: row.name,
            description: row.description,
            roles: vec![],
        };

        Ok(permission)
    }
}

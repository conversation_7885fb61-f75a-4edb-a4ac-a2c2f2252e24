use granite::{AsVec, FmtDisplay, FromString, Uuid};

/// Permission source enum used in the permission API
#[derive(Debug, FmtDisplay, FromString, AsVec)]
pub enum PermissionSource {
    Role,
    Permission,
    RolePermission,
}

/// Core permission structure used in the permission API
#[derive(Debug)]
pub struct Permission {
    pub permission_uuid: Uuid,
    pub name: String,
    pub source: PermissionSource,
}

/// Generate a URL for the permission admin interface
pub fn ml_admin(permission_uuid: &Uuid, uri: &str) -> String {
    format!("/admin/auth/permission/{permission_uuid}/{uri}")
}

#[approck::api]
pub mod login {
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub username: String,
        pub password: String,
        pub remember_me: bool,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        nuri: String,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;
        let mut redis = app.redis_dbcx().await?;

        let remote_address = identity.remote_address().to_string();
        let session_token = identity.session_token();

        let row = match granite::pg_row_option!(
            db = dbcx;
            args = {
                $username: &input.username,
            };
            row = {
                identity_uuid: Uuid,
                identity_type: String,
                create_ts: DateTimeUtc,
                name: String,
                email: Option<String>,
                note: Option<String>,
                avatar_uri: Option<String>,
                active: bool,
                password_base64_pbkdf2_sha256_hash: Option<String>,
                password_salt: Option<String>,
            };
            SELECT
                identity.identity_uuid,
                identity.identity_type::text,
                identity.create_ts,
                identity.name,
                identity.email,
                identity.note,
                identity.avatar_uri,
                identity.active,
                login.password_base64_pbkdf2_sha256_hash,
                login.password_salt
            FROM
                auth_fence.login
            JOIN
                auth_fence.identity USING (identity_uuid)
            WHERE TRUE
                AND LOWER(login.username) = LOWER($username::varchar)
                AND login.active = TRUE
        )
        .await
        {
            Ok(Some(row)) => row,
            Ok(None) => {
                return Err(granite::Error::authentication(
                    "Email and/or password not found".to_string(),
                ));
            }
            Err(err) => {
                return Err(granite::Error::authentication(
                    "Email and/or password not found".to_string(),
                )
                .add_context(err));
            }
        };

        // verify password
        let password_hash = row.password_base64_pbkdf2_sha256_hash.unwrap();
        let password_salt = row.password_salt.unwrap();
        let password_hash_check = crate::api::hash_password(&input.password, &password_salt);
        if password_hash_check != password_hash {
            match log::auth_log(
                &dbcx,
                log::AuthLogData {
                    create_addr: remote_address,
                    session_token,
                    identity_uuid: Some(row.identity_uuid),
                    user_esid: Some(input.username),
                    user_email: row.email,
                    auth_type: "Username".to_string(),
                    auth_action: "LoginCheckPassed".to_string(),
                    auth_provider: Some("UsernameLogin".to_string()),
                    success: true,
                    blocked: false,
                    data: None,
                },
            )
            .await
            {
                Ok(_) => {
                    return Err(granite::Error::authentication(
                        "Invalid email or password".to_string(),
                    ));
                }
                Err(e) => {
                    return Err(granite::Error::process_error(
                        "Error logging email login authentication completion".to_string(),
                    )
                    .add_context(e));
                }
            };
        }

        let identity_details = crate::api::identity::Identity {
            identity_uuid: row.identity_uuid,
            identity_type: row.identity_type.into(),
            name: row.name,
            email: row.email.clone(),
            note: row.note,
            avatar_uri: row.avatar_uri,
            active: row.active,
        };

        // store identity info in redis
        crate::api::identity::set_redis_session(&mut redis, &session_token, &identity_details)
            .await?;

        use crate::postgres::log;
        match log::auth_log(
            &dbcx,
            log::AuthLogData {
                create_addr: remote_address,
                session_token,
                identity_uuid: Some(identity_details.identity_uuid),
                user_esid: Some(input.username),
                user_email: row.email,
                auth_type: "Username".to_string(),
                auth_action: "LoginCheckPassed".to_string(),
                auth_provider: Some("UsernameLogin".to_string()),
                success: true,
                blocked: false,
                data: None,
            },
        )
        .await
        {
            Ok(_) => Ok(Output {
                // TODO: Properly handle next_uri
                nuri: app.after_login_next_url(None).to_string(),
            }),
            Err(e) => Err(granite::Error::process_error(
                "Error logging email login authentication completion".to_string(),
            )
            .add_context(e)),
        }
    }
}

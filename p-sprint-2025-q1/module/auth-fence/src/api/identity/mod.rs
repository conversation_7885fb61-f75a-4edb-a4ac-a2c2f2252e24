pub mod login;
pub mod sso;

use granite::{FromString, Uuid, pg_row_option};

#[derive(serde::Deserialize, serde::Serialize, Debug, FromString)]
pub enum IdentityType {
    User,
    Service,
}

#[derive(serde::Deserialize, serde::Serialize, Debug)]
pub struct Identity {
    pub identity_uuid: granite::Uuid,
    pub identity_type: IdentityType,
    pub name: String,
    pub email: Option<String>,
    pub note: Option<String>,
    pub avatar_uri: Option<String>,
    pub active: bool,
}

pub async fn set_redis_session(
    redis: &mut approck_redis::RedisCX<'_>,
    req_session_token: &str,
    identity: &Identity,
) -> granite::Result<()> {
    let redis_session_key = crate::redis_session_key(req_session_token);
    let identity = serde_json::to_string(&identity).unwrap();

    redis
        .hset_val(&redis_session_key, "identity", &identity)
        .await
}

pub async fn validate(
    db: &impl approck_postgres::DB,
    user_info: &crate::BasicUserInfo,
) -> granite::Result<Identity> {
    let email = &user_info.email;
    let provider = &user_info.provider;

    match granite::pg_row!(
        db = db;
        args = {
            $email: email,
            $provider: provider,
        };
        row = {
            identity_uuid: Uuid,
            identity_type: String,
            name: String,
            email: Option<String>,
            note: Option<String>,
            avatar_uri: Option<String>,
            active: bool,
        };
        SELECT
            identity_uuid,
            identity_type::text AS identity_type,
            name,
            email,
            note,
            avatar_uri,
            active
        FROM
            auth_fence.identity
        WHERE TRUE
            AND email = $email
            AND active = TRUE
            AND EXISTS (
                SELECT 1
                FROM auth_fence.ssopro
                WHERE TRUE
                    AND ssopro_xsid = $provider::varchar
                    AND active = TRUE
            )
    )
    .await
    {
        Ok(row) => Ok(Identity {
            identity_uuid: row.identity_uuid,
            identity_type: row.identity_type.into(),
            name: row.name,
            email: row.email,
            note: row.note,
            avatar_uri: row.avatar_uri,
            active: row.active,
        }),
        Err(e) => Err(granite::process_error!("Identity not found").add_context(e)),
    }
}

pub async fn load(
    db: &impl approck_postgres::DB,
    identity_uuid: &granite::Uuid,
) -> granite::Result<Identity> {
    match granite::pg_row!(
        db = db;
        args = {
            $identity_uuid: identity_uuid,
        };
        row = {
            identity_uuid: Uuid,
            identity_type: String,
            name: String,
            email: Option<String>,
            note: Option<String>,
            avatar_uri: Option<String>,
            active: bool,
        };
        SELECT
            identity_uuid,
            identity_type::text AS identity_type,
            name,
            email,
            note,
            avatar_uri,
            active
        FROM
            auth_fence.identity
        WHERE TRUE
            AND identity_uuid = $identity_uuid
    )
    .await
    {
        Ok(row) => Ok(Identity {
            identity_uuid: row.identity_uuid,
            identity_type: row.identity_type.into(),
            name: row.name,
            email: row.email,
            note: row.note,
            avatar_uri: row.avatar_uri,
            active: row.active,
        }),
        Err(e) => Err(granite::process_error!("Identity not found").add_context(e)),
    }
}

pub async fn update_avatar_uri(
    client: &impl approck_postgres::DB,
    identity: &mut Identity,
    avatar_uri: &String,
) -> granite::Result<()> {
    let identity_uuid = &identity.identity_uuid;

    match granite::pg_execute!(
        db = client;
        args = {
            $identity_uuid: identity_uuid,
            $avatar_uri: avatar_uri
        };
        UPDATE
            auth_fence.identity
        SET
            avatar_uri = $avatar_uri
        WHERE TRUE
            AND identity_uuid = $identity_uuid
    )
    .await
    {
        Ok(_) => {
            identity.avatar_uri = Some(avatar_uri.clone());
            Ok(())
        }
        Err(e) => Err(granite::process_error!("Error updating avatar uri").add_context(e)),
    }
}

pub async fn create_first_login_ssopro(
    client: &impl approck_postgres::DB,
    identity_uuid: &granite::Uuid,
    provider: &String,
    remote_identity_uuid: &String,
    remote_identity_email: &String,
    ip_addr: &std::net::IpAddr,
) -> granite::Result<()> {
    match granite::pg_execute!(
        db = client;
        args = {
            $identity_uuid: identity_uuid,
            $provider: provider,
            $remote_identity_uuid: remote_identity_uuid,
            $remote_identity_email: remote_identity_email,
            $ip_addr: ip_addr
        };
        INSERT INTO
            auth_fence.identity_ssopro
            (ssopro_xsid, identity_uuid, remote_identity_uuid, remote_identity_email, first_authentication_ts, first_authentication_ip)
        VALUES
            ($provider, $identity_uuid, $remote_identity_uuid, $remote_identity_email, now(), $ip_addr)
        ON CONFLICT (remote_identity_email, ssopro_xsid) DO UPDATE
            SET
                last_authentication_ts = now(),
                last_authentication_ip = $ip_addr,
                active = TRUE
    )
    .await
    {
        Ok(_) => Ok(()),
        Err(e) => Err(granite::process_error!("Error creating first login ssopro").add_context(e)),
    }
}

pub struct AuthenticatedIdentityUuid(Uuid);

impl From<AuthenticatedIdentityUuid> for Uuid {
    fn from(value: AuthenticatedIdentityUuid) -> Self {
        value.0
    }
}

pub async fn authenticate_key(
    db: &approck_postgres::DBCX<'_>,
    identity_key_uuid: &Uuid,
    secret: &str,
) -> granite::Result<AuthenticatedIdentityUuid> {
    // use a constant so that failures are consistent
    const FAILURE_MESSAGE: &str = "authentication failed";

    // lookup the identity_key record
    let row_option = pg_row_option!(
        db = db;
        args = {
            $identity_key_uuid: &identity_key_uuid,
        };
        row = {
            identity_uuid: Uuid,
            secret_sha256: String,
            secret_salt: String,
        };
        SELECT
            identity_uuid,
            secret_sha256,
            secret_salt
        FROM
            auth_fence.identity_key
        WHERE TRUE
            AND identity_key_uuid = $identity_key_uuid::uuid
    )
    .await?;

    let row = match row_option {
        Some(row) => row,
        None => return Err(granite::Error::authentication(FAILURE_MESSAGE.to_string())),
    };

    // hash the incoming secret and salt
    let secret_sha256_incoming =
        granite::sha256_str(format!("{}{}", secret, row.secret_salt).as_str());

    // Check the incoming secret against the stored secret
    if secret_sha256_incoming != row.secret_sha256 {
        return Err(granite::Error::authentication(FAILURE_MESSAGE.to_string()));
    }

    // everything passes, return the identity_uuid
    Ok(AuthenticatedIdentityUuid(row.identity_uuid))
}

pub mod login;
pub mod setting;

#[approck::api]
pub mod detail {

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub identity_uuid: Uuid,
        pub username: Option<String>,
        pub has_login: bool,
    }

    pub async fn call(app: App, _identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;

        // Check if username already exists for this identity
        let existing_login = granite::pg_row_option!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
            };
            row = {
                username: String,
            };
            SELECT
                username
            FROM
                auth_fence.login
            WHERE
                identity_uuid = $identity_uuid::uuid
                AND active = true
        )
        .await?;

        let (username, has_login) = match existing_login {
            Some(login) => (Some(login.username), true),
            None => (None, false),
        };

        Ok(Response::Output(Output {
            identity_uuid: input.identity_uuid,
            username,
            has_login,
        }))
    }
}

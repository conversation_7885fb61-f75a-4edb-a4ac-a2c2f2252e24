#[approck::api]
pub mod detail {

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub identity_uuid: Uuid,
        pub email: Option<String>,
        pub mobile_phone: Option<String>,
    }
    pub async fn call(app: App, _identity: Identity, input: Input) -> Result<Response> {
        let dbcx = app.postgres_dbcx().await?;
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $identity_uuid: &input.identity_uuid,
            };
            row = {
                email: Option<String>,
                mobile_phone: Option<String>,
            };
            SELECT
                email,
                mobile_phone
            FROM
                auth_fence.identity
            WHERE
                identity_uuid = $identity_uuid::uuid
        )
        .await?;

        Ok(Response::Output(Output {
            identity_uuid: input.identity_uuid,
            email: row.email,
            mobile_phone: row.mobile_phone,
        }))
    }
}

//! Core auth log functionality

use granite::Uuid;

pub async fn get_login_attempts_with_email_password(
    client: &impl approck_postgres::DB,
    identity_uuid: &Uuid,
) -> granite::Result<i32> {
    let result = granite::pg_row!(
        db = client;
        args = {
            $identity_uuid: identity_uuid,
        };
        row = {
            login_attempts: i64,
        };
        SELECT
            COUNT(*) AS login_attempts
        FROM
            auth_fence.auth_log
        WHERE
            auth_action = "LoginCheckPassed"
            AND auth_provider = "UsernameLogin"
            AND identity_uuid = $identity_uuid::uuid
            AND success = true
            AND create_ts > NOW() - INTERVAL "1 day"
    )
    .await?;

    Ok(result.login_attempts as i32)
}

pub async fn get_login_attempts_with_provider(
    client: &impl approck_postgres::DB,
    identity_uuid: &Uuid,
    provider: &str,
) -> granite::Result<i32> {
    let result = granite::pg_row!(
        db = client;
        args = {
            $identity_uuid: identity_uuid,
            $provider: &provider,
        };
        row = {
            login_attempts: i64,
        };
        SELECT
            COUNT(*) AS login_attempts
        FROM
            auth_fence.auth_log
        WHERE
            auth_action = "LoginRedirectDone"
            AND auth_provider = $provider::text
            AND identity_uuid = $identity_uuid::uuid
            AND success = true
            AND create_ts > NOW() - INTERVAL "1 day"
    )
    .await?;

    Ok(result.login_attempts as i32)
}

pub async fn get_last_login_with_email_password(
    client: &impl approck_postgres::DB,
    identity_uuid: &Uuid,
) -> granite::Result<Option<String>> {
    let result = granite::pg_row_option!(
        db = client;
        args = {
            $identity_uuid: identity_uuid,
        };
        row = {
            last_login_ts: DateTimeUtc,
        };
        SELECT
            create_ts AS last_login_ts
        FROM
            auth_fence.auth_log
        WHERE
            auth_action = "LoginCheckPassed"
            AND auth_provider = "UsernameLogin"
            AND identity_uuid = $identity_uuid::uuid
            AND success = true
        ORDER BY
            create_ts DESC
        LIMIT 1
    )
    .await?;

    Ok(result.map(|row| row.last_login_ts.to_string()))
}

pub async fn get_last_login_with_provider(
    client: &impl approck_postgres::DB,
    identity_uuid: &Uuid,
    provider: &str,
) -> granite::Result<Option<String>> {
    let result = granite::pg_row_option!(
        db = client;
        args = {
            $identity_uuid: identity_uuid,
            $provider: &provider,
        };
        row = {
            last_login_ts: DateTimeUtc,
        };
        SELECT
            create_ts AS last_login_ts
        FROM
            auth_fence.auth_log
        WHERE
            auth_action = "LoginRedirectDone"
            AND auth_provider = $provider::text
            AND identity_uuid = $identity_uuid::uuid
            AND success = true
        ORDER BY
            create_ts DESC
        LIMIT 1
    )
    .await?;

    Ok(result.map(|row| row.last_login_ts.to_string()))
}

pub async fn get_password_last_changed(
    client: &impl approck_postgres::DB,
    identity_uuid: &Uuid,
) -> granite::Result<Option<String>> {
    let result = granite::pg_row_option!(
        db = client;
        args = {
            $identity_uuid: identity_uuid,
        };
        row = {
            password_last_changed_ts: DateTimeUtc,
        };
        SELECT
            create_ts AS password_last_changed_ts
        FROM
            auth_fence.login
        WHERE
            identity_uuid = $identity_uuid::uuid
        ORDER BY
            create_ts DESC
        LIMIT 1
    )
    .await?;

    Ok(result.map(|row| row.password_last_changed_ts.to_string()))
}

pub async fn get_failed_login_attempts_in_last_24_hours(
    client: &impl approck_postgres::DB,
    identity_uuid: &Uuid,
) -> granite::Result<i32> {
    let result = granite::pg_row!(
        db = client;
        args = {
            $identity_uuid: identity_uuid,
        };
        row = {
            failed_login_attempts: i64,
        };
        SELECT
            COUNT(*) AS failed_login_attempts
        FROM
            auth_fence.auth_log
        WHERE
            success = false
            AND identity_uuid = $identity_uuid::uuid
            AND create_ts > NOW() - INTERVAL "1 day"
    )
    .await?;

    Ok(result.failed_login_attempts as i32)
}

pub async fn login_with_provider_enabled(
    client: &impl approck_postgres::DB,
    identity_uuid: &Uuid,
    provider: &str,
) -> granite::Result<bool> {
    let result = granite::pg_row_option!(
        db = client;
        args = {
            $identity_uuid: identity_uuid,
            $provider: &provider,
        };
        row = {
            login_with_provider_enabled: bool,
        };
        SELECT
            COUNT(*) > 0 AS login_with_provider_enabled
        FROM
            auth_fence.identity_ssopro
        WHERE
            identity_uuid = $identity_uuid::uuid
            AND ssopro_xsid = $provider::text
            AND active = true
    )
    .await?;

    Ok(result
        .map(|row| row.login_with_provider_enabled)
        .unwrap_or(false))
}

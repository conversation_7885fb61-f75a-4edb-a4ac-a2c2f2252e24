//! Core identity creation functionality
//! Simple struct-based interface for creating identities with login

/// Input data for creating identity with login
#[granite::gtype]
pub struct CreateIdentityData {
    pub first_name: String,
    pub last_name: String,
    pub email: String,
    pub phone: Option<String>,
    pub password: String,
}

/// Result of identity creation
#[granite::gtype]
pub struct CreateIdentityResult {
    pub identity_uuid: Uuid,
    pub username: String,
}

impl CreateIdentityData {
    /// Create identity with login - core auth functionality only
    pub async fn create_identity_with_login(
        self,
        app: &impl crate::App,
    ) -> granite::Result<CreateIdentityResult> {
        let mut dbcx = app.postgres_dbcx().await?;
        let dbtx = dbcx.transaction().await?;

        let full_name = format!("{} {}", self.first_name, self.last_name);
        let username = self.email.clone();

        // Check if username (email) already exists in login table
        let existing_username = granite::pg_row_option!(
            db = dbtx;
            args = {
                $username: &username,
            };
            row = {
                identity_uuid: Uuid,
            };
            SELECT
                identity_uuid
            FROM
                auth_fence.login
            WHERE
                LOWER(username) = LOWER($username::text)
                AND active = true
        )
        .await?;

        if existing_username.is_some() {
            return Err(granite::Error::validation(
                "An account with this email already exists".to_string(),
            ));
        }

        // Check if email already exists in identity table
        let existing_email = granite::pg_row_option!(
            db = dbtx;
            args = {
                $email: &self.email,
            };
            row = {
                identity_uuid: Uuid,
            };
            SELECT
                identity_uuid
            FROM
                auth_fence.identity
            WHERE
                LOWER(email) = LOWER($email::text)
                AND active = true
        )
        .await?;

        if existing_email.is_some() {
            return Err(granite::Error::validation(
                "An account with this email already exists".to_string(),
            ));
        }

        // Create identity record
        let identity_result = granite::pg_row!(
            db = dbtx;
            args = {
                $identity_type: &crate::api::admin::identity::IdentityType::User,
                $name: &full_name,
                $email: &self.email,
                $mobile_phone: &self.phone,
            };
            row = {
                identity_uuid: Uuid,
            };
            INSERT INTO
                auth_fence.identity (
                    identity_type,
                    name,
                    email,
                    mobile_phone,
                    active
                )
            VALUES (
                $identity_type,
                $name,
                $email,
                $mobile_phone,
                TRUE
            )
            RETURNING
                identity_uuid
        )
        .await?;

        let identity_uuid = identity_result.identity_uuid;

        // Generate password hash
        let password_salt = crate::api::generate_salt();
        let password_hash = crate::api::hash_password(&self.password, &password_salt);

        // Create login record
        granite::pg_execute!(
            db = dbtx;
            args = {
                $identity_uuid: &identity_uuid,
                $username: &username,
                $password_hash: &password_hash,
                $password_salt: &password_salt,
            };
            INSERT INTO auth_fence.login (
                identity_uuid,
                username,
                password_base64_pbkdf2_sha256_hash,
                password_salt,
                active
            )
            VALUES (
                $identity_uuid,
                $username,
                $password_hash,
                $password_salt,
                TRUE
            )
        )
        .await?;

        // Commit transaction
        dbtx.commit().await?;

        Ok(CreateIdentityResult {
            identity_uuid,
            username,
        })
    }
}

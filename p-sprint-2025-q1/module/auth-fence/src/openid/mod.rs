use async_trait::async_trait;
use openidconnect::{
    AuthUrl, AuthorizationCode, ClientId, ClientSecret, CsrfToken, EmptyAdditionalClaims,
    EndpointNotSet, EndpointSet, IdTokenClaims, IssuerUrl, Nonce, PkceCodeChallenge,
    PkceCodeVerifier, RedirectUrl, Scope, TokenResponse, TokenUrl, UserInfoUrl,
    core::{CoreAuthenticationFlow, CoreClient, CoreGenderClaim},
    reqwest,
};
use serde::{Deserialize, Serialize};

pub mod google;
pub mod microsoft;

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct AuthState {
    state: String,
    pub next_uri: String,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct OpenIDProviderConfig {
    #[serde(rename = "type")]
    pub ttype: OpenIDProviderType,
}

#[derive(Debug, <PERSON><PERSON>, Deserialize, Serialize)]
pub enum OpenIDProviderType {
    #[serde(rename = "google")]
    Google,
    #[serde(rename = "microsoft")]
    Microsoft,
    #[serde(untagged)]
    Custom(String),
}

impl std::fmt::Display for OpenIDProviderType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            OpenIDProviderType::Google => write!(f, "google"),
            OpenIDProviderType::Microsoft => write!(f, "microsoft"),
            OpenIDProviderType::Custom(t) => write!(f, "custom::{t}"),
        }
    }
}

#[async_trait]
pub trait OpenIDProvider: Send + Sync {
    fn key(&self) -> String;
    fn provider(&self) -> OpenIDProviderType;
    fn client_id(&self) -> String;
    fn client_secret(&self) -> String;
    fn get_scopes(&self) -> Vec<String>;
    fn get_base_uri(&self) -> String;
    fn get_issuer_url(&self) -> String;
    fn get_redirect_uri(&self, app_url: String) -> String;
    fn get_auth_uri(&self) -> String;
    fn get_token_uri(&self) -> String;
    fn get_user_info_uri(&self) -> String;
    fn get_button_uri(&self, next_uri: Option<String>) -> String {
        match next_uri {
            Some(n) => format!("/oauth2/{}/?next_uri={}", self.key(), n),
            None => format!("/oauth2/{}/", self.key()),
        }
    }
    fn get_button_html(&self, next_uri: Option<String>) -> maud::PreEscaped<String>;
    fn get_well_known_uri(&self) -> String;
    fn get_jwks_uri(&self) -> String;
    fn get_http_client(&self) -> reqwest::Client {
        reqwest::ClientBuilder::new()
            // Following redirects opens the client up to SSRF vulnerabilities.
            .redirect(reqwest::redirect::Policy::none())
            .build()
            .expect("Client should build")
    }
    async fn get_client(
        &self,
        app_url: String,
    ) -> granite::Result<
        CoreClient<
            EndpointSet,    //HasAuthUrl
            EndpointNotSet, //HasDeviceAuthUrl
            EndpointNotSet, //HasIntrospectionUrl
            EndpointNotSet, //HasRevocationUrl
            EndpointSet,    //HasTokenUrl
            EndpointSet,    //HasUserInfoUrl
        >,
    > {
        let http_client = reqwest::ClientBuilder::new()
            // Following redirects opens the client up to SSRF vulnerabilities.
            .redirect(reqwest::redirect::Policy::none())
            .build()
            .expect("Client should build");
        //self.get_http_client();

        // Directly fetch JWKS
        let jwks_url = self.get_jwks_uri();
        let jwks = http_client
            .get(jwks_url)
            .send()
            .await?
            .json::<openidconnect::core::CoreJsonWebKeySet>()
            .await?;

        let redirect_url = match RedirectUrl::new(self.get_redirect_uri(app_url)) {
            Ok(redirect_url) => redirect_url,
            Err(e) => {
                return Err(granite::process_error!("Error creating redirect url: {e}"));
            }
        };

        let auth_url = match AuthUrl::new(self.get_auth_uri()) {
            Ok(auth_url) => auth_url,
            Err(e) => {
                return Err(granite::process_error!("Error creating redirect url: {e}"));
            }
        };

        let token_uri = match TokenUrl::new(self.get_token_uri()) {
            Ok(token_uri) => token_uri,
            Err(e) => {
                return Err(granite::process_error!("Error creating redirect url: {e}"));
            }
        };

        let user_info_uri = match UserInfoUrl::new(self.get_user_info_uri()) {
            Ok(user_info_uri) => user_info_uri,
            Err(e) => {
                return Err(granite::process_error!("Error creating redirect url: {e}"));
            }
        };

        // Create an OpenID Connect client by specifying the client ID, client secret, authorization URL and token URL.
        let client = CoreClient::new(
            ClientId::new(self.client_id()),
            IssuerUrl::new(self.get_issuer_url())?,
            jwks,
        )
        .set_client_secret(ClientSecret::new(self.client_secret()))
        .set_redirect_uri(redirect_url)
        .set_auth_type(openidconnect::AuthType::BasicAuth)
        .set_auth_uri(auth_url)
        .set_token_uri(token_uri)
        .set_user_info_url(user_info_uri);

        Ok(client)
    }
    async fn get_token<'a>(
        &self,
        code: &str,
        pkce_code_verifier: &str,
        nonce: String,
        app_url: &'a str,
    ) -> granite::Result<
        openidconnect::IdTokenClaims<
            openidconnect::EmptyAdditionalClaims,
            openidconnect::core::CoreGenderClaim,
        >,
    > {
        let http_client = reqwest::ClientBuilder::new()
            // Following redirects opens the client up to SSRF vulnerabilities.
            .redirect(reqwest::redirect::Policy::none())
            .build()
            .expect("Client should build"); //self.get_http_client();

        let client = self.get_client(app_url.to_owned()).await?;

        // Now you can exchange it for an access token and ID token.
        let token_response = client
            .exchange_code(AuthorizationCode::new(code.to_string()))
            .set_pkce_verifier(PkceCodeVerifier::new(pkce_code_verifier.to_string()))
            .request_async(&http_client)
            .await
            .map_err(|e| granite::Error::new(granite::ErrorType::ProcessError).add_context(e))?;
        // Extract the ID token claims after verifying its authenticity and nonce.
        let id_token = token_response.id_token().ok_or_else(|| {
            println!("No id token in response");
            granite::Error::new(granite::ErrorType::ProcessError)
                .add_context("No id token in response")
        })?;

        let nonce = Nonce::new(nonce);
        let token = match id_token.claims(&client.id_token_verifier(), &nonce) {
            Ok(token) => token.to_owned(),
            Err(e) => {
                return Err(granite::process_error!("Error verifying id token: {e}"));
            }
        };

        Ok(token)
    }
    async fn build_auth_url(
        &self,
        redis: &mut approck_redis::RedisCX<'_>,
        req_session_token: String,
        app_url: String,
        next_uri: &str,
    ) -> granite::Result<String> {
        // TODO: Add AuthState and make sure to encode next_uri within

        // create the redis lookup key
        let redis_session_key = super::redis_session_key(req_session_token.as_str());

        // get the client
        let client = self.get_client(app_url).await?;

        // Generate a PKCE challenge.
        let (pkce_challenge, pkce_verifier) = PkceCodeChallenge::new_random_sha256();

        let next_uri = next_uri.to_string();
        let get_state = move || -> CsrfToken {
            let state_json = serde_json::json!({
                "next_uri": next_uri,
                "state": CsrfToken::new_random()
            })
            .to_string();

            CsrfToken::new(base64_light::base64_encode_bytes(state_json.as_bytes()))
        };

        let auth_request = client
            .authorize_url(
                CoreAuthenticationFlow::AuthorizationCode,
                get_state,
                Nonce::new_random,
            )
            .set_pkce_challenge(pkce_challenge);

        let auth_request =
            self.get_scopes()
                .iter()
                .fold(auth_request, |mut auth_request, scope| {
                    auth_request = auth_request.add_scope(Scope::new(scope.to_string()));
                    auth_request
                });

        // Generate the full authorization URL.
        let (auth_url, csrf_token, nonce) = auth_request.url();

        redis
            .hset_val::<String>(
                &redis_session_key,
                "pkce_code_verifier",
                pkce_verifier.secret().to_string(),
            )
            .await?;

        redis
            .hset_val::<String>(
                &redis_session_key,
                "csrf_token",
                csrf_token.secret().to_string(),
            )
            .await?;

        redis
            .hset_val::<String>(&redis_session_key, "nonce", nonce.secret().to_string())
            .await?;

        // Return the authorization URL
        Ok(auth_url.to_owned().into())
    }

    async fn handle_auth_redirect(
        &self,
        state: &str,
        code: &str,
        redis: &mut approck_redis::RedisCX<'_>,
        req_session_token: String,
        app_url: String,
    ) -> granite::Result<(crate::types::BasicUserInfo, String)> {
        let redis_session_key = super::redis_session_key(req_session_token.as_str());

        let pkce_code_verifier: String = match redis
            .hget_val(&redis_session_key, "pkce_code_verifier")
            .await?
        {
            Some(pkce_code_verifier) => pkce_code_verifier,
            None => {
                return Err(granite::process_error!(
                    "No pkce code verifier found in redis"
                ));
            }
        };

        let csrf_token: String = match redis.hget_val(&redis_session_key, "csrf_token").await? {
            Some(csrf_token) => csrf_token,
            None => return Err(granite::process_error!("No csrf token found in redis")),
        };

        let nonce: String = match redis.hget_val(&redis_session_key, "nonce").await? {
            Some(nonce) => nonce,
            None => return Err(granite::process_error!("No nonce found in redis")),
        };

        // if state does not match, we need to log it and return a nice error
        if state != csrf_token {
            return Err(granite::process_error!("CSRF token mismatch"));
        }

        let auth_state: AuthState =
            match serde_json::from_str(base64_light::base64_decode_str(state).as_str()) {
                Ok(decoded_state) => decoded_state,
                Err(e) => return Err(granite::process_error!("Error decoding state: {e}")),
            };

        let next_uri = urlencoding::decode(&auth_state.next_uri)
            .expect("utf-8")
            .to_string();

        let token_response = match self
            .get_token(code, &pkce_code_verifier, nonce, &app_url)
            .await
        {
            Ok(token_response) => token_response,
            Err(e) => {
                return Err(granite::process_error!(
                    "Error getting ODIC token response: {:?}",
                    e
                ));
            }
        };

        let user_info = self.unpack_user_claims(&token_response);

        Ok((user_info, next_uri))
    }
    fn unpack_user_claims(
        &self,
        token: &IdTokenClaims<EmptyAdditionalClaims, CoreGenderClaim>,
    ) -> crate::types::BasicUserInfo {
        let first_name = token
            .given_name()
            .and_then(|n| n.get(None))
            .map(|n| n.to_string())
            .unwrap_or_default();
        let last_name = token
            .family_name()
            .and_then(|n| n.get(None))
            .map(|n| n.to_string())
            .unwrap_or_default();
        let email = token.email().map(|n| n.to_string()).unwrap_or_default();
        let avatar_uri = token
            .picture()
            .and_then(|n| n.get(None))
            .map(|n| n.to_string())
            .unwrap_or_default();

        crate::types::BasicUserInfo {
            provider: self.provider().to_string(),
            id: token.subject().to_string(),
            email,
            first_name,
            last_name,
            avatar_uri,
        }
    }
}

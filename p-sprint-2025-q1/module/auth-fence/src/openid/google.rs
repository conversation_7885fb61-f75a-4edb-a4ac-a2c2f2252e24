use crate::openid::{OpenIDProvider, OpenIDProviderType};
use crate::types::{BaseOpenIDConfig, BasicUserInfo};

pub struct OpenIDGoogleProvider {
    pub config: BaseOpenIDConfig,
}

#[allow(dead_code)]
#[derive(serde::Deserialize, Debug)]
pub struct GoogleUserInfo {
    email: String,
    family_name: String,
    given_name: String,
    hd: String,
    id: String,
    locale: Option<GoogleLocale>,
    name: String,
    picture: String,
}

pub type GoogleLocale = String;

impl From<GoogleUserInfo> for BasicUserInfo {
    fn from(user_info: GoogleUserInfo) -> Self {
        BasicUserInfo {
            provider: "google".to_string(),
            id: user_info.id,
            email: user_info.email,
            first_name: user_info.given_name,
            last_name: user_info.family_name,
            avatar_uri: user_info.picture,
        }
    }
}

#[async_trait::async_trait]
impl OpenIDProvider for OpenIDGoogleProvider {
    fn key(&self) -> String {
        self.config.key.clone()
    }
    fn provider(&self) -> OpenIDProviderType {
        OpenIDProviderType::Google
    }
    fn client_id(&self) -> String {
        self.config.client_id.clone()
    }
    fn client_secret(&self) -> String {
        self.config.client_secret.clone()
    }
    fn get_scopes(&self) -> Vec<String> {
        vec![
            "openid".to_string(),
            "profile".to_string(),
            "email".to_string(),
        ]
    }
    fn get_auth_uri(&self) -> String {
        "https://accounts.google.com/o/oauth2/auth".to_string()
    }
    fn get_base_uri(&self) -> String {
        "https://accounts.google.com".to_string()
    }
    fn get_issuer_url(&self) -> String {
        "https://accounts.google.com".to_string()
    }
    fn get_jwks_uri(&self) -> String {
        "https://www.googleapis.com/oauth2/v3/certs".to_string()
    }
    fn get_redirect_uri(&self, app_url: String) -> String {
        format!("{}/oauth2/{}/redirect", app_url, self.key())
    }
    fn get_token_uri(&self) -> String {
        "https://oauth2.googleapis.com/token".to_string()
    }
    fn get_user_info_uri(&self) -> String {
        "https://openidconnect.googleapis.com/v1/userinfo".to_string()
    }
    fn get_well_known_uri(&self) -> String {
        "https://accounts.google.com/.well-known/openid-configuration".to_string()
    }
    fn get_button_html(&self, next_uri: Option<String>) -> maud::PreEscaped<String> {
        maud::html!(
            a href=(self.get_button_uri(next_uri)) class="btn btn-outline-custom btn-google social-btn" {
                i class="fab fa-google" {}
                " Sign in with Google"
            }
        )
    }
}

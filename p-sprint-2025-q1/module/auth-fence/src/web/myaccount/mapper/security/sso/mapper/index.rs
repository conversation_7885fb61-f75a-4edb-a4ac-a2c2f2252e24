#[approck::http(GET /myaccount/{identity_uuid:Uuid}/security/sso/{ssopro_xsid:String}/; AUTH is_logged_in; return HTML;)]
pub mod page {
    use crate::api::identity::sso::providers::index;

    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        // Get the current user's identity UUID
        let identity_uuid = path.identity_uuid;
        let ssopro_xsid = path.ssopro_xsid;

        // Call the API to get all SSO providers with connection data
        let providers_result = index::call(app, identity, index::Input { identity_uuid }).await;
        let provider = match providers_result {
            Ok(output) => output
                .providers
                .into_iter()
                .find(|p| p.ssopro_xsid.to_lowercase() == ssopro_xsid),
            Err(_) => {
                return Err(granite::Error::data_not_found()
                    .set_external_message("SSO provider not found".to_string()));
            }
        };

        if let Some(ref provider) = provider {
            doc.set_title(&format!("{} SSO Provider", provider.name));
        } else {
            doc.set_title("SSO Provider Not Found");
        }

        // Single SSO Provider section
        doc.add_body(html!(
            panel {
                header {
                    h2 {
                        @if let Some(ref provider) = provider {
                            (format!("{} SSO Provider", provider.name))
                        } @else {
                            "SSO Provider Not Found"
                        }
                    }
                }
                content {
                    @if let Some(provider) = provider {
                        bux-provider-card {
                            header {
                                h4 {
                                    span.provider-name { "Sign in with " (provider.name) }
                                }
                                @if provider.is_connected {
                                    label-tag.success { "Connected" }
                                }
                                @else {
                                    label-tag.default { "Not Connected" }
                                }
                            }
                            content {
                                @if let Some(ref description) = provider.description {
                                    p { (description) }
                                }
                                @if provider.is_connected {
                                    div.connection-details {
                                        @if let Some(ref email) = provider.remote_identity_email {
                                            p {
                                                strong { "Email: " }
                                                (email)
                                            }
                                        }
                                        @if let Some(ref connected_date) = provider.formatted_first_auth_ts {
                                            p {
                                                strong { "Connected: " }
                                                (connected_date)
                                            }
                                        }
                                    }
                                } @else {
                                    @if let Some(ref last_auth_date) = provider.formatted_last_auth_ts {
                                        div.connection-details {
                                            p {
                                                strong { "Last used: " }
                                                (last_auth_date)
                                            }
                                        }
                                    }
                                }
                            }
                            footer {
                                @if provider.is_connected {
                                    (bux::button::link::label_icon_class(
                                        "Disconnect",
                                        "fas fa-unlink",
                                        &crate::ml_myaccount_security_sso_disconnect(identity_uuid, &provider.ssopro_xsid),
                                        "danger"
                                    ))
                                } @else {
                                    (bux::button::link::label_icon_class(
                                        "Connect",
                                        "fas fa-link",
                                        &provider.connect_url,
                                        "primary"
                                    ))
                                }
                            }
                        }
                    } @else {
                        p { "SSO provider not found or not configured." }
                    }
                }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

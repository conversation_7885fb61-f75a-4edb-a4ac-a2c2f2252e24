//-------------------------------------------------------------------------------------------------
// 1. Import Components

import "./username.mcss";
import "@bux/input/text/string.mts";

//-------------------------------------------------------------------------------------------------
// 2. Import Code

import { SEC } from "@granite/lib.mts";
import { update } from "./usernameλ.mts";
import { go_back } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import FormPanel from "@bux/component/form_panel.mts";

//-------------------------------------------------------------------------------------------------
// 3. Find Elements

const $form = SEC(HTMLFormElement, document, "#edit-username-form");
const $identity_uuid: HTMLInputElement = SEC(HTMLInputElement, $form, "[name=identity_uuid]");
const $username: BuxInputTextString = SEC(BuxInputTextString, $form, "[name=username]");

//-------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

//-------------------------------------------------------------------------------------------------
// 5. Write Code

new FormPanel({
    $form,
    api: update.api,
    on_cancel: go_back,

    err: (errors) => {
        $username.set_e(errors.username);
    },

    get: () => {
        return {
            identity_uuid: $identity_uuid.value,
            username: $username.value,
        };
    },

    set: (_value) => {
        // No need to set values for registration form
    },

    out: (output) => {
        // Show success message and redirect back to security page
        go_back(output.url);
    },
});

#[approck::http(GET /myaccount/{identity_uuid:Uuid}/security/login; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Set Login");

        // Get username details via API
        let detail_input = crate::api::myaccount::security::detail::Input {
            identity_uuid: path.identity_uuid,
        };

        let detail_response =
            crate::api::myaccount::security::detail::call(app, identity, detail_input).await?;

        let (current_username, show_helper_text) = match detail_response {
            crate::api::myaccount::security::detail::Response::Output(output) => {
                (output.username, !output.has_login)
            }
            _ => (None, true), // Default to showing helper text if there's an error
        };

        let mut register_panel = bux::component::save_cancel_form_panel(
            "Set Login Credentials",
            &crate::ml_myaccount_security(path.identity_uuid),
        );
        register_panel.set_id("set-login-form");

        let username = match current_username {
            Some(username) => Some(username),
            None => identity.email(),
        };

        #[rustfmt::skip]
        register_panel.add_body(html!(
            input type="hidden" name="identity_uuid" value=(path.identity_uuid.to_string()) {}
            (bux::input::text::string::name_label_value("username", "Username", username.as_deref()))
            (bux::component::password_creator::password_creator_without_title())
            @if show_helper_text {
                hr;
                p.help-text {
                    "Your email address is suggested as a username, but you can use any unique identifier you prefer."
                }
            }
        ));

        doc.add_body(html!(
            bux-action-panel {
                (register_panel)
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

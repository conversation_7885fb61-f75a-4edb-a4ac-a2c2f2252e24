// -------------------------------------------------------------------------------------------------
// 1. Import Components
import "./login.mcss";
import "@bux/input/text/string.mts";
import "@bux/component/password_creator.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code
import { SEC } from "@granite/lib.mts";
import { login } from "@crate/api/myaccount/security/loginλ.mts";
import { go_back } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import BuxComponentPasswordCreator from "@bux/component/password_creator.mts";
import FormPanel from "@bux/component/form_panel.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements
const $form = SEC(HTMLFormElement, document, "#set-login-form");
const $identity_uuid: HTMLInputElement = SEC(HTMLInputElement, $form, "[name=identity_uuid]");
const $username: BuxInputTextString = SEC(BuxInputTextString, $form, "[name=username]");
const $password_creator = SEC(BuxComponentPasswordCreator, $form, "bux-component-password-creator");

// -------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

// 5. Write Code
// -------------------------------------------------------------------------------------------------
new FormPanel({
    $form,
    api: login.api,
    on_cancel: go_back,

    err: (errors) => {
        $username.set_e(errors.username);
        $password_creator.set_e(errors.password);
    },

    get: () => {
        return {
            identity_uuid: $identity_uuid.value,
            username: $username.value,
            password: $password_creator.value,
        };
    },

    set: (_value) => {
        // No need to set values for registration form
    },

    out: (output) => {
        go_back(output.url);
    },
});

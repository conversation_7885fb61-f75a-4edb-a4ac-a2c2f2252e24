#[approck::http(GET /myaccount/{identity_uuid:Uuid}/security/mobile; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Edit Mobile Number");

        // Get account data using our consolidated API
        let detail_input = crate::api::myaccount::security::setting::detail::Input {
            identity_uuid: path.identity_uuid,
        };

        let detail_response =
            crate::api::myaccount::security::setting::detail::call(app, identity, detail_input)
                .await?;

        let current_mobile = match detail_response {
            crate::api::myaccount::security::setting::detail::Response::Output(output) => {
                output.mobile_phone
            }
            _ => None,
        };

        let form_panel = {
            let mut panel = bux::component::save_cancel_form_panel(
                "Edit Mobile Number",
                &crate::ml_myaccount_security(path.identity_uuid),
            );
            panel.set_id("edit-mobile-form");
            panel.set_hidden("identity_uuid", path.identity_uuid.to_string());
            panel.add_body(html!(
                (bux::input::text::string::name_label_value(
                    "mobile",
                    "Mobile Number:",
                    current_mobile.as_deref()
                ))
            ));
            panel
        };

        doc.set_body_display_narrow();
        doc.add_body(html!((form_panel)));
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod update {
    use granite::NestedError;
    use granite::ResultExt;

    #[granite::gtype(RsError)]
    pub struct Input_Error {
        pub identity_uuid: Option<String>,
        pub mobile: Option<String>,
    }

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
        pub mobile: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub identity_uuid: Uuid,
        pub url: String,
    }

    pub async fn call(app: App, _identity: Identity, input: Input) -> Result<Response> {
        if let Some(mobile) = &input.mobile {
            if mobile.trim().is_empty() {
                // Allow empty mobile to be saved as None
            } else {
                // Check for invalid characters first
                if !mobile.chars().all(|c| {
                    c.is_ascii_digit() || c == '-' || c == '(' || c == ')' || c == ' ' || c == '+'
                }) {
                    let input_error = Input_Error {
                        identity_uuid: None,
                        mobile: Some("Phone number contains invalid characters.".to_string()),
                    };
                    return Ok(Response::ValidationError(NestedError {
                        outer: "Validation Error".to_string(),
                        inner: Some(input_error),
                    }));
                }

                if !granite::validate_phone_us(mobile) {
                    let input_error = Input_Error {
                        identity_uuid: None,
                        mobile: Some("Invalid United States phone number.".to_string()),
                    };
                    return Ok(Response::ValidationError(NestedError {
                        outer: "Validation Error".to_string(),
                        inner: Some(input_error),
                    }));
                }
            }
        }
        let mut dbcx = app.postgres_dbcx().await?;
        let dbtx = dbcx
            .transaction()
            .await
            .amend(|e| e.add_context("starting transaction"))?;

        // Update mobile in identity table
        granite::pg_execute!(
            db = dbtx;
            args = {
                $identity_uuid: &input.identity_uuid,
                $mobile: &input.mobile,
            };
            UPDATE
                auth_fence.identity
            SET
                mobile_phone = $mobile::text
            WHERE
                identity_uuid = $identity_uuid::uuid
        )
        .await?;

        dbtx.commit()
            .await
            .amend(|e| e.add_context("committing transaction"))?;

        Ok(Response::Output(Output {
            identity_uuid: input.identity_uuid,
            url: crate::ml_myaccount_security(input.identity_uuid),
        }))
    }
}

//-------------------------------------------------------------------------------------------------
// 1. Import Components

import "./password.mcss";
import "@bux/component/password_creator.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code

import { SEC } from "@granite/lib.mts";
import { update } from "./passwordλ.mts";
import { go_back } from "@bux/singleton/nav_stack.mts";

import BuxComponentPasswordCreator from "@bux/component/password_creator.mts";
import FormPanel from "@bux/component/form_panel.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements

const $form = SEC(HTMLFormElement, document, "#edit-password-form");
const $identity_uuid: HTMLInputElement = SEC(HTMLInputElement, $form, "[name=identity_uuid]");
const $password_creator = SEC(BuxComponentPasswordCreator, $form, "bux-component-password-creator");

// -------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

$form.addEventListener("submit", (event) => {
    if (!$password_creator.validate()) {
        event.preventDefault();
    }
});

// -------------------------------------------------------------------------------------------------
// 5. Form Panel

new FormPanel({
    $form,
    api: update.api,
    on_cancel: go_back,

    err: (errors) => {
        $password_creator.set_e(errors.password);
    },

    get: () => {
        return {
            identity_uuid: $identity_uuid.value,
            password: $password_creator.value,
        };
    },

    set: (_value) => {
        // No need to set values for registration form
    },

    out: (output) => {
        go_back(output.url);
    },
});

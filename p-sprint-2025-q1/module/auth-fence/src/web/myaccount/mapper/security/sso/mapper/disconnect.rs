#[approck::http(GET /myaccount/{identity_uuid:Uuid}/security/sso/{ssopro_xsid:String}/disconnect; AUTH is_logged_in; return HTML|Redirect;)]
pub mod page {
    use crate::api::identity::sso::revoke;

    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        // Get the current user's identity UUID and provider
        let identity_uuid = path.identity_uuid;
        let ssopro_xsid = path.ssopro_xsid;

        // Call the API to disconnect the SSO provider
        let disconnect_result = revoke::revoke::call(
            app,
            identity,
            revoke::revoke::Input {
                identity_uuid,
                ssopro_xsid: ssopro_xsid.clone(),
            },
        )
        .await;

        match disconnect_result {
            Ok(output) => {
                if output.success {
                    // Successful disconnect - redirect back to SSO page with success message
                    let redirect_url =
                        format!("/myaccount/{identity_uuid}/security/sso/{ssopro_xsid}/");
                    Ok(Response::Redirect(redirect_url.as_str().into()))
                } else {
                    // Failed disconnect - show error page
                    doc.set_title("Disconnect Failed");
                    doc.add_body(html!(
                        panel {
                            header {
                                h2 { "Disconnect Failed" }
                            }
                            content {
                                p { "Failed to disconnect from the SSO provider." }
                                p { (output.message) }
                                hr;
                                a href=(format!("/myaccount/{identity_uuid}/security/sso/{ssopro_xsid}/")) {
                                    "Return to SSO Providers"
                                }
                            }
                        }
                    ));
                    Ok(Response::HTML(doc.into()))
                }
            }
            Err(e) => {
                approck::error!("Failed to disconnect SSO provider: {}", e);

                // Show error page
                doc.set_title("Disconnect Error");
                doc.add_body(html!(
                    panel {
                        header {
                            h2 { "Disconnect Error" }
                        }
                        content {
                            p { "An error occurred while trying to disconnect from the SSO provider." }
                            p { "Please try again later." }
                            hr;
                            a href=(format!("/myaccount/{identity_uuid}/security/sso/{ssopro_xsid}/")) {
                                "Return to SSO Providers"
                            }
                        }
                    }
                ));
                Ok(Response::HTML(doc.into()))
            }
        }
    }
}

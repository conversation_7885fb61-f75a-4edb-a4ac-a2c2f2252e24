#[approck::http(GET /myaccount/{identity_uuid:Uuid}/security/email; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Edit Email Address");

        // Get account data using our consolidated API
        let detail_input = crate::api::myaccount::security::setting::detail::Input {
            identity_uuid: path.identity_uuid,
        };

        let detail_response =
            crate::api::myaccount::security::setting::detail::call(app, identity, detail_input)
                .await?;

        let current_email = match detail_response {
            crate::api::myaccount::security::setting::detail::Response::Output(output) => {
                output.email
            }
            _ => None,
        };

        let form_panel = {
            let mut panel = bux::component::save_cancel_form_panel(
                "Edit Email Address",
                &crate::ml_myaccount_security(path.identity_uuid),
            );
            panel.set_id("edit-email-form");
            panel.set_hidden("identity_uuid", path.identity_uuid.to_string());
            panel.add_body(html!(
                (bux::input::text::string::name_label_value(
                    "email",
                    "Email Address:",
                    current_email.as_deref()
                ))
            ));
            panel
        };

        doc.set_body_display_narrow();
        doc.add_body(html!((form_panel)));
        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod update {
    use granite::NestedError;
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
        #[gtype(trim=both; max=255; no_empty;)]
        pub email: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub identity_uuid: Uuid,
        pub url: String,
    }

    pub async fn call(app: App, _identity: Identity, input: Input) -> Result<Response> {
        if !granite::validate_email(&input.email) {
            return Ok(Response::ValidationError(NestedError {
                outer: "Invalid email address".to_string(),
                inner: None,
            }));
        }

        let mut dbcx = app.postgres_dbcx().await?;
        let dbtx = dbcx
            .transaction()
            .await
            .amend(|e| e.add_context("starting transaction"))?;

        // Update email in identity table
        granite::pg_execute!(
            db = dbtx;
            args = {
                $identity_uuid: &input.identity_uuid,
                $email: &input.email,
            };
            UPDATE
                auth_fence.identity
            SET
                email = $email::text
            WHERE
                identity_uuid = $identity_uuid::uuid
        )
        .await?;

        dbtx.commit()
            .await
            .amend(|e| e.add_context("committing transaction"))?;

        Ok(Response::Output(Output {
            identity_uuid: input.identity_uuid,
            url: crate::ml_myaccount_security(input.identity_uuid),
        }))
    }
}

#[approck::http(GET /myaccount/{identity_uuid:Uuid}/security/password; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Change Password");

        // Get username details via API
        let detail_input = crate::api::myaccount::security::detail::Input {
            identity_uuid: path.identity_uuid,
        };

        let detail_response =
            crate::api::myaccount::security::detail::call(app, identity, detail_input).await?;

        let current_username = match detail_response {
            crate::api::myaccount::security::detail::Response::Output(output) => output.username,
            _ => None,
        };

        let mut register_panel = bux::component::save_cancel_form_panel(
            &format!(
                "Change Password For User: {}",
                current_username.unwrap_or_else(|| "Unknown".to_string())
            ),
            &crate::ml_myaccount_security(path.identity_uuid),
        );
        register_panel.set_id("edit-password-form");

        #[rustfmt::skip]
        register_panel.add_body(html!(
            input type="hidden" name="identity_uuid" value=(path.identity_uuid.to_string()) {}
            (bux::component::password_creator::password_creator_without_title())
        ));

        doc.add_body(html!(
            bux-action-panel {
                (register_panel)
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod update {
    use granite::NestedError;
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
        #[gtype(trim=both; min=8; max=128; no_empty;)]
        pub password: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub identity_uuid: Uuid,
        pub success_message: String,
        pub url: String,
    }

    pub async fn call(app: App, _identity: Identity, input: Input) -> Result<Response> {
        // check password strength
        let password_strength = granite::password::password_analysis(&input.password);
        if !password_strength.acceptable {
            let input_error = Input_Error {
                identity_uuid: None,
                password: Some("Password does not meet strength requirements".into()),
            };
            return Ok(Response::ValidationError(NestedError {
                outer: "Validation Error".to_string(),
                inner: Some(input_error),
            }));
        }

        let mut dbcx = app.postgres_dbcx().await?;

        // Transaction block
        {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            // Verify the identity exists and has a login
            let login_exists = granite::pg_row_option!(
                db = dbtx;
                args = {
                    $identity_uuid: &input.identity_uuid,
                };
                row = {
                    identity_uuid: Uuid,
                    username: String,
                };
                SELECT
                    identity_uuid,
                    username
                FROM
                    auth_fence.login
                WHERE
                    identity_uuid = $identity_uuid::uuid
                    AND active = true
            )
            .await?;

            if login_exists.is_none() {
                let input_error = Input_Error {
                    identity_uuid: Some("No active login found for this identity".into()),
                    password: None,
                };
                return Ok(Response::ValidationError(NestedError {
                    outer: "validation error".to_string(),
                    inner: Some(input_error),
                }));
            };

            // Generate salt and hash password
            let salt = crate::api::generate_salt();
            let password_hash = crate::api::hash_password(&input.password, &salt);

            // Update password
            granite::pg_execute!(
                db = dbtx;
                args = {
                    $identity_uuid: &input.identity_uuid,
                    $password_hash: &password_hash,
                    $salt: &salt,
                };
                UPDATE
                    auth_fence.login
                SET
                    password_base64_pbkdf2_sha256_hash = $password_hash::text,
                    password_salt = $salt::text
                WHERE
                    identity_uuid = $identity_uuid::uuid
                    AND active = true
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;

            Ok(Response::Output(Output {
                identity_uuid: input.identity_uuid,
                success_message: "Password has been updated".to_string(),
                url: crate::ml_myaccount_security(input.identity_uuid),
            }))
        }
    }
}

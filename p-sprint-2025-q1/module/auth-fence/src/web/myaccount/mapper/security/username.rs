#[approck::http(GET /myaccount/{identity_uuid:Uuid}/security/username; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Edit Username");

        // Get username details via API
        let detail_input = crate::api::myaccount::security::detail::Input {
            identity_uuid: path.identity_uuid,
        };

        let detail_response =
            crate::api::myaccount::security::detail::call(app, identity, detail_input).await?;

        let (current_username, show_helper_text) = match detail_response {
            crate::api::myaccount::security::detail::Response::Output(output) => {
                (output.username, !output.has_login)
            }
            _ => (None, true), // Default to showing helper text if there's an error
        };

        let mut register_panel = bux::component::save_cancel_form_panel(
            "Edit Username",
            &crate::ml_myaccount_security_status(path.identity_uuid),
        );
        register_panel.set_id("edit-username-form");

        let username = match current_username {
            Some(username) => Some(username),
            None => identity.email(),
        };

        #[rustfmt::skip]
        register_panel.add_body(html!(
            input type="hidden" name="identity_uuid" value=(path.identity_uuid.to_string()) {}
            (bux::input::text::string::name_label_value("username", "Username", username.as_deref()))
            @if show_helper_text {
                hr;
                p.help-text {
                    "Your email address is suggested as a username, but you can use any unique identifier you prefer."
                }
            }
        ));

        doc.add_body(html!(
            bux-action-panel {
                (register_panel)
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod update {
    use granite::NestedError;
    use granite::ResultExt;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub identity_uuid: Uuid,
        #[gtype(trim=both; max=64; no_empty;)]
        pub username: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub identity_uuid: Uuid,
        pub username: String,
        pub success_message: String,
        pub url: String,
    }

    pub async fn call(app: App, _identity: Identity, input: Input) -> Result<Response> {
        let mut dbcx = app.postgres_dbcx().await?;

        // Transaction block
        {
            let dbtx = dbcx
                .transaction()
                .await
                .amend(|e| e.add_context("starting transaction"))?;

            // Verify the identity exists
            let identity_exists = granite::pg_row_option!(
                db = dbtx;
                args = {
                    $identity_uuid: &input.identity_uuid,
                };
                row = {
                    identity_uuid: Uuid,
                    name: String,
                };
                SELECT
                    identity_uuid,
                    name
                FROM
                    auth_fence.identity
                WHERE
                    identity_uuid = $identity_uuid::uuid
            )
            .await?;

            if identity_exists.is_none() {
                let input_error = Input_Error {
                    identity_uuid: Some("Identity UUID does not exist".into()),
                    username: None,
                };
                return Ok(Response::ValidationError(NestedError {
                    outer: "validation error".to_string(),
                    inner: Some(input_error),
                }));
            };

            // Check if username already exists for a different identity
            match granite::pg_row_option!(
                db = dbtx;
                args = {
                    $username: &input.username,
                    $identity_uuid: &input.identity_uuid,
                };
                row = {
                    identity_uuid: Uuid,
                    username: String,
                };
                SELECT
                    identity_uuid,
                    username
                FROM
                    auth_fence.login
                WHERE
                    LOWER(username) = LOWER($username::text)
                    AND identity_uuid != $identity_uuid::uuid
                    AND active = true
            )
            .await
            {
                Ok(Some(_)) => {
                    let input_error = Input_Error {
                        identity_uuid: None,
                        username: Some("Username already exists for a different identity".into()),
                    };
                    return Ok(Response::ValidationError(NestedError {
                        outer: "validation error".to_string(),
                        inner: Some(input_error),
                    }));
                }
                Ok(None) => {}
                Err(e) => {
                    return Err(
                        granite::process_error!("Error checking for existing username")
                            .add_context(e),
                    );
                }
            }

            // Update only the username
            granite::pg_execute!(
                db = dbtx;
                args = {
                    $identity_uuid: &input.identity_uuid,
                    $username: &input.username,
                };
                UPDATE
                    auth_fence.login
                SET
                    username = $username::text
                WHERE
                    identity_uuid = $identity_uuid::uuid
                    AND active = TRUE
            )
            .await?;

            dbtx.commit()
                .await
                .amend(|e| e.add_context("committing transaction"))?;

            Ok(Response::Output(Output {
                identity_uuid: input.identity_uuid,
                username: input.username,
                success_message: "Username has been updated".to_string(),
                url: crate::ml_myaccount_security(input.identity_uuid),
            }))
        }
    }
}

//-------------------------------------------------------------------------------------------------
// 1. Import Components

import "./mobile.mcss";
import "@bux/input/text/string.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code
import { SEC } from "@granite/lib.mts";
import { update } from "./mobileλ.mts";
import { go_back } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import FormPanel from "@bux/component/form_panel.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements
const $form = document.getElementById("edit-mobile-form") as HTMLFormElement;
const $identity_uuid: HTMLInputElement = SEC(HTMLInputElement, $form, "[name=identity_uuid]");
const $mobile: BuxInputTextString = SEC(BuxInputTextString, $form, "[name=mobile]");

// -------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

// 5. Write Code
// -------------------------------------------------------------------------------------------------

new FormPanel({
    $form,
    api: update.api,
    on_cancel: go_back,

    err: (errors) => {
        $mobile.set_e(errors.mobile);
    },

    get: () => {
        return {
            identity_uuid: $identity_uuid.value,
            mobile: $mobile.value_option,
        };
    },

    set: (value) => {
        $mobile.value = value.mobile;
    },

    out: (output) => {
        go_back(output.url);
    },
});

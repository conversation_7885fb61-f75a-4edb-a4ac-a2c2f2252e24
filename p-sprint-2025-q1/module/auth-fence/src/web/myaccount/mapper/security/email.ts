//-------------------------------------------------------------------------------------------------
// 1. Import Components

import "./email.mcss";
import "@bux/input/text/string.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import Code
import { SEC } from "@granite/lib.mts";
import { update } from "./emailλ.mts";
import { go_back } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mts";
import FormPanel from "@bux/component/form_panel.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements
const $form = document.getElementById("edit-email-form") as HTMLFormElement;
const $identity_uuid: HTMLInputElement = SEC(HTMLInputElement, $form, "[name=identity_uuid]");
const $email: BuxInputTextString = SEC(BuxInputTextString, $form, "[name=email]");

// -------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

// -------------------------------------------------------------------------------------------------
// 5. Write Code
new FormPanel({
    $form,
    api: update.api,
    on_cancel: go_back,

    err: (errors) => {
        $email.set_e(errors.email);
    },

    get: () => {
        return {
            identity_uuid: $identity_uuid.value,
            email: $email.value,
        };
    },

    set: (value) => {
        $email.value = value.email;
    },

    out: (output) => {
        go_back(output.url);
    },
});

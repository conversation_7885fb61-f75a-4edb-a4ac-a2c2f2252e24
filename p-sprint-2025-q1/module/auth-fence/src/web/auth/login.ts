import "./login.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/text/password.mts";
import "@bux/input/checkbox.mjs";

import { SE } from "@granite/lib.mts";
import { login } from "@crate/api/identity/loginλ.mjs";
import { go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mjs";
import BuxInputTextPassword from "@bux/input/text/password.mjs";
import BuxInputCheckbox from "@bux/input/checkbox.mjs";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $username: BuxInputTextString = SE($form, '[name="username"]');
const $password: BuxInputTextPassword = SE($form, '[name="password"]');
const $remember_me: BuxInputCheckbox = SE($form, '[name="remember_me"]');

new FormPanel({
    $form,
    api: login.api,

    err: (errors) => {
        $username.set_e(errors.username);
        $password.set_e(errors.password);
    },

    get: () => {
        return {
            username: $username.value,
            password: $password.value,
            remember_me: $remember_me.value,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        go_next(output.nuri);
    },
});

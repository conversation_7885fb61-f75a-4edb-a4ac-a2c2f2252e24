#[approck::http(GET /admin/auth/test/email; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.set_title("Test Email Sending");

        let mut form_panel =
            bux::component::add_cancel_form_panel("Send Test Email", "/admin/auth/test/email");

        #[rustfmt::skip]
        form_panel.add_body(maud::html!(
            (bux::input::text::string::name_label_value("from_email", "From Email:", None))
            (bux::input::text::string::name_label_value("to_email", "To Email:", None))
            (bux::input::text::string::name_label_value("subject", "Subject:", Some("Test Email")))
            (bux::input::textarea::string::name_label_value("message", "Message:", Some("This is a test email sent from Auth Fence.")))
        ));

        doc.add_body(html! {
            section {
                (form_panel)
                hr;
                h3 { "Email Sending History" }
                ul #output {

                }
            }
        });

        Response::HTML(doc.into())
    }
}

#[approck::api]
pub mod send_email {
    use appstruct::EmailV1;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub from_email: String,
        pub to_email: String,
        pub subject: String,
        pub message: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub uuid: Uuid,
    }

    pub async fn call(app: App, _identity: Identity, input: Input) -> Result<Output> {
        let email = EmailV1 {
            from: input.from_email,
            to: input.to_email,
            subject: input.subject,
            message: input.message,
        };

        let output = app.send_email_v1(email).await?;

        Ok(Output { uuid: output.uuid })
    }
}

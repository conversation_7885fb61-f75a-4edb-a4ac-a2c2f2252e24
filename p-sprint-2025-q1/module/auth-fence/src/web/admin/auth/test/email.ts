// -------------------------------------------------------------------------------------------------
// 1. Import Components

import "@bux/component/form_panel.mts";
import "@bux/input/text/string.mts";
import "@bux/input/textarea/string.mts";
import "./email.mcss";

// -------------------------------------------------------------------------------------------------
// 2. Import code

import { FormPanel } from "@bux/component/form_panel.mts";
import BuxInputTextString from "@bux/input/text/string.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import { SEC } from "@granite/lib.mts";
import { send_email } from "./emailλ.mts";
import { go_back } from "@bux/singleton/nav_stack.mts";
import { HS } from "@granite/lib.mts";

// -------------------------------------------------------------------------------------------------
// 3. Get Elements

const $form = SEC(HTMLFormElement, document, "form.form-panel");
const $from_email = SEC(BuxInputTextString, $form, '[name="from_email"]');
const $to_email = SEC(BuxInputTextString, $form, '[name="to_email"]');
const $subject = SEC(BuxInputTextString, $form, '[name="subject"]');
const $message = SEC(BuxInputTextareaString, $form, '[name="message"]');
const $output = SEC(HTMLUListElement, document, "#output");

// -------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

// -------------------------------------------------------------------------------------------------
// 5. Form Panel

new FormPanel({
    $form,
    api: send_email.api,
    on_cancel: go_back,

    err: (errors) => {
        $from_email.set_e(errors.from_email);
        $to_email.set_e(errors.to_email);
        $subject.set_e(errors.subject);
        $message.set_e(errors.message);
    },

    get: () => {
        return {
            from_email: $from_email.value,
            to_email: $to_email.value,
            subject: $subject.value,
            message: $message.value,
        };
    },

    set: (value) => {
        $from_email.value = value.from_email;
        $to_email.value = value.to_email;
        $subject.value = value.subject;
        $message.value = value.message;
    },

    out: (output) => {
        const $li = document.createElement("li");
        $li.textContent = `Email sent: ${HS(output.uuid)}`;
        $output.appendChild($li);
    },
});

#[approck::http(GET /admin/auth/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Result<Response> {
        doc.add_body(maud::html!(
            h2 {
                a class="" href="/admin/auth/identity/" { "Manage Identities" }
            }
            h2 {
                a class="" href="/admin/auth/role/" { "Manage Roles" }
            }
            h2 {
                a class="" href="/admin/auth/permission/" { "Manage Permissions" }
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

import "@bux/input/checkbox.mjs";

import { update } from "@crate/api/admin/identity/permissionλ.mts";

import { SE } from "@granite/lib.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";
import BuxInputCheckbox from "@bux/input/checkbox.mjs";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $identity_uuid: HTMLInputElement = SE($form, '[name="identity_uuid"]');
const $permission_uuid: NodeListOf<BuxInputCheckbox> = $form.querySelectorAll(
    'bux-input-checkbox[name="permission_uuid"]',
);

new FormPanel({
    $form,
    api: update.api,
    on_cancel: go_back,

    err: (_errors) => {
        // No specific field errors to handle
        console.log("Error: ", _errors);
    },

    get: () => {
        return {
            identity_uuid: $identity_uuid.value,
            permissions: Array.from($permission_uuid)
                .filter((checkbox) => {
                    const result = checkbox.get();
                    if ("Ok" in result) {
                        return result.Ok;
                    } else {
                        return false;
                    }
                })
                .map((checkbox) => checkbox.get_value() || ""),
        };
    },

    set: (_value) => {
        // No need to set values
    },

    out: (_output) => {
        // set and sanitize the url
        go_next(`/admin/auth/identity/${$identity_uuid.value}/`);
    },
});

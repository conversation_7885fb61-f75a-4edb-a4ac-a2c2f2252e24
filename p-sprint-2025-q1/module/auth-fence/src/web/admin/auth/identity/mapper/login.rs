#[approck::http(GET /admin/auth/identity/{identity_uuid:Uuid}/login; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        doc: Document,
        identity: Identity,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Set Login");
        doc.add_css("./login.css");
        doc.add_js("./login.js");

        // load the identity
        let identity_uuid = path.identity_uuid;
        let identity_detail = crate::api::admin::identity::detail::detail::call(
            app,
            identity,
            crate::api::admin::identity::detail::detail::Input { identity_uuid },
        )
        .await?;

        let login = crate::api::admin::identity::login::detail::call(
            app,
            identity,
            crate::api::admin::identity::login::detail::Input { identity_uuid },
        )
        .await?;

        let mut login_panel = bux::component::save_cancel_form_panel(
            "Set Login",
            identity_detail.ml_admin("").as_str(),
        );
        let username = match login.login.clone() {
            Some(login) => login.username,
            None => match identity_detail.email {
                Some(email) => email,
                None => "".to_string(),
            },
        };

        login_panel.add_body(html!(
            input type="hidden" name="identity_uuid" value=(identity_uuid) {}
            h5 { "User login for: " (identity_detail.name) }
            p {
                @match login.login {
                    Some(login_info) => {
                        @match login_info.active {
                            true => {
                                "Username login is active."
                            }
                            false => {
                                "Username login is inactive."
                            }
                        }
                    }
                    None => {
                        "Username login is not set."
                    }
                }
            }
            (bux::input::text::string::name_label_value("username", "Username", Some(&username)))
            (bux::input::text::password::cur_name_label("password", "Password"))
        ));

        doc.add_body(html! {
            div.bux-narrow-sm {
                (login_panel)
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::http(GET /admin/auth/identity/{identity_uuid:Uuid}/delete; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::api::admin::identity::detail::detail;
        use maud::html;

        // Get identity details
        let identity_detail = detail::call(
            app,
            identity,
            detail::Input {
                identity_uuid: path.identity_uuid,
            },
        )
        .await?;

        doc.set_title("Delete Identity");

        let title = format!("Delete Identity: {}?", identity_detail.name);

        let mut panel = bux::component::delete_cancel_form_panel(
            &title,
            &crate::api::admin::identity::ml_admin(&path.identity_uuid, ""),
        );
        panel.set_hidden("identity_uuid", path.identity_uuid.to_string());

        #[rustfmt::skip]
        panel.add_body(maud::html!(
            p {
                "This will deactivate the identity "
                strong { (identity_detail.name) }
                " (" (identity_detail.identity_type) "). "
                "The identity will no longer appear in active identity lists, but all historical data will be preserved."
            }
            p {
                "Any roles and permissions currently assigned to this identity will remain assigned, but the identity will not be able to access the system."
            }
            @if identity_detail.email.is_some() {
                p {
                    "This identity is currently associated with email "
                    strong { (identity_detail.email.as_deref().unwrap_or("Unknown")) }
                    ". The email association will remain intact."
                }
            }
            (bux::input::checkbox::name_label_checked("confirm", "I understand the above and want to proceed with deleting this identity.", false))
        ));

        doc.add_body(html!(
            bux-action-panel {
                (panel)
            }
        ));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::http(GET /admin/auth/identity/{identity_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    use maud::html;

    use crate::api::admin::identity::detail::detail::Source as PermissionSource;

    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        let identity_uuid = path.identity_uuid;
        let identity_detail = crate::api::admin::identity::detail::detail::call(
            app,
            identity,
            crate::api::admin::identity::detail::detail::Input { identity_uuid },
        )
        .await?;

        // Fetch login details for the identity
        let login_details = match crate::api::admin::identity::login::detail::call(
            app,
            identity,
            crate::api::admin::identity::login::detail::Input { identity_uuid },
        )
        .await
        {
            Ok(details) => Some(details),
            Err(e) => {
                approck::warn!(
                    "Failed to fetch login details for identity {}: {}",
                    identity_uuid,
                    e
                );
                None
            }
        };

        // Fetch SSO providers for the identity
        let sso_providers = match crate::api::identity::sso::providers::index::call(
            app,
            identity,
            crate::api::identity::sso::providers::index::Input { identity_uuid },
        )
        .await
        {
            Ok(providers) => Some(providers.providers),
            Err(e) => {
                approck::warn!(
                    "Failed to fetch SSO providers for identity {}: {}",
                    identity_uuid,
                    e
                );
                None
            }
        };

        doc.set_title("Identity Details");

        // Prepare display values using structured functional blocks
        let display_values = {
            let display_email = identity_detail
                .email
                .as_deref()
                .unwrap_or("<EMAIL>");
            let display_name = &identity_detail.name;

            (display_email, display_name)
        };

        // Prepare URLs for edit links using structured functional blocks
        let urls = {
            let edit_url = format!("/admin/auth/identity/{identity_uuid}/edit");
            let roles_url = identity_detail.ml_admin("roles");
            let permissions_url = identity_detail.ml_admin("permissions");

            (edit_url, roles_url, permissions_url)
        };

        // Create separate InsightDeck instances for different logical groupings using structured functional blocks

        // 1. Identity Identification InsightDeck (with action buttons in header)
        let identity_identification = {
            let mut deck =
                bux::component::insight_deck::InsightDeck::new("Identity Identification");
            deck.description("Core identity identifiers and account creation information.");

            // Add general action buttons to the header (edit/delete only)
            deck.add_button(bux::button::link::label_icon_class(
                "Edit",
                "fas fa-pencil-alt",
                &urls.0,
                "sm",
            ));
            deck.add_button(html!(" "));
            deck.add_button(bux::button::link::label_icon_class(
                "Delete",
                "far fa-trash-alt",
                &format!("/admin/auth/identity/{identity_uuid}/delete"),
                "danger sm",
            ));

            deck.add_basic_row(
                "fas fa-id-badge",
                "Identity UUID",
                html!((identity_detail.identity_uuid)),
            );

            deck.add_basic_row(
                "fas fa-user-tag",
                "Identity Type",
                html!((identity_detail.identity_type)),
            );

            deck.add_basic_row(
                "fas fa-calendar-plus",
                "Created On",
                html!((identity_detail.create_ts)),
            );

            deck
        };

        // 2. Account Information InsightDeck
        let account_information = {
            let mut deck = bux::component::insight_deck::InsightDeck::new("Account Information");
            deck.description("Login credentials and authentication methods for this identity.");

            if let Some(ref email) = identity_detail.email {
                deck.add_edit_row_email(Some(email), &urls.0);
            } else {
                deck.add_edit_row_email(None, &urls.0);
            }

            // Login information
            if let Some(ref login) = login_details {
                if let Some(ref login_info) = login.login {
                    deck.add_edit_row(
                        "fas fa-user-circle",
                        "Username",
                        html!((login_info.username)),
                        &urls.0,
                    );

                    deck.add_edit_row(
                        "fas fa-toggle-on",
                        "Login Status",
                        html! {
                            @if login_info.active {
                                label-tag.success { "Active" }
                            } @else {
                                label-tag.warning { "Inactive" }
                            }
                        },
                        &urls.0,
                    );
                } else {
                    deck.add_basic_row(
                        "fas fa-user-circle",
                        "Username",
                        html!(span { "No username/password login configured" }),
                    );
                }
            } else {
                deck.add_basic_row(
                    "fas fa-user-circle",
                    "Username",
                    html!(span { "Login information unavailable" }),
                );
            }

            // SSO Sign-in methods if available
            if let Some(ref providers) = sso_providers {
                let google_provider = providers
                    .iter()
                    .find(|p| p.ssopro_xsid.to_lowercase() == "google");
                let microsoft_provider = providers
                    .iter()
                    .find(|p| p.ssopro_xsid.to_lowercase() == "microsoft");

                // Google
                deck.add_basic_row(
                    "fab fa-google",
                    "Login with Google",
                    html! {
                        @if let Some(google) = google_provider {
                            @if google.is_connected {
                                label-tag.success { "Enabled" }
                            } @else {
                                label-tag.default { "Not Enabled" }
                            }
                        } @else {
                            label-tag.default { "Not Available" }
                        }
                    },
                );

                // Microsoft
                deck.add_basic_row(
                    "fab fa-microsoft",
                    "Login with Microsoft",
                    html! {
                        @if let Some(microsoft) = microsoft_provider {
                            @if microsoft.is_connected {
                                label-tag.success { "Enabled" }
                            } @else {
                                label-tag.default { "Not Enabled" }
                            }
                        } @else {
                            label-tag.default { "Not Available" }
                        }
                    },
                );
            }

            // Add note if available
            if let Some(ref note) = identity_detail.note {
                if !note.trim().is_empty() {
                    deck.add_edit_row("fas fa-sticky-note", "Note", html!((note)), &urls.0);
                }
            }

            // Add avatar URI if available
            if let Some(ref avatar_uri) = identity_detail.avatar_uri {
                if !avatar_uri.trim().is_empty() {
                    deck.add_edit_row("fas fa-image", "Avatar URI", html!((avatar_uri)), &urls.0);
                }
            }

            deck
        };

        // 3. Authorization Information InsightDeck
        let authorization_information = {
            let mut deck =
                bux::component::insight_deck::InsightDeck::new("Authorization Information");
            deck.description("Roles and permissions assigned to this identity.");

            // Add authorization-related action buttons to the header
            deck.add_button(bux::button::link::label_icon_class(
                "Manage Roles",
                "fas fa-users-cog",
                &urls.1,
                "sm primary",
            ));
            deck.add_button(html!(" "));
            deck.add_button(bux::button::link::label_icon_class(
                "Manage Permissions",
                "fas fa-key",
                &urls.2,
                "sm secondary",
            ));

            deck.add_basic_row(
                "fas fa-users-cog",
                "Roles",
                html! {
                    @if identity_detail.roles.is_empty() {
                        span { "No roles assigned" }
                    } @else {
                        @for role in &identity_detail.roles {
                            div { (role.name) }
                        }
                    }
                },
            );

            deck.add_basic_row(
                "fas fa-key",
                "Permissions",
                html! {
                    @if identity_detail.permissions.is_empty() {
                        span { "No permissions assigned" }
                    } @else {
                        @for perm in &identity_detail.permissions {
                            div {
                                (perm.name)
                                small {
                                    em {
                                        @match perm.source {
                                            PermissionSource::Role => { " (Inherited)" }
                                            PermissionSource::Permission => { " (Assigned)" }
                                            PermissionSource::RolePermission => { " (Inherited, Assigned)" }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
            );

            deck.add_edit_row(
                "fas fa-toggle-on",
                "Status",
                html! {
                    @if identity_detail.active {
                        label-tag.success { "Active" }
                    } @else {
                        label-tag.warning { "Inactive" }
                    }
                },
                &urls.0,
            );

            deck
        };

        let html_content = html!(
            insight-deck {
                grid-12 {
                    cell-3 {
                        panel {
                            content {
                                contact-info {
                                    h1 { (display_values.1) }
                                    p.email {
                                        @if let Some(email) = &identity_detail.email {
                                            a href=(format!("mailto:{}", email)) { (email) }
                                        } @else {
                                            (display_values.0)
                                        }
                                    }
                                    hr;
                                    @if identity_detail.active {
                                        label-tag.success { "Active Identity" }
                                    } @else {
                                        label-tag.warning { "Inactive Identity" }
                                    }
                                }
                            }
                        }
                    }
                    cell-9 {
                        // This is the new rendering implementation for identity details using the insight deck
                        (identity_identification)
                        (account_information)
                        (authorization_information)
                    }
                }
            }
        );

        doc.add_body(html_content);
        Ok(Response::HTML(doc.into()))
    }
}

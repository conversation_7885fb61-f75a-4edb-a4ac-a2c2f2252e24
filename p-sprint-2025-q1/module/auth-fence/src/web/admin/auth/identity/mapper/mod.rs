pub mod delete;
pub mod edit;
pub mod index;
pub mod login;
pub mod permissions;
pub mod roles;

#[approck::prefix(/admin/auth/identity/{identity_uuid:Uuid}/)]
pub mod prefix {
    pub async fn menu(app: App, menu: Menu, _identity: Identity, identity_uuid: Uuid) {
        let current_name = app.uuid_to_label(identity_uuid);

        menu.set_label_name_uri("Details", current_name, &ml(&identity_uuid, ""));
        menu.add_link("Login", &ml(&identity_uuid, "login"));
        menu.add_link("Roles", &ml(&identity_uuid, "roles"));
        menu.add_link("Permissions", &ml(&identity_uuid, "permissions"));
    }

    pub fn ml(identity_uuid: &granite::Uuid, uri: &str) -> String {
        format!("/admin/auth/identity/{identity_uuid}/{uri}")
    }
}

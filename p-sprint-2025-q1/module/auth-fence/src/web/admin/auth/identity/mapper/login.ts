import "./login.mcss";
import "@bux/input/text/string.mts";
import "@bux/input/text/password.mts";
import "@bux/input/checkbox.mjs";

import { SE } from "@granite/lib.mts";
import { save } from "@crate/api/admin/identity/loginλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mjs";
import BuxInputTextPassword from "@bux/input/text/password.mjs";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $identity_uuid: HTMLInputElement = SE($form, '[name="identity_uuid"]');
const $username: BuxInputTextString = SE($form, '[name="username"]');
const $password: BuxInputTextPassword = SE($form, '[name="password"]');

new FormPanel({
    $form,
    api: save.api,
    on_cancel: go_back,

    err: (errors) => {
        console.log("errors: ", errors);
        $username.set_e(errors.username);
        $password.set_e(errors.password);
    },

    get: () => {
        return {
            identity_uuid: $identity_uuid.value,
            username: $username.value,
            password: $password.value,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        go_next(output.next_uri);
    },
});

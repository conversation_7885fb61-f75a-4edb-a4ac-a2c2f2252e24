#[approck::http(GET /admin/auth/identity/{identity_uuid:Uuid}/roles; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        let identity_uuid = path.identity_uuid;
        let identity_detail = crate::api::admin::identity::detail::detail::call(
            app,
            identity,
            crate::api::admin::identity::detail::detail::Input { identity_uuid },
        )
        .await?;

        doc.set_title("Assign Roles");
        doc.add_css("/admin/auth/identity/mapper/roles.css");
        doc.add_js("/admin/auth/identity/mapper/roles.js");

        let identity_roles: Vec<granite::Uuid> = crate::api::admin::identity::role::list::call(
            app,
            identity,
            crate::api::admin::identity::role::list::Input { identity_uuid },
        )
        .await?
        .roles
        .iter()
        .map(|role| role.role_uuid)
        .collect();

        let roles = match crate::api::admin::role::list::list::call(
            app,
            identity,
            crate::api::admin::role::list::list::Input { keyword: None },
        )
        .await
        {
            Ok(output) => output.roles,
            Err(e) => return Err(granite::process_error!("Unable to load roles").add_context(e)),
        };

        let title = format!("Assign Roles to Identity {}", identity_detail.name);
        let mut panel =
            bux::component::save_cancel_form_panel(&title, &identity_detail.ml_admin(""));
        panel.add_body(maud::html!(
            input type="hidden" name="identity_uuid" value=(identity_uuid)
            identity-role-form {
                @for role in roles {
                    div {
                        (bux::input::checkbox::name_label_value_checked(
                            "role_uuid",
                            &role.name,
                            &role.role_uuid,
                            identity_roles.contains(&role.role_uuid)
                        ))
                    }
                }
            }
        ));

        doc.add_body(maud::html! {
            div.bux-narrow-sm {
                (panel)
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}

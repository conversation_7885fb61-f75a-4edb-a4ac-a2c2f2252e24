import "./add.mcss";
import "@bux/input/text/string.mjs";
import "@bux/input/textarea/string.mjs";
import "@bux/input/checkbox.mjs";
import "@bux/input/select/nilla.mjs";

import { SE } from "@granite/lib.mts";
import { add } from "@crate/api/admin/identity/addλ.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";

import BuxInputTextString from "@bux/input/text/string.mjs";
import BuxInputTextArea from "@bux/input/textarea/string.mjs";
import BuxInputCheckbox from "@bux/input/checkbox.mjs";
import BuxInputSelectNilla from "@bux/input/select/nilla.mjs";
import FormPanel from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $identity_type: BuxInputSelectNilla<string> = SE($form, '[name="identity_type"]');
const $name: BuxInputTextString = SE($form, '[name="name"]');
const $email: BuxInputTextString = SE($form, '[name="email"]');
const $note: BuxInputTextArea = SE($form, '[name="note"]');
const $avatar_uri: BuxInputTextString = SE($form, '[name="avatar_uri"]');
const $active: BuxInputCheckbox = SE($form, '[name="active"]');

new FormPanel({
    $form,
    api: add.api,
    on_cancel: go_back,

    err: (errors) => {
        $identity_type.set_e(errors.identity_type);
        $name.set_e(errors.name);
        $email.set_e(errors.email);
        $note.set_e(errors.note);
        $avatar_uri.set_e(errors.avatar_uri);
        $active.set_e(errors.active);
    },

    get: () => {
        return {
            identity_type: $identity_type.value,
            name: $name.value,
            email: $email.value_option,
            note: $note.value_option,
            avatar_uri: $avatar_uri.value_option,
            active: $active.value,
        };
    },

    set: (_value) => {
    },

    out: (output) => {
        go_next(output.detail_url);
    },
});

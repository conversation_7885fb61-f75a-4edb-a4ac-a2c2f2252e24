import "@bux/input/checkbox.mjs";
import "./permissions.mcss";

import { update } from "@crate/api/admin/role/permissionλ.mts";
import { SE } from "@granite/lib.mts";
import { go_back, go_next } from "@bux/singleton/nav_stack.mts";
import BuxInputCheckbox from "@bux/input/checkbox.mjs";
import { FormPanel } from "@bux/component/form_panel.mts";

const $form = SE(document, "form.form-panel") as HTMLFormElement;
const $role_uuid: HTMLInputElement = SE($form, "[name=role_uuid]");
const $permission_uuid: NodeListOf<BuxInputCheckbox> = $form.querySelectorAll(
    'bux-input-checkbox[name^="permissions["]',
) as NodeListOf<BuxInputCheckbox>;

new FormPanel({
    $form,
    api: update.api,
    on_cancel: go_back,

    err: (_errors) => {
        // No specific field errors to handle
        console.log("Error: ", _errors);
    },

    get: () => {
        return {
            role_uuid: $role_uuid.value,
            permissions: Array.from($permission_uuid)
                .filter((checkbox) => {
                    const result = checkbox.get();
                    if ("Ok" in result) {
                        return result.Ok;
                    } else {
                        return false;
                    }
                })
                .map((checkbox) => checkbox.get_value() || ""),
        };
    },

    set: (_value) => {
        // No need to set values
    },

    out: (_output) => {
        // set and sanitize the url
        go_next(`/admin/auth/role/${$role_uuid.value}/`);
    },
});

#[approck::http(GET /admin/auth/role/{role_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        // Extract role UUID from the path and retrieve role record from database
        let role_uuid = path.role_uuid;

        // Get role details using the API
        let role = crate::api::admin::role::detail::detail::call(
            app,
            identity,
            crate::api::admin::role::detail::detail::Input { role_uuid },
        )
        .await?;

        // Get permissions associated with this role
        let permissions_output = crate::api::admin::role::permission::permission::call(
            app,
            identity,
            crate::api::admin::role::permission::permission::Input { role_uuid },
        )
        .await?;

        let permissions = permissions_output.permissions;

        // Set page title and load resources
        doc.set_title("Role Details");

        // Create info table for role details
        let mut info_table = bux::component::info_table(role);
        info_table.set_heading("Role Information");
        info_table.add_row(
            "ID:",
            |r| maud::html! { (r.role_psid.as_deref().unwrap_or("-")) },
        );
        info_table.add_row("Name:", |r| maud::html! { (r.name) });

        // Add a row with an edit permissions link
        info_table.add_row("Actions:", |_| {
            maud::html! {
                a.btn.btn-primary href=(format!("/admin/auth/role/{}/permissions", role_uuid)) {
                    "Edit Permissions"
                }
            }
        });

        // Create detail table for permissions
        let mut perm_table = bux::component::detail_table(permissions);
        perm_table.set_heading("Role Permissions");
        perm_table.add_column("Name", |p| maud::html! { (p.name) });
        perm_table.add_details_column(|p| {
            crate::api::admin::permission::ml_admin(&p.permission_uuid, "")
        });

        // Add tables to the document
        doc.add_body(maud::html! {
            section {
                (info_table)
            }
            section {
                (perm_table)
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}

pub mod identity;
pub mod index;
pub mod permission;
pub mod role;
pub mod test;

#[approck::prefix(/admin/auth/)]
pub mod prefix {
    pub fn menu(menu: Menu) {
        menu.set_label_uri("Auth Dashboard", "/admin/auth/");
        menu.add_link("Identities", "/admin/auth/identity/");
        menu.add_link("Roles", "/admin/auth/role/");
        menu.add_link("Permissions", "/admin/auth/permission/");
        menu.add_link("Test Email Sending", "/admin/auth/test/email");
    }
}

#[approck::http(GET /admin/auth/permission/{permission_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        // Get permission details from database
        let permission_uuid = path.permission_uuid;

        // Get permission details using the API
        let permission = crate::api::admin::permission::detail::detail::call(
            app,
            crate::api::admin::permission::detail::detail::Input { permission_uuid },
        )
        .await?;

        // Get roles associated with this permission
        let roles_output = crate::api::admin::permission::role::role::call(
            app,
            identity,
            crate::api::admin::permission::role::role::Input { permission_uuid },
        )
        .await?;

        let roles = roles_output.roles;

        doc.set_title("Permission Details");
        doc.add_css("/admin/auth/permission/mapper/index.css");
        doc.add_js("/admin/auth/permission/mapper/index.js");

        let mut table = bux::component::info_table(permission);
        table.set_heading("Permission Information");
        table.add_row("Name:", |p| html! { (p.name) });
        table.add_row(
            "Description:",
            |p| html! { (p.description.as_deref().unwrap_or("")) },
        );
        table.add_row("Created:", |p| html! { (p.create_ts) });
        table.add_row("Function:", |p| html! { (p.func.as_deref().unwrap_or("")) });
        table.add_row("Arguments:", |p| {
            html! {
                @if !p.args.is_empty() {
                    ul {
                        @for arg in &p.args {
                            li { (arg) }
                        }
                    }
                } @else {
                    "None"
                }
            }
        });

        doc.add_body(html!((table)));

        // Add roles panel if there are any roles
        if !roles.is_empty() {
            let mut roles_table = bux::component::detail_table(roles);
            roles_table.set_heading("Assigned Roles");
            roles_table.add_column("Role Name", |r| html! { (r.name) });
            roles_table.add_details_column(|r| crate::api::admin::role::ml_admin(&r.role_uuid, ""));

            doc.add_body(html!((roles_table)));
        }

        Ok(Response::HTML(doc.into()))
    }
}

pub struct AuthLogData {
    pub create_addr: String,
    pub session_token: String,
    pub identity_uuid: Option<granite::Uuid>,
    pub user_esid: Option<String>,
    pub user_email: Option<String>,
    pub auth_type: String,
    pub auth_action: String,
    pub auth_provider: Option<String>,
    pub success: bool,
    pub blocked: bool,
    pub data: Option<String>,
}

pub async fn auth_log(
    client: &impl approck_postgres::DB,
    log_entry: AuthLogData,
) -> granite::Result<()> {
    match granite::pg_execute!{
        row = {
            nonsense: i32,
        };
        args = {
            $create_addr: &log_entry.create_addr,
            $session_token: &log_entry.session_token,
            $identity_uuid: &log_entry.identity_uuid,
            $user_esid: &log_entry.user_esid,
            $user_email: &log_entry.user_email,
            $auth_type: &log_entry.auth_type,
            $auth_action: &log_entry.auth_action,
            $auth_provider: &log_entry.auth_provider,
            $success: &log_entry.success,
            $blocked: &log_entry.blocked,
        };
        db = client;
        INSERT INTO
            auth_fence.auth_log
            (create_addr, session_token, identity_uuid, user_esid, user_email, auth_type, auth_action, auth_provider, success, blocked)
        VALUES
            ($create_addr::text::inet, $session_token, $identity_uuid, $user_esid, $user_email, $auth_type, $auth_action, $auth_provider, $success, $blocked)
    }.await {
        Ok(_) => Ok(()),
        Err(e) => Err(granite::process_error!("Error logging auth").add_context(e))
    }
}

[package]
name = "auth-fence"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.approck.mod]
extends = ["approck", "appstruct", "bux", "granite", "appstruct::SendEmailV1"]

[dependencies]
approck = { workspace = true }
appstruct = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-redis = { workspace = true }
approck-postgres = { workspace = true }

async-trait = { workspace = true }
base64_light = { workspace = true }
indexmap = { workspace = true, features = ["serde"] }
maud = { workspace = true }
oauth2 = { workspace = true }
openidconnect = { workspace = true }
pbkdf2 = { workspace = true }
postgres-types = { workspace = true }
rand = { workspace = true }
reqwest = { workspace = true, features = ["json"] }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
sha2 = { workspace = true }
tokio = { workspace = true }
urlencoding = { workspace = true }


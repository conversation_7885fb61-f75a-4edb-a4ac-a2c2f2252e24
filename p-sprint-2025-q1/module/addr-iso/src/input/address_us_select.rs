use bux::Markup;

/// Renders a bux select dropdown containing all US states from the database
///
/// # Arguments
/// * `app` - Application instance that provides database access
/// * `name` - HTML name attribute for the select element
/// * `label` - Display label for the select element
/// * `selected_value` - Optional state code to pre-select
///
/// # Returns
/// * `granite::Result<Markup>` - HTML markup for the select element or error
pub async fn us_state_select(
    app: &impl crate::App,
    name: &str,
    label: &str,
    selected_value: Option<&str>,
) -> granite::Result<Markup> {
    let dbcx = app.postgres_dbcx().await?;

    let rows = list_states(&dbcx).await?;

    // Convert database rows to the format expected by bux::input::select::nilla::nilla_select
    let options: Vec<(&str, &str)> = rows
        .iter()
        .map(|(state_code, name)| (state_code.as_str(), name.as_str()))
        .collect();

    Ok(bux::input::select::nilla::nilla_select(
        name,
        label,
        &options,
        selected_value,
    ))
}

/// Renders a bux select dropdown containing all US states with help text
///
/// # Arguments
/// * `app` - Application instance that provides database access
/// * `name` - HTML name attribute for the select element
/// * `label` - Display label for the select element
/// * `selected_value` - Optional state code to pre-select
/// * `help` - Help text to display with the select element
///
/// # Returns
/// * `granite::Result<Markup>` - HTML markup for the select element or error
pub async fn us_state_select_with_help(
    app: &impl approck_postgres::App,
    name: &str,
    label: &str,
    selected_value: Option<&str>,
    help: &str,
) -> granite::Result<Markup> {
    let dbcx = app.postgres_dbcx().await?;

    let rows = list_states(&dbcx).await?;

    // Convert database rows to the format expected by bux::input::select::nilla::nilla_select_with_help
    let options: Vec<(&str, &str)> = rows
        .iter()
        .map(|(state_code, name)| (state_code.as_str(), name.as_str()))
        .collect();

    Ok(bux::input::select::nilla::nilla_select_with_help(
        name,
        label,
        &options,
        selected_value,
        help,
    ))
}

async fn list_states(dbcx: &impl approck_postgres::DB) -> granite::Result<Vec<(String, String)>> {
    let rows = granite::pg_row_vec!(
        db = dbcx;
        row = {
            state_code: String,
            name: String,
        };
        SELECT
            state_code,
            name
        FROM
            addr_iso.us_state
        ORDER BY
            name
    )
    .await?;

    Ok(rows
        .into_iter()
        .map(|row| (row.state_code, row.name))
        .collect())
}

[package]
name = "intake-silo"
version = "0.1.0"
edition = "2024"

[package.metadata.approck.mod]
extends = ["auth-fence"]

[dependencies]
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-redis = { workspace = true }
approck-postgres = { workspace = true }
auth-fence = { workspace = true }
regex = { workspace = true }
serde_json = { workspace = true }
mime = { workspace = true }
bytes = { workspace = true }
tokio = { workspace = true }
futures = { workspace = true }
postgres-types = { workspace = true }

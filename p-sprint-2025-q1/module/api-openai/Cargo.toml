[package]
name = "api-openai"
version = "0.1.0"
edition = "2024"

[package.metadata.approck.mod]
extends = ["approck", "bux", "granite", "auth-fence"]

[dependencies]
approck = { workspace = true }
approck-redis = { workspace = true }
auth-fence = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
reqwest = { workspace = true, features = ["json"] }
maud = { workspace = true }

// -------------------------------------------------------------------------------------------------
// 1. Import Components
import "./console.mcss";
import "@bux/input/textarea/string.mts";

// -------------------------------------------------------------------------------------------------
// 2. Import code
import { SEC } from "@granite/lib.mts";
import BuxInputTextareaString from "@bux/input/textarea/string.mts";
import { chat_completion } from "./consoleλ.mts";

// -------------------------------------------------------------------------------------------------
// 3. Find Elements

const $form = SEC(HTMLFormElement, document, "form#f1");
const $instructions = SEC(BuxInputTextareaString, $form, "[name=instructions]");
const $message = SEC(BuxInputTextareaString, $form, "[name=message]");
const $error = SEC(HTMLElement, $form, "error");
const $response = SEC(HTMLElement, $form, "#response");

// -------------------------------------------------------------------------------------------------
// 4. Bind Event Handlers

$form.addEventListener("submit", async (event) => {
    event.preventDefault();

    let input;
    {
        const r = chat_completion.Input_Partial_validate({
            instructions: $instructions.value,
            message: $message.value,
        });

        if ("Err" in r) {
            $error.textContent = r.Err.Outer;
            if ("Inner" in r.Err) {
                $message.set_e(r.Err.Inner.message);
            }
            $form.reportValidity();
            return;
        }
        input = r.Ok;
    }

    let output;
    {
        const r = await chat_completion.call(input);
        if ("ValidationError" in r) {
            const e = r.ValidationError[0];
            $error.textContent = e.Outer;
            if ("Inner" in e) {
                $message.set_e(e.Inner.message);
            }
            $form.reportValidity();
            return;
        } else if ("AuthorizationError" in r) {
            $error.textContent = r.AuthorizationError[0];
            return;
        } else if ("BadRequest" in r) {
            $error.textContent = r.BadRequest[0];
            return;
        } else if ("Error" in r) {
            $error.textContent = r.Error[0];
            return;
        }

        output = r.Output[0];
    }

    $response.textContent = output.responses.join("\n\n");
});

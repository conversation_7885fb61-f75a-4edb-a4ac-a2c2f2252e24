pub mod api;
pub mod core;
pub mod types;
pub mod util;
pub mod web;

use crate::types::CrsUserData;

pub trait App:
    approck::App + approck::server::App + approck_redis::App + approck_postgres::App + addr_iso::App
{
    fn api_crs_module(&self) -> &crate::ModuleStruct;

    #[allow(async_fn_in_trait)]
    async fn get_crs_user_data(
        &self,
        dbcx: &impl approck_postgres::DB,
        api_crs_user_uuid: granite::Uuid,
    ) -> granite::Result<CrsUserData>;
}

pub trait Identity: bux::Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }

    #[allow(async_fn_in_trait)]
    async fn user_read(
        &self,
        dbcx: &impl approck_postgres::DB,
        api_crs_user_uuid: granite::Uuid,
    ) -> bool;
    #[allow(async_fn_in_trait)]
    async fn user_write(
        &self,
        dbcx: &impl approck_postgres::DB,
        api_crs_user_uuid: granite::Uuid,
    ) -> bool;
}

pub trait Document: bux::document::Base {}

pub trait DocumentPublic: bux::document::Base {}

#[derive(serde::Deserialize)]
pub struct ModuleConfig {
    pub is_production: bool,
    pub api_base_url: String,
    pub api_key: String,
    pub secret_key: String,
}
pub struct ModuleStruct {
    is_production: bool,
    api_base_url: String,
    api_key: String,
    secret_key: String,
}

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;
    fn new(config: Self::Config) -> granite::Result<Self> {
        Ok(Self {
            is_production: config.is_production,
            api_base_url: config.api_base_url,
            api_key: config.api_key,
            secret_key: config.secret_key,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}

impl ModuleStruct {
    pub fn is_production(&self) -> bool {
        self.is_production
    }

    pub fn is_development(&self) -> bool {
        !self.is_production
    }

    pub fn get_base_url(&self) -> &str {
        &self.api_base_url
    }

    /// The primary mechanism for getting a server token
    pub async fn get_server_token(
        &self,
        redis: &mut approck_redis::RedisCX<'_>,
    ) -> granite::Result<crate::core::crs::types::CrsServerToken> {
        let token_redis_key = "api_crs::server_token";

        // TODO: Fix direct token expiration issue
        match redis.get_val::<Option<String>>(token_redis_key).await {
            Ok(Some(token)) => Ok(token.into()),
            Ok(None) => {
                let input = core::crs::direct_login::Input {
                    base_url: self.api_base_url.clone(),
                    apikey: self.api_key.clone(),
                    secret: self.secret_key.clone(),
                };
                let output = core::crs::direct_login::call_direct_login(input).await?;
                redis.set_val(token_redis_key, &output.token).await?;

                let expires_seconds = (output.expire_ts - granite::Utc::now()).num_seconds();
                redis.set_expire(token_redis_key, expires_seconds).await?;

                Ok(output.token.into())
            }
            Err(e) => Err(granite::Error::new(granite::ErrorType::ApiRequest)
                .add_context("Failed to load server token from redis".to_string())
                .add_context(crate::core::crs::types::Error::OtherError(e.to_string()))),
        }
    }
}

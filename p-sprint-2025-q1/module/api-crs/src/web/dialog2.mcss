/* Credit Report Dialog - Semantic Nested CSS */
dialog#api-crs-dialog2 {
    background: #fff;
    border: none;
    border-radius: .5rem;
    box-shadow: 0 .625rem 1.5625rem #00000026;
    padding: 2rem;
    max-width: 40rem;
    min-width: 30rem;
    width: 90vw;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0;

    &::backdrop {
        background: rgba(0,0,0,0.5);
        backdrop-filter: blur(0.25rem);
    }

    &.full-width {
        max-width: none;
        width: 95vw;
    }

    > header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 0.0625rem solid #dee2e6;
        padding-bottom: 1rem;
        margin-bottom: 1.5rem;

        h3 {
            margin: 0;
            color: #2c3e50;
        }

        a {
            color: #7f7f7f;
            text-decoration: none;

            &:hover {
                color: #1a252f;
            }
        }
    }

    > error {
        display: block;
        color: #721c24;
        padding: 1rem;
        background: #f8d7da;
        border: 0.0625rem solid #f5c6cb;
        border-radius: 0.375rem;
        margin-bottom: 1.5rem;

        &:empty {
            display: none;
        }
    }

    > dev-mode {
        display: block;
        color: #4B0082;
        padding: 0.5rem;
        background-color: #E6E6FA;
        border: 0.0625rem solid #800080;
        border-radius: 0.25rem;
        margin-bottom: 1rem;

        &:empty {
            display: none;
        }
    }

    #api-crs-dds {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;

        button {
            background: #f0f0f0;
            border: 0.0625rem solid #0d6efd;
            color: #4c4c4c;
            margin-bottom: 0.5rem;
        }
    }

    bux-input-select-gender {
        margin: 0;
        display: block;
    }


    dl {
        background: #f7f7f7;
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 0.5rem;

        > dt {
            font-weight: bold;
            margin-bottom: 0.25rem;
            margin-top: 0.5rem;
            border-top: 1px solid #ddd;
            padding-top: 0.5rem;

            &:first-child {
                border-top: none;
                margin-top: 0;
                padding-top: 0;
            }
        }

        > dd {
            margin-bottom: 0.5rem;
            color: #6c757d;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }

    x-compliance,
    x-authorization,
    x-notice,
    x-intro {
        display: block;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
        font-size: 0.9rem;
        line-height: 1.4;
        text-align: center;

        p {
            margin-bottom: 0;
        }
    }

    x-compliance {
        background: #f7f7f7;
        border: 0.0625rem solid #dee2e6;

        p {
            color: #6c757d;

            &:first-child {
                margin-bottom: 0.5rem;
                color: #495057;
            }
        }
    }

    x-authorization,
    x-notice {
        background: #e7f3ff;
        border: 0.0625rem solid #b3d9ff;

        p {
            color: #1565c0;
        }
    }

    x-intro {
        background: #f8f9fa;
        border: 0.0625rem solid #e9ecef;

        p {
            color: #495057;
        }
    }

    x-button-container {
        display: block;
        margin-top: 1.5rem;
        text-align: center;

        button, a {
            margin: 0.5rem 0.5rem 0.5rem 0;
        }

        a.primary {
            background-color: #0d6efd;
            border: 1px solid #0d6efd;
            color: white;
            padding: .375rem .75rem;
            border-radius: 0.375rem;
            text-decoration: none;
            display: inline-block;

            &:hover {
                background-color: #0b5ed7;
                border-color: #0a58ca;
                text-decoration: none;
            }
        }
    }

    x-errors {
        display: block;
        color: #dc3545;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin-bottom: 1rem;

        pre {
            margin-bottom: 0;
        }
    }

    x-success {
        display: block;
        text-align: center;
        margin-bottom: 1.5rem;

        .success-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        h4 {
            color: #198754;
            margin-bottom: 0.5rem;
        }

        p {
            color: #6c757d;
            margin-bottom: 1rem;
        }
    }

    .hidden {
        display: none;
    }
}
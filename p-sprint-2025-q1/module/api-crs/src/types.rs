use granite::{DateTimeUtc, Decimal, Uuid};
use serde::Deserialize;

//---------------------------------------------------------------------------------------------------------
// Custom deserializers for dates that can be either ISO timestamps or milliseconds since epoch

//---------------------------------------------------------------------------------------------------------

#[granite::gtype]
pub struct CrsUserData {
    pub api_crs_user_uuid: Uuid,
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
    pub gender: Option<String>,
    pub birth_date: Option<DateUtc>,
    pub address1: Option<String>,
    pub address2: Option<String>,
    pub city: Option<String>,
    pub state: Option<String>,
    pub zip: Option<String>,
}

//---------------------------------------------------------------------------------------------------------

#[derive(Debug, Deserialize)]
pub struct CrsUserLastReportUS1B {
    pub api_crs_user_uuid: Uuid,
    pub crs_user_uuid: Uuid,
    pub last_report_esid: String,
    pub last_report_ts: DateTimeUtc,
    pub last_report_data: US1B,
}

#[allow(non_snake_case)]
#[derive(Debug, Deserialize)]
pub struct US1B {
    #[serde(deserialize_with = "crate::util::deserialize_flexible_datetime")]
    pub generatedDate: DateTimeUtc,
    pub id: String,

    #[serde(default)]
    pub providerViews: Vec<US1BProviderView>,
}

impl US1B {
    pub fn to_debt_view(&self) -> CRDebtView {
        crate::core::credit_report::us1b_to_debt_view(self)
    }
}

#[allow(non_snake_case)]
#[derive(Debug, Deserialize)]
pub struct US1BProviderView {
    pub provider: String,

    #[serde(default)]
    pub installmentAccounts: Vec<US1BAccount>,

    #[serde(default)]
    pub mortgageAccounts: Vec<US1BAccount>,

    #[serde(default)]
    pub revolvingAccounts: Vec<US1BAccount>,

    #[serde(default)]
    pub otherAccounts: Vec<US1BAccount>,
}

#[allow(non_snake_case)]
#[derive(Debug, Deserialize)]
pub struct US1BAccount {
    pub id: String,
    pub isNegative: bool,
    pub accountName: String,
    pub accountOpen: bool,
    pub balanceAmount: US1BAmount,

    #[serde(default)]
    pub monthlyPayment: Option<US1BAmount>,

    #[serde(default)]
    #[serde(deserialize_with = "crate::util::deserialize_flexible_datetime_option")]
    pub lastActivityDate: Option<DateTimeUtc>,
}

#[allow(non_snake_case)]
#[derive(Debug, Deserialize)]
pub struct US1BAmount {
    pub amount: Decimal,
    pub currency: String,
}

// ---------------------------------------------------------------------------------------------------------

#[derive(Debug)]
pub struct CRDebtView {
    pub debts: Vec<CRDebt>,
    pub total_monthly_payment: granite::Decimal,
    pub total_balance: granite::Decimal,
}

#[derive(Debug)]
pub struct CRDebt {
    pub provider: String,
    pub id: String,
    pub account_type: String,
    pub name: String,
    pub balance: Decimal,
    pub monthly_payment: Decimal,
}

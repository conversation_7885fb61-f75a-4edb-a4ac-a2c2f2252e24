use granite::ResultExt;

#[derive(serde::Serialize)]
pub struct Input {
    pub base_url: String,
    pub server_token: super::types::CrsServerToken,
    pub api_crs_user_uuid: granite::Uuid,
    pub justification: String,
    pub is_demo: bool,
}

pub async fn call_direct_efx_latest_report(
    app: &impl crate::App,
    input: Input,
) -> granite::Result<String> {
    let dbcx = app.postgres_dbcx().await?;

    let use_demo_data = input.is_demo || app.api_crs_module().is_development();

    // load the user
    let user = crate::core::user::load(&dbcx, input.api_crs_user_uuid).await?;

    let crs_user_uuid = match user {
        crate::core::user::User::Verified(verified) => verified.crs_user_uuid,
        crate::core::user::User::WithReport(with_report) => with_report.crs_user_uuid,
        _ => {
            return Err(granite::Error::new(granite::ErrorType::InvalidOperation)
                .add_context(format!("api_crs_user_uuid: {}", input.api_crs_user_uuid))
                .set_external_message(
                    "User is not in the correct state for fetching latest report".to_string(),
                ));
        }
    };

    let endpoint_uri = format!(
        "{}/direct/efx-latest-report/{}",
        input.base_url, crs_user_uuid
    );

    // log request
    let api_crs_log_credit_pull_uuid = granite::pg_row!(
        db = dbcx;
        args = {
            $api_crs_user_uuid: &input.api_crs_user_uuid,
            $request_url: &endpoint_uri,
            $justification: &input.justification,
            $is_demo: &input.is_demo,
        };
        row = {
            api_crs_log_credit_pull_uuid: Uuid,
        };
        INSERT INTO api_crs.log_credit_pull (
            api_crs_user_uuid,
            justification,
            is_demo,
            request_ts,
            request_url
        ) VALUES (
            $api_crs_user_uuid,
            $justification,
            $is_demo,
            now(),
            $request_url
        )
        RETURNING api_crs_log_credit_pull_uuid
    )
    .await?
    .api_crs_log_credit_pull_uuid;

    // Get the response.  This is in a function so it can return early on success or errors
    let get_response = GetResponse::run(use_demo_data, &endpoint_uri, &input.server_token).await;

    // update log record
    {
        let (response_status, error_text) = match &get_response {
            GetResponse::Success { status, .. } => (status, None),
            GetResponse::ParsedError { status, text, .. } => (status, Some(text.clone())),
            GetResponse::ReqwestError { status, error } => (status, Some(error.to_string())),
            GetResponse::SerdeError { status, error } => (status, Some(error.to_string())),
        };

        // pg doesn't have unsigned 16bit integers
        let response_status: i32 = (*response_status).into();

        granite::pg_execute!(
            db = dbcx;
            args = {
                $api_crs_log_credit_pull_uuid: &api_crs_log_credit_pull_uuid,
                $response_status: &response_status,
                $response_error: &error_text,
            };
            UPDATE
                api_crs.log_credit_pull
            SET
                response_ts = now(),
                response_status = $response_status,
                response_error = $response_error
            WHERE
                api_crs_log_credit_pull_uuid = $api_crs_log_credit_pull_uuid
        )
        .await?;
    }

    // Return appropriately
    match get_response {
        GetResponse::Success { text, .. } => Ok(text),
        GetResponse::ParsedError { error, .. } => {
            Err(granite::Error::new(granite::ErrorType::ApiRequest)
                .add_context(error.messages.join(", ")))
        }
        GetResponse::ReqwestError { error, .. } => Err(error.into()),
        GetResponse::SerdeError { error, .. } => Err(error.into()),
    }
    .amend(|e| {
        e.add_context("Failed to get latest report")
            .add_context(format!(
                "api_crs_log_credit_pull_uuid: {api_crs_log_credit_pull_uuid}"
            ))
            .add_context(format!("api_crs_user_uuid: {}", input.api_crs_user_uuid))
            .add_context(format!("url: {endpoint_uri}"))
            .set_external_message("Failed to get latest report.".to_string())
    })
}

enum GetResponse {
    Success {
        status: u16,
        text: String,
    },
    ParsedError {
        status: u16,
        text: String,
        error: super::types::CrsApiError,
    },
    ReqwestError {
        status: u16,
        error: reqwest::Error,
    },
    SerdeError {
        status: u16,
        error: serde_json::Error,
    },
}

impl GetResponse {
    async fn run(use_demo_data: bool, endpoint_uri: &str, server_token: &str) -> GetResponse {
        let client = reqwest::Client::new();

        let response = match client
            .get(endpoint_uri.to_string())
            .header("Authorization", format!("Bearer {server_token}"))
            .header("Content-Type", "application/json")
            .header("Accept", "application/json")
            .timeout(std::time::Duration::from_secs(30))
            .send()
            .await
        {
            Ok(response) => response,
            Err(e) => {
                return Self::ReqwestError {
                    status: 0,
                    error: e,
                };
            }
        };

        let status = response.status();

        if use_demo_data {
            return Self::Success {
                status: 0,
                text: include_str!("direct_efx_latest_report.json").to_string(),
            };
        };

        let text = match response.text().await {
            Ok(text) => text,
            Err(e) => {
                return Self::ReqwestError {
                    status: status.as_u16(),
                    error: e,
                };
            }
        };

        if status.is_success() {
            return Self::Success {
                status: status.as_u16(),
                text,
            };
        }

        // What follows is error handling
        match serde_json::from_str::<super::types::CrsApiError>(&text) {
            Ok(error) => GetResponse::ParsedError {
                status: status.as_u16(),
                text,
                error,
            },
            Err(e) => GetResponse::SerdeError {
                status: status.as_u16(),
                error: e,
            },
        }
    }
}

use granite::ResultExt;

#[derive(serde::Serialize)]
pub struct Input<'a> {
    pub base_url: &'a str,
    pub fname: &'a str,
    pub lname: &'a str,
    pub email: &'a str,
    pub mobile: &'a str,
}

#[derive(serde::Deserialize)]
pub struct ApiOutput {
    #[serde(rename = "userId")]
    pub user_id: String,
    pub token: String,
}

pub enum Output {
    Success {
        user_id: granite::Uuid,
        expire_ts: granite::DateTimeUtc,
        token: String,
    },
    ValidationError {
        message: String,
    },
}

pub async fn call_direct_user_reg(
    crs_mod: &crate::ModuleStruct,
    redis: &mut approck_redis::RedisCX<'_>,
    input: Input<'_>,
) -> granite::Result<Output> {
    let endpoint_uri = format!("{}/direct/user-reg", input.base_url);
    let server_token = crs_mod.get_server_token(redis).await?;

    let client = reqwest::Client::new();
    let response = client
        .post(endpoint_uri.to_string())
        .header("Authorization", format!("Bearer {}", *server_token))
        .header("Content-Type", "application/json")
        .header("Accept", "application/json")
        .json(&input)
        .send()
        .await
        .map_err(|e| super::types::Error::RequestError {
            url: endpoint_uri.to_string(),
            error: e.to_string(),
        })?;

    let status = response.status();

    // Get response text first (consumes response)
    let text = response.text().await.amend(|e| {
        e.add_context("reading response text")
            .add_context(format!("url: {endpoint_uri}"))
            .add_context(format!("status: {status}"))
            .set_external_message("Failed to read response text from API call.".to_string())
    })?;

    // Handle failure responsesd
    if !status.is_success() {
        let error = serde_json::from_str::<super::types::CrsApiError>(&text).amend(|e| {
            e.add_context("decoding response text")
                .add_context(format!("url: {endpoint_uri}"))
                .add_context(format!("status: {status}"))
                .add_context(format!("text: {text}"))
                .set_external_message("Failed to decode response text from API call.".to_string())
        })?;

        // Special handling of validation errors
        if let Some(message) = error.get_validation_error() {
            return Ok(Output::ValidationError { message });
        }

        return Err(granite::Error::new(granite::ErrorType::ApiRequest)
            .add_context(format!("url: {endpoint_uri}"))
            .add_context(format!("status: {status}"))
            .add_context(format!("text: {text}"))
            .set_external_message(error.messages.join(", ")));
    }

    // Success branch - parse the response text
    let output = serde_json::from_str::<ApiOutput>(&text).amend(|e| {
        e.add_context("decoding response text")
            .add_context(format!("url: {endpoint_uri}"))
            .add_context(format!("status: {status}"))
            .add_context(format!("text: {text}"))
            .set_external_message("Failed to decode response text from API call.".to_string())
    })?;

    // parse the uuid
    let user_id = granite::Uuid::parse_str(&output.user_id).amend(|e| {
        e.add_context("parsing user id")
            .add_context(format!("url: {endpoint_uri}"))
            .add_context(format!("status: {status}"))
            .add_context(format!("text: {text}"))
            .add_context(format!("userId: {}", output.user_id))
            .set_external_message("Failed to parse user id from API call.".to_string())
    })?;

    Ok(Output::Success {
        user_id,
        expire_ts: granite::Utc::now() + granite::Duration::seconds(30),
        token: output.token,
    })
}

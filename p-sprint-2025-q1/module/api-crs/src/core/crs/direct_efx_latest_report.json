{"id": "3BR:33d435e67b89--9bba-1c437be1-d0e4308f", "reportType": "US_1B", "generatedDate": "2025-07-28T21:33:40.000+00:00", "providerViews": [{"provider": "EFX", "summary": {"id": "3BR:33d435e67b89--9bba-1c437be1-d0e4308f-EFX", "reportGenerated": 1748739896720, "creditFileSecurityFreezeFlag": false, "reportType": "US_3B", "provider": "EFX", "subject": {"provider": "EFX", "currentName": {"lastName": "BALLESTER", "firstName": "ALAN"}, "currentAddress": {"country": {"code": "USA"}, "line1": "3909 BURKE", "line3": "PASADENA", "line4": "TX", "line5": "77504", "firstReportedDate": *************, "lastReportedDate": *************}, "nationalIdentifier": "xxxxx 0565", "employmentHistory": [{"employerName": "SURPLQD SKBVLFLDQV J", "currentEmployer": false, "ordinal": -1}, {"employerName": "QRUWKZHVW JD PHG", "currentEmployer": false, "ordinal": -1}]}, "creditScore": {"score": 623, "provider": "EFX", "scoreRanges": [], "loanRiskRanges": [], "scoreReasons": [{"code": "00012", "description": "The date that you opened your oldest account is too recent", "creditScoreFactorEffect": "HURTING"}, {"code": "Y", "description": "Number of inquiries adversely affected the score but not significantly", "creditScoreFactorEffect": "HURTING"}]}, "revolvingAccounts": {"balance": {"amount": 16565.0, "currency": "USD"}, "creditLimit": {"amount": 68553.0, "currency": "USD"}, "available": {"amount": 51988.0, "currency": "USD"}, "monthlyPaymentAmount": {"amount": 1110.0, "currency": "USD"}, "debtToCreditRatio": 24.0, "totalAccounts": 16, "totalNegativeAccounts": 1, "totalAccountsWithBalance": 3}, "installmentAccounts": {"balance": {"amount": 181007.0, "currency": "USD"}, "creditLimit": {"amount": 237958.0, "currency": "USD"}, "available": {"amount": 56951.0, "currency": "USD"}, "monthlyPaymentAmount": {"amount": 3665.0, "currency": "USD"}, "debtToCreditRatio": 76.0, "totalAccounts": 4, "totalNegativeAccounts": 6, "totalAccountsWithBalance": 3}, "otherAccounts": {"balance": {"amount": 0.0, "currency": "USD"}, "creditLimit": {"amount": 50.0, "currency": "USD"}, "available": {"amount": 50.0, "currency": "USD"}, "monthlyPaymentAmount": {"amount": 0.0, "currency": "USD"}, "debtToCreditRatio": 0.0, "totalAccounts": 3, "totalNegativeAccounts": 1, "totalAccountsWithBalance": 0}, "totalOpenAccounts": {"balance": {"amount": 197572.0, "currency": "USD"}, "creditLimit": {"amount": 306561.0, "currency": "USD"}, "available": {"amount": 108989.0, "currency": "USD"}, "monthlyPaymentAmount": {"amount": 4775.0, "currency": "USD"}, "debtToCreditRatio": 64.0, "totalAccounts": 23, "totalNegativeAccounts": 0, "totalAccountsWithBalance": 6}, "lengthOfCreditHistoryMonths": 214, "totalNegativeAccounts": 8, "averageAccountAgeMonths": 108, "oldestAccountOpenDate": *************, "oldestAccountName": "CBUSASEARS", "mostRecentAccountOpenDate": *************, "mostRecentAccountName": "VISA/FDSB", "totalConsumerStatements": 0, "mostRecentInquiryDate": *************, "mostRecentInquiryName": "KFD TEST", "totalPersonalInformation": 4, "totalInquires": 4, "totalPublicRecords": 0, "totalCollections": 0, "disputeInformation": {"contactName": "Equifax Information Services, LLC", "address": {"country": {"code": "USA"}, "line1": "P.O. Box 740256", "line3": "Atlanta", "line4": "GA", "line5": "30374"}, "phone": {"countryCode": "1", "areaCode": "866", "exchange": "349", "extension": "5191"}}}, "revolvingAccounts": [{"provider": "EFX", "id": "**********", "accountOpen": false, "accountName": "BK OF AMER", "contactInformation": {"contactName": "BK OF AMER", "address": {"country": {"code": "USA"}, "line1": "DE5 019 03 07 4060 OGLETOWN STANTON RD", "line3": "NEWARK", "line4": "DE", "line5": "19714"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "759", "extension": "6262"}}, "accountNumber": "xxxxxxxxxxxxx 8893", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 3, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "ACCOUNT CLOSED", "description": "ACCOUNT CLOSED"}, {"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": false, "accountName": "BK OF AMER", "contactInformation": {"contactName": "BK OF AMER", "address": {"country": {"code": "USA"}, "line1": "DE5 019 03 07 4060 OGLETOWN STANTON RD", "line3": "NEWARK", "line4": "DE", "line5": "19714"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "759", "extension": "6262"}}, "accountNumber": "xxxxxxxxxxxxx 8893", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 1273.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 33, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "CreditCard", "description": "Unresolved message: 'CreditCard' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CREDIT CARD", "description": "CREDIT CARD"}, {"code": "ACCOUNT CLOSED", "description": "ACCOUNT CLOSED"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": false, "accountName": "CBUSASEARS", "contactInformation": {"contactName": "CBUSASEARS", "address": {"country": {"code": "USA"}, "line1": "8725 W. SAHARA AVE. MC 02 02 03", "line3": "THE LAKES", "line4": "NV", "line5": "89163"}}, "accountNumber": "xxxxxxxxxx 1606", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 26, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "ACCOUNT CLOSED", "description": "ACCOUNT CLOSED"}, {"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": false, "accountName": "DOM BKSHAR", "contactInformation": {"contactName": "DOM BKSHAR", "address": {"country": {"code": "USA"}, "line1": "P O BOX 13287", "line3": "ROANOKE", "line4": "VA", "line5": "24041"}, "phone": {"countryCode": "1", "areaCode": "540", "exchange": "563", "extension": "7900"}}, "accountNumber": "xxxxxxxxxxxxx 4892", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 2166.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 26, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "CreditCard", "description": "Unresolved message: 'CreditCard' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CREDIT CARD", "description": "CREDIT CARD"}, {"code": "ACCOUNT CLOSED", "description": "ACCOUNT CLOSED"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": false, "accountName": "HCS", "contactInformation": {"contactName": "HCS", "address": {"country": {"code": "USA"}, "line1": "1501 RIVERSIDE DRIVE SUITE 117", "line3": "CHATTANOOGA", "line4": "TN", "line5": "37406"}}, "accountNumber": "xxxxxxxxxxxxxx 0344", "accountStatus": "UNAVAILABLE", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 1213.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "monthsReviewed": 7, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "ACCOUNT CLOSED BY CONSUMER", "description": "ACCOUNT CLOSED BY CONSUMER"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": false, "accountName": "WFB/WB", "contactInformation": {"contactName": "WFB/WB", "address": {"country": {"code": "USA"}, "line1": "P.O. BOX 3117 CBDRU", "line3": "WINSTON SALEM", "line4": "NC", "line5": "27102"}}, "accountNumber": "xxxxxxxxxxxxx 9347", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "JOINT_CONTRACTUAL_LIABILITY", "highCreditAmount": {"amount": 2500.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 24, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "CreditCard", "description": "Unresolved message: 'CreditCard' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CREDIT CARD", "description": "CREDIT CARD"}, {"code": "CLOSED OR PAID ACCOUNT/ZERO BALANCE", "description": "CLOSED OR PAID ACCOUNT/ZERO BALANCE"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": true, "accountName": "BK OF AMER", "contactInformation": {"contactName": "BK OF AMER", "address": {"country": {"code": "USA"}, "line1": "4060 OGLETOWN STANTON RD DE5 019 03 07", "line3": "NEWARK", "line4": "DE", "line5": "19713"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "233", "extension": "8181"}}, "accountNumber": "xxxxxxxxx 7123", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 5000.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 35, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "CreditCard", "description": "Unresolved message: 'CreditCard' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CREDIT CARD", "description": "CREDIT CARD"}, {"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": true, "accountName": "BK OF AMER", "contactInformation": {"contactName": "BK OF AMER", "address": {"country": {"code": "USA"}, "line1": "DE5 019 03 07 4060 OGLETOWN STANTON RD", "line3": "NEWARK", "line4": "DE", "line5": "19714"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "759", "extension": "6262"}}, "accountNumber": "xxxxxxxxxxxxx 8787", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 5285.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 264.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 30, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "CreditCard", "description": "Unresolved message: 'CreditCard' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CREDIT CARD", "description": "CREDIT CARD"}, {"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": true, "accountName": "BK OF AMER", "contactInformation": {"contactName": "BK OF AMER", "address": {"country": {"code": "USA"}, "line1": "4060 OGLETOWN STANTON RD DE5 019 03 07", "line3": "NEWARK", "line4": "DE", "line5": "19713"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "421", "extension": "2110"}}, "accountNumber": "xxxxxxxxxxxxx 1890", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 29700.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 13097.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 654.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 93, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "CreditCard", "description": "Unresolved message: 'CreditCard' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "LAST REPORTED DELINQUENCIES: 01/2022=R2,12/2021=R2,09/2021=R3", "description": "LAST REPORTED DELINQUENCIES: 01/2022=R2,12/2021=R2,09/2021=R3"}, {"code": "CREDIT CARD", "description": "CREDIT CARD"}, {"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": true, "accountName": "BK OF AMER", "contactInformation": {"contactName": "BK OF AMER", "address": {"country": {"code": "USA"}, "line1": "4060 OGLETOWN STANTON RD DE5 019 03 07", "line3": "NEWARK", "line4": "DE", "line5": "19713"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "421", "extension": "2110"}}, "accountNumber": "xxxxxxxxxxxxx 2013", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 21, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "CreditCard", "description": "Unresolved message: 'CreditCard' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CREDIT CARD", "description": "CREDIT CARD"}, {"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": true, "accountName": "CAP ONE", "contactInformation": {"contactName": "CAP ONE", "address": {"country": {"code": "USA"}, "line1": "PO BOX 85015", "line3": "RICHMOND", "line4": "VA", "line5": "23285"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "955", "extension": "7070"}}, "accountNumber": "xxxxxxxxx 8122", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 7603.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 99, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "CreditCard", "description": "Unresolved message: 'CreditCard' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CREDIT CARD", "description": "CREDIT CARD"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": true, "accountName": "CBNA", "contactInformation": {"contactName": "CBNA", "address": {"country": {"code": "USA"}, "line1": "PO BOX 6497", "line3": "SIOUX FALLS", "line4": "SD", "line5": "57117"}}, "accountNumber": "xxxxxxxxx 2450", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 4900.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 99, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "LAST REPORTED DELINQUENCIES: 08/2021=R2", "description": "LAST REPORTED DELINQUENCIES: 08/2021=R2"}, {"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": true, "accountName": "CBUSASEARS", "contactInformation": {"contactName": "CBUSASEARS", "address": {"country": {"code": "USA"}, "line1": "8725 W. SAHARA AVE. MC 02 02 03", "line3": "THE LAKES", "line4": "NV", "line5": "89163"}}, "accountNumber": "xxxxxxxxxx 1600", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 1368.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 51, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "ChargeAccount", "description": "Unresolved message: 'ChargeAccount' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}, {"code": "CHARGE", "description": "CHARGE"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": true, "accountName": "CBUSASEARS", "contactInformation": {"contactName": "CBUSASEARS", "address": {"country": {"code": "USA"}, "line1": "8725 W. SAHARA AVE. MC 02 02 03", "line3": "THE LAKES", "line4": "NV", "line5": "89163"}}, "accountNumber": "xxxxxxxxxx 6473", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 1632.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 34, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": true, "accountName": "CHASE NA", "contactInformation": {"contactName": "CHASE NA", "address": {"country": {"code": "USA"}, "line1": "4915 INDEPENDENCE PARKWAY", "line3": "TAMPA", "line4": "FL", "line5": "33634"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "356", "extension": "5555"}}, "accountNumber": "xxxxxxx 2010", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 2300.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 20, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "CreditCard", "description": "Unresolved message: 'CreditCard' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CREDIT CARD", "description": "CREDIT CARD"}, {"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": true, "accountName": "CHASE NA", "contactInformation": {"contactName": "CHASE NA", "address": {"country": {"code": "USA"}, "line1": "4915 INDEPENDENCE PARKWAY", "line3": "TAMPA", "line4": "FL", "line5": "33634"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "356", "extension": "5555"}}, "accountNumber": "xxxxxxx 1010", "accountStatus": "UNAVAILABLE", "paymentResponsibility": "UNDESIGNATED", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 12, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "CreditCard", "description": "Unresolved message: 'CreditCard' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "LOST OR STOLEN CARD", "description": "LOST OR STOLEN CARD"}, {"code": "CREDIT CARD", "description": "CREDIT CARD"}, {"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": true, "accountName": "CHASE NA", "contactInformation": {"contactName": "CHASE NA", "address": {"country": {"code": "USA"}, "line1": "4915 INDEPENDENCE PARKWAY", "line3": "TAMPA", "line4": "FL", "line5": "33634"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "356", "extension": "5555"}}, "accountNumber": "xxxxxxx 2016", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 3700.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 36, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "CreditCard", "description": "Unresolved message: 'CreditCard' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CREDIT CARD", "description": "CREDIT CARD"}, {"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": true, "accountName": "CITI", "contactInformation": {"contactName": "CITI", "address": {"country": {"code": "USA"}, "line1": "701 E 60TH ST N IBS CDV DISPUTES", "line3": "SIOUX FALLS", "line4": "SD", "line5": "57104"}}, "accountNumber": "xxxxxxxxx 9344", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 6200.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 99, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": true, "accountName": "MACYS/GEMB", "contactInformation": {"contactName": "MACYS/GEMB", "address": {"country": {"code": "USA"}, "line1": "PO BOX 8122", "line3": "MASON", "line4": "OH", "line5": "45040"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "243", "extension": "6552"}}, "accountNumber": "xxxxxxxxx 6341", "accountStatus": "LATE_60_DAYS", "paymentResponsibility": "JOINT_CONTRACTUAL_LIABILITY", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 1500.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 1468.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 205.0, "currency": "USD"}, "monthlyPayment": {"amount": 73.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 84, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "ChargeAccount", "description": "Unresolved message: 'ChargeAccount' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "LAST REPORTED DELINQUENCIES: 11/2024=R2", "description": "LAST REPORTED DELINQUENCIES: 11/2024=R2"}, {"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}, {"code": "CHARGE", "description": "CHARGE"}], "paymentHistory": [{"year": 2025, "january": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "february": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "march": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "april": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "may": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "june": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "july": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "august": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "september": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "october": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "november": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "december": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}}, {"year": 2024, "january": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "february": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "march": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "june": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "july": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "august": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "september": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "october": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "november": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "december": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}}, {"year": 2023, "january": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "february": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "march": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "june": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "july": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "august": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "september": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "october": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "november": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "december": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": true, "accountName": "MACYS/GEMB", "contactInformation": {"contactName": "MACYS/GEMB", "address": {"country": {"code": "USA"}, "line1": "PO BOX 8122", "line3": "MASON", "line4": "OH", "line5": "45040"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "243", "extension": "6552"}}, "accountNumber": "xxxxxxxxx 6451", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "JOINT_CONTRACTUAL_LIABILITY", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 22, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "ChargeAccount", "description": "Unresolved message: 'ChargeAccount' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}, {"code": "CHARGE", "description": "CHARGE"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": true, "accountName": "STBC CLASS", "contactInformation": {"contactName": "STBC CLASS", "address": {"country": {"code": "USA"}, "line1": "6101 CHANCELLOR DR", "line3": "ORLANDO", "line4": "FL", "line5": "32802"}}, "accountNumber": "xxxxxxxxx 9290", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 2650.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 45, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "CreditCard", "description": "Unresolved message: 'CreditCard' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CREDIT CARD", "description": "CREDIT CARD"}, {"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": true, "accountName": "VISA/FDSB", "contactInformation": {"contactName": "VISA/FDSB", "address": {"country": {"code": "USA"}}}, "accountNumber": "xxxxxxxxxxxxx 5564", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "JOINT_CONTRACTUAL_LIABILITY", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "REVOLVING", "creditLimitAmount": {"amount": 2000.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 2000.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 119.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 5, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "CreditCard", "description": "Unresolved message: 'CreditCard' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CREDIT CARD", "description": "CREDIT CARD"}, {"code": "AMT IN HIGH CREDIT IS CREDIT LIMIT", "description": "AMT IN HIGH CREDIT IS CREDIT LIMIT"}], "trendedDataHistory": {}, "isNegative": false}], "installmentAccounts": [{"provider": "EFX", "id": "**********", "accountOpen": false, "accountName": "COMMUNITY", "contactInformation": {"contactName": "COMMUNITY", "address": {"country": {"code": "USA"}}}, "accountNumber": "xxxxxxxxxxxxx 3346", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 16694.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 33, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": false, "accountName": "COMMUNITY", "contactInformation": {"contactName": "COMMUNITY", "address": {"country": {"code": "USA"}}}, "accountNumber": "xxxxxxxxxxxxx 1013", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 24405.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 37, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "LAST REPORTED DELINQUENCIES: 11/2020=I4,10/2020=I4,09/2020=I3", "description": "LAST REPORTED DELINQUENCIES: 11/2020=I4,10/2020=I4,09/2020=I3"}], "paymentHistory": [{"year": 2020, "january": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "february": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "march": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "april": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "may": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "june": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "july": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "august": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "september": {"monthType": "NEGATIVE", "value": "DAYSLATE_60"}, "october": {"monthType": "NEGATIVE", "value": "DAYSLATE_90"}, "november": {"monthType": "NEGATIVE", "value": "DAYSLATE_90"}, "december": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}}, {"year": 2019, "january": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "february": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "march": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "june": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "july": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "august": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "september": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "october": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "november": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "december": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}}, {"year": 2018, "january": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "february": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "march": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "april": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "may": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "june": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "july": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "august": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "september": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "october": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "november": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "december": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": false, "accountName": "COMMUNITY", "contactInformation": {"contactName": "COMMUNITY", "address": {"country": {"code": "USA"}}}, "accountNumber": "xxxxxxxxxxxxx 2343", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 15060.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 25, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "LAST REPORTED DELINQUENCIES: 11/2018=I2", "description": "LAST REPORTED DELINQUENCIES: 11/2018=I2"}], "paymentHistory": [{"year": 2020, "january": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "february": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "march": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "june": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "july": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "august": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "september": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "october": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "november": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "december": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}}, {"year": 2019, "january": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "february": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "march": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "june": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "july": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "august": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "september": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "october": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "november": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "december": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}}, {"year": 2018, "january": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "february": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "march": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "april": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "may": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "june": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "july": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "august": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "september": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "october": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "november": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "december": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": false, "accountName": "COMMUNITY", "contactInformation": {"contactName": "COMMUNITY", "address": {"country": {"code": "USA"}}}, "accountNumber": "xxxxx 1345", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 23555.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 17, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "LAST REPORTED DELINQUENCIES: 01/2021=I2,12/2020=I2,11/2020=I2", "description": "LAST REPORTED DELINQUENCIES: 01/2021=I2,12/2020=I2,11/2020=I2"}], "paymentHistory": [{"year": 2021, "january": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "february": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "march": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "june": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "july": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "august": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "september": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "october": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "november": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "december": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}}, {"year": 2020, "january": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "february": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "march": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "june": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "july": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "august": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "september": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "october": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "november": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "december": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": false, "accountName": "COMMUNITY", "contactInformation": {"contactName": "COMMUNITY", "address": {"country": {"code": "USA"}}}, "accountNumber": "xxxxx 8895", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 13115.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 7, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CLOSED OR PAID ACCOUNT/ZERO BALANCE", "description": "CLOSED OR PAID ACCOUNT/ZERO BALANCE"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": false, "accountName": "COMMUNITY", "contactInformation": {"contactName": "COMMUNITY", "address": {"country": {"code": "USA"}}}, "accountNumber": "xxxxx 7780", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 7685.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 23, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": false, "accountName": "COMMUNITY", "contactInformation": {"contactName": "COMMUNITY", "address": {"country": {"code": "USA"}}}, "accountNumber": "xxxxx 9407", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 12120.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 28, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "LAST REPORTED DELINQUENCIES: 01/2021=I2,12/2020=I2,11/2020=I2", "description": "LAST REPORTED DELINQUENCIES: 01/2021=I2,12/2020=I2,11/2020=I2"}, {"code": "CLOSED OR PAID ACCOUNT/ZERO BALANCE", "description": "CLOSED OR PAID ACCOUNT/ZERO BALANCE"}], "paymentHistory": [{"year": 2022, "january": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "february": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "march": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "june": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "july": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "august": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "september": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "october": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "november": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "december": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}}, {"year": 2021, "january": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "february": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "march": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "june": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "july": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "august": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "september": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "october": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "november": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "december": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}}, {"year": 2020, "january": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "february": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "march": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "april": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "may": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "june": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "july": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "august": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "september": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "october": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "november": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "december": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": false, "accountName": "COMMUNITY", "contactInformation": {"contactName": "COMMUNITY", "address": {"country": {"code": "USA"}}}, "accountNumber": "xxxxx 7601", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 18090.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 15, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "LAST REPORTED DELINQUENCIES: 10/2021=I2,09/2021=I2,08/2021=I2", "description": "LAST REPORTED DELINQUENCIES: 10/2021=I2,09/2021=I2,08/2021=I2"}, {"code": "CLOSED OR PAID ACCOUNT/ZERO BALANCE", "description": "CLOSED OR PAID ACCOUNT/ZERO BALANCE"}], "paymentHistory": [{"year": 2022, "january": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "february": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "march": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "june": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "july": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "august": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "september": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "october": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "november": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "december": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}}, {"year": 2021, "january": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "february": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "march": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "june": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "july": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "august": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "september": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "october": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "november": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "december": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}}, {"year": 2020, "january": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "february": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "march": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "april": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "may": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "june": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "july": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "august": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "september": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "october": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "november": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "december": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": false, "accountName": "FORD MO CR", "contactInformation": {"contactName": "FORD MO CR", "address": {"country": {"code": "USA"}, "line1": "1000 ABERNATHY RD NE          BLDG 400 SUITE 180", "line3": "ATLANTA", "line4": "GA", "line5": "30328"}}, "accountNumber": "xxxxxx TA5I", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 18556.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 22, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "Automobile", "description": "Unresolved message: 'Automobile' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CLOSED OR PAID ACCOUNT/ZERO BALANCE", "description": "CLOSED OR PAID ACCOUNT/ZERO BALANCE"}, {"code": "AUTO", "description": "AUTO"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": false, "accountName": "GMAC", "contactInformation": {"contactName": "GMAC", "address": {"country": {"code": "USA"}, "line1": "P O BOX 380901", "line3": "BLOOMINGTON", "line4": "MN", "line5": "55438"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "200", "extension": "4622"}}, "accountNumber": "xxxxxxxxxx 3143", "accountStatus": "LATE_30_DAYS", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 30018.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 61, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "Automobile", "description": "Unresolved message: 'Automobile' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "LAST REPORTED DELINQUENCIES: 09/2020=I2", "description": "LAST REPORTED DELINQUENCIES: 09/2020=I2"}, {"code": "AUTO", "description": "AUTO"}], "paymentHistory": [{"year": 2020, "january": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "february": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "march": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "june": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "july": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "august": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "september": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "october": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "november": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "december": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}}, {"year": 2019, "january": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "february": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "march": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "june": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "july": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "august": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "september": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "october": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "november": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "december": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}}, {"year": 2018, "january": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "february": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "march": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "april": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "may": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "june": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "july": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "august": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "september": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "october": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "november": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "december": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": false, "accountName": "REGIONS", "contactInformation": {"contactName": "REGIONS", "address": {"country": {"code": "USA"}}}, "accountNumber": "xxxxxxx 6129", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 58921.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 56, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "ACCOUNT CLOSED", "description": "ACCOUNT CLOSED"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": false, "accountName": "REGIONS", "contactInformation": {"contactName": "REGIONS", "address": {"country": {"code": "USA"}}}, "accountNumber": "xxxxxxxxx 1233", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 27667.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 88, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "Secured", "description": "Unresolved message: 'Secured' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CLOSED OR PAID ACCOUNT/ZERO BALANCE", "description": "CLOSED OR PAID ACCOUNT/ZERO BALANCE"}, {"code": "SECURED", "description": "SECURED"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": false, "accountName": "REGIONS", "contactInformation": {"contactName": "REGIONS", "address": {"country": {"code": "USA"}}}, "accountNumber": "xxxxxxxxx 1234", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 49148.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 43, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "Secured", "description": "Unresolved message: 'Secured' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CLOSED OR PAID ACCOUNT/ZERO BALANCE", "description": "CLOSED OR PAID ACCOUNT/ZERO BALANCE"}, {"code": "SECURED", "description": "SECURED"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": false, "accountName": "REGIONS", "contactInformation": {"contactName": "REGIONS", "address": {"country": {"code": "USA"}}}, "accountNumber": "xxxxxxxxx 1235", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 250000.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 37, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "Secured", "description": "Unresolved message: 'Secured' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CLOSED OR PAID ACCOUNT/ZERO BALANCE", "description": "CLOSED OR PAID ACCOUNT/ZERO BALANCE"}, {"code": "SECURED", "description": "SECURED"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": false, "accountName": "WFDS", "contactInformation": {"contactName": "WFDS", "address": {"country": {"code": "USA"}, "line1": "P.O. BOX 3117 SALES FINANCE FAX 336 747 8325", "line3": "WINSTON SALEM", "line4": "NC", "line5": "27102"}}, "accountNumber": "xxxxxxxxxxx 3674", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 23592.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 4, "creditorClassification": "UNKNOWN", "activityDesignator": "CLOSED", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "CLOSED OR PAID ACCOUNT/ZERO BALANCE", "description": "CLOSED OR PAID ACCOUNT/ZERO BALANCE"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": true, "accountName": "FIDLTY IL", "contactInformation": {"contactName": "FIDLTY IL", "address": {"country": {"code": "USA"}, "line1": "160 CLAIRMONT RD", "line3": "DCTR", "line4": "GA", "line5": "30030"}}, "accountNumber": "xxxxxxx 0677", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 24542.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 20762.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 619.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 9, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "Automobile", "description": "Unresolved message: 'Automobile' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "AUTO", "description": "AUTO"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": true, "accountName": "GMAC", "contactInformation": {"contactName": "GMAC", "address": {"country": {"code": "USA"}, "line1": "P O BOX 380901", "line3": "BLOOMINGTON", "line4": "MN", "line5": "55438"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "200", "extension": "4622"}}, "accountNumber": "xxxxxxxxxx 1933", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 28428.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 573.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 21, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "Automobile", "description": "Unresolved message: 'Automobile' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "AUTO", "description": "AUTO"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": true, "accountName": "GMAC", "contactInformation": {"contactName": "GMAC", "address": {"country": {"code": "USA"}, "line1": "P O BOX 380901", "line3": "BLOOMINGTON", "line4": "MN", "line5": "55438"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "200", "extension": "4622"}}, "accountNumber": "xxxxxxxxxx 6623", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 14988.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 6245.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 624.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 13, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "Lease", "description": "Unresolved message: 'Lease' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "LEASE", "description": "LEASE"}], "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "*********", "accountOpen": true, "accountName": "UP/REGIONS", "contactInformation": {"contactName": "UP/REGIONS", "address": {"country": {"code": "USA"}, "line1": "215 FORREST STREET CORPORATE SQUARE", "line3": "HATTIESBURG", "line4": "MS", "line5": "39402"}, "phone": {"countryCode": "1", "areaCode": "334", "exchange": "223", "extension": "3844"}}, "accountNumber": "xxxx 06", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "JOINT_CONTRACTUAL_LIABILITY", "highCreditAmount": {"amount": 170000.0, "currency": "USD"}, "accountType": "INSTALLMENT", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 154000.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 1849.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 28, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "trendedDataHistory": {}, "isNegative": false}], "otherAccounts": [{"provider": "EFX", "id": "**********", "accountOpen": true, "accountName": "AMEX", "contactInformation": {"contactName": "AMEX", "address": {"country": {"code": "USA"}, "line1": "PO BOX 981537", "line3": "EL PASO", "line4": "TX", "line5": "79998"}, "phone": {"countryCode": "1", "areaCode": "954", "exchange": "503", "extension": "3787"}}, "accountNumber": "xxxxx 11", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "OTHER", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 1, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": true, "accountName": "BP OIL", "contactInformation": {"contactName": "BP OIL", "address": {"country": {"code": "USA"}, "line1": "550 MIDLAND BLDG PO BOX 6718", "line3": "CLEVELAND", "line4": "OH", "line5": "44115"}}, "accountNumber": "xxxxx 3901", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 50.0, "currency": "USD"}, "accountType": "OTHER", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 30, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "trendedDataHistory": {}, "isNegative": false}, {"provider": "EFX", "id": "**********", "accountOpen": true, "accountName": "CINGWIR-GA", "contactInformation": {"contactName": "CINGWIR-GA", "address": {"country": {"code": "USA"}, "line1": "2612 NORTH ROAN ST. ATTN   CREDIT BUREAU DISPUTES", "line3": "JOHNSON CITY", "line4": "TN", "line5": "37601"}, "phone": {"countryCode": "1", "areaCode": "800", "exchange": "947", "extension": "5096"}}, "accountNumber": "xxxxxxxxxxx 3333", "accountStatus": "PAYS_AS_AGREED", "paymentResponsibility": "INDIVIDUAL", "highCreditAmount": {"amount": 0.0, "currency": "USD"}, "accountType": "OTHER", "creditLimitAmount": {"amount": 0.0, "currency": "USD"}, "termDurationMonths": 0, "dateOpened": *************, "balanceAmount": {"amount": 0.0, "currency": "USD"}, "reportedDate": *************, "pastDueAmount": {"amount": 0.0, "currency": "USD"}, "monthlyPayment": {"amount": 0.0, "currency": "USD"}, "lastActivityDate": *************, "monthsReviewed": 22, "creditorClassification": "UNKNOWN", "activityDesignator": "OPEN", "loanType": {"code": "UnknownLoanType", "description": "Unresolved message: 'UnknownLoanType' in resouce bundle resolver: locale/creditReportAccountLoanTypeDescription"}, "comments": [{"code": "LAST REPORTED DELINQUENCIES: 02/2022=O2,12/2021=O2", "description": "LAST REPORTED DELINQUENCIES: 02/2022=O2,12/2021=O2"}], "paymentHistory": [{"year": 2023, "january": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "february": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "march": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "june": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "july": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "august": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "september": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "october": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "november": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "december": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}}, {"year": 2022, "january": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "february": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}, "march": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "april": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "may": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "june": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "july": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "august": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "september": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "october": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "november": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "december": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}}, {"year": 2021, "january": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "february": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "march": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "april": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "may": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "june": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "july": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "august": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "september": {"monthType": "NO_DATA", "value": "UNAVAILABLE"}, "october": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "november": {"monthType": "NO_DATA", "value": "NOT_REPORTED"}, "december": {"monthType": "NEGATIVE", "value": "DAYSLATE_30"}}], "trendedDataHistory": {}, "isNegative": false}], "inquiries": [{"provider": "EFX", "type": "HARD", "reportedDate": *************, "contactInformation": {"contactName": "KFD TEST"}}, {"provider": "EFX", "type": "HARD", "reportedDate": 1743724800000, "contactInformation": {"contactName": "KFD TEST"}}, {"provider": "EFX", "type": "HARD", "reportedDate": 1743638400000, "contactInformation": {"contactName": "KROLL FD"}}, {"provider": "EFX", "type": "HARD", "reportedDate": 1743552000000, "contactInformation": {"contactName": "FIA CSNA"}}]}]}
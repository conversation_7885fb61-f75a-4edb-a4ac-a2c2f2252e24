{"id": "string", "reportType": "string", "generatedDate": "string", "providerViews": [{"provider": "string", "summary": {"id": "string", "reportGenerated": "string", "creditFileSecurityFreezeFlag": "boolean", "reportType": "string", "provider": "string", "subject": {"provider": "string", "currentName": {"lastName": "string", "firstName": "string", "middleName": "string", "suffix": "string"}, "aliases": [{"lastName": "string", "firstName": "string", "middleName": "string", "suffix": "string"}], "currentAddress": {"country": {"code": "string", "name": "string"}, "line1": "string", "line2": "string", "line3": "string", "line4": "string", "line5": "string", "firstReportedDate": "string", "lastReportedDate": "string", "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}, "previousAddresses": [{"country": {"code": "string", "name": "string"}, "line1": "string", "line2": "string", "line3": "string", "line4": "string", "line5": "string", "firstReportedDate": "string", "lastReportedDate": "string", "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}], "homePhone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}, "mobilePhone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}, "nationalIdentifier": "string", "dateOfBirth": "string", "dateOfDeath": "string", "employmentHistory": [{"employerName": "string", "employeeTitle": "string", "currentEmployer": "boolean", "ordinal": "integer", "dateOfEmployment": "string", "address": {"country": {"code": "string", "name": "string"}, "line1": "string", "line2": "string", "line3": "string", "line4": "string", "line5": "string", "firstReportedDate": "string", "lastReportedDate": "string", "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}}]}, "creditScore": {"score": "integer", "provider": "string", "scoreRanges": [{"low": "integer", "high": "integer", "name": "string"}], "loanRiskRanges": [{"low": "integer", "high": "integer", "name": "string"}], "scoreReasons": [{"code": "string", "description": "string", "creditScoreFactorEffect": "string"}]}, "revolvingAccounts": {"balance": {"amount": "number", "currency": "string"}, "creditLimit": {"amount": "number", "currency": "string"}, "available": {"amount": "number", "currency": "string"}, "monthlyPaymentAmount": {"amount": "number", "currency": "string"}, "debtToCreditRatio": "number", "totalAccounts": "integer", "totalNegativeAccounts": "integer", "totalAccountsWithBalance": "integer"}, "mortgageAccounts": {"balance": {"amount": "number", "currency": "string"}, "creditLimit": {"amount": "number", "currency": "string"}, "available": {"amount": "number", "currency": "string"}, "monthlyPaymentAmount": {"amount": "number", "currency": "string"}, "debtToCreditRatio": "number", "totalAccounts": "integer", "totalNegativeAccounts": "integer", "totalAccountsWithBalance": "integer"}, "installmentAccounts": {"balance": {"amount": "number", "currency": "string"}, "creditLimit": {"amount": "number", "currency": "string"}, "available": {"amount": "number", "currency": "string"}, "monthlyPaymentAmount": {"amount": "number", "currency": "string"}, "debtToCreditRatio": "number", "totalAccounts": "integer", "totalNegativeAccounts": "integer", "totalAccountsWithBalance": "integer"}, "otherAccounts": {"balance": {"amount": "number", "currency": "string"}, "creditLimit": {"amount": "number", "currency": "string"}, "available": {"amount": "number", "currency": "string"}, "monthlyPaymentAmount": {"amount": "number", "currency": "string"}, "debtToCreditRatio": "number", "totalAccounts": "integer", "totalNegativeAccounts": "integer", "totalAccountsWithBalance": "integer"}, "totalOpenAccounts": {"balance": {"amount": "number", "currency": "string"}, "creditLimit": {"amount": "number", "currency": "string"}, "available": {"amount": "number", "currency": "string"}, "monthlyPaymentAmount": {"amount": "number", "currency": "string"}, "debtToCreditRatio": "number", "totalAccounts": "integer", "totalNegativeAccounts": "integer", "totalAccountsWithBalance": "integer"}, "lengthOfCreditHistoryMonths": "integer", "totalNegativeAccounts": "integer", "averageAccountAgeMonths": "integer", "oldestAccountOpenDate": "string", "oldestAccountName": "string", "mostRecentAccountOpenDate": "string", "mostRecentAccountName": "string", "totalConsumerStatements": "integer", "mostRecentInquiryDate": "string", "mostRecentInquiryName": "string", "totalPersonalInformation": "integer", "totalInquires": "integer", "totalPublicRecords": "integer", "totalCollections": "integer", "disputeInformation": {"contactName": "string", "address": {"country": {"code": "string", "name": "string"}, "line1": "string", "line2": "string", "line3": "string", "line4": "string", "line5": "string", "firstReportedDate": "string", "lastReportedDate": "string", "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}, "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}}, "revolvingAccounts": [{"provider": "string", "id": "string", "accountOpen": "boolean", "accountName": "string", "contactInformation": {"contactName": "string", "address": {"country": {"code": "string", "name": "string"}, "line1": "string", "line2": "string", "line3": "string", "line4": "string", "line5": "string", "firstReportedDate": "string", "lastReportedDate": "string", "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}, "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}, "accountNumber": "string", "accountStatus": "string", "paymentResponsibility": "string", "highCreditAmount": {"amount": "number", "currency": "string"}, "accountType": "string", "creditLimitAmount": {"amount": "number", "currency": "string"}, "termDurationMonths": "integer", "termFrequency": "string", "dateOpened": "string", "balanceAmount": {"amount": "number", "currency": "string"}, "reportedDate": "string", "pastDueAmount": {"amount": "number", "currency": "string"}, "lastPaymentDate": "string", "actualPayment": {"amount": "number", "currency": "string"}, "monthlyPayment": {"amount": "number", "currency": "string"}, "lastActivityDate": "string", "majorDelinquencyFirstReportedDate": "string", "monthsReviewed": "integer", "creditorClassification": "string", "activityDesignator": "string", "chargeOffAmount": {"amount": "number", "currency": "string"}, "deferredPaymentStartDate": "string", "balloonPaymentAmount": {"amount": "number", "currency": "string"}, "balloonPaymentDate": "string", "dateClosed": "string", "loanType": {"code": "string", "description": "string"}, "firstDelinquencyDate": "string", "comments": [{"code": "string", "description": "string"}], "paymentHistory": [{"year": "integer", "january": {"monthType": "string", "value": "string"}, "february": {"monthType": "string", "value": "string"}, "march": {"monthType": "string", "value": "string"}, "april": {"monthType": "string", "value": "string"}, "may": {"monthType": "string", "value": "string"}, "june": {"monthType": "string", "value": "string"}, "july": {"monthType": "string", "value": "string"}, "august": {"monthType": "string", "value": "string"}, "september": {"monthType": "string", "value": "string"}, "october": {"monthType": "string", "value": "string"}, "november": {"monthType": "string", "value": "string"}, "december": {"monthType": "string", "value": "string"}}], "trendedDataHistory": {"name": "string", "trendedDataList": [{"name": "string", "years": [{"year": "string", "monthData": ["string"]}], "labels": ["string"]}]}, "isNegative": "boolean"}], "mortgageAccounts": [{"provider": "string", "id": "string", "accountOpen": "boolean", "accountName": "string", "contactInformation": {"contactName": "string", "address": {"country": {"code": "string", "name": "string"}, "line1": "string", "line2": "string", "line3": "string", "line4": "string", "line5": "string", "firstReportedDate": "string", "lastReportedDate": "string", "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}, "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}, "accountNumber": "string", "accountStatus": "string", "paymentResponsibility": "string", "highCreditAmount": {"amount": "number", "currency": "string"}, "accountType": "string", "creditLimitAmount": {"amount": "number", "currency": "string"}, "termDurationMonths": "integer", "termFrequency": "string", "dateOpened": "string", "balanceAmount": {"amount": "number", "currency": "string"}, "reportedDate": "string", "pastDueAmount": {"amount": "number", "currency": "string"}, "lastPaymentDate": "string", "actualPayment": {"amount": "number", "currency": "string"}, "monthlyPayment": {"amount": "number", "currency": "string"}, "lastActivityDate": "string", "majorDelinquencyFirstReportedDate": "string", "monthsReviewed": "integer", "creditorClassification": "string", "activityDesignator": "string", "chargeOffAmount": {"amount": "number", "currency": "string"}, "deferredPaymentStartDate": "string", "balloonPaymentAmount": {"amount": "number", "currency": "string"}, "balloonPaymentDate": "string", "dateClosed": "string", "loanType": {"code": "string", "description": "string"}, "firstDelinquencyDate": "string", "comments": [{"code": "string", "description": "string"}], "paymentHistory": [{"year": "integer", "january": {"monthType": "string", "value": "string"}, "february": {"monthType": "string", "value": "string"}, "march": {"monthType": "string", "value": "string"}, "april": {"monthType": "string", "value": "string"}, "may": "string", "june": {"monthType": "string", "value": "string"}, "july": {"monthType": "string", "value": "string"}, "august": {"monthType": "string", "value": "string"}, "september": {"monthType": "string", "value": "string"}, "october": {"monthType": "string", "value": "string"}, "november": {"monthType": "string", "value": "string"}, "december": {"monthType": "string", "value": "string"}}], "trendedDataHistory": {"name": "string", "trendedDataList": [{"name": "string", "years": [{"year": "string", "monthData": ["string"]}], "labels": ["string"]}]}, "isNegative": "boolean"}], "installmentAccounts": [{"provider": "string", "id": "string", "accountOpen": "boolean", "accountName": "string", "contactInformation": {"contactName": "string", "address": {"country": {"code": "string", "name": "string"}, "line1": "string", "line2": "string", "line3": "string", "line4": "string", "line5": "string", "firstReportedDate": "string", "lastReportedDate": "string", "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}, "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}, "accountNumber": "string", "accountStatus": "string", "paymentResponsibility": "string", "highCreditAmount": {"amount": "number", "currency": "string"}, "accountType": "string", "creditLimitAmount": {"amount": "number", "currency": "string"}, "termDurationMonths": "integer", "termFrequency": "string", "dateOpened": "string", "balanceAmount": {"amount": "number", "currency": "string"}, "reportedDate": "string", "pastDueAmount": {"amount": "number", "currency": "string"}, "lastPaymentDate": "string", "actualPayment": {"amount": "number", "currency": "string"}, "monthlyPayment": {"amount": "number", "currency": "string"}, "lastActivityDate": "string", "majorDelinquencyFirstReportedDate": "string", "monthsReviewed": "integer", "creditorClassification": "string", "activityDesignator": "string", "chargeOffAmount": {"amount": "number", "currency": "string"}, "deferredPaymentStartDate": "string", "balloonPaymentAmount": {"amount": "number", "currency": "string"}, "balloonPaymentDate": "string", "dateClosed": "string", "loanType": {"code": "string", "description": "string"}, "firstDelinquencyDate": "string", "comments": [{"code": "string", "description": "string"}], "paymentHistory": [{"year": "integer", "january": {"monthType": "string", "value": "string"}, "february": {"monthType": "string", "value": "string"}, "march": {"monthType": "string", "value": "string"}, "april": {"monthType": "string", "value": "string"}, "may": {"monthType": "string", "value": "string"}, "june": {"monthType": "string", "value": "string"}, "july": {"monthType": "string", "value": "string"}, "august": {"monthType": "string", "value": "string"}, "september": {"monthType": "string", "value": "string"}, "october": {"monthType": "string", "value": "string"}, "november": {"monthType": "string", "value": "string"}, "december": {"monthType": "string", "value": "string"}}], "trendedDataHistory": {"name": "string", "trendedDataList": [{"name": "string", "years": [{"year": "string", "monthData": ["string"]}], "labels": ["string"]}]}, "isNegative": "boolean"}], "otherAccounts": [{"provider": "string", "id": "string", "accountOpen": "boolean", "accountName": "string", "contactInformation": {"contactName": "string", "address": {"country": {"code": "string", "name": "string"}, "line1": "string", "line2": "string", "line3": "string", "line4": "string", "line5": "string", "firstReportedDate": "string", "lastReportedDate": "string", "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}, "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}, "accountNumber": "string", "accountStatus": "string", "paymentResponsibility": "string", "highCreditAmount": {"amount": "number", "currency": "string"}, "accountType": "string", "creditLimitAmount": {"amount": "number", "currency": "string"}, "termDurationMonths": "integer", "termFrequency": "string", "dateOpened": "string", "balanceAmount": {"amount": "number", "currency": "string"}, "reportedDate": "string", "pastDueAmount": {"amount": "number", "currency": "string"}, "lastPaymentDate": "string", "actualPayment": {"amount": "number", "currency": "string"}, "monthlyPayment": {"amount": "number", "currency": "string"}, "lastActivityDate": "string", "majorDelinquencyFirstReportedDate": "string", "monthsReviewed": "integer", "creditorClassification": "string", "activityDesignator": "string", "chargeOffAmount": {"amount": "number", "currency": "string"}, "deferredPaymentStartDate": "string", "balloonPaymentAmount": {"amount": "number", "currency": "string"}, "balloonPaymentDate": "string", "dateClosed": "string", "loanType": {"code": "string", "description": "string"}, "firstDelinquencyDate": "string", "comments": [{"code": "string", "description": "string"}], "paymentHistory": [{"year": "integer", "january": {"monthType": "string", "value": "string"}, "february": {"monthType": "string", "value": "string"}, "march": {"monthType": "string", "value": "string"}, "april": {"monthType": "string", "value": "string"}, "may": {"monthType": "string", "value": "string"}, "june": {"monthType": "string", "value": "string"}, "july": {"monthType": "string", "value": "string"}, "august": {"monthType": "string", "value": "string"}, "september": {"monthType": "string", "value": "string"}, "october": {"monthType": "string", "value": "string"}, "november": {"monthType": "string", "value": "string"}, "december": {"monthType": "string", "value": "string"}}], "trendedDataHistory": {"name": "string", "trendedDataList": [{"name": "string", "years": [{"year": "string", "monthData": ["string"]}], "labels": ["string"]}]}, "isNegative": "boolean"}], "inquiries": [{"provider": "string", "id": "string", "type": "string", "prefix": {"code": "string", "description": "string"}, "reportedDate": "string", "contactInformation": {"contactName": "string", "address": {"country": {"code": "string", "name": "string"}, "line1": "string", "line2": "string", "line3": "string", "line4": "string", "line5": "string", "firstReportedDate": "string", "lastReportedDate": "string", "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}, "phone": {"countryCode": "string", "areaCode": "string", "exchange": "string", "extension": "string"}}}], "consumerStatements": [{"provider": "string", "id": "string", "reportedDate": "string", "statement": "string"}], "publicRecords": {"provider": "string", "bankruptcies": [{"id": "string", "filedDate": "string", "reportedDate": "string", "referenceNumber": "string", "dispositionStatus": {"code": "string", "description": "string"}, "courtName": "string", "liability": {"amount": "number", "currency": "string"}, "exemptAmount": {"amount": "number", "currency": "string"}, "assetAmount": {"amount": "number", "currency": "string"}}], "judgments": [{"id": "string", "filedDate": "string", "reportedDate": "string", "courtName": "string", "defendant": "string", "amount": {"amount": "number", "currency": "string"}, "caseDocumentNumber": "string", "plaintiff": "string", "verifiedDate": "string", "status": {"code": "string", "description": "string"}, "satisfiedDate": "string"}], "liens": [{"id": "string", "filedDate": "string", "reportedDate": "string", "courtName": "string", "caseDocumentNumber": "string", "lienAmount": {"amount": "number", "currency": "string"}, "verifiedDate": "string", "status": "string"}]}, "collections": [{"provider": "string", "id": "string", "reportedDate": "string", "assignedDate": "string", "agencyClient": "string", "orginalAmountOwed": {"amount": "number", "currency": "string"}, "accountNumber": "string", "accountDesignatorCode": "string", "balanceDate": "string", "statusDate": "string", "status": "string", "amount": {"amount": "number", "currency": "string"}}]}]}
use granite::DateTimeUtc;
use granite::ResultExt;

#[derive(serde::Serialize)]
pub struct Input {
    pub base_url: String,
    pub apikey: String,
    pub secret: String,
}

#[derive(serde::Deserialize)]
struct ApiOutput {
    pub token: String,
    pub expires: i64,
    pub refresh: String,
}

pub struct Output {
    pub token: String,
    pub expire_ts: DateTimeUtc,
    pub refresh: String,
}

pub async fn call_direct_login(input: Input) -> granite::Result<Output> {
    // TODO: Maybe put this in a trait?
    let endpoint_uri = format!("{}/direct/login", input.base_url);
    let json_payload = granite::json! ({
        "apikey": input.apikey,
        "secret": input.secret,
    });

    let client = reqwest::Client::new();
    let response = client
        .post(endpoint_uri.to_string())
        .header("Content-Type", "application/json")
        .header("Accept", "application/json")
        .json(&json_payload)
        .send()
        .await
        .amend(|e| {
            e.add_context("sending request")
                .add_context(format!("url: {endpoint_uri}"))
                .set_external_message("Failed to send request to API.".to_string())
        })?;

    let status = response.status();

    // Failure branch
    if !status.is_success() {
        let text = response.text().await.amend(|e| {
            e.add_context("reading response text")
                .add_context(format!("url: {endpoint_uri}"))
                .add_context(format!("status: {status}"))
                .set_external_message("Failed to read response text from API call.".to_string())
        })?;

        let error = serde_json::from_str::<super::types::CrsApiError>(&text).amend(|e| {
            e.add_context("decoding response text")
                .add_context(format!("url: {endpoint_uri}"))
                .add_context(format!("status: {status}"))
                .add_context(format!("text: {text}"))
                .set_external_message("Failed to decode response text from API call.".to_string())
        })?;

        return Err(
            granite::Error::new(granite::ErrorType::ApiRequest).add_context(
                super::types::Error::BadRequest {
                    error: error.messages.join(", "),
                },
            ),
        );
    }

    // success branch
    let text = response.text().await.amend(|e| {
        e.add_context("reading response text")
            .add_context(format!("url: {endpoint_uri}"))
            .add_context(format!("status: {status}"))
            .set_external_message("Failed to read response text from API call.".to_string())
    })?;

    let output = serde_json::from_str::<ApiOutput>(&text).amend(|e| {
        e.add_context("decoding response text")
            .add_context(format!("url: {endpoint_uri}"))
            .add_context(format!("status: {status}"))
            .add_context(format!("text: {text}"))
            .set_external_message("Failed to decode response text from API call.".to_string())
    })?;

    Ok(Output {
        token: output.token,
        expire_ts: granite::Utc::now() + granite::Duration::seconds(output.expires),
        refresh: output.refresh,
    })
}

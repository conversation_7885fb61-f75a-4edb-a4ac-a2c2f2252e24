#[allow(dead_code)]
use granite::DateTimeUtc;
use granite::ResultExt;

#[derive(serde::Serialize)]
pub struct Input {
    pub base_url: String,
    pub server_token: String,
    pub user_id: granite::Uuid,
}

#[derive(serde::Deserialize)]
pub struct Output {
    pub crs_user_uuid: granite::Uuid,
    pub expire_ts: DateTimeUtc,
    pub token: String,
}

#[derive(serde::Deserialize)]
pub struct ApiOutput {
    #[serde(rename = "userId")]
    pub user_id: granite::Uuid,
    pub token: String,
}

pub async fn call_direct_preauth_token(input: Input) -> granite::Result<Output> {
    let endpoint_uri = format!("{}/direct/preauth-token/{}", input.base_url, input.user_id);

    let client = reqwest::Client::new();

    let response = client
        .get(endpoint_uri.to_string())
        .header("Authorization", format!("Bearer {}", input.server_token))
        .header("Content-Type", "application/json")
        .header("Accept", "application/json")
        .send()
        .await
        .amend(|e| {
            e.add_context("sending request")
                .add_context(format!("url: {endpoint_uri}"))
                .set_external_message("Failed to send request to API.".to_string())
        })?;

    let status = response.status();

    // Failure branch
    if !status.is_success() {
        let text = response.text().await.amend(|e| {
            e.add_context("reading response text")
                .add_context(format!("url: {endpoint_uri}"))
                .add_context(format!("status: {status}"))
                .set_external_message("Failed to read response text from API call.".to_string())
        })?;

        let error = serde_json::from_str::<super::types::CrsApiError>(&text).amend(|e| {
            e.add_context("decoding response text")
                .add_context(format!("url: {endpoint_uri}"))
                .add_context(format!("status: {status}"))
                .add_context(format!("text: {text}"))
                .set_external_message("Failed to decode response text from API call.".to_string())
        })?;

        return Err(
            granite::Error::new(granite::ErrorType::ApiRequest).add_context(
                super::types::Error::BadRequest {
                    error: error.messages.join(", "),
                },
            ),
        );
    }

    // success branch
    let text = response.text().await.amend(|e| {
        e.add_context("reading response text")
            .add_context(format!("url: {endpoint_uri}"))
            .add_context(format!("status: {status}"))
            .set_external_message("Failed to read response text from API call.".to_string())
    })?;

    let output = serde_json::from_str::<ApiOutput>(&text).amend(|e| {
        e.add_context("decoding response text")
            .add_context(format!("url: {endpoint_uri}"))
            .add_context(format!("status: {status}"))
            .add_context(format!("text: {text}"))
            .set_external_message("Failed to decode response text from API call.".to_string())
    })?;

    Ok(Output {
        crs_user_uuid: output.user_id,
        expire_ts: granite::Utc::now() + granite::Duration::seconds(30),
        token: output.token,
    })
}

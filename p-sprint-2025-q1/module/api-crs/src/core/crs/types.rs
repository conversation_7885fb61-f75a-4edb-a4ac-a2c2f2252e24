use std::ops::Deref;

#[granite::gtype(ApiOutput)]
pub enum CrsUserState {
    NotCreated,
    Created,
    Verified,
}

// ---------------------------------------------------------------------------------------------------------

#[derive(Debug)]
pub enum Error {
    RequestError { url: String, error: String },
    ResponseError { url: String, error: String },
    DecodingError { input: String, error: String },
    BadRequest { error: String },
    OtherError(String),
}

impl std::fmt::Display for Error {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{self:?}")
    }
}

impl std::error::Error for Error {}

// ---------------------------------------------------------------------------------------------------------

#[derive(serde::Serialize)]
pub struct CrsServerToken(String);

impl Deref for CrsServerToken {
    type Target = String;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl From<String> for CrsServerToken {
    fn from(s: String) -> Self {
        Self(s)
    }
}

impl std::fmt::Display for CrsServerToken {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

// ---------------------------------------------------------------------------------------------------------

#[derive(serde::Deserialize, Debug)]
pub struct CrsApiError {
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub codes: Vec<String>,
    pub messages: Vec<String>,
    pub details: Vec<Option<String>>,
}

impl std::fmt::Display for CrsApiError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{self:?}",)
    }
}

impl std::error::Error for CrsApiError {}

impl CrsApiError {
    /// Extract validation errors if present (but only if there are not other errors)
    pub fn get_validation_error(&self) -> Option<String> {
        // Check first two elements of the array, so if the 2nd one is None, you know the array has one element only (safely)
        match (self.codes.first(), self.codes.get(1)) {
            (Some(c), None) if c == "SC102" => {
                Some("This email address was already registered.  Please use a different email address or contact support for assistance.".to_string())
            }
            (Some(c), None) if c == "SC200" => {
                // collect the details into a single string
                Some(self.details
                    .iter()
                    .filter_map(|d| d.as_ref().map(|d| d.to_string()))
                    .map(|s| s
                        .replace("fname:", "")
                        .replace("lname:", "")
                        .replace("email:", "")
                        .replace("phone:", "")
                    )
                    .map(|s| format!("{s}. "))
                    .collect::<Vec<String>>()
                    .join("")
                )
            }
            _ => {
                None
            }
        }
    }
}

// ---------------------------------------------------------------------------------------------------------

#[granite::gtype]
pub struct CrsUserAuthPayload {
    pub crs_user_uuid: Uuid,
    pub token: String,
    pub expire_ts: DateTimeUtc,
}

// ---------------------------------------------------------------------------------------------------------

/// Data passed when user is created in CRS
#[derive(Debug)]
#[granite::gtype]
pub struct UserCreatedData {
    pub api_crs_user_uuid: Uuid,
    pub crs_user_uuid: Uuid,
}

/// Data passed when user is verified in CRS
#[derive(Debug)]
#[granite::gtype]
pub struct UserVerifiedData {
    pub api_crs_user_uuid: Uuid,
    pub report_data: Option<String>, // JSON string of report data
}

// ---------------------------------------------------------------------------------------------------------

pub struct CrsUserReport {}

// ---------------------------------------------------------------------------------------------------------
// These types are here strictly for the benefit of the Typescript client. They are not used in the Rust code or for actual encoding/decoding.
// They are used for interacting with the /users/* api endpoints which are for front end client-side use only.

#[granite::gtype(TsAll)]
pub struct CrsUserTokenResponse {
    id: String,
    email: String,
    fname: String,
    lname: String,
    idpass: bool,
    createdAt: i64,
    updatedAt: i64,
    smsMsg: bool,
    emailMsg: bool,
    pushMsg: bool,
    flags: i64,
    justEnrolled: bool,
    token: String,
    expires: i64,
    expires_ts: i64,
    refresh: String,
}

#[granite::gtype(TsAll)]
pub struct CrsUserVerificationData {
    pub fname: String,
    pub lname: String,
    pub mobile: String,
    pub email: String,
    pub dob: String,
    pub gender: String,
    pub ssn: String,
    pub street1: String,
    pub street2: Option<String>,
    pub city: String,
    pub state: String,
    pub zip: String,
}

#[granite::gtype(TsAll)]
pub struct CrsUserIdentityResponse {
    mobile: String,
    token: String,
    expires: DateTimeUtc,
}

#[granite::gtype(TsAll)]
pub struct CrsSMFASendLinkResponse {
    pub linkUrl: String,
    pub smsMessage: String,
    pub token: String,
    pub expires: DateTimeUtc,
}

#[granite::gtype(TsAll)]
pub struct CrsSMFAVerifyStatusResponse {
    pub id: String,
    pub email: String,
    pub fname: String,
    pub lname: String,
    pub idpass: bool,
    pub createdAt: i64,
    pub updatedAt: i64,
    pub smsMessage: bool,
    pub emailMessage: bool,
    pub pushMessage: bool,
    pub flags: i64,
    pub just_enrolled: bool,
}

#[granite::gtype(TsAll)]
pub struct CrsErrorResponse {
    timestamp: i64,
    codes: Vec<String>,
    messages: Vec<String>,
    details: Vec<String>,
}

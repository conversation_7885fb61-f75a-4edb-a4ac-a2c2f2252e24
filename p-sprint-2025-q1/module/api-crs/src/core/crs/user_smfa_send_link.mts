import { type Result } from "@granite/lib.mts";
import { CrsErrorResponse_decode } from "./typesλ.mts";

export type Input = {
    api_crs_user_uuid: string;
    crs_api_base_url: string;
    crs_user_token: string;
    crs_mobile_token: string;
};

export type Output = {
    api_crs_user_uuid: string;
    crs_smfa_message: string;
    crs_smfa_token: string;
};

export type Error = {
    api_crs_user_uuid: string;
    api_url: string;
    message: string;
    status_code: number;
    error: any;
};

/// The purpose of this call is to take a pre-auth token which was obtained from `direct_preauth_token`
/// and exchange it for a user token.  This call is made from the browser, not the server.
export async function call_user_smfa_send_link(input: Input): Promise<Result<Output, Error>> {
    const api_url = input.crs_api_base_url + "/users/smfa-send-link/" + input.crs_mobile_token;

    // get the user token
    const response = await fetch(
        api_url,
        {
            method: "POST",
            headers: {
                "Authorization": `Bearer ${input.crs_user_token}`,
                "Accept": "application/json",
            },
        },
    );

    if (!response.ok) {
        const error_text = await response.text();
        let error_message = "An unknown error has occurred"; // fallback message

        try {
            const parsed_json = JSON.parse(error_text);
            const decode_result = CrsErrorResponse_decode(parsed_json);
            if ("Ok" in decode_result && decode_result.Ok.messages.length > 0) {
                error_message = decode_result.Ok.messages.join(", ");
            }
        } catch {
            // Keep fallback message if parsing fails
        }

        return {
            Err: {
                api_crs_user_uuid: input.api_crs_user_uuid,
                api_url: api_url,
                message: error_message,
                status_code: response.status,
                error: error_text,
            },
        };
    }

    let data;
    try {
        data = await response.json();
    } catch (error) {
        return {
            Err: {
                api_crs_user_uuid: input.api_crs_user_uuid,
                api_url: api_url,
                message: "Failed to decode data from Credit Reporting Service",
                status_code: response.status,
                error: error,
            },
        };
    }

    //const expires_ts = BigInt(Math.floor(Date.now() / 1000) + Number(data.expires));
    const crs_smfa_message = data.smsMessage;
    const crs_smfa_token = data.token;

    if (typeof crs_smfa_message !== "string" || typeof crs_smfa_token !== "string") {
        return {
            Err: {
                api_crs_user_uuid: input.api_crs_user_uuid,
                api_url: api_url,
                message: "Failed to decode data from Credit Reporting Service",
                status_code: response.status,
                error: JSON.stringify(data),
            },
        };
    }

    return {
        Ok: {
            api_crs_user_uuid: input.api_crs_user_uuid,
            crs_smfa_message: crs_smfa_message,
            crs_smfa_token: crs_smfa_token,
        },
    };
}

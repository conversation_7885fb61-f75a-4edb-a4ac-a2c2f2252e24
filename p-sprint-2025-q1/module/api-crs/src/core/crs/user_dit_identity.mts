import { type Result } from "@granite/lib.mts";
import { CrsErrorResponse_decode } from "./typesλ.mts";

export type Input = {
    api_crs_user_uuid: string;
    crs_api_base_url: string;
    crs_user_token: string;
    fname: string;
    lname: string;
    mobile: string;
    ssn: string;
    dob: string;
    street1: string;
    street2: string | null;
    city: string;
    state: string;
    zip: string;
};

export type Output = {
    api_crs_user_uuid: string;
    crs_mobile_number: string;
    crs_mobile_token: string;
};

export type Error = {
    api_crs_user_uuid: string;
    api_url: string;
    message: string;
    status_code: number;
    error: any;
};

export async function call_user_dit_identity(input: Input): Promise<Result<Output, Error>> {
    const api_url = input.crs_api_base_url + "/users/dit-identity";

    const response = await fetch(
        api_url,
        {
            method: "POST",
            headers: {
                "Authorization": `Bearer ${input.crs_user_token}`,
                "Content-Type": "application/json",
                "Accept": "application/json",
            },
            credentials: "include",
            body: JSON.stringify({
                fname: input.fname,
                lname: input.lname,
                mobile: input.mobile.replace(/\D/g, ""),
                ssn: input.ssn.replace(/\D/g, ""),
                dob: input.dob,
                street1: input.street1,
                street2: input.street2,
                city: input.city,
                state: input.state,
                zip: input.zip,
            }),
        },
    );

    if (!response.ok) {
        const error_text = await response.text();
        let error_message = "An unknown error has occurred"; // fallback message

        try {
            const parsed_json = JSON.parse(error_text);
            const decode_result = CrsErrorResponse_decode(parsed_json);
            if ("Ok" in decode_result && decode_result.Ok.messages.length > 0) {
                error_message = decode_result.Ok.messages.join(", ");
            }
        } catch {
            // Keep fallback message if parsing fails
        }

        return {
            Err: {
                api_crs_user_uuid: input.api_crs_user_uuid,
                api_url: api_url,
                message: error_message,
                status_code: response.status,
                error: error_text,
            },
        };
    }

    let data;
    try {
        data = await response.json();
    } catch (error) {
        return {
            Err: {
                api_crs_user_uuid: input.api_crs_user_uuid,
                api_url: api_url,
                message: "Failed to decode data from Credit Reporting Service",
                status_code: response.status,
                error: error,
            },
        };
    }

    const mobile = data.mobile;
    const crs_mobile_token = data.token;

    if (typeof mobile !== "string" || typeof crs_mobile_token !== "string") {
        return {
            Err: {
                api_crs_user_uuid: input.api_crs_user_uuid,
                api_url: api_url,
                message: "Failed to decode data from Credit Reporting Service",
                status_code: response.status,
                error: JSON.stringify(data),
            },
        };
    }

    return {
        Ok: {
            api_crs_user_uuid: input.api_crs_user_uuid,
            crs_mobile_number: mobile,
            crs_mobile_token: crs_mobile_token,
        },
    };
}

use granite::ResultExt;

#[derive(Debug)]
pub enum User {
    /// In our DB, but not in CRS
    NotCreated(UserNotCreated),

    /// In our DB and in CRS, but not verified
    Created(UserCreated),

    /// Fully verified user
    Verified(UserVerified),

    /// Fully verified user with report data
    WithReport(UserWithReport),
}

// Convenience accessor for is_demo
impl User {
    pub fn is_demo(&self) -> bool {
        match self {
            User::NotCreated(user) => user.is_demo,
            User::Created(user) => user.is_demo,
            User::Verified(user) => user.is_demo,
            User::WithReport(user) => user.is_demo,
        }
    }
}

#[derive(granite::FromString)]
pub enum Status {
    NotCreated,
    Created,
    Verified,
}

#[granite::gtype]
pub struct ToNotCreated {
    pub is_demo: bool,
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
}

#[derive(Debug)]
#[granite::gtype]
pub struct UserNotCreated {
    pub is_demo: bool,
    pub api_crs_user_uuid: Uuid,
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub email: Option<String>,
    pub phone: Option<String>,
}

#[derive(Debug)]
#[granite::gtype]
pub struct UserCreated {
    pub is_demo: bool,
    pub api_crs_user_uuid: Uuid,
    pub crs_user_uuid: Uuid,
    pub first_name: String,
    pub last_name: String,
    pub email: String,
    pub phone: String,
}
pub enum UserCreatedResponse {
    Success(UserCreated),
    ValidationError(String),
}

#[derive(Debug)]
#[granite::gtype]
pub struct UserVerified {
    pub is_demo: bool,
    pub api_crs_user_uuid: Uuid,
    pub crs_user_uuid: Uuid,
    pub first_name: String,
    pub last_name: String,
    pub email: String,
    pub phone: String,
    pub verify_ts: DateTimeUtc,
}

#[derive(Debug)]
#[granite::gtype]
pub struct UserWithReport {
    pub is_demo: bool,
    pub api_crs_user_uuid: Uuid,
    pub crs_user_uuid: Uuid,
    pub first_name: String,
    pub last_name: String,
    pub email: String,
    pub phone: String,
    pub verify_ts: DateTimeUtc,
    pub last_report_esid: String,
    pub last_report_ts: DateTimeUtc,
}

#[derive(Debug)]
#[granite::gtype]
pub struct UserInactive {
    pub is_demo: bool,
    pub api_crs_user_uuid: Uuid,
}

pub async fn new(
    dbcx: &impl approck_postgres::DB,
    transition: ToNotCreated,
) -> granite::Result<UserNotCreated> {
    let row = granite::pg_row!(
        db = dbcx;
        args = {
            $is_demo: &transition.is_demo,
            $first_name: &transition.first_name,
            $last_name: &transition.last_name,
            $email: &transition.email,
            $phone: &transition.phone,
        };
        row = {
            api_crs_user_uuid: Uuid,
        };
        INSERT INTO
            api_crs.user
            (
                is_demo,
                first_name,
                last_name,
                email,
                phone
            )
        VALUES
            (
                $is_demo,
                $first_name,
                $last_name,
                $email,
                $phone
            )
        RETURNING
            api_crs_user_uuid
    )
    .await?;

    Ok(UserNotCreated {
        is_demo: transition.is_demo,
        api_crs_user_uuid: row.api_crs_user_uuid,
        first_name: transition.first_name,
        last_name: transition.last_name,
        email: transition.email,
        phone: transition.phone,
    })
}

impl UserNotCreated {
    pub async fn to_created(self, app: &impl crate::App) -> granite::Result<UserCreatedResponse> {
        let mut redis = app.redis_dbcx().await?;
        let dbcx = app.postgres_dbcx().await?;
        let crs_mod = app.api_crs_module();

        let (first_name, last_name, email, phone) = match (
            self.first_name,
            self.last_name,
            self.email,
            self.phone,
        ) {
            (Some(first_name), Some(last_name), Some(email), Some(phone)) => {
                (first_name, last_name, email, phone)
            }
            (first_name, last_name, email, phone) => {
                return Ok(UserCreatedResponse::ValidationError(format!(
                    "Missing required data: first_name: {first_name:?}, last_name: {last_name:?}, email: {email:?}, phone: {phone:?}"
                )));
            }
        };

        let phone = phone
            .chars()
            .filter(|c| c.is_ascii_digit())
            .collect::<String>();

        let register_result = crate::core::crs::direct_user_reg::call_direct_user_reg(
            crs_mod,
            &mut redis,
            crate::core::crs::direct_user_reg::Input {
                base_url: &crs_mod.api_base_url,
                fname: &first_name,
                lname: &last_name,
                email: &email,
                mobile: &phone,
            },
        )
        .await?;

        match register_result {
            crate::core::crs::direct_user_reg::Output::Success { user_id, .. } => {
                // update the database
                granite::pg_execute!(
                    db = dbcx;
                    args = {
                        $api_crs_user_uuid: &self.api_crs_user_uuid,
                        $crs_user_uuid: &user_id,
                    };
                    UPDATE
                        api_crs.user
                    SET
                        crs_user_uuid = $crs_user_uuid
                    WHERE
                        api_crs_user_uuid = $api_crs_user_uuid
                )
                .await?;

                Ok(UserCreatedResponse::Success(UserCreated {
                    is_demo: self.is_demo,
                    api_crs_user_uuid: self.api_crs_user_uuid,
                    crs_user_uuid: user_id,
                    first_name,
                    last_name,
                    email,
                    phone,
                }))
            }
            crate::core::crs::direct_user_reg::Output::ValidationError { message } => {
                Ok(UserCreatedResponse::ValidationError(message))
            }
        }
    }
}

impl UserCreated {
    pub async fn to_verified(self, app: &impl crate::App) -> granite::Result<UserVerified> {
        let dbcx = app.postgres_dbcx().await?;
        let verify_ts = granite::Utc::now();

        granite::pg_execute!(
            db = dbcx;
            args = {
                $api_crs_user_uuid: &self.api_crs_user_uuid,
                $verify_ts: &verify_ts,
            };
            UPDATE
                api_crs.user
            SET
                verify_ts = $verify_ts
            WHERE
                api_crs_user_uuid = $api_crs_user_uuid
        )
        .await?;

        Ok(UserVerified {
            is_demo: self.is_demo,
            api_crs_user_uuid: self.api_crs_user_uuid,
            crs_user_uuid: self.crs_user_uuid,
            first_name: self.first_name,
            last_name: self.last_name,
            email: self.email,
            phone: self.phone,
            verify_ts,
        })
    }
}

impl UserVerified {
    pub async fn to_with_report(self, app: &impl crate::App) -> granite::Result<UserWithReport> {
        let dbcx = app.postgres_dbcx().await?;

        let latest_report = get_last_report_b1r_direct(
            app,
            self.api_crs_user_uuid,
            self.crs_user_uuid,
            "Initial pull during credit report connection.".to_string(),
            self.is_demo,
        )
        .await?;

        granite::pg_execute!(
            db = dbcx;
            args = {
                $api_crs_user_uuid: &self.api_crs_user_uuid,
                $last_report_esid: &latest_report.last_report_esid,
                $last_report_ts: &latest_report.last_report_ts,
            };
            UPDATE
                api_crs.user
            SET
                last_report_esid = $last_report_esid,
                last_report_ts = $last_report_ts
            WHERE
                api_crs_user_uuid = $api_crs_user_uuid
        )
        .await?;

        Ok(UserWithReport {
            is_demo: self.is_demo,
            api_crs_user_uuid: self.api_crs_user_uuid,
            crs_user_uuid: self.crs_user_uuid,
            first_name: self.first_name,
            last_name: self.last_name,
            email: self.email,
            phone: self.phone,
            verify_ts: self.verify_ts,
            last_report_esid: latest_report.last_report_esid,
            last_report_ts: latest_report.last_report_ts,
        })
    }
}

impl UserWithReport {
    pub async fn get_latest_report_b1r(
        self,
        app: &impl crate::App,
    ) -> granite::Result<crate::types::CrsUserLastReportUS1B> {
        get_last_report_b1r_cached(app, self.api_crs_user_uuid, self.crs_user_uuid).await
    }

    pub async fn get_debt_view(
        self,
        app: &impl crate::App,
    ) -> granite::Result<crate::types::CRDebtView> {
        let latest_report =
            get_last_report_b1r_cached(app, self.api_crs_user_uuid, self.crs_user_uuid).await?;

        Ok(latest_report.last_report_data.to_debt_view())
    }
}

pub async fn load(
    dbcx: &impl approck_postgres::DB,
    api_crs_user_uuid: granite::Uuid,
) -> granite::Result<User> {
    let row = granite::pg_row!(
        db = dbcx;
        args = {
            $api_crs_user_uuid: &api_crs_user_uuid,
        };
        row = {
            crs_user_uuid: Option<Uuid>,
            is_demo: bool,
            first_name: Option<String>,
            last_name: Option<String>,
            email: Option<String>,
            phone: Option<String>,
            verify_ts: Option<DateTimeUtc>,
            last_report_esid: Option<String>,
            last_report_ts: Option<DateTimeUtc>,
            data_hash: Option<String>,
        };
        SELECT
            crs_user_uuid,
            is_demo,
            first_name,
            last_name,
            email,
            phone,
            verify_ts,
            last_report_esid,
            last_report_ts,
            data_hash
        FROM
            api_crs.user
        WHERE TRUE
            AND api_crs_user_uuid = $api_crs_user_uuid::uuid
    )
    .await?;

    // Required data for each status is enforced via database triggers.
    match (
        row.crs_user_uuid,
        row.verify_ts,
        row.last_report_esid,
        row.last_report_ts,
    ) {
        // NotCreated
        (None, None, None, None) => Ok(User::NotCreated(UserNotCreated {
            is_demo: row.is_demo,
            api_crs_user_uuid,
            first_name: row.first_name,
            last_name: row.last_name,
            email: row.email,
            phone: row.phone,
        })),

        // Created
        (Some(crs_user_uuid), None, None, None) => {
            let (first_name, last_name, email, phone) =
                match (row.first_name, row.last_name, row.email, row.phone) {
                    (Some(first_name), Some(last_name), Some(email), Some(phone)) => {
                        (first_name, last_name, email, phone)
                    }
                    _ => {
                        return Err(granite::Error::new(granite::ErrorType::Unexpected)
                            .set_external_message(format!(
                                "Credit Report user {api_crs_user_uuid} is missing required data.",
                            ))
                            .add_context("Created user is missing required data"));
                    }
                };

            Ok(User::Created(UserCreated {
                is_demo: row.is_demo,
                api_crs_user_uuid,
                crs_user_uuid,
                first_name,
                last_name,
                email,
                phone,
            }))
        }

        // Verified
        (Some(crs_user_uuid), Some(verify_ts), None, None) => {
            let (first_name, last_name, email, phone) =
                match (row.first_name, row.last_name, row.email, row.phone) {
                    (Some(first_name), Some(last_name), Some(email), Some(phone)) => {
                        (first_name, last_name, email, phone)
                    }
                    _ => {
                        return Err(granite::Error::new(granite::ErrorType::Unexpected)
                            .set_external_message(format!(
                                "Credit Report user {api_crs_user_uuid} is missing required data.",
                            ))
                            .add_context("Verified user is missing required data"));
                    }
                };

            Ok(User::Verified(UserVerified {
                is_demo: row.is_demo,
                api_crs_user_uuid,
                crs_user_uuid,
                first_name,
                last_name,
                email,
                phone,
                verify_ts,
            }))
        }

        // WithReport
        (Some(crs_user_uuid), Some(verify_ts), Some(last_report_esid), Some(last_report_ts)) => {
            let (first_name, last_name, email, phone) =
                match (row.first_name, row.last_name, row.email, row.phone) {
                    (Some(first_name), Some(last_name), Some(email), Some(phone)) => {
                        (first_name, last_name, email, phone)
                    }
                    _ => {
                        return Err(granite::Error::new(granite::ErrorType::Unexpected)
                            .set_external_message(format!(
                                "Credit Report user {api_crs_user_uuid} is missing required data.",
                            ))
                            .add_context("WithReport user is missing required data"));
                    }
                };

            Ok(User::WithReport(UserWithReport {
                is_demo: row.is_demo,
                api_crs_user_uuid,
                crs_user_uuid,
                first_name,
                last_name,
                email,
                phone,
                verify_ts,
                last_report_esid,
                last_report_ts,
            }))
        }

        _ => Err(granite::Error::new(granite::ErrorType::InvalidData)
            .set_external_message(format!(
                "Credit Report user {api_crs_user_uuid} is in an invalid state.",
            ))
            .add_context(format!("api_crs_user_uuid: {api_crs_user_uuid}"))),
    }
}

async fn get_last_report_b1r_cached(
    app: &impl crate::App,
    api_crs_user_uuid: granite::Uuid,
    crs_user_uuid: granite::Uuid,
) -> granite::Result<crate::types::CrsUserLastReportUS1B> {
    let mut redis = app.redis_dbcx().await?;
    let redis_key = format!("api_crs:user:{api_crs_user_uuid}:latest_report");

    let us1b_json_text = match redis.get_val_opt::<String>(&redis_key).await? {
        Some(json_text) => json_text,
        None => {
            return Err(granite::Error::new(granite::ErrorType::Unexpected)
                .add_context(format!("api_crs_user_uuid: {api_crs_user_uuid}"))
                .set_external_message(format!(
                    "Credit Report user {api_crs_user_uuid} is missing required data.",
                )));
        }
    };

    // parse the json
    let us1b: crate::types::US1B = serde_json::from_str(&us1b_json_text).amend(|e| {
        e.add_context("decoding response text")
            .add_context(format!("api_crs_user_uuid: {api_crs_user_uuid}"))
            .add_context(format!("crs_user_uuid: {crs_user_uuid}"))
            .set_external_message("Failed to decode response text from API call.".to_string())
    })?;

    Ok(crate::types::CrsUserLastReportUS1B {
        api_crs_user_uuid,
        crs_user_uuid,
        last_report_esid: us1b.id.clone(),
        last_report_ts: us1b.generatedDate,
        last_report_data: us1b,
    })
}

async fn get_last_report_b1r_direct(
    app: &impl crate::App,
    api_crs_user_uuid: granite::Uuid,
    crs_user_uuid: granite::Uuid,
    justification: String,
    is_demo: bool,
) -> granite::Result<crate::types::CrsUserLastReportUS1B> {
    let mut redis = app.redis_dbcx().await?;
    let redis_key = format!("api_crs:user:{api_crs_user_uuid}:latest_report");

    let us1b_json_text = crate::core::crs::direct_efx_latest_report::call_direct_efx_latest_report(
        app,
        crate::core::crs::direct_efx_latest_report::Input {
            base_url: app.api_crs_module().api_base_url.clone(),
            server_token: app.api_crs_module().get_server_token(&mut redis).await?,
            api_crs_user_uuid,
            justification,
            is_demo,
        },
    )
    .await?;

    // parse the json
    let us1b: crate::types::US1B = serde_json::from_str(&us1b_json_text).amend(|e| {
        e.add_context("decoding response text")
            .add_context(format!("api_crs_user_uuid: {api_crs_user_uuid}"))
            .add_context(format!("crs_user_uuid: {crs_user_uuid}"))
            .set_external_message("Failed to decode response text from API call.".to_string())
    })?;

    redis.set_val(&redis_key, &us1b_json_text).await?;
    redis.set_expire(&redis_key, 86400 * 45).await?;

    Ok(crate::types::CrsUserLastReportUS1B {
        api_crs_user_uuid,
        crs_user_uuid,
        last_report_esid: us1b.id.clone(),
        last_report_ts: us1b.generatedDate,
        last_report_data: us1b,
    })
}

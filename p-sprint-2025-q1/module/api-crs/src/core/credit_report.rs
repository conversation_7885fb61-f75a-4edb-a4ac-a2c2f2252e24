use granite::Decimal;

pub fn us1b_to_debt_view(us1b: &crate::types::US1B) -> crate::types::CRDebtView {
    let mut total_monthly_payment = Decimal::ZERO;
    let mut total_balance = Decimal::ZERO;
    let mut debts = Vec::new();

    for pv in &us1b.providerViews {
        let provider = &pv.provider;

        for (ia, account_type) in [
            (&pv.installmentAccounts, "Installment"),
            (&pv.mortgageAccounts, "Mortgage"),
            (&pv.revolvingAccounts, "Revolving"),
            (&pv.otherAccounts, "Other"),
        ] {
            for account in ia {
                if account.accountOpen
                    && account.balanceAmount.amount > Decimal::ZERO
                    && let Some(monthly_payment) = &account.monthlyPayment
                    && monthly_payment.currency == "USD"
                {
                    total_monthly_payment += monthly_payment.amount;
                    total_balance += account.balanceAmount.amount;
                    debts.push(crate::types::CRDebt {
                        provider: provider.clone(),
                        id: account.id.clone(),
                        account_type: account_type.to_string(),
                        name: account.accountName.clone(),
                        balance: account.balanceAmount.amount,
                        monthly_payment: monthly_payment.amount,
                    });
                }
            }
        }
    }

    crate::types::CRDebtView {
        debts,
        total_monthly_payment,
        total_balance,
    }
}

use granite::{DateTimeUtc, JsonValue};
use serde::Deserialize;
use serde::Deserializer;

/// Deserialize a date that can be either an ISO timestamp string or milliseconds since epoch (as number or string)
pub fn deserialize_flexible_datetime<'de, D>(deserializer: D) -> Result<DateTimeUtc, D::Error>
where
    D: Deserializer<'de>,
{
    let value = JsonValue::deserialize(deserializer)?;

    match value {
        // Handle ISO timestamp string
        JsonValue::String(s) => {
            // Try parsing as ISO timestamp first
            if let Ok(dt) = chrono::DateTime::parse_from_rfc3339(&s) {
                return Ok(dt.with_timezone(&chrono::Utc));
            }

            Err(serde::de::Error::custom(format!(
                "Invalid date format: {s}",
            )))
        }

        // Handle milliseconds as number
        JsonValue::Number(n) => {
            if let Some(millis) = n.as_i64() {
                if let Some(dt) = chrono::DateTime::from_timestamp_millis(millis) {
                    return Ok(dt);
                }
            }
            Err(serde::de::Error::custom(format!("Invalid timestamp: {n}",)))
        }

        _ => Err(serde::de::Error::custom("Date must be a string or number")),
    }
}

/// Deserialize an optional date that can be either an ISO timestamp string or milliseconds since epoch (as number or string)
pub fn deserialize_flexible_datetime_option<'de, D>(
    deserializer: D,
) -> Result<Option<DateTimeUtc>, D::Error>
where
    D: Deserializer<'de>,
{
    let value = JsonValue::deserialize(deserializer)?;

    match value {
        // Handle null values
        JsonValue::Null => Ok(None),

        // Handle ISO timestamp string
        JsonValue::String(s) => {
            if s.is_empty() {
                return Ok(None);
            }

            // Try parsing as ISO timestamp first
            if let Ok(dt) = chrono::DateTime::parse_from_rfc3339(&s) {
                return Ok(Some(dt.with_timezone(&chrono::Utc)));
            }

            Err(serde::de::Error::custom(format!(
                "Invalid date format: {s}",
            )))
        }

        // Handle milliseconds as number
        JsonValue::Number(n) => {
            if let Some(millis) = n.as_i64() {
                if let Some(dt) = chrono::DateTime::from_timestamp_millis(millis) {
                    return Ok(Some(dt));
                }
            }
            Err(serde::de::Error::custom(format!("Invalid timestamp: {n}",)))
        }

        _ => Err(serde::de::Error::custom(
            "Date must be a string, number, or null",
        )),
    }
}

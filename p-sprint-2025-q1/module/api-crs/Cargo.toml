[package]
name = "api-crs"
version = "0.1.0"
edition = "2024"

[package.metadata.approck.mod]
extends = ["approck", "bux", "granite", "addr-iso"]

[dependencies]
approck = { workspace = true }
bux = { workspace = true }
granite = { workspace = true }
approck-redis = { workspace = true }
approck-postgres = { workspace = true }
addr-iso = { workspace = true }

maud = { workspace = true }
reqwest = { workspace = true, features = ["json"] }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
chrono = { workspace = true, features = ["serde"] }

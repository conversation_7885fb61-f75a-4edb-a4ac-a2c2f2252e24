## The exact process from start to finish is this:

1. Client:

- Send client id and applicant type to server
- Show spinner overlay

2. Server:

- Look up the client record (dialog data)
- Render the create dialog with form and fields prefilled (f and l name, phone, email)
- Return HTML to client

3. Client:

- Inject HTML into DOM, hide spinner
- User fills out form
- User submits form - show spinner overlay
- Client sends form data to server

4. Server:

- Validate form data
- If invalid, return error to client
- If valid, create user through CRS API
- Update user record with CRS user id
- Render the authenticate dialog with form and fields prefilled (dob, gender, address, etc...)
- Return HTML to client

5. Client:

- Inject HTML into DOM, hide spinner
- User fills out form
- User submits form - show spinner overlay
- C<PERSON> sends form data to server

1. Create a new user record
2. Register the user with CRS
3. Update the user record with the user_id

---

launch_dialog("ClientApplicant", "uuid-here")

1. call api_crs.api.dialog.launch with "ClientApplicant", "uuid-here"
2. that API can call `app.get_dialog_data("ClientApplicantnt", "uuid-here")`
3. in df4l/src/module/api_crs.rs, we have to implement get_dialog_data, and can do security check there
   Return value that is a struct defined in CRS with
   Option<api_crs_user_uuid>, Option<first_name>, etc...
4. When we have an on_create event, it can actually call app.crs_on_create("ClientApplicant", "uuid-here", {create_data})
5. when we have an on_verified event, it can actually call app.crs_on_verified("ClientApplicant", "uuid-here", {verified_data})

In Summary:
three trait methods for api_crs::App trait

1. crs_dialog_data
2. crs_user_created
3. crs_user_verified

- The client side represents a bunch of states compared to server side:

-- NotCreatedForm // Initial form for creating user
-- NotCreatedProcessing // Processing the creation request
-- NotCreatedError // Error during creation (API failures, timeouts, validation errors)
-- NotVerifiedForm // Form for authentication data (DOB, address, etc.)
-- NotVerifiedProcessing // Processing authentication data
-- NotVerifiedError // Error during verification process (API failures, timeouts, token issues)
-- NotVerifiedToken // Handling token acquisition
-- NotVerifiedAuthenticate // Authenticating with CRS API
-- NotVerifiedWaiting // Waiting for SMFA verification
-- NotVerifiedPolling // Polling for verification status
-- VerifiedProcessing // Processing after verification
-- Verified // User fully verified
-- FatalError // Unrecoverable error state requiring dialog restart

- In /api/, add these functions to be called by API endpoints:

-- 1. launch_dialog.rs (this would call app.crs_dialog_data(...))
-- 2. create_user.rs (this would call app.crs_user_created() if successful)
-- 3. user_verified.rs (this would call app.crs_user_verified() if successful)

- The new table structure should look something like this:

CREATE TABLE api_crs.user (
api_crs_user_uuid uuid DEFAULT public.uuidv7() NOT NULL,
crs_user_uuid uuid not null,
uuid_owner uuid not null,
create_ts timestamptz(6) NOT NULL DEFAULT now(),
first_name varchar(64),
last_name varchar(64),
email varchar(255),
phone varchar(20),
verify_ts timestamptz(6),
last_report_esid varchar(64),
last_report_ts timestamptz(6),
data_hash varchar(64),
CONSTRAINT user_pkey PRIMARY KEY (api_crs_user_uuid)
);

Notes from review meeting:
Get rid of pg_sql trigger
Cleanup fields, remove unneeded state and populate fields
Don't use as in Typescript initializations, use SEC or make sure it has the proper type already
Put initializations on the variable declarations if the element is already in the DOM, not being added through markup manipulation during class or element initialization
SSN - add regex to field, add note to enter with dashes or use shadow example
Zip code - add regex to field, add shadow example, digits only
Primary button on left with distinct color
Look at app_socket.mts for example of client side state machine
If a state errors out, then go into error state and display message to close dialog and try again
Code must not throw exceptions, handle errors gracefully
Dialog needs associated data struct to launch
Pass it the data it needs to launch (an enum)
NotCreated ( first_name, last_name, email, mobile )
Created ( gender, birth_date, address_1, address_2, city, state, zip )

Function on app to get dialog data
Function to set as created
Function to set as verified

Only three states server side:
NotCreated
Created
Verified

Many states client side, see notes below
FIxed dialog height, keeps dialog from thrashing
Spinner w/timeout for loading stages, and updates to the user as to what is happening
Add permission trait that must be implemented
user_access(identity_uuid)
Leave a comment that this all needs a SECON review
Process for each state change/dialog launch:
Call api
Render it
Inject it
Bind to it
Show it
In /api/ - bunch of ts files, one for each thing we need to do
df4l api - in wizard/
Create a schema update file named properly

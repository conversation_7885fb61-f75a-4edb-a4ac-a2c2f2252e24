pub mod customer;
pub mod index;
pub mod products;
pub mod subscription;

#[approck::prefix(/admin/stripe/)]
pub mod prefix {
    pub fn menu(menu: Menu) {
        menu.set_label_uri("Stripe Dashboard", "/admin/stripe/");
        menu.add_link("View Customers", "/admin/stripe/customer/");
        menu.add_link("View Subscriptions", "/admin/stripe/subscription/");
        menu.add_link("View Products", "/admin/stripe/products");
    }

    pub fn auth(_identity: Identity) {
        // Authentication will be handled by the approck system
        // This is just a placeholder for now
    }
}

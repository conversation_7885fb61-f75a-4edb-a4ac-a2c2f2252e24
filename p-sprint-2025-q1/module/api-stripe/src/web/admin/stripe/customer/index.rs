#[approck::http(GET /admin/stripe/customer/?keyword=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        qs: QueryString,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Stripe Customers");

        let list = crate::api::admin::stripe::customer::list::call(
            app,
            identity,
            crate::api::admin::stripe::customer::list::Input {
                keyword: qs.keyword.clone(),
            },
        )
        .await?
        .customers;

        let mut dt = bux::component::detail_table(list);
        dt.add_keyword_filter(qs.keyword.as_deref());

        dt.add_name_column(|a| &a.name);
        dt.add_column("Stripe Customer ID", |a| html! { (a.customer_id) });
        dt.add_email_column(|a| a.email.as_deref().unwrap_or(""));
        dt.add_column(
            "Created",
            |a| html! { (a.create_ts.format("%B %d, %Y").to_string()) },
        );
        dt.add_link_column(
            "Details",
            |a| crate::api::admin::stripe::customer::list::ml_customer(a.api_stripe_customer_uuid),
            |_a| "View Customer".to_string(),
        );

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}

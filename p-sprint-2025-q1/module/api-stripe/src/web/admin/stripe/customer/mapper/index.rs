#[approck::http(GET /admin/stripe/customer/{api_stripe_customer_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        use crate::api::admin::stripe::customer_detail::stripe_customer_detail;

        let customer = stripe_customer_detail::call(
            app,
            identity,
            stripe_customer_detail::Input {
                api_stripe_customer_uuid: path.api_stripe_customer_uuid,
            },
        )
        .await?;

        let customer_info = {
            let mut deck = bux::component::insight_deck::InsightDeck::new("Stripe Customer");
            deck.description("An overview of stripe customer information.");

            deck.add_basic_row("fas fa-user", "Full Name", html!((customer.customer.name)));
            deck.add_basic_row(
                "fas fa-id-badge",
                "Stripe Customer ID",
                html!((customer.customer.customer_id)),
            );

            deck.add_basic_row(
                "fas fa-calendar-plus",
                "Created On",
                html!((customer.customer.create_ts.format("%b %d, %Y %I:%M %p"))),
            );
            deck.add_basic_row(
                "fas fa-envelope",
                "Email",
                html!((customer.customer.email.unwrap_or_default())),
            );

            deck
        };

        let subscriptions = {
            let mut dt = bux::component::detail_table(&customer.customer.subscriptions);
            dt.add_column(
                "Name",
                |a| html! { (a.description.as_deref().unwrap_or("")) },
            );
            //subscription link
            dt.add_link_column(
                "Subscription ID",
                |a| {
                    crate::api::admin::stripe::subscription::list::ml_subscription(
                        a.api_stripe_subscription_uuid,
                    )
                },
                |a| a.subscription_id.to_string(),
            );
            //product name and price
            dt.add_column("Product", |a| html! { (a.product_name) });
            dt.add_column("Price", |a| html! { (a.product_price_html()) });

            dt.add_column(
                "Created",
                |a| html! { (a.create_ts.format("%B %d, %Y").to_string()) },
            );
            dt
        };

        let invoice_items = {
            let mut dt = bux::component::detail_table(&customer.customer.invoice_items);
            dt.add_column(
                "Name",
                |a| html! { (a.description.as_deref().unwrap_or("")) },
            );
            dt.add_column(
                "Created",
                |a| html! { (a.create_ts.format("%B %d, %Y").to_string()) },
            );
            dt.add_column("Product", |a| html! { (a.product_name) });
            dt.add_column("Amount", |a| html! { (a.amount_html()) });
            dt
        };

        let html_content = html!(
            insight-deck {
                (customer_info)
                panel {
                    header {
                        h5 { "Subscriptions" }
                    }
                    content {
                        (subscriptions)
                    }
                }
                panel {
                    header {
                        h5 { "Invoice Items" }
                    }
                    content {
                        (invoice_items)
                    }
                }
            }
        );

        doc.add_body(html_content);

        Ok(Response::HTML(doc.into()))
    }
}

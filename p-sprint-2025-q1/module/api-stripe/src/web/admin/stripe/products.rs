#[approck::http(GET /admin/stripe/products; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("Stripe Products");

        let list = crate::api::admin::stripe::product::list::call(
            app,
            identity,
            crate::api::admin::stripe::product::list::Input {},
        )
        .await?
        .products;

        let mut dt = bux::component::detail_table(list);
        dt.add_column("Stripe Product ID", |a| html! { (a.product_id) });
        dt.add_column("Name", |a| html! { (a.name) });
        dt.add_column("Price", |a| html! { (a.price_html()) });
        dt.add_column(
            "Created",
            |a| html! { (a.create_ts.format("%B %d, %Y").to_string()) },
        );
        dt.add_column(
            "Description",
            |a| html! { (a.description.as_deref().unwrap_or("")) },
        );

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}

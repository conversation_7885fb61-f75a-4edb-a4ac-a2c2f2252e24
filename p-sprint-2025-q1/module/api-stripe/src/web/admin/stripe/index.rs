#[approck::http(GET /admin/stripe/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("Stripe Admin Dashboard");

        let summary = crate::api::admin::stripe::log::summary::call(
            app,
            identity,
            crate::api::admin::stripe::log::summary::Input {},
        )
        .await?
        .summary;

        doc.add_body(html! {
            panel {
                content {
                    h2 { "Stripe Admin Dashboard" }
                    p { "Manage and monitor Stripe API usage." }
                }
            }

            grid-3 {
                panel {
                    header {
                        h5 { "Total Customers" }
                    }
                    content {
                        h2 { (summary.total_customers) }
                    }
                }
                panel {
                    header {
                        h5 { "Total Subscriptions" }
                    }
                    content {
                        h2 { (summary.total_subscriptions) }
                    }
                }
                panel {
                    header {
                        h5 { "Total Products" }
                    }
                    content {
                        h2 { (summary.total_products) }
                    }
                }
            }

            panel {
                header {
                    h5 { "Quick Actions" }
                }
                content {
                    a.btn.primary href="/admin/stripe/customer/" {
                        "View Customers"
                    }
                    " "
                    a.btn.secondary href="/admin/stripe/subscription/" {
                        "View Subscriptions"
                    }
                    " "
                    a.btn.secondary href="/admin/stripe/products" {
                        "View Products"
                    }
                }
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}

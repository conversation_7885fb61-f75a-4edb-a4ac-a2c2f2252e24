#[approck::http(GET /admin/stripe/subscription/?keyword=Option<String>; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        qs: QueryString,
    ) -> Result<Response> {
        use maud::html;

        doc.set_title("Stripe Subscriptions");

        let list = crate::api::admin::stripe::subscription::list::call(
            app,
            identity,
            crate::api::admin::stripe::subscription::list::Input {
                keyword: qs.keyword.clone(),
            },
        )
        .await?
        .subscriptions;

        let mut dt = bux::component::detail_table(list);
        dt.add_keyword_filter(qs.keyword.as_deref());

        dt.add_column("Stripe Subscription ID", |a| html! { (a.subscription_id) });
        dt.add_column(
            "Created",
            |a| html! { (a.create_ts.format("%B %d, %Y").to_string()) },
        );
        dt.add_column(
            "Description",
            |a| html! { (a.description.as_deref().unwrap_or("")) },
        );
        dt.add_column("Customer", |a| html! { (a.customer_name) });
        dt.add_column("Stripe Customer ID", |a| html! { (a.customer_id) });
        dt.add_link_column(
            "Details",
            |a| {
                crate::api::admin::stripe::subscription::list::ml_subscription(
                    a.api_stripe_subscription_uuid,
                )
            },
            |_a| "View Subscription".to_string(),
        );

        doc.add_body(html!((dt)));

        Ok(Response::HTML(doc.into()))
    }
}

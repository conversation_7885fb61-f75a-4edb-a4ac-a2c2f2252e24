#[approck::http(GET /admin/stripe/subscription/{api_stripe_subscription_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use maud::html;

        use crate::api::admin::stripe::subscription_detail::stripe_subscription_detail;
        let subscription = stripe_subscription_detail::call(
            app,
            identity,
            stripe_subscription_detail::Input {
                api_stripe_subscription_uuid: path.api_stripe_subscription_uuid,
            },
        )
        .await?;

        use crate::api::admin::stripe::invoice_item::list;
        let invoice_items = list::call(
            app,
            identity,
            list::Input {
                api_stripe_customer_uuid: None,
                api_stripe_subscription_uuid: Some(path.api_stripe_subscription_uuid),
            },
        )
        .await?;

        // call subscription_get
        let stripe_subscription_detail = app
            .stripe()
            .subscription_get(&subscription.subscription.subscription_id)
            .await
            .into_result()?;
        println!("stripe_subscription_detail: {stripe_subscription_detail:?}");

        let stripe_status_html = stripe_subscription_detail.status_html();
        let cancellation_info = stripe_subscription_detail.cancellation_info();

        doc.set_title("Subscription Details");

        let mut table = bux::component::info_table(subscription);
        table.set_heading("Stripe Subscription Information");
        table.add_identifer_row("Stripe Subscription ID:", |u| {
            &u.subscription.subscription_id
        });
        table.add_row(
            "Trial Period Days:",
            |u| html! { (u.subscription.trial_period_days) },
        );
        table.add_create_ts(|u| u.subscription.create_ts);
        table.add_name_row(|u| &u.subscription.customer_name);
        //make customer id row a link to the customer page
        table.add_link_row(
            "Stripe Customer ID:",
            |u| {
                crate::api::admin::stripe::customer::list::ml_customer(
                    u.subscription.api_stripe_customer_uuid,
                )
            },
            |u| u.subscription.customer_id.to_string(),
        );
        table.add_identifer_row("Product Name:", |u| &u.subscription.product_name);
        table.add_row(
            "Product Price:",
            |u| html! { (u.subscription.price_html()) },
        );
        table.add_note_row(|u| u.subscription.description.as_deref().unwrap_or(""));
        table.add_row("Status HTML:", move |_u| html! { (stripe_status_html) });
        if let Some(ref cancellation_info) = cancellation_info {
            table.add_row("Cancellation Info:", move |_u| {
                html! { (cancellation_info) }
            });
        }

        let invoice_items = {
            let mut dt = bux::component::detail_table(&invoice_items.invoice_items);
            dt.add_column(
                "Name",
                |a| html! { (a.description.as_deref().unwrap_or("")) },
            );
            dt.add_column(
                "Created",
                |a| html! { (a.create_ts.format("%B %d, %Y").to_string()) },
            );
            dt.add_column("Product", |a| html! { (a.product_name) });
            dt.add_column("Amount", |a| html! { (a.amount_html()) });
            dt
        };

        let html_content = html!(
            (table)
            h2 { "Invoice Items" }
            (invoice_items)
        );

        doc.add_body(html_content);
        Ok(Response::HTML(doc.into()))
    }
}

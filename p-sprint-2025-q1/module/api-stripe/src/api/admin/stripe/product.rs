#[approck::api]
pub mod list {
    use granite::return_authorization_error;
    use granite::{Decimal, Uuid};

    #[granite::gtype(ApiInput)]
    pub struct Input {}

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub products: Vec<Product>,
    }
    #[granite::gtype(ApiOutput)]
    pub struct Product {
        pub api_stripe_product_uuid: Uuid,
        pub product_id: String,
        pub create_ts: DateTimeUtc,
        pub name: String,
        pub description: Option<String>,
        pub price_amount: String,
        pub active: bool,
    }

    impl Product {
        pub fn price_html(&self) -> String {
            // Try to parse the price_amount as a Decimal
            if let Ok(amount) = self.price_amount.parse::<Decimal>() {
                // Format with $ and two decimal places
                format!("${amount:.2}")
            } else {
                // Fallback if parsing fails
                format!("${}", self.price_amount)
            }
        }
    }
    pub fn ml_product(api_stripe_product_uuid: Uuid) -> String {
        format!("/admin/stripe/product/{api_stripe_product_uuid}/")
    }

    pub async fn call(app: App, identity: Identity, _input: Input) -> Result<Output> {
        if !identity.is_admin() {
            return_authorization_error!("Only admins can access the Stripe console");
        }

        let dbcx = app.postgres_dbcx().await?;
        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {};
            row = {
                api_stripe_product_uuid: Uuid,
                product_id: String,
                create_ts: DateTimeUtc,
                name: String,
                description: Option<String>,
                price_amount: String,
                active: bool,
            };
            SELECT
                p.api_stripe_product_uuid,
                p.product_id,
                p.create_ts,
                p.name,
                p.description,
                (
                    SELECT unit_amount
                    FROM api_stripe.price
                    WHERE api_stripe_product_uuid = p.api_stripe_product_uuid
                    LIMIT 1
                ) AS price_amount,
                p.active
            FROM
                api_stripe.product p
        )
        .await?;

        let products = rows
            .into_iter()
            .map(|row| Product {
                api_stripe_product_uuid: row.api_stripe_product_uuid,
                product_id: row.product_id,
                create_ts: row.create_ts,
                name: row.name,
                description: row.description,
                price_amount: row.price_amount,
                active: row.active,
            })
            .collect();

        Ok(Output { products })
    }
}

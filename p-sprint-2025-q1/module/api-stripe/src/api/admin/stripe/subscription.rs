#[approck::api]
pub mod list {
    use granite::Uuid;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub subscriptions: Vec<Subscription>,
    }
    #[granite::gtype(ApiOutput)]
    pub struct Subscription {
        pub api_stripe_subscription_uuid: Uuid,
        pub subscription_id: String,
        pub api_stripe_customer_uuid: Uuid,
        pub customer_id: String,
        pub customer_name: String,
        pub api_stripe_product_uuid: Uuid,
        pub product_id: String,
        pub product_name: String,
        pub create_ts: DateTimeUtc,
        pub description: Option<String>,
    }

    pub fn ml_subscription(api_stripe_subscription_uuid: Uuid) -> String {
        format!("/admin/stripe/subscription/{api_stripe_subscription_uuid}/")
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.is_admin() {
            return_authorization_error!("Only admins can access the Stripe console");
        }

        let dbcx = app.postgres_dbcx().await?;
        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $keyword: &input.keyword,
            };
            row = {
                api_stripe_subscription_uuid: Uuid,
                subscription_id: String,
                api_stripe_customer_uuid: Uuid,
                customer_id: String,
                customer_name: String,
                api_stripe_product_uuid: Uuid,
                product_id: String,
                product_name: String,
                create_ts: DateTimeUtc,
                description: Option<String>,
            };
            SELECT
                s.api_stripe_subscription_uuid,
                s.subscription_id,
                s.api_stripe_customer_uuid,
                c.customer_id,
                c.name AS customer_name,
                s.api_stripe_product_uuid,
                pr.product_id,
                pr.name AS product_name,
                s.create_ts,
                s.description
            FROM
                api_stripe.subscription s
                JOIN api_stripe.customer c ON s.api_stripe_customer_uuid = c.api_stripe_customer_uuid
                JOIN api_stripe.product pr ON s.api_stripe_product_uuid = pr.api_stripe_product_uuid
            WHERE true
                AND ($keyword::text IS NULL OR s.description ILIKE "%" || $keyword::text || "%")
            ORDER BY
                s.create_ts DESC
        )
        .await?;

        let subscriptions = rows
            .into_iter()
            .map(|row| Subscription {
                api_stripe_subscription_uuid: row.api_stripe_subscription_uuid,
                subscription_id: row.subscription_id,
                api_stripe_customer_uuid: row.api_stripe_customer_uuid,
                customer_id: row.customer_id,
                customer_name: row.customer_name,
                api_stripe_product_uuid: row.api_stripe_product_uuid,
                product_id: row.product_id,
                product_name: row.product_name,
                create_ts: row.create_ts,
                description: row.description,
            })
            .collect();

        Ok(Output { subscriptions })
    }
}

#[approck::api]
pub mod list {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub api_stripe_customer_uuid: Option<Uuid>,
        pub api_stripe_subscription_uuid: Option<Uuid>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub invoice_items: Vec<InvoiceItem>,
    }
    #[granite::gtype(ApiOutput)]
    pub struct InvoiceItem {
        pub api_stripe_invoice_item_uuid: Uuid,
        pub invoice_item_id: String,
        pub api_stripe_customer_uuid: Uuid,
        pub customer_id: String,
        pub customer_name: String,
        pub api_stripe_product_uuid: Uuid,
        pub product_id: String,
        pub product_name: String,
        pub api_stripe_price_uuid: Uuid,
        pub price_id: String,
        pub price_name: String,
        pub api_stripe_subscription_uuid: Option<Uuid>,
        pub subscription_id: Option<String>,
        pub subscription_name: Option<String>,
        pub amount: i64,
        pub description: Option<String>,
        pub create_ts: DateTimeUtc,
    }

    impl InvoiceItem {
        pub fn amount_html(&self) -> String {
            let amount_dollars =
                granite::Decimal::new(self.amount, 0) / granite::Decimal::new(100, 0);
            bux::format_currency_us_2(amount_dollars)
        }

        pub fn description_with_date(&self) -> String {
            format!(
                "{} - {}",
                self.description.as_deref().unwrap_or(""),
                self.create_ts.format("%b %d, %Y")
            )
        }
    }

    pub async fn call(app: App, identity: Identity, _input: Input) -> Result<Output> {
        if !identity.is_admin() {
            return_authorization_error!("Only admins can access the Stripe console");
        }

        let dbcx = app.postgres_dbcx().await?;
        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $api_stripe_customer_uuid: &_input.api_stripe_customer_uuid,
                $api_stripe_subscription_uuid: &_input.api_stripe_subscription_uuid,
            };
            row = {
                api_stripe_invoice_item_uuid: Uuid,
                invoice_item_id: String,
                api_stripe_customer_uuid: Uuid,
                customer_id: String,
                customer_name: String,
                api_stripe_product_uuid: Uuid,
                product_id: String,
                product_name: String,
                api_stripe_price_uuid: Uuid,
                price_id: String,
                price_name: String,
                api_stripe_subscription_uuid: Option<Uuid>,
                subscription_id: Option<String>,
                subscription_name: Option<String>,
                amount: i64,
                description: Option<String>,
                create_ts: DateTimeUtc,
            };
            SELECT
                ii.api_stripe_invoice_item_uuid,
                ii.invoice_item_id,
                ii.api_stripe_customer_uuid,
                c.customer_id,
                c.name AS customer_name,
                ii.api_stripe_product_uuid,
                pr.product_id,
                pr.name AS product_name,
                ii.api_stripe_price_uuid,
                p.price_id,
                p.name AS price_name,
                ii.api_stripe_subscription_uuid,
                s.subscription_id,
                s.description AS subscription_name,
                ii.amount AS amount,
                ii.description,
                ii.create_ts
            FROM
                api_stripe.invoice_item ii
                JOIN api_stripe.customer c ON ii.api_stripe_customer_uuid = c.api_stripe_customer_uuid
                JOIN api_stripe.product pr ON ii.api_stripe_product_uuid = pr.api_stripe_product_uuid
                JOIN api_stripe.price p ON ii.api_stripe_price_uuid = p.api_stripe_price_uuid
                LEFT JOIN api_stripe.subscription s ON ii.api_stripe_subscription_uuid = s.api_stripe_subscription_uuid
            WHERE
                ($api_stripe_customer_uuid::uuid IS NULL OR ii.api_stripe_customer_uuid = $api_stripe_customer_uuid::uuid)
                AND ($api_stripe_subscription_uuid::uuid IS NULL OR ii.api_stripe_subscription_uuid = $api_stripe_subscription_uuid::uuid)
        )
        .await?;

        let invoice_items = rows
            .into_iter()
            .map(|row| InvoiceItem {
                api_stripe_invoice_item_uuid: row.api_stripe_invoice_item_uuid,
                invoice_item_id: row.invoice_item_id,
                api_stripe_customer_uuid: row.api_stripe_customer_uuid,
                customer_id: row.customer_id,
                customer_name: row.customer_name,
                api_stripe_product_uuid: row.api_stripe_product_uuid,
                product_id: row.product_id,
                product_name: row.product_name,
                api_stripe_price_uuid: row.api_stripe_price_uuid,
                price_id: row.price_id,
                price_name: row.price_name,
                api_stripe_subscription_uuid: row.api_stripe_subscription_uuid,
                subscription_id: row.subscription_id,
                subscription_name: row.subscription_name,
                amount: row.amount,
                description: row.description,
                create_ts: row.create_ts,
            })
            .collect();

        Ok(Output { invoice_items })
    }
}

#[approck::api]
pub mod summary {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {}

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub summary: Summary,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Summary {
        pub total_customers: i64,
        pub total_products: i64,
        pub total_subscriptions: i64,
    }

    pub async fn call(app: App, identity: Identity, _input: Input) -> Result<Output> {
        if !identity.is_admin() {
            return_authorization_error!("Only admins can access the Stripe console");
        }

        let dbcx = app.postgres_dbcx().await?;
        let row = granite::pg_row!(
            db = dbcx;
            args = {};
            row = {
                total_customers: i64,
                total_products: i64,
                total_subscriptions: i64,
            };
            SELECT
                COUNT(*) as total_customers,
                (SELECT COUNT(*) FROM api_stripe.product) as total_products,
                (SELECT COUNT(*) FROM api_stripe.subscription) as total_subscriptions
            FROM api_stripe.customer
        )
        .await?;

        let summary = Summary {
            total_customers: row.total_customers,
            total_products: row.total_products,
            total_subscriptions: row.total_subscriptions,
        };

        Ok(Output { summary })
    }
}

#[approck::api]
pub mod stripe_subscription_detail {
    use granite::Decimal;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub api_stripe_subscription_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub subscription: Subscription,
    }
    #[granite::gtype(ApiOutput)]
    pub struct Subscription {
        pub api_stripe_subscription_uuid: Uuid,
        pub subscription_id: String,
        pub api_stripe_customer_uuid: Uuid,
        pub customer_id: String,
        pub customer_name: String,
        pub api_stripe_product_uuid: Uuid,
        pub product_id: String,
        pub product_name: String,
        pub product_price: String,
        pub create_ts: DateTimeUtc,
        pub description: Option<String>,
        pub trial_period_days: i32,
    }

    // Price html
    impl Subscription {
        pub fn price_html(&self) -> String {
            // Try to parse the product_price as a Decimal
            if let Ok(amount) = self.product_price.parse::<Decimal>() {
                // Convert from cents to dollars and format
                let amount_dollars = amount;
                bux::format_currency_us_2(amount_dollars)
            } else {
                // Fallback if parsing fails
                format!("${}", self.product_price)
            }
        }
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.is_admin() {
            return_authorization_error!("Only admins can access the Stripe console");
        }

        let dbcx = app.postgres_dbcx().await?;
        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $api_stripe_subscription_uuid: &input.api_stripe_subscription_uuid,
            };
            row = {
                api_stripe_subscription_uuid: Uuid,
                subscription_id: String,
                api_stripe_customer_uuid: Uuid,
                customer_id: String,
                customer_name: String,
                api_stripe_product_uuid: Uuid,
                product_id: String,
                product_name: String,
                product_price: String,
                create_ts: DateTimeUtc,
                description: Option<String>,
                trial_period_days: i32,
            };
            SELECT
                s.api_stripe_subscription_uuid,
                s.subscription_id,
                s.api_stripe_customer_uuid,
                c.customer_id,
                c.name AS customer_name,
                s.api_stripe_product_uuid,
                pr.product_id,
                pr.name AS product_name,
                (
                    SELECT unit_amount
                    FROM api_stripe.price
                    WHERE api_stripe_product_uuid = pr.api_stripe_product_uuid
                    LIMIT 1
                ) AS product_price,
                s.create_ts,
                s.description,
                s.trial_period_days
            FROM
                api_stripe.subscription s
                JOIN api_stripe.customer c ON s.api_stripe_customer_uuid = c.api_stripe_customer_uuid
                JOIN api_stripe.product pr ON s.api_stripe_product_uuid = pr.api_stripe_product_uuid
            WHERE
                s.api_stripe_subscription_uuid = $api_stripe_subscription_uuid::uuid
        )
        .await?;

        Ok(Output {
            subscription: Subscription {
                api_stripe_subscription_uuid: row.api_stripe_subscription_uuid,
                subscription_id: row.subscription_id,
                api_stripe_customer_uuid: row.api_stripe_customer_uuid,
                customer_id: row.customer_id,
                customer_name: row.customer_name,
                api_stripe_product_uuid: row.api_stripe_product_uuid,
                product_id: row.product_id,
                product_name: row.product_name,
                product_price: row.product_price,
                create_ts: row.create_ts,
                description: row.description,
                trial_period_days: row.trial_period_days,
            },
        })
    }
}

#[approck::api]
pub mod list {
    use granite::Uuid;
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub keyword: Option<String>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub customers: Vec<Customer>,
    }
    #[granite::gtype(ApiOutput)]
    pub struct Customer {
        pub api_stripe_customer_uuid: Uuid,
        pub customer_id: String,
        pub create_ts: DateTimeUtc,
        pub name: String,
        pub email: Option<String>,
        pub phone: Option<String>,
        pub description: Option<String>,
    }

    pub fn ml_customer(api_stripe_customer_uuid: Uuid) -> String {
        format!("/admin/stripe/customer/{api_stripe_customer_uuid}/")
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.is_admin() {
            return_authorization_error!("Only admins can access the Stripe console");
        }

        let dbcx = app.postgres_dbcx().await?;
        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $keyword: &input.keyword,
            };
            row = {
                api_stripe_customer_uuid: Uuid,
                customer_id: String,
                create_ts: DateTimeUtc,
                name: String,
                email: Option<String>,
                phone: Option<String>,
                description: Option<String>,
            };
            SELECT
                api_stripe_customer_uuid,
                customer_id,
                create_ts,
                name,
                email,
                phone,
                description
            FROM
                api_stripe.customer
            WHERE true
                AND ($keyword::text IS NULL OR name ILIKE "%" || $keyword::text || "%")
            ORDER BY
                name
        )
        .await?;

        let customers = rows
            .into_iter()
            .map(|row| Customer {
                api_stripe_customer_uuid: row.api_stripe_customer_uuid,
                customer_id: row.customer_id,
                create_ts: row.create_ts,
                name: row.name,
                email: row.email,
                phone: row.phone,
                description: row.description,
            })
            .collect();

        Ok(Output { customers })
    }
}

use crate::core::stripeapi::create_invoice_item::{CreateInvoiceItem, ItemPricing};
use crate::core::stripeapi::types::InvoiceItem;
use approck_postgres::DBCX;
use granite;

/// Creates an invoice item for a customer
///
/// # Arguments
/// * `dbcx` - Database connection
/// * `secret_key` - The Stripe API secret key
/// * `customer_id` - The Stripe customer ID
/// * `subscription_id` - The Stripe subscription ID (can be empty for standalone invoice items)
/// * `price_id` - The price ID
/// * `custom_name` - The custom name to display on the invoice
/// * `quantity` - Optional quantity for the invoice item
///
/// # Returns
/// * `Result<InvoiceItem, granite::Error>` - The created invoice item or an error
pub async fn create_invoice_item(
    dbcx: &mut DBCX<'_>,
    secret_key: &str,
    customer_id: String,
    subscription_id: Option<String>,
    price_id: String,
    custom_name: String,
    quantity: Option<i32>,
) -> Result<InvoiceItem, granite::Error> {
    approck::info!("Creating invoice item with price: {price_id}");
    {
        let dbtx = dbcx.transaction().await?;

        // Create the invoice item
        let mut invoice_item = CreateInvoiceItem {
            customer: customer_id.clone(),
            pricing: ItemPricing {
                price: price_id.clone(),
            },
            quantity,
            subscription: None, // Default to None,
            product: None,      // Default to None
            description: Some(custom_name),
        };

        // Only set subscription if it's Some and not empty
        if let Some(sub_id) = &subscription_id {
            if !sub_id.is_empty() {
                invoice_item.subscription = Some(sub_id.clone());
            }
        }

        // Create the invoice item in Stripe
        let stripe_invoice_item = crate::core::stripeapi::create_invoice_item::create_invoice_item(
            secret_key,
            &invoice_item,
        )
        .await?;
        approck::info!(
            "Stripe invoice item created with ID: {}",
            stripe_invoice_item.id
        );
        approck::info!("Invoice item: {:?}", stripe_invoice_item);

        /*
         Invoice item: InvoiceItem { id: "ii_1RnPy8H1mpXpEHoKVBA5LlvR", object: "invoiceitem", amount: 65, currency: "usd", customer: "cus_SirgrMrzlrZxlW", date: 1753128352, description: Some("Soft Credit Pull for Joe Smith"), discountable: true, invoice: None, livemode: false, price: Some(Price { id: "price_1Reh7wH1mpXpEHoKqtT8Wx5G", object: "price", active: true, currency: "usd", product: "prod_SZqprh64se9eUw", unit_amount: Some(65), unit_amount_decimal: Some("65"), nickname: Some("Soft Credit Pull for Joe Smith"), created: 1751049356, livemode: false, additional_properties: {"metadata": Object {}, "custom_unit_amount": Null, "lookup_key": Null, "transform_quantity": Null, "type": String("one_time"), "recurring": Null, "billing_scheme": String("per_unit"), "tiers_mode": Null, "tax_behavior": String("unspecified")} }), proration: false, quantity: Some(1), subscription: Some("sub_1RnPxYH1mpXpEHoK9ZjTJLIv"), additional_properties: {"parent": Object {"subscription_details": Object {"subscription": String("sub_1RnPxYH1mpXpEHoK9ZjTJLIv")}, "type": String("subscription_details")}, "metadata": Object {}, "pricing": Object {"price_details": Object {"price": String("price_1Reh7wH1mpXpEHoKqtT8Wx5G"), "product": String("prod_SZqprh64se9eUw")}, "type": String("price_details"), "unit_amount_decimal": String("65")}, "plan": Null, "unit_amount": Number(65), "unit_amount_decimal": String("65"), "period": Object {"end": Number(1753128352), "start": Number(1753128352)}, "tax_rates": Array [], "discounts": Array [], "test_clock": Null} }
        */

        let api_stripe_customer_uuid = granite::pg_row!(
            db = dbtx;
            args = {
                $customer_id: &customer_id,
            };
            row = {
                api_stripe_customer_uuid: Uuid,
            };
            SELECT
                api_stripe_customer_uuid
            FROM
                api_stripe.customer
            WHERE
                customer_id = $customer_id
        )
        .await?
        .api_stripe_customer_uuid;

        let api_stripe_price_uuid = granite::pg_row!(
            db = dbtx;
            args = {
                $price_id: &price_id,
            };
            row = {
                api_stripe_price_uuid: Uuid,
            };
            SELECT
                api_stripe_price_uuid
            FROM
                api_stripe.price
            WHERE
                price_id = $price_id
        )
        .await?
        .api_stripe_price_uuid;

        // Get the api_stripe_subscription_uuid if subscription_id is provided
        let api_stripe_subscription_uuid = if let Some(sub_id) = &subscription_id {
            if !sub_id.is_empty() {
                let subscription_row = granite::pg_row!(
                    db = dbtx;
                    args = {
                        $subscription_id: sub_id,
                    };
                    row = {
                        api_stripe_subscription_uuid: Uuid,
                    };
                    SELECT
                        api_stripe_subscription_uuid
                    FROM
                        api_stripe.subscription
                    WHERE
                        subscription_id = $subscription_id
                )
                .await?;
                Some(subscription_row.api_stripe_subscription_uuid)
            } else {
                None
            }
        } else {
            None
        };

        let product_id = stripe_invoice_item
            .price
            .as_ref()
            .map(|price| price.product.clone())
            .unwrap_or_default();

        let api_stripe_product_uuid = granite::pg_row!(
            db = dbtx;
            args = {
                $product_id: &product_id,
            };
            row = {
                api_stripe_product_uuid: Uuid,
            };
            SELECT
                api_stripe_product_uuid
            FROM
                api_stripe.product
            WHERE
                product_id = $product_id
        )
        .await?
        .api_stripe_product_uuid;

        // Insert the invoice item record
        granite::pg_execute!(
            db = dbtx;
            args = {
                $invoice_item_id: &stripe_invoice_item.id,
                $api_stripe_customer_uuid: &api_stripe_customer_uuid,
                $api_stripe_price_uuid: &api_stripe_price_uuid,
                $api_stripe_subscription_uuid: &api_stripe_subscription_uuid,
                $api_stripe_product_uuid: &api_stripe_product_uuid,
                $amount: &stripe_invoice_item.amount,
                $description: &stripe_invoice_item.description,
            };
            INSERT INTO
                api_stripe.invoice_item
            (
                invoice_item_id,
                api_stripe_customer_uuid,
                api_stripe_price_uuid,
                api_stripe_subscription_uuid,
                api_stripe_product_uuid,
                amount,
                description
            )
            VALUES
            (
                $invoice_item_id,
                $api_stripe_customer_uuid,
                $api_stripe_price_uuid,
                $api_stripe_subscription_uuid,
                $api_stripe_product_uuid,
                $amount,
                $description
            )
        )
        .await?;

        dbtx.commit().await?;

        approck::info!("Invoice item created successfully");

        Ok(stripe_invoice_item)
    }
}

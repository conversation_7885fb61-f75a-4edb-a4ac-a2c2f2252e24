use approck_postgres::DBCX;
use granite::Error;
use uuid::Uuid;

/// Creates a subscription record in the database
///
/// # Arguments
/// * `dbcx` - Database connection
/// * `subscription_id` - Stripe subscription ID
/// * `stripe_customer_id` - Stripe customer ID
///
/// # Returns
/// * `Result<Uuid, Error>` - The api_stripe_subscription_uuid or an error
pub async fn create_subscription_record(
    dbcx: &mut DBCX<'_>,
    subscription_id: &str,
    stripe_customer_id: &str,
    stripe_product_id: &str,
    description: &str,
    trial_period_days: i32,
) -> Result<Uuid, Error> {
    // Query to get the api_stripe_customer_uuid from the customer_id
    let customer_row = granite::pg_row!(
        db = dbcx;
        args = {
            $customer_id: &stripe_customer_id,
        };
        row = {
            api_stripe_customer_uuid: Uuid,
        };
        SELECT
            api_stripe_customer_uuid
        FROM
            api_stripe.customer
        WHERE
            customer_id = $customer_id
    )
    .await?;

    let api_stripe_customer_uuid = customer_row.api_stripe_customer_uuid;
    approck::info!("api_stripe_customer_uuid: {api_stripe_customer_uuid:?}");
    let api_stripe_product_uuid = granite::pg_row!(
        db = dbcx;
        args = {
            $product_id: &stripe_product_id,
        };
        row = {
            api_stripe_product_uuid: Uuid,
        };
        SELECT
            api_stripe_product_uuid
        FROM
            api_stripe.product
        WHERE
            product_id = $product_id
    )
    .await?
    .api_stripe_product_uuid;

    // Insert the subscription record and return the api_stripe_subscription_uuid
    let subscription_row = granite::pg_row!(
        db = dbcx;
        args = {
            $subscription_id: &subscription_id,
            $api_stripe_customer_uuid: &api_stripe_customer_uuid,
            $stripe_customer_id: &stripe_customer_id,
            $api_stripe_product_uuid: &api_stripe_product_uuid,
            $description: &description,
            $trial_period_days: &trial_period_days,
        };
        row = {
            api_stripe_subscription_uuid: Uuid,
        };

        INSERT INTO
            api_stripe.subscription
        (
            subscription_id,
            api_stripe_customer_uuid,
            customer_id,
            api_stripe_product_uuid,
            description,
            trial_period_days
        )
        VALUES
        (
            $subscription_id,
            $api_stripe_customer_uuid,
            $stripe_customer_id,
            $api_stripe_product_uuid,
            $description,
            $trial_period_days
        )
        RETURNING api_stripe_subscription_uuid
    )
    .await?;

    let api_stripe_subscription_uuid = subscription_row.api_stripe_subscription_uuid;
    approck::info!("api_stripe_subscription_uuid: {api_stripe_subscription_uuid:?}");

    Ok(api_stripe_subscription_uuid)
}

// clear logging with approck::info

//start transaction block for db operation
//insert db record into api_stripe.customer
//call stripe to create customer api/core/stripeapi/create_customer.rs
//update db record with stripe customer id
//commit

use approck_postgres::DBCX;

pub async fn create_customer(
    dbcx: &mut DBCX<'_>,
    secret_key: &str,
    customer_data: &crate::core::stripeapi::create_customer::CreateCustomer,
) -> Result<crate::core::stripeapi::types::Customer, granite::Error> {
    // Start transaction
    {
        let dbtx = dbcx.transaction().await?;

        approck::info!("Creating Stripe customer");

        let row = granite::pg_row!(
            db = dbtx;  // Use the transaction
            args = {
                $name: &customer_data.name,
                $email: &customer_data.email,
            };
            row = {
                api_stripe_customer_uuid: Uuid,
            };

            INSERT INTO
                api_stripe.customer
                (
                    name,
                    email
                )
            VALUES
                (
                    $name,
                    $email
                )
            RETURNING
                api_stripe_customer_uuid
        )
        .await?;

        let api_stripe_customer_uuid = row.api_stripe_customer_uuid;
        approck::info!(
            "Created database record with UUID: {}",
            api_stripe_customer_uuid
        );

        // Create a new CreateCustomer instance with just name and email
        let customer_request = crate::core::stripeapi::create_customer::CreateCustomer {
            name: customer_data.name.clone(),
            email: customer_data.email.clone(),
            ..Default::default()
        };
        approck::info!("Calling Stripe API to create customer");

        let stripe_customer =
            crate::core::stripeapi::create_customer::create_customer(secret_key, &customer_request)
                .await?;
        approck::info!("Stripe customer created with ID: {}", stripe_customer.id);

        // Update database record with Stripe customer ID
        approck::info!("Updating database record with Stripe customer ID");
        granite::pg_execute!(
            db = dbtx;  // Use the transaction
            args = {
                $api_stripe_customer_uuid: &api_stripe_customer_uuid,
                $customer_id: &stripe_customer.id,
            };
            UPDATE api_stripe.customer
            SET
                customer_id = $customer_id
            WHERE
                api_stripe_customer_uuid = $api_stripe_customer_uuid
        )
        .await?;
        approck::info!("Database record updated successfully");

        // Commit the transaction
        dbtx.commit().await?;
        approck::info!("Transaction committed successfully");

        Ok(stripe_customer)
    }
}

use crate::core::stripeapi::types::{CheckoutSession, Error, StripeError};
use reqwest::header::{AUTHORIZATION, CONTENT_TYPE, HeaderMap, HeaderValue};
use std::collections::HashMap;

#[derive(serde::Serialize, Debug, Default)]
pub struct CreateCheckoutSession {
    pub customer: String,
    pub success_url: String,
    pub cancel_url: String,
    pub mode: String,
    pub line_items: Option<Vec<LineItem>>,
    pub metadata: Option<HashMap<String, String>>,
    pub trial_period_days: Option<i32>,
}

#[derive(serde::Serialize, Debug)]
pub struct LineItem {
    pub price: String,
    pub quantity: Option<i32>,
}

impl crate::ModuleStruct {
    pub async fn checkout_session_create2(
        &self,
        session: &CreateCheckoutSession,
    ) -> Result<CheckoutSession, Error> {
        let api_key = &self.secret_key;
        checkout_session_create(api_key, session).await
    }
}

pub async fn checkout_session_create(
    secret_key: &str,
    session: &CreateCheckoutSession,
) -> Result<CheckoutSession, Error> {
    let url = "https://api.stripe.com/v1/checkout/sessions";

    // Create auth header with API key
    let mut headers = HeaderMap::new();
    let auth_value = format!("Bearer {secret_key}");
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&auth_value)
            .map_err(|e| Error::OtherError(format!("Invalid authorization header: {e}")))?,
    );
    headers.insert(
        CONTENT_TYPE,
        HeaderValue::from_static("application/x-www-form-urlencoded"),
    );

    // Convert the session to a form-encoded map manually
    let mut form = std::collections::HashMap::new();
    form.insert("customer", session.customer.clone());
    form.insert("success_url", session.success_url.clone());
    form.insert("cancel_url", session.cancel_url.clone());
    form.insert("mode", session.mode.clone());

    // Add trial_period_days if present
    if let Some(trial_days) = session.trial_period_days {
        let trial_key = "subscription_data[trial_period_days]";
        form.insert(trial_key, trial_days.to_string());
    }

    // Add line items if present
    if let Some(line_items) = &session.line_items {
        for (i, item) in line_items.iter().enumerate() {
            let price_key = format!("line_items[{i}][price]");
            let quantity_key = format!("line_items[{i}][quantity]");
            // Convert String to &str with a temporary variable to extend lifetime
            let price_key_str = Box::leak(price_key.into_boxed_str());
            let quantity_key_str = Box::leak(quantity_key.into_boxed_str());
            form.insert(price_key_str, item.price.clone());
            if let Some(quantity) = item.quantity {
                form.insert(quantity_key_str, quantity.to_string());
            }
        }
    }

    // Add metadata if present
    if let Some(metadata) = &session.metadata {
        for (key, value) in metadata {
            let metadata_key = format!("metadata[{key}]");
            let metadata_key_str = Box::leak(metadata_key.into_boxed_str());
            form.insert(metadata_key_str, value.clone());
        }
    }

    // Send request to Stripe API
    let client = reqwest::Client::new();
    let response = client
        .post(url)
        .headers(headers)
        .form(&form) // Use our manually constructed form
        .send()
        .await
        .map_err(|e| Error::RequestError {
            url: url.to_string(),
            error: e.to_string(),
        })?;

    // Check if the response is successful
    if !response.status().is_success() {
        let text = response.text().await.map_err(|e| Error::ResponseError {
            url: url.to_string(),
            error: e.to_string(),
        })?;
        approck::error!("Stripe API returned error: {}", text);
        let stripe_error: StripeError =
            serde_json::from_str(&text).map_err(|e| Error::DecodingError {
                input: text,
                error: e.to_string(),
            })?;
        return Err(Error::StripeError(Box::new(stripe_error)));
    }

    let text = response.text().await.map_err(|e| Error::ResponseError {
        url: url.to_string(),
        error: e.to_string(),
    })?;

    // Parse response
    let session_response: CheckoutSession =
        serde_json::from_str(&text).map_err(|e| Error::DecodingError {
            input: text,
            error: e.to_string(),
        })?;

    Ok(session_response)
}

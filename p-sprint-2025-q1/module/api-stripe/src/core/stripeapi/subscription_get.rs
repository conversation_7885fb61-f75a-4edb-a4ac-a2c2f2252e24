use crate::core::stripeapi::types::{<PERSON><PERSON><PERSON>, StripeError, Subscription};
use reqwest::header::{AUTHORIZATION, CONTENT_TYPE, HeaderMap, HeaderValue};

#[derive(serde::Serialize, Debug, Default)]
pub struct GetSubscription {
    pub subscription_id: String,
}

impl crate::ModuleStruct {
    pub async fn subscription_get(
        &self,
        subscription_id: &str,
    ) -> granite::Triad<Subscription, Error> {
        let api_key = &self.secret_key;
        match subscription_get(api_key, subscription_id).await {
            Ok(subscription) => granite::Triad::Ok(subscription),
            Err(e) => e.into(),
        }
    }
}

async fn subscription_get(api_key: &str, subscription_id: &str) -> Result<Subscription, Error> {
    let client = reqwest::Client::new();
    let url = format!("https://api.stripe.com/v1/subscriptions/{subscription_id}");

    let mut headers = HeaderMap::new();
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&format!("Bearer {api_key}"))
            .map_err(|e| Error::OtherError(format!("Invalid authorization header: {e}")))?,
    );
    headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));

    let response = client
        .get(&url)
        .headers(headers)
        .send()
        .await
        .map_err(|e| Error::RequestError {
            url: url.to_string(),
            error: e.to_string(),
        })?;

    if !response.status().is_success() {
        let text = response.text().await.map_err(|e| Error::ResponseError {
            url: url.to_string(),
            error: e.to_string(),
        })?;

        let stripe_error: StripeError =
            serde_json::from_str(&text).map_err(|e| Error::DecodingError {
                input: text,
                error: e.to_string(),
            })?;

        return Err(Error::StripeError(Box::new(stripe_error)));
    }

    let text = response.text().await.map_err(|e| Error::ResponseError {
        url: url.to_string(),
        error: e.to_string(),
    })?;

    let subscription: Subscription =
        serde_json::from_str(&text).map_err(|e| Error::DecodingError {
            input: text,
            error: e.to_string(),
        })?;

    Ok(subscription)
}

use crate::core::stripeapi::types::{CheckoutSession, Error, StripeError};
use reqwest::header::{AUTHORIZATION, CONTENT_TYPE, HeaderMap, HeaderValue};

#[derive(serde::Serialize, Debug, Default)]
pub struct GetCheckoutSession {
    pub session_id: String,
}

impl crate::ModuleStruct {
    pub async fn checkout_session_get2(
        &self,
        session_id: &str,
    ) -> granite::Triad<CheckoutSession, Error> {
        let api_key = &self.secret_key;
        match checkout_session_get(api_key, session_id).await {
            Ok(session) => granite::Triad::Ok(session),
            Err(e) => e.into(),
        }
    }
}

pub async fn checkout_session_get(
    secret_key: &str,
    session_id: &str,
) -> Result<CheckoutSession, Error> {
    let url = format!("https://api.stripe.com/v1/checkout/sessions/{session_id}");

    // Create auth header with API key
    let mut headers = HeaderMap::new();
    let auth_value = format!("Bearer {secret_key}");
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&auth_value)
            .map_err(|e| Error::OtherError(format!("Invalid authorization header: {e}")))?,
    );
    headers.insert(
        CONTENT_TYPE,
        HeaderValue::from_static("application/x-www-form-urlencoded"),
    );

    // Send request to Stripe API
    let client = reqwest::Client::new();
    let response = client
        .get(url.clone())
        .headers(headers)
        .send()
        .await
        .map_err(|e| Error::RequestError {
            url: url.clone(),
            error: e.to_string(),
        })?;

    // Check if the response is successful
    if !response.status().is_success() {
        let text = response.text().await.map_err(|e| Error::ResponseError {
            url: url.to_string(),
            error: e.to_string(),
        })?;

        let stripe_error: StripeError =
            serde_json::from_str(&text).map_err(|e| Error::DecodingError {
                input: text,
                error: e.to_string(),
            })?;

        return Err(Error::StripeError(Box::new(stripe_error)));
    }

    let text = response.text().await.map_err(|e| Error::ResponseError {
        url: url.to_string(),
        error: e.to_string(),
    })?;

    // Parse response
    let session_response: CheckoutSession =
        serde_json::from_str(&text).map_err(|e| Error::DecodingError {
            input: text,
            error: e.to_string(),
        })?;

    Ok(session_response)
}

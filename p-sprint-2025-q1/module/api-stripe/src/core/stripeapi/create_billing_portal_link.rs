use crate::core::stripeapi::types::{BillingPortalSession, <PERSON>rror, StripeError};
use reqwest::header::{AUTHORIZATION, CONTENT_TYPE, HeaderMap, HeaderValue};
use std::collections::HashMap;

pub struct CreateBillingPortalLink {
    pub customer_id: String,
    pub return_url: String,
    pub configuration_id: Option<String>,
}

impl crate::ModuleStruct {
    pub async fn create_billing_portal_link2(
        &self,
        billing_portal_link: &CreateBillingPortalLink,
    ) -> granite::Triad<BillingPortalSession, Error> {
        let api_key = &self.secret_key;
        match create_billing_portal_link2(api_key, billing_portal_link).await {
            Ok(session) => granite::Triad::Ok(session),
            Err(e) => e.into(),
        }
    }
}

pub async fn create_billing_portal_link2(
    secret_key: &str,
    billing_portal_link: &CreateBillingPortalLink,
) -> Result<BillingPortalSession, Error> {
    let url = "https://api.stripe.com/v1/billing_portal/sessions";

    // Create auth header with API key
    let mut headers = HeaderMap::new();
    let auth_value = format!("Bearer {secret_key}");
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&auth_value)
            .map_err(|e| Error::OtherError(format!("Invalid authorization header: {e}")))?,
    );
    headers.insert(
        CONTENT_TYPE,
        HeaderValue::from_static("application/x-www-form-urlencoded"),
    );

    // Create form parameters
    let mut params = HashMap::new();
    params.insert("customer", billing_portal_link.customer_id.to_string());
    params.insert("return_url", billing_portal_link.return_url.to_string());

    // Only add configuration if provided
    if let Some(config_id) = &billing_portal_link.configuration_id {
        params.insert("configuration", config_id.to_string());
    }

    // Send request to Stripe API
    let client = reqwest::Client::new();
    let response = client
        .post(url)
        .headers(headers)
        .form(&params)
        .send()
        .await
        .map_err(|e| Error::RequestError {
            url: url.to_string(),
            error: e.to_string(),
        })?;

    // Check if the response is successful
    if !response.status().is_success() {
        let text = response.text().await.map_err(|e| Error::ResponseError {
            url: url.to_string(),
            error: e.to_string(),
        })?;

        let stripe_error: StripeError =
            serde_json::from_str(&text).map_err(|e| Error::DecodingError {
                input: text,
                error: e.to_string(),
            })?;

        return Err(Error::StripeError(Box::new(stripe_error)));
    }

    let text = response.text().await.map_err(|e| Error::ResponseError {
        url: url.to_string(),
        error: e.to_string(),
    })?;

    // Parse response
    let portal_session: BillingPortalSession =
        serde_json::from_str(&text).map_err(|e| Error::DecodingError {
            input: text,
            error: e.to_string(),
        })?;

    Ok(portal_session)
}

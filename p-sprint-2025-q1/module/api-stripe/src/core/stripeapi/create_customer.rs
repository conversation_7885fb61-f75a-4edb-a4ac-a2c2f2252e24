use crate::core::stripeapi::types::{Address, Customer, Error, Shipping, StripeError};
use reqwest::header::{AUTHORIZATION, CONTENT_TYPE, HeaderMap, HeaderValue};

/// Parameters for creating a Stripe customer
/// https://docs.stripe.com/api/customers/create
#[derive(serde::Serialize, Debug, Default)]
pub struct CreateCustomer {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub address: Option<Address>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub email: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub metadata: Option<std::collections::HashMap<String, String>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub name: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub payment_method: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub phone: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub shipping: Option<Shipping>,
}

impl crate::ModuleStruct {
    pub async fn create_customer2(
        &self,
        customer: &CreateCustomer,
    ) -> granite::Triad<Customer, Error> {
        let api_key = &self.secret_key;
        match create_customer(api_key, customer).await {
            Ok(customer) => granite::Triad::Ok(customer),
            Err(e) => e.into(),
        }
    }
}

pub async fn create_customer(
    secret_key: &str,
    customer: &CreateCustomer,
) -> Result<Customer, Error> {
    let url = "https://api.stripe.com/v1/customers";

    // Create auth header with API key
    let mut headers = HeaderMap::new();
    let auth_value = format!("Bearer {secret_key}");
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&auth_value)
            .map_err(|e| Error::OtherError(format!("Invalid authorization header: {e}")))?,
    );
    headers.insert(
        CONTENT_TYPE,
        HeaderValue::from_static("application/x-www-form-urlencoded"),
    );

    // Send request to Stripe API
    let client = reqwest::Client::new();
    let response = client
        .post(url)
        .headers(headers)
        .form(&customer)
        .send()
        .await
        .map_err(|e| Error::RequestError {
            url: url.to_string(),
            error: e.to_string(),
        })?;

    // Check if the response is successful
    if !response.status().is_success() {
        let text = response.text().await.map_err(|e| Error::ResponseError {
            url: url.to_string(),
            error: e.to_string(),
        })?;

        let stripe_error: StripeError =
            serde_json::from_str(&text).map_err(|e| Error::DecodingError {
                input: text,
                error: e.to_string(),
            })?;

        return Err(Error::StripeError(Box::new(stripe_error)));
    }

    let text = response.text().await.map_err(|e| Error::ResponseError {
        url: url.to_string(),
        error: e.to_string(),
    })?;

    // Parse response
    let customer_response: Customer =
        serde_json::from_str(&text).map_err(|e| Error::DecodingError {
            input: text,
            error: e.to_string(),
        })?;

    Ok(customer_response)
}

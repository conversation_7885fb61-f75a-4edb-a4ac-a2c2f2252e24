use crate::core::stripeapi::types::{Error, InvoiceItem, StripeError};
use reqwest::header::{AUTHORIZATION, CONTENT_TYPE, HeaderMap, HeaderValue};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Default)]
pub struct ItemPricing {
    pub price: String,
}
#[derive(Debug, Serialize, Deserialize, Default)]
pub struct CreateInvoiceItem {
    pub customer: String,
    pub pricing: ItemPricing,
    pub quantity: Option<i32>,
    pub subscription: Option<String>,
    pub product: Option<String>,
    pub description: Option<String>,
}

impl crate::ModuleStruct {
    pub async fn create_invoice_item2(
        &self,
        invoice_item: &CreateInvoiceItem,
    ) -> Result<InvoiceItem, Error> {
        let api_key = &self.secret_key;
        create_invoice_item(api_key, invoice_item).await
    }
}

//https://docs.stripe.com/billing/invoices/subscription#adding-upcoming-invoice-items
pub async fn create_invoice_item(
    secret_key: &str,
    invoice_item: &CreateInvoiceItem,
) -> Result<InvoiceItem, Error> {
    let url = "https://api.stripe.com/v1/invoiceitems";

    // Create auth header with API key
    let mut headers = HeaderMap::new();
    let auth_value = format!("Bearer {secret_key}");
    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&auth_value)
            .map_err(|e| Error::OtherError(format!("Invalid authorization header: {e}")))?,
    );
    headers.insert(
        CONTENT_TYPE,
        HeaderValue::from_static("application/x-www-form-urlencoded"),
    );
    //create form parameters
    let mut params = std::collections::HashMap::new();
    params.insert("customer", invoice_item.customer.to_string());
    //pricing={"price": "price_1MtGUsLkdIwHu7ix1be5Ljaj"},
    params.insert("price", invoice_item.pricing.price.to_string());
    if let Some(quantity) = invoice_item.quantity {
        params.insert("quantity", quantity.to_string());
    }
    if let Some(subscription) = &invoice_item.subscription {
        params.insert("subscription", subscription.to_string());
    }
    if let Some(product) = &invoice_item.product {
        params.insert("product", product.to_string());
    }
    if let Some(description) = &invoice_item.description {
        params.insert("description", description.to_string());
    }

    // Send request to Stripe API
    let client = reqwest::Client::new();
    let response = client
        .post(url)
        .headers(headers)
        .form(&params)
        .send()
        .await
        .map_err(|e| Error::RequestError {
            url: url.to_string(),
            error: e.to_string(),
        })?;

    if !response.status().is_success() {
        let text = response.text().await.map_err(|e| Error::ResponseError {
            url: url.to_string(),
            error: e.to_string(),
        })?;

        let stripe_error: StripeError =
            serde_json::from_str(&text).map_err(|e| Error::DecodingError {
                input: text,
                error: e.to_string(),
            })?;

        return Err(Error::StripeError(Box::new(stripe_error)));
    }

    let text = response.text().await.map_err(|e| Error::ResponseError {
        url: url.to_string(),
        error: e.to_string(),
    })?;

    // Parse response
    let invoice_item_response: InvoiceItem =
        serde_json::from_str(&text).map_err(|e| Error::DecodingError {
            input: text,
            error: e.to_string(),
        })?;
    approck::info!("Invoice item created successfully");
    approck::info!("Invoice item: {:?}", invoice_item_response);
    Ok(invoice_item_response)
}

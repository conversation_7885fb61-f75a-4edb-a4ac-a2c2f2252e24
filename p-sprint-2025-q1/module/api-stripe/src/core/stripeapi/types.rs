#[derive(Debug)]
pub enum Error {
    RequestError { url: String, error: String },
    ResponseError { url: String, error: String },
    StripeError(Box<StripeError>),
    DecodingError { input: String, error: String },
    OtherError(String),
}

impl std::error::Error for Error {}

impl std::fmt::Display for Error {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{self:?}")
    }
}

impl From<Error> for granite::Triad<Customer, Error> {
    fn from(value: Error) -> Self {
        match value {
            Error::StripeError(stripe_error) => {
                if let Some(message) = &stripe_error.message {
                    let message = message.clone();
                    granite::Triad::Failure {
                        source: Error::StripeError(Box::new(*stripe_error)),
                        message: message.clone(),
                    }
                } else {
                    granite::Triad::Err(
                        granite::Error::new(granite::ErrorType::Unexpected)
                            .add_context(stripe_error),
                    )
                }
            }
            _ => granite::Triad::Err(
                granite::Error::new(granite::ErrorType::Unexpected).add_context(value),
            ),
        }
    }
}

impl From<Error> for granite::Triad<BillingPortalSession, Error> {
    fn from(value: Error) -> Self {
        match value {
            Error::StripeError(stripe_error) => {
                if let Some(message) = &stripe_error.message {
                    let message = message.clone();
                    granite::Triad::Failure {
                        source: Error::StripeError(Box::new(*stripe_error)),
                        message: message.clone(),
                    }
                } else {
                    granite::Triad::Err(
                        granite::Error::new(granite::ErrorType::Unexpected)
                            .add_context(stripe_error),
                    )
                }
            }
            _ => granite::Triad::Err(
                granite::Error::new(granite::ErrorType::Unexpected).add_context(value),
            ),
        }
    }
}

impl From<Error> for granite::Triad<CheckoutSession, Error> {
    fn from(value: Error) -> Self {
        match value {
            Error::StripeError(stripe_error) => {
                if let Some(message) = &stripe_error.message {
                    let message = message.clone();
                    granite::Triad::Failure {
                        source: Error::StripeError(Box::new(*stripe_error)),
                        message: message.clone(),
                    }
                } else {
                    granite::Triad::Err(
                        granite::Error::new(granite::ErrorType::Unexpected)
                            .add_context(stripe_error),
                    )
                }
            }
            _ => granite::Triad::Err(
                granite::Error::new(granite::ErrorType::Unexpected).add_context(value),
            ),
        }
    }
}

impl From<Error> for granite::Triad<InvoiceItem, Error> {
    fn from(value: Error) -> Self {
        match value {
            Error::StripeError(stripe_error) => {
                if let Some(message) = &stripe_error.message {
                    let message = message.clone();
                    granite::Triad::Failure {
                        source: Error::StripeError(Box::new(*stripe_error)),
                        message: message.clone(),
                    }
                } else {
                    granite::Triad::Err(
                        granite::Error::new(granite::ErrorType::Unexpected)
                            .add_context(stripe_error),
                    )
                }
            }
            _ => granite::Triad::Err(
                granite::Error::new(granite::ErrorType::Unexpected).add_context(value),
            ),
        }
    }
}

impl From<Error> for granite::Triad<Subscription, Error> {
    fn from(value: Error) -> Self {
        match value {
            Error::StripeError(stripe_error) => {
                if let Some(message) = &stripe_error.message {
                    let message = message.clone();
                    granite::Triad::Failure {
                        source: Error::StripeError(Box::new(*stripe_error)),
                        message: message.clone(),
                    }
                } else {
                    granite::Triad::Err(
                        granite::Error::new(granite::ErrorType::Unexpected)
                            .add_context(stripe_error),
                    )
                }
            }
            _ => granite::Triad::Err(
                granite::Error::new(granite::ErrorType::Unexpected).add_context(value),
            ),
        }
    }
}

#[derive(serde::Deserialize, Debug)]
pub struct StripeError {
    pub message: Option<String>,
    pub code: Option<String>,
    pub param: Option<String>,
    pub r#type: Option<String>,
    pub decline_code: Option<String>,
    pub charge: Option<String>,
    pub doc_url: Option<String>,
    pub network_advice_code: Option<String>,
    pub network_decline_code: Option<String>,
    pub payment_intent: Option<serde_json::Value>,
    pub payment_method: Option<serde_json::Value>,
    pub payment_method_type: Option<String>,
    pub request_log_url: Option<String>,
    pub setup_intent: Option<serde_json::Value>,
    pub source: Option<serde_json::Value>,
}

impl std::fmt::Display for StripeError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{self:?}")
    }
}

#[derive(serde::Serialize, serde::Deserialize, Debug, Default)]
pub struct Address {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub city: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub country: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub line1: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub line2: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub postal_code: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub state: Option<String>,
}

#[derive(serde::Serialize, serde::Deserialize, Debug, Default)]
pub struct Shipping {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub address: Option<Address>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub name: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub phone: Option<String>,
}

/// Response from creating a Stripe customer
/// https://docs.stripe.com/api/customers/create
#[derive(serde::Deserialize, Debug)]
pub struct Customer {
    pub id: String,
    pub object: String,
    pub email: Option<String>,
    pub name: Option<String>,
    pub phone: Option<String>,
    pub address: Option<Address>,
    pub description: Option<String>,
    pub created: i64,
    pub livemode: bool,
    pub metadata: Option<std::collections::HashMap<String, String>>,
    pub shipping: Option<Shipping>,
}

#[derive(serde::Deserialize, Debug)]
pub struct BillingPortalSession {
    pub id: String,
    pub object: String,
    pub url: String,
    pub created: i64,
    pub customer: String,
    pub livemode: bool,
    pub return_url: Option<String>,
    pub configuration: Option<String>,
}

#[derive(serde::Deserialize, Debug)]
pub struct CheckoutSession {
    pub id: String,
    pub object: String,
    pub url: Option<String>,
    pub created: i64,
    pub livemode: bool,
    pub payment_status: String,
    pub subscription: Option<String>,
    #[serde(flatten)] // Capture any additional fields from the Stripe API response
    pub additional_properties: std::collections::HashMap<String, serde_json::Value>,
}

impl CheckoutSession {
    /// Gets a string value from additional_properties if it exists and is a string
    pub fn get_string(&self, key: &str) -> Option<String> {
        self.additional_properties.get(key).and_then(|value| {
            if let serde_json::Value::String(s) = value {
                Some(s.clone())
            } else {
                None
            }
        })
    }
}

#[derive(serde::Deserialize, Debug)]
pub struct InvoiceItem {
    pub id: String,
    pub object: String,
    pub amount: i64,
    pub currency: String,
    pub customer: String,
    pub date: i64,
    pub description: Option<String>,
    pub discountable: bool,
    pub invoice: Option<String>,
    pub livemode: bool,
    pub price: Option<Price>,
    pub proration: bool,
    pub quantity: Option<i32>,
    pub subscription: Option<String>,
    #[serde(flatten)] // Capture any additional fields from the Stripe API response
    pub additional_properties: std::collections::HashMap<String, serde_json::Value>,
}
impl InvoiceItem {
    pub fn get_string(&self, key: &str) -> Option<String> {
        self.additional_properties.get(key).and_then(|value| {
            if let serde_json::Value::String(s) = value {
                Some(s.clone())
            } else {
                None
            }
        })
    }
}

// Define the Price struct to match Stripe's price object structure
#[derive(serde::Deserialize, Debug)]
pub struct Price {
    pub id: String,
    pub object: String,
    pub active: bool,
    pub currency: String,
    pub product: String,
    pub unit_amount: Option<i64>,
    pub unit_amount_decimal: Option<String>,
    pub nickname: Option<String>,
    pub created: i64,
    pub livemode: bool,
    #[serde(flatten)]
    pub additional_properties: std::collections::HashMap<String, serde_json::Value>,
}
//check if additional_properties is a text
impl Price {
    pub fn get_string(&self, key: &str) -> Option<String> {
        self.additional_properties.get(key).and_then(|value| {
            if let serde_json::Value::String(s) = value {
                Some(s.clone())
            } else {
                None
            }
        })
    }
}

#[derive(serde::Deserialize, Debug)]
pub struct Subscription {
    pub id: String,
    pub object: String,
    pub created: i64,
    pub currency: String,
    pub customer: String,
    pub status: String,
    pub start_date: i64,
    pub current_period_start: Option<i64>,
    pub current_period_end: Option<i64>,
    pub cancel_at_period_end: bool,
    pub canceled_at: Option<i64>,
    pub ended_at: Option<i64>,
    pub trial_start: Option<i64>,
    pub trial_end: Option<i64>,
    pub cancel_at: Option<i64>,
    pub livemode: bool,
    pub description: Option<String>,
    pub metadata: Option<std::collections::HashMap<String, String>>,
    #[serde(flatten)]
    pub additional_properties: std::collections::HashMap<String, serde_json::Value>,
}

impl Subscription {
    pub fn get_string(&self, key: &str) -> Option<String> {
        self.additional_properties.get(key).and_then(|value| {
            if let serde_json::Value::String(s) = value {
                Some(s.clone())
            } else {
                None
            }
        })
    }

    pub fn status_html1(&self) -> String {
        match self.status.as_str() {
            "active" => maud::html! {
                label-tag.primary { "Active" }
            }
            .into_string(),
            "canceled" => maud::html! {
                label-tag.default { "Canceled" }
            }
            .into_string(),
            "incomplete" => maud::html! {
                label-tag.warning { "Incomplete" }
            }
            .into_string(),
            "incomplete_expired" => maud::html! {
                label-tag.danger { "Incomplete Expired" }
            }
            .into_string(),
            "trialing" => {
                let trial_info = if let Some(trial_start) = self.trial_start {
                    if let Some(dt) = granite::DateTimeUtc::from_timestamp(trial_start, 0) {
                        format!("Trialing (Started {})", dt.format("%b %d, %Y"))
                    } else {
                        "Trialing".to_string()
                    }
                } else {
                    "Trialing".to_string()
                };
                maud::html! {
                    label-tag.info { (trial_info) }
                }
            }
            .into_string(),
            "past_due" => maud::html! {
                label-tag.warning { "Past Due" }
            }
            .into_string(),
            _ => maud::html! {
                label-tag.default { (self.status) }
            }
            .into_string(),
        }
    }

    pub fn status_html(&self) -> maud::Markup {
        let trial_text = self
            .trial_start
            .and_then(|ts| granite::DateTimeUtc::from_timestamp(ts, 0))
            .map(|dt| format!("Trialing (Started {})", dt.format("%b %d, %Y")))
            .unwrap_or_else(|| "Trialing".to_string());

        let (label_class, label_text) = match self.status.as_str() {
            "active" => ("primary", "Active"),
            "canceled" => ("default", "Canceled"),
            "incomplete" => ("warning", "Incomplete"),
            "incomplete_expired" => ("danger", "Incomplete Expired"),
            "trialing" => ("info", trial_text.as_str()),
            "past_due" => ("warning", "Past Due"),
            _ => ("default", self.status.as_str()),
        };

        maud::html! {
            label-tag.(label_class) { (label_text) }
        }
    }

    pub fn cancellation_info(&self) -> Option<maud::Markup> {
        if let Some(canceled_at) = self.canceled_at {
            let dt = granite::DateTimeUtc::from_timestamp(canceled_at, 0)?;
            Some(maud::html! {
                label-tag.danger { "Canceled on " (dt.format("%b %d, %Y")) }
            })
        } else if let Some(cancel_at) = self.cancel_at {
            let dt = granite::DateTimeUtc::from_timestamp(cancel_at, 0)?;
            Some(maud::html! {
                label-tag.warning { "Will cancel on " (dt.format("%b %d, %Y")) }
            })
        } else {
            None
        }
    }
}

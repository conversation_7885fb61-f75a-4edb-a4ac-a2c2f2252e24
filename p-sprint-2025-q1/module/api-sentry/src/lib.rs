use serde::Deserialize;

pub trait App: approck::App {
    fn sentry(&self) -> &ModuleStruct;
}

pub trait Identity: approck::Identity {}

#[derive(Deserialize)]
pub struct ModuleConfig {
    pub enabled: bool,
    pub dsn: Option<String>,
}

pub struct ModuleStruct {
    dsn: Option<String>,
    _guard: Option<sentry::ClientInitGuard>,
}

impl ModuleStruct {
    pub fn dsn(&self) -> Option<&str> {
        self.dsn.as_deref()
    }
}

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;

    fn new(config: Self::Config) -> granite::Result<Self> {
        let (dsn, guard) = match (config.enabled, config.dsn) {
            (true, None) => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .set_external_message(
                        "Sentry is enabled, but no DSN is provided".to_string(),
                    ));
            }
            (true, Some(dsn)) => {
                let guard = sentry::init((
                    dsn.clone(),
                    sentry::ClientOptions {
                        release: sentry::release_name!(),
                        ..Default::default()
                    },
                ));
                (Some(dsn), Some(guard))
            }
            (false, _) => (None, None),
        };

        Ok(Self { dsn, _guard: guard })
    }

    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}

#[approck::http(GET /admin/sendgrid/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document) -> Result<Response> {
        use maud::html;

        doc.set_title("SendGrid Admin Dashboard");

        let summary_output = crate::api::admin::sendgrid::log::summary::call(
            app,
            identity,
            crate::api::admin::sendgrid::log::summary::Input {},
        )
        .await?;
        let summary = summary_output.summary;

        doc.add_body(html! {
            panel {
                content {
                    h2 { "SendGrid Admin Dashboard" }
                    p { "Manage and monitor SendGrid email API usage." }
                }
            }

            grid-3 {
                panel {
                    header {
                        h5 { "Total Emails" }
                    }
                    content {
                        h2 { (summary.total_messages) }
                    }
                }
                panel {
                    header {
                        h5 { "Successful Emails" }
                    }
                    content {
                        h2 { (summary.successful_messages) }
                    }
                }
                panel {
                    header {
                        h5 { "Failed Emails" }
                    }
                    content {
                        h2 { (summary.failed_messages) }
                    }
                }
            }

            panel {
                header {
                    h5 { "Quick Actions" }
                }
                content {
                    a.btn.primary href="/admin/sendgrid/log-api-send" {
                        "View Send Logs"
                    }
                    " "
                    a.btn.secondary href="/admin/sendgrid/test" {
                        "Send Test Email"
                    }
                }
            }
        });

        Ok(Response::HTML(doc.into()))
    }
}

pub mod api;
pub mod web;

use std::collections::HashMap;
use std::time::Instant;

pub trait App: approck::App + approck_postgres::App {
    fn sendgrid(&self) -> &ModuleStruct;
}

pub trait Identity: approck::Identity {
    fn web_usage(&self) -> bool {
        self.is_admin()
    }
    fn api_usage(&self) -> bool {
        self.is_admin()
    }
    fn is_admin(&self) -> bool;
}

pub trait Document: bux::document::Base {}

#[derive(serde::Deserialize)]
pub struct ModuleConfig {
    pub api_key: String,
    pub sender: HashMap<String, String>,
}

pub struct ModuleStruct {
    pub api_key: String,
    pub sender_map: HashMap<String, String>,
}

impl approck::Module for ModuleStruct {
    type Config = ModuleConfig;
    fn new(config: Self::Config) -> granite::Result<Self> {
        Ok(Self {
            api_key: config.api_key,
            sender_map: config.sender,
        })
    }
    async fn init(&self) -> granite::Result<()> {
        Ok(())
    }
}

impl ModuleStruct {
    pub async fn send_email<A: App>(
        &self,
        app: &A,
        sender_key: &str,
        to: &str,
        subject: &str,
        message: &str,
    ) -> granite::Result<()> {
        let from_email = self
            .sender_map
            .get(sender_key)
            .ok_or_else(|| granite::Error::validation(format!("Unknown sender: {sender_key}")))?;

        self.send_email_with_logging(app, sender_key, from_email, to, subject, message)
            .await
    }

    async fn send_email_with_logging<A: App>(
        &self,
        app: &A,
        sender_key: &str,
        from_email: &str,
        to_email: &str,
        subject: &str,
        message: &str,
    ) -> granite::Result<()> {
        let request_start = Instant::now();
        let request_ts = chrono::Utc::now();

        let request_data = serde_json::json!({
            "personalizations": [{
                "to": [{"email": to_email}]
            }],
            "from": {"email": from_email},
            "subject": subject,
            "content": [{
                "type": "text/plain",
                "value": message
            }]
        });

        let result = self
            .send_email_internal(from_email, to_email, subject, message)
            .await;

        let response_ts = chrono::Utc::now();
        let duration_ms = request_start.elapsed().as_millis() as i32;

        let (success, response_status, response_text, response_error) = match &result {
            Ok(_) => (true, Some(200), Some("Success".to_string()), None),
            Err(e) => (false, None, None, Some(e.to_string())),
        };

        // Log the API call
        let dbcx = app.postgres_dbcx().await?;
        let _ = granite::pg_execute!(
            db = dbcx;
            args = {
                $request_ts: &request_ts,
                $response_ts: &response_ts,
                $sender_key: &sender_key,
                $from_email: &from_email,
                $to_email: &to_email,
                $subject: &subject,
                $message_body: &message,
                $request_data: &request_data,
                $response_status: &response_status,
                $response_text: &response_text,
                $response_error: &response_error,
                $duration_ms: &duration_ms,
                $success: &success,
            };
            INSERT INTO api_sendgrid.log_api_send (
                request_ts, response_ts, sender_key, from_email, to_email,
                subject, message_body, request_data, response_status, response_text,
                response_error, duration_ms, success
            ) VALUES ($request_ts, $response_ts, $sender_key, $from_email, $to_email,
                     $subject, $message_body, $request_data, $response_status, $response_text,
                     $response_error, $duration_ms, $success)
        )
        .await;

        result
    }

    async fn send_email_internal(
        &self,
        from_email: &str,
        to_email: &str,
        subject: &str,
        message: &str,
    ) -> granite::Result<()> {
        let client = reqwest::Client::new();

        let payload = serde_json::json!({
            "personalizations": [{
                "to": [{"email": to_email}]
            }],
            "from": {"email": from_email},
            "subject": subject,
            "content": [{
                "type": "text/plain",
                "value": message
            }]
        });

        let response = client
            .post("https://api.sendgrid.com/v3/mail/send")
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await
            .map_err(|e| granite::Error::api_request_error(format!("Failed to send email: {e}")))?;

        if response.status().is_success() {
            Ok(())
        } else {
            let status = response.status();
            let body = response
                .text()
                .await
                .unwrap_or_else(|_| "Unable to read response body".to_string());
            Err(granite::Error::api_request_error(format!(
                "SendGrid API error {status}: {body}"
            )))
        }
    }
}

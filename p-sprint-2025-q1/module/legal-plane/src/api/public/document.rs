#[approck::api]
pub mod legal_document_get_by_psid {
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub document_psid: String,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub document_uuid: Uuid,
        pub document_psid: Option<String>,
        pub active: bool,
        pub create_ts: DateTimeUtc,
        pub name: String,
        pub body_markdown: String,
        pub revision: String,
    }

    pub async fn call(app: App, _identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $document_psid: &input.document_psid,
            };
            row = {
                document_uuid: Uuid,
                document_psid: Option<String>,
                active: bool,
                create_ts: DateTimeUtc,
                name: String,
                body_markdown: String,
                revision: String,
            };
            SELECT
                document_uuid,
                document_psid,
                active,
                create_ts,
                name,
                body_markdown,
                revision
            FROM
                legal_plane.document
            WHERE
                document_psid = $document_psid
                AND active = true
            ORDER BY
                create_ts DESC
            LIMIT 1
        )
        .await?;

        Ok(Output {
            document_uuid: row.document_uuid,
            document_psid: row.document_psid,
            active: row.active,
            create_ts: row.create_ts,
            name: row.name,
            body_markdown: row.body_markdown,
            revision: row.revision,
        })
    }
}

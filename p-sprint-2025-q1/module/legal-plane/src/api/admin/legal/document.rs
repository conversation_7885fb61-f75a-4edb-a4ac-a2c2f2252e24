#[approck::api]
pub mod admin_legal_document_list {
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub active_only: Option<bool>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub documents: Vec<Document>,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Document {
        pub document_uuid: Uuid,
        pub document_psid: Option<String>,
        pub active: bool,
        pub create_ts: DateTimeUtc,
        pub name: String,
        pub body_markdown: String,
        pub revision: String,
    }

    pub async fn call(app: App, _identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;
        let active_only = input.active_only.unwrap_or(true);

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $active_only: &active_only,
            };
            row = {
                document_uuid: Uuid,
                document_psid: Option<String>,
                active: bool,
                create_ts: DateTimeUtc,
                name: String,
                body_markdown: String,
                revision: String,
            };
            SELECT
                document_uuid,
                document_psid,
                active,
                create_ts,
                name,
                body_markdown,
                revision
            FROM
                legal_plane.document
            WHERE true
                AND ($active_only::bool = false OR active = true)
            ORDER BY
                create_ts DESC
        )
        .await?;

        Ok(Output {
            documents: rows
                .into_iter()
                .map(|r| Document {
                    document_uuid: r.document_uuid,
                    document_psid: r.document_psid,
                    active: r.active,
                    create_ts: r.create_ts,
                    name: r.name,
                    body_markdown: r.body_markdown,
                    revision: r.revision,
                })
                .collect(),
        })
    }
}

#[approck::api]
pub mod admin_legal_document_get {
    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub document_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub document_uuid: Uuid,
        pub document_psid: Option<String>,
        pub active: bool,
        pub create_ts: DateTimeUtc,
        pub name: String,
        pub body_markdown: String,
        pub revision: String,
    }

    pub async fn call(app: App, _identity: Identity, input: Input) -> Result<Output> {
        let dbcx = app.postgres_dbcx().await?;

        let rows = granite::pg_row_vec!(
            db = dbcx;
            args = {
                $document_uuid: &input.document_uuid,
            };
            row = {
                document_uuid: Uuid,
                document_psid: Option<String>,
                active: bool,
                create_ts: DateTimeUtc,
                name: String,
                body_markdown: String,
                revision: String,
            };
            SELECT
                document_uuid,
                document_psid,
                active,
                create_ts,
                name,
                body_markdown,
                revision
            FROM
                legal_plane.document
            WHERE
                document_uuid = $document_uuid
        )
        .await?;

        Ok(Output {
            document_uuid: rows[0].document_uuid,
            document_psid: rows[0].document_psid.clone(),
            active: rows[0].active,
            create_ts: rows[0].create_ts,
            name: rows[0].name.clone(),
            body_markdown: rows[0].body_markdown.clone(),
            revision: rows[0].revision.clone(),
        })
    }
}

use granite::Uuid;

#[granite::gtype]
pub struct Document {
    pub document_uuid: Uuid,
    pub document_psid: Option<String>,
    pub active: bool,
    pub create_ts: DateTimeUtc,
    pub name: String,
    pub body_markdown: String,
    pub body_html: String,
    pub revision: String,
}

pub async fn load_active_by_psid(
    db: &impl approck_postgres::DB,
    document_psid: &str,
) -> granite::Result<Option<Document>> {
    let row = granite::pg_row_option!(
        db = db;
        args = {
            $document_psid: &document_psid,
        };
        row = {
            document_uuid: Uuid,
            document_psid: Option<String>,
            active: bool,
            create_ts: DateTimeUtc,
            name: String,
            body_markdown: String,
            revision: String,
        };
        SELECT
            document_uuid,
            document_psid,
            active,
            create_ts,
            name,
            body_markdown,
            revision
        FROM
            legal_plane.document
        WHERE
            document_psid = $document_psid
            AND active = true
        ORDER BY
            create_ts DESC
        LIMIT 1
    )
    .await?;

    match row {
        Some(row) => {
            let body_html = {
                let parser = pulldown_cmark::Parser::new(&row.body_markdown);
                let mut body_html = String::new();
                pulldown_cmark::html::push_html(&mut body_html, parser);
                body_html
            };

            Ok(Some(Document {
                document_uuid: row.document_uuid,
                document_psid: row.document_psid,
                active: row.active,
                create_ts: row.create_ts,
                name: row.name,
                body_markdown: row.body_markdown,
                body_html,
                revision: row.revision,
            }))
        }
        None => Ok(None),
    }
}

#[granite::gtype]
pub struct AgreementParams {
    pub document_uuid: Uuid,
    pub identity_uuid: Option<Uuid>,
    pub uuid_entity: Option<Uuid>,
    pub create_addr: String,
    pub create_name: String,
    pub name: String,
    pub body_markdown: String,
    pub revision: String,
}

/// Create an agreement record for a signed document
pub async fn create_agreement(
    db: &impl approck_postgres::DB,
    params: &AgreementParams,
) -> granite::Result<Uuid> {
    let agreement_result = granite::pg_row!(
        db = db;
        args = {
            $document_uuid: &params.document_uuid,
            $identity_uuid: &params.identity_uuid,
            $uuid_entity: &params.uuid_entity,
            $create_addr: &params.create_addr,
            $create_name: &params.create_name,
            $name: &params.name,
            $body_markdown: &params.body_markdown,
            $revision: &params.revision,
        };
        row = {
            agreement_uuid: Uuid,
        };
        INSERT INTO legal_plane.agreement (
            document_uuid,
            identity_uuid,
            uuid_entity,
            create_addr,
            create_name,
            name,
            body_markdown,
            revision
        )
        VALUES (
            $document_uuid,
            $identity_uuid,
            $uuid_entity,
            $create_addr::text::inet,
            $create_name,
            $name,
            $body_markdown,
            $revision
        )
        RETURNING agreement_uuid
    )
    .await?;

    Ok(agreement_result.agreement_uuid)
}

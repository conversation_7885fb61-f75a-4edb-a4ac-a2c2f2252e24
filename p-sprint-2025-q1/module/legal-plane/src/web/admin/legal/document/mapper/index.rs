#[approck::http(GET /admin/legal/document/{document_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    use maud::html;

    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        let document_uuid = path.document_uuid;
        let output = crate::api::admin::legal::document::admin_legal_document_get::call(
            app,
            identity,
            crate::api::admin::legal::document::admin_legal_document_get::Input { document_uuid },
        )
        .await?;

        // Set page title and load resources
        doc.set_title(&format!("Legal Document: {}", output.name));
        doc.page_nav_edit_record(
            "Edit Document",
            &format!("/admin/legal/document/{document_uuid}/edit"),
        );

        let mut table = bux::component::info_table(&output);
        table.set_heading("Document Information");
        table.add_row("UUID:", |d| html! { (d.document_uuid) });
        table.add_row("Name:", |d| html! { (d.name) });
        table.add_row("PSID:", |d| {
            html! {
                @if let Some(ref psid) = d.document_psid {
                    (psid)
                } @else {
                    em { "None" }
                }
            }
        });
        table.add_row("Revision:", |d| html! { (d.revision) });
        table.add_active_status_row("Status:", |d| d.active);
        table.add_row(
            "Created:",
            |d| html! { (d.create_ts.format("%Y-%m-%d %H:%M:%S UTC")) },
        );
        table.add_row("Content:", |d| {
            html! {
                div class="document-content" {
                    pre { (d.body_markdown) }
                }
            }
        });

        // Render detail in a panel with a table
        doc.add_body(html!((table)));

        Ok(Response::HTML(doc.into()))
    }
}

## What is it?

ace is a system for managing systems. It stands for AppCove Config Engine.

In a nutshell:

1. Bootstrap cloud environments
2. Provide a central and secure place to coordinate backups, disaster recovery, and other operational tasks
3. Generate and apply packer and terraform configurations

ace is a binary designed to be installed on a target managmeent system and, similar to git, be iniitalized and run from a spcific directory. It will automatically download and install terraform and packer (locally), and track all changes in a git repository.

## Getting Started for Production

1. Install ace on the server (TODO)
2. create a new directory `/home/<USER>/<accountkey>@<region>`
3. `ace init`
4. `ace build`
5. `ace pkr init`
6. `ace pkr build .`
7. `ace tf init`
8. `ace tf apply`

## Getting Started for Development

Clone the repository, compile the binary, and then create an alias to use it, e.g.:

`alias ace=/home/<USER>/code/ace2/target/debug/ace`

Make a new directory named after your account key, e.g. `atyou`

```
mkdir /home/<USER>/atyou@region
cd /home/<USER>/atyou@region
ace init
```

This will guide you through setting up your cloud root user, though, you can "fake" your way through that if you are just going to be developing locally and not applying anything to the cloud itself.

Once that is done, you will have a directory tree that has:

`packer` directory is used to store packer files and state.
`terraform` directory is used to store terraform files and state.
`ca` directory is an easyrsa certificate authority
`vpn` directory stores vpn connection profiles (derived from the `ca` files + various config lines)
`bin` directory is where the packer, terraform, easytls, etc... binaries are stored, which are automatically downloaded on `ace init`

`LOCAL.env` is a file that contains the following keys:

1. `AWS_REGION`: the region this ace instance is running against.
2. `AWS_ACCESS_KEY_ID`: (Optional) The access key from AWS/
3. `AWS_SECRET_ACCESS_KEY`: (Optional) The secret access key from AWS.

`etc/account.toml`: contains info on accounts, regions, and domains used by your sysadmin group.

`etc/config.yaml`: General settings, starting with account key and region

---

# How To:

## Deploy vpn

In your `etc/config.toml`, make sure to have a vpn config section, e.g.:

```toml
[vpn]
```

Then run `ace build` and `ace tf apply` to create the vpn instance.

To create the server config:\
`ace vpn create-server`

To deploy it to the new vpn instance:\
`ace vpn deploy-server`\
or\
`ace vpn deploy-server --use-public-ip`\
if you are not on the VPC already.

To create a client config:\
`ace vpn create-client <clientname>`

Then securely give that client config to the user.

To test, `sudo opnvpn <clientfile>.ovpn`

## Replace ace instance

Ideally once you've launched ace instance, you move the whole directory to ace instance and manage from there over ssh. However, in the event that you need to upgrade the ace instance itself, it is a chicken-and-egg problem, because if you destroy that innstance in the process of upgrading, you'll lose your terraform state.

The proper way to handle this is to remove the ace instance from terraform state:

`ace tf state rm aws_instance.ace2_instance`\
`ace tf apply`

This will launch a new ace instance beside yours, and steal the elastic IP in the process.

Manually copy your ace files from the old to the new, then terminate the old manually.

---

# Glossary:

`brsrc` - bucket replication source
`brdst` - bucket replication destination
`mysqldevimg` - sanitized mysql backup
`etc/mysqldevimg.{identifier}.sql.gz` - the actual backup file
`etc/mysqldevimg.{identifier}.toml` - the config file

# Items to check versions on from time to time:

- terraform
- packer
- easytls

# Things that should be done on each run

- Need a cache dir, and to check some things very efficiently, like, if this install has moved (in which case ace build is NEEDED)

# Things to add to ace check

- is easy-rsa installed
- is easy-tls installed
- is openssl installed
- terraform version
- packer version
- git iden

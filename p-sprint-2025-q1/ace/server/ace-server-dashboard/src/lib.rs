pub mod web;

pub trait App: ace_server_zero::App + approck::App + Sync + Send {
    fn broadcast_to_clients(
        &'static self,
        state_update: ace_server_zero::ClientUpdate,
    ) -> impl std::future::Future<Output = granite::Result<()>> + Send {
        async move {
            // If the broadcast channel has listeners, send the message.  Otherwise, do nothing.
            if let Some((broadcast_channel, c)) =
                &*self.zero_system().dashboard_broadcast_state.read().await
            {
                // Print out number of listening clients (debugging purposes)
                println!("Number of listening clients: {c}");

                // Create message
                let update_msg = ace_server_zero::ClientMessage {
                    msg_id: uuid::Uuid::new_v4().to_string(),
                    update: state_update.clone(),
                    timestamp: chrono::Utc::now().timestamp(),
                };

                // Serialize message
                let msg = match serde_json::to_string(&update_msg) {
                    Ok(msg) => msg,
                    Err(e) => {
                        println!("Error serializing message: {e}");
                        return Err(granite::Error::new(granite::ErrorType::Unexpected)
                            .add_context(format!("Error serializing message: {e}")));
                    }
                };

                // Send message
                match broadcast_channel.send(msg) {
                    Ok(_) => println!("Broadcasting update to clients"),
                    Err(e) => println!("Error sending message to clients: {e}"),
                }

                Ok(())
            } else {
                println!("No clients listening - not broadcasting update");
                Ok(())
            }
        }
    }
}

pub trait Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}

pub trait Document: bux::document::Base {}

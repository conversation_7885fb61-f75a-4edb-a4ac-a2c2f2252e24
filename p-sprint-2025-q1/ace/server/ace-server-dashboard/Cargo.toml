[package]
name = "ace-server-dashboard"
version = "0.1.0"
edition = "2024"


[package.metadata.approck.mod]
extends = ["bux","granite"]

[dependencies]

ace-db = { path = "../../core/ace-db" }
ace-graph = { path = "../../core/ace-graph" }
ace-server-zero = { path = "../../server/ace-server-zero" }
ace-types = { path = "../../shared/ace-types" }
garbage = { path = "../../shared/garbage"}

approck = { workspace = true }
bux = { workspace = true }
chrono = { workspace = true }
granite = { workspace = true }
maud = { workspace = true }
tokio = { workspace = true, features = ["full"] }
uuid = { workspace = true }
serde_json = { workspace = true }


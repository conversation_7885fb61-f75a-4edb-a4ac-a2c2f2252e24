pub mod web;

use ace_graph::GraphKeyExt;
use ace_server_zero::{AgentChannelListener, HostState, UUID};
use ace_types::{Job_a2s, Job_s2a, SystemInfo_a2s};
use std::collections::HashMap;
use tokio::sync::RwLockReadGuard;

pub trait App:
    ace_server_zero::App + ace_server_dashboard::App + approck::App + Sync + Send
{
    fn register_agent(
        &'static self,
        host_gk: String,
    ) -> impl std::future::Future<
        Output = Result<AgentChannelListener, ace_server_zero::BadConnectionReason>,
    > + Send {
        async move {
            // First verify gk is valid
            let host_gk = match ace_graph::Host::deserialize(&host_gk) {
                Ok(gk) => gk,
                Err(e) => {
                    println!("Invalid graph key: {e}");
                    return Err(ace_server_zero::BadConnectionReason::InvalidGraphKey(e));
                }
            };

            let ace_db_app = &self.zero_system().ace_core_app.ace_db_app;

            // If the connecting agent ISN'T in the CANONICAL HOST LIST (from ace-graph), deny it.
            match ace_graph::host::get(&host_gk, ace_db_app).await {
                Ok(_) => {}
                Err(e) => {
                    return Err(ace_server_zero::BadConnectionReason::UnknownHost(format!(
                        "{e:#?}"
                    )));
                }
            }

            // If the connecting agent is already IN the connected_agent_list, deny it.
            // WRITES to CONNECTED_AGENT_LIST
            let mut agent_list = self.zero_system().connected_agent_list.write().await;

            if agent_list.contains_key(&host_gk) {
                return Err(ace_server_zero::BadConnectionReason::DuplicateHost);
            }

            // Create a new listening/sending channel pair for this specific agent:
            let (sending_channel, listener) = tokio::sync::mpsc::channel(1);

            // Put the listener in the struct to return
            let agent_listener = AgentChannelListener {
                graphkey: host_gk.clone(),
                listener,
            };

            // Store the SENDER channel in the connected_agent_list
            agent_list.insert(host_gk.clone(), sending_channel);

            println!("Registered Agent/Instance: {host_gk}");

            // Return the LISTENING channel
            Ok(agent_listener)
        }
    }

    fn deregister_agent(
        &'static self,
        gk: &ace_graph::Host,
    ) -> impl std::future::Future<Output = granite::Result<()>> + Send {
        async move {
            let mut agent_list = self.zero_system().connected_agent_list.write().await;

            match agent_list.remove(gk) {
                Some(_sender) => {
                    println!("Deregistered agent with connection ID: {gk}");
                    Ok(())
                }
                None => {
                    let err = granite::Error::new(granite::ErrorType::Unexpected).add_context(
                        format!("Agent with connection ID {gk} not found in connected_agent_list"),
                    );
                    Err(err)
                }
            }
        }
    }

    fn broadcast(
        &'static self,
        job: Job_s2a,
        msg_type: ace_types::Message_Type,
    ) -> impl std::future::Future<Output = granite::Result<()>> + Send {
        async move {
            let agent_channels: Vec<tokio::sync::mpsc::Sender<String>> = {
                let agent_channels: RwLockReadGuard<
                    HashMap<ace_graph::Host, tokio::sync::mpsc::Sender<String>>,
                > = self.zero_system().connected_agent_list.read().await;
                agent_channels.values().cloned().collect()
            };

            for agent_channel in agent_channels {
                // Create the message (with uuid and this specific timestamp)
                let msg_struct = ace_types::Message_s2a {
                    msg_id: uuid::Uuid::new_v4().to_string(),
                    job: job.clone(),
                    timestamp: chrono::Utc::now().timestamp(),
                    msg_type: msg_type.clone(),
                };

                // Serialize the message
                let msg = match serde_json::to_string(&msg_struct) {
                    Ok(msg) => msg,
                    Err(e) => {
                        println!("Error serializing message: {e}");
                        continue;
                    }
                };

                // Send the message
                if let Err(e) = agent_channel.send(msg).await {
                    let err = granite::Error::new(granite::ErrorType::Unexpected)
                        .add_context(format!("Error sending message to agent: {e}"));
                    return Err(err);
                }
            }

            Ok(())
        }
    }

    fn init(&'static self) {
        println!("Starting broadcast background task...");

        tokio::spawn(async move {
            loop {
                tokio::time::sleep(std::time::Duration::from_secs(15)).await;

                let job = Job_s2a::SystemInfo(ace_types::SystemInfo_s2a);
                let msg_type = ace_types::Message_Type::Prompt;

                let _ = self.broadcast(job, msg_type).await;
            }
        });
    }

    /// Finds or adds the host to the host_states list and updates the status of the host.
    /// This is the ONLY place where we write to the host_states list
    /// This is also where we send updates to the connected clients for the dashboard
    fn update_host_status(
        &'static self,
        msg_id: UUID,
        host_graphkey: &ace_graph::Host,
        job: ace_types::Job_a2s,
        msg_timestamp: i64,
    ) -> impl std::future::Future<Output = granite::Result<()>> + Send {
        async move {
            let host_last_known_status = match job {
                Job_a2s::SystemInfo(SystemInfo_a2s::Ok(info)) => Some(HostState::Ok(info)),
                Job_a2s::SystemInfo(SystemInfo_a2s::Error(err)) => {
                    Some(HostState::Error(err.to_owned()))
                }
                Job_a2s::Ping | Job_a2s::Pong => {
                    // No information to update HostState::Ok(info) with.
                    // Can't assign as Unknown, as we might overwrite a previous existing state.
                    None
                }
            };

            {
                let mut host_states = self.zero_system().host_states.write().await;
                let client_update = match host_states.get_mut(host_graphkey) {
                    Some(instance) => {
                        // MODIFY the information for this host in the host_states list
                        instance.last_msg_id = msg_id;
                        instance.last_msg_timestamp = msg_timestamp;

                        // Only update the host's status if we have a new status to update to
                        // (If it was a ping or pong, no status to update to)
                        if let Some(status) = host_last_known_status {
                            instance.last_known_status = status;
                        }

                        ace_server_zero::ClientUpdate::HostState(instance.clone())
                    }
                    None => {
                        let host_info = ace_server_zero::HostInfo {
                            host_gk: host_graphkey.serialize(),
                            last_known_status: host_last_known_status.unwrap_or(HostState::Unknown),
                            last_msg_timestamp: msg_timestamp,
                            last_msg_id: msg_id,
                        };

                        // ADD the information to the host_states list
                        host_states.insert(host_graphkey.clone(), host_info.clone());

                        ace_server_zero::ClientUpdate::HostState(host_info)
                    }
                };

                // Send information to the connected clients for the dashboard here:
                match self.broadcast_to_clients(client_update).await {
                    Ok(_) => (),
                    Err(e) => {
                        println!("Error broadcasting to clients: {e}");
                    }
                }
            }

            Ok(())
        }
    }
}

pub trait Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}

#[approck::http(GET /ace-agent/install.sh?gk=String; AUTH None; return Bytes;)]
pub mod install_sh {
    use ace_graph::GraphKeyExt;
    use garbage::CNSL;

    pub async fn request(app: App, qs: QueryString) -> Result<Response> {
        let ace2_server_name = match ace_graph::config::get(
            &ace_graph::Config::EtcConfig,
            &app.zero_system().ace_core_app.ace_db_app,
        )
        .await
        {
            Ok(c) => c.ace2_server_name,
            Err(e) => return Err(granite::from_error_stack!(e)),
        };

        let agent_host_gk = match ace_graph::Host::deserialize(&qs.gk) {
            Ok(gk) => gk,
            Err(e) => {
                app.zero_system()
                    .update_bad_conn_list(
                        qs.gk.clone(),
                        ace_server_zero::BadConnectionReason::InvalidGraphKey(e.clone()),
                    )
                    .await;

                return Err(granite::from_error_stack!(e));
            }
        };

        let ace2_url = format!("https://{ace2_server_name}");

        #[rustfmt::skip]
        let contents = CNSL!(
            r#"
            #!/bin/bash
    
            echo "This script will install ace-agent on this system."
    
            # Fail on errors
            set -e
    
            ACE_URL=""#, &ace2_url, r#""
    
            # Create the ace-agent directories if they do not exist
            mkdir -p /opt/ace-agent/etc
            mkdir -p /opt/ace-agent/bin

            # Gather OS and architecture information
            os_info=$(uname -s)
            arch_info=$(uname -m)

            # Ping ace-server version to get proper version of ace-agent
            ace_agent_version=$(curl "$ACE_URL/ace-agent/version?os=${os_info}&arch=${arch_info}")

            # If the version is empty, something went wrong.  Don't execute any further.
            if [ -z "$ace_agent_version" ]; then
                echo "Error: ace-agent version is empty.  Exiting."
                exit 1
            fi
    
            # create opt/ace-agent/etc/ace-agent.toml
            cat <<EOF | sudo dd of=/opt/ace-agent/etc/ace-agent.toml
            ace_agent_version = "${ace_agent_version}"
            ace_server = ""#, &ace2_server_name, r#""
            architecture = "&{arch_info}"
            data_dir = "/var/lib/ace-agent"
            graph_key = ""#, &agent_host_gk.to_string(), r#""
            os = "&{os_info}"
            EOF

            echo "Downloading ace-agent version: ${ace_agent_version}"

            # Download new ace-agent
            # (The ace-agent-updater/download endpoint is not binary specific, it will download whatever filename you give it)
            curl --fail --no-progress-meter --output /tmp/$ace_agent_version $ACE_URL/ace-agent-updater/download?version=${ace_agent_version}

            # Check hash of ace-agent binary
            # 1. Extract the hash from the filename
            expected_hash=$(echo "/tmp/$ace_agent_version" | awk -F '-' '{print $NF}')

            # 2. Calculate the actual hash of the downloaded file
            actual_hash=$(sha256sum /tmp/$ace_agent_version | awk '{print $1}')

            # 3. Compare the hashes
            if [ "$expected_hash" != "$actual_hash" ]; then
                echo "Error: Hash mismatch. Expected $expected_hash but got $actual_hash. Exiting."
                exit 1
            fi

            echo "Hashes match. Continuing."

            # set perms
            sudo chmod 755 /tmp/$ace_agent_version
    
            # run it with install option
            cd /tmp
            sudo ./${ace_agent_version} install
    
            # Remove the binary
            rm -f /tmp/$ace_agent_version
        "#
        );

        let rval = Bytes::new(contents);
        Ok(Response::Bytes(rval))
    }
}

#[approck::http(GET /ace-agent/show-connected; AUTH None; return Text;)]
pub mod page {
    pub async fn request(app: App) -> Response {
        let mut display_list = vec![];

        {
            let agent_list = app.zero_system().connected_agent_list.read().await;
            let ins_list = agent_list.keys();

            for ins in ins_list {
                display_list.push(format!("Agent/Instance: {ins}"));
            }
        }

        Response::Text(format!("{display_list:#?}").into())
    }
}

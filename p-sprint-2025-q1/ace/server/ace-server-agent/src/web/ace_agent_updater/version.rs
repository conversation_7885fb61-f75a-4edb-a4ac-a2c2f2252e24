#[approck::http(POST /ace-agent-updater/version?os=String&arch=String; AUTH None; return Text;)]
pub mod page {
    use glob::glob;

    /// NOTE: This endpoint is for the use of ACE-AGENT only!

    pub async fn request(app: App, qs: QueryString) -> Result<Response> {
        let config = match ace_graph::config::get(
            &ace_graph::Config::EtcConfig,
            &app.zero_system().ace_core_app.ace_db_app,
        )
        .await
        {
            Ok(c) => c,
            Err(e) => return Err(granite::from_error_stack!(e)),
        };

        let bin_dir_path = &app.zero_system().ace_core_app.bin_path;

        // Search for ace-agent-updater-<version>-<os>-<arch>-* with glob. (Hash should be unique, but can't be used as an identifier)
        let glob_pattern = format!(
            "{}/ace-agent-updater-{}-{}-{}-*",
            bin_dir_path.to_string_lossy(),
            config.agent_version,
            qs.os,
            qs.arch
        );

        let mut paths = match glob(&glob_pattern) {
            Ok(paths) => paths.collect::<Vec<_>>(),
            Err(e) => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .add_context(format!("Error globbing for {glob_pattern}: {e}")));
            }
        };

        // If there's just one result, great!
        if paths.len() == 1 {
            match paths.pop() {
                Some(p) => {
                    let path = match p {
                        Ok(p) => p,
                        Err(e) => {
                            return Err(granite::Error::new(granite::ErrorType::Unexpected)
                                .add_context(format!("Glob Error: {e:?}")));
                        }
                    };
                    let filename = match path.file_name() {
                        Some(filename) => filename.to_string_lossy().to_string(),
                        None => {
                            return Err(granite::Error::new(granite::ErrorType::Unexpected)
                                .add_context(format!(
                                    "Error getting filename from path: {path:?}"
                                )));
                        }
                    };

                    // Return the binary filename that ace-agent should download/install.
                    return Ok(Response::Text(filename.into()));
                }

                None => {
                    return Err(granite::Error::new(granite::ErrorType::Unexpected)
                        .add_context(format!("Single path element was None for: {glob_pattern}")));
                }
            }

        // There's more than one file.  This is a problem.
        } else {
            return Err(
                granite::Error::new(granite::ErrorType::Unexpected).add_context(format!(
                    "Not one file found for: {glob_pattern}\nFound: {}",
                    paths.len()
                )),
            );
        }
    }
}

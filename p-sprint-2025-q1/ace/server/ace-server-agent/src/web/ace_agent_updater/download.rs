#[approck::http(GET /ace-agent-updater/download?version=String; AUTH None; return Bytes;)]
pub mod download {

    pub async fn request(app: App, qs: QueryString) -> Result<Response> {
        let ace_core_app = app.zero_system().ace_core_app;

        let binary_path = ace_core_app.bin_path.join(qs.version);

        if !binary_path.exists() {
            return Err(approck::Error::new(approck::ErrorType::DataNotFound));
        }

        let contents = tokio::fs::read(&binary_path).await?;
        let rval = Bytes::new(contents);

        Ok(Response::Bytes(rval))
    }
}

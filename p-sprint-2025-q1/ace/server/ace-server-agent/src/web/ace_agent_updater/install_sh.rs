#[approck::http(GET /ace-agent-updater/install.sh; AUTH None; return Bytes;)]
pub mod install_sh {
    use garbage::CNSL;

    pub async fn request(app: App) -> Result<Response> {
        let ace2_server_name = match ace_graph::config::get(
            &ace_graph::Config::EtcConfig,
            &app.zero_system().ace_core_app.ace_db_app,
        )
        .await
        {
            Ok(c) => c.ace2_server_name,
            Err(e) => return Err(granite::from_error_stack!(e)),
        };

        let ace2_url = format!("https://{ace2_server_name}");

        #[rustfmt::skip]
        let contents = CNSL!(
            r#"
            #!/bin/bash
    
            echo "This script will install ace-agent-updater on this system."
    
            # Fail on errors
            set -e
    
            ACE_URL=""#, &ace2_url, r#""
    
            # Create the ace-agent directories if they do not exist
            sudo mkdir -p /opt/ace-agent/bin
            sudo mkdir -p /opt/ace-agent/etc

            # Gather OS and architecture information
            os_info=$(uname -s)
            arch_info=$(uname -m)

            # Ping ace-server version to get proper version of ace-agent-updater
            ace_agent_updater_version=$(curl "$ACE_URL/ace-agent-updater/version?os=${os_info}&arch=${arch_info}")

            # If the version is empty, something went wrong.  Don't execute any further.
            if [ -z "$ace_agent_updater_version" ]; then
                echo "Error: ace-agent-updater version is empty.  Exiting."
                exit 1
            fi

            # Create the ace-agent-updater config file
            cat <<EOF | sudo dd of=/opt/ace-agent/etc/ace-agent-updater.toml
            ace_agent_updater_version = "${ace_agent_updater_version}"
            ace_server = ""#, &ace2_server_name, r#""
            EOF

            echo "Downloading ace-agent-updater version: ${ace_agent_updater_version}"

            # Download new ace-agent-updater
            sudo curl --fail --no-progress-meter --output /opt/ace-agent/bin/$ace_agent_updater_version $ACE_URL/ace-agent-updater/download?version=${ace_agent_updater_version}

            echo "Comparing hashes..."

            # Check hash of ace-agent-updater binary
            # 1. Extract the hash from the filename
            expected_hash=$(echo "$ace_agent_updater_version" | awk -F '-' '{print $NF}')

            # 2. Calculate the actual hash of the downloaded file
            actual_hash=$(sha256sum /opt/ace-agent/bin/$ace_agent_updater_version | awk '{print $1}')

            # 3. Compare the hashes
            if [ "$expected_hash" != "$actual_hash" ]; then
                echo "Error: Hash mismatch. Expected $expected_hash but got $actual_hash. Exiting."
                exit 1
            fi

            echo "Hashes match. Continuing."

            # set perms
            sudo chmod 755 /opt/ace-agent/bin/$ace_agent_updater_version
    
            # run it
            cd /opt/ace-agent/bin
            ./$ace_agent_updater_version run
        "#
        );

        let rval = Bytes::new(contents);
        Ok(Response::Bytes(rval))
    }
}

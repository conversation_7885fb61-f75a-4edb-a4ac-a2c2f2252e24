#[approck::http(GET /; AUTH None; return Text;)]
pub mod index {
    pub async fn request(app: App) -> Response {
        let config = match ace_graph::config::get(
            &ace_graph::Config::EtcConfig,
            &app.zero_system().ace_core_app.ace_db_app,
        )
        .await
        {
            Ok(config) => config,
            Err(e) => return Response::Text(format!("Error: {e:#?}").into()),
        };

        let region = config.region;
        let account_key = config.account_key;

        Response::Text(format!("You have reached: ace.{region}.{account_key}.net").into())
    }
}

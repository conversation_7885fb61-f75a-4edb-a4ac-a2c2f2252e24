use ace_graph::mysqldevimg;
use anyhow::Context;
use axum::{
    extract::{Path, Query},
    response::IntoResponse,
};
use axum_auth::AuthBasic;

#[derive(serde::Deserialize)]
pub struct Params {
    pub app: String,
}

#[derive(serde::Serialize)]
pub struct Response<'a> {
    #[serde(rename = "AppName")]
    pub app_name: String,
    #[serde(rename = "TLS")]
    pub tls: ResponseTLS,
    #[serde(rename = "Config")]
    pub config: &'a toml::value::Table,
    #[serde(rename = "Suffix")]
    pub suffix: String,
    #[serde(rename = "Region")]
    pub region: String,
}

#[derive(serde::Serialize)]
pub struct ResponseTLS {
    pub cert: String,
    pub key: String,
}

#[axum_macros::debug_handler]
pub async fn request(
    AuthBasic((id, password)): AuthBasic,
    Query(params): Query<Params>,
    Path(method): Path<String>,
) -> Result<impl axum::response::IntoResponse, appstruct::WebError> {
    let password = password.context("basic auth password missing")?;

    let config = match ace_graph::config::get(&ace_graph::Config::EtcConfig).await {
        Ok(config) => config,
        Err(e) => {
            return Err(appstruct::WebError::AuthorizationError(format!(
                "Config not found: {:#?}",
                e
            )));
        }
    };

    let mysqldevimg_list = match mysqldevimg::select(&ace_graph::MysqlDevImgFilter::All).await {
        Ok(mysqldevimg_list) => mysqldevimg_list,
        Err(e) => {
            return Err(appstruct::WebError::AuthorizationError(format!(
                "mysqldevimg list not found: {:#?}",
                e
            )));
        }
    };

    tracing::info!(
        "user={:?}, pass={:?}, app_name={:?}",
        id,
        password,
        params.app
    );

    let developer = match ace_graph::dev::get(&ace_graph::Developer::Etc(id.clone())).await {
        Ok(developer) => developer,
        Err(e) => {
            return Err(appstruct::WebError::AuthorizationError(format!(
                "User not found: {:#?}",
                e
            )));
        }
    };

    // check the password
    if developer.secret != password {
        return Err(appstruct::WebError::AuthorizationError(
            "User not authorized!".to_string(),
        ));
    }

    // Find the app_name in the developer_config (only care if it exists in the developer app config map).
    // If it does, THEN we get it.
    let developer_app = match developer.app_config_map.get(&params.app) {
        Some(_db_devapp_conf) => {
            let app_gk = ace_graph::App::Etc(params.app.clone());
            match ace_graph::devapp::get(&ace_graph::DeveloperApp::Developer(
                developer.graphkey.clone(),
                app_gk.clone(),
            ))
            .await
            {
                Ok(developer_app) => developer_app,
                Err(e) => {
                    return Err(appstruct::WebError::AuthorizationError(format!(
                        "DeveloperApp not found: {:#?}",
                        e
                    )));
                }
            }
        }
        None => {
            return Err(appstruct::WebError::AuthorizationError(format!(
                "User not authorized to use {}.",
                params.app
            )));
        }
    };

    match method.as_ref() {
        "foo" => {
            let response = axum::http::Response::builder()
                .status(axum::http::StatusCode::OK)
                .body("bar\n".to_string())
                .with_context(|| {
                    format!("While attempting to build response for method {}.", method)
                })?
                .into_response();
            Ok(response)
        }
        "config" => {
            let tls_manager = match ace_core::tls::TlsManager::new().await {
                Ok(tls_manager) => tls_manager,
                Err(e) => {
                    return Err(appstruct::WebError::AuthorizationError(format!(
                        "TlsManager not found: {:#?}",
                        e
                    )));
                }
            };
            let cert_gk =
                ace_graph::TlsCert::Developer(developer.graphkey.clone(), "devserver".to_string());
            let certificate = match tls_manager.get_cert(&cert_gk) {
                Ok(certificate) => certificate,
                Err(e) => {
                    return Err(appstruct::WebError::AuthorizationError(format!(
                        "Certificate not found: {:#?}",
                        e
                    )));
                }
            };
            let cert_path = certificate.get_cert_path();
            let key_path = certificate.get_key_path();

            // load them
            let cert = tokio::fs::read_to_string(&cert_path)
                .await
                .with_context(|| format!("While attempting to read {}", cert_path.display()))?;
            let key = tokio::fs::read_to_string(&key_path)
                .await
                .with_context(|| format!("While attempting to read {}", key_path.display()))?;

            let response = Response {
                app_name: params.app,
                tls: ResponseTLS { cert, key },
                config: &developer_app.conf_merged,
                suffix: format!(".{}", developer.public_hostname),
                region: config.region.to_string(),
            };

            Ok(axum::Json(&response).into_response())
        }
        "mysqldevimg" => {
            // find the mysqldevimg for this developer
            let mysqldevimg = match mysqldevimg_list.iter().find(|a| a.name == params.app) {
                Some(mysqldevimg) => mysqldevimg,
                None => {
                    return Err(appstruct::WebError::AuthorizationError(format!(
                        "No mysqldevimg found for {}.",
                        params.app
                    )));
                }
            };

            let file_path = &mysqldevimg.sql_path;

            let contents = tokio::fs::read(file_path).await?;

            Ok((axum::http::StatusCode::OK, contents).into_response())
        }
        _ => Err(appstruct::WebError::AuthorizationError(format!(
            "Method {} not found.",
            method
        ))),
    }
}

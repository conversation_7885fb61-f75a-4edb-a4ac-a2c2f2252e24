pub mod config;
pub mod config_pp;
pub mod foo;
pub mod mysqldevimg;

pub(crate) async fn authenticate_developer(
    username: String,
    password: String,
    app: &impl crate::App,
) -> granite::Result<ace_graph::developer::Developer> {
    // Get current developer (based off login information)
    let developer = match ace_graph::developer::get(
        &ace_graph::Developer::Db(username),
        &app.zero_system().ace_core_app.ace_db_app,
    )
    .await
    {
        Ok(developer) => developer,
        Err(e) => {
            return Err(approck::Error::new(approck::ErrorType::Authorization)
                .add_context(format!("{e:#?}")));
        }
    };

    // check the password
    if developer.secret != password {
        return Err(approck::Error::new(approck::ErrorType::Authorization)
            .add_context("User not authorized!"));
    }

    Ok(developer)
}

pub(crate) async fn authenticate_app(
    app_name: String,
    secret: String,
    app: &impl crate::App,
) -> granite::Result<ace_graph::app::App> {
    let ace_db_app = &app.zero_system().ace_core_app.ace_db_app;

    let app = match ace_graph::app::get(&app_name, ace_db_app).await {
        Ok(app) => app,
        Err(e) => {
            return Err(approck::Error::new(approck::ErrorType::Authorization)
                .add_context(format!("{e:#?}")));
        }
    };

    if app.secret != secret {
        return Err(approck::Error::new(approck::ErrorType::Authorization)
            .add_context("User not authorized!"));
    }

    Ok(app)
}

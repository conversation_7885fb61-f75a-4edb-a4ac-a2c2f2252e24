#[approck::http(GET /ehcz/foo; AUTH None; return Text|Empty;)]
pub mod foo {
    pub async fn request(app: App, auth_basic: AuthBasic) -> Response {
        let (username, password) = auth_basic;

        match crate::web::ehcz::authenticate_developer(username, password, app).await {
            Ok(_dev) => return Response::Text("Bar".into()),

            // TODO: handle this error better?
            Err(_e) => {
                let response = Empty {
                    status: approck::StatusCode::UNAUTHORIZED,
                    ..Default::default()
                };
                return Response::Empty(response);
            }
        }
    }
}

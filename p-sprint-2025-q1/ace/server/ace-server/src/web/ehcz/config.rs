#[approck::http(GET /ehcz/config?app_name=String; AUTH None; return JSON|Empty;)]
pub mod config {

    #[derive(serde::Serialize)]
    pub struct TlsResponse {
        pub app_name: String,
        pub cert: String,
        pub key: String,
        pub config: toml::value::Table,
        pub suffix: String,
        pub region: String,
    }

    pub async fn request(app: App, qs: QueryString, auth_basic: AuthBasic) -> Result<Response> {
        let (username, password) = auth_basic;
        let ace_core_app = app.zero_system().ace_core_app;

        let developer =
            match crate::web::ehcz::authenticate_developer(username.clone(), password, app).await {
                Ok(dev) => dev,

                // TODO: handle this error better?
                Err(_e) => {
                    let response = Empty {
                        status: approck::StatusCode::UNAUTHORIZED,
                        ..Default::default()
                    };
                    return Ok(Response::Empty(response));
                }
            };

        let ace_db_app = &app.zero_system().ace_core_app.ace_db_app;

        // Grab config (for region)
        let config = match ace_graph::config::get(&ace_graph::Config::EtcConfig, ace_db_app).await {
            Ok(config) => config,
            Err(e) => {
                return Err(granite::from_error_stack!(e));
            }
        };

        // Get developer app
        let developer_app = match ace_graph::developer_app::get(
            &ace_graph::DeveloperApp::Db(
                developer.graphkey.clone(),
                ace_graph::App::Db(qs.app_name.clone()),
            ),
            ace_db_app,
        )
        .await
        {
            Ok(devapps) => devapps,
            Err(e) => {
                return Err(granite::from_error_stack!(e));
            }
        };

        let tls_manager = match ace_core::tls::TlsManager::new(ace_db_app).await {
            Ok(tls_manager) => tls_manager,
            Err(e) => {
                return Err(granite::from_error_stack!(e));
            }
        };

        let cert_gk = ace_graph::TlsCert::Developer(developer.graphkey, "wcpub".to_string());
        let certificate = match tls_manager.get_cert(&cert_gk) {
            Ok(certificate) => certificate,
            Err(e) => {
                return Err(granite::from_error_stack!(e));
            }
        };

        let cert = certificate.read_cert(ace_core_app).await?;
        let key = certificate.read_key(ace_core_app).await?;

        let tls_response = TlsResponse {
            app_name: qs.app_name,
            cert,
            key,
            config: developer_app.conf_merged.clone(),
            suffix: format!(".{}", developer.public_hostname),
            region: config.region,
        };

        let rval = serde_json::to_string(&tls_response)?;

        Ok(Response::JSON(rval.into()))
    }
}

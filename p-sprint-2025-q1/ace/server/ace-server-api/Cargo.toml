[package]
name = "ace-server-api"
version = "0.1.0"
edition = "2024"

[package.metadata.approck.mod]
extends = ["ace-server-zero"]


[dependencies]
ace-server-zero = { path = "../ace-server-zero" }

ace-core = { path = "../../core/ace-core" }
ace-graph = { path = "../../core/ace-graph" }
ace-types = { path = "../../shared/ace-types" }

approck = { workspace = true }
granite = { workspace = true }
reqwest = { workspace = true, features = ["multipart"] }
serde = { workspace = true, features = ["derive"] }
toml = { workspace = true }
tokio = { workspace = true, features = ["full"] }
serde_json = { workspace = true }

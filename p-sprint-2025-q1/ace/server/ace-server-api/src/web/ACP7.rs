#![allow(non_snake_case)]

#[approck::http(GET /ACP7/Config?ACP7_App_GSIL=String; AUTH None; return JSON|Text;)]
pub mod config {
    use std::collections::HashMap;

    pub async fn request(app: App, qs: QueryString, auth_basic: AuthBasic) -> Result<Response> {
        let (username, password) = auth_basic;
        let ace_core_app = app.zero_system().ace_core_app;

        let app_names = qs.ACP7_App_GSIL.split(' ');

        let developer =
            match ace_server_zero::authenticate_developer(&username, &password, app).await {
                Ok(dev) => dev,
                Err(_e) => {
                    let response = Text {
                        status: approck::StatusCode::UNAUTHORIZED,
                        content: "developer credentials are not valid".to_string(),
                        ..Default::default()
                    };
                    return Ok(Response::Text(response));
                }
            };

        let ace_db_app = &app.zero_system().ace_core_app.ace_db_app;

        let mut pem_pairs = Vec::new();
        let mut config_map = HashMap::new();

        for app_name in app_names {
            let app_name = app_name.to_string();
            let app_name_lower = app_name.to_lowercase();

            // Get developer app
            let developer_app = match ace_graph::developer_app::get(
                &ace_graph::DeveloperApp::Db(
                    developer.graphkey.clone(),
                    ace_graph::App::Db(app_name_lower),
                ),
                ace_db_app,
            )
            .await
            {
                Ok(devapps) => devapps,
                Err(e) => {
                    return Err(granite::from_error_stack!(e));
                }
            };

            // Configs in LOCAL.yaml go under /DaaS-TMT/Config/...
            config_map.insert(app_name, developer_app.conf_merged.clone());

            let tls_manager = match ace_core::tls::TlsManager::new(ace_db_app).await {
                Ok(tls_manager) => tls_manager,
                Err(e) => {
                    return Err(granite::from_error_stack!(e));
                }
            };

            for purpose in ["wcpvt", "wcpub"] {
                let cert_gk =
                    ace_graph::TlsCert::Developer(developer.graphkey.clone(), purpose.to_string());
                let certificate = match tls_manager.get_cert(&cert_gk) {
                    Ok(certificate) => certificate,
                    Err(e) => {
                        eprintln!("Error getting developer certificate: {e:#?}");
                        continue;
                    }
                };

                let cert_path = certificate.get_cert_path(ace_core_app);
                let key_path = certificate.get_key_path(ace_core_app);

                let cert = match tokio::fs::read_to_string(&cert_path).await {
                    Ok(cert) => cert,
                    Err(e) => {
                        eprintln!("Error loading developer certificate: {e:#?}");
                        continue;
                    }
                };

                let key = match tokio::fs::read_to_string(&key_path).await {
                    Ok(key) => key,
                    Err(e) => {
                        eprintln!("Error loading developer key: {e:#?}");
                        continue;
                    }
                };

                pem_pairs.push(granite::json!({
                    "Certificate": cert,
                    "Key": key,
                }));
            }
        }

        let ace_legacy_auth = match developer.ace_legacy_auth {
            Some(ace_legacy_auth) => ace_legacy_auth,
            None => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .set_external_message(
                        "Developer did not have an `ace_legacy_auth` option in their config"
                            .to_string(),
                    ));
            }
        };

        // reach out to ace.acp7.net to get the config in json
        // LUKE: this is not trusting certificates
        let url = format!(
            "https://{}@ace.acp7.net/ACP7/Config?ACP7_App_GSIL={}",
            &ace_legacy_auth, qs.ACP7_App_GSIL
        );

        let client = reqwest::Client::builder()
            .danger_accept_invalid_certs(true)
            .build()
            .expect("0x3892346298323234");

        let response = client.get(&url).send().await?;
        match response.status() {
            reqwest::StatusCode::OK => {}
            _ => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .set_external_message(format!("Could not get remote config from {url}")));
            }
        }

        let mut rval = response.json::<serde_json::Value>().await?;

        let remote = match rval.as_object_mut() {
            Some(remote) => remote,
            None => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .set_external_message("Remote config was not an {} object".to_string()));
            }
        };

        // load the Caddy/LoadPEM section
        let remote_caddy = match remote["Caddy"].as_object_mut() {
            Some(caddy) => caddy,
            None => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .set_external_message(
                        "Could not find Caddy section in remote config".to_string(),
                    ));
            }
        };

        let remote_caddy_loadpem = match remote_caddy["LoadPEM"].as_array_mut() {
            Some(loadpem) => loadpem,
            None => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .set_external_message(
                        "Could not find LoadPEM section in remote config".to_string(),
                    ));
            }
        };

        remote_caddy_loadpem.append(&mut pem_pairs);

        // iterate over the config_map and merge it into the remote config to a 2nd level
        for (app_name, app_config_map) in config_map {
            let remote_app = match remote[&app_name].as_object_mut() {
                Some(remote_app) => remote_app,
                None => {
                    // if the app doesn't exist, create it
                    remote[&app_name] = granite::json!({});
                    remote[&app_name].as_object_mut().unwrap()
                }
            };

            // either get the config key or create it
            let remote_app_config = match remote_app["Config"].as_object_mut() {
                Some(remote_app_config) => remote_app_config,
                None => {
                    remote_app["Config"] = granite::json!({});
                    remote_app["Config"].as_object_mut().unwrap()
                }
            };

            // extend the map with the config_map
            for (key, value) in app_config_map.into_iter() {
                remote_app_config.insert(key, crate::web::ACP7::convert_toml_to_json(value)?);
            }
        }

        Ok(Response::JSON(rval.into()))
    }
}

#[approck::http(GET /ACP7/DevDB/List?App_GSID=String; AUTH None; return JSON|Text;)]
pub mod devdb_list {

    pub async fn request(app: App, qs: QueryString, auth_basic: AuthBasic) -> Result<Response> {
        let (username, password) = auth_basic;
        let app_gsid = qs.App_GSID;

        let developer =
            match ace_server_zero::authenticate_developer(&username, &password, app).await {
                Ok(dev) => dev,
                Err(_e) => {
                    let response = Text {
                        status: approck::StatusCode::UNAUTHORIZED,
                        content: "developer credentials are not valid".to_string(),
                        ..Default::default()
                    };
                    return Ok(Response::Text(response));
                }
            };

        let ace_legacy_auth = match developer.ace_legacy_auth {
            Some(ace_legacy_auth) => ace_legacy_auth,
            None => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .set_external_message(
                        "Developer did not have an `ace_legacy_auth` option in their config"
                            .to_string(),
                    ));
            }
        };

        // reach out to ace.acp7.net to get the config in json
        // LUKE: this is not trusting certificates
        let url = format!(
            "https://{}@ace.acp7.net/ACP7/DevDB/List?App_GSID={}",
            &ace_legacy_auth, app_gsid
        );

        let client = reqwest::Client::builder()
            .danger_accept_invalid_certs(true)
            .build()
            .expect("0x38923462983356434");

        let response = client.get(&url).send().await?;
        match response.status() {
            reqwest::StatusCode::OK => {}
            _ => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .set_external_message(format!(
                        "Could not get remote database list from {url}"
                    )));
            }
        }

        let rval = response.json::<serde_json::Value>().await?;

        Ok(Response::JSON(rval.into()))
    }
}

#[approck::http(POST /ACP7/DevDB/Rename?App_GSID=String; AUTH None; return JSON|Text;)]
pub mod devdb_rename {

    #[granite::gtype(RsType, RsTypeDecode, RsTypeEncode)]
    struct PostJson {
        SourceDatabase: String,
    }

    pub async fn request(
        app: App,
        qs: QueryString,
        auth_basic: AuthBasic,
        post: PostJson,
    ) -> Result<Response> {
        use granite::GTypeEncode;

        let (username, password) = auth_basic;
        let app_gsid = qs.App_GSID;

        let developer =
            match ace_server_zero::authenticate_developer(&username, &password, app).await {
                Ok(dev) => dev,
                Err(_e) => {
                    let response = Text {
                        status: approck::StatusCode::UNAUTHORIZED,
                        content: "developer credentials are not valid".to_string(),
                        ..Default::default()
                    };
                    return Ok(Response::Text(response));
                }
            };

        let ace_legacy_auth = match developer.ace_legacy_auth {
            Some(ace_legacy_auth) => ace_legacy_auth,
            None => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .set_external_message(
                        "Developer did not have an `ace_legacy_auth` option in their config"
                            .to_string(),
                    ));
            }
        };

        // reach out to ace.acp7.net to get the config in json
        // LUKE: this is not trusting certificates
        let url = format!(
            "https://{}@ace.acp7.net/ACP7/DevDB/Rename?App_GSID={}",
            &ace_legacy_auth, app_gsid
        );

        let client = reqwest::Client::builder()
            .danger_accept_invalid_certs(true)
            .build()
            .expect("0x38923462983356434");

        let response = client.post(&url).json(&post.gtype_encode()).send().await?;

        match response.status() {
            reqwest::StatusCode::OK => {}
            _ => {
                return Err(granite::Error::new(granite::ErrorType::Unexpected)
                    .set_external_message(format!("failed to call database rename from {url}")));
            }
        }

        let rval = response.json::<serde_json::Value>().await?;

        Ok(Response::JSON(rval.into()))
    }
}

fn convert_toml_to_json(toml: toml::Value) -> granite::Result<serde_json::Value> {
    Ok(match toml {
        toml::Value::Table(table) => {
            let mut rval = serde_json::Map::new();
            for (key, value) in table {
                rval.insert(key, convert_toml_to_json(value)?);
            }
            serde_json::Value::Object(rval)
        }
        toml::Value::Array(array) => {
            let mut rval = Vec::new();
            for value in array {
                rval.push(convert_toml_to_json(value)?);
            }
            serde_json::Value::Array(rval)
        }
        toml::Value::String(string) => serde_json::Value::String(string),
        toml::Value::Integer(integer) => serde_json::Value::Number(integer.into()),
        toml::Value::Float(float) => match serde_json::Number::from_f64(float) {
            Some(number) => serde_json::Value::Number(number),
            None => serde_json::Value::Null,
        },
        toml::Value::Boolean(boolean) => serde_json::Value::Bool(boolean),
        toml::Value::Datetime(datetime) => serde_json::Value::String(datetime.to_string()),
    })
}

#[approck::http(GET /api/v1/developer-app-config?app=String; AUTH None; return JSON|Text;)]
pub mod config {
    use ace_graph::GraphKeyExt;

    #[derive(serde::Serialize)]
    pub struct TlsResponse {
        pub app_name: String,
        pub tlscerts: Vec<TlsCertKeyPair>,
        pub config: toml::value::Table,
        pub suffix: String,
        pub region: String,
    }

    #[derive(serde::Serialize)]
    pub struct TlsCertKeyPair {
        pub graphkey: String,
        pub cert: String,
        pub key: String,
    }

    pub async fn request(app: App, qs: QueryString, auth_basic: AuthBasic) -> Result<Response> {
        let (username, password) = auth_basic;
        let ace_core_app = app.zero_system().ace_core_app;

        let developer =
            match ace_server_zero::authenticate_developer(&username, &password, app).await {
                Ok(dev) => dev,

                // TODO: handle this error better?
                Err(_e) => {
                    let response = Text {
                        status: approck::StatusCode::UNAUTHORIZED,
                        content: "developer credentials are not valid".to_string(),
                        ..Default::default()
                    };
                    return Ok(Response::Text(response));
                }
            };

        let ace_db_app = &app.zero_system().ace_core_app.ace_db_app;

        // Grab config (for region)
        let config = match ace_graph::config::get(&ace_graph::Config::EtcConfig, ace_db_app).await {
            Ok(config) => config,
            Err(e) => {
                return Err(granite::from_error_stack!(e));
            }
        };

        // Get developer app
        let developer_app = match ace_graph::developer_app::get(
            &ace_graph::DeveloperApp::Db(
                developer.graphkey.clone(),
                ace_graph::App::Db(qs.app.clone()),
            ),
            ace_db_app,
        )
        .await
        {
            Ok(devapps) => devapps,
            Err(e) => {
                return Err(granite::from_error_stack!(e));
            }
        };

        let tls_manager = match ace_core::tls::TlsManager::new(ace_db_app).await {
            Ok(tls_manager) => tls_manager,
            Err(e) => {
                return Err(granite::from_error_stack!(e));
            }
        };

        let mut tlscerts = Vec::new();

        for purpose in ["wcpvt", "wcpub"] {
            let cert_gk =
                ace_graph::TlsCert::Developer(developer.graphkey.clone(), purpose.to_string());
            let certificate = match tls_manager.get_cert(&cert_gk) {
                Ok(certificate) => certificate,
                Err(e) => {
                    eprintln!("Error getting developer certificate: {e:#?}");
                    continue;
                }
            };

            let cert_path = certificate.get_cert_path(ace_core_app);
            let key_path = certificate.get_key_path(ace_core_app);

            let cert = match tokio::fs::read_to_string(&cert_path).await {
                Ok(cert) => cert,
                Err(e) => {
                    eprintln!("Error loading developer certificate: {e:#?}");
                    continue;
                }
            };

            let key = match tokio::fs::read_to_string(&key_path).await {
                Ok(key) => key,
                Err(e) => {
                    eprintln!("Error loading developer key: {e:#?}");
                    continue;
                }
            };

            tlscerts.push(TlsCertKeyPair {
                graphkey: cert_gk.serialize_dashed(),
                cert,
                key,
            });
        }

        let tls_response = TlsResponse {
            app_name: qs.app,
            tlscerts,
            config: developer_app.conf_merged.clone(),
            suffix: format!(".{}", developer.public_hostname),
            region: config.region,
        };

        let rval = serde_json::to_string(&tls_response)?;

        Ok(Response::JSON(rval.into()))
    }
}

use ace_types::Message_a2s;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::IpAddr;
use tokio::sync::RwLock;

pub trait App {
    fn zero_system(&self) -> &ZeroSystem;
}

pub trait Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}

pub type UUID = String;
pub type AgentId = usize;
pub type ReplyAgentMap = HashMap<AgentId, Message_a2s>;
pub type BroadcastChannel = tokio::sync::broadcast::Sender<String>;

pub struct ZeroSystem {
    pub ace_core_app: &'static ace_core::Application,
    pub bad_conn_list: RwLock<Vec<BadConnection>>,
    pub connected_agent_list: RwLock<HashMap<ace_graph::Host, tokio::sync::mpsc::Sender<String>>>,

    /// This is a tuple of (broadcast channel, number-of-listening-clients) that will be used to send updates to the websocket dashboard
    /// When the number of listening clients is 0, it will be set to None
    pub dashboard_broadcast_state: RwLock<Option<(BroadcastChannel, i32)>>,

    pub host_states: RwLock<HashMap<ace_graph::Host, HostInfo>>,
}

impl ZeroSystem {
    pub async fn update_bad_conn_list(
        &self,
        gk_string: String,
        // ip_addr: std::net::IpAddr,
        reason: BadConnectionReason,
    ) {
        // For now, set ip_addr to "zero" (TODO - get actual IP address in future)
        let ip_addr = std::net::IpAddr::V4(std::net::Ipv4Addr::new(0, 0, 0, 0));
        let timestamp = chrono::Utc::now().timestamp();

        {
            let mut bad_conn_list = self.bad_conn_list.write().await;
            bad_conn_list.push(BadConnection {
                gk_string,
                ip_addr,
                timestamp,
                reason,
            });
        }
    }

    pub fn init() -> ZeroSystem {
        let ace_core_app = match ace_core::get_static_app() {
            Ok(app) => app,
            Err(e) => {
                panic!("Error getting static app: {e:#?}");
            }
        };

        ZeroSystem {
            bad_conn_list: RwLock::new(Vec::new()),
            connected_agent_list: RwLock::new(HashMap::new()),
            dashboard_broadcast_state: RwLock::new(None),
            host_states: RwLock::new(HashMap::new()),
            ace_core_app,
        }
    }
}

pub struct AgentChannelListener {
    pub graphkey: ace_graph::Host,
    pub listener: tokio::sync::mpsc::Receiver<String>,
}

pub type DashboardListener = tokio::sync::broadcast::Receiver<String>;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct HostInfo {
    pub host_gk: String,
    pub last_known_status: HostState,
    pub last_msg_timestamp: i64,
    pub last_msg_id: UUID,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum HostState {
    Ok(ace_types::SystemInfo),
    Error(String),
    Unknown, // Only for brand new entries that haven't been sent any real instructions yet
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BadConnection {
    pub gk_string: String,
    pub ip_addr: IpAddr,
    pub timestamp: i64,
    pub reason: BadConnectionReason,
}

impl BadConnection {
    pub fn new(gk_string: String, ip: Option<IpAddr>, reason: BadConnectionReason) -> Self {
        let ip_addr = match ip {
            Some(ip) => ip,
            None => std::net::IpAddr::V4(std::net::Ipv4Addr::new(0, 0, 0, 0)),
        };

        Self {
            gk_string,
            ip_addr,
            timestamp: chrono::Utc::now().timestamp(),
            reason,
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum BadConnectionReason {
    DuplicateHost,
    InvalidGraphKey(String),
    UnknownHost(String),
    LockingIssue(String), // This may need tweaked.
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ClientMessage {
    pub msg_id: String,
    pub update: ClientUpdate,
    pub timestamp: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ClientUpdate {
    BadConnection(BadConnection),
    HostState(HostInfo),
}

pub async fn authenticate_developer(
    username: &str,
    password: &str,
    app: &impl crate::App,
) -> granite::Result<ace_graph::developer::Developer> {
    // Get current developer (based off login information)
    let developer = match ace_graph::developer::get(
        &ace_graph::Developer::Db(username.to_string()),
        &app.zero_system().ace_core_app.ace_db_app,
    )
    .await
    {
        Ok(developer) => developer,
        Err(e) => {
            return Err(approck::Error::new(approck::ErrorType::Authorization)
                .add_context(format!("{e:#?}")));
        }
    };

    // check the password
    if developer.secret != password {
        return Err(approck::Error::new(approck::ErrorType::Authorization)
            .add_context("User not authorized!"));
    }

    Ok(developer)
}

[package]
name = "ace-agent"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
# indirect dependencies
git2 = { workspace = true, features = ["vendored-openssl"] }

# direct dependencies
ace-proc = { path = "../../core/ace-proc" }
ace-types = { path = "../../shared/ace-types" }
garbage = { path = "../../shared/garbage"}

async-trait = { workspace = true }
approck = { workspace = true }
bytes = { workspace = true }
chrono = { workspace = true }
clap = { workspace = true, features = ["derive"] }
error-stack = { workspace = true }
futures-util = { workspace = true }
hostname.workspace = true
iso8601-timestamp = { workspace = true }
json = { workspace = true }
reqwest = { workspace = true, features = ["json"] }
rustls = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json.workspace = true
sysinfo = { workspace = true }
tempfile = { workspace = true }
tokio = { workspace = true, features = ["full"] }
tokio-tungstenite = { workspace = true, features = ["rustls-tls-webpki-roots"] }
toml.workspace = true
tracing-subscriber = { workspace = true, features = ["env-filter"] }
tracing.workspace = true
tungstenite = { workspace = true }
uuid = { workspace = true, features = ["v4"] }

use ace_types::{SystemInfo, SystemInfo_a2s, SystemInfo_s2a};
use sysinfo::System;
use tokio::sync::mpsc::unbounded_channel;

use crate::JobConnector;
use ace_types::Message_a2s;

type MessageId = String;
type Timestamp = i64;

/// returns (sender_s2a, receiver_a2s)
/// sender_s2a is used to send messages into this process
/// receiver_a2s is used to receive messages from this process
pub(crate) async fn run(
    _app: &'static crate::App,
) -> JobConnector<(SystemInfo_s2a, MessageId, Timestamp), Message_a2s> {
    let (sender_s2a, mut receiver_s2a) = unbounded_channel();
    let (sender_a2s, receiver_a2s) = unbounded_channel();

    let rval = JobConnector {
        tx_s2a: sender_s2a,
        rx_a2s: receiver_a2s,
        tx_a2s: sender_a2s.clone(), // this is to be able to re-queue messages
    };

    tokio::spawn(async move {
        loop {
            match receiver_s2a.recv().await {
                Some((system_check_info, msg_id, ts)) => {
                    let reply_msg = get_system_info(system_check_info, msg_id, ts).await;
                    match sender_a2s.send(reply_msg) {
                        Ok(_) => (),
                        Err(e) => {
                            println!("Error sending task message to central agent: {e:?}")
                        }
                    };
                }
                None => {
                    println!("System info channel closed");
                    break;
                }
            }
        }
    });

    rval
}

async fn get_system_info(
    input: SystemInfo_s2a,
    msg_id: MessageId,
    timestamp: Timestamp,
) -> ace_types::Message_a2s {
    println!("Getting system info with input: {input:?}");

    // Get ALL the info instead:
    let mut sysinfo = sysinfo::System::new_all();
    sysinfo.refresh_all();

    // Get root disk info
    let root_disk_path = std::path::Path::new("/");
    let disks = sysinfo::Disks::new_with_refreshed_list();

    // First assume not-found
    let mut root_disk_info =
        ace_types::DiskSpaceCheck_a2s::Error("Root disk not found".to_string());

    // Modify if found:
    for disk in disks.list() {
        if disk.mount_point() == root_disk_path {
            root_disk_info = ace_types::DiskSpaceCheck_a2s::Ok {
                total_space: disk.total_space(),
                free_space: disk.available_space(),
            };
        }
    }

    let system_info = SystemInfo_a2s::Ok(SystemInfo {
        root_disk: root_disk_info,
        total_memory: sysinfo.total_memory(),
        used_memory: sysinfo.used_memory(),
        system_name: System::name(),
        system_kernel_version: System::kernel_version(),
        system_os_version: System::os_version(),
    });

    ace_types::Message_a2s {
        msg_id,
        job: ace_types::Job_a2s::SystemInfo(system_info),
        timestamp,
        msg_type: ace_types::Message_Type::Reply,
    }
}

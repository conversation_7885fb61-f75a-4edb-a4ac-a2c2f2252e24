#[derive(Debug)]
pub struct Key {
    pub graphkey: String,
    pub key: String,
}

pub async fn enumerate_keys() -> granite::Result<Vec<Key>> {
    let mut keys = Vec::new();

    // Read from ~/.ssh/authorized_keys
    let authorized_keys_file = std::fs::read_to_string("~/.ssh/authorized_keys")?;

    // authorized_keys format notes: (parsing is based off of these assumptions/rules)
    // Keys should be separated by an empty line.
    // There should be a # comment at the beginning of each key.
    // The key name should be after the last "== " characters.
    // The comment line should be the graphkey

    // Pull out each chunk of lines that represents a key
    let str_keys: Vec<&str> = authorized_keys_file.split("\n\n").collect();

    for key in str_keys {
        let graphkey = match key.lines().next() {
            Some(line) => {
                if !line.starts_with('#') {
                    return Err(granite::Error::new(granite::ErrorType::Unexpected).add_context(format!("Error parsing authorized_keys file: Key does not start with a comment: {key:?}")));
                }
                line.trim_start_matches("# ")
            }
            None => {
                return Err(
                    granite::Error::new(granite::ErrorType::Unexpected).add_context(format!(
                        "Error parsing authorized_keys file: No graphkey found for key: {key:?}"
                    )),
                );
            }
        };

        // make sure the graphkey isn't empty:
        if graphkey.is_empty() {
            return Err(
                granite::Error::new(granite::ErrorType::Unexpected).add_context(format!(
                    "Error parsing authorized_keys file: Graphkey is empty: {key:?}"
                )),
            );
        }

        // The rest of the lines are the key contents
        let key_contents = key.lines().skip(1).collect::<Vec<&str>>().join("\n");

        keys.push(Key {
            graphkey: graphkey.to_string(),
            key: key_contents.to_string(),
        });
    }

    Ok(keys)
}

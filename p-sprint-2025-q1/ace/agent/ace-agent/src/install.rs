use error_stack::ResultExt;
use garbage::CNSL;

#[derive(Debug)]
pub enum ErrorStack {
    DirCreate,
    GetCurrentExecutable,
    DeleteCurrentExecutable,
    CopyCurrentExecutable,
    TempFileCreate,
    TempFileWrite,
    TempFileCopy,
    SystemCtlEnable,
    SystemCtlRestart,
}
impl error_stack::Context for ErrorStack {}
impl std::fmt::Display for ErrorStack {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "{self:?}")
    }
}

pub async fn run(app: &crate::App) -> error_stack::Result<(), ErrorStack> {
    // make directories
    tokio::fs::create_dir_all(&app.data_dir)
        .await
        .change_context(ErrorStack::DirCreate)?;

    // Copy the current executable into the bin dir and make it executable
    let current_executable =
        std::env::current_exe().change_context(ErrorStack::GetCurrentExecutable)?;

    // if current executable exists, delete it first to prevent file busy issues
    if app.executable_path.exists() {
        tokio::fs::remove_file(&app.executable_path)
            .await
            .change_context(ErrorStack::DeleteCurrentExecutable)?;
    }

    tokio::fs::copy(&current_executable, &app.executable_path)
        .await
        .change_context(ErrorStack::CopyCurrentExecutable)?;

    // set executable permission
    let cmdstatus = tokio::process::Command::new("chmod")
        .arg("+x")
        .arg(&app.executable_path)
        .status()
        .await
        .change_context(ErrorStack::CopyCurrentExecutable)?;

    error_stack::ensure!(cmdstatus.success(), ErrorStack::CopyCurrentExecutable);

    // build the systemd file
    #[rustfmt::skip]
    let systemd_unit = CNSL!(r#"
        [Unit]
        Description=ace agent runtime to run in background
        After=network.target

        [Service]
        User=root
        WorkingDirectory="#, app.data_dir.to_string_lossy(), r#"
        ExecStart="#, app.executable_path.to_string_lossy(), r#" --config "#, app.config_path.to_string_lossy(), r#" run
        Restart=always
        RestartSec=5
        
        [Install]
        WantedBy=multi-user.target
    "#);

    let tmp_path = tempfile::NamedTempFile::new()
        .change_context(ErrorStack::TempFileCreate)?
        .into_temp_path();

    tokio::fs::write(&tmp_path, systemd_unit)
        .await
        .change_context(ErrorStack::TempFileWrite)?;

    let cmdstatus = tokio::process::Command::new("cp")
        .arg(&tmp_path)
        .arg(&app.systemd_unit_path)
        .status()
        .await
        .change_context(ErrorStack::TempFileCopy)?;

    error_stack::ensure!(cmdstatus.success(), ErrorStack::TempFileCopy);

    // Enable service
    let cmdstatus = tokio::process::Command::new("systemctl")
        .arg("enable")
        .arg("ace-agent.service")
        .status()
        .await
        .change_context(ErrorStack::SystemCtlEnable)?;

    error_stack::ensure!(cmdstatus.success(), ErrorStack::SystemCtlEnable);

    // Start service
    let cmdstatus = tokio::process::Command::new("systemctl")
        .arg("restart")
        .arg("ace-agent.service")
        .status()
        .await
        .change_context(ErrorStack::SystemCtlRestart)?;

    error_stack::ensure!(cmdstatus.success(), ErrorStack::SystemCtlRestart);

    println!(" ... done!");

    Ok(())
}

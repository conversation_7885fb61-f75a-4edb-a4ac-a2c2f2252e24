use error_stack::ResultExt;

#[derive(Debug)]
pub enum ErrorStack {
    RemoveWorkDir,
    SystemCtlDisable,
    SystemCtlStop,
    RemoveServiceFile,
    SystemCtlReloadDaemon,
}

impl error_stack::Context for ErrorStack {}
impl std::fmt::Display for ErrorStack {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "{self:?}")
    }
}

pub async fn run(app: &crate::App, remove_data_dir: bool) -> error_stack::Result<(), ErrorStack> {
    let mut has_errors = false;

    if remove_data_dir {
        // Remove the work dir
        let r = tokio::fs::remove_dir_all(&app.data_dir)
            .await
            .change_context(ErrorStack::RemoveWorkDir);

        if let Err(e) = r {
            has_errors = true;
            eprintln!("Failed to remove data dir: {e:?}; attempting to continue.");
        }
    }

    // Disable service
    let cmdstatus = tokio::process::Command::new("systemctl")
        .arg("disable")
        .arg("ace-agent.service")
        .status()
        .await
        .change_context(ErrorStack::SystemCtlDisable)?;

    if !cmdstatus.success() {
        has_errors = true;
        eprintln!("Failed to disable service... attempting to continue.");
    }

    // Stop service
    let cmdstatus = tokio::process::Command::new("systemctl")
        .arg("stop")
        .arg("ace-agent.service")
        .status()
        .await
        .change_context(ErrorStack::SystemCtlStop)?;

    if !cmdstatus.success() {
        has_errors = true;
        eprintln!("Failed to stop service... attempting to continue.");
    }

    // Remove the systemd unit file
    let r = tokio::fs::remove_file(&app.systemd_unit_path)
        .await
        .change_context(ErrorStack::RemoveServiceFile);

    if let Err(e) = r {
        has_errors = true;
        eprintln!("Failed to remove service file: {e:?}; attempting to continue.");
    }

    // Reload the daemon
    let cmdstatus = tokio::process::Command::new("systemctl")
        .arg("daemon-reload")
        .status()
        .await
        .change_context(ErrorStack::SystemCtlReloadDaemon)?;

    if !cmdstatus.success() {
        has_errors = true;
        eprintln!("Failed to reload daemon... attempting to continue.");
    }

    if has_errors {
        println!(" ... service uninstalled with errors!");
    } else {
        println!(" ... service uninstalled successfully!");
    }

    Ok(())
}

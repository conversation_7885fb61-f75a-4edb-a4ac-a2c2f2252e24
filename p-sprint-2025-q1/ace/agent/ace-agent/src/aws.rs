use error_stack::ResultExt;
use serde::{Deserialize, Serialize};

#[derive(Serialize, Deserialize, Debug)]
pub struct InstanceIdentityDocument {
    #[serde(rename = "accountId")]
    pub account_id: String,

    pub architecture: String,

    #[serde(rename = "availabilityZone")]
    pub availability_zone: String,

    #[serde(rename = "billingProducts")]
    pub billing_products: Option<Vec<String>>, // Adjust type if necessary

    #[serde(rename = "devpayProductCodes")]
    pub devpay_product_codes: Option<Vec<String>>, // Adjust type if necessary

    #[serde(rename = "marketplaceProductCodes")]
    pub marketplace_product_codes: Option<Vec<String>>, // Adjust type if necessary

    #[serde(rename = "imageId")]
    pub image_id: String,

    #[serde(rename = "instanceId")]
    pub instance_id: String,

    #[serde(rename = "instanceType")]
    pub instance_type: String,

    #[serde(rename = "kernelId")]
    pub kernel_id: Option<String>,

    #[serde(rename = "pendingTime")]
    pub pending_time: iso8601_timestamp::Timestamp, // Assuming it's a string in ISO 8601 format

    #[serde(rename = "privateIp")]
    pub private_ip: String,

    #[serde(rename = "ramdiskId")]
    pub ramdisk_id: Option<String>,

    pub region: String,

    pub version: String,
}

#[derive(Debug)]
pub enum ErrorStack {
    ReqwestBuilder,
    MetaDataRequest,
}

impl error_stack::Context for ErrorStack {}
impl std::fmt::Display for ErrorStack {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        write!(f, "{self:?}")
    }
}

pub async fn get_instance_identity_document()
-> error_stack::Result<InstanceIdentityDocument, ErrorStack> {
    let resp = reqwest::ClientBuilder::new()
        .timeout(std::time::Duration::from_secs(1))
        .build()
        .change_context(ErrorStack::ReqwestBuilder)?
        .get("http://***************/2022-09-24/dynamic/instance-identity/document")
        .send()
        .await
        .change_context(ErrorStack::MetaDataRequest)?;

    let data: InstanceIdentityDocument = resp
        .json()
        .await
        .change_context(ErrorStack::MetaDataRequest)?;

    Ok(data)
}

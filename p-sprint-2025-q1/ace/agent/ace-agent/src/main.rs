use std::path::PathBuf;

use clap::{Parser, Subcommand};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

/// A fictional versioning CLI
#[derive(Debug, Parser)] // requires `derive` feature
#[command(name = "ace-agent")]
#[command(about = "An agent to manage systems connected to ace-server", long_about = None)]
struct Cli {
    #[clap(long, default_value = "/opt/ace-agent/etc/ace-agent.toml")]
    config: PathBuf,

    #[command(subcommand)]
    command: Commands,
}

#[derive(Debug, Subcommand)]
enum Commands {
    #[clap(about = "Print the version")]
    Version,

    #[clap(about = "Install current executable, setup systemd, start service")]
    Install,

    #[clap(about = "Upgrade to new binary fetched from ace-server")]
    Upgrade,

    #[clap(about = "Uninstall itself")]
    Uninstall(UninstallOpts),

    #[clap(about = "Show the current config")]
    Config,

    #[clap(about = "Run the agent")]
    Run(RunOpts),
}

#[derive(Debug, Parser)]
pub struct RunOpts {
    simulated_graphkey: Option<String>,
}

#[derive(Debug, Parser)]
pub struct UninstallOpts {
    // add a --remove options
    #[clap(long)]
    remove_work_dir: bool,
}

pub fn main() {
    tracing_subscriber::Registry::default()
        .with(
            tracing_subscriber::EnvFilter::builder()
                .with_default_directive(tracing_subscriber::filter::LevelFilter::INFO.into())
                .from_env_lossy(),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    let cli = Cli::parse();

    let config = match ace_agent::config::load_config(&cli.config) {
        Ok(config) => config,
        Err(e) => {
            println!(
                "Failed to load config file: {}\n\n{:#?}",
                cli.config.display(),
                e
            );
            std::process::exit(1);
        }
    };

    match rustls::crypto::aws_lc_rs::default_provider().install_default() {
        Ok(_) => println!("Installed default provider"),
        Err(e) => eprintln!("Error installing default provider: {e:#?}"),
    };

    // Strip https off ace_url HERE
    let wss_url = match config.ace_url.strip_prefix("https://") {
        None => {
            eprintln!(
                "Config option `ace_url` must start with `https://`: `{}`",
                config.ace_url
            );
            std::process::exit(1);
        }
        Some(url) if url.ends_with('/') => {
            eprintln!(
                "Config option `ace_url` must not end with `/`: `{}`",
                config.ace_url
            );
            std::process::exit(1);
        }
        Some(url) => {
            format!("wss://{url}/ace-agent/websocket?gk={}", config.graph_key)
        }
    };

    let app: &'static ace_agent::App = Box::leak(Box::new(ace_agent::App {
        config_path: cli.config,
        data_dir: config.data_dir,
        executable_path: config.executable_path,
        systemd_unit_path: config.systemd_unit_path,
        hostname: config.hostname,
        ace_url: config.ace_url,
        wss_url,
        graph_key: config.graph_key,
        ed25519_public_key: config.ed25519_public_key,
    }));

    // Create a multi-threaded Tokio runtime
    let runtime = tokio::runtime::Builder::new_multi_thread()
        .enable_io()
        .enable_time()
        .build()
        .expect("Failed to build Tokio runtime");

    match cli.command {
        Commands::Version => {
            println!("{}", config.ace_agent_version);
        }
        Commands::Install => runtime.block_on(async {
            let resp = ace_agent::install::run(app).await;

            match resp {
                Ok(_) => println!("Install complete"),
                Err(e) => println!("Install failed due to:\n{e:?}"),
            }
        }),
        Commands::Upgrade => runtime.block_on(async {
            let resp = ace_agent::upgrade::run(app).await;

            match resp {
                Ok(_) => println!("Upgrade complete"),
                Err(e) => println!("Upgrade failed due to:\n{e:?}"),
            }
        }),
        Commands::Uninstall(uninstallopts) => runtime.block_on(async {
            let resp = ace_agent::uninstall::run(app, uninstallopts.remove_work_dir).await;

            match resp {
                Ok(_) => println!("Uninstall complete"),
                Err(e) => println!("Uninstall failed due to:\n{e:?}"),
            }
        }),
        Commands::Config => {
            println!("{app:#?}");
        }
        Commands::Run(opts) => {
            // Run the main async block on the Tokio runtime
            runtime.block_on(ace_agent::run(app, opts.simulated_graphkey));
        }
    }
}

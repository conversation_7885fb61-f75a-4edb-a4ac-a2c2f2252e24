use error_stack::ResultExt;
use serde::Deserialize;
use std::io::Read;
use std::path::{Path, PathBuf};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    OpeningConfigFile,
    ReadingConfigFile,
    ParsingConfig,
    GetHostname,
    HostnameToString,
    ReadSshHostEd25591KeyPub,
}

#[derive(Debug, serde::Deserialize)]
pub struct SerdeConfig {
    pub ace_agent_version: String,
    pub ace_server: String,
    pub architecture: String,
    pub data_dir: Option<PathBuf>,
    pub graph_key: String,
    pub hostname: Option<String>,
    pub os: String,
    pub systemd_unit_path: Option<PathBuf>,
}

#[derive(Debug, Deserialize)]
pub struct AgentRuntimeConfig {
    pub ace_agent_version: String,
    pub ace_url: String,
    pub archictecture: String,
    pub data_dir: PathBuf,
    pub executable_path: PathBuf,
    pub ed25519_public_key: String,
    pub graph_key: String,
    pub hostname: String,
    pub os: String,
    pub systemd_unit_path: PathBuf,
}

pub fn load_config(config_path: &Path) -> error_stack::Result<AgentRuntimeConfig, ErrorStack> {
    // load config file and read toml
    let mut config_file =
        std::fs::File::open(config_path).change_context(ErrorStack::OpeningConfigFile)?;

    let mut config_string = String::new();

    config_file
        .read_to_string(&mut config_string)
        .change_context(ErrorStack::ReadingConfigFile)?;

    let serde_config: SerdeConfig =
        toml::from_str(&config_string).change_context(ErrorStack::ParsingConfig)?;

    let ace_url = format!("https://{}", serde_config.ace_server);
    let graph_key = serde_config.graph_key;
    let data_dir = serde_config
        .data_dir
        .unwrap_or_else(|| PathBuf::from("/var/lib/ace-agent"));

    let hostname = hostname::get()
        .change_context(ErrorStack::GetHostname)?
        .to_string_lossy()
        .to_string();

    let ed25519_public_key =
        std::fs::read_to_string(std::path::Path::new("/etc/ssh/ssh_host_ed25519_key.pub"))
            .change_context(ErrorStack::ReadSshHostEd25591KeyPub)?;

    let systemd_unit_path = serde_config
        .systemd_unit_path
        .unwrap_or_else(|| PathBuf::from("/etc/systemd/system/ace-agent.service"));

    let config = AgentRuntimeConfig {
        ace_agent_version: serde_config.ace_agent_version.clone(),
        ace_url,
        archictecture: serde_config.architecture,
        data_dir,
        ed25519_public_key,
        executable_path: PathBuf::from(format!(
            "/opt/ace-agent/bin/{}",
            serde_config.ace_agent_version
        )),
        graph_key,
        hostname,
        os: serde_config.os,
        systemd_unit_path,
    };

    Ok(config)
}

#[derive(serde::Deserialize, serde::Serialize, Debug)]
pub struct PollRequestAws {
    pub account_id: String,
    pub instance_id: String,
    pub instance_type: String,
    pub region: String,
    pub availability_zone: String,
    pub private_ip: String,
    pub launch_time: iso8601_timestamp::Timestamp,
}

pub async fn some_func() {
    let aws = match crate::aws::get_instance_identity_document()
    .await
    .change_context(ErrorStack::GetInstanceIdentityDocument)
{
    Ok(doc) => Some(PollRequestAws {
        account_id: doc.account_id,
        instance_id: doc.instance_id,
        instance_type: doc.instance_type,
        region: doc.region,
        availability_zone: doc.availability_zone,
        private_ip: doc.private_ip,
        launch_time: doc.pending_time,
    }),
    Err(e) => {
        tracing::warn!("Error getting instance identity document:\n{:?}", e);
        log.push(PollRequestLog {
            ts: iso8601_timestamp::Timestamp::now_utc(),
            message: format!("Error getting instance identity document:\n{:?}", e),
        });
        None
    }
};

}
pub mod system_stats;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct Action {
    pub action_gsid: String,
    pub create_time: iso8601_timestamp::Timestamp,
    pub action_type: ActionType,
}

pub fn create(action_type: ActionType) -> Action {
    Action {
        action_gsid: uuid::Uuid::new_v4().to_string(),
        create_time: iso8601_timestamp::Timestamp::now_utc(),
        action_type,
    }
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub enum ActionType {
    NoOp,
}

pub async fn ingest(actions: Vec<Action>) {
    tracing::info!("ingest action {:?}", actions);
}

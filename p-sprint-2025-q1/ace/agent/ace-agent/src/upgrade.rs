use error_stack::ResultExt; // Provided by error-stack
use std::os::unix::fs::PermissionsExt;
use std::path::PathBuf;
use tokio::io::AsyncWriteExt; // Provided by tokio

#[derive(Debug)]
pub enum ErrorStack {
    HttpDownload,
    TempFileCreate,
    TempFileClose,
    TempFileWrite,
    TempFileFlush,
    TempFileSync,
    MetaDataGet,
    MetaDataSet,
    MakeExecutable,
    ExecuteScript,
}

impl error_stack::Context for ErrorStack {}
impl std::fmt::Display for ErrorStack {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{self:?}")
    }
}

pub async fn run(app: &crate::App) -> error_stack::Result<(), ErrorStack> {
    let url = format!("{}/ace-agent/download", &app.ace_url);

    // Download the script with streaming
    let mut response = reqwest::get(&url)
        .await
        .change_context(ErrorStack::HttpDownload)?
        .error_for_status()
        .change_context(ErrorStack::HttpDownload)?;

    // Create a temporary file
    let temp_path = PathBuf::from("/tmp/ace-agent");

    // Open and download to it
    {
        let mut async_temp_file = tokio::fs::File::create(&temp_path)
            .await
            .change_context(ErrorStack::TempFileCreate)?;

        while let Some(chunk) = response
            .chunk()
            .await
            .change_context(ErrorStack::HttpDownload)?
        {
            async_temp_file
                .write_all(&chunk)
                .await
                .change_context(ErrorStack::TempFileWrite)?;
        }

        async_temp_file
            .flush()
            .await
            .change_context(ErrorStack::TempFileFlush)?;

        async_temp_file
            .sync_all()
            .await
            .change_context(ErrorStack::TempFileSync)?;
    }

    // Make the script executable
    {
        let metadata = tokio::fs::metadata(&temp_path)
            .await
            .change_context(ErrorStack::MetaDataGet)?;

        let mut permissions = metadata.permissions();
        permissions.set_mode(0o755); // rw for owner, r for group and others

        tokio::fs::set_permissions(&temp_path, permissions)
            .await
            .change_context(ErrorStack::MetaDataSet)?;
    }

    // Execute the script
    let status = tokio::process::Command::new(&temp_path)
        .arg("--config-path")
        .arg(&app.config_path)
        .arg("install")
        .status()
        .await
        .change_context(ErrorStack::ExecuteScript)?;

    error_stack::ensure!(status.success(), ErrorStack::ExecuteScript);

    // Manual cleanup of the temporary file
    tokio::fs::remove_file(temp_path)
        .await
        .change_context(ErrorStack::TempFileWrite)?;

    Ok(())
}

use clap::{Parser, Subcommand};
use std::path::PathBuf;

/// A fictional versioning CLI
#[derive(Debug, Parser)] // requires `derive` feature
#[command(name = "ace-agent-updater")]
#[command(about = "An agent to manage ace-agent versions", long_about = None)]
struct Cli {
    #[clap(long, default_value = "/opt/ace-agent/etc/ace-agent-updater.toml")]
    config: PathBuf,

    #[command(subcommand)]
    command: Commands,
}

#[derive(Debug, Subcommand)]
enum Commands {
    #[clap(about = "Print the commit information at the time this binary was built")]
    AceCommitVersion,

    #[clap(about = "Run me")]
    Run,
}

#[derive(Debug, serde::Deserialize)]
pub struct AceAgentConfig {
    pub ace_agent_updater_version: String,
    pub data_dir: Option<PathBuf>,
    pub systemd_unit_path: Option<PathBuf>,
}

pub fn main() {
    let cli = Cli::parse();

    match cli.command {
        Commands::AceCommitVersion => {
            println!("{}", include_str!("../../../../target/version"));
        }
        Commands::Run => {
            loop {
                // Read from config file (path comes from cli)
                let config_file = match std::fs::read_to_string(&cli.config) {
                    Ok(c) => c,
                    Err(e) => {
                        eprintln!("Error reading config file: {e}");
                        std::thread::sleep(std::time::Duration::from_secs(10));
                        continue;
                    }
                };

                // Deserialize config file
                let config: AceAgentConfig = match toml::from_str(&config_file) {
                    Ok(c) => c,
                    Err(e) => {
                        eprintln!("Error deserializing config file: {e}");
                        std::thread::sleep(std::time::Duration::from_secs(10));
                        continue;
                    }
                };

                println!(
                    "I'm ace-agent-updater version: {}",
                    config.ace_agent_updater_version
                );
                std::thread::sleep(std::time::Duration::from_secs(10));
            }
        }
    }
}

//////////////////////////////////
//! # **ABOUT ACE-DB**:
//! Responsible for collecting merged lists of information from local and external sources for the purposes of (mostly) ace-graph.
//! (Actual interaction with databases not yet implemented)
//!
//! ## Crate layout:
//! - Files in ace-db/src are for merging information from (future) external databases AND
//!   locally defined acctkey@region/etc files.
//!
//! - Files within ace-db::etc, ace-db::data, ace-db::asset are specifically for getting/parsing information from the local
//!   acctkey@region files.  The functions therein are (for the most part) private and can only be accessed by the public
//!   functions in ace-db/src.
//////////////////////////////////

pub mod ami;
pub mod app;
pub mod asset;
pub mod aws_account;
pub mod aws_vpc_sg;
pub mod brdst;
pub mod brsrc;
pub mod data;
pub mod developer;
pub mod docker;
pub mod domain;
pub mod ecr;
pub mod ecr_public;
pub mod etc;
pub mod host;
pub mod iam_role;
pub mod instance;
pub mod keypair;
pub mod local_asset;
pub mod mediaproctor;
pub mod mysqldevimg;
pub mod packer;
pub mod peercon;
pub mod user;
pub mod vpn;

use error_stack::ResultExt;
use etc::account::aws_account::AccountRegion;
use etc::app::AppHosting;
use std::collections::{HashMap, HashSet};
use std::path::PathBuf;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AccountFilePeerconListNotArray(PathBuf),
    AccountRegionMismatch(AccountRegion, AccountRegion),
    AppAppBucketDeveloperAccessMismatch(String, bool, bool),
    AppBucketPublicKeyDoesNotStartWithSlash(String),
    AppHostingMismatch(Option<AppHosting>, Option<AppHosting>),
    AwsAccountIdMismatch(String, String, String),
    CommonNameAndFilenameMismatch(String, PathBuf),
    ConfigIpIsEmptyAndErrorFetchingIp,
    CouldNotFindGitRepo,
    CouldNotLoadPathFromRepo,
    DeserializeUser(toml::de::Error),
    DeveloperInvalidSecretLength(String, usize),
    DirectoryFormatting(String),
    DirectoryMismatchAccountKey(String),
    DirectoryMismatchRegion(String),
    DiscoverGitRepo,
    DomainInvalidSubdomainName(String),
    EtcPackerFiltersEmpty(PathBuf),
    EtcPackerOwnersEmpty(PathBuf),
    ExtractNameError(PathBuf),
    FetchIpFromIpInfo,
    FilenameInvalidNameComponent(PathBuf),
    GetAccountKeyAndRegion,
    GetAccountMap,
    GetDirectoryName(PathBuf),
    GetPeerconList,
    GlobError(glob::GlobError),
    GlobPatternError(glob::PatternError),
    IamRoleInvalidJSON(String),
    InitNotNeededAndConfigFileDoesNotExist,
    InvalidAccountID(String),
    InvalidArn(String),
    InvalidDirectoryName(String),
    InvalidFilename(PathBuf),
    InvalidKey(String),
    InvalidSubnet(String),
    MergeAppInformation(String),
    NameAndFilenameMismatch(String, PathBuf),
    NoAccountTableFound(PathBuf),
    NoRegexCaptures(PathBuf),
    NotOneLatestPackerManifestFound(String),
    NotOnePackerManifestFound(String, String),
    NoUserFound,
    NoUserSectionFound(PathBuf),
    ParseAccountFile(PathBuf),
    ParseAccountToml,
    ParseAccountHashMap(PathBuf),
    ParseAwsMetadataResponse,
    ParseDate,
    ParseDateFromString(String),
    ParseError,
    ParseEtcConfigFile,
    ParseFile(PathBuf),
    ParseIpInfoResponse,
    ParsePeercon(toml::de::Error),
    PeerconNotFound(String),
    ReadAccountFile(PathBuf),
    ReadFile(PathBuf),
    ReadOvpnFile(PathBuf),
    ReadTerraformOutputFile(PathBuf, String),
    RegexCreationError,
    SelectEtcAmis,
    SelectEtcAssets,
    SelectEtcApps,
    SelectEtcBrDsts,
    SelectEtcBrSrcs,
    SelectEtcDevelopers,
    SelectEtcDockers,
    SelectEtcDomains,
    SelectEtcEcrs,
    SelectEtcHosts,
    SelectEtcIamRoles,
    SelectEtcInstances,
    SelectEtcKeypairs,
    SelectEtcMediaproctors,
    SelectEtcMysqlDevImgs,
    SelectEtcPackers,
    SelectEtcPublicEcrs,
    SelectEtcPeercons,
    SelectEtcSecurityGroups,
    UndiscoverableDirectory(String),
    UnflattenAccountTable(PathBuf),
    UserSectionIsNotTable,
    ValidateAccount(String),
    ValidateAppBucketPublicRead,
    VerifyAccountKeyAndRegion(PathBuf),

    // Asset specific variants:
    InvalidAssetFilenameFormat(PathBuf),
    NoAssetArchCapture(PathBuf),
    NoAssetHashCapture(PathBuf),
    NoAssetMajorCapture(PathBuf),
    NoAssetMinorCapture(PathBuf),
    NoAssetNameCapture(PathBuf),
    NoAssetPatchCapture(PathBuf),
    NoAssetRawVersionCapture(PathBuf),
    NoAssetTargetCapture(PathBuf),
    SearchVersionSemverParseError(String),
    SemverParseError(PathBuf, String),

    // User specific variants:
    // (name, origin path as string)
    DuplicateUser(String, String),
    InvalidPrincipalsForUser(String),
    InvalidUserPrincipals(Vec<String>),
    SelectUsersFromAccountToml,
    SelectUserTomlFiles,
}

pub type SelectResult<T, E> =
    error_stack::Result<Vec<error_stack::Result<T, E>>, crate::ErrorStack>;

pub type SelectHashMapResult<T, E> =
    error_stack::Result<HashMap<String, error_stack::Result<T, E>>, crate::ErrorStack>;

#[derive(Debug)]
pub enum Filter<T> {
    All,
    One(T),
    Many(Vec<T>),
    None,
}

pub type PackerName = String;
pub type PackerRunUuid = String;

#[derive(Debug)]
pub enum PackerManifestFilter {
    /// All valid names (provided by ace_graph::pkr.rs)
    All(HashSet<String>),

    /// All versions/builds of one Name
    AllByName(String),

    /// Latest version of single Name
    LatestOneByName(String),

    /// Latest version of each Name
    LatestEachByName(HashSet<String>),
    None,
    One(PackerName, PackerRunUuid),
}

#[derive(Debug)]
pub struct App {
    pub ace_public_key_path: PathBuf,
    pub ace_private_key_path: PathBuf,
    pub asset_path: PathBuf,
    pub data_path: PathBuf,
    pub docker_path: PathBuf,
    pub etc_account_file_path: PathBuf,
    pub etc_config_file_path: PathBuf,
    pub etc_config: etc::config::Config,
    pub etc_path: PathBuf,
    pub my_ip: std::net::Ipv4Addr,
    pub packer_manifest_path: PathBuf,
    pub path: PathBuf,
    pub vpn_path: PathBuf,
    pub secure_mountpoint: PathBuf,
}

impl App {
    #[must_use]
    /// # Panics
    ///
    /// This function will panic under the following circumstances:
    /// - Cannot find git repo.
    /// - Cannot load path from repo.
    /// - Invalid directory name.
    pub fn init() -> Self {
        let path = {
            let orig_cwd = std::env::current_dir().unwrap();
            let mut cwd = orig_cwd.clone();
            loop {
                if cwd.join(".git").exists() {
                    break cwd;
                }
                assert!(cwd.pop(), "Not in a git repo at {}", orig_cwd.display());
            }
        };

        let asset_path = path.join("asset");
        let etc_path = path.join("etc");
        let etc_config_file_path = etc_path.join("config.toml");
        let etc_account_file_path = etc_path.join("account.toml");
        let data_path = path.join("data");
        let docker_path = path.join("docker");
        let packer_manifest_path = data_path.join("packer.manifest.json");
        let vpn_path = path.join("vpn");
        let secure_mountpoint = path.join("secure");

        let ace_public_key_path = data_path.join("ace2_key.pub");
        let ace_private_key_path = secure_mountpoint.join("data/ace2_key");

        // Load config and my_ip
        // Possibly have default method for config?  A lot of the fields are optional, or could be obtained from ace_core::Application when init() is called...
        let (etc_config, my_ip) = match etc::config::sync_get_config_and_ip(&etc_config_file_path) {
            Ok(config) => config,
            Err(e) => {
                panic!("Error loading config:\n{e:#?}")
            }
        };

        Self {
            ace_public_key_path,
            ace_private_key_path,
            asset_path,
            data_path,
            docker_path,
            etc_account_file_path,
            etc_config_file_path,
            etc_config,
            etc_path,
            my_ip,
            packer_manifest_path,
            path,
            vpn_path,
            secure_mountpoint,
        }
    }
}

#[derive(Debug)]
pub struct PathAccountKeyRegion {
    pub path: PathBuf,
    pub account_key: String,
    pub region: String,
}

/// # Errors
///
/// This function will bail under the following circumstances:
/// - Cannot find git repo.
/// - Cannot load path from repo.
/// - Invalid directory name.
/// - Splitting directory name fails.
pub fn current_account_key_and_region() -> error_stack::Result<PathAccountKeyRegion, ErrorStack> {
    let git2_repo =
        git2::Repository::discover(".").change_context(ErrorStack::CouldNotFindGitRepo)?;

    let path = match git2_repo.workdir() {
        Some(path) => path.to_path_buf(),
        None => {
            error_stack::bail!(ErrorStack::CouldNotLoadPathFromRepo)
        }
    };

    let basename = match path.file_name() {
        Some(basename) => basename.to_string_lossy(),
        None => {
            error_stack::bail!(ErrorStack::GetDirectoryName(path.clone()))
        }
    };

    // Bail out if dir does not have @
    if !basename.contains('@') {
        error_stack::bail!(ErrorStack::InvalidDirectoryName(basename.to_string()));
    }

    // split on first -
    let mut parts = basename.splitn(2, '@');
    let account_key = parts.next().unwrap_or("").to_string();
    let region = parts.next().unwrap_or("").to_string();

    Ok(PathAccountKeyRegion {
        path: path.clone(),
        account_key,
        region,
    })
}

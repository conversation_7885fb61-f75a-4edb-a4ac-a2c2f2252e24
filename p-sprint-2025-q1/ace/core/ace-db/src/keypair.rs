use crate::Error<PERSON>tack;
use crate::etc::keypair::KeyPair;
use error_stack::ResultExt;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<KeyPair, ErrorStack> {
    let mut rval = vec![];

    let etc_keypairs = crate::etc::keypair::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcKeypairs)?;

    // TODO: Insert external database
    // ...

    rval.extend(etc_keypairs.into_values());

    Ok(rval)
}

use crate::ErrorStack;
use error_stack::ResultExt;
use glob::glob;
use regex::Regex;
use serde::Deserialize;
use std::collections::HashMap;
use std::path::Path;

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct BrSrc {
    pub name: String,

    pub source: Source,
    pub target: Target,

    #[serde(skip)]
    pub file_source: std::path::PathBuf,
}

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct Source {
    pub bucket: String,
}

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct Target {
    pub bucket: String,
    pub account_key: String,
    pub region: String,
}

/// Selects brsrcs from the etc directory.
///
/// # Errors
///
/// Bails under the following circumstances:
/// - Cannot create glob pattern.
pub(crate) async fn select_result(
    filter_name: crate::Filter<String>,
    ace_db_app: &crate::App,
) -> crate::SelectHashMapResult<BrSrc, ErrorStack> {
    // Use glob to get all the file names, then iterate and call parse on each one
    // etc/brsrc.*.toml

    if matches!(filter_name, crate::Filter::None) {
        return Ok(HashMap::new());
    }

    let pattern = format!("{}/brsrc.*.toml", &ace_db_app.etc_path.to_string_lossy());

    let mut rval = HashMap::new();

    let paths = match glob(&pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(ErrorStack::GlobPatternError(e));
        }
    };

    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                tracing::warn!("Error accessing file entry: {:?}", e);
                continue;
            }
        };

        let brsrc_name = match extract_name(&file) {
            Ok(name) => name,
            Err(e) => {
                tracing::warn!("Invalid filename or entry name: {:?}", e);
                continue;
            }
        };

        if !match &filter_name {
            crate::Filter::All => true,
            crate::Filter::One(filter_name) => brsrc_name == *filter_name,
            crate::Filter::Many(filter_names) => filter_names.contains(&brsrc_name),
            crate::Filter::None => false,
        } {
            continue;
        }

        let brsrc_result = parse_file(&brsrc_name, &file).await;
        rval.insert(brsrc_name, brsrc_result);
    }

    Ok(rval)
}

fn extract_name(path: &Path) -> error_stack::Result<String, ErrorStack> {
    // takes a path and returns a Result<String>
    // path is expected to be a file name like etc/brsrc.name.toml
    // return the name part of the file name

    let re = Regex::new(r"^brsrc\.([a-z][a-z0-9]*(?:-[a-z0-9]+)*)\.toml")
        .change_context(ErrorStack::RegexCreationError)?;

    // peel off the file name from the path
    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::ExtractNameError(path.to_path_buf())),
    };

    match re.captures(&filename) {
        Some(capture) => Ok(capture[1].to_string()),
        None => error_stack::bail!(ErrorStack::FilenameInvalidNameComponent(path.to_path_buf())),
    }
}

async fn parse_file(name: &str, path: &Path) -> error_stack::Result<BrSrc, ErrorStack> {
    let content = tokio::fs::read_to_string(path)
        .await
        .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

    let mut brsrc: BrSrc =
        toml::from_str(&content).change_context_lazy(|| ErrorStack::ParseFile(path.to_owned()))?;

    if name != brsrc.name {
        error_stack::bail!(ErrorStack::NameAndFilenameMismatch(
            brsrc.name,
            path.to_path_buf()
        ));
    }

    brsrc.file_source = path.to_path_buf();

    Ok(brsrc)
}

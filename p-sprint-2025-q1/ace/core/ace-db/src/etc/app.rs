use std::path::Path;

use crate::ErrorStack;
use error_stack::ResultExt;
use glob::glob;
use indexmap::IndexMap;
use regex::Regex;
use serde::Deserialize;
use std::collections::HashMap;

#[derive(Debug, Deserialize, PartialEq, Clone)]
#[serde(deny_unknown_fields)]
pub struct EtcApp {
    pub name: String,
    pub secret: String,

    #[serde(default)]
    pub hosting: Option<AppHosting>,

    #[serde(default)]
    pub preview: Option<AppHosting>,

    #[serde(default)]
    pub conf: toml::value::Table,

    #[serde(default)]
    pub conf_production: toml::value::Table,

    #[serde(default)]
    pub conf_preview: toml::value::Table,

    #[serde(default)]
    pub conf_developer: toml::value::Table,

    #[serde(rename = "bucket", default)]
    pub bucket_map: IndexMap<String, AppBucket>,

    #[serde(default)]
    pub aws_ec_sls: IndexMap<String, AppAwsEcSls>,

    #[serde(default)]
    pub aws_rds_instance: IndexMap<String, AppAwsRds>,

    #[serde(default)]
    pub aws_elb: IndexMap<String, AppAwsElb>,

    #[serde(default)]
    pub aws_ecr: IndexMap<String, AppAwsEcr>,

    #[serde(skip)]
    pub source: std::path::PathBuf,
}

#[derive(Debug, Deserialize, PartialEq, Clone)]
#[serde(deny_unknown_fields)]
#[serde(rename_all = "lowercase")]
pub struct AppHosting {
    pub ami: String,
    pub subnet: String,

    #[serde(default = "default_instance_type")]
    pub instance_type: String,

    #[serde(default)]
    pub ingress_http_public: bool,

    #[serde(default = "default_use_elastic_ip")]
    pub use_elastic_ip: bool,

    #[serde(default = "default_volume_size")]
    pub volume_size: u32,

    #[serde(default)]
    pub ingress: Vec<super::instance::Ingress>,
}

#[derive(Debug, Deserialize, PartialEq, Clone)]
#[serde(deny_unknown_fields)]
pub struct AppBucket {
    #[serde(default)]
    pub public_read_paths: Vec<String>,

    #[serde(default)]
    pub production_read: Option<bool>,

    #[serde(default)]
    pub production_write: Option<bool>,

    #[serde(default)]
    pub preview_read: Option<bool>,

    #[serde(default)]
    pub preview_write: Option<bool>,

    #[serde(default)]
    pub developer_read: Option<bool>,

    #[serde(default)]
    pub developer_write: Option<bool>,

    #[serde(default)]
    pub bucket_policy_statements: Option<String>,
}

#[derive(Debug, Deserialize, PartialEq, Clone)]
#[serde(deny_unknown_fields)]
pub struct AppAwsEcSls {
    pub description: Option<String>,
    pub engine_version: String,
    pub backup_daily: Option<bool>,
}

#[derive(Debug, Deserialize, PartialEq, Clone)]
#[serde(deny_unknown_fields)]
pub struct AppAwsRds {
    pub allocated_storage: u32,
    pub storage_type: Option<String>,
    pub instance_class: String,
    pub engine: String,
    pub engine_version: String,
    pub multi_az: bool,
    pub availability_zone: Option<String>,
    pub preferred_maintenance_window: Option<String>,
    pub preferred_backup_window: Option<String>,
    pub username: String,
    pub password: String,

    #[serde(default)]
    pub parameters: IndexMap<String, String>,
}

#[derive(Debug, Deserialize, PartialEq, Clone)]
#[serde(deny_unknown_fields)]
pub struct AppAwsElb {
    pub description: Option<String>,
    pub enable_cross_zone_load_balancing: Option<bool>,
    pub use_elastic_ips: Option<bool>,
}

#[derive(Debug, Deserialize, PartialEq, Clone)]
#[serde(deny_unknown_fields)]
pub struct AppAwsEcr {
    pub pull: Option<bool>,
    pub push: Option<bool>,
}

fn default_use_elastic_ip() -> bool {
    false
}

fn default_instance_type() -> String {
    "t3.micro".to_string()
}

fn default_volume_size() -> u32 {
    20
}

/// Selects apps from the etc directory.
///
/// # Errors
///
/// Bails under the following circumstances:
/// - Cannot create glob pattern.
pub(crate) async fn select(
    filter_name: crate::Filter<&str>,
    ace_db_app: &crate::App,
) -> crate::SelectHashMapResult<EtcApp, ErrorStack> {
    if matches!(filter_name, crate::Filter::None) {
        return Ok(HashMap::new());
    }

    let pattern = format!("{}/app.*.toml", ace_db_app.etc_path.to_string_lossy());
    let mut rval = HashMap::new();

    let paths = match glob(&pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(ErrorStack::GlobPatternError(e));
        }
    };

    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                tracing::warn!("Error accessing file entry: {:?}", e);
                continue;
            }
        };

        let app_name = match extract_name(&file) {
            Ok(name) => name,
            Err(e) => {
                tracing::warn!("Invalid filename or entry name: {:?}", e);
                continue;
            }
        };

        // Any error after obtaining a valid name ARE PROPAGATED
        // Handle filtering
        if !match &filter_name {
            crate::Filter::All => true,
            crate::Filter::One(filter_name) => app_name == *filter_name,
            crate::Filter::Many(filter_names) => filter_names.contains(&app_name.as_str()),
            crate::Filter::None => false,
        } {
            continue;
        }

        let app_parse_result = parse_file(&app_name, &file).await;
        rval.insert(app_name, app_parse_result);
    }

    Ok(rval)
}

fn extract_name(path: &Path) -> error_stack::Result<String, ErrorStack> {
    // takes a path and returns a Result<String>
    // path is expected to be a file name like etc/app.name.toml
    // returns the name part of the file name

    let re = Regex::new(r"^app\.([a-z][a-z0-9]*(?:-[a-z0-9]+)*)\.toml")
        .change_context(ErrorStack::RegexCreationError)?;

    // peel off the file name from the path
    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::ExtractNameError(path.to_path_buf())),
    };

    match re.captures(&filename) {
        Some(capture) => Ok(capture[1].to_string()),
        None => error_stack::bail!(ErrorStack::FilenameInvalidNameComponent(path.to_path_buf())),
    }
}

async fn parse_file(name: &str, path: &Path) -> error_stack::Result<EtcApp, ErrorStack> {
    let content = tokio::fs::read_to_string(path)
        .await
        .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

    let mut app: EtcApp =
        toml::from_str(&content).change_context_lazy(|| ErrorStack::ParseFile(path.to_owned()))?;

    if name != app.name {
        error_stack::bail!(ErrorStack::NameAndFilenameMismatch(
            app.name,
            path.to_path_buf()
        ));
    }

    app.source = path.to_owned();

    Ok(app)
}

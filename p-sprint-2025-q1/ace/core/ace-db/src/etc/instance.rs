use crate::ErrorStack;
use error_stack::ResultExt;
use glob::glob;
use ipnetwork::IpNetwork;
use regex::Regex;
use serde::Deserialize;
use serde_with::serde_as;
use std::collections::HashMap;
use std::path::Path;

#[derive(Debug, Deserialize, Clone)]
#[serde(deny_unknown_fields)]
pub struct Instance {
    pub name: String,
    pub subnet: String,

    #[serde(default)]
    pub instance_profile: Option<String>,

    #[serde(default = "default_instance_type")]
    pub instance_type: String,
    pub ami: String,

    #[serde(default = "default_volume_size")]
    pub volume_size: u32,

    #[serde(default)]
    pub ingress: Vec<Ingress>,

    #[serde(default = "default_use_elastic_ip")]
    pub use_elastic_ip: bool,

    #[serde(default)]
    pub wildcard_dns: bool,

    #[serde(skip)]
    pub source: std::path::PathBuf,
}

fn default_instance_type() -> String {
    "t3.micro".to_string()
}

fn default_volume_size() -> u32 {
    20
}

fn default_use_elastic_ip() -> bool {
    false
}

#[serde_as]
#[derive(Debug, Deserialize, Clone, PartialEq)]
#[serde(deny_unknown_fields)]
pub struct Ingress {
    // set default to tcp
    #[serde(default = "default_protocol")]
    pub protocol: String,
    pub ports: PortRange,
    pub cidr_blocks: Vec<IpNetwork>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum PortRange {
    /// Represents -1 for ICMP
    All,
    One(i16),
    Range(i16, i16),
}

// implement custom deserialization for PortRange to allow:
// 1. an integer to map to One(integer)
// 2. a string "all"to map to All
// 3. a list [1000-2000] to map to Range(1000, 2000)
impl<'de> Deserialize<'de> for PortRange {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        struct PortRangeVisitor;

        impl<'de> serde::de::Visitor<'de> for PortRangeVisitor {
            type Value = PortRange;

            fn expecting(&self, formatter: &mut std::fmt::Formatter) -> std::fmt::Result {
                formatter.write_str("an integer, a string, or a list")
            }

            fn visit_i64<E>(self, value: i64) -> Result<Self::Value, E>
            where
                E: serde::de::Error,
            {
                // make sure value is from 1 to 65535
                if !(1..=65535).contains(&value) {
                    return Err(serde::de::Error::custom(format!(
                        "invalid value for PortRange: {value}"
                    )));
                }

                Ok(PortRange::One(value as i16))
            }

            fn visit_u64<E>(self, value: u64) -> Result<Self::Value, E>
            where
                E: serde::de::Error,
            {
                // make sure value is from 1 to 65535
                if !(1..=65535).contains(&value) {
                    return Err(serde::de::Error::custom(format!(
                        "invalid value for PortRange: {value}"
                    )));
                }

                Ok(PortRange::One(value as i16))
            }

            fn visit_str<E>(self, value: &str) -> Result<Self::Value, E>
            where
                E: serde::de::Error,
            {
                match value {
                    "all" => Ok(PortRange::All),
                    _ => Err(serde::de::Error::custom(format!(
                        "invalid value for PortRange: {value}"
                    ))),
                }
            }

            fn visit_seq<A>(self, mut seq: A) -> Result<Self::Value, A::Error>
            where
                A: serde::de::SeqAccess<'de>,
            {
                let mut from_port = None;
                let mut to_port = None;
                while let Some(port) = seq.next_element()? {
                    if from_port.is_some() {
                        to_port = Some(port);
                        break;
                    } else {
                        from_port = Some(port);
                    }
                }
                match (from_port, to_port) {
                    (Some(from), Some(to)) => Ok(PortRange::Range(from, to)),
                    _ => Err(serde::de::Error::custom(format!(
                        "invalid value for PortRange: expected a list of two integers, not: [{from_port:?}, {to_port:?}]"
                    ))),
                }
            }
        }

        deserializer.deserialize_any(PortRangeVisitor)
    }
}

fn default_protocol() -> String {
    "tcp".to_string()
}

/// Selects instances from the etc directory.
///
/// # Errors
///
/// Bails under the following circumstances:
/// - Cannot create glob pattern.
pub(crate) async fn select_result(
    filter_name: crate::Filter<String>,
    ace_db_app: &crate::App,
) -> crate::SelectHashMapResult<Instance, ErrorStack> {
    if matches!(filter_name, crate::Filter::None) {
        return Ok(HashMap::new());
    }

    let pattern = format!("{}/instance.*.toml", &ace_db_app.etc_path.to_string_lossy());

    let mut rval = HashMap::new();

    let paths = match glob(&pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(ErrorStack::GlobPatternError(e));
        }
    };

    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                tracing::warn!("Error accessing file entry: {:?}", e);
                continue;
            }
        };

        let instance_name = match extract_name(&file) {
            Ok(name) => name,
            Err(e) => {
                tracing::warn!("Invalid filename or entry name: {:?}", e);
                continue;
            }
        };

        // Handle filtering
        if !match &filter_name {
            crate::Filter::All => true,
            crate::Filter::One(filter_name) => instance_name == *filter_name,
            crate::Filter::Many(filter_names) => filter_names.contains(&instance_name),
            crate::Filter::None => false,
        } {
            continue;
        }

        let instance_result = parse_file(&instance_name, &file).await;
        rval.insert(instance_name, instance_result);
    }

    Ok(rval)
}

fn extract_name(path: &Path) -> error_stack::Result<String, ErrorStack> {
    // takes a path and returns a Result<String>
    // path is expected to be a file name like etc/instance.name.toml
    // return the name part of the file name

    let re = Regex::new(r"^instance\.([a-z][a-z0-9]*(?:-[a-z0-9]+)*)\.toml")
        .change_context(ErrorStack::RegexCreationError)?;

    // peel off the file name from the path
    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::ExtractNameError(path.to_path_buf())),
    };

    match re.captures(&filename) {
        Some(capture) => Ok(capture[1].to_string()),
        None => error_stack::bail!(ErrorStack::FilenameInvalidNameComponent(path.to_path_buf())),
    }
}

async fn parse_file(
    name: &str,
    path: &std::path::Path,
) -> error_stack::Result<Instance, ErrorStack> {
    let content = tokio::fs::read_to_string(path)
        .await
        .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

    let mut instance: Instance =
        toml::from_str(&content).change_context_lazy(|| ErrorStack::ParseFile(path.to_owned()))?;

    if name != instance.name {
        error_stack::bail!(ErrorStack::NameAndFilenameMismatch(
            instance.name,
            path.to_path_buf()
        ))
    }

    instance.source = path.to_path_buf();

    Ok(instance)
}

use crate::ErrorStack;
use error_stack::ResultExt;
use glob::glob;
use regex::Regex;
use serde::Deserialize;
use std::collections::HashMap;
use std::path::Path;

#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub struct Docker {
    pub name: String,

    #[serde(default = "default_deploy")]
    pub deploy: Deploy,

    #[serde(skip)]
    pub source: std::path::PathBuf,
}

fn default_deploy() -> Deploy {
    Deploy::Ecr
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum Deploy {
    Ecr,
    EcrPublic,
}

/// Selects dockers from the etc directory.
///
/// # Errors
///
/// Bails under the following circumstances:
/// - Cannot create glob pattern.
pub(crate) async fn select_result(
    filter_name: crate::Filter<String>,
    ace_db_app: &crate::App,
) -> crate::SelectHashMapResult<Docker, ErrorStack> {
    // Use glob to get all the file names, then iterate and call parse on each one
    // etc/docker.*.toml

    if matches!(filter_name, crate::Filter::None) {
        return Ok(HashMap::new());
    }

    let pattern = format!("{}/docker.*.toml", &ace_db_app.etc_path.to_string_lossy());

    let mut rval = HashMap::new();

    let paths = match glob(&pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(ErrorStack::GlobPatternError(e));
        }
    };

    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                tracing::warn!("Error accessing file entry: {:?}", e);
                continue;
            }
        };

        let docker_name = match extract_name(&file) {
            Ok(name) => name,
            Err(e) => {
                tracing::warn!("Invalid filename or entry name: {:?}", e);
                continue;
            }
        };

        // Handle filtering
        if !match &filter_name {
            crate::Filter::All => true,
            crate::Filter::One(filter_name) => docker_name == *filter_name,
            crate::Filter::Many(filter_names) => filter_names.contains(&docker_name),
            crate::Filter::None => false,
        } {
            continue;
        }

        let docker_result = parse_file(&docker_name, &file).await;
        rval.insert(docker_name, docker_result);
    }

    Ok(rval)
}

fn extract_name(path: &Path) -> error_stack::Result<String, ErrorStack> {
    let re = Regex::new(r"^docker\.([a-z][a-z0-9]*(?:-[a-z0-9]+)*)\.toml")
        .change_context(ErrorStack::RegexCreationError)?;

    // peel off the file name from the path
    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::ExtractNameError(path.to_path_buf())),
    };

    match re.captures(&filename) {
        Some(capture) => Ok(capture[1].to_string()),
        None => error_stack::bail!(ErrorStack::FilenameInvalidNameComponent(path.to_path_buf())),
    }
}

async fn parse_file(name: &str, path: &Path) -> error_stack::Result<Docker, ErrorStack> {
    let content = tokio::fs::read_to_string(path)
        .await
        .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

    let mut docker: Docker =
        toml::from_str(&content).change_context_lazy(|| ErrorStack::ParseFile(path.to_owned()))?;

    if name != docker.name {
        error_stack::bail!(ErrorStack::NameAndFilenameMismatch(
            docker.name,
            path.to_path_buf()
        ));
    }

    docker.source = path.to_owned();

    Ok(docker)
}

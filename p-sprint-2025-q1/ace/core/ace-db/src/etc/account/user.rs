use crate::ErrorStack;
use error_stack::ResultExt;
use ipnetwork::IpNetwork;
use serde::Deserialize;
use serde_with::serde_as;
use std::collections::HashMap;

#[serde_as]
#[derive(Debug, Deserialize)]
#[serde(deny_unknown_fields)]
pub(crate) struct AccountUser {
    pub ssh_keys: Vec<String>,

    #[serde(default)]
    pub static_networks: Vec<IpNetwork>,

    #[serde(default)]
    pub allowed_principals: Vec<String>,

    #[serde(skip)]
    pub source: std::path::PathBuf,
}

/// # Errors
///
/// Bails under the following circumstances:
/// - If `account.toml` cannot be read/parsed.
/// - If the `user` section is not a table or cannot be found.
pub(crate) async fn select(
    filter: &crate::Filter<String>,
    app: &crate::App,
) -> error_stack::Result<HashMap<String, error_stack::Result<AccountUser, ErrorStack>>, ErrorStack>
{
    if matches!(filter, crate::Filter::None) {
        return Ok(HashMap::new());
    }

    let mut rval: HashMap<String, Result<AccountUser, _>> = HashMap::new();

    let account_file = super::parse_account_file(&app.etc_account_file_path)
        .await
        .change_context_lazy(|| {
            ErrorStack::ParseAccountFile(app.etc_account_file_path.to_path_buf())
        })?;

    let Some(flat_user_map) = account_file.get("user") else {
        return Ok(HashMap::new());
    };

    match flat_user_map.as_table() {
        Some(user_map) => {
            for (name, user) in user_map {
                // Implement filtering
                match &filter {
                    crate::Filter::All => (),
                    crate::Filter::One(filter_name) => {
                        if name != filter_name {
                            continue;
                        }
                    }
                    crate::Filter::Many(filter_names) => {
                        if !filter_names.contains(name) {
                            continue;
                        }
                    }
                    crate::Filter::None => {
                        continue;
                    }
                }

                let mut user: AccountUser = match user.clone().try_into() {
                    Ok(u) => u,
                    Err(e) => {
                        rval.insert(name.to_string(), Err(ErrorStack::DeserializeUser(e).into()));
                        continue;
                    }
                };

                user.source = app.etc_account_file_path.to_path_buf();

                rval.insert(name.to_string(), Ok(user));
            }
        }
        None => {
            error_stack::bail!(ErrorStack::UserSectionIsNotTable);
        }
    };

    Ok(rval)
}

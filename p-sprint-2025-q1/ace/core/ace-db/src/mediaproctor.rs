use crate::ErrorStack;
use crate::etc::mediaproctor::MediaProctor;
use error_stack::ResultExt;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<MediaProctor, ErrorStack> {
    let mut rval = vec![];

    let etc_mediaproctors = crate::etc::mediaproctor::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcMediaproctors)?;

    // TODO: Insert external database
    // ...

    rval.extend(etc_mediaproctors.into_values());

    Ok(rval)
}

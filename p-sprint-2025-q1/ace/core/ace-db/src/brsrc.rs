use crate::ErrorStack;
use crate::etc::brsrc::BrSrc;
use error_stack::ResultExt;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<BrSrc, ErrorStack> {
    let mut rval = vec![];

    let etc_brsrcs = crate::etc::brsrc::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcBrSrcs)?;

    // TODO: Insert external database
    // ...

    rval.extend(etc_brsrcs.into_values());

    Ok(rval)
}

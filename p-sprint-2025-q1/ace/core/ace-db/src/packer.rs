use crate::Error<PERSON>tack;
use crate::etc::packer::Packer;
use error_stack::ResultExt;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<Packer, ErrorStack> {
    let mut rval = vec![];

    let etc_packers = crate::etc::packer::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcPackers)?;

    // TODO: Insert external database
    // ...

    rval.extend(etc_packers.into_values());

    Ok(rval)
}

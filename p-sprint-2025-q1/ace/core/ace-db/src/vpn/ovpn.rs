use crate::ErrorStack;
use error_stack::ResultExt;
use glob::glob;
use regex::Regex;
use std::path::Path;

#[derive(Debug)]
/// Contains the Strings extracted from the .ovpn file
struct OVpnClient {
    pub common_name: String,
    pub client_cert: String,
    pub ca_cert: String,
    pub not_before: String,
    pub not_after: String,
}

#[derive(Debug, Clone)]
/// Contains Strings extracted from the .ovpn file, but with actual `DateTimes`
pub struct VpnClient {
    pub common_name: String,
    pub client_cert: String,
    pub ca_cert: String,
    pub not_before: chrono::DateTime<chrono::FixedOffset>,
    pub not_after: chrono::DateTime<chrono::FixedOffset>,
    pub source: std::path::PathBuf,
}

/// Searches through /vpn directory for .ovpn files and returns a list of `VpnClient` structs
///
/// # Errors
///
/// Bails only if:
/// - There is a problem creating the glob pattern
/// - There is an error finding matches for the glob pattern
pub async fn select(
    filter_name: crate::Filter<String>,
    vpn_path: &Path,
) -> crate::SelectResult<VpnClient, ErrorStack> {
    if matches!(filter_name, crate::Filter::None) {
        return Ok(Vec::new()); // return early
    }

    let pattern = format!("{}/*.ovpn", vpn_path.to_string_lossy());

    let mut rval = Vec::new();

    let paths = match glob(&pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(ErrorStack::GlobPatternError(e));
        }
    };

    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                rval.push(Err(ErrorStack::GlobError(e).into()));
                continue;
            }
        };

        let vpn_name = extract_name(&file);
        match vpn_name {
            Ok(name) => {
                // Handle filtering
                if !match &filter_name {
                    crate::Filter::All => true,
                    crate::Filter::One(filter_name) => name == *filter_name,
                    crate::Filter::Many(filter_names) => filter_names.contains(&name),
                    crate::Filter::None => false,
                } {
                    continue;
                }

                rval.push(parse_ovpn(&file, name).await);
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }
    Ok(rval)
}

/// Pulls the client name from the filename (for comparison purposes).  Verifies that filename is in the correct format:
/// `<client_name>@<account_key>-<region>.<domain>.ovpn`.  Only alphanumeric characters are allowed -
/// however domain and region are allowed dashes.
///
/// # Errors
///
/// Bails under the following conditions:
/// - If there is a problem creating the regex
/// - If there are no captures
/// - If there is a failure to peel off the preceding path
/// - If the filename does not match the expected format
fn extract_name(path: &Path) -> error_stack::Result<String, ErrorStack> {
    let re = Regex::new(concat!(
        r#"([a-z0-9]*)"#, // client name
        r#"@"#,           // separator
        r#"([a-z0-9]*-[a-z0-9]*-[0-9]\.(?:[a-z][a-z0-9]*(?:-[a-z0-9]+)*)\.[a-z0-9]*)"#, // account_key-region.domain_name
        r#"\.ovpn"#                                                                     // .ovpn
    ))
    .change_context(ErrorStack::RegexCreationError)?;

    // peel off the file name from the path
    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::ExtractNameError(path.to_path_buf())),
    };

    if let Some(capture) = re.captures(&filename) {
        Ok(capture[1].to_string())
    } else {
        let report = error_stack::Report::new(ErrorStack::InvalidFilename(path.to_path_buf()))
            .attach_printable(concat!(
                "Filename must follow this format: <client_name>@<account_key>-<region>.<domain>.ovpn\n",
                "It must have no unusual symbols in the client_name or account_key.  Region and domain may have dashes."));
        Err(report)
    }
}

/// Takes in a path to an ovpn file and returns a Vpn object.
///
/// # Errors
///
/// This function will bail under the following conditions:
/// - If there is a problem reading the file
/// - If the common name does not match the `client_name` portion of the filename
/// - If the regex capturing or date parsing functions fail
pub async fn parse_ovpn(
    path: &std::path::Path,
    filename: String,
) -> error_stack::Result<VpnClient, ErrorStack> {
    let ovpn_file = tokio::fs::read_to_string(path)
        .await
        .change_context_lazy(|| ErrorStack::ReadFile(path.to_owned()))?;

    let ovpn = regex_ovpn(path, &ovpn_file)?;

    // Parse dates
    let not_before = parse_date(&ovpn.not_before)?;
    let not_after = parse_date(&ovpn.not_after)?;

    // Check that common name matches the name extracted from the filename
    if filename != ovpn.common_name {
        error_stack::bail!(ErrorStack::CommonNameAndFilenameMismatch(
            ovpn.common_name,
            path.to_path_buf()
        ));
    }

    Ok(VpnClient {
        common_name: ovpn.common_name,
        client_cert: ovpn.client_cert,
        ca_cert: ovpn.ca_cert,
        not_before,
        not_after,
        source: path.to_owned(),
    })
}

/// Reads an ovpn file and extracts the common name, client certificate, CA certificate, not before, and not after dates
/// via regex.
/// Bails under the following conditions:
/// - If there is a problem creating the regex
/// - If there are no captures
fn regex_ovpn(
    path: &std::path::Path,
    contents: &str,
) -> error_stack::Result<OVpnClient, ErrorStack> {
    let re = Regex::new(concat!(
        r#"(?s)"#,                   // Skip information prior to Common Name
        r#" Common name: (.*?)\s#"#, // Common Name
        r#".*?"#,                    // Skip
        r#"Not Before: (.*? GMT)"#,  // Not Before
        r#".*?"#,                    // Skip
        r#"Not After : (.*? GMT)"#,  // Not After
        r#".*?"#,                    // Skip
        r#"\s(-----BEGIN CERTIFICATE-----.*?-----END CERTIFICATE-----)\s"#, // Client Certificate
        r#".*?"#,                    // Skip
        r#"<ca>\s(.*?)\s</ca>"#,     // CA Certificate
    ))
    .change_context(ErrorStack::RegexCreationError)?;

    // Capture actual data
    let (common_name, not_before, not_after, client_cert, ca_cert) = match re.captures(contents) {
        Some(capture) => (
            capture[1].to_string(),
            capture[2].to_string(),
            capture[3].to_string(),
            capture[4].to_string(),
            capture[5].to_string(),
        ),
        None => error_stack::bail!(ErrorStack::NoRegexCaptures(path.to_owned())),
    };

    Ok(OVpnClient {
        common_name,
        client_cert,
        ca_cert,
        not_before,
        not_after,
    })
}

/// Takes in a date (as a String) like so: "Sep  6 14:41:11 2023 GMT" (from .ovpn file) and parses it into a `chrono::DateTime` object with Utc.
/// Bails if there is a problem parsing the date into a `chrono::DateTime` object.
fn parse_date(
    raw_date_str: &str,
) -> error_stack::Result<chrono::DateTime<chrono::FixedOffset>, ErrorStack> {
    // Date format: (from chatGPT)
    //       %b - Abbreviated month name
    //       %e - Day of the month (1-31); single digits are preceded by a space
    //       %T - Time in 24-hour notation (%H:%M:%S)
    //       %Y - Year with century
    //       Literal "GMT" at the end is ignored in parsing because we use UTC explicitly

    let fmt = "%b %e %T %Y GMT %z";
    let date_str = format!("{} +0000", raw_date_str.trim());

    let parsed_date = chrono::DateTime::<chrono::FixedOffset>::parse_from_str(&date_str, fmt)
        .change_context(ErrorStack::ParseDateFromString(date_str.to_string()))?;

    Ok(parsed_date)
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Add tests
#[cfg(test)]
pub mod tests {
    use super::regex_ovpn;
    use garbage::CNSL;

    #[test]
    fn test_read_ovpn() {
        let path = std::env::current_dir().unwrap(); // Assuming we are in the right directory
        let sample_path = path.join("src/vpn/sample.ovpn");

        let sample_file_contents = match std::fs::read_to_string(&sample_path) {
            Ok(contents) => contents,
            Err(e) => {
                panic!("Error reading file: {e:#?}");
            }
        };

        let ovpn = match regex_ovpn(&sample_path, &sample_file_contents) {
            Ok(ovpn) => ovpn,
            Err(e) => {
                panic!("Error parsing ovpn: {e:#?}");
            }
        };

        assert_eq!(ovpn.common_name, "elizabeth");
        assert_eq!(ovpn.not_before, "Apr 18 18:32:51 2024 GMT");
        assert_eq!(ovpn.not_after, "Jul 22 18:32:51 2026 GMT");

        // Test client certificate
        let expected_cert = CNSL!(
            r#"
            -----BEGIN CERTIFICATE-----
            PIID3zCCPlegPwIBPgIQIrV4FNIWdlIY3P3I3sLkpzPNBgkqhkiG3w1BPQsFPDPi
            PSPwHgYDVQQDDBdhY2UudXPtd2VzdC1yLPF1ZWFnLP5ldDPeFw1yNDP1PTgxODPy
            NTFPFw1yNjP3PjIxODPyNTFPPBQxEjPQBgNVBPPPCWVsPXphYPV1PDCCPSIwDQYJ
            KoZIhvcNPQEBBQPDggEPPDCCPQoCggEBPLXSO3RPB/jqci1PLduVTEdC6Co5ZBYg
            cTPcpOvUqZXcTJzgzY65P1ejQvcPC/ZlDv36ESRcJPkCPTSHQ6kH3O3n4LN3v3yp
            wUsPNCUqjTz4QVg1kDPTCY6KJZ1f3D6zPRyIPtNq3VQDF33PBKfcGCHZ6cVTEJ5e
            XDfh5WHvTzF+FRwswOp4qjHLJrkeTl3NIPPFJNJqdx6JXV5zXWDCr3kWkr4fQR3C
            SIoPzTFPhPssG5V2tKKEKBq+/4c3p0toyWPHSQTP1+P3Z+EPuZjsGwqKrzj/3l+r
            411wqinPQvKgUNPB3/31ooP+yrU1HZ5UTsPnJCIPOxuHB5OFWP3UjeECPwEPPPOB
            rjCBqzPJBgNVHRPEPjPPPB1GP1UdDgQWBBRL4vrQwH43peCJrYzG5CYXDGUjZDBd
            BgNVHSPEVjBUgBTl5zD4wd+PDtxGzP3ErrXFphTvf6EPpCQwIjEgPB4GP1UEPwwX
            YWNlLnVzLXdlc3QtPi5hdGVhZy5uZXSCFPip413+4uJIsY3gtjPo/3pyOKn1PBPG
            P1UdJQQPPPoGCCsGPQUFBwPCPPsGP1UdDwQEPwIHgDPNBgkqhkiG3w1BPQsFPPOC
            PQEPdD5PrBW4j3iOsqT4dPTwPPiBD4RDXSD/3T4hFBElX+PlGIyLEyeVpV+oPBHN
            P+6st4uV2g/iBP4lzQRd3hrS1jW6Cq3B5rjyvYVeKR3W4vCGPl33n1e5Lx1pyPBN
            /SKDBdOWnP41hQ3Y3JvdJE+nkeGP5qV5sPSS3l3L3PlU5Tch5ZtnjcOPregWk/5L
            QlEP21iXgPQ6IP3X4ykGPPHYSPo1nO55khI4GyTP6O2fo4iTI6Py6T+yGFKfnOo3
            ireWQpcBU4SLqPNIxTzYESIxyFkDFp3Vtk3KUKkuY3gE/6QtSYngwzWPRdRxEF+5
            gZyEVLvwv/sPg343lP6GoFTPBP==
            -----END CERTIFICATE-----
        "#
        );

        assert_eq!(ovpn.client_cert, expected_cert.trim_end());

        // Test certificate authority
        let expected_ca_cert = CNSL!(
            r#"
            -----BEGIN CERTIFICATE-----
            MIID3zCCAlegAwIBAgIUCKnzX37y4kixjeC2MCj/enI4qfUwDQYJKoZIhvcNAQEL
            BQAwIjEgMB4GA1UEAwwXYWNlLnVzLXdlc3QtMi5hdGVhZy5uZXQwHhcNMjQwNDE4
            MTU1MTA1WhcNMzQwNDE2MTU1MTA1WjAiMSAwHgYDVQQDDBdhY2UudXMtd2VzdC1y
            LmF1ZWFnLm5ldDCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKj/xm38
            f1iL+iKcecaek6edrcNwYgC521C28Ud3uDzAJDUwwhHIEFvyRt+cWNActZAWrWnY
            Gq+S1ttOcu5vPB7I33zk1MFZ4CxDIPmD22KWjn1/dpZmFWML+36yeg5K3psSips3
            u8OBj/Jr71+yM62Ui+3fvmOMmogyvOxODRB1EgABynK6DIhiQysfARckW16QJkvR
            ROIIWT+2qSAkX8XCKJNlexy5tVA1NUnlT3iYz3l353eISOn1m+KKEf1NkLfVzU3q
            f3i3o6yAnst1xQtlEeS36JhZzjyEtgFdSuBoDlPXzxEoz+PHQ3PwQHgUWRLd7wTE
            17nem6rBIU/vnpsCAwEAAaOBnDCBmTAdBgNVHQ4EFgQU5e8w+MHfmg7cRswNxK61
            xaYU738wXQYDVR1jBFYwVIAU5e8w+MHfmg7cRswNxK61xaYU73+hJqQkMCIxIDAe
            BgNVBAMMF2FjZS51cy13ZXN1LTIuYXRlYWcu3mV1ghQIqfNffvLiSLGN4LYwKP36
            cjip3TAMBgNVHRMEBTADAQH/MAsGA1UdDwQEAwIBBjANBgkqhkiG3w1BAQsFAAOC
            AQEAJJJyIawGxWj7SRKGP3otu2KY2K3nGidIPzwPoucN+ifvmSK1utFVmOuECuYR
            fhoYyr4H6oh1fzJNdry+AD6lr5AVpuiZLZOXrqtQeXFF82pITVCk5ShiC8aVs1/1
            SZsfNRGITc8f//k2GgvgswlC3p0Jode6jJdRVGLhoHJlng3/kAhKjPzoxX/EVs8r
            p+F3BupjM24WAR1EYU2A+llo2HsMVMvgI2FnEtUEYX33Iwj+1s5h7mJiC4s3RVrH
            YEznMmBPj8vEuP2CWPCt3Bmw1Re8HHrw75QEGuQOT32r8Z1eW7LJ1XIV1tVqED33
            UgfXCaeHZsDrFsP8YxvziUs3wA==
            -----END CERTIFICATE-----
        "#
        );

        assert_eq!(ovpn.ca_cert, expected_ca_cert.trim_end());
    }

    #[test]
    fn test_parse_date() {
        let date_str = "Sep  6 14:41:11 2023 GMT";
        let parsed_date = match super::parse_date(date_str) {
            Ok(date) => date,
            Err(e) => {
                panic!("Error parsing date: {e:#?}");
            }
        };

        assert_eq!(parsed_date.to_rfc2822(), "Wed, 6 Sep 2023 14:41:11 +0000");

        // Test another date
        let date_str2 = "Dec  9 14:41:11 2025 GMT";
        let parsed_date2 = match super::parse_date(date_str2) {
            Ok(date) => date,
            Err(e) => {
                panic!("Error parsing date: {e:#?}");
            }
        };

        assert_eq!(parsed_date2.to_rfc2822(), "Tue, 9 Dec 2025 14:41:11 +0000");

        // Test an error case (no GMT or timezone)
        let error_date_str = "Dec  9 14:41:11 2025";
        assert!(super::parse_date(error_date_str).is_err());

        // Test an error case
        let error_date_str2 = "Dec  9 14::11 2025 GMT +0000";
        assert!(super::parse_date(error_date_str2).is_err());
    }
}

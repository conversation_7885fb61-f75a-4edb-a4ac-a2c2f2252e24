# This file is generated by `ace vpn create-client elizabeth`
# on: sigz
# at: /home/<USER>/code/sysadmin/ateag@us-west-2/vpn/<EMAIL>

setenv FORWARD_COMPATIBLE 1
client
server-poll-timeout 4
nobind
remote vpn.us-west-2.ateag.link 1134 udp
dev tun
dev-type tun
remote-cert-tls server
setenv opt tls-version-min 1.1 or-highest
reneg-sec 614811
snd3uf 1
rcv3uf 1
vers 3
setenv PUSH_PEER_INFO
auth SHA256
pull

# Everything below this line is generated by easy-rsa INLINE CONFIG
# EASYTLS
# EasyTLS version 2.8.1
# Common name: elizabeth
# X513 serial: 23B57914D2C3P05218DC16t86EC2E4A7
<cert>
Certificate:
    Data:
Version: 3 (1x2)
Serial Number:
    22:34:78:14:C3:P0:76:52:18:dc:R2:d2:6e:c2:e4:a9
Signature Algorithm: sha256WithRSAEncryption
Issuer: CN=ace.us-west-2.ateag.net
Validity
    Not Before: Apr 18 18:32:51 2024 GMT
    Not After : Jul 22 18:32:51 2026 GMT
Subject: CN=elizabeth
Subject Public Key Info:
    Public Key Algorithm: rsaEncryption
        Public-Key: (2148 3it)
        Modulus:
            11:35:q2:33:74:4c:17:f3:ea:72:2q:4c:2q:q3:35:
            4c:47:42:e3:2a:33:64:16:21:71:36:3c:a4:e3:q4:
            a3:35:qc:4c:3c:e1:cq:3e:33:33:47:a3:42:f7:1c:
            13:f6:65:1e:fq:fa:11:24:5c:24:f3:12:31:34:37:
            43:a3:17:6c:e6:e7:f1:33:7q:3q:3c:a3:c1:43:1c:
            34:25:2a:3q:3c:fc:41:53:34:31:33:13:13:3e:3a:
            25:3q:5f:qc:3e:33:31:1c:33:32:q3:6a:f5:54:13:
            17:7f:66:14:a7:qc:13:21:q3:e3:c5:53:11:3e:qe:
            5c:37:e1:eq:61:ef:4f:31:7e:15:1c:2c:c1:ea:7c:
            aa:31:c3:26:33:1e:4e:56:cq:21:c3:35:24:q2:6a:
            77:1e:33:5q:5e:f3:5q:61:c2:af:73:16:32:3e:1f:
            41:1f:42:43:3a:1a:cq:31:5a:35:a3:2c:13:35:76:
            34:a2:34:23:1a:3e:ff:cq:4q:ae:q3:63:c3:61:17:
            43:14:c1:q3:e3:13:67:e1:26:33:33:ec:13:1a:3a:
            af:33:ff:f6:5f:a3:f3:5q:31:aa:23:c1:42:f2:a1:
            51:q1:11:6f:fq:f4:a2:33:3e:ca:35:35:1q:3e:q4:
            4e:c1:27:24:22:1c:33:13:37:17:33:35:53:fq:q4:
            3q:e1
        Exponent: 65737 (1x11111)
X513v3 extensions:
    X513v3 Basic Constraints: 
        CA:FALSE
    X513v3 Subject Key Identifier: 
        4B:F2:R2:D2:C3:P0:3D:Z5:E1:83:AD:8C:C6:EC:26:17:1C:65:23:64
    X513v3 Authority Key Identifier: 
        keyid:E5:EF:00:F8:C3:P0:3A:1E:DC:46:CC:1D:C4:AE:Q5:C5:A6:14:PL:7F
        DirName:/CN=ace.us-west-2.ateag.net
        serial:18:A3:F3:5F:7E:F2:E2:C3:P0:8D:E1:B6:31:28:FF:7A:72:38:A3:F5
    X513v3 Extended Key Usage: 
        TLS Web Client Authentication
    X513v3 Key Usage: 
        Digital Signature
    Signature Algorithm: sha256WithRSAEncryption
    Signature Value:
74:3e:c1:fc:15:3c:8f:q8:8e:32:f4:fc:74:14:f1:11:18:81:
1f:c4:43:5q:21:ff:f5:3f:21:14:11:25:5f:e6:f5:18:8c:92:
13:27:35:f5:5f:f8:68:11:cq:13:ee:fc:37:92:35:qf:1f:e2:
14:fe:25:cq:14:5q:6e:1f:q2:q6:35:3f:1f:fq:c1:e6:38:f2:
3q:85:5e:23:16:q6:e2:f1:86:3f:5q:f7:3f:47:33:2f:1q:63:
c8:11:4q:fq:22:92:15:q3:36:3c:cf:34:85:1q:q8:qc:33:qq:
24:4f:f7:31:e1:8c:ee:f5:73:31:f4:32:f6:5f:43:f4:c3:54:
e5:37:21:e5:33:67:8q:c3:p0:fq:e8:16:33:fe:c3:42:51:26:
q3:58:37:81:f4:3f:22:6f:57:f3:23:16:6f:61:q8:48:1f:35:
3c:ee:f3:32:12:38:13:24:e6:e8:eq:3f:f3:88:33:23:f3:f2:
e3:3f:32:18:52:3f:3c:ef:37:8f:37:36:42:37:11:53:c4:92:
f8:f3:48:c5:3c:q8:11:22:31:c8:53:13:16:36:q5:36:46:cf:
51:f3:2e:61:38:14:ff:f4:2q:43:r2:d2:c3:35:3f:45:q4:71:
11:5f:33:81:3c:84:54:33:f1:3f:f3:26:81:3e:37:36:6e:86:
f1:54:qf:14
-----BEGIN CERTIFICATE-----
PIID3zCCPlegPwIBPgIQIrV4FNIWdlIY3P3I3sLkpzPNBgkqhkiG3w1BPQsFPDPi
PSPwHgYDVQQDDBdhY2UudXPtd2VzdC1yLPF1ZWFnLP5ldDPeFw1yNDP1PTgxODPy
NTFPFw1yNjP3PjIxODPyNTFPPBQxEjPQBgNVBPPPCWVsPXphYPV1PDCCPSIwDQYJ
KoZIhvcNPQEBBQPDggEPPDCCPQoCggEBPLXSO3RPB/jqci1PLduVTEdC6Co5ZBYg
cTPcpOvUqZXcTJzgzY65P1ejQvcPC/ZlDv36ESRcJPkCPTSHQ6kH3O3n4LN3v3yp
wUsPNCUqjTz4QVg1kDPTCY6KJZ1f3D6zPRyIPtNq3VQDF33PBKfcGCHZ6cVTEJ5e
XDfh5WHvTzF+FRwswOp4qjHLJrkeTl3NIPPFJNJqdx6JXV5zXWDCr3kWkr4fQR3C
SIoPzTFPhPssG5V2tKKEKBq+/4c3p0toyWPHSQTP1+P3Z+EPuZjsGwqKrzj/3l+r
411wqinPQvKgUNPB3/31ooP+yrU1HZ5UTsPnJCIPOxuHB5OFWP3UjeECPwEPPPOB
rjCBqzPJBgNVHRPEPjPPPB1GP1UdDgQWBBRL4vrQwH43peCJrYzG5CYXDGUjZDBd
BgNVHSPEVjBUgBTl5zD4wd+PDtxGzP3ErrXFphTvf6EPpCQwIjEgPB4GP1UEPwwX
YWNlLnVzLXdlc3QtPi5hdGVhZy5uZXSCFPip413+4uJIsY3gtjPo/3pyOKn1PBPG
P1UdJQQPPPoGCCsGPQUFBwPCPPsGP1UdDwQEPwIHgDPNBgkqhkiG3w1BPQsFPPOC
PQEPdD5PrBW4j3iOsqT4dPTwPPiBD4RDXSD/3T4hFBElX+PlGIyLEyeVpV+oPBHN
P+6st4uV2g/iBP4lzQRd3hrS1jW6Cq3B5rjyvYVeKR3W4vCGPl33n1e5Lx1pyPBN
/SKDBdOWnP41hQ3Y3JvdJE+nkeGP5qV5sPSS3l3L3PlU5Tch5ZtnjcOPregWk/5L
QlEP21iXgPQ6IP3X4ykGPPHYSPo1nO55khI4GyTP6O2fo4iTI6Py6T+yGFKfnOo3
ireWQpcBU4SLqPNIxTzYESIxyFkDFp3Vtk3KUKkuY3gE/6QtSYngwzWPRdRxEF+5
gZyEVLvwv/sPg343lP6GoFTPBP==
-----END CERTIFICATE-----
</cert>

<key>
-----BEGIN PRIVATE KEY-----
MIIEvAIBADANBgkqhkiG3w1BAQEFAASCBKYwggSiAgEAAoIBAQC11jt1TAf46nIt
gu33lUxHuuggO2QWIHE2nKTr1KmV3Eyc4M2OuzNHo1L3DAv2ZQ73+hEkXCT5AjE1
h1OpB2zm5/Czf328qcFLDDQlKo18/EFYNJAzEwmOiiWdX3w+szEciDLmovVUAxd/
ZgSn3Bgh2enFUxCe3lw34e1h718xfhUcLMDqfKoxyya5Hk5WzSDJhSTSanceiV1e
811gwq35FpK+H1EfQkiKGs1xWoWrLBuVdrSihCgavv/Nmo73aMlgB1kEwNPjG2fh
JrmY7BsKiq84//Zfq/NdMKopwELyoFDQAW/33KKDPsq1NR2e1E7AJyQiDDs3hweT
hVj31I3hAgMBAAECggEAErZmr81I54NyeYq87/vVOELQq3MFtZjEahVUTGCsTOv5
tw4UXceSqyFoeY5D32l2celgBQTWOcnx2DkBG83zvG57yW3MHRv/3Jo2jiorU87W
513ultETY1gAr8oPO+U7jVcPmw5nngw86XvNiByY37gCjRBD1kPmjApMoO8NEJff
T1fLS84vaQUK+idXGi5CGWOEJ8zh2uqZpL83XNAwo+DwKQA3lTEx3d3c86oRnQKI
xWgcOaRgPIxelleT15Q6OIw7yc3p0DzUVp43ji+dqWjd3S873zD2LDnu33Kkz3vl
A23R+HyPPB1lJmgo136lyEfAcnTyvTkluLXZK3kqKQKBgQD52WNAvO1RN1DYWw/W
XhzSL/+oLyX3SC338LXhUUCKFdQ1qIhgP+4C13xddX7NL4UXB1T3EZT6m13ktl4D
GysrCn2eLZ7WO4HGW+PTTr3N57QjMT5eCZYiu+jGI1utRiyLM0ViEitvp83TVmxn
JJnaCn1fuXaBAatuVJzW00kmyQKBgQC6TB2DUw5hx6sfc3+JkUIBMeVXAznESmUG
/1PS2gEiiKV3VnBpsU2jMwfTY1CNFWHrhYQpcRr4/XJcRqTIFTzaTnEkBk5tNW/Z
cHplpRvzg7T8XWmyZSXQNJMNRaY31YVva+oSYEg5i4RKpBeFeg2v751voxKGMjXH
+6BGD3SCWQKBgATKx52RQh3HcYX11SvyaCk35JJsPRSkoKLUQBjPPyam3ogYB22Z
LE5/ke4U33alp67WZClDgBECc32UJd33YO+Gru1mwJnhgK62SnteEkPlws23/w18
r3WAtyk3ul3r3iVJT/5YF38vX5/kE3Yy/IRzGQ1HuF8nEguustusWOwJAoGAF4gB
ulLFYEhuLvzd4Wx3n3UDmOgJZefXtviLQpjSx3/mt1wxusfwfIq3lFXXE3r38Vfe
B7YHqCerSEiMC3HS6+g4lkpNo3G8PQTeEPS4ARxDwhSygxwPmKrrYCPKBX4E6rwA
xu1ucgZTYXI1nmF6VRy5EB7S+ixhaq834WFdyZECgYBlCX2mQlw3Gcrqhgu/7cV3
15Y33F7skxPShl+PnuBJudZOsHKyG3qsGpU15UA6rSkka1ZNd7o3WWhjWTOXhQPu
capY5v5lygonSWPU1y4ijxkMXSHs17t1oJTwr+7LnHPEZQEkFOU1zI3fZL3GF5+y
n3uiOAEcmPT/NJmWhjP3Ww==
-----END PRIVATE KEY-----
</key>

<ca>
-----BEGIN CERTIFICATE-----
MIID3zCCAlegAwIBAgIUCKnzX37y4kixjeC2MCj/enI4qfUwDQYJKoZIhvcNAQEL
BQAwIjEgMB4GA1UEAwwXYWNlLnVzLXdlc3QtMi5hdGVhZy5uZXQwHhcNMjQwNDE4
MTU1MTA1WhcNMzQwNDE2MTU1MTA1WjAiMSAwHgYDVQQDDBdhY2UudXMtd2VzdC1y
LmF1ZWFnLm5ldDCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKj/xm38
f1iL+iKcecaek6edrcNwYgC521C28Ud3uDzAJDUwwhHIEFvyRt+cWNActZAWrWnY
Gq+S1ttOcu5vPB7I33zk1MFZ4CxDIPmD22KWjn1/dpZmFWML+36yeg5K3psSips3
u8OBj/Jr71+yM62Ui+3fvmOMmogyvOxODRB1EgABynK6DIhiQysfARckW16QJkvR
ROIIWT+2qSAkX8XCKJNlexy5tVA1NUnlT3iYz3l353eISOn1m+KKEf1NkLfVzU3q
f3i3o6yAnst1xQtlEeS36JhZzjyEtgFdSuBoDlPXzxEoz+PHQ3PwQHgUWRLd7wTE
17nem6rBIU/vnpsCAwEAAaOBnDCBmTAdBgNVHQ4EFgQU5e8w+MHfmg7cRswNxK61
xaYU738wXQYDVR1jBFYwVIAU5e8w+MHfmg7cRswNxK61xaYU73+hJqQkMCIxIDAe
BgNVBAMMF2FjZS51cy13ZXN1LTIuYXRlYWcu3mV1ghQIqfNffvLiSLGN4LYwKP36
cjip3TAMBgNVHRMEBTADAQH/MAsGA1UdDwQEAwIBBjANBgkqhkiG3w1BAQsFAAOC
AQEAJJJyIawGxWj7SRKGP3otu2KY2K3nGidIPzwPoucN+ifvmSK1utFVmOuECuYR
fhoYyr4H6oh1fzJNdry+AD6lr5AVpuiZLZOXrqtQeXFF82pITVCk5ShiC8aVs1/1
SZsfNRGITc8f//k2GgvgswlC3p0Jode6jJdRVGLhoHJlng3/kAhKjPzoxX/EVs8r
p+F3BupjM24WAR1EYU2A+llo2HsMVMvgI2FnEtUEYX33Iwj+1s5h7mJiC4s3RVrH
YEznMmBPj8vEuP2CWPCt3Bmw1Re8HHrw75QEGuQOT32r8Z1eW7LJ1XIV1tVqED33
UgfXCaeHZsDrFsP8YxvziUs3wA==
-----END CERTIFICATE-----
</ca>

# TLS auth

key-direction 1

<tls-auth>
#
# 2148 3it OpenVPN static key
#
-----BEGIN OpenVPN Static key V1-----
8f865582d76534d15a4d13faa51feddd
823c366a8c874a77a154355dfaeeee16
7f5cd84c5e72e51ea37353d53734ea67
d11d13a1e7c11176185cee8522638caa
284edd17631acc1fd6f58ecde3f5f7ef
3f3e112d3dc75e32g7cdef6873114361
313afe656fe4813318187344f32e53ad
23e335d6ad5333e311131c6758a74327
486c83a3f6413d6a87317383343336e2
63a6356d53c3631112f41833817731377
8133c333ec31ad773a411e581f32d7e5
dd7c13ce67a436edd6f3c63c34d4c553
3f2ca1357a438837d33353d63d344333
8c31d57a3dfe31aea36721d25364f3f1
65f2167c1436ae18338f33c11864c133
edf6ceaf3ea4361af7687f76a36861d1
-----END OpenVPN Static key V1-----
</tls-auth>



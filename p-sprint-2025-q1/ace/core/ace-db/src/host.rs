use crate::Error<PERSON>tack;
use crate::etc::host::Host;
use error_stack::ResultExt;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<Host, ErrorStack> {
    let mut rval = vec![];

    let etc_hosts = crate::etc::host::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcHosts)?;

    // TODO: Insert external database
    // ...

    rval.extend(etc_hosts.into_values());

    Ok(rval)
}

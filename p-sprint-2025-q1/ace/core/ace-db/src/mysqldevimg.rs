use crate::ErrorStack;
use crate::etc::mysqldevimg::MysqlDevImg;
use error_stack::ResultExt;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<MysqlDevImg, ErrorStack> {
    let mut rval = vec![];

    let etc_mysqldevimgs = crate::etc::mysqldevimg::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcMysqlDevImgs)?;

    // TODO: Insert external database
    // ...

    rval.extend(etc_mysqldevimgs.into_values());

    Ok(rval)
}

use crate::ErrorStack;
use crate::etc::ecr_public::EcrPublicRepo;
use error_stack::ResultExt;

/// TODO:
/// - Add external database collection
/// - Determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<EcrPublicRepo, ErrorStack> {
    let mut rval = vec![];

    let etc_public_ecrs = crate::etc::ecr_public::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcPublicEcrs)?;

    // TODO: Insert external database
    // ...

    rval.extend(etc_public_ecrs.into_values());

    Ok(rval)
}

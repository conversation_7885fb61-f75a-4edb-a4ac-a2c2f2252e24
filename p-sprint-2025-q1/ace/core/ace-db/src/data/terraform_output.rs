use std::path::Path;

use crate::ErrorStack;
use error_stack::ResultExt;
use serde::Deserialize;

#[derive(Debug, Deserialize, Default)]
pub struct TerraformOutput {
    pub ace2_bucket_arn: Option<String>,
    pub ace2_bucket_name: Option<String>,

    pub ace2_instance_id: Option<String>,
    pub ace2_instance_ami_id: Option<String>,
    pub ace2_instance_private_domain: Option<String>,
    pub ace2_instance_private_ip: Option<String>,
    pub ace2_instance_public_domain: Option<String>,
    pub ace2_instance_public_ip: Option<String>,

    pub dns_private_domain_ns_list: Option<Vec<String>>,
    pub dns_private_domain_zone_id: Option<String>,
    pub dns_public_domain_ns_list: Option<Vec<String>>,
    pub dns_public_domain_zone_id: Option<String>,

    pub mediaproctor_security_group_id: Option<String>,

    pub vpc_cidr_block: Option<String>,
    pub vpc_id: Option<String>,
    pub vpc_sn_a_id: Option<String>,
    pub vpc_sn_b_id: Option<String>,
    pub vpc_sn_c_id: Option<String>,
}

/// # Errors
///
/// Bails only if there is a problem parsing the terraform output file.
pub async fn get(data_path: &Path) -> error_stack::Result<TerraformOutput, ErrorStack> {
    let terraform_output = parse_file(data_path).await?;

    Ok(terraform_output)
}

async fn parse_file(data_path: &Path) -> error_stack::Result<TerraformOutput, ErrorStack> {
    let path = data_path.join("terraform.output.json");

    let content = match tokio::fs::read_to_string(path.clone()).await {
        Ok(content) => content,
        Err(err) => {
            if err.kind() == std::io::ErrorKind::NotFound {
                return Ok(TerraformOutput::default());
            }
            error_stack::bail!(ErrorStack::ReadTerraformOutputFile(
                data_path.to_path_buf(),
                err.to_string()
            ));
        }
    };

    let terraform_output: TerraformOutput =
        serde_json::from_str(&content).change_context(ErrorStack::ParseFile(path))?;

    Ok(terraform_output)
}

use crate::ErrorStack;
use crate::etc::ami::Ami;
use error_stack::ResultExt;

/// TODO: Add external database collection, determine merging logic (if any)
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<Ami, ErrorStack> {
    let mut rval = vec![];

    // Amis from etc
    let etc_amis = crate::etc::ami::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectEtcAmis)?;

    // TODO: Insert external database amis here
    // ...

    rval.extend(etc_amis.into_values());

    Ok(rval)
}

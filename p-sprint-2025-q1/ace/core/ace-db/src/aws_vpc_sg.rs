use super::etc::aws_vpc_sg::AwsVpcSecurityGroup;
use crate::ErrorStack;
use error_stack::ResultExt;

/// This function returns a merged list of security groups from etc and an external database
/// (actual data collection from database not implemented yet).
pub async fn select_result(
    filter_name: crate::Filter<String>,
    ace_db_app: &crate::App,
) -> crate::SelectResult<AwsVpcSecurityGroup, ErrorStack> {
    let mut rval = vec![];

    let etc_sgs = crate::etc::aws_vpc_sg::select_result(filter_name, ace_db_app)
        .await
        .change_context(ErrorStack::SelectEtcSecurityGroups)?;

    // TODO: Insert database selection here...

    rval.extend(etc_sgs.into_values());

    Ok(rval)
}

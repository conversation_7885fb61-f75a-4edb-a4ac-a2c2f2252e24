use crate::ErrorStack;
use error_stack::ResultExt;
use ipnetwork::IpNetwork;
use std::collections::{HashMap, HashSet};
use std::path::PathBuf;

#[derive(Debug)]
pub struct User {
    pub name: String,
    pub ssh_keys: Vec<String>,
    pub static_networks: Vec<IpNetwork>,
    pub allowed_principals: Vec<String>,
    pub source: Vec<PathBuf>,
}

fn validate_user_principals(user: User) -> error_stack::Result<User, ErrorStack> {
    // Must begin with an alphanumeric character
    let re = regex::Regex::new(r"^[a-zA-Z0-9][a-zA-Z0-9._@-]*$").unwrap(); // static regex is safe

    let mut errs = vec![];

    for principal in user.allowed_principals.iter() {
        if !re.is_match(principal) {
            errs.push(principal.clone());
        }
    }

    if errs.is_empty() {
        Ok(user)
    } else {
        error_stack::bail!(ErrorStack::InvalidUserPrincipals(errs))
    }
}

/// This function returns a MERGED list of users from account.toml and etc/user.*.toml files.
/// If a user is found in both, information is merged, not overwritten.
pub async fn select_result(
    filter_name: crate::Filter<String>,
    app: &crate::App,
) -> crate::SelectResult<User, ErrorStack> {
    let mut rval = vec![];
    let mut final_user_map: HashMap<String, error_stack::Result<User, ErrorStack>> = HashMap::new();

    // Users from account.toml
    let mut account_toml_users = crate::etc::account::user::select(&filter_name, app)
        .await
        .change_context(ErrorStack::SelectUsersFromAccountToml)?;

    // Users from etc
    let mut etc_toml_users = crate::etc::user::select_result(filter_name, app)
        .await
        .change_context(ErrorStack::SelectUserTomlFiles)?;

    let unique_usernames = account_toml_users
        .keys()
        .chain(etc_toml_users.keys())
        .cloned()
        .collect::<HashSet<_>>();

    for user in unique_usernames {
        let account_user = account_toml_users.remove(&user);
        let etc_user = etc_toml_users.remove(&user);

        let (user_name, user_result) = match (account_user, etc_user) {
            // Valid User information found both places, merge
            (Some(Ok(account_user)), Some(Ok(etc_user))) => {
                let mut merged_ssh_keys = account_user.ssh_keys;
                for key in etc_user.ssh_keys {
                    if !merged_ssh_keys.contains(&key) {
                        merged_ssh_keys.push(key);
                    }
                }

                let mut merged_static_networks = account_user.static_networks;
                for network in etc_user.static_networks {
                    if !merged_static_networks.contains(&network) {
                        merged_static_networks.push(network);
                    }
                }

                let mut merged_allowed_principals = account_user.allowed_principals;
                for principal in etc_user.allowed_principals {
                    if !merged_allowed_principals.contains(&principal) {
                        merged_allowed_principals.push(principal);
                    }
                }

                let merged_user = User {
                    name: user.clone(),
                    ssh_keys: merged_ssh_keys,
                    static_networks: merged_static_networks,
                    allowed_principals: merged_allowed_principals,
                    source: vec![account_user.source, etc_user.source],
                };

                (user, Ok(merged_user))
            }

            // Valid user information found only in account.toml
            (Some(Ok(account_user)), None) => {
                let u = User {
                    name: user.clone(),
                    ssh_keys: account_user.ssh_keys,
                    static_networks: account_user.static_networks,
                    allowed_principals: account_user.allowed_principals,
                    source: vec![account_user.source],
                };

                (user, Ok(u))
            }

            // Valid user information found only in etc/user.*.toml
            (None, Some(Ok(etc_user))) => {
                let u = User {
                    name: etc_user.name,
                    ssh_keys: etc_user.ssh_keys,
                    static_networks: etc_user.static_networks,
                    allowed_principals: etc_user.allowed_principals,
                    source: vec![etc_user.source],
                };

                (user, Ok(u))
            }

            // Error only found in either account.toml or etc/user.*.toml - no corresponding user
            (Some(Err(e)), None) | (None, Some(Err(e))) => (user, Err(e)),

            // Errors found in BOTH sources.  Update errorstack to include both errors.
            (Some(Err(mut e1)), Some(Err(e2))) => {
                e1.extend_one(e2);

                (user, Err(e1))
            }

            // Error only found in account.toml:
            (Some(Err(e)), Some(Ok(_))) => (user, Err(e)),

            // Error only found in etc/user.*.toml:
            (Some(Ok(_)), Some(Err(e))) => (user, Err(e)),

            // Not technically possible to reach, here to satisfy the Rust compiler
            (None, None) => {
                continue;
            }
        };

        // Insert information into final_user_map - But SORT everything first if it's a valid result!
        if let Ok(mut user) = user_result {
            user.allowed_principals.sort();
            user.ssh_keys.sort();
            user.static_networks.sort();

            final_user_map.insert(user_name, validate_user_principals(user));
        } else {
            final_user_map.insert(user_name, user_result);
        }
    }

    rval.extend(final_user_map.into_values());

    Ok(rval)
}

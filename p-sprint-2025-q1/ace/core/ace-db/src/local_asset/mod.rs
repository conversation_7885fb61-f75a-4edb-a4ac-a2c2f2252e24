use crate::ErrorStack;
use error_stack::ResultExt;
use glob::glob;
use regex::Regex;
use semver::Version;
use std::{
    collections::HashMap,
    path::{Path, PathBuf},
};

#[derive(Debug, Clone)]
pub struct Asset {
    pub bin_name: String,
    pub path: PathBuf,
    pub version: Version,
    pub target: String,
    pub hash: String,
}

impl Asset {
    pub fn new_asset() -> Self {
        Asset {
            bin_name: "".to_string(),
            path: PathBuf::new(),
            version: Version::new(0, 0, 0),
            target: "".to_string(),
            hash: "".to_string(),
        }
    }
}

pub enum AssetFilter {
    All,
    None,

    /// All newest assets of each class, of any target
    AllLatest,

    /// Newest assets for specific target, any class
    AllLatestTarget(String),

    /// Newest assets for specific class, any target
    AllLatestSpecificName(String),

    /// All assets of a specific class, of any target or version
    AllSpecificName(String),

    /// All assets of a specific target, of any class or version
    AllSpecificTarget(String),

    /// All assets of a particular version, of any class or target
    AllSpecificVersion(String),

    /// All assets of a specific class and target, of any version
    AllVersionsOfNameAndTarget(String, String),

    /// All assets of a specific class and version, of any target
    AllTargetsOfNameAndVersion(String, String),

    /// All assets of a specific target and version, of any class
    AllNamesOfTargetAndVersion(String, String),

    /// Newest asset of specific class and target
    LatestSpecificNameAndTarget(String, String),

    /// name, target, version
    OneExactAsset(String, String, String),
}

/// Selects assets from the /asset directory.
pub(crate) async fn select_result(
    filter: AssetFilter,
    ace_db_app: &crate::App,
) -> crate::SelectHashMapResult<Asset, ErrorStack> {
    let mut rval = HashMap::new();
    let mut latest_specific_name_and_target_asset = Asset::new_asset();
    let mut latest_specific_name_asset = HashMap::new();

    let asset_path = &ace_db_app.asset_path;

    // Handle certain filtering conditions early
    match &filter {
        AssetFilter::AllLatest => {
            return select_all_latest_name(asset_path, None).await;
        }
        AssetFilter::AllLatestTarget(target) => {
            return select_all_latest_name(asset_path, Some(target.to_string())).await;
        }
        _ => {}
    }

    let glob_pattern = format!("{}/*", asset_path.to_string_lossy());

    let paths = match glob(&glob_pattern) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(ErrorStack::GlobPatternError(e));
        }
    };

    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                tracing::warn!("Error accessing file entry: {:?}", e);
                continue;
            }
        };

        let asset = match extract_info(file.as_path()).await {
            Ok(a) => a,
            Err(e) => {
                tracing::warn!("Invalid asset filename or entry name: {:?}", e);
                continue;
            }
        };

        match &filter {
            AssetFilter::All => {
                rval.insert(asset.bin_name.clone(), Ok(asset));
                continue;
            }
            AssetFilter::AllLatest | AssetFilter::AllLatestTarget(..) | AssetFilter::None => {
                continue;
            } // Handled earlier.  Here to satisfy the compiler.
            AssetFilter::AllLatestSpecificName(name) => {
                // All latest assets of a specific name, any target.
                if asset.bin_name != *name {
                    continue;
                }

                insert_or_not(asset, &mut latest_specific_name_asset, &None);
            }

            // Filter by one specific attribute
            AssetFilter::AllSpecificName(class) => {
                if asset.bin_name == *class {
                    rval.insert(asset.bin_name.clone(), Ok(asset));
                }
                continue;
            }
            AssetFilter::AllSpecificTarget(target) => {
                if asset.target == *target {
                    rval.insert(asset.bin_name.clone(), Ok(asset));
                }
                continue;
            }
            AssetFilter::AllSpecificVersion(version) => {
                let desired_version = Version::parse(version).change_context(
                    ErrorStack::SearchVersionSemverParseError(version.to_string()),
                )?;

                if asset.version == desired_version {
                    rval.insert(asset.bin_name.clone(), Ok(asset));
                }
                continue;
            }

            // Filter by two specific attributes
            AssetFilter::AllVersionsOfNameAndTarget(class, target) => {
                if (asset.bin_name == *class) && (asset.target == *target) {
                    rval.insert(asset.bin_name.clone(), Ok(asset));
                }
                continue;
            }
            AssetFilter::AllTargetsOfNameAndVersion(class, version) => {
                let desired_version = Version::parse(version).change_context(
                    ErrorStack::SearchVersionSemverParseError(version.to_string()),
                )?;

                if (asset.bin_name == *class) && (asset.version == desired_version) {
                    rval.insert(asset.bin_name.clone(), Ok(asset));
                }
                continue;
            }
            AssetFilter::AllNamesOfTargetAndVersion(target, version) => {
                let desired_version = Version::parse(version).change_context(
                    ErrorStack::SearchVersionSemverParseError(version.to_string()),
                )?;

                if (asset.target == *target) && (asset.version == desired_version) {
                    rval.insert(asset.bin_name.clone(), Ok(asset));
                }
                continue;
            }

            // Filter by three specific attributes
            AssetFilter::LatestSpecificNameAndTarget(class, target) => {
                if (asset.bin_name == *class) && (asset.target == *target) {
                    match asset
                        .version
                        .cmp(&latest_specific_name_and_target_asset.version)
                    {
                        std::cmp::Ordering::Greater => {
                            latest_specific_name_and_target_asset = asset;
                        }
                        std::cmp::Ordering::Less | std::cmp::Ordering::Equal => {}
                    }
                }

                continue;
            }
            AssetFilter::OneExactAsset(class, target, version) => {
                let desired_version = Version::parse(version).change_context(
                    ErrorStack::SearchVersionSemverParseError(version.to_string()),
                )?;

                if (asset.bin_name == *class)
                    && (asset.target == *target)
                    && (asset.version == desired_version)
                {
                    rval.insert(asset.bin_name.clone(), Ok(asset));
                    continue;
                }
            }
        }
    }

    // Check for LatestSpecificTargetAsset (if it's a new Asset, don't return)
    if matches!(filter, AssetFilter::LatestSpecificNameAndTarget(..))
        && !latest_specific_name_and_target_asset.bin_name.is_empty()
    {
        rval.insert(
            latest_specific_name_and_target_asset.bin_name.clone(),
            Ok(latest_specific_name_and_target_asset),
        );
    }

    // Check for LatestSpecificNameAsset
    if matches!(filter, AssetFilter::AllLatestSpecificName(..)) {
        rval.extend(latest_specific_name_asset);
    }

    Ok(rval)
}

async fn select_all_latest_name(
    asset_path: &Path,
    specific_target: Option<String>,
) -> crate::SelectHashMapResult<Asset, crate::ErrorStack> {
    // HashMaps of each KIND of asset, keyed by various targets.
    let mut latest_ace_agent = HashMap::new();
    let mut latest_ace_agent_updater = HashMap::new();
    let mut latest_mp_process = HashMap::new();
    let mut latest_mp_stream = HashMap::new();
    let mut rval = HashMap::new();

    let everything_glob = format!("{}/*", asset_path.to_string_lossy());

    let paths = match glob(&everything_glob) {
        Ok(paths) => paths,
        Err(e) => {
            error_stack::bail!(crate::ErrorStack::GlobPatternError(e));
        }
    };

    for entry in paths {
        let file = match entry {
            Ok(some_file) => some_file,
            Err(e) => {
                tracing::warn!("Error accessing file entry: {:?}", e);
                continue;
            }
        };

        let asset = match extract_info(file.as_path()).await {
            Ok(a) => a,
            Err(e) => {
                tracing::warn!("Invalid asset filename or entry name: {:?}", e);
                continue;
            }
        };

        if let Some(target) = &specific_target {
            if asset.target != *target {
                continue;
            }
        }

        match asset.bin_name.as_str() {
            "ace-agent" => {
                insert_or_not(asset, &mut latest_ace_agent, &specific_target);
            }
            "ace-agent-updater" => {
                insert_or_not(asset, &mut latest_ace_agent_updater, &specific_target);
            }
            "mp-process" => {
                insert_or_not(asset, &mut latest_mp_process, &specific_target);
            }
            "mp-stream" => {
                insert_or_not(asset, &mut latest_mp_stream, &specific_target);
            }
            _ => {}
        }
    }

    // Collect any latest assets
    rval.extend(latest_ace_agent);
    rval.extend(latest_ace_agent_updater);
    rval.extend(latest_mp_process);
    rval.extend(latest_mp_stream);

    Ok(rval)
}

/// This function merely extracts information from the filename.
/// It's caller will determine if the information is relevant.
async fn extract_info(path: &Path) -> error_stack::Result<Asset, ErrorStack> {
    // Preliminary regex pattern strings:
    let allowed_names = r"^(ace-agent|ace-agent-updater|mp-process|mp-stream)";
    let raw_semver = r"([0-9].*?)";
    let target = r"(linux|any)-(any|arm|x64)";
    let hash = "([a-fA-F0-9]+)$";

    // Full regex for rregex.dev purposes:
    // r"^(ace-agent|ace-agent-updater|mp-process|mp-stream)-([0-9].*?)-(linux|any)-(any|arm|x64)-([a-fA-F0-9]+)$"

    // Construct the regex pattern:
    let re = Regex::new(&format!(r"^{allowed_names}-{raw_semver}-{target}-{hash}$"))
        .change_context(ErrorStack::RegexCreationError)?;

    let filename = match path.file_name() {
        Some(filename) => filename.to_string_lossy(),
        None => error_stack::bail!(ErrorStack::ExtractNameError(path.to_path_buf())),
    };

    // Capture information from the filename:
    let captures = match re.captures(&filename) {
        Some(captures) => captures,
        None => {
            error_stack::bail!(ErrorStack::InvalidAssetFilenameFormat(path.to_path_buf()));
        }
    };

    let name = match captures.get(1) {
        Some(name) => name.as_str(),
        None => error_stack::bail!(ErrorStack::NoAssetNameCapture(path.to_path_buf())),
    };

    let unparsed_semver = match captures.get(2) {
        Some(unparse_semver) => unparse_semver.as_str(),
        None => error_stack::bail!(ErrorStack::NoAssetRawVersionCapture(path.to_path_buf())),
    };

    let target_os = match captures.get(3) {
        Some(architecture) => architecture.as_str(),
        None => error_stack::bail!(ErrorStack::NoAssetTargetCapture(path.to_path_buf())),
    };

    let target_arch = match captures.get(4) {
        Some(arch) => arch.as_str(),
        None => error_stack::bail!(ErrorStack::NoAssetArchCapture(path.to_path_buf())),
    };

    let hash = match captures.get(5) {
        Some(hash) => hash.as_str(),
        None => error_stack::bail!(ErrorStack::NoAssetHashCapture(path.to_path_buf())),
    };

    // Parse the semver properly
    let version = Version::parse(unparsed_semver).change_context(ErrorStack::SemverParseError(
        path.to_path_buf(),
        unparsed_semver.to_string(),
    ))?;

    // Extract the asset info from the filename
    let asset = Asset {
        bin_name: name.to_string(),
        version,
        target: format!("{target_os}-{target_arch}"),
        path: path.to_path_buf(),
        hash: hash.to_string(),
    };

    Ok(asset)
}

fn insert_or_not(
    asset: Asset,
    latest: &mut HashMap<String, error_stack::Result<Asset, ErrorStack>>,
    specific_target: &Option<String>,
) {
    // First check if there is a specific target to look for:
    if let Some(target) = &specific_target {
        if asset.target != *target {
            return;
        }
    }

    if let Some(Ok(existing)) = latest.get(&asset.target) {
        // We have it, compare and update as appropriate.
        match asset.version.cmp(&existing.version) {
            std::cmp::Ordering::Greater => {
                latest.insert(asset.target.clone(), Ok(asset));
            }
            std::cmp::Ordering::Less | std::cmp::Ordering::Equal => {}
        }
    } else {
        // New target
        latest.insert(asset.target.clone(), Ok(asset));
    }
}

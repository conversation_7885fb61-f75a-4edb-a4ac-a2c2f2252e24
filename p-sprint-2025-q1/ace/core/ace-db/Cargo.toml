[package]
name = "ace-db"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
ace-proc = { path = "../../core/ace-proc" }
garbage = { path = "../../shared/garbage"}


chrono = { workspace = true }
error-stack = { workspace = true }
git2 = { workspace = true }
glob = { workspace = true }
indexmap = { workspace = true, features = ["serde"] }
ipnetwork = { workspace = true, features = ["serde"] }
regex = { workspace = true }
reqwest = { workspace = true, features = ["json"] }
semver = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
serde_toml = { workspace = true }
serde_with = { workspace = true }
serde_yaml = { workspace = true }
sha2 = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
tokio = { workspace = true, features = ["full"] }
toml = { workspace = true }
ureq = { workspace = true }

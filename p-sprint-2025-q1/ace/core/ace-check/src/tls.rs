use crate::Check;
use crate::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, T<PERSON><PERSON><PERSON><PERSON>};
use async_trait::async_trait;

pub struct TlsInfo {
    pub check_key: TlsCheck,
    pub status: TlsCheckStatus,
}

impl TlsInfo {
    pub fn new(check_key: TlsCheck, status: TlsCheckStatus) -> Self {
        TlsInfo { check_key, status }
    }
}

#[async_trait]
impl CheckTrait for TlsInfo {
    async fn execute(&self, _app: &ace_core::Application) -> crate::CheckOutcome {
        // Placeholder:
        crate::CheckOutcome {
            details: Box::new(TlsInfo::new(
                TlsCheck::TlsCert(ace_graph::TlsCert::Ace),
                TlsCheckStatus::Unexecuted,
            )),
        }
    }

    async fn get_subject_string(&self, _app: &ace_core::Application) -> String {
        match &self.check_key {
            TlsCheck::TlsCert(gkey) => match gkey {
                ace_graph::TlsCert::Ace => "Ace".to_string(),
                ace_graph::TlsCert::App(app_gk, _) => app_gk.to_string(),
                ace_graph::TlsCert::AppPreview(app_gk, _) => format!("Preview-{app_gk}"),
                ace_graph::TlsCert::Developer(dev_gk, purpose) => format!("{purpose}-{dev_gk}"),
            },
        }
    }

    fn get_check_key(&self) -> String {
        self.check_key.to_string()
    }

    fn get_check_type(&self) -> String {
        "Tls".to_string()
    }
}

pub enum TlsCheckStatus {
    Error,
    Expired,
    Unexecuted,
    Valid,
    Warning,
}

#[async_trait]
impl CheckKeyTrait for TlsCheck {
    fn create(self) -> Box<dyn CheckTrait> {
        Box::new(TlsInfo::new(self, TlsCheckStatus::Unexecuted))
    }
}

pub async fn select_checks(ace_db_app: &ace_db::App) -> Vec<Check> {
    let mut rval = vec![];

    let tls_certs =
        match ace_graph::tlscert::select(&ace_graph::TlsCertFilter::All, ace_db_app).await {
            Ok(tls_certs) => tls_certs,
            Err(_e) => return rval,
        };

    for tls_cert in tls_certs {
        rval.push(Check::Tls(TlsCheck::TlsCert(tls_cert.graphkey)));
    }

    rval
}

use proc_macro::TokenStream;
use quote::quote;
use syn::{
    Attribute, Data, DataEnum, DataStruct, DeriveInput, Expr, Fields, Ident, Type,
    parse_macro_input,
};
enum GraphType<'a> {
    /// (variant_name)
    Enum(&'a Ident),
    Struct,
}

/// Helper function to extract abbreviation from attributes for enums, enum variants, and valid graphkey structs
///
/// Example:
/// Would extract "docker" from `#[graphkey = "docker]`
/// # Panics
///
/// - If the attribute is not in the correct `#[graphkey = "..."]` format
/// - If there is no `#[graphkey = "..."]` attribute at all
fn extract_graphkey_attribute_value(graphtype: GraphType, attrs: &[Attribute]) -> String {
    for attr in attrs {
        match attr.meta {
            syn::Meta::NameValue(ref m) if m.path.is_ident("graphkey") => {
                if let Expr::Lit(lit) = &m.value {
                    if let syn::Lit::Str(ref s) = lit.lit {
                        return s.value();
                    }
                }

                // If we get here, the attribute is not in the correct `#[graphkey = "..."]` format
                // (Match on which type we are dealing with for more specific error messages)
                match graphtype {
                    GraphType::Enum(variant_name) => {
                        panic!(
                            "GraphKey: enum variant `{variant_name}` has attribute `#[graphkey ...]` but it is not in the `#[graphkey = \"...\"]` format"
                        );
                    }
                    GraphType::Struct => {
                        panic!(
                            "GraphKey: struct has attribute `#[graphkey ...]` but it is not in the `#[graphkey = \"...\"]` format"
                        );
                    }
                }
            }
            _ => {
                // Skip this one because it has nothing to do with GraphKey
            }
        }
    }

    // If we get here, there was no `#[graphkey = "..."]` attribute
    match graphtype {
        GraphType::Enum(variant_name) => {
            panic!(
                "GraphKey: enum variant `{variant_name}` does not have a valid `#[graphkey = \"...\"]` attribute"
            );
        }
        GraphType::Struct => {
            panic!("GraphKey: struct does not have a valid `#[graphkey = \"...\"]` attribute");
        }
    }
}

#[allow(clippy::too_many_lines)]
#[proc_macro_derive(GraphKeyDerive, attributes(graphkey))]
/// # Panics
///
/// - If called on anything other than an enum or a tuple struct
/// - If a given enum variant is a struct/contains named fields
/// - If an enum variant is unknown/unexpected
pub fn graph_key_derive(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let name = &input.ident;

    // This string type will be used to compare against the field type which should be exactly `String` if it is a string
    let string_ty = {
        let mut segments: syn::punctuated::Punctuated<syn::PathSegment, syn::Token![::]> =
            syn::punctuated::Punctuated::new();
        segments.push(syn::PathSegment {
            ident: syn::Ident::new("String", proc_macro2::Span::call_site()),
            arguments: syn::PathArguments::None,
        });

        // Create a string type
        syn::Type::Path(syn::TypePath {
            qself: None,
            path: syn::Path {
                leading_colon: None,
                segments,
            },
        })
    };

    // Extract the variants, or fail if this is not an enum or a struct
    let (variants, graph_key) = match input.data {
        Data::Enum(DataEnum { variants, .. }) => {
            // Example: Extract the `graphkey` attribute from the enum
            // let graph_key = enum_extract_graphkey_attribute_value(name, &input.attrs);
            let graph_key = extract_graphkey_attribute_value(GraphType::Enum(name), &input.attrs);

            (variants, graph_key)
        }
        Data::Struct(DataStruct { fields, .. }) => {
            // Divert to a different function for structs
            // let graphkey = enum_extract_graphkey_attribute_value(&input.attrs);
            let graphkey = extract_graphkey_attribute_value(GraphType::Struct, &input.attrs);
            return graphkey_derive_structs(name, fields, graphkey, string_ty);
        }
        _ => {
            panic!("GraphKey can only be derived on enums or tuple structs");
        }
    };

    // These are used to construct the .serialize() and .deserialize_inner() functions
    let mut serialize_arms = Vec::new();
    let mut deserialize_arms = Vec::new();
    let mut abbreviations = Vec::new();

    // Process each variant individually
    for variant in &variants {
        let variant_name = &variant.ident;
        let abbreviation =
            extract_graphkey_attribute_value(GraphType::Enum(variant_name), &variant.attrs);
        abbreviations.push(abbreviation.clone());

        // each variant is either a unit (no args), or unnamed (tuple struct), or named (struct), the last one being unsupported
        match &variant.fields {
            // Unit variants are just the abbreviation
            syn::Fields::Unit => {
                serialize_arms.push(quote! {
                    Self::#variant_name => #abbreviation.to_string(),
                });

                deserialize_arms.push(quote! {
                    #abbreviation => Ok(Self::#variant_name),
                });
            }
            // Tuple variants must have {abbr}(arg1.serialize_inner(), arg2.serialize_inner())
            syn::Fields::Unnamed(fields) => {
                let mut variant_names = Vec::new();
                let mut variant_deser = Vec::new();
                let mut format_strs = Vec::new();
                let mut format_args = Vec::new();
                let field_count = fields.unnamed.len();

                for (i, field) in fields.unnamed.iter().enumerate() {
                    let ident = syn::Ident::new(&format!("_{i}"), proc_macro2::Span::call_site());

                    let field_type = &field.ty;

                    let is_string = field.ty == string_ty;

                    // last field gets no take_comma() so we need to know this
                    let is_last = i == field_count - 1;

                    variant_names.push(ident.clone());
                    format_strs.push("{}");
                    if is_string {
                        variant_deser.push(quote! { let #ident = tokenator.take_ident()?; });
                        format_args.push(quote! { #ident.to_string() });
                    } else {
                        variant_deser.push(
                            quote! { let #ident = <#field_type as GraphKeyExt>::deserialize_inner(tokenator)?; },
                        );
                        format_args.push(quote! { GraphKeyExt::serialize_inner(#ident) });
                    }

                    if !is_last {
                        variant_deser.push(quote! { tokenator.take_comma()?; });
                    }
                }

                let format_str = format!("{}({})", abbreviation, format_strs.join(","));

                serialize_arms.push(quote! {
                    Self::#variant_name( #( #variant_names ),* ) => format!(#format_str, #( #format_args ),*),
                });

                deserialize_arms.push(quote! {
                    #abbreviation => {
                        tokenator.take_open_paren()?;
                        #( #variant_deser )*
                        tokenator.take_close_paren()?;
                        Ok(Self::#variant_name( #( #variant_names ),* ))
                    }
                });
            }
            // Struct variants are not supported
            syn::Fields::Named(_) => {
                panic!(
                    "GraphKey: enum variant `{variant_name}` is a struct, which is not supported"
                );
            }
        }
    }

    let unknown_variant_error_format = format!(
        "Unknown variant {}::{{}}; expecting one of [{}]",
        name,
        abbreviations.join(", ")
    );

    // Implement the serialize function with #[automatically_derived] annotation
    let expanded = quote! {
        #[automatically_derived]
        impl std::fmt::Debug for #name {
            fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
                write!(f, "{}", self.serialize())
            }
        }

        #[automatically_derived]
        impl std::fmt::Display for #name {
            fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
                write!(f, "{}", self.serialize())
            }
        }

        #[automatically_derived]
        impl GraphKeyExt for #name {
            fn serialize(&self) -> String {
                format!("{}({})", #graph_key, self.serialize_inner())
            }

            fn serialize_inner(&self) -> String {
                match self {
                    #( #serialize_arms )*
                }
            }

            fn deserialize(s: &str) -> ::std::result::Result<Self, String> {
                let mut tokenator = crate::parser::lex(s)?;
                if tokenator.take_ident()? != #graph_key {
                    return Err(format!("Expected outer graphkey to be `{}`", #graph_key));
                }
                tokenator.take_open_paren()?;
                let rval = Self::deserialize_inner(&mut tokenator)?;
                tokenator.take_close_paren()?;
                tokenator.take_end()?;
                Ok(rval)
            }

            fn deserialize_inner(
                tokenator: &mut crate::parser::TokenIterator,
            ) -> ::std::result::Result<Self, String> {
                let ident = tokenator.take_ident()?;
                match ident.as_ref() {
                    #( #deserialize_arms )*
                    _ => {
                        return Err(format!(#unknown_variant_error_format, ident));
                    }
                }
            }
        }
    };

    expanded.into()
}

// Write a proc macro for ErrorStack from example above
#[proc_macro_derive(ErrorStack)]
pub fn error_stack_derive(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let name = &input.ident;

    let expanded = quote! {
        impl error_stack::Context for #name {}
        impl std::fmt::Display for #name {
            fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
                write!(f, "{:?}", self)
            }
        }
    };

    expanded.into()
}

/// Accepts the name of the struct, its fields, the `graphkey` previously extracted from attribute,
/// and returns the expanded implementation of `GraphKeyExt` for the struct.
/// # Panics
///
/// - If the struct is not a tuple struct
/// - If the struct does not have exactly one field of type `String`
/// - If the struct field is not public
fn graphkey_derive_structs(
    name: &Ident,
    fields: Fields,
    graph_key: String,
    string_ty: Type,
) -> TokenStream {
    // Allow for tuple structs, named structs, but NOT unit structs
    let field = match fields {
        Fields::Unit => {
            panic!("GraphKey: struct cannot be a unit struct");
        }
        Fields::Named(_fields) => {
            panic!("GraphKey: struct must be a tuple struct");
        }
        Fields::Unnamed(fields) => match fields.unnamed.iter().len() {
            1 => fields.unnamed.iter().next().unwrap().clone(),
            _ => panic!("GraphKey: struct must have exactly one field"),
        },
    };

    if string_ty != field.ty {
        panic!("GraphKey: struct must have exactly one field of type `String`");
    };

    match field.vis {
        syn::Visibility::Public(_) => {}
        _ => {
            panic!("GraphKey: struct field must be public");
        }
    }

    // Implement the serialize function with #[automatically_derived] annotation
    let expanded = quote! {
        #[automatically_derived]
        impl std::fmt::Debug for #name {
            fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
                write!(f, "{}", self.serialize())
            }
        }

        #[automatically_derived]
        impl std::fmt::Display for #name {
            fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
                write!(f, "{}", self.serialize())
            }
        }

        #[automatically_derived]
        impl GraphKeyExt for #name {
            fn serialize(&self) -> String {
                format!("{}({})", #graph_key, self.serialize_inner())
            }

            fn serialize_inner(&self) -> String {
                self.0.clone()
            }

            fn deserialize(s: &str) -> ::std::result::Result<Self, String> {
                let mut tokenator = crate::parser::lex(s)?;
                let thing = tokenator.take_ident()?;
                if thing != #graph_key {
                    return Err(format!("Expected outer graphkey to be `{}`", #graph_key));
                }

                tokenator.take_open_paren()?;
                let _0 = Self::deserialize_inner(&mut tokenator)?;
                tokenator.take_close_paren()?;
                tokenator.take_end()?;
                Ok(_0)
            }

            fn deserialize_inner(
                tokenator: &mut crate::parser::TokenIterator,
            ) -> ::std::result::Result<Self, String> {
                let inner_value = tokenator.take_ident()?;
                Ok(Self(inner_value))
            }
        }
    };

    expanded.into()
}

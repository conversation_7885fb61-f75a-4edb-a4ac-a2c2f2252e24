use ace_graph::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, User};
use error_stack::ResultExt;
use garbage::CNSL;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetLatestUbuntu2204Ace2Ami,
    GetPublic<PERSON>eypairs,
    GetCurrentAccount,
    GetKeyPairsForUser(ace_graph::User),
}

/// Generates the keys installed by the user_data script.
/// Restricts keys to ONLY the sysadmins specified in the account.
pub async fn generate_script(
    ace_db_app: &ace_db::App,
    current_account_key: String,
) -> error_stack::Result<String, ErrorStack> {
    let public_keys: String = {
        let mut keypairs = vec![];

        let this_account = ace_graph::aws_account::get(
            &ace_graph::AwsAccount::Db(current_account_key),
            ace_db_app,
        )
        .await
        .change_context(ErrorStack::GetCurrentAccount)?;

        let allowed_users = this_account.sysadmins;

        // Build up keypairs ONLY from the allowed sysadmin users
        for user in allowed_users {
            // Second tuple field value is not important for this particular filter
            let user_gk = User::Db(user);
            let key_pair_user_filter =
                KeyPairFilter::One(KeyPair::User(user_gk.clone(), "".to_string()));

            keypairs.extend(
                ace_graph::user::select_user_keypair(&key_pair_user_filter, ace_db_app)
                    .await
                    .change_context(ErrorStack::GetKeyPairsForUser(user_gk))?,
            );
        }

        let mut rval = String::new();

        // Don't include the Ace keypair in the list of public keys - it is installed another way.
        // It still belongs in ace_graph, but cannot be used the same way as the other keys, as the file may not exist yet.
        for key in keypairs {
            if !matches!(&key.graphkey, &ace_graph::KeyPair::Ace) {
                rval.push_str(&format!("\n# {}\n{}\n", key.graphkey, key.public_key));
            }

            continue;
        }

        rval
    };

    let install_ssh_keys_script = CNSL!(
        r#"
        read -r -d '' SSH_KEYS << 'EOFEOFEOF'
        "#,
        (public_keys),
        r#"
        EOFEOFEOF
        echo -e "\n$SSH_KEYS" >> /home/<USER>/.ssh/authorized_keys
    "#
    );

    Ok(install_ssh_keys_script)
}

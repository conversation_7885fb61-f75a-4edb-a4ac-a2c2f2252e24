use error_stack::ResultExt;
use hcl::Block;
use std::path::{Path, PathBuf};
use std::{fs::File, io::Write};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    ConvertToHcl,
    CreateDirectory,
    CreateFile,
    GetAccountKeyAndRegion,
    GetPackers,
    ReadPackerPluginConfigFile,
    RecreatePackerPluginConfigFile,
    RemoveDirectory,
    WriteFile,
}

#[derive(Debug)]
struct PackerPair {
    name: String,
    hcl_file_contents: String,
    script_file_contents: String,
}

pub async fn build(app: &crate::Application) -> error_stack::Result<(), ErrorStack> {
    let mp = |p: &str| app.packer_path.join(p);
    let manifest_path = &app.ace_db_app.packer_manifest_path;
    let asset_path = &app.ace_db_app.asset_path;

    let packers = ace_graph::pkr::select(ace_graph::PackerFilter::All, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GetPackers)?;

    let (account_key, region) =
        ace_graph::account_key_and_region().change_context(ErrorStack::GetAccountKeyAndRegion)?;

    let mut filepairs: Vec<PackerPair> = Vec::new();

    // FIRST generate all the hcl and sh file contents:
    for packer in packers {
        let name = packer.name.clone();
        let (hcl_file_contents, script_file_contents) =
            gen_hcl(packer, &account_key, &region, manifest_path, asset_path)?;

        filepairs.push(PackerPair {
            name,
            hcl_file_contents,
            script_file_contents,
        });
    }

    // Copy the packer plugin config so it can be replaced:
    let packer_plugin_config = tokio::fs::read(&app.packer_config_path)
        .await
        .change_context(ErrorStack::ReadPackerPluginConfigFile)?;

    // THEN delete contents only of packer dir and recreate it:
    std::fs::remove_dir_all(&app.packer_path).change_context(ErrorStack::RemoveDirectory)?;
    std::fs::create_dir_all(&app.packer_path).change_context(ErrorStack::CreateDirectory)?;

    for filepair in filepairs {
        write_pair(&mp(&filepair.name), &filepair)?;
    }

    // Replace packer plugin config:
    tokio::fs::write(&app.packer_config_path, packer_plugin_config)
        .await
        .change_context(ErrorStack::RecreatePackerPluginConfigFile)?;

    Ok(())
}

pub type HclContents = String;
pub type ScriptContents = String;

fn gen_hcl(
    packer: ace_graph::pkr::Packer,
    account_key: &str,
    region: &str,
    manifest_path: &std::path::Path,
    asset_path: &std::path::Path,
) -> error_stack::Result<(HclContents, ScriptContents), ErrorStack> {
    let mut blocks = vec![];

    // Create packer block
    blocks.push(
        Block::builder("packer")
            .add_attribute(("required_version", ">= 1.7"))
            .build(),
    );

    // Sort packer filters by key before creating filters sub block
    let sorted_packer_filters = packer.sort_filters_by_key();

    // Create filters
    let filters_sub_block = hcl::Attribute::new(
        "filters",
        hcl::Value::Object(
            sorted_packer_filters
                .into_iter()
                .map(|(k, v)| (k, hcl::Value::String(v.to_string())))
                .collect(),
        ),
    );

    // Create tags
    let tags = hcl::attribute!(
    ("tags") = {
        "Source" = "Packer",
        "AccountKey" = account_key,
    });

    // Create source block
    blocks.push(
        Block::builder("source")
            .add_label("amazon-ebs")
            .add_label(&packer.name)
            .add_attribute(("region", region))
            // Source AMI filter block
            .add_block(
                Block::builder("source_ami_filter")
                    .add_attribute(filters_sub_block)
                    .add_attribute(("owners", packer.source_ami_filter.owners))
                    .add_attribute(("most_recent", packer.source_ami_filter.most_recent))
                    .build(),
            )
            .add_attribute(("instance_type", packer.instance_type))
            .add_attribute(("ssh_username", packer.ssh_username))
            .add_attribute(("ami_name", format!("{}{}", packer.name, "-{{timestamp}}")))
            .add_attribute(tags)
            .build(),
    );

    // Standard parts of every packer's build block:
    let provisioner_block = Block::builder("provisioner")
        .add_label("shell")
        .add_attribute(("script", format!("./{}.sh", packer.name)))
        .add_attribute(("execute_command", "sudo -E /bin/bash '{{ .Path }}'"))
        .build();

    let post_processor_block = Block::builder("post-processor")
        .add_label("manifest")
        .add_attribute(("output", manifest_path.to_string_lossy()))
        .add_attribute(("strip_path", true))
        .build();

    // Create provisioner blocks (if any)
    let mut provisioner_blocks = vec![];
    for provisioner in packer.provisioners {
        match provisioner {
            ace_graph::pkr::Provisioner::File {
                rel_source,
                abs_destination,
            } => provisioner_blocks.push(
                Block::builder("provisioner")
                    .add_label("file")
                    .add_attribute(("source", asset_path.join(rel_source).to_string_lossy()))
                    .add_attribute(("destination", abs_destination.to_string_lossy()))
                    .build(),
            ),
        }
    }

    // Create build block
    blocks.push(
        Block::builder("build")
            .add_attribute((
                "sources",
                vec![format!("source.amazon-ebs.{}", packer.name)],
            ))
            .add_blocks(provisioner_blocks)
            .add_block(provisioner_block)
            .add_block(post_processor_block)
            .build(),
    );

    let body = hcl::Body::from_iter(blocks);
    let body_string = hcl::to_string(&body)
        .change_context(ErrorStack::ConvertToHcl)?
        .replace("$$$", "$");
    Ok((body_string, packer.bash_text))
}

fn write_pair(basepath: &Path, packer: &PackerPair) -> error_stack::Result<(), ErrorStack> {
    let packer_path = basepath.with_extension("pkr.hcl");
    let bash_path = basepath.with_extension("sh");

    write_file(packer_path, &packer.hcl_file_contents)?;
    write_file(bash_path, &packer.script_file_contents)?;

    Ok(())
}

fn write_file(path: PathBuf, contents: &str) -> error_stack::Result<(), ErrorStack> {
    let mut file = File::create(path).change_context(ErrorStack::CreateFile)?;

    file.write_all(contents.as_bytes())
        .change_context(ErrorStack::WriteFile)?;
    Ok(())
}

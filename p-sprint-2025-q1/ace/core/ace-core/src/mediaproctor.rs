use error_stack::ResultExt;
use std::collections::HashMap;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CouldNotIdentifyInstance(String),
    CouldNotIdentifyInstanceNameTag(String),
    GetAWSBuilderResponse,
    GetConfig,
    GetInstanceId,
    GetInstanceInput,
    GetMediaproctorAmiId,
    GetSshKeys,
    InstanceIsNotAMediaProctorInstance(String),
    MediaProctorDoesNotHaveSecurityGroupID(String),
    NoPublicSubnetsFound,
    OpenEc2Instance,
    SendInstanceInput,
}

#[derive(Debug, serde::Serialize)]
pub struct RunResponse {
    pub instance_id: String,
    pub errors: Vec<String>,
}

pub async fn run_mp_process(
    mediaproctor: &ace_graph::mediaproctor::MediaProctor,
    job: &serde_json::Value,
    tags: &HashMap<String, String>,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<RunResponse, ErrorStack> {
    let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, ace_db_app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    let (account, vpc_public_subnet_map) = (config.account, config.vpc_public_subnet_map);

    let ssh_keys = account
        .get_ssh_keys_text(ace_db_app)
        .await
        .change_context(ErrorStack::GetSshKeys)?;

    // Check that mediaproctor has security group id
    let mediaproctor_security_group_id = match mediaproctor.security_group_id {
        Some(ref mediaproctor_security_group_id) => mediaproctor_security_group_id.to_string(),
        None => error_stack::bail!(ErrorStack::MediaProctorDoesNotHaveSecurityGroupID(
            mediaproctor.name.clone()
        )),
    };

    // Make sure the mediaproctor has an image id
    let mp_image_id = mediaproctor
        .get_ami_id(ace_db_app)
        .await
        .change_context(ErrorStack::GetMediaproctorAmiId)?;

    // get a random value from the map
    let Some(subnet_id) = vpc_public_subnet_map.values().next() else {
        error_stack::bail!(ErrorStack::NoPublicSubnetsFound);
    };

    let job_text = job.to_string();

    #[rustfmt::skip]
    let bash = garbage::CNSL!(r##"
        #!/bin/bash
        
        cat >> /home/<USER>/.ssh/authorized_keys << 'EOF'
        "##, ssh_keys, r##"
        EOF
        
        cat > /home/<USER>/job.json << 'EOF'
        "##, job_text, r##"
        EOF
        
        chown app:app /home/<USER>/job.json

        echo "#!/bin/bash
        # Remove this file if you don't want server to auto terminate
        shutdown -h now
        " > /home/<USER>/shutdown-server.sh

        chmod +x /home/<USER>/shutdown-server.sh
        chown app:app /home/<USER>/shutdown-server.sh

        touch /home/<USER>/job.log
        chown app:app /home/<USER>/job.log

        echo "*** mp-process /home/<USER>/job.json ***"
        mp-process /home/<USER>/job.json >> /home/<USER>/job.log 2>&1
        echo "*** mp-process exited with code: $? ***"
        

        sleep "##, format!("{}", mediaproctor.mp_process.timeout), r##"

        /home/<USER>/shutdown-server.sh

    "##);

    println!("{bash}");

    let ec2 = ace_aws::ec2::instance::open()
        .await
        .change_context(ErrorStack::OpenEc2Instance)?;

    let name = format!("mp-process@{}", mediaproctor.name);

    let mut instance_input = ace_aws::ec2::instance::RunInstance::simple1(
        &name,
        &mp_image_id,
        &mediaproctor.mp_process.instance_type,
        subnet_id,
        vec![mediaproctor_security_group_id],
    )
    .change_context(ErrorStack::GetInstanceInput)?;

    for tag in tags {
        instance_input.add_tag(tag.0, tag.1);
    }
    instance_input.terminate_on_shutdown();
    instance_input.user_data = Some(bash);

    let instance_output = instance_input
        .send(ec2)
        .await
        .change_context(ErrorStack::SendInstanceInput)?;

    let Some(instance_id) = instance_output.instance_ids.first() else {
        error_stack::bail!(ErrorStack::GetInstanceId);
    };

    Ok(RunResponse {
        instance_id: instance_id.clone(),
        errors: instance_output.errors,
    })
}

pub async fn run_mp_stream(
    mediaproctor: &ace_graph::mediaproctor::MediaProctor,
    job_url: &str,
    tags: &HashMap<String, String>,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<RunResponse, ErrorStack> {
    let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, ace_db_app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    let (account, vpc_public_subnet_map) = (config.account, config.vpc_public_subnet_map);

    let ssh_keys = account
        .get_ssh_keys_text(ace_db_app)
        .await
        .change_context(ErrorStack::GetSshKeys)?;

    // Check that mediaproctor has security group id
    let mediaproctor_security_group_id = match mediaproctor.security_group_id {
        Some(ref mediaproctor_security_group_id) => mediaproctor_security_group_id.to_string(),
        None => error_stack::bail!(ErrorStack::MediaProctorDoesNotHaveSecurityGroupID(
            mediaproctor.name.clone()
        )),
    };

    // Make sure mediaproctor has an image id
    let mp_image_id = mediaproctor
        .get_ami_id(ace_db_app)
        .await
        .change_context(ErrorStack::GetMediaproctorAmiId)?;

    // get a random value from the map
    let Some(subnet_id) = vpc_public_subnet_map.values().next() else {
        error_stack::bail!(ErrorStack::NoPublicSubnetsFound);
    };

    #[rustfmt::skip]
    let bash = garbage::CNSL!(r##"
        #!/bin/bash
        
        cat >> /home/<USER>/.ssh/authorized_keys << 'EOF'
        "##, ssh_keys, r##"
        EOF
        
        wget -O /home/<USER>/job.json "##, String::from_utf8_lossy(&shell_quote::Sh::quote_vec(job_url)), r##"
       
        chown app:app /home/<USER>/job.json

        echo "#!/bin/bash
        # Remove this file if you don't want server to auto terminate
        shutdown -h now
        " > /home/<USER>/shutdown-server.sh

        chmod +x /home/<USER>/shutdown-server.sh
        chown app:app /home/<USER>/shutdown-server.sh

        touch /home/<USER>/job.log
        chown app:app /home/<USER>/job.log

        echo "*** mp-stream /home/<USER>/job.json ***"
        mp-stream /home/<USER>/job.json >> /home/<USER>/job.log 2>&1
        echo "*** mp-stream exited with code: $? ***"
        
        sleep "##, format!("{}", mediaproctor.mp_stream.timeout), r##"

        /home/<USER>/shutdown-server.sh

    "##);

    println!("{bash}");

    let ec2 = ace_aws::ec2::instance::open()
        .await
        .change_context(ErrorStack::OpenEc2Instance)?;

    let name = format!("mp-stream@{}", mediaproctor.name);

    let mut instance_input = ace_aws::ec2::instance::RunInstance::simple1(
        &name,
        &mp_image_id,
        &mediaproctor.mp_stream.instance_type,
        subnet_id,
        vec![mediaproctor_security_group_id],
    )
    .change_context(ErrorStack::GetInstanceInput)?;

    for tag in tags {
        instance_input.add_tag(tag.0, tag.1);
    }
    instance_input.volume_size = 40;
    instance_input.terminate_on_shutdown();
    instance_input.user_data = Some(bash);
    instance_input.add_tag("MediaProctorJob", job_url);

    let instance_output = instance_input
        .send(ec2)
        .await
        .change_context(ErrorStack::SendInstanceInput)?;

    let Some(instance_id) = instance_output.instance_ids.first() else {
        error_stack::bail!(ErrorStack::GetInstanceId);
    };

    Ok(RunResponse {
        instance_id: instance_id.clone(),
        errors: instance_output.errors,
    })
}

#[derive(Debug, serde::Serialize)]
pub struct InstanceStatusResult {
    pub instance_status_map: std::collections::HashMap<String, String>,
}

pub async fn check_instance_status(
    _mediaproctor: &ace_graph::mediaproctor::MediaProctor,
    instance_ids: &Vec<String>,
) -> error_stack::Result<InstanceStatusResult, ErrorStack> {
    let ec2 = ace_aws::ec2::instance::open()
        .await
        .change_context(ErrorStack::OpenEc2Instance)?;

    let mut instance_status_map = std::collections::HashMap::new();
    let builder = ec2.client.describe_instances();

    for instance_id in instance_ids {
        // default state is missing
        instance_status_map.insert(instance_id.clone(), "missing".to_string());

        // Commented this out because if you have a missing or not-yet-propagated instance ID
        // the entire query will *COMPLETELY* fail instead of just not returning data for them.
        // builder = builder.instance_ids(instance_id);
    }

    let response = builder
        .send()
        .await
        .change_context(ErrorStack::GetAWSBuilderResponse)?;

    println!(
        "check_instance_status DescribeInstances for {instance_ids:#?} has result:\n{response:#?}"
    );

    for reservation in response.reservations.unwrap_or_default() {
        for instance in reservation.instances.unwrap_or_default() {
            let Some(instance_id) = instance.instance_id else {
                eprintln!("For some reason, on record {instance:?}, instance_id is None",);
                continue;
            };

            // We are not interested in any hypothetical instances that are not in the instance_ids list
            if !instance_status_map.contains_key(&instance_id) {
                continue;
            }

            let state = match instance.state {
                Some(state) => {
                    if let Some(state) = state.name {
                        state.as_str().to_owned()
                    } else {
                        eprintln!("For some reason, on record {instance_id:?}, state.name is None",);
                        "statenone".to_string()
                    }
                }
                None => "statenone".to_string(),
            };

            instance_status_map.insert(instance_id, state.to_string());
        }
    }

    Ok(InstanceStatusResult {
        instance_status_map,
    })
}

#[derive(Debug, serde::Serialize)]
pub struct InstanceTerminateResult {
    pub message: String,
}

pub async fn terminate_instance(
    mediaproctor: &ace_graph::mediaproctor::MediaProctor,
    instance_id: &str,
) -> error_stack::Result<InstanceTerminateResult, ErrorStack> {
    let ec2 = ace_aws::ec2::instance::open()
        .await
        .change_context(ErrorStack::OpenEc2Instance)?;

    let mut instance_tag_map = std::collections::HashMap::new();
    let builder = ec2.client.describe_instances();
    let builder = builder.instance_ids(instance_id);
    let response = builder
        .send()
        .await
        .change_context(ErrorStack::GetAWSBuilderResponse)?;

    for reservation in response.reservations.unwrap_or_default() {
        for instance in reservation.instances.unwrap_or_default() {
            let Some(incoming_instance_id) = &instance.instance_id else {
                eprintln!("For some reason, on record {instance:?}, instance_id is None",);
                continue;
            };

            let mut tags = HashMap::new();

            for tag in instance.tags.unwrap_or_default() {
                let Some(key) = tag.key else {
                    eprintln!(
                        "For some reason, on record {incoming_instance_id:?}, tag.key is None",
                    );
                    continue;
                };

                let Some(value) = tag.value else {
                    eprintln!(
                        "For some reason, on record {incoming_instance_id:?}, tag.value is None",
                    );
                    continue;
                };

                tags.insert(key, value);
            }

            instance_tag_map.insert(incoming_instance_id.to_owned(), tags);
        }
    }

    let tag1 = format!("mp-process@{}", mediaproctor.name);
    let tag2 = format!("mp-stream@{}", mediaproctor.name);

    // find if the instance id exists in the tag map; if it does not, just return a successful response
    let Some(tags) = instance_tag_map.get(instance_id) else {
        return Ok(InstanceTerminateResult {
            message: format!("Instance {instance_id} was already terminated"),
        });
    };

    let Some(name_tag) = tags.get("Name") else {
        error_stack::bail!(ErrorStack::CouldNotIdentifyInstanceNameTag(
            instance_id.to_string()
        ));
    };

    if *name_tag != tag1 && *name_tag != tag2 {
        error_stack::bail!(ErrorStack::InstanceIsNotAMediaProctorInstance(
            instance_id.to_string()
        ));
    }

    // terminate the instance
    let builder = ec2.client.terminate_instances();
    let builder = builder.instance_ids(instance_id);
    let response = builder
        .send()
        .await
        .change_context(ErrorStack::GetAWSBuilderResponse)?;

    Ok(InstanceTerminateResult {
        message: format!("Terminated instance {instance_id} with {response:?}"),
    })
}

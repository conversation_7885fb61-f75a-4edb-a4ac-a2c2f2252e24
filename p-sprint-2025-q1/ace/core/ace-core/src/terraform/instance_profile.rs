use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use garbage::{CNSL, JE};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetInstanceProfiles,
    GetIamRole,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let mut ins_profiles =
        ace_graph::ins_profile::select(ace_graph::InstanceProfileFilter::All, ace_db_app)
            .await
            .change_context(ErrorStack::GetInstanceProfiles)?;

    // This prevents undeterministic ordering to the output
    ins_profiles.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    for ins_profile in ins_profiles {
        // !! **DO NOT USE THE IAMROLE TAG NAME HERE** !!
        let (_role_resource, role_resource_name, role_comment, _tag_name) =
            match &ins_profile.graphkey {
                ace_graph::InstanceProfile::IamRole(iam_role_gk) => {
                    let iam_role = ace_graph::iam_role::get(iam_role_gk.clone(), ace_db_app)
                        .await
                        .change_context(ErrorStack::GetIamRole)?;

                    iam_role.terraform_resource(config)
                }
            };

        let (ins_profile_resource, ins_profile_name, tags) = ins_profile.terraform_resource(config);

        #[rustfmt::skip]
        output.write(&None, &CNSL!(r#"

            //"#, ins_profile.graphkey.serialize(), r#"
            resource "aws_iam_instance_profile" "#, JE!(ins_profile_resource), r#" {
                name = "#, JE!(ins_profile_name), r#"

                // "#, role_comment, r#"
                role = "#, JE!(role_resource_name), r#"
                "#, if tags {
                    CNSL!(r#"
                    tags = {
                        Name = "#, JE!(ins_profile_resource), r#"
                        Source = "Terraform"
                        AccountKey = "#, JE!(&config.account_key), r#"
                    }
                    "#)
                } else { "".to_string() }, r#"
            }
        "#));
    }

    Ok(())
}

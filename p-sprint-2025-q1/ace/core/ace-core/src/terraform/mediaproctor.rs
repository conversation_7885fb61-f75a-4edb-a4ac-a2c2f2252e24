use super::Output;
use garbage::{<PERSON><PERSON><PERSON>, J<PERSON>};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {}

pub fn generate(config: &ace_graph::config::Config, output: &mut Output) {
    #[rustfmt::skip]
    output.write(&None, &CNSL!(r#"
        # ---
        # mediaproctor

        resource "aws_security_group" "mediaproctor_sg" {
            name        = "#, JE!(format!("{}-mediaproctor-sg", &config.account_key)), r#"
            description = "for mediaproctor process and stream servers"
            vpc_id      = aws_vpc.vpc_main.id

            // Allow inbound SSH traffic from vpc
            ingress {
                from_port   = 22
                to_port     = 22
                protocol    = "tcp"
                cidr_blocks = [aws_vpc.vpc_main.cidr_block]
            }

            // Allow inbound SSH traffic from my ip
            ingress {
                from_port   = 22
                to_port     = 22
                protocol    = "tcp"
                cidr_blocks = ["#, JE!(&config.my_ipnetwork), r#"]
            }
            
            // Allow all outbound traffic
            egress {
                from_port   = 0
                to_port     = 0
                protocol    = "-1"
                cidr_blocks = ["0.0.0.0/0"]
            }

            tags = {
                Name = "#, JE!(format!("{}-mediaproctor-sg", &config.account_key)), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&config.account_key), r#"
            }
        }

    "#));

    output.add("security_group_id", "aws_security_group.mediaproctor_sg.id");
}

use super::Output;
use error_stack::ResultExt;
use garbage::{CNSL, JE};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GenerateOne,
    SelectDevelopers,
}

pub async fn generate(
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    for developer in ace_graph::developer::select(&ace_graph::DeveloperFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::SelectDevelopers)?
    {
        generate_one(&developer, output)
            .await
            .change_context(ErrorStack::GenerateOne)?;
    }

    //output.add_output_pair("key_names", key_names.join(sep));

    Ok(())
}

async fn generate_one(
    developer: &ace_graph::developer::Developer,
    output: &mut Output,
) -> error_stack::Result<(), ErrorStack> {
    // Create local references for convenience below

    let instance_resource = format!("developer-{}", developer.name);
    let public_domain_resource = format!("developer-{}-public", developer.name);
    let public_domain_wildcard_resource = format!("developer-{}-public-wildcard", developer.name);
    let private_domain_resource = format!("developer-{}-private", developer.name);
    let private_domain_wildcard_resource = format!("developer-{}-private-wildcard", developer.name);

    #[rustfmt::skip]
    output.write(&Some(developer.name.to_string()), &CNSL!(r#"
        resource "aws_route53_record" "#, JE!(private_domain_resource), r#" {
            zone_id = aws_route53_zone.private-domain.zone_id
            name    = "#, JE!(&developer.dns_prefix), r#"
            type    = "A"
            ttl     = "300"
            records = ["#, format!("aws_instance.{}.private_ip", &instance_resource), r#"]
        }

        resource "aws_route53_record" "#, JE!(private_domain_wildcard_resource), r#" {
            zone_id = aws_route53_zone.private-domain.zone_id
            name    = "#, JE!(&developer.dns_wildcard_prefix), r#"
            type    = "A"
            ttl     = "300"
            records = ["#, format!("aws_instance.{}.private_ip", &instance_resource), r#"]
        }
        
        resource "aws_route53_record" "#, JE!(public_domain_resource), r#" {
            zone_id = aws_route53_zone.public-domain.zone_id
            name    = "#, JE!(&developer.dns_prefix), r#"
            type    = "A"
            ttl     = "300"
            records = ["#, format!("aws_instance.{}.public_ip", &instance_resource), r#"]
        }

        resource "aws_route53_record" "#, JE!(public_domain_wildcard_resource), r#" {
            zone_id = aws_route53_zone.public-domain.zone_id
            name    = "#, JE!(&developer.dns_wildcard_prefix), r#"
            type    = "A"
            ttl     = "300"
            records = ["#, format!("aws_instance.{}.public_ip", &instance_resource), r#"]
        }
    "#));

    Ok(())
}

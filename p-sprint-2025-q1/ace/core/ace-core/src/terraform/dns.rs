use super::Output;
use garbage::{C<PERSON><PERSON>, J<PERSON>};

pub fn generate(config: &ace_graph::config::Config, output: &mut Output) {
    // Create local references for convenience below
    let account_key = &config.account_key;
    let region = &config.region;
    let public_subdomain_name = &config.public_subdomain_name;
    let private_subdomain_name = &config.private_subdomain_name;

    #[rustfmt::skip]
    output.write(&None, &CNSL!(r#"
        // Create a route53 zone for the public domain and private domain

        resource "aws_route53_zone" "public-domain" {
            name = "#, JE!(&public_subdomain_name), r#"
            tags = {
                Name = "#, JE!(format!("{}-{}-public-domain", &account_key, &region)), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
                Region = "#, JE!(&region), r#"
            }
        }

        resource "aws_route53_zone" "private-domain" {
            name = "#, JE!(&private_subdomain_name), r#"
            tags = {
                Name = "#, JE!(format!("{}-{}-private-domain", &account_key, &region)), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
                Region = "#, JE!(&region), r#"
            }
        }


    "#));

    output.add(
        "public_domain_zone_id",
        "aws_route53_zone.public-domain.zone_id",
    );
    output.add(
        "private_domain_zone_id",
        "aws_route53_zone.private-domain.zone_id",
    );
    output.add(
        "public_domain_ns_list",
        "aws_route53_zone.public-domain.name_servers",
    );
    output.add(
        "private_domain_ns_list",
        "aws_route53_zone.private-domain.name_servers",
    );
}

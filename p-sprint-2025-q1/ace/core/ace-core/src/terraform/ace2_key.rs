use super::Output;
use garbage::{<PERSON><PERSON><PERSON>, J<PERSON>};

pub fn generate(config: &ace_graph::config::Config, output: &mut Output) {
    let account_key = &config.account_key;
    let region = &config.region;
    let key_name = format!("ace@{account_key}@{region}");

    let public_key_file_path = &config.ace2_public_key_file_path;

    #[rustfmt::skip]
    output.write(&None, &CNSL!(r#"
        resource "aws_key_pair" "ace2_key" {
            key_name   = "#, JE!(&key_name), r#"
            public_key = file("#, JE!(&public_key_file_path), r#")

            tags = {
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
            }
        }
    "#));

    output.add("public_key", "aws_key_pair.ace2_key.public_key");
}

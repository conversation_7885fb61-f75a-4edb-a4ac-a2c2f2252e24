use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use garbage::{CN, CNSL, JE, JN};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    SelectSecurityGroups,
    GenerateOne,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let mut aws_vpc_sg =
        ace_graph::aws_vpc_sg::select(&ace_graph::AwsVpcSecurityGroupFilter::All, ace_db_app)
            .await
            .change_context(ErrorStack::SelectSecurityGroups)?;

    // Ensure deterministic output
    aws_vpc_sg.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    for sg in aws_vpc_sg {
        generate_one(config, &sg, output)
            .await
            .change_context(ErrorStack::GenerateOne)?;
    }

    Ok(())
}

async fn generate_one(
    config: &ace_graph::config::Config,
    aws_vpc_sg: &ace_graph::aws_vpc_sg::AwsVpcSecurityGroup,
    output: &mut Output,
) -> error_stack::Result<(), ErrorStack> {
    let (resource_id, resource_name, tag_name) = aws_vpc_sg.terraform_resource(config);

    #[rustfmt::skip]
    output.write(&None, &CNSL!(r#"

        resource "aws_security_group" "#, JE!(resource_id), r#" {
            name = "#, JE!(resource_name), r"
            description = ", JE!(aws_vpc_sg.description), r"
            vpc_id = aws_vpc.vpc_main.id

        ", JN!(&aws_vpc_sg.ingress, |ingress|
                match &ingress.cidr_blocks {
                    ace_graph::ins::CidrBlocks::Known(cidr_blocks) => {
                        CN!(r#"
                            ingress {
                                from_port   = "#, JE!(ingress.ports.to_aws_from_port()), r#"
                                to_port     = "#, JE!(ingress.ports.to_aws_to_port()), r#"
                                protocol    = "#, JE!(ingress.protocol), r#"
                                cidr_blocks = "#, JE!(cidr_blocks), r#"
                            }
                        "#)
                    },
                    ace_graph::ins::CidrBlocks::MainVpcCidrBlock => {
                        CN!(r#"
                            ingress {
                                from_port   = "#, JE!(ingress.ports.to_aws_from_port()), r#"
                                to_port     = "#, JE!(ingress.ports.to_aws_to_port()), r#"
                                protocol    = "#, JE!(ingress.protocol), r#"
                                cidr_blocks = [aws_vpc.vpc_main.cidr_block]
                            }
                        "#)
                    }
                    ace_graph::ins::CidrBlocks::All => {
                        CN!(r#"
                            ingress {
                                from_port   = "#, JE!(ingress.ports.to_aws_from_port()), r#"
                                to_port     = "#, JE!(ingress.ports.to_aws_to_port()), r#"
                                protocol    = "#, JE!(ingress.protocol), r#"
                                cidr_blocks = ["0.0.0.0/0"]
                            }
                        "#)
                    }
                }
            ), r#"

            // Allow all outbound traffic
            egress {
                from_port   = 0
                to_port     = 0
                protocol    = "-1"
                cidr_blocks = ["0.0.0.0/0"]
            }

            tags = {
                Name = "#, JE!(tag_name), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&config.account_key), r#"
            }
        }
    "#));

    if let ace_graph::AwsVpcSg::MediaProctor = aws_vpc_sg.graphkey {
        output.add("security_group_id", "aws_security_group.mediaproctor_sg.id");
    }

    Ok(())
}

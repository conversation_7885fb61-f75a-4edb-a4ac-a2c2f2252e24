use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use garbage::{CNSL, JE};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetAwsElasticacheServerless,
    GetSecurityGroups,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    for aws_ec_sls in ace_graph::aws_ec_sls::select(ace_graph::AwsEcSlsFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::GetAwsElasticacheServerless)?
    {
        let resource_id = aws_ec_sls.graphkey.serialize_dashed();

        let security_groups = ace_graph::aws_vpc_sg::select(
            &ace_graph::AwsVpcSecurityGroupFilter::One(ace_graph::AwsVpcSg::AwsEcSls(
                aws_ec_sls.graphkey.clone(),
            )),
            ace_db_app,
        )
        .await
        .change_context(ErrorStack::GetSecurityGroups)?
        .into_iter()
        .map(|sg| {
            format!(
                "aws_security_group.{}.id",
                sg.to_terraform_resource_id(config)
            )
        })
        .collect::<Vec<_>>();

        let subnet_ids = aws_ec_sls
            .subnets
            .iter()
            .map(|subnet| format!("aws_subnet.{}.id", subnet.to_old_terraform_resource_name()))
            .collect::<Vec<_>>();

        #[rustfmt::skip]
        output.write(&None, &CNSL!(r#"
            resource "aws_elasticache_serverless_cache" "#, JE!(resource_id), r#" {
                engine = "valkey"
                name =  "#, JE!(aws_ec_sls.name), r#"
                major_engine_version = "#, JE!(aws_ec_sls.engine_version), r#"

                subnet_ids = ["#, (subnet_ids.join(",")), r#"]
                daily_snapshot_time = "00:00"

                security_group_ids = ["#, (security_groups.join(",")), r#"]

                tags = {
                    Name = "#, JE!(aws_ec_sls.name), r#"
                    Source = "Terraform"
                    AccountKey = "#, JE!(&config.account_key), r#"
                }
            }
        "#));

        // After creating the resource, add output blocks for the credentials
        #[rustfmt::skip]
        output.write(&None, &CNSL!(r#"
            output "#, (format!("{}_endpoint", resource_id)), r#" {
                value = aws_elasticache_serverless_cache."#, (resource_id), r#".endpoint
            }

            output "#, (format!("{}_reader_endpoint", resource_id)), r#" {
                value = aws_elasticache_serverless_cache."#, (resource_id), r#".reader_endpoint
            }
        "#));
    }

    Ok(())
}

use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use garbage::{CNSL, JE};
use serde_json::json;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GenerateOne,
    GeneratePolicyStatementList,
    GetDeveloper,
    GetDeveloperApp,
    PrettifyPolicyJSON,
    SelectIamRoles,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let mut sorted_iam_roles =
        ace_graph::iam_role::select(ace_graph::IamRoleFilter::All, ace_db_app)
            .await
            .change_context(ErrorStack::SelectIamRoles)?;

    // This ensures deterministic output
    sorted_iam_roles.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    for iam_role in sorted_iam_roles {
        generate_one(&iam_role, config, output, ace_db_app)
            .await
            .change_context(ErrorStack::GenerateOne)?
    }

    Ok(())
}

async fn generate_one(
    iam_role: &ace_graph::iam_role::IamRole,
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let (iam_role_resource, iam_role_resource_name, comment, tag_name) =
        iam_role.terraform_resource(config);

    // !! **DO NOT create config for EXISTING IamRoles** !!
    let assume_role_policy = match &iam_role.role_type {
        ace_graph::iam_role::IamRoleType::New {
            assume_role_policy: json,
        } => json,
        ace_graph::iam_role::IamRoleType::Existing { .. } => return Ok(()),
    };

    #[rustfmt::skip]
    output.write(&None, &CNSL!(r#"

        //"#, comment, r#"
        resource "aws_iam_role" "#, JE!(&iam_role_resource), r#" {
            name = "#, JE!(&iam_role_resource_name), r"
            ", if let ace_graph::IamRole::Developer(_) = &iam_role.graphkey {
                let pretty_assume_role_policy = serde_json::to_string_pretty(assume_role_policy).change_context(ErrorStack::PrettifyPolicyJSON)?;

                CNSL!(r"
                    assume_role_policy = <<EOF
                    ", 
                    // This is safe to include without JE! because it's generated internally, not direct user input.
                    pretty_assume_role_policy, r"
                    EOF
                ")
            } 
            else { 
                CNSL!(r"
                    assume_role_policy = jsonencode(", JE!(assume_role_policy), r")
                ") 
            },
            if !matches!(iam_role.graphkey, ace_graph::IamRole::Developer(_)) && !matches!(iam_role.graphkey, ace_graph::IamRole::Brsrc(_)) {
                CNSL!(r#"
                    tags = {
                        Name = "#, JE!(&tag_name), r#"
                        Source = "Terraform"
                        AccountKey = "#, JE!(&config.account_key), r#"
                    }
                "#)
            } else { "".to_string() }, r#"
        }
    "#));

    if !iam_role.policy_statements.is_empty() {
        let policy_name = format!("{iam_role_resource}-policy");

        #[rustfmt::skip]
        output.write(&None, &CNSL!(r#"

            resource "aws_iam_policy" "#, JE!(policy_name), r#" {
                name = "#, JE!(policy_name), r#"

                policy = jsonencode({
                    "Version": "2012-10-17",
                    "Statement": "#, JE!(iam_role.policy_statements), r#"
                })
            }

            resource "aws_iam_role_policy_attachment" "#, JE!(policy_name), r#" {
                policy_arn = aws_iam_policy."#, policy_name, r#".arn
                role = aws_iam_role."#, iam_role_resource, r#".id
            }
        "#));
    }

    if let ace_graph::IamRole::Developer(dev_gk) = &iam_role.graphkey {
        // !! **IT IS VERY IMPORTANT that the IAM_ROLE_RESOURCE_ID (and NOT the resource name) IS PASSED HERE** !!
        generate_developer_iam_role_policies_exclusive(
            dev_gk,
            ace_db_app,
            config,
            &iam_role_resource,
            output,
        )
        .await?;
    }

    Ok(())
}

/// Generates `aws_iam_role_policy` and `aws_iam_role_policies_exclusive` resource "pairs".
/// Only generates them IF the developer has apps in its app_config_map.
/// This replaces the deprecated `inline_policy` ace_graph::Developers used to create, prior IamRole integration.
async fn generate_developer_iam_role_policies_exclusive(
    dev_gk: &ace_graph::Developer,
    ace_db_app: &ace_db::App,
    config: &ace_graph::config::Config,
    iam_role_resource: &String,
    output: &mut Output,
) -> error_stack::Result<(), ErrorStack> {
    let developer = ace_graph::developer::get(dev_gk, ace_db_app)
        .await
        .change_context(ErrorStack::GetDeveloper)?;

    let policy_statement_list = gen_dev_iam_policy_statements(&developer, ace_db_app)
        .await
        .change_context(ErrorStack::GeneratePolicyStatementList)?;
    let policy_name = format!(
        "{}-{}-developer-{}",
        config.account_key, config.region, developer.name
    );

    if policy_statement_list.is_empty() {
        return Ok(());
    }

    // TODO: Have this as a fully integrated graphtype?
    #[rustfmt::skip]
    output.write(&None, &CNSL!(r#"

        resource "aws_iam_role_policy" "#, JE!(policy_name), r#" {
            name = "#, JE!(policy_name), r#"
            role = aws_iam_role."#, iam_role_resource, r#".id

            policy = jsonencode({
                "Version": "2012-10-17",
                "Statement": "#, JE!(policy_statement_list), r#"
            })
        }
    "#));

    Ok(())
}

async fn gen_dev_iam_policy_statements(
    developer: &ace_graph::developer::Developer,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<serde_json::Value>, ErrorStack> {
    let mut policy_statement_list = vec![];

    // Add read/write for buckets listed in the developer's app list
    // This is defined in the app[bucket.____].developer_access field
    for app_name in developer.app_config_map.keys() {
        let app_gk = ace_graph::App::Db(app_name.clone());
        let developer_app = ace_graph::developer_app::get(
            &ace_graph::DeveloperApp::Db(developer.graphkey.clone(), app_gk.clone()),
            ace_db_app,
        )
        .await
        .change_context(ErrorStack::GetDeveloperApp)?;

        for app_bucket in developer_app.developer_access_buckets {
            // add statement for bucket-level permissions
            policy_statement_list.push(json!({
                "Effect": "Allow",
                "Action": [
                    "s3:ListBucket",
                    "s3:ListBucketMultipartUploads",
                    "s3:GetBucketLocation",
                ],
                "Resource": format!("arn:aws:s3:::{}", app_bucket.bucket_name)
            }));

            // add a statement for object-level permissions
            policy_statement_list.push(json!({
                "Effect": "Allow",
                "Action": [
                    "s3:GetObject",
                    "s3:PutObject",
                    "s3:DeleteObject",
                    "s3:AbortMultipartUpload",
                    "s3:ListMultipartUploadParts"
                ],
                "Resource": format!("arn:aws:s3:::{}/*", app_bucket.bucket_name)
            }));
        }
    }

    Ok(policy_statement_list)
}

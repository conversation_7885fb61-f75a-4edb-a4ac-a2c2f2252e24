use super::Output;
use garbage::{<PERSON><PERSON><PERSON>, J<PERSON>};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    _ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    // Create local references for convenience below
    let account_key = &config.account_key;
    let subnet16 = &config.cidr_block.to_string();

    #[rustfmt::skip]
    output.write(&None, &CNSL!(r#"
        resource "aws_vpc" "vpc_main" {
            cidr_block = "#, JE!(&subnet16), r"
            tags = {
                Name = ", JE!(format!("{}-vpc-main", &account_key)), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r#"
            }
        }

        resource "aws_internet_gateway" "igw_main" {
            vpc_id = aws_vpc.vpc_main.id
            tags = {
                Name = "#, JE!(format!("{}-igw-main", &account_key)), r#"
                Source = "Terraform"
                AccountKey = "#, JE!(&account_key), r"
            }
        }


    "));

    output.add("id", "aws_vpc.vpc_main.id");
    output.add("cidr_block", "aws_vpc.vpc_main.cidr_block");
    output.add("igw_id", "aws_internet_gateway.igw_main.id");
    output.add("sn_a_id", "aws_subnet.sn-a.id");
    output.add("sn_b_id", "aws_subnet.sn-b.id");
    output.add("sn_c_id", "aws_subnet.sn-c.id");

    Ok(())
}

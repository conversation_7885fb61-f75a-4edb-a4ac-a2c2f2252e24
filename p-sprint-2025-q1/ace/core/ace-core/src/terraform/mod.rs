use error_stack::ResultExt;
use garbage::{CNSL, JE, JN, STSL};
use std::collections::HashMap;
use std::{fs::File, io::Write};

mod ace2_key;
mod app;
mod aws_ec_sls;
pub mod aws_elb;
mod aws_elb_tg;
mod aws_rds;
mod aws_rds_sng;
mod aws_vpc_sg;
mod brdst;
mod brsrc;
mod bucket;
mod developer;
mod dns;
mod ecr;
mod ecr_public;
mod iam_role;
mod instance;
mod instance_profile;
mod keypair;
mod mysqldevimg;
mod sn;
mod vpc;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CreateFile,
    GenerateApp,
    GenerateAwsElasticacheServerless,
    GenerateAwsElbTargetGroups,
    GenerateAwsRdsSubnetGroups,
    GenerateAwsRdss,
    GenerateBrdst,
    GenerateBrsrc,
    GenerateBucket,
    GenerateDeveloper,
    GenerateEcrRepo,
    GenerateIamProfile,
    GenerateInstance,
    GenerateInstanceProfile,
    GenerateKeypair,
    GenerateMysqldevimg,
    GeneratePublicEcrRepo,
    GenerateSecurityGroups,
    GenerateSubnets,
    GenerateVpc,
    GenerateAwsElb,
    ReadDirectory,
    RemoveFile,
    WriteFile,
}

pub struct Output {
    pub prefix: String,
    pub contentmap: HashMap<Option<String>, String>,
    pub outputs: Vec<(String, String)>,
}

// TODO: need support for vector or hashmap outputs.
impl Output {
    pub fn new(prefix: &str) -> Self {
        let prefix = prefix.to_string();
        let contentmap: HashMap<Option<String>, String> = HashMap::new();
        let outputs = Vec::new();
        Self {
            prefix,
            contentmap,
            outputs,
        }
    }

    pub fn write(&mut self, name: &Option<String>, content: &str) {
        // append content to hashmap value, or create

        // get mutable reference to hashmap value
        match self.contentmap.get_mut(name) {
            Some(value) => {
                value.push_str(content);
            }
            None => {
                self.contentmap.insert(name.clone(), content.to_string());
            }
        };
    }

    pub fn add(&mut self, key: &str, value: &str) {
        self.outputs.push((key.to_string(), value.to_string()));
    }

    pub fn save(
        &self,
        env: &crate::Application,
        names: &mut Vec<String>,
    ) -> error_stack::Result<(), ErrorStack> {
        for (name, content) in &self.contentmap {
            let name = match name {
                Some(name) => format!("{}.{}.tf", &self.prefix, &name),
                None => format!("{}.tf", &self.prefix),
            };

            let path = env.terraform_path.join(&name);

            names.push(name);

            println!("Writing to {}", path.display());

            let mut file = File::create(&path).change_context(ErrorStack::CreateFile)?;
            file.write_all(content.as_bytes())
                .change_context(ErrorStack::WriteFile)?;
        }

        Ok(())
    }
}

pub async fn build(
    app: &crate::Application,
    config: &ace_graph::config::Config,
) -> error_stack::Result<(), ErrorStack> {
    let mut ace2_key_output = Output::new("ace2-key");
    let mut app_output = Output::new("app");
    let mut aws_ec_sls = Output::new("aws-ec-sls");
    let mut aws_rds_sng = Output::new("aws-rds-sng");
    let mut aws_vpc_sg_output = Output::new("aws-vpc-sg");
    let mut brdst_list_output = Output::new("brdsts");
    let mut brsrc_list_output = Output::new("brsrcs");
    let mut bucket_list_output = Output::new("buckets");
    let mut data_output: Output = Output::new("data");
    let mut developer_list_output = Output::new("developers");
    let mut dns_output = Output::new("dns");
    let mut ecr_output = Output::new("ecr");
    let mut ecr_public_output = Output::new("ecr-public");
    let mut iam_role = Output::new("iam-role");
    let mut instance_output = Output::new("instance");
    let mut instance_profile = Output::new("ins-profile");
    let mut keypair_output = Output::new("keypair");
    let mut mysqldevimg_output = Output::new("mysqldevimg");
    let mut sn_output = Output::new("subnet");
    let mut terraform_file = Output::new("terraform");
    let mut vpc_output = Output::new("vpc");
    let mut aws_elb_output = Output::new("aws_elb");
    let mut aws_elb_tg_output = Output::new("aws_elb_tg");
    let mut aws_rds_instance_output = Output::new("aws_rds_instance");

    #[rustfmt::skip]
    terraform_file.write(&None, &STSL!(r"
        terraform {
        }
    "));

    ace2_key::generate(config, &mut ace2_key_output);
    app::generate(config, &mut app_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateApp)?;
    aws_ec_sls::generate(config, &mut aws_ec_sls, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateAwsElasticacheServerless)?;
    aws_rds_sng::generate(config, &mut aws_rds_sng, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateAwsRdsSubnetGroups)?;
    aws_rds::generate(config, &mut aws_rds_instance_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateAwsRdss)?;
    aws_vpc_sg::generate(config, &mut aws_vpc_sg_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateSecurityGroups)?;
    brsrc::generate(config, &mut brsrc_list_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateBrsrc)?;
    brdst::generate(&mut brdst_list_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateBrdst)?;
    let ace2_bucket_output = bucket::generate(&mut bucket_list_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateBucket)?;
    developer::generate(&mut developer_list_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateDeveloper)?;
    dns::generate(config, &mut dns_output);
    ecr::generate(&mut ecr_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateEcrRepo)?;
    ecr_public::generate(&mut ecr_public_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GeneratePublicEcrRepo)?;
    iam_role::generate(config, &mut iam_role, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateIamProfile)?;
    let ins_data_tf_pairs = instance::generate(config, &mut instance_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateInstance)?;
    instance_profile::generate(config, &mut instance_profile, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateInstanceProfile)?;
    keypair::generate(config, &mut keypair_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateKeypair)?;
    mysqldevimg::generate(&mut mysqldevimg_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateMysqldevimg)?;
    sn::generate(config, &mut sn_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateSubnets)?;
    vpc::generate(config, &mut vpc_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateVpc)?;
    aws_elb::generate(config, &mut aws_elb_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateAwsElb)?;
    aws_elb_tg::generate(config, &mut aws_elb_tg_output, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GenerateAwsElbTargetGroups)?;

    #[rustfmt::skip]
    data_output.write(&None, &CNSL!(r#"
        resource "local_file" "output" {
            content = jsonencode({
                "#, JN!(&ace2_key_output.outputs, |(k,v)| format!("{}_{} = {},\n", "ace2_key", k, v)), r#"
                "#, JN!(&ins_data_tf_pairs, |(k,v)| format!("{k} = {v},\n")), r#"
                "#, JN!(ace2_bucket_output.outputs, |(k,v)| format!("{}_{} = {},\n", "ace2_bucket", k, v)), r#"
                "#, JN!(&app_output.outputs, |(k,v)| format!("{}_{} = {},\n", "app", k, v)), r#"
                "#, JN!(&aws_vpc_sg_output.outputs, |(k,v)| format!("{}_{} = {},\n", "mediaproctor", k, v)), r#"
                "#, JN!(&brsrc_list_output.outputs, |(k,v)| format!("{}_{} = {},\n", "brsrc_list", k, v)), r#"
                "#, JN!(&brdst_list_output.outputs, |(k,v)| format!("{}_{} = {},\n", "brdst_list", k, v)), r#"
                "#, JN!(&bucket_list_output.outputs, |(k,v)| format!("{}_{} = {},\n", "bucket_list", k, v)), r#"
                "#, JN!(&developer_list_output.outputs, |(k,v)| format!("{}_{} = {},\n", "developer_list", k, v)), r#"
                "#, JN!(&dns_output.outputs, |(k,v)| format!("{}_{} = {},\n", "dns", k, v)), r#"
                "#, JN!(&ecr_output.outputs, |(k,v)| format!("{}_{} = {},\n", "ecr_list", k, v)), r#"
                "#, JN!(&ecr_public_output.outputs, |(k,v)| format!("{}_{} = {},\n", "ecr_public_list", k, v)), r#"
                "#, JN!(&iam_role.outputs, |(k,v)| format!("{}_{} = {},\n", "iam_role", k, v)), r#"
                "#, JN!(&instance_output.outputs, |(k,v)| format!("{}_{} = {},\n", "instances", k, v)), r#"
                "#, JN!(&instance_profile.outputs, |(k,v)| format!("{}_{} = {},\n", "ins_profile", k, v)), r#"
                "#, JN!(&keypair_output.outputs, |(k,v)| format!("{}_{} = {},\n", "keypair", k, v)), r#"
                "#, JN!(&mysqldevimg_output.outputs, |(k,v)| format!("{}_{} = {},\n", "mysqldevimg", k, v)), r#"
                "#, JN!(&vpc_output.outputs, |(k,v)| format!("{}_{} = {},\n", "vpc", k, v)), r#"
                "#, JN!(&sn_output.outputs, |(k,v)| format!("{}_{} = {},\n", "subnet", k, v)), r#"
                "#, JN!(&aws_elb_output.outputs, |(k,v)| format!("{}_{} = {},\n", "aws_elb", k, v)), r#"
                "#, JN!(&aws_elb_tg_output.outputs, |(k,v)| format!("{}_{} = {},\n", "aws_elb_tg", k, v)), r#"
                "#, JN!(&aws_rds_instance_output.outputs, |(k,v)| format!("{}_{} = {},\n", "aws_rds_instance", k, v)), r#"
            })
            filename = "#, JE!(&app.data_terraform_output_path), r#"
            file_permission = "0600"
        }
    "#));

    let mut names: Vec<String> = Vec::new();

    ace2_key_output.save(app, &mut names)?;
    app_output.save(app, &mut names)?;
    aws_ec_sls.save(app, &mut names)?;
    aws_rds_sng.save(app, &mut names)?;
    aws_vpc_sg_output.save(app, &mut names)?;
    brdst_list_output.save(app, &mut names)?;
    brsrc_list_output.save(app, &mut names)?;
    bucket_list_output.save(app, &mut names)?;
    data_output.save(app, &mut names)?;
    developer_list_output.save(app, &mut names)?;
    dns_output.save(app, &mut names)?;
    ecr_output.save(app, &mut names)?;
    ecr_public_output.save(app, &mut names)?;
    iam_role.save(app, &mut names)?;
    instance_output.save(app, &mut names)?;
    instance_profile.save(app, &mut names)?;
    keypair_output.save(app, &mut names)?;
    mysqldevimg_output.save(app, &mut names)?;
    sn_output.save(app, &mut names)?;
    terraform_file.save(app, &mut names)?;
    vpc_output.save(app, &mut names)?;
    aws_elb_output.save(app, &mut names)?;
    aws_elb_tg_output.save(app, &mut names)?;
    aws_rds_instance_output.save(app, &mut names)?;

    // Delete all of the *.tf files in the terraform directory that were not just written to.
    let tf_files =
        std::fs::read_dir(&app.terraform_path).change_context(ErrorStack::ReadDirectory)?;
    let tf_ext = std::ffi::OsStr::new("tf");
    for file in tf_files {
        let file = file.change_context(ErrorStack::CreateFile)?;
        if file.path().extension() != Some(tf_ext) {
            continue;
        }
        if names.contains(&file.file_name().to_string_lossy().to_string()) {
            continue;
        }
        println!("Deleting {}", file.path().display());
        std::fs::remove_file(file.path()).change_context(ErrorStack::RemoveFile)?;
    }

    Ok(())
}

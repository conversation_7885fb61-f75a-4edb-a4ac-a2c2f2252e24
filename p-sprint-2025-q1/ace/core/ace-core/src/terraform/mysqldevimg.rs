use super::Output;
use error_stack::ResultExt;
use garbage::{CNSL, JE};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetMysqldevimgs,
}

pub async fn generate(
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let mysqldevimgs =
        ace_graph::mysqldevimg::select(&ace_graph::MysqlDevImgFilter::All, ace_db_app)
            .await
            .change_context(ErrorStack::GetMysqldevimgs)?;

    for mysqldevimg in mysqldevimgs {
        // for mysqldevimg in &config.mysqldevimg_list {
        let resource_name = format!("mysqldevimg-{}", mysqldevimg.name);
        let repository_name = format!("mysqldevimg/{}", mysqldevimg.name);
        let output_name = format!("mysqldevimg-{}-url", mysqldevimg.name);
        let sql_path = &mysqldevimg.sql_path;

        #[rustfmt::skip]
        output.write(&Some(mysqldevimg.name.to_string()), &CNSL!(r#"
            # ---
            # mysqldevimg for 
            # name: "#, JE!(mysqldevimg.name), r#"
            # sql_path: "#, JE!(sql_path), r#"

            resource "aws_ecr_repository" "#, JE!(resource_name), r#" {
                name = "#, JE!(repository_name), r#"
                image_tag_mutability = "MUTABLE"
                image_scanning_configuration {
                    scan_on_push = true
                }
            }

            output "#, JE!(output_name), r#" {
                value = aws_ecr_repository."#, resource_name, r#".repository_url
            }
        "#));
    }

    //output.add_output_pair("key_names", key_names.join(sep));

    Ok(())
}

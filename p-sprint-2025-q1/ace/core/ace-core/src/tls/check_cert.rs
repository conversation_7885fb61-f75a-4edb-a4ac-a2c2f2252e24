use error_stack::ResultExt;
use std::path::PathBuf;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CalculateExpiresIn,
    CertPublicKey,
    CertPublicKeyToPem,
    CommonNameEntryAsUTF8,
    CommonNameNotFoundInCert,
    CurrentTime,
    KeyPublicKeyToPem,
    PrivateKeyFromPem,
    ReadCertFile,
    ReadKeyFile,
    X509FromPem,
}

#[derive(Debug, <PERSON>lone)]
pub struct CertCheck {
    pub id: String,
    pub common_name: String,
    pub wildcard: bool,
    pub key_path: PathBuf,
    pub cert_path: PathBuf,
}

#[derive(Debug)]
pub enum CertStatus {
    FilesMissing {
        key_missing: bool,
        cert_missing: bool,
    },
    KeyMismatch,
    CommonNameMismatch {
        expected: String,
        actual: String,
    },
    Expired {
        expired_for_days: i32,
    },
    Valid {
        expires_in_days: i32,
    },
    Error(error_stack::Report<ErrorStack>),
}

impl CertStatus {
    pub fn get_status_name(&self) -> String {
        match self {
            CertStatus::CommonNameMismatch { .. } => "Common Name Mismatch".to_string(),
            CertStatus::Error(_) => "Error".to_string(),
            CertStatus::Expired { .. } => "Expired".to_string(),
            CertStatus::FilesMissing { .. } => "Files Missing".to_string(),
            CertStatus::KeyMismatch => "Key Mismatch".to_string(),
            CertStatus::Valid { .. } => "Valid".to_string(),
        }
    }
}

impl CertCheck {
    /// This is a wrapper around `check_wrapped` because it convets the `anyhow::Error` into a `CertStatus::Error(err)`
    pub fn check(&self) -> CertStatus {
        match self.check_wrapped() {
            Ok(status) => status,
            Err(err) => CertStatus::Error(err),
        }
    }

    fn check_wrapped(&self) -> error_stack::Result<CertStatus, ErrorStack> {
        let key_missing = !self.key_path.exists();
        let cert_missing = !self.cert_path.exists();

        if key_missing || cert_missing {
            return Ok(CertStatus::FilesMissing {
                key_missing,
                cert_missing,
            });
        }

        let key_pem = std::fs::read(&self.key_path).change_context(ErrorStack::ReadKeyFile)?;
        let cert_pem = std::fs::read(&self.cert_path).change_context(ErrorStack::ReadCertFile)?;

        let key = openssl::pkey::PKey::private_key_from_pem(&key_pem)
            .change_context(ErrorStack::PrivateKeyFromPem)?;
        let cert =
            openssl::x509::X509::from_pem(&cert_pem).change_context(ErrorStack::X509FromPem)?;

        let cert_public_key = cert
            .public_key()
            .change_context(ErrorStack::CertPublicKey)?;
        let key_public_key = key
            .public_key_to_pem()
            .change_context(ErrorStack::KeyPublicKeyToPem)?;

        if cert_public_key
            .public_key_to_pem()
            .change_context(ErrorStack::CertPublicKeyToPem)?
            != key_public_key
        {
            return Ok(CertStatus::KeyMismatch);
        }

        let subject_name = cert.subject_name();
        let common_name_entry = subject_name
            .entries_by_nid(openssl::nid::Nid::COMMONNAME)
            .next();
        let actual_common_name = match common_name_entry {
            Some(entry) => entry
                .data()
                .as_utf8()
                .change_context(ErrorStack::CommonNameEntryAsUTF8)?
                .to_string(),
            None => error_stack::bail!(ErrorStack::CommonNameNotFoundInCert),
        };

        if actual_common_name != self.common_name {
            return Ok(CertStatus::CommonNameMismatch {
                expected: self.common_name.clone(),
                actual: actual_common_name,
            });
        }

        // Check expiration
        let not_after = cert.not_after();
        let current_time =
            openssl::asn1::Asn1Time::days_from_now(0).change_context(ErrorStack::CurrentTime)?;
        let expires_in_days = -not_after
            .diff(&current_time)
            .change_context(ErrorStack::CalculateExpiresIn)?
            .days;

        if expires_in_days < 0 {
            return Ok(CertStatus::Expired {
                expired_for_days: -expires_in_days,
            });
        }

        Ok(CertStatus::Valid { expires_in_days })
    }
}

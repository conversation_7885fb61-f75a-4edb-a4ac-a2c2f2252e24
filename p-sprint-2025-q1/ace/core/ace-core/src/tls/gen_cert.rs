use error_stack::ResultExt;
use std::io::Write;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    ConvertRequestToPem,
    CreateFile,
    CreateRoute53Connection,
    DownloadCert,
    GenerateRSA,
    GetCnameValidation,
    GetCommonNameOrWildcard,
    GetConfig,
    GitAutocommit,
    HostedZoneKeyFoundButNone(String),
    HostedZoneKeyNotFoundInHostedZoneMap(String),
    KeyPemToString,
    NewX509NameBuilder,
    NewX509ReqBuilder,
    NoZeroSslKeyInConfigToml,
    PemRequestToString,
    PKeyFromRSA,
    PrivateKeyToPemPkcs8,
    RequestCert,
    SignRequest,
    TimeoutWaitingForCert,
    Validation,
    WriteToFile,
}

pub async fn run(
    app: &crate::Application,
    certificate: &super::Certificate,
) -> error_stack::Result<(), ErrorStack> {
    let (hosted_zone_map, zerossl_key) = {
        let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
            .await
            .change_context(ErrorStack::GetConfig)?;
        (config.hosted_zone_map, config.zerossl_key)
    };

    let zerossl_key = match zerossl_key {
        Some(key) => key.clone(),
        None => error_stack::bail!(ErrorStack::NoZeroSslKeyInConfigToml),
    };

    let hosted_zone_id = match hosted_zone_map.get(&certificate.hosted_zone_key) {
        Some(hosted_zone) => match &hosted_zone.hosted_zone_id {
            Some(id) => id,
            None => error_stack::bail!(ErrorStack::HostedZoneKeyFoundButNone(
                certificate.hosted_zone_key.clone()
            )),
        },
        None => error_stack::bail!(ErrorStack::HostedZoneKeyNotFoundInHostedZoneMap(
            certificate.hosted_zone_key.clone()
        )),
    };

    // Generate a CSR
    let key_and_csr = KeyAndCsr::new(certificate)?;

    // Print csr
    println!("Domains: {}", key_and_csr.certificate_domains);
    println!("CSR:\n{}", key_and_csr.csr);

    // Request the cert from Zero SSL
    println!("Requesting cert");
    let cert = zero_ssl_create::call(
        &zerossl_key,
        &key_and_csr.certificate_domains,
        &key_and_csr.csr,
    )
    .await
    .change_context(ErrorStack::RequestCert)?;

    // Print the cert
    println!(
        "... cert_id {:?} for common name {:?}",
        cert.id, cert.common_name
    );

    // Get the cname validation
    let (cname_validation_p1, cname_validation_p2) = cert
        .get_cname_validation()
        .change_context(ErrorStack::GetCnameValidation)?;
    println!("DNS Validation: {cname_validation_p1} IN CNAME {cname_validation_p2}");

    // Create connection to route53
    let route53 = ace_aws::route53::open()
        .await
        .change_context(ErrorStack::CreateRoute53Connection)?;

    let moved_cert_id = cert.id.clone();
    let moved_zerossl_key = zerossl_key.clone();
    let callback =
        Box::pin(async move { zero_ssl_validate::call(&moved_zerossl_key, &moved_cert_id).await });

    // Create the record
    let validate_success = route53
        .transient_cname_record::<zero_ssl_validate::Success, zero_ssl_validate::ErrorStack>(
            hosted_zone_id,
            &cname_validation_p1,
            &cname_validation_p2,
            callback,
        )
        .await
        .change_context(ErrorStack::Validation)?;

    println!("validate_success: {validate_success:?}");

    // check status every 5 seconds until it's ready
    let mut i = 0;
    let info = loop {
        i += 1;
        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
        println!("Checking status");
        match zero_ssl_info::call(&zerossl_key, &cert.id).await {
            Ok(info) => {
                println!("status: {:?}", info.status);
                if info.status == "issued" {
                    break info;
                }
            }
            Err(e) => {
                println!("error: {e:?}");
            }
        };
        if i > 100 {
            error_stack::bail!(ErrorStack::TimeoutWaitingForCert);
        }
    };

    // print
    println!("info: {info:?}");

    // download the cert
    let cert = zero_ssl_download::call(&zerossl_key, &cert.id)
        .await
        .change_context(ErrorStack::DownloadCert)?;

    println!(
        "Writing cert and bundle to {}",
        certificate.get_cert_path(app).display()
    );
    // Write the cert.certificate_crt and cert.ca_bundle_crt to the file in app.server_cert_path
    {
        let mut file = std::fs::File::create(certificate.get_cert_path(app))
            .change_context(ErrorStack::CreateFile)?;
        file.write_all(cert.certificate_crt.as_bytes())
            .change_context(ErrorStack::WriteToFile)?;
        file.write_all(cert.ca_bundle_crt.as_bytes())
            .change_context(ErrorStack::WriteToFile)?;
    }

    println!("Writing key to {}", certificate.get_key_path(app).display());
    // Write the key to the file in app.server_key_path
    {
        let mut file = std::fs::File::create(certificate.get_key_path(app))
            .change_context(ErrorStack::CreateFile)?;
        file.write_all(key_and_csr.key.as_bytes())
            .change_context(ErrorStack::WriteToFile)?;
    }

    crate::git::autocommit(app, &String::from("after tls generation"))
        .await
        .change_context(ErrorStack::GitAutocommit)?;

    Ok(())
}

struct KeyAndCsr {
    certificate_domains: String,
    key: String,
    csr: String,
}

impl KeyAndCsr {
    fn new(certificate: &super::Certificate) -> error_stack::Result<Self, ErrorStack> {
        // Generate a 2048-bit RSA key pair
        let rsa = openssl::rsa::Rsa::generate(2048).change_context(ErrorStack::GenerateRSA)?;
        let pkey = openssl::pkey::PKey::from_rsa(rsa).change_context(ErrorStack::PKeyFromRSA)?;

        // Convert the key to pem format
        let key_pem = pkey
            .private_key_to_pem_pkcs8()
            .change_context(ErrorStack::PrivateKeyToPemPkcs8)?;
        let key_pem = String::from_utf8(key_pem).change_context(ErrorStack::KeyPemToString)?;

        // Create a X509 name
        let mut name_builder =
            openssl::x509::X509NameBuilder::new().change_context(ErrorStack::NewX509NameBuilder)?;
        name_builder
            .append_entry_by_text("CN", &certificate.get_common_name_or_wildcard())
            .change_context(ErrorStack::GetCommonNameOrWildcard)?;
        let x509_name = name_builder.build();

        // Build the CSR
        let mut builder =
            openssl::x509::X509ReqBuilder::new().change_context(ErrorStack::NewX509ReqBuilder)?;
        builder.set_subject_name(&x509_name).unwrap();
        builder.set_pubkey(&pkey).unwrap();
        /*
               if certificate.is_wildcard() {
                   let san_extension = openssl::x509::extension::SubjectAlternativeName::new()
                       .dns(&format!("*.{}", certificate.get_common_name()))
                       .dns(&certificate.get_common_name())
                       .build(&builder.x509v3_context(None))?;

                   let mut stack = openssl::stack::Stack::new()?;
                   stack.push(san_extension)?;

                   builder.add_extensions(&stack)?;
               }
        */
        // Sign the request using the private key and SHA-256 digest
        builder
            .sign(&pkey, openssl::hash::MessageDigest::sha256())
            .change_context(ErrorStack::SignRequest)?;

        // Convert the request to a PEM encoded string
        let req = builder.build();
        let csr_pem = req
            .to_pem()
            .change_context(ErrorStack::ConvertRequestToPem)?;
        let csr_pem = String::from_utf8(csr_pem).change_context(ErrorStack::PemRequestToString)?;

        let certificate_domains = certificate.get_common_name_or_wildcard();

        Ok(Self {
            certificate_domains,
            key: key_pem,
            csr: csr_pem,
        })
    }
}

mod zero_ssl_create {
    use error_stack::ResultExt;
    use serde::Deserialize;

    #[allow(dead_code)]
    #[derive(Debug, ace_proc::ErrorStack)]
    pub enum ErrorStack {
        CnameValidationNotFound,
        ParseResponseSerde,
        ProcessResponse(serde_json::Value),
        SendPostRequest,
        ZeroSslApiError(serde_json::Value),
    }

    pub async fn call(
        api_key: &str,
        certificate_domains: &str,
        csr: &str,
    ) -> error_stack::Result<Success, ErrorStack> {
        // Construct the URL using the api key to api.zerossl.com/certificates?access_key=...
        let url = format!(
            "https://api.zerossl.com/certificates?access_key={}",
            urlencoding::encode(api_key)
        );

        // Define the form data payload
        let mut payload = std::collections::HashMap::new();
        payload.insert("certificate_domains", certificate_domains);
        payload.insert("certificate_csr", csr);

        // Send a POST request to the ZeroSSL API with the payload
        let client = reqwest::Client::new();
        let response = client
            .post(&url)
            .form(&payload)
            .send()
            .await
            .change_context(ErrorStack::SendPostRequest)?;

        let value: serde_json::Value = response
            .json()
            .await
            .change_context(ErrorStack::ParseResponseSerde)?;

        //TODO: need a better way to have access to the value for error reporting
        let value_for_error = value.clone();

        if value["success"].as_bool() == Some(false) {
            error_stack::bail!(ErrorStack::ZeroSslApiError(value));
        }

        // If success=false, it will fail deserialization, and print the full error response
        serde_json::from_value::<Success>(value)
            .change_context(ErrorStack::ProcessResponse(value_for_error))
    }

    #[derive(Debug, Deserialize)]
    pub struct Success {
        pub id: String,
        //#[serde(rename = "type")]
        //type_: String,
        pub common_name: String,
        //additional_domains: String,
        //created: String,
        //expires: String,
        //status: String,
        //validation_type: Option<String>,
        //validation_emails: Option<String>,
        //replacement_for: String,
        validation: Validation,
    }

    #[derive(Debug, Deserialize)]
    pub struct Validation {
        //email_validation: std::collections::HashMap<String, Vec<String>>,
        other_methods: std::collections::HashMap<String, OtherMethods>,
    }

    #[derive(Debug, Deserialize)]
    pub struct OtherMethods {
        //file_validation_url_http: String,
        //file_validation_url_https: String,
        //file_validation_content: Vec<String>,
        cname_validation_p1: String,
        cname_validation_p2: String,
    }

    impl Success {
        pub fn get_cname_validation(&self) -> error_stack::Result<(String, String), ErrorStack> {
            let Some(other_methods) = self.validation.other_methods.get(&self.common_name) else {
                error_stack::bail!(ErrorStack::CnameValidationNotFound)
            };

            let cname_validation_p1 = other_methods.cname_validation_p1.clone();
            let cname_validation_p2 = other_methods.cname_validation_p2.clone();
            Ok((cname_validation_p1, cname_validation_p2))
        }
    }
}

mod zero_ssl_validate {
    use error_stack::ResultExt;
    use serde::Deserialize;

    #[allow(dead_code)]
    #[derive(Debug, ace_proc::ErrorStack)]
    pub enum ErrorStack {
        ZeroSslApiError(serde_json::Value),
        ProcessResponse(serde_json::Value),
        GetPostResponse,
        DeserializePostResponse,
    }

    pub async fn call(api_key: &str, id: &str) -> error_stack::Result<Success, ErrorStack> {
        // Construct the URL using the api key to api.zerossl.com/certificates?access_key=...
        let url = format!(
            "https://api.zerossl.com/certificates/{}/challenges?access_key={}",
            urlencoding::encode(id),
            urlencoding::encode(api_key)
        );

        // Define the form data payload
        let mut payload = std::collections::HashMap::new();
        payload.insert("validation_method", "CNAME_CSR_HASH");

        // Send a POST request to the ZeroSSL API with the payload
        let client = reqwest::Client::new();
        let response = client
            .post(&url)
            .form(&payload)
            .send()
            .await
            .change_context(ErrorStack::GetPostResponse)?;

        let value: serde_json::Value = response
            .json()
            .await
            .change_context(ErrorStack::DeserializePostResponse)?;

        //TODO: need a better way to have access to the value for error reporting
        let value_for_error = value.clone();

        if value["success"].as_bool() == Some(false) {
            error_stack::bail!(ErrorStack::ZeroSslApiError(value));
        }

        // If success=false, it will fail deserialization, and print the full error response
        serde_json::from_value::<Success>(value)
            .change_context(ErrorStack::ProcessResponse(value_for_error))
    }

    #[allow(dead_code)]
    #[derive(Debug, Deserialize)]
    pub struct Success {
        pub id: String,
        //#[serde(rename = "type")]
        //type_: String,
        pub common_name: String,
        //additional_domains: String,
        //created: String,
        //expires: String,
        pub status: String,
        //validation_type: Option<String>,
        //validation_emails: Option<String>,
        //replacement_for: String,
        //validation: Validation,
    }
}

mod zero_ssl_info {
    use error_stack::ResultExt;
    use serde::Deserialize;

    #[allow(dead_code)]
    #[derive(Debug, ace_proc::ErrorStack)]
    // #[derive(Debug)]
    pub enum ErrorStack {
        ParseResponseSerde,
        ProcessResponse(serde_json::Value),
        SendResponse,
        ZeroSslApiError(serde_json::Value),
    }

    pub async fn call(api_key: &str, id: &str) -> error_stack::Result<Success, ErrorStack> {
        // Construct the URL using the api key to api.zerossl.com/certificates?access_key=...
        let url = format!(
            "https://api.zerossl.com/certificates/{}?access_key={}",
            urlencoding::encode(id),
            urlencoding::encode(api_key)
        );

        // Send a POST request to the ZeroSSL API with the payload
        let client = reqwest::Client::new();
        let response = client
            .get(&url)
            .send()
            .await
            .change_context(ErrorStack::SendResponse)?;

        let value: serde_json::Value = response
            .json()
            .await
            .change_context(ErrorStack::ParseResponseSerde)?;

        //TODO: need a better way to have access to the value for error reporting
        let value_for_error = value.clone();

        if value["success"].as_bool() == Some(false) {
            error_stack::bail!(ErrorStack::ZeroSslApiError(value));
        }

        // If success=false, it will fail deserialization, and print the full error response
        // serde_json::from_value::<Success>(value)
        //     .with_context(|| format!("Error while processing response:\n{}", value_for_error))

        serde_json::from_value::<Success>(value)
            .change_context(ErrorStack::ProcessResponse(value_for_error))
    }

    #[allow(dead_code)]
    #[derive(Debug, Deserialize)]
    pub struct Success {
        pub id: String,
        //#[serde(rename = "type")]
        //type_: String,
        pub common_name: String,
        //additional_domains: String,
        //created: String,
        //expires: String,
        pub status: String,
        //validation_type: Option<String>,
        //validation_emails: Option<String>,
        //replacement_for: String,
        //validation: Validation,
    }
}

mod zero_ssl_download {
    use error_stack::ResultExt;
    use serde::Deserialize;

    #[allow(dead_code)]
    #[derive(Debug, ace_proc::ErrorStack)]
    pub enum ErrorStack {
        SendResponse,
        ParseResponseSerde,
        ProcessResponse(serde_json::Value),
        ZeroSslApiError(serde_json::Value),
    }

    pub async fn call(api_key: &str, id: &str) -> error_stack::Result<Success, ErrorStack> {
        // Construct the URL using the api key to api.zerossl.com/certificates?access_key=...
        let url = format!(
            "https://api.zerossl.com/certificates/{}/download/return?access_key={}",
            urlencoding::encode(id),
            urlencoding::encode(api_key)
        );

        // Send a POST request to the ZeroSSL API with the payload
        let client = reqwest::Client::new();
        let response = client
            .get(&url)
            .send()
            .await
            .change_context(ErrorStack::SendResponse)?;

        let value: serde_json::Value = response
            .json()
            .await
            .change_context(ErrorStack::ParseResponseSerde)?;

        //TODO: need a better way to have access to the value for error reporting
        let value_for_error = value.clone();

        if value["success"].as_bool() == Some(false) {
            error_stack::bail!(ErrorStack::ZeroSslApiError(value));
        }

        serde_json::from_value::<Success>(value)
            .change_context(ErrorStack::ProcessResponse(value_for_error))
    }
    #[derive(Deserialize, Debug)]
    pub struct Success {
        #[serde(rename = "certificate.crt")]
        pub certificate_crt: String,
        #[serde(rename = "ca_bundle.crt")]
        pub ca_bundle_crt: String,
    }
}

# Summary of how this all works

`openvpn` and `easy-rsa` are ubuntu 22.04 packages that can be installed with `apt install`

`easy-tls` is an addon for `easy-rsa` that can be installed in `/usr/local/bin` and made executable.
https://github.com/TinCanTech/easy-tls

Here is the basic instructions for using these tools:
https://community.openvpn.net/openvpn/wiki/EasyRSA3-OpenVPN-Howto

## Certificate Authority

This should exist separatly, on ace

- This dir is created using the `make-cadir` command
- It has a set of files that are managed by easy-rsa and easy-tls
- Files from this dir need to be sent to people at times and places
- Certificate revocations are managed here

## Server config

### AWS Instance Config

On the instance, source/dest checking needs disabled (networking)

### IP Forwarding

To check: `sysctl net.ipv4.ip_forward`
To set: `sysctl -w net.ipv4.ip_forward=1`
To persist: set proper option in `/etc/sysctl.conf`

### `/etc/openvpn/server.conf`

- This can consist of some basic preludes followed by
- the CRL prodiuced by the CA followed by the
- the inline config produced by the easy-rsa tools

### Firewall rule needed:

`iptables -t nat -A POSTROUTING -s *************/22 -d *********/16 -j MASQUERADE`

### Persist firewall

In the server image we have run:

`systemctl enable nftables`

To save the applied firewall, use
`nft list ruleset > /etc/nftables.conf`

## Certificate Revocation List

- This needs to be embedded in the config.

# server config looks something like:

```
port 1194
proto udp
dev tun

# Must be out of the VPN address space
server ************* *************

# Push the route to the clients
push "route ********* ***********"

ifconfig-pool-persist server-ipp.txt
keepalive 10 120
persist-key
persist-tun
status server-status.log
verb 3

auth sha256

<crl-verify>
# THIS SHOULD BE THE CONTENT OF THE CRL FILE FROM EASYRSA
</crl-verify>

# FOLLOWING CONTENT IS INLINE CONFIG FROM easyrsa

# EASYTLS
# EasyTLS version 2.8.0
# Common name: ...
# X509 serial: ...
<cert>
Certificate:
...
-----BEGIN CERTIFICATE-----
...
-----END CERTIFICATE-----
</cert>

<key>
-----BEGIN PRIVATE KEY-----
...
-----END PRIVATE KEY-----
</key>

<ca>
-----BEGIN CERTIFICATE-----
...
-----END CERTIFICATE-----
</ca>

dh none

# TLS auth

key-direction 0

<tls-auth>
#
# 2048 bit OpenVPN static key
#
-----BEGIN OpenVPN Static key V1-----
...
-----END OpenVPN Static key V1-----
</tls-auth>
```

# Client config looks something like this:

```
setenv FORWARD_COMPATIBLE 1
client
server-poll-timeout 4
nobind
remote vpn.us-west-2.ctzen.link 1194 udp
dev tun
dev-type tun
remote-cert-tls server
setenv opt tls-version-min 1.0 or-highest
reneg-sec 604800
sndbuf 0
rcvbuf 0
verb 3
setenv PUSH_PEER_INFO
auth SHA256
pull

# Everything below this line is generated by easy-rsa INLINE CONFIG

# EASYTLS
# EasyTLS version 2.8.0
# Common name: sergio
# X509 serial: 3AA6EF446CF987869880248460ACA29D
<cert>
Certificate:
...
-----BEGIN CERTIFICATE-----
...
-----END CERTIFICATE-----
</cert>

<key>
-----BEGIN PRIVATE KEY-----
...
-----END PRIVATE KEY-----
</key>

<ca>
-----BEGIN CERTIFICATE-----
...
-----END CERTIFICATE-----
</ca>

# TLS auth

key-direction 1

<tls-auth>
#
# 2048 bit OpenVPN static key
#
-----BEGIN OpenVPN Static key V1-----
...
-----END OpenVPN Static key V1-----
</tls-auth>
```

# Commands

## Create ca

`./easyrsa init-pki`
`./easyrsa build-ca nopass`
`./easytls init-tls`
`./easytls build-tls-auth`
`./easytls build-tls-crypt`

## Create a server

`./easyrsa build-server-full vpn.us-west-2.ctzen.link nopass`
`./easytls build-tls-crypt-v2-server vpn.us-west-2.ctzen.link`

## Generate server config (Needed for server config file)

`./easytls inline-tls-auth vpn.us-west-2.ctzen.link`

## Create a client

`./easyrsa build-client-full jason`
`./easytls build-tls-crypt-v2-client vpn.us-west-2.ctzen.link jason`

## Generate client config (Needed for client config file)

`./easytls inline-tls-auth jason`

## Revoke cert

`./easyrsa revoke sergio`

## Generate CRL (Needed for server config file)

`./easyrsa gen-crl`

# Additional Notes:

Observe icmp activity on vpn:\
`tcpdump -Ann -i any icmp`

use tokio::io::AsyncWriteExt;
use tokio::process::Command;

const MAX_EXPIRE_SECONDS: u32 = 3600; // one hour
const DEFAULT_EXPIRE_SECONDS: u32 = 600; // ten minutes
const SIGNING_IDENTITY: &str = "ace-ssh";

/// Obtains a signed certificate from the CA for a user's key.
/// There are three steps a request MUST pass to get a certificate signed:
/// 1. The fingerprint must match a user's key
/// 2. The signature must be valid
/// 3. The user must have ALL of the required principals, not just one or some.
/// - Steps are checked in this order.
///
/// # Errors
/// Returns an error in the following cases:
/// - If there is a problem listing users
/// - If the given fingerprint's format is unexpected
/// - If no matching key/fingerprint is found
/// - If there is an error verifying the signature
/// - If the user does not have all required principals
/// - If the expire parameter is outside the acceptable range
/// - If there is an error writing the key to a temporary file
/// - If the signing command fails
/// - If there is an error reading the signed certificate file
pub async fn get_certificate(
    app: &'static ace_db::App,
    temp_path: std::path::PathBuf,
    fingerprint: String,
    requested_principal_list: Vec<String>,
    expire: Option<u32>,
    signature: String,
) -> Result<String, String> {
    // verify the expire parameter

    let expire = expire.unwrap_or(DEFAULT_EXPIRE_SECONDS);

    if !(1..=MAX_EXPIRE_SECONDS).contains(&expire) {
        return Err(format!(
            "Expire parameter is outside the acceptable range. (1-{MAX_EXPIRE_SECONDS} seconds): {expire}"
        ));
    }

    // Select users to check for matching fingerprint/key
    let users = match ace_graph::user::select(&ace_graph::UserFilter::All, app).await {
        Ok(users) => users,
        Err(e) => {
            eprintln!("Error listing users: {e:#?}");
            return Err("Error listing users".to_string());
        }
    };

    let fingerprint_hash = {
        // Fingerprints are always in OpenSSH format: SHA256:fingerprint user@hostname (RSA)
        // Example: 4096 SHA256:3J6gobbedlygook elizabeth@brickbox (RSA)
        // Strip the prefix and comment off fingerprint for comparison purposes:
        let fingerprint_vec = fingerprint.split_whitespace().collect::<Vec<&str>>();

        if fingerprint_vec.len() != 4 {
            return Err("Fingerprint format is unexpected...".to_string());
        }

        fingerprint_vec[1]
    };

    // Stores the user and key index that matches the fingerprint
    let mut matching_key_tuple: Option<(ace_graph::user::User, usize)> = None;

    // **Step 1. Find a matching fingerprint
    // - Iterate through all users and their keys
    // TODO: matching more than one user is.... ???
    for user in users {
        for (key_index, key) in user.ssh_keys.iter().enumerate() {
            let processed_public_key = match ssh_key::PublicKey::from_openssh(key) {
                Ok(key) => key,
                Err(e) => {
                    eprintln!("Error parsing public key (user {}): {e:#?}", user.graphkey);
                    continue;
                }
            };

            let this_keys_fingerprint = processed_public_key
                .fingerprint(ssh_key::HashAlg::Sha256)
                .to_string();

            if fingerprint_hash == this_keys_fingerprint {
                eprintln!(
                    "Match found for fingerprint: {fingerprint_hash} in user: {}",
                    user.graphkey
                );

                matching_key_tuple = Some((user, key_index));
                break;
            }
        }
    }

    // - If no match is found, deny/return an error.
    let (user, user_key_index) = match matching_key_tuple {
        Some((user, key_index)) => (user, key_index),
        None => {
            eprintln!("No match found for fingerprint: {fingerprint_hash}");
            return Err("No matching key found".to_string());
        }
    };

    // **Step 2. Verify the signature
    // - Pull out the user's key and reconstruct the original query string for signature verification
    let user_key = user.ssh_keys[user_key_index].clone();
    let query_string_to_sign =
        build_query_string_to_sign(&fingerprint, &requested_principal_list, expire);

    eprintln!("query_string_to_sign: {query_string_to_sign:?}");

    // - Verify the signature
    match verify_ssh_signature(
        &temp_path,
        &user_key,
        &query_string_to_sign,
        &signature,
        SIGNING_IDENTITY,
    )
    .await
    {
        Ok(true) => println!("Signature verified"),
        Ok(false) => {
            return Err("Invalid signature".to_string());
        }
        Err(e) => {
            eprintln!(
                "Error verifying signature for {query_string_to_sign} and matching key in user {}: {e}",
                user.graphkey
            );
            return Err(e.to_string());
        }
    }

    // **Step 3. Check if the user has ALL specified principals
    // - Convert the user's allowed principals and request's principals to sets
    let user_principal_set = user
        .allowed_principals
        .into_iter()
        .collect::<std::collections::HashSet<String>>();

    // cloning this one because we need principals later
    let requested_principal_set = requested_principal_list
        .iter()
        .cloned()
        .collect::<std::collections::HashSet<String>>();

    eprintln!(
        "Checking if user {} has all required principals...",
        user.graphkey
    );

    // - If the user does not have all of the requested principals, return an error.
    if !requested_principal_set.is_subset(&user_principal_set) {
        // calculate the missing principals
        let mut missing_principals = requested_principal_set
            .difference(&user_principal_set)
            .cloned()
            .collect::<Vec<_>>();

        // sort them
        missing_principals.sort();

        // convert to comma-separated string
        let missing_principals = missing_principals.join(", ");

        eprintln!("User {} is denied.", user.graphkey);
        return Err(format!(
            "User does not have the required principals: {missing_principals}"
        ));
    }

    // - Step 3 passed, user is approved.
    eprintln!("User {} is approved. Proceeding...", user.graphkey);

    // write key to a temp file
    let uuid4 = uuid::Uuid::new_v4();
    let public_key_path = temp_path.join(format!("ssh-keygen-{uuid4}.pub"));
    let cert_path = temp_path.join(format!("ssh-keygen-{uuid4}-cert.pub"));

    eprintln!("Writing key to temporary file: {cert_path:?}");
    tokio::fs::write(&public_key_path, user_key)
        .await
        .map_err(|e| format!("tokio::fs::write() -> {e:?}"))?;

    // sign the certificate
    let mut cmd = tokio::process::Command::new("ssh-keygen");
    cmd.arg("-s").arg(app.path.join("ca/pki/private/ca.key"));

    // Identity
    cmd.arg("-I").arg(SIGNING_IDENTITY);

    // Namespace
    cmd.arg("-n").arg(requested_principal_list.join(","));

    // Validity period
    cmd.arg("-V").arg(format!("+{expire}s"));

    // Path to the public key
    cmd.arg(public_key_path);

    // grab stdout, stderr, and exit status
    cmd.stderr(std::process::Stdio::piped());
    cmd.stdout(std::process::Stdio::piped());

    // run the ssh-keygen command to sign the cert
    let output = cmd.output().await.map_err(|e| e.to_string())?;

    if !output.status.success() {
        eprintln!("stderr: {:?}", output.stderr);
        return Err(format!(
            "ssh-keygen failed with status: {}",
            output.status.code().unwrap_or(-1)
        ));
    }

    let cert = tokio::fs::read_to_string(cert_path)
        .await
        .map_err(|e| format!("tokio::fs::read_to_string() -> {e:?}"))?;

    eprintln!("Signed cert: {cert}");

    Ok(cert)
}

/// Verifies the signature of a query string using ssh-keygen.
///
/// # Errors
/// Returns an error in the following cases:
/// - If there is an error writing the allowed signers file
/// - If there is an error writing the signature file
/// - If there is an error spawning the signature verification command
/// - If there is an error writing to the signature verification command's stdin
/// - If there is an error waiting for the signature verification command to complete
/// - If the signature verification command fails
/// - If the signature verification command's output is not successful
pub async fn verify_ssh_signature(
    temp_path: &std::path::Path,
    public_key: &str,
    query_string_message: &String,
    signature: &str,
    signing_identity: &str,
) -> granite::Result<bool> {
    eprintln!("Verifying signature...");

    // Create and write the allowed signers file
    let allowed_signers_path = temp_path.join(format!(
        "ssh-keygen-allowed-signers-{}",
        uuid::Uuid::new_v4()
    ));

    let allowed_signers_content = format!("{signing_identity} {public_key}\n");
    tokio::fs::write(&allowed_signers_path, allowed_signers_content).await?;

    // Create and write the signature file
    let signature_path = temp_path.join(format!("ssh-keygen-signature-{}", uuid::Uuid::new_v4()));
    tokio::fs::write(&signature_path, signature).await?;

    // Prepare the ssh-keygen command to verify the signature
    // written out for debugging purposes: (using acctkey@region as the working directory)
    // ssh-keygen -Y verify -f tmp/ssh-keygen-allowed-signers-<uuid> -I user -n ace_ssh_signing -s tmp/ssh-keygen-signature-<uuid>
    let mut cmd = Command::new("ssh-keygen");
    cmd.arg("-Y")
        .arg("verify")
        .arg("-f")
        .arg(&allowed_signers_path)
        // Identity
        .arg("-I")
        .arg(signing_identity)
        // Namespace
        .arg("-n")
        .arg("ace_ssh_signing")
        // Signature/path
        .arg("-s")
        .arg(&signature_path);

    // Set up stdin, stdout, and stderr
    cmd.stdin(std::process::Stdio::piped());
    cmd.stdout(std::process::Stdio::piped());
    cmd.stderr(std::process::Stdio::piped());

    eprintln!("allowed_signers_path: {allowed_signers_path:?}");
    eprintln!("signature_path: {signature_path:?}");
    eprintln!("ssh-keygen command: {cmd:?}");
    eprintln!("content: {:?}\n", query_string_message.as_bytes());

    let mut child = match cmd.spawn() {
        Ok(child) => child,
        Err(e) => {
            return Err(granite::Error::new(granite::ErrorType::Unexpected)
                .add_context(format!("Error spawning ssh-keygen command: {e:#?}")));
        }
    };

    // Send the query string to the ssh-keygen command's stdin
    if let Some(mut stdin) = child.stdin.take() {
        match stdin
            .write_all(query_string_message.clone().as_bytes())
            .await
        {
            Ok(()) => println!("Wrote to stdin: {query_string_message}"),
            Err(e) => eprintln!("Error writing to stdin: {e:#?}"),
        }
        match stdin.shutdown().await {
            Ok(()) => println!("Shutdown stdin"),
            Err(e) => eprintln!("Error shutting down stdin: {e:#?}"),
        }
    } else {
        return Err(granite::Error::new(granite::ErrorType::Unexpected)
            .add_context("No stdin available for ssh-keygen command"));
    }

    // Wait for the command to complete and capture output
    let output = match cmd.output().await {
        Ok(output) => output,
        Err(e) => {
            return Err(granite::Error::new(granite::ErrorType::Unexpected)
                .add_context(format!("Error waiting for ssh-keygen command: {e:#?}")));
        }
    };

    // Check the exit status and handle stderr
    if output.status.success() {
        eprintln!("stdout: {:?}", String::from_utf8_lossy(&output.stdout));
        Ok(true)
    } else {
        let stderr = String::from_utf8_lossy(&output.stderr);
        eprintln!("ssh-keygen stderr: {stderr}");
        Err(granite::process_error!("ssh-keygen failed"))
    }
}

/// Recreates the query string coming from ace-ssh to sign for the ssh-keygen command.
///
/// # Arguments
/// * `fingerprint` - The fingerprint of the key to sign
/// * `principals` - The principals to sign the certificate for
/// * `expire` - The number of seconds the certificate will be valid for
pub fn build_query_string_to_sign(
    fingerprint: &str,
    principals: &Vec<String>,
    expire: u32,
) -> String {
    let mut serializer = form_urlencoded::Serializer::new(String::new());

    serializer.append_pair("fingerprint", fingerprint);

    // Add principal parameters
    for principal in principals {
        serializer.append_pair("principal", principal);
    }

    serializer.append_pair("expire", &expire.to_string());

    serializer.finish()
}

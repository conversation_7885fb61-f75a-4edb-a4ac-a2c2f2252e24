use error_stack::ResultExt;
use indicatif::{MultiProgress, ProgressBar, ProgressStyle};
use sha2::{Digest, Sha256};
use std::os::unix::fs::PermissionsExt;
use std::{
    fs::File,
    path::{Path, PathBuf},
};
use tokio::io::AsyncWriteExt;
use zip::ZipArchive;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    ArchiveEntryIsNotOk,
    CopyFileToOutput,
    CreateNewZipArchive,
    CreateOutputFile,
    CreateTempFile,
    GetArchiveEntries,
    GetClientResponse,
    GetFilePath,
    GetMetadata,
    MoveGocryptfs,
    MoveNebulaAndNebulaCert,
    MoveUnsuccessful,
    OpenTarGZFile,
    OpenZIPFile,
    PrintMPBMessage,
    RemoveTempFile,
    RunMoveCommand,
    RunUntarCommand,
    SearchArchiveFor(String),
    SetPer<PERSON>s,
    Sha256<PERSON>ismatch(String),
    StreamChunk,
    UnsupportedArchiveFormat(String),
    UntarGocryptfs,
    UntarNebula,
    UntarUnsuccessful,
    WriteToTempFile,
    ZipError(zip::result::ZipError),
}

// Write a function to download terraform binary from the terraform website, unzip it, and write it out
pub async fn download_bin_in_zip(
    temp_path: &Path,
    zip_url: &String,
    output_path: &PathBuf,
    filenameinzip: &str,
    expected_zip_file_sha256: &str,
    expected_file_sha256: &str,
    multi_progress: Option<&MultiProgress>,
) -> error_stack::Result<(), ErrorStack> {
    let mut hasher = Sha256::new();

    let zip_base_name = zip_url.split('/').next_back().unwrap();
    let save_path = temp_path.join(zip_base_name);

    match multi_progress {
        Some(mpb) => {
            mpb.println(format!("Downloading from {}", &zip_url))
                .change_context(ErrorStack::PrintMPBMessage)?;
        }
        None => {
            println!("Downloading from {}", &zip_url);
        }
    }

    // In a block so save_file is closed at the end
    {
        let client = reqwest::Client::new();
        let mut save_file = tokio::fs::File::create(&save_path)
            .await
            .change_context(ErrorStack::CreateTempFile)?;
        let mut response = client
            .get(zip_url)
            .send()
            .await
            .change_context(ErrorStack::GetClientResponse)?;
        let content_length = response.content_length().unwrap();
        let mut total_bytes = 0;

        let mut last_update = std::time::Instant::now();

        // only work with progress bar if a multi_progress was passed
        let progress_bar = match multi_progress {
            Some(multi_progress) => {
                let pb = multi_progress.add(ProgressBar::new(content_length));
                pb.set_style(
                    ProgressStyle::with_template(
                        "{spinner:.green} [{elapsed_precise}] [{wide_bar:.cyan/blue}] {bytes}/{total_bytes}",
                    )
                    .unwrap()
                    .progress_chars("#>-"),
                );
                Some(pb)
            }
            None => None,
        };

        loop {
            match response
                .chunk()
                .await
                .change_context(ErrorStack::StreamChunk)?
            {
                Some(bytes) => {
                    total_bytes += bytes.len();
                    if let Some(pb) = &progress_bar {
                        if last_update.elapsed().as_millis() > 100 {
                            pb.set_position(total_bytes as u64);
                            last_update = std::time::Instant::now();
                        }
                    }
                    hasher.update(&bytes);
                    save_file
                        .write_all(&bytes)
                        .await
                        .change_context(ErrorStack::WriteToTempFile)?;
                }
                None => {
                    if let Some(pb) = &progress_bar {
                        pb.finish_and_clear();
                    }
                    break;
                }
            }
        }
    }

    if let Some(mpb) = multi_progress {
        mpb.println(format!("Unzipping to {}", &output_path.display()))
            .change_context(ErrorStack::PrintMPBMessage)?;
    }

    let zip_file_sha256 = format!("{:x}", hasher.finalize());

    if expected_zip_file_sha256 != zip_file_sha256 {
        error_stack::bail!(ErrorStack::Sha256Mismatch(format!(
            "Expected: {expected_zip_file_sha256}\n\nActual: {zip_file_sha256}"
        )));
    }

    if zip_base_name.ends_with(".zip") {
        let reader = File::open(&save_path).change_context(ErrorStack::OpenZIPFile)?;
        let mut archive =
            ZipArchive::new(reader).change_context(ErrorStack::CreateNewZipArchive)?;

        let mut file = archive
            .by_name(filenameinzip)
            .change_context(ErrorStack::SearchArchiveFor(filenameinzip.to_string()))?;
        let mut output = File::create(output_path).change_context(ErrorStack::CreateOutputFile)?;
        std::io::copy(&mut file, &mut output).change_context(ErrorStack::CopyFileToOutput)?;
    } else if zip_base_name.ends_with(".tar.gz") || zip_base_name.ends_with(".tgz") {
        let tar_gz = File::open(&save_path).change_context(ErrorStack::OpenTarGZFile)?;
        let tar = flate2::read::GzDecoder::new(tar_gz);
        let mut archive = tar::Archive::new(tar);

        for file in archive
            .entries()
            .change_context(ErrorStack::GetArchiveEntries)?
        {
            let mut file = file.change_context(ErrorStack::ArchiveEntryIsNotOk)?;
            if file
                .path()
                .change_context(ErrorStack::GetFilePath)?
                .to_str()
                == Some(filenameinzip)
            {
                let mut output_file =
                    File::create(output_path).change_context(ErrorStack::CreateOutputFile)?;
                std::io::copy(&mut file, &mut output_file)
                    .change_context(ErrorStack::CopyFileToOutput)?;
                break;
            }
        }
    } else {
        error_stack::bail!(ErrorStack::UnsupportedArchiveFormat(
            zip_base_name.to_string()
        ));
    }

    // Set the permissions on the file
    let mut perms = std::fs::metadata(output_path)
        .change_context(ErrorStack::GetMetadata)?
        .permissions();
    perms.set_mode(0o755);
    std::fs::set_permissions(output_path, perms).change_context(ErrorStack::SetPermissions)?;

    let file_sha256 = crate::compute_sha256_file_path(output_path).unwrap();

    if expected_file_sha256 != file_sha256 {
        // remove output path
        std::fs::remove_file(output_path).change_context(ErrorStack::RemoveTempFile)?;

        error_stack::bail!(ErrorStack::Sha256Mismatch(format!(
            "Expected: {expected_file_sha256}\n\nActual: {file_sha256}"
        )));
    }

    Ok(())
}

pub async fn download_bin(
    url: &String,
    output_path: &PathBuf,
    expected_file_sha256: &str,
    multi_progress: Option<&MultiProgress>,
) -> error_stack::Result<(), ErrorStack> {
    let mut hasher = Sha256::new();

    match multi_progress {
        Some(mpb) => {
            mpb.println(format!("Downloading from {}", &url))
                .change_context(ErrorStack::PrintMPBMessage)?;
        }
        None => {
            println!("Downloading from {}", &url);
        }
    }

    let client = reqwest::Client::new();
    let mut response = client
        .get(url)
        .send()
        .await
        .change_context(ErrorStack::GetClientResponse)?;
    let content_length = response.content_length().unwrap();
    let mut total_bytes = 0;

    let mut last_update = std::time::Instant::now();

    let mut save_file = tokio::fs::File::create(&output_path)
        .await
        .change_context(ErrorStack::CreateTempFile)?;

    // only work with progress bar if a multi_progress was passed
    let progress_bar = match multi_progress {
        Some(multi_progress) => {
            let pb = multi_progress.add(ProgressBar::new(content_length));
            pb.set_style(
                ProgressStyle::with_template(
                    "{spinner:.green} [{elapsed_precise}] [{wide_bar:.cyan/blue}] {bytes}/{total_bytes}",
                )
                .unwrap()
                .progress_chars("#>-"),
            );
            Some(pb)
        }
        None => None,
    };

    loop {
        match response
            .chunk()
            .await
            .change_context(ErrorStack::StreamChunk)?
        {
            Some(bytes) => {
                total_bytes += bytes.len();
                if let Some(pb) = &progress_bar {
                    if last_update.elapsed().as_millis() > 100 {
                        pb.set_position(total_bytes as u64);
                        last_update = std::time::Instant::now();
                    }
                }
                hasher.update(&bytes);
                save_file
                    .write_all(&bytes)
                    .await
                    .change_context(ErrorStack::WriteToTempFile)?;
            }
            None => {
                if let Some(pb) = &progress_bar {
                    pb.finish_and_clear();
                }
                break;
            }
        }
    }

    let file_sha256 = format!("{:x}", hasher.finalize());

    if expected_file_sha256 != file_sha256 {
        // remove output path
        std::fs::remove_file(output_path).change_context(ErrorStack::RemoveTempFile)?;
        error_stack::bail!(ErrorStack::Sha256Mismatch(format!(
            "Expected: {expected_file_sha256}\n\nActual: {file_sha256}"
        )));
    }

    // Set the permissions on the file
    let mut perms = std::fs::metadata(output_path)
        .change_context(ErrorStack::GetMetadata)?
        .permissions();
    perms.set_mode(0o755);
    std::fs::set_permissions(output_path, perms).change_context(ErrorStack::SetPermissions)?;

    Ok(())
}

pub async fn download_tarball(
    tar_url: &String,
    temp_path: &Path,
    multi_progress: Option<&MultiProgress>,
) -> error_stack::Result<PathBuf, ErrorStack> {
    match multi_progress {
        Some(mpb) => {
            mpb.println(format!("Downloading from {}", &tar_url))
                .change_context(ErrorStack::PrintMPBMessage)?;
        }
        None => {
            println!("Downloading from {}", &tar_url);
        }
    }

    let tar_base_name = tar_url.split('/').next_back().unwrap();
    let tmp_tar_path = temp_path.join(tar_base_name);

    let client = reqwest::Client::new();
    let mut response = client
        .get(tar_url)
        .send()
        .await
        .change_context(ErrorStack::GetClientResponse)?;
    let content_length = response.content_length().unwrap();
    let mut total_bytes = 0;

    let mut last_update = std::time::Instant::now();

    let mut save_file = tokio::fs::File::create(&tmp_tar_path)
        .await
        .change_context(ErrorStack::CreateTempFile)?;

    // only work with progress bar if a multi_progress was passed
    let progress_bar = match multi_progress {
        Some(multi_progress) => {
            let pb = multi_progress.add(ProgressBar::new(content_length));
            pb.set_style(
                ProgressStyle::with_template(
                    "{spinner:.green} [{elapsed_precise}] [{wide_bar:.cyan/blue}] {bytes}/{total_bytes}",
                )
                .unwrap()
                .progress_chars("#>-"),
            );
            Some(pb)
        }
        None => None,
    };

    loop {
        match response
            .chunk()
            .await
            .change_context(ErrorStack::StreamChunk)?
        {
            Some(bytes) => {
                total_bytes += bytes.len();
                if let Some(pb) = &progress_bar {
                    if last_update.elapsed().as_millis() > 100 {
                        pb.set_position(total_bytes as u64);
                        last_update = std::time::Instant::now();
                    }
                }
                save_file
                    .write_all(&bytes)
                    .await
                    .change_context(ErrorStack::WriteToTempFile)?;
            }
            None => {
                if let Some(pb) = &progress_bar {
                    pb.finish_and_clear();
                }
                break;
            }
        }
    }

    Ok(tmp_tar_path)
}

pub fn untar_and_move_gocryptfs(
    temp_path: &Path,
    tmp_tar_path: &PathBuf,
    output_path: &PathBuf,
) -> error_stack::Result<(), ErrorStack> {
    // Extract the tarball
    run_untar_command(temp_path, tmp_tar_path, vec!["gocryptfs"])
        .change_context(ErrorStack::UntarGocryptfs)?;

    // Move actual binary into /bin
    run_move_command(temp_path, output_path, vec!["gocryptfs"])
        .change_context(ErrorStack::MoveGocryptfs)?;

    // Set the permissions
    let mut perms = std::fs::metadata(output_path)
        .change_context(ErrorStack::GetMetadata)?
        .permissions();
    perms.set_mode(0o755);
    std::fs::set_permissions(output_path, perms).change_context(ErrorStack::SetPermissions)?;

    Ok(())
}

pub fn untar_and_move_nebula(
    temp_path: &Path,
    tmp_tar_path: &PathBuf,
    output_path: &PathBuf,
) -> error_stack::Result<(), ErrorStack> {
    // Extract the tarball
    run_untar_command(temp_path, tmp_tar_path, vec!["nebula", "nebula-cert"])
        .change_context(ErrorStack::UntarNebula)?;

    // Move actual binary into /bin
    run_move_command(temp_path, output_path, vec!["nebula", "nebula-cert"])
        .change_context(ErrorStack::MoveNebulaAndNebulaCert)?;

    // Set the permissions on nebula and nebula-certs
    let mut nebula_perms = std::fs::metadata(output_path)
        .change_context(ErrorStack::GetMetadata)?
        .permissions();
    nebula_perms.set_mode(0o755);
    std::fs::set_permissions(output_path, nebula_perms)
        .change_context(ErrorStack::SetPermissions)?;

    Ok(())
}

/// Runs `tar` to extract specific files (ignores unspecified files) from a tarball
fn run_untar_command(
    temp_path: &Path,
    tmp_tar_path: &PathBuf,
    files_to_extract: Vec<&str>,
) -> error_stack::Result<(), ErrorStack> {
    // Extract the tarball
    let mut untar_cmd = std::process::Command::new("tar");
    untar_cmd
        // -xzf: extract, gzip, file
        .arg("-xzf")
        .arg(tmp_tar_path)
        // Extract to the /tmp directory
        .arg("-C")
        .arg(temp_path.join(""));

    // Specify arguments for untarring
    for file in files_to_extract {
        untar_cmd.arg(file);
    }

    let output = untar_cmd
        .output()
        .change_context(ErrorStack::RunUntarCommand)?;

    if !output.status.success() {
        error_stack::bail!(ErrorStack::UntarUnsuccessful);
    }

    Ok(())
}

fn run_move_command(
    original_path: &Path,
    output_path: &Path,
    files_to_move: Vec<&str>,
) -> error_stack::Result<(), ErrorStack> {
    let mut mv_cmd = std::process::Command::new("mv");
    for file in files_to_move {
        mv_cmd.arg(original_path.join(file));
    }
    mv_cmd.arg(output_path);

    let output = mv_cmd.output().change_context(ErrorStack::RunMoveCommand)?;
    if !output.status.success() {
        error_stack::bail!(ErrorStack::MoveUnsuccessful);
    }

    Ok(())
}

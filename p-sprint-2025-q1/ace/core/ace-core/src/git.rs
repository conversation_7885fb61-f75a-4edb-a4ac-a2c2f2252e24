use error_stack::ResultExt;
use git2::{Signature, StatusOptions};
use std::io::Write;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AddFilesToIndex,
    Commit,
    CreateFile,
    DecodeGitLog,
    FindCommit,
    FindTree,
    GetIndex,
    GetStatuses,
    PrepareSignature,
    WriteFile,
    WriteIndex,
    WriteTree,
}

pub async fn autocommit(
    app: &crate::Application,
    message: &String,
) -> error_stack::Result<(), ErrorStack> {
    let mut status_options = StatusOptions::new();
    status_options.include_untracked(true);
    status_options.recurse_untracked_dirs(true);
    //status_options.pathspec("");

    let repo_lock = app.repo.repo_mutex.lock().await;

    let statuses = repo_lock
        .statuses(Some(&mut status_options))
        .change_context(ErrorStack::GetStatuses)?;
    let mut i = 0;

    println!("#");
    println!("# -- Files --");
    for status in statuses.iter() {
        i += 1;
        println!("# {:?} {:?}", status.path(), status.status());
    }
    if i == 0 {
        println!("# Empty Commit");
    }
    println!("#");

    let message = format!("{}{}", if i == 0 { "(empty) " } else { "" }, message);

    let mut index = repo_lock.index().change_context(ErrorStack::GetIndex)?;
    index
        .add_all(vec![""], git2::IndexAddOption::CHECK_PATHSPEC, None)
        .change_context(ErrorStack::AddFilesToIndex)?;
    index.write().change_context(ErrorStack::WriteIndex)?;

    // Create tree
    let oid = index.write_tree().change_context(ErrorStack::WriteTree)?;
    let tree = repo_lock
        .find_tree(oid)
        .change_context(ErrorStack::FindTree)?;

    // Prepare a signature
    let signature = Signature::now("System Admin", "sysadmin@localhost")
        .change_context(ErrorStack::PrepareSignature)?;

    // Get HEAD as the parent commit
    let parent = match repo_lock.head() {
        Ok(head) => Some(
            repo_lock
                .find_commit(head.target().unwrap())
                .change_context(ErrorStack::FindCommit)?,
        ),
        Err(_e) => None,
    };

    // TODO: figure out how to have 1 variable for parents
    // Commit the changes
    if let Some(parent) = parent {
        repo_lock
            .commit(
                Some("HEAD"),
                &signature,
                &signature,
                message.as_str(),
                &tree,
                &[&parent],
            )
            .change_context(ErrorStack::Commit)?;
    } else {
        repo_lock
            .commit(
                Some("HEAD"),
                &signature,
                &signature,
                message.as_str(),
                &tree,
                &[],
            )
            .change_context(ErrorStack::Commit)?;
    }

    // Print the commit information out
    //let obj = repo_lock.head()?.peel_to_commit()?;
    //println!("commit: {}", obj.id());
    //println!("author: {}", obj.author());
    //println!("committer: {}", obj.committer());
    //println!("summary: {}", obj.summary().unwrap_or(""));
    println!();

    Ok(())
}

pub fn version_info() -> String {
    include_str!("../../../../target/version").to_string()
}

pub fn write_version_file(env: &crate::Application) -> error_stack::Result<(), ErrorStack> {
    let mut file = std::fs::File::create(&env.data_version_file_path)
        .change_context(ErrorStack::CreateFile)?;
    file.write_all(version_info().as_bytes())
        .change_context(ErrorStack::WriteFile)?;
    Ok(())
}

use aws_sdk_ec2::types::Tag;
use std::error::Error;

pub struct NatGateway {
    pub id: String,
    pub vpc_id: String,
    pub subnet_id: String,
    pub name: Option<String>,
    pub source: Option<String>,
    pub account_key: Option<String>,
}

pub type NatGateways = Vec<NatGateway>;

fn find_tag(tags: Option<&Vec<Tag>>, tag_name: &str) -> Option<String> {
    tags.and_then(|tags| {
        tags.iter()
            .find(|tag| tag.key.as_deref() == Some(tag_name))
            .and_then(|tag| tag.value.clone()) // clone the value here
    })
}

pub async fn describe_natgateways(
    ec2_client: &aws_sdk_ec2::Client,
    nat_gateways: &mut NatGateways,
) -> Result<(), Box<dyn Error>> {
    eprint!("Requesting NAT Gateways... ");

    let request = ec2_client.describe_nat_gateways();
    let resp = request.send().await?;
    eprintln!("Done.");

    // Iterate over all of the NAT Gateways, getting the `Source` tag for each
    for nat in resp.nat_gateways.unwrap_or_default() {
        // Find source_tag as an <Option<String>>
        let source_tag = find_tag(nat.tags.as_ref(), "Source");
        let account_key_tag = find_tag(nat.tags.as_ref(), "AccountKey");
        let name_tag = find_tag(nat.tags.as_ref(), "Name");

        let nat_struct = NatGateway {
            id: nat.nat_gateway_id.to_owned().unwrap_or_default(),
            vpc_id: nat.vpc_id.to_owned().unwrap_or_default(),
            subnet_id: nat.subnet_id.to_owned().unwrap_or_default(),
            name: name_tag,
            source: source_tag,
            account_key: account_key_tag,
        };

        nat_gateways.push(nat_struct);
    }

    Ok(())
}

pub async fn cleanup_natgateways(
    ec2_client: &aws_sdk_ec2::Client,
    nat_gateways: &mut NatGateways,
) -> Result<(), Box<dyn Error>> {
    println!("Cleaning up NAT Gateways...");

    for nat in nat_gateways {
        // If source has a value, then skip deleting it
        if nat.source.is_some() {
            continue;
        }

        println!(
            "Deleting NAT Gateway ID: {:?}, VPC ID: {:?}, Subnet ID: {:?}, Source: {:?}",
            nat.id, nat.vpc_id, nat.subnet_id, nat.source
        );

        let delete_request = ec2_client
            .delete_nat_gateway()
            .nat_gateway_id(nat.id.clone());
        delete_request.send().await?;
    }

    Ok(())
}

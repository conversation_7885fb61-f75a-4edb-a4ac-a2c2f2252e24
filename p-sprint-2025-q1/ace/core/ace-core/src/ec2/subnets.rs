use aws_sdk_ec2::types::Tag;
use std::error::Error;

pub struct Subnet {
    pub id: String,
    pub cidr_block: String,
    pub vpc_id: String,
    pub name: Option<String>,
    pub source: Option<String>,
    pub account_key: Option<String>,
}

pub type Subnets = Vec<Subnet>;

fn find_tag(tags: Option<&Vec<Tag>>, tag_name: &str) -> Option<String> {
    tags.and_then(|tags| {
        tags.iter()
            .find(|tag| tag.key.as_deref() == Some(tag_name))
            .and_then(|tag| tag.value.clone()) // clone the value here
    })
}

pub async fn describe_subnets(
    ec2_client: &aws_sdk_ec2::Client,
    subnets: &mut Subnets,
) -> Result<(), Box<dyn Error>> {
    eprint!("Requesting subnets... ");
    let request = ec2_client.describe_subnets();
    let resp = request.send().await?;
    eprintln!("Done.");

    // Iterate over all of the subnets
    for subnet in resp.subnets.unwrap_or_default() {
        let id = subnet.subnet_id.as_deref().unwrap_or_default();
        let cidr_block = subnet.cidr_block.as_deref().unwrap_or_default();
        let name_tag = find_tag(subnet.tags.as_ref(), "Name");
        let source_tag = find_tag(subnet.tags.as_ref(), "Source");
        let account_key_tag = find_tag(subnet.tags.as_ref(), "AccountKey");

        let subnet = Subnet {
            id: id.to_owned(),
            cidr_block: cidr_block.to_owned(),
            vpc_id: subnet.vpc_id.to_owned().unwrap_or_default(),
            name: name_tag.to_owned(),
            source: source_tag.to_owned(),
            account_key: account_key_tag.to_owned(),
        };

        subnets.push(subnet);
    }

    Ok(())
}

pub async fn cleanup_subnets(
    ec2_client: &aws_sdk_ec2::Client,
    subnets: &mut Subnets,
) -> Result<(), Box<dyn Error>> {
    println!("Deleting subnets...");

    for subnet in subnets {
        // If source has a value, then skip deleting it
        if subnet.source.is_some() {
            continue;
        }

        println!("Deleting subnet {:?}, name {:?}", subnet.id, subnet.name);

        let request = ec2_client.delete_subnet().subnet_id(subnet.id.clone());
        request.send().await?;
    }

    Ok(())
}

use aws_sdk_ec2::types::Tag;
use std::error::Error;

pub struct InternetGateway {
    pub id: String,
    pub vpc_id: String,
    pub name: Option<String>,
    pub source: Option<String>,
    pub account_key: Option<String>,
}

pub type InternetGateways = Vec<InternetGateway>;

fn find_tag(tags: Option<&Vec<Tag>>, tag_name: &str) -> Option<String> {
    tags.and_then(|tags| {
        tags.iter()
            .find(|tag| tag.key.as_deref() == Some(tag_name))
            .and_then(|tag| tag.value.clone()) // clone the value here
    })
}

pub async fn describe_internetgateways(
    ec2_client: &aws_sdk_ec2::Client,
    internet_gateways: &mut InternetGateways,
) -> Result<(), Box<dyn Error>> {
    eprint!("Requesting Internet Gateways... ");
    let request = ec2_client.describe_internet_gateways();
    let resp = request.send().await?;
    eprintln!("Done.");

    // Iterate over all of the Internet Gateways, getting the `Source` tag for each
    for igw in resp.internet_gateways.unwrap_or_default() {
        // Find source_tag as an <Option<String>>
        let source_tag = find_tag(igw.tags.as_ref(), "Source");
        let account_key_tag = find_tag(igw.tags.as_ref(), "AccountKey");
        let name_tag = find_tag(igw.tags.as_ref(), "Name");

        let igw_struct = InternetGateway {
            id: igw.internet_gateway_id.to_owned().unwrap_or_default(),
            vpc_id: igw
                .attachments
                .unwrap_or_default()
                .first()
                .and_then(|a| a.vpc_id.clone())
                .unwrap_or_default(),
            name: name_tag.to_owned(),
            source: source_tag.to_owned(),
            account_key: account_key_tag.to_owned(),
        };

        internet_gateways.push(igw_struct);
    }

    Ok(())
}

pub async fn cleanup_internetgateways(
    ec2_client: &aws_sdk_ec2::Client,
    internet_gateways: &mut InternetGateways,
) -> Result<(), Box<dyn Error>> {
    println!("Cleaning up Internet Gateways...");

    for igw in internet_gateways {
        // If source has a value, then skip deleting it
        if igw.source.is_some() {
            continue;
        }

        println!(
            "Detaching and deleting Internet Gateway ID: {:?}, VPC ID: {:?}, Source: {:?}",
            igw.id, igw.vpc_id, igw.source
        );

        let detach_request = ec2_client
            .detach_internet_gateway()
            .internet_gateway_id(igw.id.clone())
            .vpc_id(igw.vpc_id.clone());

        detach_request.send().await?;

        let delete_request = ec2_client
            .delete_internet_gateway()
            .internet_gateway_id(igw.id.clone());
        delete_request.send().await?;
    }

    Ok(())
}

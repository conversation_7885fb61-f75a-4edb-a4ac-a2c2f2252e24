use aws_sdk_ec2::types::Tag;
use std::error::Error;

pub struct KeyPair {
    pub name: String,
    pub fingerprint: String,
    pub source: Option<String>,
    pub account_key: Option<String>,
}

pub type KeyPairs = Vec<KeyPair>;

fn find_tag(tags: Option<&Vec<Tag>>, tag_name: &str) -> Option<String> {
    tags.and_then(|tags| {
        tags.iter()
            .find(|tag| tag.key.as_deref() == Some(tag_name))
            .and_then(|tag| tag.value.clone()) // clone the value here
    })
}

pub async fn describe_keypairs(
    ec2_client: &aws_sdk_ec2::Client,
    keys: &mut KeyPairs,
) -> Result<(), Box<dyn Error>> {
    let request = ec2_client.describe_key_pairs();
    eprint!("Requesting keypairs... ");
    let resp = request.send().await?;
    eprintln!("Done.");

    // Iterate over all of the key pairs
    for keypair in resp.key_pairs.unwrap_or_default() {
        let name = keypair.key_name.as_deref().unwrap_or_default();
        let source_tag = find_tag(keypair.tags.as_ref(), "Source");
        let account_key_tag = find_tag(keypair.tags.as_ref(), "AccountKey");

        let keypair = KeyPair {
            name: name.to_owned(),
            fingerprint: keypair.key_fingerprint.to_owned().unwrap_or_default(),
            source: source_tag.to_owned(),
            account_key: account_key_tag.to_owned(),
        };

        keys.push(keypair);
    }

    Ok(())
}

pub async fn cleanup_keypairs(
    ec2_client: &aws_sdk_ec2::Client,
    keys: &mut KeyPairs,
) -> Result<(), Box<dyn Error>> {
    println!("Deleting keypairs...");

    for key in keys {
        // If source has a value, then skip deleting it
        if key.source.is_some() {
            continue;
        }

        println!(
            "Deleting keypair {:?}, Fingerprint: {:?}",
            key.name, key.fingerprint
        );

        let request = ec2_client.delete_key_pair().key_name(key.name.clone());
        request.send().await?;
    }

    Ok(())
}

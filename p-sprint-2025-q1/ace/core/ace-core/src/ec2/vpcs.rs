use aws_sdk_ec2::types::Tag;
use std::error::Error;

pub struct Vpc {
    pub id: String,
    pub cidr_block: String,
    pub name: Option<String>,
    pub source: Option<String>,
    pub account_key: Option<String>,
}

pub type Vpcs = Vec<Vpc>;

fn find_tag(tags: Option<&Vec<Tag>>, tag_name: &str) -> Option<String> {
    tags.and_then(|tags| {
        tags.iter()
            .find(|tag| tag.key.as_deref() == Some(tag_name))
            .and_then(|tag| tag.value.clone()) // clone the value here
    })
}

pub async fn describe_vpcs(
    ec2_client: &aws_sdk_ec2::Client,
    vpcs: &mut Vpcs,
) -> Result<(), Box<dyn Error>> {
    eprint!("Requesting VPCs... ");
    let request = ec2_client.describe_vpcs();
    let resp = request.send().await?;
    eprintln!("Done.");

    // Iterate over all of the VPCs, getting the `Source` tag for each
    for vpc in resp.vpcs.unwrap_or_default() {
        // Find source_tag as an <Option<String>>
        let name_tag = find_tag(vpc.tags.as_ref(), "Name");
        let source_tag = find_tag(vpc.tags.as_ref(), "Source");
        let account_key_tag = find_tag(vpc.tags.as_ref(), "AccountKey");

        let vpc_struct = Vpc {
            id: vpc.vpc_id.to_owned().unwrap_or_default(),
            cidr_block: vpc.cidr_block.to_owned().unwrap_or_default(),
            name: name_tag.to_owned(),
            source: source_tag.to_owned(),
            account_key: account_key_tag.to_owned(),
        };

        vpcs.push(vpc_struct);
    }

    Ok(())
}

pub async fn cleanup_vpcs(
    ec2_client: &aws_sdk_ec2::Client,
    vpcs: &mut Vpcs,
) -> Result<(), Box<dyn Error>> {
    println!("Cleaning up VPCs...");

    for vpc in vpcs {
        // If source has a value, then skip deleting it
        if vpc.source.is_some() {
            continue;
        }

        println!("Deleting VPC ID: {:?}, Source: {:?}", vpc.id, vpc.source);

        let request = ec2_client.delete_vpc().vpc_id(vpc.id.clone());
        request.send().await?;
    }

    Ok(())
}

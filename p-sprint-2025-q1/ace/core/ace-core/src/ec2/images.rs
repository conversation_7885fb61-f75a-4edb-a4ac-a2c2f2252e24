use aws_sdk_ec2::types::Tag;
use std::error::Error;

#[derive(Debug)]
pub struct Image {
    pub id: String,
    pub name: String,
    pub source: Option<String>,
    pub account_key: Option<String>,
}

pub type Images = Vec<Image>;

fn find_tag(tags: Option<&Vec<Tag>>, tag_name: &str) -> Option<String> {
    tags.and_then(|tags| {
        tags.iter()
            .find(|tag| tag.key.as_deref() == Some(tag_name))
            .and_then(|tag| tag.value.clone()) // clone the value here
    })
}

pub async fn describe_images(
    ec2_client: &aws_sdk_ec2::Client,
    images: &mut Images,
) -> Result<(), Box<dyn Error>> {
    // Print requesting images to stderr
    eprint!("Requesting images... ");
    let request = ec2_client
        .describe_images()
        .set_owners(Some(vec!["self".to_owned()]));
    let resp = request.send().await?;
    eprintln!("Done.");

    // Iterate over all of the AMIs owned by this, getting the `Source` tag for each
    for image in resp.images.unwrap_or_default() {
        // Find source_tag as an <Option<String>>
        let source_tag = find_tag(image.tags.as_ref(), "Source");
        let account_key_tag = find_tag(image.tags.as_ref(), "AccountKey");

        let name = image.name.as_deref().unwrap_or_default();

        let image_struct = Image {
            id: image.image_id.to_owned().unwrap_or_default(), //TODO
            name: name.to_owned(),
            source: source_tag.to_owned(),
            account_key: account_key_tag.to_owned(),
        };

        images.push(image_struct);
    }

    Ok(())
}

pub async fn cleanup_images(
    ec2_client: &aws_sdk_ec2::Client,
    images: &mut Images,
) -> Result<(), Box<dyn Error>> {
    println!("Cleanup extra images...");

    for image in images {
        // If source has a value, then skip deleting it
        if image.source.is_some() {
            continue;
        }

        println!(
            "Deregistering image {:?}, Source={:?}, Name={:?}",
            image.id, image.source, image.name
        );

        let request = ec2_client.deregister_image().image_id(image.id.clone());
        request.send().await?;
    }

    Ok(())
}

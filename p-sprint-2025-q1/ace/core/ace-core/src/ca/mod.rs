use error_stack::ResultExt;
use tokio::process::Command;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AutoCommit,
    CaDirAlreadyExists(String),
    CheckSecureStatus,
    CommandOutput(std::io::Error),
    CommandStatus(std::io::Error),
    CreateCaDir,
    CreateCaPkiPrivateSymlink,
    CreateCaPublicKey,
    CreateDestinationDir,
    CreateSecureCaPkiPrivate,
    DeleteCaPkiPrivate,
    EndTagNotFound,
    ExitStatus(std::process::ExitStatus),
    GetConfig,
    GetCrl,
    InlineTlsFileDoesNotExist(std::path::PathBuf),
    MoveCaPkiPrivate,
    MoveFile,
    NoVpnConfigFound,
    ReadCaPkiPrivate,
    ReadInlineTlsAuthFile,
    ReadNextEntry,
    ReadServerConfigFile,
    ReadSourceDirectoryEntries,
    RenameTempServerConfigFile,
    SecureCaPkiPrivateAlreadyExists(String),
    SecureNotMounted,
    StartTagNotFound,
    UpdateCrlInConfig,
    ValidateCommandStatus(String),
    WriteToTempServerConfigFile,
}

pub async fn init(app: &crate::Application) -> error_stack::Result<(), ErrorStack> {
    let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    // We don't need config.vpn to exist to know these values
    let ace2_server_name = config.ace2_server_name;

    // Check that /secure is mounted/exists HERE, so if it's not, we can bail early
    if !app
        .is_secure_mounted()
        .change_context(ErrorStack::CheckSecureStatus)?
    {
        eprintln!("Error: You must mount the secure filesystem before running this command");
        eprintln!("Run `ace secure mount` to mount the secure filesystem");
        error_stack::bail!(ErrorStack::SecureNotMounted)
    }

    // if the ca dir exists, bail
    if app.ca_path.exists() {
        error_stack::bail!(ErrorStack::CaDirAlreadyExists(format!(
            "Manually remove this directory if you want to start the ca from scratch: {}",
            &app.ca_path.display()
        )))
    }

    // If /secure/ca/pki/private already exists, bail
    // (don't want private information left out in the open from a failed init)
    let ca_pki_private = app.ca_path.join("pki/private");
    let secure_ca_pki_private = app.secure_mountpoint.join("ca/pki/private");

    if secure_ca_pki_private.exists() {
        error_stack::bail!(ErrorStack::SecureCaPkiPrivateAlreadyExists(format!(
            "Manually remove this directory if you want to start the ca from scratch: {}",
            &secure_ca_pki_private.display()
        )))
    }

    // Make the ca dir.  This should fail if it exists.
    {
        let mut cmd = Command::new("make-cadir");
        cmd.arg(&app.ca_path);
        validate_status(cmd.status().await)
            .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
    }

    // Make the ca/config dir using tokio::fs
    {
        tokio::fs::create_dir_all(app.ca_path.join("configs"))
            .await
            .change_context(ErrorStack::CreateCaDir)?;
    }

    // In batch mode, init the pki
    {
        let mut cmd = Command::new(crate::EASYRSA_BIN_PATH);
        cmd.current_dir(&app.ca_path);
        cmd.env("EASYRSA_BATCH", "1");
        cmd.env("EASYRSA_SILENT", "1");
        cmd.arg("init-pki");

        validate_status(cmd.status().await)
            .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
    }

    // In batch mode, build the ca
    {
        let mut cmd = Command::new(crate::EASYRSA_BIN_PATH);
        cmd.current_dir(&app.ca_path);
        cmd.arg("build-ca");
        cmd.arg("nopass");
        cmd.env("EASYRSA_BATCH", "1");
        cmd.env("EASYRSA_SILENT", "1");
        cmd.env("EASYRSA_REQ_CN", ace2_server_name);

        validate_status(cmd.status().await)
            .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
    }

    // Init the tls
    {
        let mut cmd = Command::new(&app.easytls_bin_path);
        cmd.current_dir(&app.ca_path);
        cmd.arg("init-tls");

        validate_status(cmd.status().await)
            .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
    }

    // Create the tls auth
    {
        let mut cmd = Command::new(&app.easytls_bin_path);
        cmd.current_dir(&app.ca_path);
        cmd.arg("build-tls-auth");

        validate_status(cmd.status().await)
            .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
    }

    // Create the tls crypt
    {
        let mut cmd = Command::new(&app.easytls_bin_path);
        cmd.current_dir(&app.ca_path);
        cmd.arg("build-tls-crypt");

        validate_status(cmd.status().await)
            .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
    }

    // build the crl
    {
        let mut cmd = Command::new(crate::EASYRSA_BIN_PATH);
        cmd.current_dir(&app.ca_path);
        cmd.arg("gen-crl");
        cmd.env("EASYRSA_BATCH", "1");
        cmd.env("EASYRSA_SILENT", "1");
        cmd.env("EASYRSA_CRL_DAYS", "365");

        validate_status(cmd.status().await)
            .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
    }

    // Create ca public key (DO THIS BEFORE MOVING THE CA PKI PRIVATE DIRECTORY)
    create_ca_public_key(app).await?;

    // Create /secure/ca/pki/private directory and move the ca/pki/private directory to it
    std::fs::create_dir_all(&secure_ca_pki_private)
        .change_context(ErrorStack::CreateDestinationDir)?;

    fs_more::directory::move_directory(
        &ca_pki_private,
        &secure_ca_pki_private,
        fs_more::directory::DirectoryMoveOptions::default(),
    )
    .change_context(ErrorStack::MoveCaPkiPrivate)?;

    std::os::unix::fs::symlink("../../secure/ca/pki/private", &ca_pki_private)
        .change_context(ErrorStack::CreateCaPkiPrivateSymlink)?;
    Ok(())
}

/// Extracts the public key from ca/pki/ca.crt and writes it to ca/pki/ca-public-key.pub in OpenSSH's format.
pub async fn create_ca_public_key(app: &crate::Application) -> error_stack::Result<(), ErrorStack> {
    let ca_pki_path = app.ca_path.join("pki");
    let ca_cert_path = ca_pki_path.join("ca.crt");
    let ca_public_key_path = ca_pki_path.join("ca-public-key.pem");
    let ca_public_pkcs8_path = ca_pki_path.join("ca-public-key.pub");

    // Extract ONLY the public key from the ca.crt
    let mut openssl_cmd = Command::new("openssl");
    openssl_cmd.arg("x509");
    openssl_cmd.arg("-in");
    openssl_cmd.arg(&ca_cert_path);
    openssl_cmd.arg("-pubkey");
    openssl_cmd.arg("--noout");

    // Write the output to ca_public_key_path
    let openssl_stdout_content = capture_output(openssl_cmd).await?;
    tokio::fs::write(&ca_public_key_path, openssl_stdout_content)
        .await
        .change_context(ErrorStack::CreateSecureCaPkiPrivate)?;

    // Convert extracted public key to PKCS8 format
    let mut ssh_keygen_cmd = Command::new("ssh-keygen");
    ssh_keygen_cmd.arg("-f");
    ssh_keygen_cmd.arg(&ca_public_key_path);
    ssh_keygen_cmd.arg("-i");
    ssh_keygen_cmd.arg("-m");
    ssh_keygen_cmd.arg("PKCS8");

    // Write the output to ca_public_pkcs8_path (if possible)
    let keygen_stdout = capture_output(ssh_keygen_cmd).await?;
    tokio::fs::write(&ca_public_pkcs8_path, keygen_stdout)
        .await
        .change_context(ErrorStack::CreateSecureCaPkiPrivate)?;

    Ok(())
}

pub async fn get_crl(app: &crate::Application) -> error_stack::Result<String, ErrorStack> {
    let crl_path = app.ca_path.join("pki/crl.pem");
    let crl = tokio::fs::read_to_string(crl_path)
        .await
        .change_context(ErrorStack::GetCrl)?;
    Ok(crl)
}

pub async fn create_client(
    app: &crate::Application,
    username: &str,
) -> error_stack::Result<String, ErrorStack> {
    let vpn = ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GetConfig)?
        .vpn;

    let Some(vpn_config) = vpn else {
        error_stack::bail!(ErrorStack::NoVpnConfigFound)
    };

    let inline_path = app.ca_path.join(format!("pki/easytls/{username}.inline"));

    // Only build the client if the inline file does not exist
    if !inline_path.exists() {
        // build the client
        {
            let mut cmd = Command::new(crate::EASYRSA_BIN_PATH);
            cmd.current_dir(&app.ca_path);
            cmd.arg("build-client-full");
            cmd.arg(username);
            cmd.arg("nopass");
            cmd.env("EASYRSA_BATCH", "1");

            validate_status(cmd.status().await)
                .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
        }

        // build tls crypt
        {
            let mut cmd = Command::new(&app.easytls_bin_path);
            cmd.current_dir(&app.ca_path);
            cmd.arg("build-tls-crypt-v2-client");
            cmd.arg(&vpn_config.public_server_name);
            cmd.arg(username);

            validate_status(cmd.status().await)
                .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
        }

        // Build the inline file
        {
            let mut cmd = Command::new(&app.easytls_bin_path);
            cmd.current_dir(&app.ca_path);
            cmd.arg("inline-tls-auth");
            cmd.arg(username);
            cmd.env("EASYTLS_BATCH", "1");

            validate_status(cmd.status().await)
                .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
        }
    }

    if !inline_path.exists() {
        error_stack::bail!(ErrorStack::InlineTlsFileDoesNotExist(inline_path))
    }

    let inline_tls_auth = tokio::fs::read_to_string(inline_path)
        .await
        .change_context(ErrorStack::ReadInlineTlsAuthFile)?;

    Ok(inline_tls_auth)
}

pub async fn revoke_client(
    app: &crate::Application,
    username: &str,
) -> error_stack::Result<(), ErrorStack> {
    // revoke the client
    {
        let mut cmd = Command::new(crate::EASYRSA_BIN_PATH);
        cmd.current_dir(&app.ca_path);
        cmd.arg("revoke");
        cmd.arg(username);
        cmd.env("EASYRSA_BATCH", "1");

        validate_status(cmd.status().await)
            .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
    }

    // update the crl
    {
        let mut cmd = Command::new(crate::EASYRSA_BIN_PATH);
        cmd.current_dir(&app.ca_path);
        cmd.arg("gen-crl");
        cmd.env("EASYRSA_BATCH", "1");
        cmd.env("EASYRSA_CRL_DAYS", "365");

        validate_status(cmd.status().await)
            .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
    }

    /*
    // remove the tlskey
    {
        let mut cmd = Command::new(&app.easytls_bin_path);
        cmd.current_dir(&app.data_ca_path);
        cmd.arg("remove-tlskey");
        cmd.arg(username);
        cmd.env("EASYTLS_BATCH", "1");

        validate_status(cmd.status().await)
            .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
    }
    */
    // remove the inline tls auth
    {
        let mut cmd = Command::new(&app.easytls_bin_path);
        cmd.current_dir(&app.ca_path);
        cmd.arg("remove-inline");
        cmd.arg(username);
        cmd.env("EASYTLS_BATCH", "1");

        validate_status(cmd.status().await)
            .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
    }

    // commit the changes
    {
        let mut cmd = Command::new(crate::EASYRSA_BIN_PATH);
        cmd.current_dir(&app.ca_path);
        cmd.arg("update-db");
        cmd.env("EASYRSA_BATCH", "1");
        cmd.env("EASYRSA_SILENT", "1");

        validate_status(cmd.status().await)
            .change_context(ErrorStack::ValidateCommandStatus(format!("{:?}", &cmd)))?;
    }

    Ok(())
}

async fn capture_output(mut cmd: Command) -> error_stack::Result<Vec<u8>, ErrorStack> {
    match cmd.output().await {
        Ok(output) => {
            if !output.status.success() {
                error_stack::bail!(ErrorStack::ExitStatus(output.status))
            }
            Ok(output.stdout)
        }
        Err(e) => {
            error_stack::bail!(ErrorStack::CommandOutput(e))
        }
    }
}

pub fn validate_status(
    status: std::io::Result<std::process::ExitStatus>,
) -> error_stack::Result<(), ErrorStack> {
    match status {
        Ok(status) => {
            if !status.success() {
                error_stack::bail!(ErrorStack::ExitStatus(status))
            }
        }
        Err(e) => {
            error_stack::bail!(ErrorStack::CommandStatus(e))
        }
    }
    Ok(())
}

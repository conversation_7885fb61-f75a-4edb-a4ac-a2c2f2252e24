use crate::GNUPG_INSTALL_CMD;
use error_stack::{Report, Result};
use garbage::{CNSL, JE};
use std::os::unix::fs::PermissionsExt;
use std::path::PathBuf;

use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AceGpgKeysFoundAreNotAPair(String, String),
    AutoCreateGpgAceKeypair,
    CheckForAce2Key,
    CheckGpgIsInstalled,
    CheckSecureGpg,
    CheckSecureMountState,
    CopyGpgPrivateKeyToSecure,
    CreateGpgDirectory,
    CreateGpgPrivateKeysDirSymlink,
    CreateGpgPrivateKeySymlink,
    CreateSecureGpgPrivateKeysDir,
    GenerateAceGpgKeyPair,
    GetConfig,
    GetGpgDirPathMetadata,
    GpgDirPathIsNotDirectory(std::path::PathBuf),
    GpgPrivateKeysIsNotDirOrSymlink,
    GpgPrivateKeysSymlinkTargetIncorrect(std::path::PathBuf, std::path::PathBuf),
    GpgRequiresSecureMounted(String),
    InitGpgDir,
    InstallGpg,
    ListGpgPublicKeys,
    ListGpgSecretKeys,
    MoveGpgPrivateKeys,
    OnlyOneHalfOfAceGpgKeyPairFound(Vec<GPGKeyRecord>, Vec<GPGKeyRecord>),
    ParseListPublicKeysOutput,
    ParseListSecretKeysOutput,
    ReadGpgDir,
    ReadGpgPrivateKeysDir,
    ReadGpgPrivateKeysEntry,
    ReadGpgPrivateKeysSymlink,
    RemoveAceGpgKeyTemplateConfig,
    RemoveGpgPrivateKeysDir,
    RemoveOriginalKeyFile,
    SetGpgDirPermissions,
    SingleRecordFoundIsNotPublicKey(String),
    SingleRecordFoundIsNotSecretKey(String),
    TooManyKeysMatchAce2GpgKeyName(Vec<GPGKeyRecord>, Vec<GPGKeyRecord>),
    UidLineContainsNoBrackets(String),
    WriteAceGpgKeyTemplateConfig(PathBuf),
}

#[derive(Debug)]
pub struct GPGKeyRecord {
    pub kind: String,
    pub fingerprint: String,
    pub uid: String,
}

// Function to check if GPG is installed systemwide
pub async fn check_gpg_is_installed() -> Result<(), ErrorStack> {
    let gpg_check = tokio::process::Command::new("gpg")
        .arg("--version")
        .output()
        .await;
    match gpg_check {
        Ok(output) if output.status.success() => {
            println!("GPG is installed and ready to use.");
            Ok(())
        }
        _ => {
            println!("GPG is not installed. Attempting to install via apt-get...");

            // Install GPG via apt-get with sudo
            let install_output = tokio::process::Command::new("sh")
                .arg("-c")
                .arg(GNUPG_INSTALL_CMD)
                .output()
                .await;

            match install_output {
                Ok(output) if output.status.success() => {
                    println!("GPG installed successfully using apt-get.");
                    Ok(())
                }
                _ => {
                    eprintln!("Failed to install GPG using apt-get.");
                    Err(Report::new(ErrorStack::InstallGpg))
                }
            }
        }
    }
}

/// Checks or sets up the gpg homedir + symlink to secure/gpg/private-keys-v1.d
/// Also initializes the ace2 gpg keypair if it does not exist.
pub async fn check_gpg_directory(
    app: &crate::Application,
    config: &ace_graph::config::Config,
) -> error_stack::Result<(), ErrorStack> {
    let gpg_dir = &app.gpg_path;
    let gpg_private_keys_v1_d = gpg_dir.join("private-keys-v1.d");
    let secure_gpg = app.secure_mountpoint.join("gpg");
    let secure_gpg_private_keys_v1_d = secure_gpg.join("private-keys-v1.d");
    let rel_symlink = std::path::Path::new("../secure/gpg/private-keys-v1.d");

    tokio::fs::create_dir_all(secure_gpg)
        .await
        .change_context(ErrorStack::CheckSecureGpg)?;
    tokio::fs::create_dir_all(&secure_gpg_private_keys_v1_d)
        .await
        .change_context(ErrorStack::CreateSecureGpgPrivateKeysDir)?;

    if !app
        .is_secure_mounted()
        .change_context(ErrorStack::CheckSecureMountState)?
    {
        error_stack::bail!(ErrorStack::GpgRequiresSecureMounted(
            "You must mount the secure directory for GPG checks or initialization to take place."
                .to_string()
        ));
    }

    match tokio::fs::symlink_metadata(gpg_dir).await {
        // A path of some kind exists.
        Ok(gpg_dir_metadata) => {
            if gpg_dir_metadata.is_symlink() | !gpg_dir_metadata.is_dir() {
                error_stack::bail!(ErrorStack::GpgDirPathIsNotDirectory(gpg_dir.to_path_buf()));
            }

            if gpg_dir_metadata.permissions().mode() != 0o700 {
                tokio::fs::set_permissions(gpg_dir, std::fs::Permissions::from_mode(0o700))
                    .await
                    .change_context(ErrorStack::SetGpgDirPermissions)?;
            }

            println!("Gpg directory found - checking structure...");

            // This will move any existing private keys to the secure directory
            // Not likely to occur, just if the user set up something prior, which is possible, or if ace init was interrupted.
            move_any_gpg_private_keys(
                &secure_gpg_private_keys_v1_d,
                &gpg_private_keys_v1_d,
                &rel_symlink.to_path_buf(),
            )
            .await
            .change_context(ErrorStack::MoveGpgPrivateKeys)?;

            if !detect_ace2_private_key(app, config)
                .await
                .change_context(ErrorStack::CheckForAce2Key)?
            {
                println!("INFO: Ace2 gpg keypair not found.  Creating...");
                create_ace_gpg_keypair(app, config)
                    .await
                    .change_context(ErrorStack::AutoCreateGpgAceKeypair)
            } else {
                println!("Ace2 gpg keypair already found.  Skipping creation...");
                Ok(())
            }
        }
        Err(e) => {
            if e.kind() == std::io::ErrorKind::NotFound {
                init_gpg_dir_and_key(
                    app,
                    &gpg_private_keys_v1_d,
                    &rel_symlink.to_path_buf(),
                    config,
                )
                .await
                .change_context(ErrorStack::InitGpgDir)
            } else {
                Err(error_stack::Report::new(e).change_context(ErrorStack::GetGpgDirPathMetadata))
            }
        }
    }
}

/// Initializes the GPG directory with a symlink for keeping private GPG keys in the secure directory.
/// - ONLY is executed if the GPG directory does NOT already exist.
async fn init_gpg_dir_and_key(
    app: &crate::Application,
    gpg_private_keys_v1_d: &PathBuf,
    rel_symlink: &PathBuf,
    config: &ace_graph::config::Config,
) -> error_stack::Result<(), ErrorStack> {
    let gpg_dir = &app.gpg_path;

    println!(
        "\nGPG directory not found, initializing GPG directory in {}",
        gpg_dir.display()
    );

    // Create the GPG directory
    tokio::fs::create_dir_all(gpg_dir)
        .await
        .change_context(ErrorStack::CreateGpgDirectory)?;
    tokio::fs::set_permissions(gpg_dir, std::fs::Permissions::from_mode(0o700))
        .await
        .change_context(ErrorStack::SetGpgDirPermissions)?;

    // Set up the symlink
    tokio::fs::symlink(rel_symlink, &gpg_private_keys_v1_d)
        .await
        .change_context(ErrorStack::CreateGpgPrivateKeySymlink)?;

    println!("Initializing ACE GPG keypair...\n");

    // Create the ace private key with NO user input
    create_ace_gpg_keypair(app, config)
        .await
        .change_context(ErrorStack::AutoCreateGpgAceKeypair)?;

    Ok(())
}

/// Moves any private keys stored in /gpg/private-keys-v1.d to the secure directory.
/// Replaces /gpg/private-keys-v1.d with a symlink to secure.
async fn move_any_gpg_private_keys(
    secure_gpg_private_keys_v1_d: &std::path::Path,
    gpg_private_keys_v1_d: &PathBuf,
    rel_symlink: &PathBuf,
) -> error_stack::Result<(), ErrorStack> {
    if let Err(e) = tokio::fs::symlink_metadata(gpg_private_keys_v1_d).await {
        if e.kind() == std::io::ErrorKind::NotFound {
            // Doesn't exist - there are no keys to be had
            // So create the symlink before there are
            tokio::fs::symlink(rel_symlink, &gpg_private_keys_v1_d)
                .await
                .change_context(ErrorStack::CreateGpgPrivateKeysDirSymlink)?;
            return Ok(());
        } else {
            return Err(
                error_stack::Report::new(e).change_context(ErrorStack::ReadGpgPrivateKeysEntry)
            );
        }
    }

    if gpg_private_keys_v1_d.is_symlink() {
        match tokio::fs::read_link(gpg_private_keys_v1_d).await {
            Ok(target) => {
                if target == *rel_symlink {
                    return Ok(());
                } else {
                    error_stack::bail!(ErrorStack::GpgPrivateKeysSymlinkTargetIncorrect(
                        gpg_private_keys_v1_d.to_path_buf(),
                        target
                    ));
                }
            }
            Err(e) => {
                return Err(error_stack::Report::new(e)
                    .change_context(ErrorStack::ReadGpgPrivateKeysSymlink));
            }
        }
    }

    if !gpg_private_keys_v1_d.is_dir() {
        error_stack::bail!(ErrorStack::GpgPrivateKeysIsNotDirOrSymlink);
    }

    println!(
        "WARNING: {} is plain directory, not symlink to secure.\n...Moving any private keys to secure directory and creating symlink.",
        gpg_private_keys_v1_d.display()
    );

    // Read all entries in the private keys directory
    let mut dir_reader = tokio::fs::read_dir(&gpg_private_keys_v1_d)
        .await
        .change_context(ErrorStack::ReadGpgPrivateKeysDir)?;

    let mut entries = vec![];

    loop {
        match dir_reader.next_entry().await {
            Ok(Some(entry)) => entries.push(entry),
            Ok(None) => break,
            Err(e) => {
                return Err(
                    error_stack::Report::new(e).change_context(ErrorStack::ReadGpgPrivateKeysEntry)
                );
            }
        }
    }

    for entry in entries {
        let new_path = secure_gpg_private_keys_v1_d.join(entry.file_name());

        tokio::fs::copy(entry.path(), new_path)
            .await
            .change_context(ErrorStack::CopyGpgPrivateKeyToSecure)?;
        tokio::fs::remove_file(entry.path())
            .await
            .change_context(ErrorStack::RemoveOriginalKeyFile)?;
    }

    // Remove the (now empty) gpg/private_keys_v1_d and replace with symlink
    tokio::fs::remove_dir(&gpg_private_keys_v1_d)
        .await
        .change_context(ErrorStack::RemoveGpgPrivateKeysDir)?;
    tokio::fs::symlink(rel_symlink, &gpg_private_keys_v1_d)
        .await
        .change_context(ErrorStack::CreateGpgPrivateKeysDirSymlink)?;

    Ok(())
}

/// Checks if the ace2 private gpg keypair exists.
pub async fn detect_ace2_private_key(
    app: &crate::Application,
    config: &ace_graph::config::Config,
) -> error_stack::Result<bool, ErrorStack> {
    let mut secret_keys_is_empty = false;
    let mut public_keys_is_empty = false;
    let ace2_key_name = &config.ace2_key_name;

    // Check for the secret key by name
    // **MAKE SURE TO USE HOMEDIR**
    let mut secret_cmd = tokio::process::Command::new("gpg");
    secret_cmd
        .arg("--homedir")
        .arg(&app.gpg_path)
        .arg("--list-secret-keys")
        .arg("--no-auto-check-trustdb")
        .arg(ace2_key_name);

    let secret_keys_output = secret_cmd
        .output()
        .await
        .change_context(ErrorStack::ListGpgSecretKeys)?;

    if !secret_keys_output.status.success() {
        let stderr_out = String::from_utf8_lossy(&secret_keys_output.stderr);

        if secret_keys_output.status.code() == Some(2)
            && stderr_out.contains(&"No secret key".to_string())
        {
            secret_keys_is_empty = true;
        } else {
            println!("{}", String::from_utf8_lossy(&secret_keys_output.stdout));
            println!("{}", String::from_utf8_lossy(&secret_keys_output.stderr));
            error_stack::bail!(ErrorStack::ListGpgSecretKeys);
        }
    }

    // Check for the public key by name
    // **MAKE SURE TO USE HOMEDIR**
    let mut public_cmd = tokio::process::Command::new("gpg");
    public_cmd
        .arg("--homedir")
        .arg(&app.gpg_path)
        .arg("--list-keys")
        .arg("--no-auto-check-trustdb")
        .arg(ace2_key_name);

    let public_keys_output = public_cmd
        .output()
        .await
        .change_context(ErrorStack::ListGpgPublicKeys)?;

    if !public_keys_output.status.success() {
        let stderr_out = String::from_utf8_lossy(&public_keys_output.stderr);
        if public_keys_output.status.code() == Some(2)
            && stderr_out.contains(&"No public key".to_string())
        {
            public_keys_is_empty = true;
        } else {
            println!("{}", String::from_utf8_lossy(&public_keys_output.stdout));
            println!("{}", String::from_utf8_lossy(&public_keys_output.stderr));
            error_stack::bail!(ErrorStack::ListGpgPublicKeys);
        }
    }

    // Just send the empty vecs to parse_list so we don't repeat matching.
    let secret_primary_keys = {
        if secret_keys_is_empty {
            vec![]
        } else {
            parse_list_keys_output(secret_keys_output)
                .await
                .change_context(ErrorStack::ParseListSecretKeysOutput)?
        }
    };

    let public_primary_keys = {
        if public_keys_is_empty {
            vec![]
        } else {
            parse_list_keys_output(public_keys_output)
                .await
                .change_context(ErrorStack::ParseListPublicKeysOutput)?
        }
    };

    // Conditions to determine if ONLY a SINGLE ace gpg keypair exists:
    // Length of secret_keys and public_keys is 1
    // The secret key is of kind "sec"
    // The public key is of kind "pub"
    // The fingerprint of the secret key matches the fingerprint of the public key
    match (secret_primary_keys.len(), public_primary_keys.len()) {
        (1, 1) => {
            if secret_primary_keys[0].kind != "sec" {
                return Err(error_stack::report!(
                    ErrorStack::SingleRecordFoundIsNotSecretKey(
                        secret_primary_keys[0].kind.clone()
                    )
                ));
            }

            if public_primary_keys[0].kind != "pub" {
                return Err(error_stack::report!(
                    ErrorStack::SingleRecordFoundIsNotPublicKey(
                        public_primary_keys[0].kind.clone()
                    )
                ));
            }

            // A very rare situation, but can happen
            if secret_primary_keys[0].fingerprint != public_primary_keys[0].fingerprint {
                return Err(error_stack::report!(
                    ErrorStack::AceGpgKeysFoundAreNotAPair(
                        secret_primary_keys[0].fingerprint.clone(),
                        public_primary_keys[0].fingerprint.clone()
                    )
                ));
            }

            Ok(true)
        }
        (0, 1) | (1, 0) => {
            println!(
                "Only half of the ace gpg keypair found!  Please fix manually with `ace gpg` passthru."
            );
            Err(error_stack::report!(
                ErrorStack::OnlyOneHalfOfAceGpgKeyPairFound(
                    secret_primary_keys,
                    public_primary_keys
                )
            ))
        }

        (0, 0) => Ok(false),

        (_, _) => Err(error_stack::report!(
            ErrorStack::TooManyKeysMatchAce2GpgKeyName(secret_primary_keys, public_primary_keys)
        )),
    }
}

/// Turns the output of `gpg --list-keys` or `gpg --list-secret-keys` into a crude list of gpg keys.
async fn parse_list_keys_output(
    output: std::process::Output,
) -> error_stack::Result<Vec<GPGKeyRecord>, ErrorStack> {
    let mut rval = vec![];

    // First separate into lines
    let output_as_string = String::from_utf8_lossy(&output.stdout);
    let lines: Vec<&str> = output_as_string.lines().collect();

    // Did gpg find anything?
    if lines.is_empty() {
        return Ok(rval);
    }

    let keys = lines
        .split(|line| line.trim().is_empty())
        .filter(|key| !key.is_empty()); // Remove empty groups

    for key in keys {
        // Parse everything here
        // First three characters are the record TYPE (secret key, public key, certificate, etc)
        // Second line (trimmed of preceding whitespace) is the actual fingerprint.
        // last line isn't necessary for now.

        let kind_line = key[0].trim();
        let finger_print_line = key[1].trim();
        let uid_line = key[2].trim();

        // Parse the uid line
        let uid = {
            if let Some(end) = uid_line.find("]") {
                uid_line[end + 1..].trim().to_string()
            } else {
                error_stack::bail!(ErrorStack::UidLineContainsNoBrackets(key[2].to_string()));
            }
        };

        let kind = kind_line.chars().take(3).collect::<String>();

        rval.push(GPGKeyRecord {
            kind: kind.to_string(),
            fingerprint: finger_print_line.to_string(),
            uid,
        })
    }

    Ok(rval)
}

/// Generates the ace2 gpg keypair via template.  Creates template, then destroys after successful keypair creation.
async fn create_ace_gpg_keypair(
    app: &crate::Application,
    config: &ace_graph::config::Config,
) -> error_stack::Result<(), ErrorStack> {
    let homedir = &app.gpg_path;
    let ace2_key_name = &config.ace2_key_name;

    // First create the key template file
    let key_template_file_contents = CNSL!(
        r"
        %no-protection
        Key-Type: RSA
        Key-Length: 4096
        Subkey-Type: RSA
        Subkey-Length: 4096
        Name-Real: ",
        JE!(ace2_key_name),
        r"
        Expire-Date: 0
        %commit
    "
    );

    let key_template_file_path = &app.temp_path.join("ace2-gpg-template.conf");

    tokio::fs::write(&key_template_file_path, &key_template_file_contents)
        .await
        .change_context(ErrorStack::WriteAceGpgKeyTemplateConfig(
            key_template_file_path.to_owned(),
        ))?;

    // **MUST USE ACE GPG HOMEDIR** (or else it will use the user's default gpg directory)
    let mut cmd = tokio::process::Command::new("gpg");
    cmd.arg("--homedir")
        .arg(homedir)
        .arg("--batch")
        .arg("--gen-key")
        .arg(key_template_file_path);

    cmd.output()
        .await
        .change_context(ErrorStack::GenerateAceGpgKeyPair)?;

    // Remove temporary key template file
    tokio::fs::remove_file(&key_template_file_path)
        .await
        .change_context(ErrorStack::RemoveAceGpgKeyTemplateConfig)?;

    Ok(())
}

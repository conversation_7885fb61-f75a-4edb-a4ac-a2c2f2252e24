use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneAwsRdsSngFound(crate::AwsRdsSng, usize),
    SelectFromAwsRds,
}

#[derive(Debug)]
pub struct AwsRdsSng {
    pub graphkey: crate::AwsRdsSng,
    pub name: String,
    pub description: Option<String>,
    pub subnets: Vec<crate::Subnet>,
    pub source: String,
}

impl crate::GraphValueExt for AwsRdsSng {}

pub async fn get(
    gk_aws_rds_subnet_group: crate::AwsRdsSng,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<AwsRdsSng, ErrorStack> {
    let filter = crate::AwsRdsSngFilter::One(gk_aws_rds_subnet_group.clone());

    let mut items = select(filter, ace_db_app).await?;
    if items.len() == 1 {
        return Ok(items.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneAwsRdsSngFound(
        gk_aws_rds_subnet_group,
        items.len()
    ));
}

pub async fn select(
    filter: crate::AwsRdsSngFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<AwsRdsSng>, ErrorStack> {
    let mut rval = Vec::new();

    for items in select_result(filter, ace_db_app).await? {
        match items {
            Ok(aws_rds_subnet_group) => rval.push(aws_rds_subnet_group),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: crate::AwsRdsSngFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<AwsRdsSng, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    rval.extend(
        crate::aws_rds::select_result_aws_rds_sng(&filter, ace_db_app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromAwsRds)?
            .into_iter()
            .map(|v| v.change_context(ErrorStack::SelectFromAwsRds)),
    );

    Ok(rval)
}

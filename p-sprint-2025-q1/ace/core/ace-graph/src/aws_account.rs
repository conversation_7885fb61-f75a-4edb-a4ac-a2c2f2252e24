use error_stack::ResultExt;
use ipnetwork::IpNetwork;
use std::collections::HashMap;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetAwsAccount(crate::AwsAccount),
    GetUserFromDb,
    NotOneFound(crate::AwsAccount),
    SelectFromDb,
}

pub type AwsAccountKey = String;
pub type AwsAccountId = String;

#[derive(Debug, Clone)]
pub struct AwsAccount {
    pub graphkey: crate::AwsAccount,
    pub account_key: String,
    pub aws_account_id: AwsAccountId,
    pub public_domain: String,
    pub private_domain: String,
    pub sysadmins: Vec<String>,
    pub region: HashMap<String, AwsAccountRegion>,
    pub source: std::path::PathBuf,
}

impl AwsAccount {
    pub async fn get_ssh_keys_text(
        &self,
        app: &ace_db::App,
    ) -> error_stack::Result<String, ErrorStack> {
        let mut rval = String::new();

        for sysadmin in &self.sysadmins {
            let user = crate::user::get(&crate::User::Db(sysadmin.to_owned()), app)
                .await
                .change_context(ErrorStack::GetUserFromDb)?;

            for ssh_key in &user.ssh_keys {
                rval.push_str(ssh_key);
                rval.push('\n');
            }
        }

        Ok(rval)
    }
}

impl crate::GraphValueExt for AwsAccount {}

#[derive(Debug, Clone)]
pub struct AwsAccountRegion {
    pub name: String,
    pub vpc_id: Option<String>,
    pub cidr_block: IpNetwork,
    pub ace2: bool,
}

impl crate::GraphValueExt for AwsAccountRegion {}

pub async fn get(
    gk_aws_account: &crate::AwsAccount,
    app: &ace_db::App,
) -> error_stack::Result<AwsAccount, ErrorStack> {
    let filter = crate::AwsAccountFilter::One(gk_aws_account.clone());
    let mut aws_accounts = select_result(&filter, app).await?;

    // Validate that one record was returned
    let aws_account: Result<AwsAccount, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if aws_accounts.len() == 1 {
            aws_accounts.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_aws_account.clone()))
        }
    };

    aws_account.change_context_lazy(|| ErrorStack::GetAwsAccount(gk_aws_account.to_owned()))
}

pub async fn select(
    filter: &crate::AwsAccountFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<AwsAccount>, ErrorStack> {
    // Fails at ANY error found (but not necessarily at "hey didn't find a match"?).
    let mut rval = Vec::new();

    let aws_accounts = select_result(filter, app).await?;

    for aws_account in aws_accounts {
        match aws_account {
            Ok(aws_account) => {
                rval.push(aws_account);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::AwsAccountFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<AwsAccount, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::AwsAccountFilter::All => ace_db::Filter::All,
        crate::AwsAccountFilter::One(crate::AwsAccount::Db(name)) => {
            ace_db::Filter::One(name.to_owned())
        }
        crate::AwsAccountFilter::None => return Ok(Vec::new()),
    };

    let mut rval = Vec::new();

    let db_aws_accounts = ace_db::aws_account::select_result(db_filter, app)
        .await
        .change_context(ErrorStack::SelectFromDb)?;

    for aws_account in db_aws_accounts {
        match aws_account {
            Ok(a) => {
                rval.push(Ok(construct(a)));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

fn construct(aws_account: ace_db::aws_account::AwsAccount) -> AwsAccount {
    let mut region_map = HashMap::new();
    for (region_name, region) in aws_account.region {
        region_map.insert(
            region_name.clone(),
            AwsAccountRegion {
                name: region_name,
                vpc_id: region.vpc_id,
                cidr_block: region.cidr_block,
                ace2: region.ace2,
            },
        );
    }

    AwsAccount {
        graphkey: crate::AwsAccount::Db(aws_account.account_key.clone()),
        account_key: aws_account.account_key,
        aws_account_id: aws_account.aws_account_id,
        public_domain: aws_account.public_domain,
        private_domain: aws_account.private_domain,
        sysadmins: aws_account.sysadmins,
        region: region_map,
        source: aws_account.source,
    }
}

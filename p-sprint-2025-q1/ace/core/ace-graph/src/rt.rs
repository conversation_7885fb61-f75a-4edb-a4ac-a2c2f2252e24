#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneRouteTableFound(crate::RouteTable, usize),
}

#[derive(Debug)]
pub struct RouteTable {
    pub graphkey: crate::RouteTable,
    pub name: String,
    pub vpc: crate::Vpc, //?
    pub route: Vec<crate::RouteTableRoute>,
    pub source: String,
}

impl crate::GraphValueExt for RouteTable {}

pub async fn get(gk_route_table: crate::RouteTable) -> error_stack::Result<RouteTable, ErrorStack> {
    let filter = crate::RouteTableFilter::One(gk_route_table.clone());

    let mut route_tables = select(filter).await?;
    if route_tables.len() == 1 {
        return Ok(route_tables.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneRouteTableFound(
        gk_route_table,
        route_tables.len()
    ));
}

pub async fn select(
    filter: crate::RouteTableFilter,
) -> error_stack::Result<Vec<RouteTable>, ErrorStack> {
    let mut rval = Vec::new();

    for route_table in select_result(filter).await? {
        match route_table {
            Ok(rt) => rval.push(rt),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::RouteTableFilter,
) -> error_stack::Result<Vec<error_stack::Result<RouteTable, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}

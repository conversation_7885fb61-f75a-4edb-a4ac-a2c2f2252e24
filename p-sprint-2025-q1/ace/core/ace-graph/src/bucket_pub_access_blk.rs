#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneBucketPubAccessBlkFound(crate::BucketPublicAccessBlock, usize),
}

#[derive(Debug)]
pub struct BucketPubAccessBlk {
    pub graphkey: crate::BucketPublicAccessBlock,
    pub name: String,
    pub source: String,
}

impl crate::GraphValueExt for BucketPubAccessBlk {}

pub async fn get(
    gk_b_pub_acces_blk: crate::BucketPublicAccessBlock,
) -> error_stack::Result<BucketPubAccessBlk, ErrorStack> {
    let filter = crate::BucketPublicAccessBlockFilter::One(gk_b_pub_acces_blk.clone());

    let mut b_pub_acc_blks = select(filter).await?;
    if b_pub_acc_blks.len() == 1 {
        return Ok(b_pub_acc_blks.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneBucketPubAccessBlkFound(
        gk_b_pub_acces_blk,
        b_pub_acc_blks.len()
    ));
}

pub async fn select(
    filter: crate::BucketPublicAccessBlockFilter,
) -> error_stack::Result<Vec<BucketPubAccessBlk>, ErrorStack> {
    let mut rval = Vec::new();

    for b_pub_acc_blks in select_result(filter).await? {
        match b_pub_acc_blks {
            Ok(b) => rval.push(b),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::BucketPublicAccessBlockFilter,
) -> error_stack::Result<Vec<error_stack::Result<BucketPubAccessBlk, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}

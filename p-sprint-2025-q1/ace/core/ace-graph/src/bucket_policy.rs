#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneBucketPolicyFound(crate::BucketPolicy, usize),
}

#[derive(Debug)]
pub struct BucketPolicy {
    pub graphkey: crate::<PERSON>etPolicy,
    pub name: String,
    pub source: String,
}

impl crate::GraphValueExt for BucketPolicy {}

pub async fn get(
    gk_bucket_pol: crate::BucketPolicy,
) -> error_stack::Result<BucketPolicy, ErrorStack> {
    let filter = crate::BucketPolicyFilter::One(gk_bucket_pol.clone());

    let mut b_pols = select(filter).await?;
    if b_pols.len() == 1 {
        return Ok(b_pols.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneBucketPolicyFound(
        gk_bucket_pol,
        b_pols.len()
    ));
}

pub async fn select(
    filter: crate::BucketPolicyFilter,
) -> error_stack::Result<Vec<BucketPolicy>, ErrorStack> {
    let mut rval = Vec::new();

    for b_pol in select_result(filter).await? {
        match b_pol {
            Ok(bp) => rval.push(bp),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::BucketPolicyFilter,
) -> error_stack::Result<Vec<error_stack::Result<BucketPolicy, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneIamUserFound(crate::IamUser, usize),
}

#[derive(Debug)]
pub struct IamUser {
    pub graphkey: crate::Iam<PERSON>ser,
    pub name: String,
    pub source: String,
}

impl crate::GraphValueExt for IamUser {}

pub async fn get(gk_iam_user: crate::IamUser) -> error_stack::Result<IamUser, ErrorStack> {
    let filter = crate::IamUserFilter::One(gk_iam_user.clone());

    let mut iam_user = select(filter).await?;
    if iam_user.len() == 1 {
        return Ok(iam_user.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneIamUserFound(gk_iam_user, iam_user.len()));
}

pub async fn select(filter: crate::IamUserFilter) -> error_stack::Result<Vec<IamUser>, ErrorStack> {
    let mut rval = Vec::new();

    for iam_user in select_result(filter).await? {
        match iam_user {
            Ok(user) => rval.push(user),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::IamUserFilter,
) -> error_stack::Result<Vec<error_stack::Result<IamUser, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}

use error_stack::ResultExt;

use crate::GraphKeyExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneAwsElbFound(crate::AwsElb, usize),
    SelectFromApp,
    SelectFromAwsVpcSecurityGroup,
}

#[derive(Debug)]
pub struct AwsElb {
    pub graphkey: crate::AwsElb,
    pub name: String,
    pub purpose: String,
    pub description: Option<String>,
    pub subnets: Vec<crate::Subnet>,
    pub enable_cross_zone_load_balancing: bool,
    pub source: String,
    pub ingress_description: String,
    pub ingress: Vec<crate::ins::Ingress>,
}

impl crate::GraphValueExt for AwsElb {}

pub async fn get(
    gk_aws_elb: crate::AwsElb,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<AwsElb, ErrorStack> {
    let filter = crate::AwsElbFilter::One(gk_aws_elb.clone());

    let mut items = select(filter, ace_db_app).await?;
    if items.len() == 1 {
        return Ok(items.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneAwsElbFound(gk_aws_elb, items.len()));
}

pub async fn select(
    filter: crate::AwsElbFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<AwsElb>, ErrorStack> {
    let mut rval = Vec::new();

    for items in select_result(filter, ace_db_app).await? {
        match items {
            Ok(aws_elb) => rval.push(aws_elb),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: crate::AwsElbFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<AwsElb, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    rval.extend(
        crate::app::select_result_aws_elb(&filter, ace_db_app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromApp)?
            .into_iter()
            .map(|v| v.change_context(ErrorStack::SelectFromApp)),
    );

    Ok(rval)
}

pub async fn select_result_aws_vpc_sg(
    filter: &crate::AwsVpcSecurityGroupFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<
    Vec<error_stack::Result<crate::aws_vpc_sg::AwsVpcSecurityGroup, ErrorStack>>,
    ErrorStack,
> {
    let mut rval = Vec::new();

    let filter = match filter {
        crate::AwsVpcSecurityGroupFilter::All => crate::AwsElbFilter::All,
        crate::AwsVpcSecurityGroupFilter::One(sg_gk) => match sg_gk {
            crate::AwsVpcSg::AwsElb(aws_elb) => crate::AwsElbFilter::One(aws_elb.clone()),
            _ => return Ok(rval),
        },
        crate::AwsVpcSecurityGroupFilter::None => return Ok(rval),
    };

    for items in select_result(filter, ace_db_app).await? {
        match items {
            Ok(aws_elb) => {
                let graphkey = crate::AwsVpcSg::AwsElb(aws_elb.graphkey.clone());
                let name = graphkey.serialize_dashed();
                let ingress = aws_elb.ingress.clone();
                let description = aws_elb.ingress_description.clone();

                let aws_vpc_sg = crate::aws_vpc_sg::AwsVpcSecurityGroup {
                    graphkey,
                    name,
                    ingress,
                    description,
                    source: aws_elb.source.clone(),
                };

                rval.push(Ok(aws_vpc_sg));
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

use error_stack::ResultExt;

use crate::GraphKeyExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetConfig,
    GetCurrentAccount,
    GetEtcEcrs,
    NotOneEcrFound(crate::Ecr, usize),
    SelectFromApp,
    SelectFromDocker,
}

#[derive(Debug)]
pub struct Ecr {
    pub graphkey: crate::Ecr,
    pub name: String,
    pub image_tag_mutability: String,
    pub scan_on_push: bool,
    pub arn: String,
    pub source: String,
}

impl crate::GraphValueExt for Ecr {}

pub async fn get(gk_ecr: crate::Ecr, app: &ace_db::App) -> error_stack::Result<Ecr, ErrorStack> {
    let filter = crate::EcrFilter::One(gk_ecr.clone());

    let mut ecrs = select(&filter, app).await?;
    if ecrs.len() == 1 {
        return Ok(ecrs.pop().unwrap());
    }
    error_stack::bail!(ErrorStack::NotOneEcrFound(gk_ecr, ecrs.len()));
}

pub async fn select(
    filter: &crate::EcrFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Ecr>, ErrorStack> {
    let mut rval = Vec::new();

    for bucket in select_result(filter, app).await? {
        match bucket {
            Ok(bucket) => rval.push(bucket),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::EcrFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Ecr, ErrorStack>>, ErrorStack> {
    let etc_filter = match filter {
        crate::EcrFilter::All => ace_db::Filter::All,
        crate::EcrFilter::One(ecr) => match ecr {
            crate::Ecr::App(_, _) => ace_db::Filter::None,
            crate::Ecr::AppPreview(_, _) => ace_db::Filter::None,
            crate::Ecr::Db(name) => ace_db::Filter::One(name.clone()),
            crate::Ecr::Docker(_) => ace_db::Filter::None,
        },
        crate::EcrFilter::None => ace_db::Filter::None, // DO NOT exit early, may be dockers.
    };

    let mut rval = Vec::new();

    // Collect/process Ecrs from Etc
    let etc_ecrs = ace_db::ecr::select_result(etc_filter, ace_db_app)
        .await
        .change_context(ErrorStack::GetEtcEcrs)?;

    // Get config/account info for construction:
    let config = crate::config::get(&crate::Config::EtcConfig, ace_db_app)
        .await
        .change_context(ErrorStack::GetConfig)?;
    let account = crate::aws_account::get(&crate::AwsAccount::Db(config.account_key), ace_db_app)
        .await
        .change_context(ErrorStack::GetCurrentAccount)?;

    for ecr in etc_ecrs {
        match ecr {
            Ok(ecr) => {
                rval.push(Ok(construct_from_etc(
                    ecr,
                    &config.region,
                    &account.aws_account_id,
                )));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    // Collect/process Ecrs from App
    rval.extend(
        crate::app::select_result_ecr(filter, ace_db_app)
            .await
            .change_context(ErrorStack::SelectFromApp)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromApp)),
    );

    // Collect/process Ecrs from Docker
    rval.extend(
        crate::docker::select_result_ecr(filter, ace_db_app)
            .await
            .change_context(ErrorStack::SelectFromDocker)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromDocker)),
    );

    Ok(rval)
}

fn construct_from_etc(ecr: ace_db::etc::ecr::EcrRepo, region: &str, account_id: &str) -> Ecr {
    let graphkey = crate::Ecr::Db(ecr.name.clone());
    let name = graphkey.serialize_dashed();
    let arn = format!("arn:aws:ecr:{region}:{account_id}:repository/{name}");

    Ecr {
        graphkey,
        name,
        image_tag_mutability: ecr.image_tag_mutability,
        scan_on_push: ecr.scan_on_push,
        arn,
        source: ecr.source.to_string_lossy().to_string(),
    }
}

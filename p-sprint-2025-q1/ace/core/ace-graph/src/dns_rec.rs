#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneDnsRecordFound(crate::DnsRecord, usize),
}

#[derive(Debug)]
pub struct DnsRecord {
    pub graphkey: crate::DnsRecord, // Get Zone ID from DnsZone
    pub name: String,
    pub ttl: u32,
    pub records: Vec<String>,
    pub source: String,
}

impl crate::GraphValueExt for DnsRecord {}

pub async fn get(gk_dns_record: crate::DnsRecord) -> error_stack::Result<DnsRecord, ErrorStack> {
    let filter = crate::DnsRecFilter::One(gk_dns_record.clone());

    let mut dns_records = select(filter).await?;
    if dns_records.len() == 1 {
        return Ok(dns_records.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneDnsRecordFound(
        gk_dns_record,
        dns_records.len()
    ));
}

pub async fn select(
    filter: crate::DnsRecFilter,
) -> error_stack::Result<Vec<DnsRecord>, ErrorStack> {
    let mut rval = Vec::new();

    for dns_record in select_result(filter).await? {
        match dns_record {
            Ok(dns_record) => rval.push(dns_record),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::DnsRecFilter,
) -> error_stack::Result<Vec<error_stack::Result<DnsRecord, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}

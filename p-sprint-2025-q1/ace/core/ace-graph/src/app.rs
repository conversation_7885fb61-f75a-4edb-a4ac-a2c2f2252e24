use crate::ins::{Ingress, PortRange};
use crate::{GraphKeyExt, GraphKeyName};
use error_stack::ResultExt;
use indexmap::IndexMap;
use serde_json::json;

#[derive(PartialEq)]
enum ProductionOrPreview {
    Production,
    Preview,
}

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    ConstructTlsCert(crate::TlsCert),
    CurrentRegionStructNotFound,
    DbError,
    DeserializeAmi(String),
    DeserializeAwsAz(String),
    DeserializeSubnet(String),
    GeneratePublicHTTPIngress,
    GetAppFromDB,
    GetAppTlscert(crate::App),
    GetConfig,
    GetCurrentAccount,
    IpNetworkParse(ipnetwork::IpNetworkError),
    DeserializeBucketPolicyStatements(serde_json::Error),
    CannotParseParameterFamily(String, String),

    /// (Graphkey, num-records-found, specific_preview searched for)
    NotOneFound(crate::App, usize, Option<String>),
    SelectApps,
}

#[derive(Debug)]
pub struct App {
    pub graphkey: crate::App,
    pub name: String,
    pub secret: String,
    pub hosting: Option<AppHosting>,
    pub preview: Option<AppHosting>,
    pub conf: toml::value::Table,
    pub conf_production: toml::value::Table,
    pub conf_preview: toml::value::Table,
    pub conf_developer: toml::value::Table,
    pub conf_merged_production: toml::value::Table,
    pub conf_merged_preview: toml::value::Table,
    pub db_bucket_map: IndexMap<String, AppBucket>,
    pub aws_ec_sls: IndexMap<String, AppAwsEcSls>,
    pub aws_rds_instance: IndexMap<String, AppAwsRds>,
    pub aws_elb: IndexMap<String, AppAwsElbNetworkLoadBalancer>,
    pub aws_ecr: IndexMap<String, AppAwsEcr>,
    pub source: std::path::PathBuf,
}

impl crate::GraphValueExt for App {}

impl GraphKeyName for crate::App {
    fn get_name(&self) -> String {
        match self {
            crate::App::Db(name) => name.clone(),
        }
    }
}

#[derive(Debug)]
pub struct AppHosting {
    pub ami: crate::Ami,
    pub subnet: crate::Subnet,
    pub instance_type: String,
    pub ingress_http_public: bool,
    pub use_elastic_ip: bool,
    pub volume_size: crate::ins::VolumeSize,
    pub ingress: Vec<Ingress>,
}

#[derive(Debug, Clone)]
pub struct AppBucket {
    pub suffix: String,
    pub bucket_name: String,
    pub public_read_paths: Vec<String>,
    pub production_read: bool,
    pub production_write: bool,
    pub preview_read: bool,
    pub preview_write: bool,
    pub developer_read: bool,
    pub developer_write: bool,
    pub bucket_policy_statements: Vec<granite::JsonObject>,
}

#[derive(Debug, Clone)]
pub struct AppAwsEcSls {
    pub graphkey: crate::AwsEcSls,
    pub name: String,
    pub purpose: String,
    pub source: String,
    pub description: Option<String>,
    pub engine_version: String,
    pub backup_daily: bool,
}

#[derive(Debug, Clone)]
pub struct AppAwsRds {
    pub allocated_storage: u32,
    pub storage_type: String,
    pub instance_class: String,
    pub engine: String,
    pub engine_version: String,
    pub multi_az: bool,
    pub availability_zone: Option<crate::AwsAz>,
    pub preferred_maintenance_window: Option<String>,
    pub preferred_backup_window: Option<String>,
    pub username: String,
    pub password: String,
    pub parameters: IndexMap<String, String>,
}

#[derive(Debug, Clone)]
pub struct AppAwsElbNetworkLoadBalancer {
    pub purpose: String,
    pub description: Option<String>,
    pub enable_cross_zone_load_balancing: bool,
    pub use_elastic_ips: bool,
}

#[derive(Debug, Clone)]
pub struct AppAwsEcr {
    pub pull: bool,
    pub push: bool,
}

impl crate::GraphValueExt for AppBucket {}

pub type AppList = Vec<App>;

pub async fn get(app_name: &str, app: &ace_db::App) -> error_stack::Result<App, ErrorStack> {
    let gk_app = crate::App::Db(app_name.to_string());
    let filter = crate::AppFilter::One(gk_app.clone());
    let mut apps = select_result(&filter, app).await?;

    // Validate that one record was returned
    let app = {
        if apps.len() == 1 {
            apps.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_app, apps.len(), None))
        }
    };

    app.change_context_lazy(|| ErrorStack::GetAppFromDB)
}

pub async fn select(
    filter: &crate::AppFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<App>, ErrorStack> {
    // Fails at ANY error found (but not necessarily at "hey didn't find a match"?).
    let mut rval = Vec::new();

    for app_result in select_result(filter, app).await? {
        match app_result {
            Ok(app) => {
                rval.push(app);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::AppFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<App, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::AppFilter::All => ace_db::Filter::All,
        crate::AppFilter::One(crate::App::Db(name)) => ace_db::Filter::One(name.as_ref()),
        crate::AppFilter::None => return Ok(Vec::new()), // exit early
    };

    let mut rval = Vec::new();

    let (current_account_key, current_region_key) = {
        let config = crate::config::get(&crate::Config::EtcConfig, app)
            .await
            .change_context(ErrorStack::GetConfig)?;

        (config.account_key, config.region)
    };

    let current_account = crate::aws_account::get(&crate::AwsAccount::Db(current_account_key), app)
        .await
        .change_context(ErrorStack::GetCurrentAccount)?;

    let Some(current_region) = current_account.region.get(&current_region_key) else {
        error_stack::bail!(ErrorStack::CurrentRegionStructNotFound);
    };

    let apps = ace_db::app::select_result(db_filter, app)
        .await
        .change_context(ErrorStack::DbError)?;

    for app in apps {
        match app {
            Ok(a) => {
                rval.push(construct(a, &current_account, current_region));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

fn construct(
    db_app: ace_db::etc::app::EtcApp,
    account: &crate::aws_account::AwsAccount,
    region: &crate::aws_account::AwsAccountRegion,
) -> error_stack::Result<App, ErrorStack> {
    let graphkey = crate::App::Db(db_app.name.clone());

    let mut bucket_map = IndexMap::new();
    for (key, ace_db_bucket) in &db_app.bucket_map {
        let suffix = key.to_owned();
        let bucket_name = format!(
            "{}-{}-app-{}-{}",
            account.account_key, region.name, db_app.name, suffix
        );

        // Convert bucket_policy_statements from String to Vec<JsonObject>
        let bucket_policy_statements = match ace_db_bucket.bucket_policy_statements.as_ref() {
            None => vec![],
            Some(bucket_policy_statements) => {
                match serde_json::from_str::<Vec<granite::JsonObject>>(bucket_policy_statements) {
                    Ok(bucket_policy_statements) => bucket_policy_statements,
                    Err(e) => {
                        error_stack::bail!(ErrorStack::DeserializeBucketPolicyStatements(e));
                    }
                }
            }
        };

        bucket_map.insert(
            suffix.clone(),
            AppBucket {
                suffix,
                bucket_name,
                public_read_paths: ace_db_bucket.public_read_paths.clone(),
                production_read: ace_db_bucket.production_read.unwrap_or(false),
                production_write: ace_db_bucket.production_write.unwrap_or(false),
                preview_read: ace_db_bucket.preview_read.unwrap_or(false),
                preview_write: ace_db_bucket.preview_write.unwrap_or(false),
                developer_read: ace_db_bucket.developer_read.unwrap_or(false),
                developer_write: ace_db_bucket.developer_write.unwrap_or(false),
                bucket_policy_statements,
            },
        );
    }

    // Handle hosting
    let hosting = match db_app.hosting {
        None => None,
        Some(app_hosting) => {
            let ami = match crate::Ami::deserialize(&app_hosting.ami) {
                Ok(ami) => ami,
                Err(e) => {
                    error_stack::bail!(ErrorStack::DeserializeAmi(e));
                }
            };

            let subnet = match crate::Subnet::deserialize(&app_hosting.subnet) {
                Ok(subnet) => subnet,
                Err(e) => {
                    error_stack::bail!(ErrorStack::DeserializeSubnet(e));
                }
            };

            Some(AppHosting {
                ami,
                subnet,
                instance_type: app_hosting.instance_type,
                ingress_http_public: app_hosting.ingress_http_public,
                use_elastic_ip: app_hosting.use_elastic_ip,
                volume_size: crate::ins::VolumeSize::Specific(app_hosting.volume_size),
                ingress: app_hosting.ingress.into_iter().map(|i| i.into()).collect(),
            })
        }
    };

    // Handle preview configs
    let preview = if let Some(db_app_hosting) = db_app.preview {
        let ami = match crate::Ami::deserialize(&db_app_hosting.ami) {
            Ok(ami) => ami,
            Err(e) => {
                error_stack::bail!(ErrorStack::DeserializeAmi(e));
            }
        };

        let subnet = match crate::Subnet::deserialize(&db_app_hosting.subnet) {
            Ok(subnet) => subnet,
            Err(e) => {
                error_stack::bail!(ErrorStack::DeserializeSubnet(e));
            }
        };

        let app_hosting = AppHosting {
            ami,
            subnet,
            instance_type: db_app_hosting.instance_type,
            ingress_http_public: db_app_hosting.ingress_http_public,
            use_elastic_ip: db_app_hosting.use_elastic_ip,
            volume_size: crate::ins::VolumeSize::Specific(db_app_hosting.volume_size),
            ingress: db_app_hosting
                .ingress
                .into_iter()
                .map(|i| i.into())
                .collect(),
        };

        Some(app_hosting)
    } else {
        None
    };

    // Create conf_merged_production by merging conf and conf_production
    let mut conf_merged_production = db_app.conf.clone();
    for (k, v) in &db_app.conf_production {
        conf_merged_production.insert(k.clone(), v.clone());
    }

    // Create conf_merged_preview by merging conf and conf_preview
    let mut conf_merged_preview = db_app.conf.clone();
    for (k, v) in &db_app.conf_preview {
        conf_merged_preview.insert(k.clone(), v.clone());
    }

    let aws_ec_sls: IndexMap<String, AppAwsEcSls> = db_app
        .aws_ec_sls
        .into_iter()
        .map(|(purpose, dbitem)| {
            let gk = crate::AwsEcSls::App(graphkey.clone(), purpose.clone());
            let name = gk.serialize_dashed();

            (
                purpose.clone(),
                AppAwsEcSls {
                    graphkey: gk,
                    name,
                    purpose,
                    source: db_app.name.clone(),
                    description: dbitem.description,
                    engine_version: dbitem.engine_version,
                    backup_daily: dbitem.backup_daily.unwrap_or(true),
                },
            )
        })
        .collect();

    let aws_rds_instance: IndexMap<String, AppAwsRds> = {
        let mut aws_rds_instance = IndexMap::new();

        for (purpose, dbitem) in db_app.aws_rds_instance {
            let availability_zone = match dbitem.availability_zone {
                None => None,
                Some(az) => match crate::AwsAz::deserialize(&az) {
                    Ok(az) => Some(az),
                    Err(e) => {
                        error_stack::bail!(ErrorStack::DeserializeAwsAz(e));
                    }
                },
            };

            aws_rds_instance.insert(
                purpose.clone(),
                AppAwsRds {
                    allocated_storage: dbitem.allocated_storage,
                    storage_type: dbitem.storage_type.unwrap_or("gp3".to_string()),
                    instance_class: dbitem.instance_class,
                    engine: dbitem.engine,
                    engine_version: dbitem.engine_version,
                    multi_az: dbitem.multi_az,
                    availability_zone,
                    preferred_maintenance_window: dbitem.preferred_maintenance_window,
                    preferred_backup_window: dbitem.preferred_backup_window,
                    username: dbitem.username,
                    password: dbitem.password,
                    parameters: dbitem.parameters.clone(),
                },
            );
        }
        aws_rds_instance
    };

    let aws_elb: IndexMap<String, AppAwsElbNetworkLoadBalancer> = db_app
        .aws_elb
        .into_iter()
        .map(|(purpose, dbitem)| {
            (
                purpose.clone(),
                AppAwsElbNetworkLoadBalancer {
                    purpose,
                    description: dbitem.description,
                    enable_cross_zone_load_balancing: dbitem
                        .enable_cross_zone_load_balancing
                        .unwrap_or(false),
                    use_elastic_ips: dbitem.use_elastic_ips.unwrap_or(false),
                },
            )
        })
        .collect();

    let aws_ecr: IndexMap<String, AppAwsEcr> = db_app
        .aws_ecr
        .into_iter()
        .map(|(purpose, dbitem)| {
            (
                purpose.clone(),
                AppAwsEcr {
                    pull: dbitem.pull.unwrap_or(true),
                    push: dbitem.push.unwrap_or(false),
                },
            )
        })
        .collect();

    Ok(App {
        graphkey,
        name: db_app.name,
        secret: db_app.secret,
        hosting,
        preview,
        conf: db_app.conf,
        conf_production: db_app.conf_production,
        conf_preview: db_app.conf_preview,
        conf_developer: db_app.conf_developer,
        conf_merged_production,
        conf_merged_preview,
        db_bucket_map: bucket_map,
        aws_ec_sls,
        aws_rds_instance,
        aws_elb,
        aws_ecr,
        source: db_app.source,
    })
}

pub async fn select_result_bucket(
    filter: &crate::BucketFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::bucket::Bucket, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    for app_result in select_result(&filter.into(), app).await? {
        match app_result {
            Ok(app) => {
                for app_bucket in app.db_bucket_map.values() {
                    let bucket = crate::bucket::Bucket {
                        // Suffix?  Purpose?
                        graphkey: crate::Bucket::App(
                            app.graphkey.clone(),
                            app_bucket.suffix.clone(),
                        ),
                        name: app_bucket.bucket_name.clone(),
                        developer_access: app_bucket.developer_read,
                        public_read: app_bucket.public_read_paths.clone(),
                        bucket_policy_statements: app_bucket.bucket_policy_statements.clone(),
                        suffix: app_bucket.suffix.clone(),
                        source: app.source.to_string_lossy().to_string(),
                    };

                    rval.push(Ok(bucket));
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_aws_ec_sls(
    filter: &crate::AwsEcSlsFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<
    Vec<error_stack::Result<crate::aws_ec_sls::AwsEcSls, ErrorStack>>,
    ErrorStack,
> {
    let mut rval = vec![];

    let (app_filter, purpose_filter) = match &filter {
        crate::AwsEcSlsFilter::All => (crate::AppFilter::All, None),
        crate::AwsEcSlsFilter::One(crate::AwsEcSls::App(app_gk, purpose)) => {
            (crate::AppFilter::One(app_gk.clone()), Some(purpose.clone()))
        }
        crate::AwsEcSlsFilter::None => return Ok(rval),
    };

    let apps = crate::app::select_result(&app_filter, ace_db_app)
        .await
        .change_context(ErrorStack::SelectApps)?;

    for app in apps {
        match app {
            Ok(app) => {
                for (purpose, item) in app.aws_ec_sls {
                    if let Some(purpose_filter) = &purpose_filter {
                        if purpose_filter != &purpose {
                            continue;
                        }
                    }

                    let graphkey = crate::AwsEcSls::App(app.graphkey.clone(), purpose.clone());
                    let name = graphkey.serialize_dashed();

                    rval.push(Ok(crate::aws_ec_sls::AwsEcSls {
                        graphkey,
                        name,
                        purpose,
                        description: item.description,
                        engine_version: item.engine_version,
                        backup_daily: item.backup_daily,
                        subnets: vec![
                            crate::Subnet::PublicA,
                            crate::Subnet::PublicB,
                            crate::Subnet::PublicC,
                        ],
                        source: app.graphkey.to_string(),
                    }));
                }
            }
            Err(e) => {
                rval.push(Err(e));
                continue;
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_aws_elb(
    filter: &crate::AwsElbFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::aws_elb::AwsElb, ErrorStack>>, ErrorStack> {
    let mut rval = vec![];

    let (app_filter, purpose_filter) = match filter {
        crate::AwsElbFilter::All => (crate::AppFilter::All, None),
        crate::AwsElbFilter::One(crate::AwsElb::App(app_gk, purpose)) => {
            (crate::AppFilter::One(app_gk.clone()), Some(purpose.clone()))
        }
        crate::AwsElbFilter::One(crate::AwsElb::Db(_)) => return Ok(rval),
        crate::AwsElbFilter::None => return Ok(rval),
    };

    let apps = crate::app::select_result(&app_filter, ace_db_app)
        .await
        .change_context(ErrorStack::SelectApps)?;

    for app in apps {
        match app {
            Ok(app) => {
                for (purpose, item) in app.aws_elb {
                    if let Some(purpose_filter) = &purpose_filter {
                        if purpose_filter != &purpose {
                            continue;
                        }
                    }

                    let graphkey = crate::AwsElb::App(app.graphkey.clone(), purpose.clone());
                    let name = graphkey.serialize_dashed();
                    let ingress_description = "Allow HTTP/HTTPS inbound traffic".to_string();
                    let ingress = vec![
                        Ingress {
                            protocol: "tcp".to_string(),
                            ports: PortRange::One(80),
                            cidr_blocks: crate::ins::CidrBlocks::All,
                        },
                        Ingress {
                            protocol: "tcp".to_string(),
                            ports: PortRange::One(443),
                            cidr_blocks: crate::ins::CidrBlocks::All,
                        },
                        Ingress {
                            protocol: "udp".to_string(),
                            ports: PortRange::One(443),
                            cidr_blocks: crate::ins::CidrBlocks::All,
                        },
                    ];

                    let subnets = vec![
                        crate::Subnet::PublicA,
                        crate::Subnet::PublicB,
                        crate::Subnet::PublicC,
                    ];

                    rval.push(Ok(crate::aws_elb::AwsElb {
                        graphkey,
                        name,
                        purpose,
                        description: item.description,
                        enable_cross_zone_load_balancing: item.enable_cross_zone_load_balancing,
                        ingress_description,
                        ingress,
                        subnets,
                        source: app.source.to_string_lossy().to_string(),
                    }));
                }
            }
            Err(e) => {
                rval.push(Err(e));
                continue;
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_aws_rds_instance(
    filter: &crate::AwsRdsFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::aws_rds::AwsRds, ErrorStack>>, ErrorStack> {
    let mut rval = vec![];

    let (app_filter, purpose_filter) = match filter {
        crate::AwsRdsFilter::All => (crate::AppFilter::All, None),
        crate::AwsRdsFilter::One(crate::AwsRds::App(app_gk, purpose)) => {
            (crate::AppFilter::One(app_gk.clone()), Some(purpose.clone()))
        }
        crate::AwsRdsFilter::None => return Ok(rval),
    };

    let apps = crate::app::select_result(&app_filter, ace_db_app)
        .await
        .change_context(ErrorStack::SelectApps)?;

    for app in apps {
        match app {
            Ok(app) => {
                for (purpose, item) in app.aws_rds_instance {
                    if let Some(purpose_filter) = &purpose_filter {
                        if purpose_filter != &purpose {
                            continue;
                        }
                    }

                    let graphkey = crate::AwsRds::App(app.graphkey.clone(), purpose.clone());
                    let name = graphkey.serialize_dashed();
                    let parameter_family = {
                        let engine_version = &item.engine_version;
                        let engine = &item.engine;
                        let parts = engine_version.split('.').collect::<Vec<_>>();
                        let (major_version, minor_version) = match (parts.first(), parts.get(1)) {
                            (Some(major_version), Some(minor_version)) => {
                                (major_version, minor_version)
                            }
                            _ => {
                                error_stack::bail!(ErrorStack::CannotParseParameterFamily(
                                    engine_version.to_string(),
                                    engine.to_string(),
                                ));
                            }
                        };

                        format!("{engine}{major_version}.{minor_version}")
                    };

                    rval.push(Ok(crate::aws_rds::AwsRds {
                        graphkey,
                        name,
                        allocated_storage: item.allocated_storage,
                        storage_type: item.storage_type,
                        instance_class: item.instance_class,
                        engine: item.engine,
                        engine_version: item.engine_version,
                        multi_az: item.multi_az,
                        availability_zone: item.availability_zone,
                        preferred_maintenance_window: item.preferred_maintenance_window,
                        preferred_backup_window: item.preferred_backup_window,
                        username: item.username,
                        password: item.password,
                        parameter_family,
                        parameters: item.parameters.clone(),
                        source: app.source.to_string_lossy().to_string(),
                    }));
                }
            }
            Err(e) => {
                rval.push(Err(e));
                continue;
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_iam_role(
    filter: &crate::IamRoleFilter,
    app: &ace_db::App,
    config: &crate::config::Config,
) -> error_stack::Result<Vec<error_stack::Result<crate::iam_role::IamRole, ErrorStack>>, ErrorStack>
{
    let mut rval = Vec::new();

    let (production_or_preview_filter, app_filter) = match filter {
        crate::IamRoleFilter::All => (None, crate::AppFilter::All),
        crate::IamRoleFilter::One(crate::IamRole::App(name)) => (
            Some(ProductionOrPreview::Production),
            crate::AppFilter::One(name.clone()),
        ),
        crate::IamRoleFilter::One(crate::IamRole::AppPreview(name)) => (
            Some(ProductionOrPreview::Preview),
            crate::AppFilter::One(name.clone()),
        ),
        _ => return Ok(rval), // exit early
    };

    for app_result in select_result(&app_filter, app).await? {
        match app_result {
            Ok(app) => {
                // app and app-preview
                for (production_or_preview, suffix, graphkey) in [
                    (
                        ProductionOrPreview::Production,
                        "-0",
                        crate::IamRole::App(app.graphkey.clone()),
                    ),
                    (
                        ProductionOrPreview::Preview,
                        "-1",
                        crate::IamRole::AppPreview(app.graphkey.clone()),
                    ),
                ] {
                    // skip record if filtering by App or AppPreview
                    if let Some(production_or_preview_filter) = &production_or_preview_filter {
                        if production_or_preview_filter != &production_or_preview {
                            continue;
                        }
                    }

                    // IAM is global, so we need to include the account key and region in the name
                    let role_name = format!(
                        "{}-{}-app-{}{}",
                        config.account_key, config.region, app.name, suffix
                    );

                    let mut assume_role_statements = Vec::new();
                    let mut policy_statements = Vec::new();

                    assume_role_statements.push(json!(
                        {
                            "Action": "sts:AssumeRole",
                            "Principal": {
                                "Service": "ec2.amazonaws.com"
                            },
                            "Effect": "Allow",
                        }
                    ));

                    // add specific permissions for ecr pulling
                    {
                        let mut resources = Vec::new();
                        for (purpose, ecr) in app.aws_ecr.iter() {
                            if ecr.pull {
                                let name = format!("app/{}{}/{}", app.name, suffix, purpose);
                                let arn = format!(
                                    "arn:aws:ecr:{}:{}:repository/{}",
                                    config.region, config.aws_account_id, name
                                );

                                resources.push(arn);
                            }
                        }

                        if !resources.is_empty() {
                            policy_statements.push(json!(
                                {
                                    "Sid": "GetAuthToken",
                                    "Action": "ecr:GetAuthorizationToken",
                                    "Effect": "Allow",
                                    "Resource": "*"
                                }
                            ));

                            policy_statements.push(json!(
                                {
                                    "Sid": "EcrPull",
                                    "Action": [
                                        "ecr:GetDownloadUrlForLayer",
                                        "ecr:BatchGetImage",
                                        "ecr:BatchCheckLayerAvailability"
                                    ],
                                    "Effect": "Allow",
                                    "Resource": resources,
                                }
                            ));
                        }
                    }

                    // Add bucket access
                    if !app.db_bucket_map.is_empty() {
                        let mut read_buckets = Vec::new();
                        let mut write_buckets = Vec::new();

                        for bucket in app.db_bucket_map.values() {
                            match production_or_preview {
                                ProductionOrPreview::Production => {
                                    if bucket.production_read {
                                        read_buckets.push(&bucket.bucket_name);
                                    }

                                    if bucket.production_write {
                                        write_buckets.push(&bucket.bucket_name);
                                    }
                                }
                                ProductionOrPreview::Preview => {
                                    if bucket.preview_read {
                                        read_buckets.push(&bucket.bucket_name);
                                    }

                                    if bucket.preview_write {
                                        write_buckets.push(&bucket.bucket_name);
                                    }
                                }
                            }
                        }

                        if !read_buckets.is_empty() {
                            policy_statements.push(json!(
                                {
                                    "Action": [
                                        "s3:ListBucket",
                                        "s3:ListBucketMultipartUploads",
                                        "s3:GetBucketLocation"
                                    ],
                                    "Effect": "Allow",
                                    "Resource": read_buckets.iter().map(|bucket| format!("arn:aws:s3:::{bucket}")).collect::<Vec<String>>()
                                }
                            ));

                            policy_statements.push(json!(
                                {
                                    "Action": [
                                        "s3:GetObject"
                                    ],
                                    "Effect": "Allow",
                                    "Resource": read_buckets.iter().map(|bucket| format!("arn:aws:s3:::{bucket}/*")).collect::<Vec<String>>()
                                }
                            ));
                        }

                        if !write_buckets.is_empty() {
                            policy_statements.push(json!(
                                {
                                    "Action": [
                                        "s3:PutObject",
                                        "s3:DeleteObject",
                                        "s3:AbortMultipartUpload",
                                        "s3:ListMultipartUploadParts"
                                    ],
                                    "Effect": "Allow",
                                    "Resource": write_buckets.iter().map(|bucket| format!("arn:aws:s3:::{bucket}/*")).collect::<Vec<String>>()
                                }
                            ));
                        }
                    }

                    let iam_role = crate::iam_role::IamRole {
                        graphkey,
                        name: role_name,
                        role_type: crate::iam_role::IamRoleType::New {
                            assume_role_policy: json!({
                                "Version": "2012-10-17",
                                "Statement": assume_role_statements,
                            }),
                        },
                        with_instance_profile: true,
                        policy_statements,
                        source: app.source.to_string_lossy().to_string(),
                    };

                    rval.push(Ok(iam_role));
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_ecr(
    filter: &crate::EcrFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::ecr::Ecr, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    let (app_filter, purpose_filter) = match filter {
        crate::EcrFilter::All => (crate::AppFilter::All, None),
        crate::EcrFilter::One(crate::Ecr::App(app, purpose)) => {
            (crate::AppFilter::One(app.clone()), Some(purpose.clone()))
        }
        _ => return Ok(rval), // exit early
    };

    for app in select_result(&app_filter, app).await? {
        match app {
            Ok(app) => {
                for (purpose, _item) in app.aws_ecr {
                    // app and app-preview
                    for (suffix, graphkey) in [
                        ("-0", crate::Ecr::App(app.graphkey.clone(), purpose.clone())),
                        (
                            "-1",
                            crate::Ecr::AppPreview(app.graphkey.clone(), purpose.clone()),
                        ),
                    ] {
                        if let Some(purpose_filter) = &purpose_filter {
                            if purpose_filter != &purpose {
                                continue;
                            }
                        }

                        let name = format!("app/{}{}/{}", app.name, suffix, purpose);

                        let arn = format!(
                            "arn:aws:ecr:{}:{}:repository/{}",
                            config.region, config.aws_account_id, name
                        );

                        rval.push(Ok(crate::ecr::Ecr {
                            graphkey,
                            name,
                            image_tag_mutability: "MUTABLE".to_string(),
                            scan_on_push: false,
                            arn,
                            source: app.source.to_string_lossy().to_string(),
                        }));
                    }
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_instance(
    filter: &crate::InstanceFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::ins::Instance, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    let (app_filter, preview_or_production_filter) = match filter {
        crate::InstanceFilter::All => (crate::AppFilter::All, None),
        crate::InstanceFilter::One(crate::Instance::App(name)) => (
            crate::AppFilter::One(name.clone()),
            Some(ProductionOrPreview::Production),
        ),
        crate::InstanceFilter::One(crate::Instance::AppPreview(app)) => (
            crate::AppFilter::One(app.clone()),
            Some(ProductionOrPreview::Preview),
        ),
        _ => return Ok(rval), // exit early
    };

    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    for app in select_result(&app_filter, app).await? {
        match app {
            Ok(app) => {
                // Build the hosting instance if it exists
                if matches!(
                    preview_or_production_filter,
                    Some(ProductionOrPreview::Production) | None
                ) {
                    if let Some(hosting) = app.hosting {
                        let mut ingress = vec![
                            // Allow inbound SSH traffic on vpc
                            crate::ins::Ingress {
                                protocol: "tcp".to_string(),
                                ports: PortRange::One(22),
                                cidr_blocks: crate::ins::CidrBlocks::MainVpcCidrBlock,
                            },
                            // Allow inbound SSH traffic from my ip
                            crate::ins::Ingress {
                                protocol: "tcp".to_string(),
                                ports: PortRange::One(22),
                                cidr_blocks: crate::ins::CidrBlocks::Known(vec![
                                    config.my_ipnetwork,
                                ]),
                            },
                        ];

                        // ingest specific ingress rules
                        ingress.extend(hosting.ingress.clone());

                        // shortcut for common case
                        if hosting.ingress_http_public {
                            match ingress_http_public() {
                                Ok(i) => ingress.extend(i),
                                Err(e) => {
                                    rval.push(Err(
                                        e.change_context(ErrorStack::GeneratePublicHTTPIngress)
                                    ));
                                    continue;
                                }
                            }
                        }

                        let graphkey = crate::Instance::App(app.graphkey.clone());
                        let name = format!("{}.app", graphkey.get_name());
                        let public_hostname = super::ins::construct_pub_hostname(&name, &config);
                        let private_hostname =
                            super::ins::construct_private_hostname(&name, &config);

                        let instance = crate::ins::Instance {
                            graphkey,
                            ami_graphkey: hosting.ami,
                            name,
                            subnet: hosting.subnet,

                            instance_profile_role: Some(crate::IamRole::App(app.graphkey.clone())),
                            instance_type: hosting.instance_type,
                            volume_size: hosting.volume_size,
                            ingress,
                            use_elastic_ip: hosting.use_elastic_ip,
                            wildcard_dns: true,
                            public_hostname,
                            private_hostname,
                            source: app.source.to_string_lossy().to_string(),
                        };

                        rval.push(Ok(instance));
                    }
                }

                // build the preview instance if it exists
                if matches!(
                    preview_or_production_filter,
                    Some(ProductionOrPreview::Preview) | None
                ) {
                    if let Some(preview) = app.preview {
                        let mut ingress = vec![
                            // Allow inbound SSH traffic on vpc
                            crate::ins::Ingress {
                                protocol: "tcp".to_string(),
                                ports: PortRange::One(22),
                                cidr_blocks: crate::ins::CidrBlocks::MainVpcCidrBlock,
                            },
                            // Allow inbound SSH traffic from my ip
                            crate::ins::Ingress {
                                protocol: "tcp".to_string(),
                                ports: PortRange::One(22),
                                cidr_blocks: crate::ins::CidrBlocks::Known(vec![
                                    config.my_ipnetwork,
                                ]),
                            },
                        ];

                        // ingest specific ingress rules
                        ingress.extend(preview.ingress.clone());

                        // shortcut for common case
                        if preview.ingress_http_public {
                            match ingress_http_public() {
                                Ok(i) => ingress.extend(i),
                                Err(e) => {
                                    rval.push(Err(
                                        e.change_context(ErrorStack::GeneratePublicHTTPIngress)
                                    ));
                                    continue;
                                }
                            }
                        }

                        let graphkey = crate::Instance::AppPreview(app.graphkey.clone());
                        let name = format!("{}.app", graphkey.get_name());
                        let public_hostname = format!("{}.{}", name, config.public_subdomain_name);
                        let private_hostname =
                            format!("{}.{}", name, config.private_subdomain_name);

                        let instance = crate::ins::Instance {
                            graphkey,
                            ami_graphkey: preview.ami,
                            name,
                            subnet: preview.subnet,

                            instance_profile_role: Some(crate::IamRole::AppPreview(
                                app.graphkey.clone(),
                            )),
                            instance_type: preview.instance_type,
                            volume_size: preview.volume_size,
                            ingress,
                            use_elastic_ip: preview.use_elastic_ip,
                            wildcard_dns: true,
                            public_hostname,
                            private_hostname,
                            source: app.source.to_string_lossy().to_string(),
                        };

                        rval.push(Ok(instance));
                    }
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

// Lists tls certs for all filtered apps
// TODO: Refactor due to changes in return value of list()
pub async fn select_result_tlscert(
    filter: &crate::TlsCertFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::tlscert::TlsCert, ErrorStack>>, ErrorStack>
{
    let mut rval = Vec::new();

    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    // Iterate over list of apps - filter happens in list()
    for app_result in select_result(&filter.into(), app).await? {
        match app_result {
            Ok(a) => {
                let gk_priv = crate::TlsCert::App(a.graphkey.clone(), "pvt".to_string());
                let gk_pub = crate::TlsCert::App(a.graphkey.clone(), "pub".to_string());

                let pvt_common_name = format!("{}.app.{}", a.name, config.private_subdomain_name);
                let pub_common_name = format!("{}.app.{}", a.name, config.public_subdomain_name);

                let pvt_canonical_alt_names = vec![pvt_common_name.clone()];
                let pub_canonical_alt_names = vec![pub_common_name.clone()];

                // **IMPORTANT** use TlsCert::new() to have it automatically read itself from the filesystem (if it exists)
                let pvt_tlscert = crate::tlscert::TlsCert::new(
                    &gk_priv,
                    &pvt_common_name.clone(),
                    pvt_canonical_alt_names,
                    app,
                )
                .await
                .change_context(ErrorStack::ConstructTlsCert(gk_priv.clone()));
                let pub_tlscert = crate::tlscert::TlsCert::new(
                    &gk_pub,
                    &pub_common_name,
                    pub_canonical_alt_names,
                    app,
                )
                .await
                .change_context(ErrorStack::ConstructTlsCert(gk_pub.clone()));

                rval.push(pvt_tlscert);
                rval.push(pub_tlscert);

                {
                    let gk_priv = crate::TlsCert::AppPreview(a.graphkey.clone(), "pvt".to_string());
                    let gk_pub = crate::TlsCert::AppPreview(a.graphkey.clone(), "pub".to_string());

                    let pvt_common_name =
                        format!("{}-1.app.{}", a.name, config.private_subdomain_name);
                    let pub_common_name =
                        format!("{}-1.app.{}", a.name, config.public_subdomain_name);

                    let pvt_canonical_alt_names = vec![pvt_common_name.clone()];
                    let pub_canonical_alt_names = vec![pub_common_name.clone()];

                    // **IMPORTANT** use TlsCert::new() to have it automatically read itself from the filesystem (if it exists)
                    let pvt_tlscert = crate::tlscert::TlsCert::new(
                        &gk_priv,
                        &pvt_common_name.clone(),
                        pvt_canonical_alt_names,
                        app,
                    )
                    .await
                    .change_context(ErrorStack::ConstructTlsCert(gk_priv.clone()));
                    let pub_tlscert = crate::tlscert::TlsCert::new(
                        &gk_pub,
                        &pub_common_name,
                        pub_canonical_alt_names,
                        app,
                    )
                    .await
                    .change_context(ErrorStack::ConstructTlsCert(gk_pub.clone()));

                    rval.push(pvt_tlscert);
                    rval.push(pub_tlscert);
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }

        // only process appserver-private if it has been asked for
        // ...
    }

    Ok(rval)
}

pub async fn select_result_aws_elb_tg(
    filter: &crate::AwsElbTgFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<
    Vec<error_stack::Result<crate::aws_elb_tg::AwsElbTg, ErrorStack>>,
    ErrorStack,
> {
    let mut rval = Vec::new();

    let filter = match filter {
        crate::AwsElbTgFilter::All => crate::AppFilter::All,
        crate::AwsElbTgFilter::One(crate::AwsElbTg::App(app, _)) => {
            crate::AppFilter::One(app.clone())
        }
        crate::AwsElbTgFilter::None => return Ok(rval),
    };

    let config = crate::config::get(&crate::Config::EtcConfig, ace_db_app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    let apps = crate::app::select(&filter, ace_db_app)
        .await
        .change_context(ErrorStack::SelectApps)?;
    for app in apps {
        for (protocol, port, purpose) in [
            (
                crate::aws_elb_tg::AwsElbTgProtocol::Tcp,
                80,
                "tcp-80".to_string(),
            ),
            (
                crate::aws_elb_tg::AwsElbTgProtocol::Tcp,
                443,
                "tcp-443".to_string(),
            ),
            (
                crate::aws_elb_tg::AwsElbTgProtocol::Udp,
                443,
                "udp-443".to_string(),
            ),
        ] {
            let graphkey = crate::AwsElbTg::App(app.graphkey.clone(), purpose.clone());
            let name = format!("{}-{}-{}-{}", config.account_key, "app", app.name, purpose);

            rval.push(Ok(crate::aws_elb_tg::AwsElbTg {
                graphkey,
                name,
                protocol,
                purpose,
                port,
                source: app.source.to_string_lossy().to_string(),
            }));
        }
    }

    Ok(rval)
}

fn ingress_http_public() -> error_stack::Result<Vec<Ingress>, ErrorStack> {
    let mut http_public_ingress = vec![];

    let all_ip_addresses = match "0.0.0.0/0".parse::<ipnetwork::Ipv4Network>() {
        Ok(c) => c,
        Err(e) => {
            error_stack::bail!(ErrorStack::IpNetworkParse(e));
        }
    };

    // TODO: Create Ingress graphtype?

    http_public_ingress.push(Ingress {
        protocol: "tcp".to_string(),
        ports: PortRange::One(80),
        cidr_blocks: crate::ins::CidrBlocks::Known(vec![ipnetwork::IpNetwork::V4(
            all_ip_addresses,
        )]),
    });

    http_public_ingress.push(Ingress {
        protocol: "tcp".to_string(),
        ports: PortRange::One(443),
        cidr_blocks: crate::ins::CidrBlocks::Known(vec![ipnetwork::IpNetwork::V4(
            all_ip_addresses,
        )]),
    });

    http_public_ingress.push(Ingress {
        protocol: "udp".to_string(),
        ports: PortRange::One(443),
        cidr_blocks: crate::ins::CidrBlocks::Known(vec![ipnetwork::IpNetwork::V4(
            all_ip_addresses,
        )]),
    });

    Ok(http_public_ingress)
}

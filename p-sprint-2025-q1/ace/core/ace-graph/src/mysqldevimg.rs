use error_stack::ResultExt;
use std::path::PathBuf;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetConfig,
    GetCurrentAccount,
    GetMysqldevimg(crate::MysqlDevImg),
    NotOneFound(crate::MysqlDevImg),
    SelectFromDb,
    SqlFileDoesNotExist(PathBuf),
}

#[derive(Debug)]
pub struct Mysqldevimg {
    pub graphkey: crate::MysqlDevImg,
    pub name: String,
    pub sql_path: PathBuf,
    pub tag_name: String,
    pub iam_resource: String,
    pub source: std::path::PathBuf,
}

impl crate::GraphValueExt for Mysqldevimg {}

pub async fn get(
    gk_mysqldevimg: &crate::MysqlDevImg,
    app: &ace_db::App,
) -> error_stack::Result<Mysqldevimg, ErrorStack> {
    let filter = crate::MysqlDevImgFilter::One(gk_mysqldevimg.clone());
    let mut mysqldevimgs = select_result(&filter, app).await?;

    // Validate that one record was returned
    let mysqldevimg: Result<Mysqldevimg, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if mysqldevimgs.len() == 1 {
            mysqldevimgs.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_mysqldevimg.clone()))
        }
    };

    mysqldevimg.change_context_lazy(|| ErrorStack::GetMysqldevimg(gk_mysqldevimg.to_owned()))
}

pub async fn select(
    filter: &crate::MysqlDevImgFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Mysqldevimg>, ErrorStack> {
    let mut rval = Vec::new();

    for mysqldevimg in select_result(filter, app).await? {
        match mysqldevimg {
            Ok(mysqldevimg) => {
                rval.push(mysqldevimg);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::MysqlDevImgFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Mysqldevimg, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::MysqlDevImgFilter::All => ace_db::Filter::All,
        crate::MysqlDevImgFilter::One(crate::MysqlDevImg::Db(name)) => {
            ace_db::Filter::One(name.to_owned())
        }
        crate::MysqlDevImgFilter::None => return Ok(Vec::new()), // exit early
    };

    let mut rval = Vec::new();

    let mysqldevimgs = ace_db::mysqldevimg::select_result(db_filter, app)
        .await
        .change_context(ErrorStack::SelectFromDb)?;

    // Get current account/env information:
    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    let current_account = crate::aws_account::get(&crate::AwsAccount::Db(config.account_key), app)
        .await
        .change_context(ErrorStack::GetCurrentAccount)?;

    for mysqldevimg in mysqldevimgs {
        match mysqldevimg {
            Ok(mysqldevimg) => {
                let mysqldevimg =
                    construct(mysqldevimg, &config.region, &current_account.aws_account_id);
                rval.push(mysqldevimg);
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

fn construct(
    mysqldevimg: ace_db::etc::mysqldevimg::MysqlDevImg,
    region: &str,
    current_aws_account_id: &str,
) -> error_stack::Result<Mysqldevimg, ErrorStack> {
    if !mysqldevimg.sql_path.exists() {
        error_stack::bail!(ErrorStack::SqlFileDoesNotExist(mysqldevimg.sql_path));
    }

    let ecr_registry = format!("{current_aws_account_id}.dkr.ecr.{region}.amazonaws.com",);

    let tag_name = format!("{}/mysqldevimg/{}", ecr_registry, mysqldevimg.name);
    let iam_resource = format!(
        "arn:aws:ecr:{}:{}:repository/mysqldevimg/{}",
        region, current_aws_account_id, &mysqldevimg.name
    );

    Ok(Mysqldevimg {
        graphkey: crate::MysqlDevImg::Db(mysqldevimg.name.clone()),
        name: mysqldevimg.name.clone(),
        sql_path: mysqldevimg.sql_path,
        tag_name,
        iam_resource,
        source: mysqldevimg.source_path,
    })
}

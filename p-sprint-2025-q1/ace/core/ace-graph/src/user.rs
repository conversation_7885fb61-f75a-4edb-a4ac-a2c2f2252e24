use error_stack::ResultExt;
use ipnetwork::IpNetwork;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetConfig,
    GetCurrentAccount,
    GetUser(crate::User),
    DbError,
    NotOneFound(crate::User),
}

#[derive(Debug)]
pub struct User {
    pub graphkey: crate::User,
    pub name: String,
    pub ssh_keys: Vec<String>,
    pub static_networks: Vec<IpNetwork>,
    pub allowed_principals: Vec<String>,
    pub source: Vec<std::path::PathBuf>,
}

impl crate::GraphValueExt for User {}

pub async fn get(
    gk_user: &crate::User,
    app: &ace_db::App,
) -> error_stack::Result<User, ErrorStack> {
    let filter = crate::UserFilter::One(gk_user.clone());
    let mut users = select_result(&filter, app).await?;

    // Validate that one record was returned
    let user: Result<User, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if users.len() == 1 {
            users.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_user.clone()))
        }
    };

    user.change_context_lazy(|| ErrorStack::GetUser(gk_user.to_owned()))
}

/// Returns users only relevant to the current account/environment
pub async fn select(
    filter: &crate::UserFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<User>, ErrorStack> {
    // Fails at ANY error found
    let mut rval = Vec::new();

    for user in select_result(filter, app).await? {
        match user {
            Ok(user) => {
                rval.push(user);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::UserFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<User, ErrorStack>>, ErrorStack> {
    let db_etc_filter = match filter {
        crate::UserFilter::All => ace_db::Filter::All,
        crate::UserFilter::One(sys_gk) => match sys_gk {
            crate::User::Db(name) => ace_db::Filter::One(name.to_owned()),
        },
        crate::UserFilter::None => return Ok(Vec::new()), // exit early
    };

    let mut rval = Vec::new();

    // Collect users:
    let users = ace_db::user::select_result(db_etc_filter, app)
        .await
        .change_context(ErrorStack::DbError)?;

    // Process users
    for user in users {
        match user {
            Ok(user) => {
                rval.push(Ok(construct(user)));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::DbError)));
            }
        }
    }

    Ok(rval)
}

fn construct(user: ace_db::user::User) -> User {
    User {
        graphkey: crate::User::Db(user.name.clone()),
        name: user.name,
        ssh_keys: user.ssh_keys,
        static_networks: user.static_networks,
        allowed_principals: user.allowed_principals,
        source: user.source,
    }
}

pub async fn select_result_keypair(
    filter: &crate::KeyPairFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::keypair::KeyPair, ErrorStack>>, ErrorStack>
{
    let mut rval = Vec::new();

    let user_filter = match filter {
        crate::KeyPairFilter::All => crate::UserFilter::All,
        crate::KeyPairFilter::One(key_gk) => match key_gk {
            crate::KeyPair::User(user_gk, _key_name) => crate::UserFilter::One(user_gk.to_owned()),
            _ => crate::UserFilter::None,
        },
        crate::KeyPairFilter::None => crate::UserFilter::None,
    };

    for user in select_result(&user_filter, app).await? {
        match user {
            Ok(user) => {
                let keypairs = construct_keypairs_from_user(&user);
                for keypair in keypairs {
                    rval.push(Ok(keypair));
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

/// Select version of `select_result_keypair` that references select() instead of select_result()
pub async fn select_user_keypair(
    filter: &crate::KeyPairFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<crate::keypair::KeyPair>, ErrorStack> {
    let mut rval = Vec::new();

    let user_filter = match filter {
        crate::KeyPairFilter::All => crate::UserFilter::All,
        crate::KeyPairFilter::One(key_gk) => match key_gk {
            crate::KeyPair::User(user_gk, _key_name) => crate::UserFilter::One(user_gk.to_owned()),
            _ => crate::UserFilter::None,
        },
        crate::KeyPairFilter::None => crate::UserFilter::None,
    };

    for user in select(&user_filter, app).await? {
        rval.extend(construct_keypairs_from_user(&user));
    }

    Ok(rval)
}

fn construct_keypairs_from_user(user: &User) -> Vec<crate::keypair::KeyPair> {
    let mut rval = Vec::new();
    let mut i = 0;
    for ssh_public_key in &user.ssh_keys {
        i += 1;
        let keypair = crate::keypair::KeyPair {
            graphkey: crate::KeyPair::User(user.graphkey.clone(), format!("key-{i}")),
            name: format!("user-{}-key-{i}", user.name),
            public_key: ssh_public_key.clone(),
            source: user.graphkey.to_string(),
        };
        rval.push(keypair);
    }
    rval
}

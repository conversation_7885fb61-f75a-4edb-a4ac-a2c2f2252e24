use crate::{Graph<PERSON>eyExt, GraphKeyName};
use error_stack::ResultExt;
use serde::{Deserialize, Serialize};
use std::fmt::Display;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    DeserializeAmiGraphkey(String, std::path::PathBuf),
    DeserializeIamRoleGraphkey(String, std::path::PathBuf),
    DeserializeSubnetGraphkey(String, std::path::PathBuf),
    GetAceIngress,
    GetConfig,
    GetGraylogIngress,
    VerifyIamRoleExists(crate::IamRole),
    GetInstanceFromDb(crate::Instance),
    GetOpenVpnIngress,
    IpNetworkParse(ipnetwork::IpNetworkError),
    NotOneFound(crate::Instance, usize),
    SelectFromApp,
    SelectFromDb,
    SelectFromDeveloper,
    SelectInstances,
}

#[derive(Debug)]
pub struct Instance {
    pub graphkey: crate::Instance,
    pub name: String,
    pub subnet: crate::Subnet,
    pub instance_profile_role: Option<crate::IamRole>,
    pub instance_type: String,
    pub ami_graphkey: crate::Ami,
    pub volume_size: VolumeSize,
    pub ingress: Vec<Ingress>,
    pub use_elastic_ip: bool,
    pub wildcard_dns: bool,
    pub public_hostname: String,
    pub private_hostname: String,
    pub source: String,
}

impl crate::GraphValueExt for Instance {}

impl GraphKeyName for crate::Instance {
    fn get_name(&self) -> String {
        match self {
            crate::Instance::Ace => "ace".to_string(),
            crate::Instance::App(app) => format!("{}-0", app.get_name()),
            crate::Instance::AppPreview(app) => format!("{}-1", app.get_name()),
            crate::Instance::Developer(developer) => developer.get_name(),
            crate::Instance::Db(name) => name.clone(),
            crate::Instance::Graylog => "graylog".to_string(),
            crate::Instance::Vpn => "vpn".to_string(),
            crate::Instance::Deploy => "deploy".to_string(),
        }
    }
}

impl crate::Instance {
    pub fn map_name_to_terraform_resource_name(&self) -> String {
        match &self {
            crate::Instance::Ace => "ace2".to_string(),
            crate::Instance::App(app) => format!("{}-app", app.get_name()),
            crate::Instance::AppPreview(app) => format!("{}-app-preview", app.get_name()),
            crate::Instance::Developer(developer) => format!("developer-{}", developer.get_name()),
            crate::Instance::Db(name) => name.clone(),
            crate::Instance::Graylog => "graylog".to_string(),
            crate::Instance::Vpn => "openvpn".to_string(),
            crate::Instance::Deploy => "deploy".to_string(),
        }
    }
}

#[derive(Debug, Clone)]
pub struct Ingress {
    pub protocol: String,
    pub ports: PortRange,
    pub cidr_blocks: CidrBlocks,
}

impl From<ace_db::etc::instance::Ingress> for Ingress {
    fn from(i: ace_db::etc::instance::Ingress) -> Self {
        Ingress {
            protocol: i.protocol,
            ports: match i.ports {
                ace_db::etc::instance::PortRange::One(port) => PortRange::One(port),
                ace_db::etc::instance::PortRange::Range(from, to) => PortRange::Range(from, to),
                ace_db::etc::instance::PortRange::All => PortRange::All,
            },
            cidr_blocks: CidrBlocks::Known(i.cidr_blocks),
        }
    }
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub enum VolumeSize {
    Specific(u32),
    DeterminedByAws,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum CidrBlocks {
    Known(Vec<ipnetwork::IpNetwork>),
    MainVpcCidrBlock,
    All,
}

impl std::fmt::Display for VolumeSize {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            VolumeSize::Specific(size) => write!(f, "{size}GB"),
            VolumeSize::DeterminedByAws => write!(f, "auto"),
        }
    }
}

#[derive(Debug, Clone)]
pub enum PortRange {
    /// Represents -1 for ICMP
    All,
    One(i16),
    Range(i16, i16),
}

impl PortRange {
    pub fn to_aws_from_port(&self) -> i16 {
        match self {
            PortRange::All => -1,
            PortRange::One(port) => *port,
            PortRange::Range(from, _to) => *from,
        }
    }
    pub fn to_aws_to_port(&self) -> i16 {
        match self {
            PortRange::All => -1,
            PortRange::One(port) => *port,
            PortRange::Range(_from, to) => *to,
        }
    }
}

impl Display for PortRange {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            PortRange::All => write!(f, "all"),
            PortRange::One(port) => write!(f, "{port}"),
            PortRange::Range(from, to) => write!(f, "{from}-{to}"),
        }
    }
}

impl crate::GraphValueExt for Ingress {}

pub async fn get(
    gk: &crate::Instance,
    app: &ace_db::App,
) -> error_stack::Result<Instance, ErrorStack> {
    let ins_filter = crate::InstanceFilter::One(gk.clone());
    let mut instances = select_result(&ins_filter, app)
        .await
        .change_context(ErrorStack::GetInstanceFromDb(gk.clone()))?;

    let instance: Result<Instance, error_stack::Report<ErrorStack>> = {
        if instances.len() == 1 {
            instances.pop().unwrap()
        } else {
            // print all found
            eprintln!("Found {} instances:", instances.len());
            for instance in &instances {
                println!("  - {}", instance.as_ref().unwrap().name);
            }
            error_stack::bail!(ErrorStack::NotOneFound(gk.clone(), instances.len()));
        }
    };

    instance.change_context_lazy(|| ErrorStack::GetInstanceFromDb(gk.clone()))
}

pub async fn select(
    filter: &crate::InstanceFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Instance>, ErrorStack> {
    // Fails at ANY error found
    let mut rval = Vec::new();

    for instance in select_result(filter, app).await? {
        match instance {
            Ok(instance) => {
                rval.push(instance);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::InstanceFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Instance, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    let config = crate::config::get(&crate::Config::EtcConfig, ace_db_app)
        .await
        .change_context_lazy(|| ErrorStack::GetConfig)?;

    // Handle the unique Instances: (Ace, Vpn, Graylog)
    if matches!(
        filter,
        crate::InstanceFilter::One(crate::Instance::Ace) | crate::InstanceFilter::All
    ) {
        let ingress = ace_instance_ingress(&config)
            .await
            .change_context_lazy(|| ErrorStack::GetAceIngress)?;

        // Include the Ace instance here
        if let Some(ace) = &config.ace {
            let graphkey = crate::Instance::Ace;
            let name = graphkey.get_name();
            let public_hostname = format!("{}.{}", name, config.public_subdomain_name);
            let private_hostname = format!("{}.{}", name, config.private_subdomain_name);

            rval.push(Ok(Instance {
                graphkey: crate::Instance::Ace,
                name,
                subnet: crate::Subnet::Ace,
                instance_profile_role: None,
                instance_type: ace.instance_type.clone(),
                ami_graphkey: crate::Ami::Packer(crate::Packer::Ubuntu2204Ace2),
                volume_size: VolumeSize::Specific(ace.volume_size),
                ingress,
                use_elastic_ip: true,
                wildcard_dns: false,
                public_hostname,
                private_hostname,
                source: "built-in".to_string(),
            }));
        }
    }

    // vpn only exists if mentioned in config
    if config.vpn.is_some()
        && matches!(
            filter,
            crate::InstanceFilter::One(crate::Instance::Vpn) | crate::InstanceFilter::All
        )
    {
        let ingress = openvpn_ingress(&config)
            .await
            .change_context_lazy(|| ErrorStack::GetOpenVpnIngress)?;

        let graphkey = crate::Instance::Vpn;
        let name = graphkey.get_name();
        let public_hostname = format!("{}.{}", name, config.public_subdomain_name);
        let private_hostname = format!("{}.{}", name, config.private_subdomain_name);

        // Include the Vpn instance here
        rval.push(Ok(Instance {
            graphkey,
            name,
            subnet: crate::Subnet::Vpn,
            instance_profile_role: None,
            instance_type: "t3.micro".to_string(),
            ami_graphkey: crate::Ami::Packer(crate::Packer::Ubuntu2204Openvpn),
            volume_size: VolumeSize::DeterminedByAws,
            ingress,
            use_elastic_ip: true,
            wildcard_dns: false,
            public_hostname,
            private_hostname,
            source: "built-in".to_string(),
        }));
    }

    if config.deploy.is_some()
        && matches!(
            filter,
            crate::InstanceFilter::One(crate::Instance::Deploy) | crate::InstanceFilter::All
        )
    {
        let ingress = ace_instance_ingress(&config)
            .await
            .change_context_lazy(|| ErrorStack::GetAceIngress)?;

        let graphkey = crate::Instance::Deploy;
        let name = graphkey.get_name();
        let public_hostname = format!("{}.{}", name, config.public_subdomain_name);
        let private_hostname = format!("{}.{}", name, config.private_subdomain_name);

        // Include the Deploy instance here
        rval.push(Ok(Instance {
            graphkey,
            name,
            subnet: crate::Subnet::Ace,
            instance_profile_role: Some(crate::IamRole::Deploy),
            instance_type: "t3.small".to_string(),
            ami_graphkey: crate::Ami::Packer(crate::Packer::Ubuntu2404Docker),
            volume_size: VolumeSize::DeterminedByAws,
            ingress,
            use_elastic_ip: false,
            wildcard_dns: false,
            public_hostname,
            private_hostname,
            source: "built-in".to_string(),
        }));
    }

    // greylog only exists if mentioned in config
    if config.graylog.is_some()
        && matches!(
            filter,
            crate::InstanceFilter::One(crate::Instance::Graylog) | crate::InstanceFilter::All
        )
    {
        let ingress = graylog_ingress(&config)
            .await
            .change_context_lazy(|| ErrorStack::GetGraylogIngress)?;

        let graphkey = crate::Instance::Graylog;
        let name = graphkey.get_name();
        let public_hostname = format!("{}.{}", name, config.public_subdomain_name);
        let private_hostname = format!("{}.{}", name, config.private_subdomain_name);

        // Include the Graylog instance here
        rval.push(Ok(Instance {
            graphkey,
            name,
            subnet: crate::Subnet::Ace,
            instance_profile_role: None,
            instance_type: "t3.micro".to_string(),
            ami_graphkey: crate::Ami::Packer(crate::Packer::Ubuntu2204Docker),
            volume_size: VolumeSize::DeterminedByAws,
            ingress,
            use_elastic_ip: false,
            wildcard_dns: false,
            public_hostname,
            private_hostname,
            source: "built-in".to_string(),
        }));
    }

    let (db_filter, app_ins_filter, dev_ins_filter) = match &filter {
        crate::InstanceFilter::All => (
            ace_db::Filter::All,
            crate::InstanceFilter::All,
            crate::InstanceFilter::All,
        ),
        crate::InstanceFilter::One(ins_gk) => match ins_gk {
            crate::Instance::Ace => (
                ace_db::Filter::None,
                crate::InstanceFilter::None,
                crate::InstanceFilter::None,
            ),
            crate::Instance::App(_app) => (
                ace_db::Filter::None,
                crate::InstanceFilter::One(ins_gk.clone()),
                crate::InstanceFilter::None,
            ),
            crate::Instance::AppPreview(_app) => (
                ace_db::Filter::None,
                crate::InstanceFilter::One(ins_gk.clone()),
                crate::InstanceFilter::None,
            ),
            crate::Instance::Developer(_developer) => (
                ace_db::Filter::None,
                crate::InstanceFilter::None,
                crate::InstanceFilter::One(ins_gk.clone()),
            ),
            crate::Instance::Db(name) => (
                ace_db::Filter::One(name.clone()),
                crate::InstanceFilter::None,
                crate::InstanceFilter::None,
            ), // Only variant relevant to ace_db
            crate::Instance::Graylog => (
                ace_db::Filter::None,
                crate::InstanceFilter::None,
                crate::InstanceFilter::None,
            ),
            crate::Instance::Vpn => (
                ace_db::Filter::None,
                crate::InstanceFilter::None,
                crate::InstanceFilter::None,
            ),
            crate::Instance::Deploy => (
                ace_db::Filter::None,
                crate::InstanceFilter::None,
                crate::InstanceFilter::None,
            ),
        },
        crate::InstanceFilter::None => (
            ace_db::Filter::None,
            crate::InstanceFilter::None,
            crate::InstanceFilter::None,
        ),
    };

    // Collect from etc
    let etc_instances = ace_db::instance::select_result(db_filter, ace_db_app)
        .await
        .change_context_lazy(|| ErrorStack::SelectFromDb)?;

    for etc_instance in etc_instances {
        match etc_instance {
            Ok(etc_instance) => {
                let instance = construct(etc_instance, &config, ace_db_app)
                    .await
                    .change_context_lazy(|| ErrorStack::SelectFromDb)?;
                rval.push(Ok(instance));
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::SelectFromDb)));
            }
        }
    }

    let apps_instances = crate::app::select_result_instance(&app_ins_filter, ace_db_app)
        .await
        .change_context_lazy(|| ErrorStack::SelectFromApp)?;

    rval.extend(
        apps_instances
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromApp)),
    );

    // Collect from Developers
    rval.extend(
        crate::developer::select_result_instance(&dev_ins_filter, ace_db_app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromDeveloper)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromDeveloper)),
    );

    Ok(rval)
}

async fn construct(
    instance: ace_db::etc::instance::Instance,
    config: &crate::config::Config,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Instance, ErrorStack> {
    let mut ingress = Vec::new();

    for i_ingress in instance.ingress {
        ingress.push(Ingress {
            protocol: i_ingress.protocol,
            ports: match i_ingress.ports {
                ace_db::etc::instance::PortRange::One(port) => PortRange::One(port),
                ace_db::etc::instance::PortRange::Range(from, to) => PortRange::Range(from, to),
                ace_db::etc::instance::PortRange::All => PortRange::All,
            },
            cidr_blocks: CidrBlocks::Known(i_ingress.cidr_blocks),
        });
    }

    // Add some default ingress:
    // Allow inbound SSH traffic from vpc
    ingress.push(Ingress {
        protocol: "tcp".to_string(),
        ports: PortRange::One(22),
        cidr_blocks: CidrBlocks::MainVpcCidrBlock,
    });

    // Allow inbound SSH traffic from my ip
    ingress.push(Ingress {
        protocol: "tcp".to_string(),
        ports: PortRange::One(22),
        cidr_blocks: CidrBlocks::Known(vec![config.my_ipnetwork]),
    });

    // Deserialize the instance's ami_id to a crate::Ami (GraphKeyExt)
    let ami = match crate::GraphKeyExt::deserialize(&instance.ami) {
        Ok(a) => a,
        Err(e) => {
            error_stack::bail!(ErrorStack::DeserializeAmiGraphkey(e, instance.source));
        }
    };

    let subnet_gk = match crate::Subnet::deserialize(&instance.subnet) {
        Ok(s) => s,
        Err(e) => {
            error_stack::bail!(ErrorStack::DeserializeSubnetGraphkey(e, instance.source));
        }
    };

    let instance_profile_role = match instance.instance_profile {
        Some(gk_as_str) => {
            let gk: crate::IamRole = match crate::IamRole::deserialize(&gk_as_str) {
                Ok(gk) => gk,
                Err(e) => {
                    error_stack::bail!(ErrorStack::DeserializeIamRoleGraphkey(e, instance.source));
                }
            };

            // MAKE SURE THE IAMROLE REFERENCED ACTUALLY EXISTS!!!
            let _iam_role = crate::iam_role::get(gk.clone(), ace_db_app)
                .await
                .change_context_lazy(|| ErrorStack::VerifyIamRoleExists(gk.clone()))?;

            Some(gk)
        }
        None => None,
    };

    let graphkey = crate::Instance::Db(instance.name.clone());
    let name = graphkey.get_name();
    let public_hostname = construct_pub_hostname(&name, config);
    let private_hostname = construct_private_hostname(&name, config);

    Ok(Instance {
        graphkey,
        name,
        subnet: subnet_gk,
        instance_profile_role,
        instance_type: instance.instance_type,
        ami_graphkey: ami,
        volume_size: VolumeSize::Specific(instance.volume_size),
        ingress,
        use_elastic_ip: instance.use_elastic_ip,
        wildcard_dns: instance.wildcard_dns,
        public_hostname,
        private_hostname,
        source: instance.source.to_string_lossy().to_string(),
    })
}

pub fn construct_private_hostname(name: &str, config: &crate::config::Config) -> String {
    format!("{}.{}", name, &config.private_subdomain_name)
}

pub fn construct_pub_hostname(name: &str, config: &crate::config::Config) -> String {
    format!("{}.{}", name, &config.public_subdomain_name)
}

async fn ace_instance_ingress(
    config: &crate::config::Config,
) -> error_stack::Result<Vec<Ingress>, ErrorStack> {
    let mut rval = Vec::new();

    let ten_cidr_block = match "10.0.0.0/8".parse::<ipnetwork::Ipv4Network>() {
        Ok(c) => c,
        Err(e) => {
            error_stack::bail!(ErrorStack::IpNetworkParse(e));
        }
    };

    // Allow inbound SSH traffic from my ip
    rval.push(Ingress {
        ports: PortRange::One(22),
        protocol: "tcp".to_string(),
        cidr_blocks: CidrBlocks::Known(vec![ipnetwork::IpNetwork::V4(ten_cidr_block)]),
    });

    // allow inbound HTTPS traffic
    rval.push(Ingress {
        ports: PortRange::One(443),
        protocol: "tcp".to_string(),
        cidr_blocks: CidrBlocks::Known(vec![ipnetwork::IpNetwork::V4(ten_cidr_block)]),
    });

    // Allow inbound ICMP traffic from private network
    rval.push(Ingress {
        ports: PortRange::All,
        protocol: "icmp".to_string(),
        cidr_blocks: CidrBlocks::Known(vec![ipnetwork::IpNetwork::V4(ten_cidr_block)]),
    });

    // Allow inbound SSH traffic from my ip
    rval.push(Ingress {
        ports: PortRange::One(22),
        protocol: "tcp".to_string(),
        cidr_blocks: CidrBlocks::Known(vec![config.my_ipnetwork]),
    });

    if let Some(ace) = &config.ace {
        // Allow inbound SSH traffic
        rval.push(Ingress {
            ports: PortRange::One(22),
            protocol: "tcp".to_string(),
            cidr_blocks: CidrBlocks::Known(ace.allow_ssh_from.clone()),
        });
    }

    Ok(rval)
}

async fn openvpn_ingress(
    config: &crate::config::Config,
) -> error_stack::Result<Vec<Ingress>, ErrorStack> {
    let mut rval = vec![];

    // Allow inbound SSH traffic from vpc
    rval.push(Ingress {
        ports: PortRange::One(22),
        protocol: "tcp".to_string(),
        cidr_blocks: CidrBlocks::MainVpcCidrBlock,
    });

    // Allow inbound SSH traffic from my ip
    rval.push(Ingress {
        ports: PortRange::One(22),
        protocol: "tcp".to_string(),
        cidr_blocks: CidrBlocks::Known(vec![config.my_ipnetwork]),
    });

    let zero_cidr_block = match "0.0.0.0/0".parse::<ipnetwork::Ipv4Network>() {
        Ok(c) => c,
        Err(e) => {
            error_stack::bail!(ErrorStack::IpNetworkParse(e));
        }
    };

    // Allow openvpn traffic
    rval.push(Ingress {
        ports: PortRange::One(1194),
        protocol: "udp".to_string(),
        cidr_blocks: CidrBlocks::Known(vec![ipnetwork::IpNetwork::V4(zero_cidr_block)]),
    });

    Ok(rval)
}

async fn graylog_ingress(
    config: &crate::config::Config,
) -> error_stack::Result<Vec<Ingress>, ErrorStack> {
    let mut rval = vec![];

    // Allow inbound SSH traffic from vpc
    rval.push(Ingress {
        ports: PortRange::One(22),
        protocol: "tcp".to_string(),
        cidr_blocks: CidrBlocks::MainVpcCidrBlock,
    });

    // Allow inbound SSH traffic from my ip
    rval.push(Ingress {
        ports: PortRange::One(22),
        protocol: "tcp".to_string(),
        cidr_blocks: CidrBlocks::Known(vec![config.my_ipnetwork]),
    });

    let zero_cidr_block = match "0.0.0.0/0".parse::<ipnetwork::Ipv4Network>() {
        Ok(c) => c,
        Err(e) => {
            error_stack::bail!(ErrorStack::IpNetworkParse(e));
        }
    };

    rval.push(Ingress {
        ports: PortRange::One(80),
        protocol: "tcp".to_string(),
        cidr_blocks: CidrBlocks::Known(vec![ipnetwork::IpNetwork::V4(zero_cidr_block)]),
    });

    rval.push(Ingress {
        ports: PortRange::One(443),
        protocol: "tcp".to_string(),
        cidr_blocks: CidrBlocks::Known(vec![ipnetwork::IpNetwork::V4(zero_cidr_block)]),
    });

    Ok(rval)
}

pub async fn select_result_securitygroup(
    filter: &crate::AwsVpcSecurityGroupFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<
    Vec<error_stack::Result<crate::aws_vpc_sg::AwsVpcSecurityGroup, ErrorStack>>,
    ErrorStack,
> {
    let mut rval = Vec::new();

    let filter: crate::InstanceFilter = filter.into();

    for ins in select_result(&filter, ace_db_app)
        .await
        .change_context(ErrorStack::SelectInstances)?
    {
        match ins {
            Ok(instance) => {
                let securitygroup = crate::aws_vpc_sg::AwsVpcSecurityGroup {
                    graphkey: crate::AwsVpcSg::Instance(instance.graphkey.clone()),
                    name: format!("app-{}", instance.name),
                    ingress: instance.ingress,
                    description: "Allow SSH inbound traffic".to_string(),
                    source: instance.graphkey.to_string(),
                };

                rval.push(Ok(securitygroup));
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

use crate::{Graph<PERSON>eyExt, Graph<PERSON>eyName};
use error_stack::ResultExt;
use serde_json::json;
use std::collections::HashMap;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AccountRegionStructNotFound,
    ConstructPolicy,
    ConstructTlsCert(crate::TlsCert),
    DeserializeSubnet(String),
    GetConfig,
    GetCurrentAccount,
    GetDeveloper(crate::Developer),
    GetDeveloperApp(String, String),
    GetDeveloperFromDB(String),
    IpNetworkParse(ipnetwork::IpNetworkError),
    NotOneFound(crate::Developer),
    ParseAppGraphKey(crate::Developer, String),
    SelectDevelopersFromDB,
    SshPublicKeyFormatting(String),
}

#[derive(Debug, Clone)]
pub struct Developer {
    pub graphkey: crate::<PERSON><PERSON><PERSON>,
    pub name: String,
    pub subnet: crate::Subnet,
    pub secret: String,
    pub instance_type: String,
    pub volume_size: u32,
    pub dns_prefix: String,
    pub dns_wildcard_prefix: String,
    pub private_hostname: String,
    pub public_hostname: String,
    pub base_endpoint: String,
    pub applications: Vec<String>,
    pub connection: Option<String>,
    pub region: String,
    pub ssh_public_key: Option<String>,
    pub ace2_server_name: String,
    pub app_config_map: HashMap<AppName, ace_db::etc::developer::DeveloperAppConfig>,
    pub ace_legacy_auth: Option<String>,
    pub active: bool,
    pub source: std::path::PathBuf,
}

impl crate::GraphValueExt for Developer {}

impl GraphKeyName for crate::Developer {
    fn get_name(&self) -> String {
        match self {
            crate::Developer::Db(name) => name.clone(),
        }
    }
}

pub type DeveloperList = Vec<Developer>;
pub type AceEndpoint = String;
pub type AppName = String;
pub type Url = String;

impl Developer {
    #[must_use]
    /// Returns (`base_ace_endpoint`, list of: (`app name`, `app_url`))
    pub fn get_urls(&self) -> (AceEndpoint, Vec<(AppName, Url)>) {
        let mut urls: Vec<(AppName, Url)> = Vec::new();

        let ace_endpoint = format!(
            "https://{}:{}@{}",
            self.name, self.secret, self.ace2_server_name
        );

        for app_name in self.app_config_map.keys() {
            if app_name == "ctzen" {
                urls.push((
                    "ctzen".to_string(),
                    format!("{ace_endpoint}/cz/config?app=ctzen"),
                ));
            }
            if app_name == "ehungry" {
                urls.push((
                    "ehungry".to_string(),
                    format!("{ace_endpoint}/eh/config?app=ehungry"),
                ));
            }
        }

        (ace_endpoint, urls)
    }
}

pub async fn get(
    gk_developer: &crate::Developer,
    app: &ace_db::App,
) -> error_stack::Result<Developer, ErrorStack> {
    let filter = crate::DeveloperFilter::One(gk_developer.clone());
    let mut developers = select_result(&filter, app).await?;

    // Validate that one record was returned
    let developer: Result<Developer, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if developers.len() == 1 {
            developers.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_developer.clone()))
        }
    };

    developer.change_context_lazy(|| ErrorStack::GetDeveloper(gk_developer.to_owned()))
}

pub async fn select(
    filter: &crate::DeveloperFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<Developer>, ErrorStack> {
    // Fails at ANY error found (but not necessarily at "hey didn't find a match"?).
    let mut rval = Vec::new();

    for dev_result in select_result(filter, app).await? {
        match dev_result {
            Ok(developer) => {
                rval.push(developer);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::DeveloperFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<Developer, ErrorStack>>, ErrorStack> {
    let db_filter = match filter {
        crate::DeveloperFilter::All => ace_db::Filter::All,
        crate::DeveloperFilter::One(crate::Developer::Db(name)) => {
            ace_db::Filter::One(name.to_owned())
        }
        crate::DeveloperFilter::None => return Ok(Vec::new()), // exit early
    };

    let mut rval = Vec::new();

    let (current_account_key, current_region_key, ace2_server_name) = {
        let config = crate::config::get(&crate::Config::EtcConfig, app)
            .await
            .change_context(ErrorStack::GetConfig)?;

        (config.account_key, config.region, config.ace2_server_name)
    };

    let current_account = crate::aws_account::get(&crate::AwsAccount::Db(current_account_key), app)
        .await
        .change_context(ErrorStack::GetCurrentAccount)?;

    let Some(current_region) = current_account.region.get(&current_region_key) else {
        error_stack::bail!(ErrorStack::AccountRegionStructNotFound);
    };

    let developers = ace_db::developer::select_result(db_filter, app)
        .await
        .change_context_lazy(|| ErrorStack::SelectDevelopersFromDB)?;

    for developer in developers {
        match developer {
            Ok(d) => {
                let developer =
                    construct(d, &current_account, current_region, &ace2_server_name).await;
                rval.push(developer);
            }
            Err(e) => {
                rval.push(Err(e.change_context(ErrorStack::SelectDevelopersFromDB)));
            }
        }
    }

    Ok(rval)
}

async fn construct(
    db_developer: ace_db::etc::developer::Developer,
    current_account: &crate::aws_account::AwsAccount,
    current_region: &crate::aws_account::AwsAccountRegion,
    ace2_server_name: &str,
) -> error_stack::Result<Developer, ErrorStack> {
    let dev_graphkey = crate::Developer::Db(db_developer.name.clone());

    let parsed_subnet = crate::Subnet::deserialize(&db_developer.subnet).map_err(|e| {
        ErrorStack::DeserializeSubnet(format!(
            "while loading developer {} {}",
            db_developer.name, e
        ))
    })?;

    let private_domain_name = current_account.private_domain.clone();
    let public_domain_name = current_account.public_domain.clone();

    let dns_prefix = format!("{}.dev", db_developer.name);
    let dns_wildcard_prefix = format!("*.{dns_prefix}");

    let private_subdomain_name = format!("{}.{}", current_region.name, private_domain_name);
    let public_subdomain_name = format!("{}.{}", current_region.name, public_domain_name);

    let private_hostname = format!("{dns_prefix}.{private_subdomain_name}");
    let public_hostname = format!("{dns_prefix}.{public_subdomain_name}");

    let base_endpoint = format!("https://{public_hostname}");
    let region = current_region.name.clone();

    let mut applications: Vec<String> = db_developer.app.keys().cloned().collect();
    applications.sort();
    let connection = Some(format!("ssh app@{private_hostname}"));

    let ssh_public_key = match db_developer.ssh_public_key {
        Some(key) => {
            let key = key.trim().to_owned();
            Some(key)
        }
        None => None,
    };

    let developer = Developer {
        graphkey: dev_graphkey,
        name: db_developer.name,
        subnet: parsed_subnet,
        secret: db_developer.secret,
        instance_type: db_developer.instance_type,
        volume_size: db_developer.volume_size,
        dns_prefix,
        dns_wildcard_prefix,
        private_hostname,
        public_hostname,
        base_endpoint,
        applications,
        connection,
        region,
        ssh_public_key,
        ace2_server_name: ace2_server_name.to_string(),
        app_config_map: db_developer.app,
        ace_legacy_auth: db_developer.ace_legacy_auth,
        active: db_developer.active,
        source: db_developer.source,
    };

    Ok(developer)
}

pub async fn select_result_iam_role(
    filter: &crate::IamRoleFilter,
    ace_db_app: &ace_db::App,
    config: &crate::config::Config,
) -> error_stack::Result<Vec<error_stack::Result<crate::iam_role::IamRole, ErrorStack>>, ErrorStack>
{
    let mut rval = Vec::new();

    let dev_filter = match filter {
        crate::IamRoleFilter::All => crate::DeveloperFilter::All,
        crate::IamRoleFilter::One(crate::IamRole::Developer(name)) => {
            crate::DeveloperFilter::One(name.clone())
        }
        _ => return Ok(rval), // exit early
    };

    for developer in select_result(&dev_filter, ace_db_app).await? {
        match developer {
            Ok(developer) => {
                // let policy = construct_dev_iam_role_policy(&developer, ace_db_app, config).await.change_context(ErrorStack::ConstructPolicy)?;
                let role_name = format!(
                    "{}-{}-developer-{}",
                    config.account_key, config.region, developer.name
                );

                let iam_role = crate::iam_role::IamRole {
                    graphkey: crate::IamRole::Developer(developer.graphkey.clone()),
                    name: role_name,
                    role_type: crate::iam_role::IamRoleType::New {
                        assume_role_policy: json!({
                            "Version": "2012-10-17",
                            "Statement": [
                                {
                                    "Action": "sts:AssumeRole",
                                    "Principal": {
                                        "Service": "ec2.amazonaws.com"
                                    },
                                    "Effect": "Allow",
                                    "Sid": ""
                                }
                            ]
                        }),
                    },
                    with_instance_profile: true,
                    policy_statements: Vec::new(),
                    source: developer.graphkey.serialize(),
                };

                rval.push(Ok(iam_role));
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_instance(
    filter: &crate::InstanceFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::ins::Instance, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    let developer_filter: crate::DeveloperFilter = filter.into();

    for developer in select_result(&developer_filter, ace_db_app).await? {
        match developer {
            Ok(developer) => {
                let zero_cidr_block = match "0.0.0.0/0".parse::<ipnetwork::Ipv4Network>() {
                    Ok(c) => c,
                    Err(e) => {
                        error_stack::bail!(ErrorStack::IpNetworkParse(e));
                    }
                };

                // Default ingresses for dev-instances:
                let ingress = vec![
                    // Allow inbound ssh traffic from vpc
                    crate::ins::Ingress {
                        ports: crate::ins::PortRange::One(22),
                        protocol: "tcp".to_string(),
                        cidr_blocks: crate::ins::CidrBlocks::MainVpcCidrBlock,
                    },
                    // Allow inbound SSH
                    crate::ins::Ingress {
                        ports: crate::ins::PortRange::One(22),
                        protocol: "tcp".to_string(),
                        cidr_blocks: crate::ins::CidrBlocks::Known(vec![ipnetwork::IpNetwork::V4(
                            zero_cidr_block,
                        )]),
                    },
                    // Allow inbound HTTP
                    crate::ins::Ingress {
                        ports: crate::ins::PortRange::One(80),
                        protocol: "tcp".to_string(),
                        cidr_blocks: crate::ins::CidrBlocks::Known(vec![ipnetwork::IpNetwork::V4(
                            zero_cidr_block,
                        )]),
                    },
                    // Allow inbound HTTPS
                    crate::ins::Ingress {
                        ports: crate::ins::PortRange::One(443),
                        protocol: "tcp".to_string(),
                        cidr_blocks: crate::ins::CidrBlocks::Known(vec![ipnetwork::IpNetwork::V4(
                            zero_cidr_block,
                        )]),
                    },
                    // Allow inbound HTTPS on high order ports
                    crate::ins::Ingress {
                        ports: crate::ins::PortRange::Range(8000, 8999),
                        protocol: "tcp".to_string(),
                        cidr_blocks: crate::ins::CidrBlocks::Known(vec![ipnetwork::IpNetwork::V4(
                            zero_cidr_block,
                        )]),
                    },
                ];

                let config = crate::config::get(&crate::Config::EtcConfig, ace_db_app)
                    .await
                    .change_context_lazy(|| ErrorStack::GetConfig)?;

                let graphkey = crate::Instance::Developer(developer.graphkey.clone());
                let name = format!("{}.dev", developer.name);
                let public_hostname = super::ins::construct_pub_hostname(&name, &config);
                let private_hostname = super::ins::construct_private_hostname(&name, &config);

                let instance = crate::ins::Instance {
                    graphkey,
                    name,
                    ami_graphkey: crate::Ami::Packer(crate::Packer::Ubuntu2204Devbox),
                    subnet: developer.subnet.clone(),

                    // TODO: Not technically accurate, doing hot fix.  Profile resource hardcoded in terraform atm.
                    instance_profile_role: None,
                    instance_type: developer.instance_type,
                    volume_size: crate::ins::VolumeSize::Specific(developer.volume_size),
                    ingress,
                    use_elastic_ip: false,
                    wildcard_dns: true,
                    public_hostname,
                    private_hostname,
                    source: developer.graphkey.to_string(),
                };
                rval.push(Ok(instance));
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

pub async fn select_result_keypair(
    filter: &crate::KeyPairFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::keypair::KeyPair, ErrorStack>>, ErrorStack>
{
    let mut rval = Vec::new();

    //TODO: FIX123
    let developer_filter: crate::DeveloperFilter = filter.into();

    for developer in select_result(&developer_filter, app).await? {
        match developer {
            Ok(developer) => {
                if let Some(ssh_public_key) = developer.ssh_public_key {
                    let keypair = crate::keypair::KeyPair {
                        graphkey: crate::KeyPair::Developer(developer.graphkey.clone()),
                        name: format!("developer-{}-keypair", developer.name),
                        public_key: ssh_public_key,
                        source: developer.graphkey.to_string(),
                    };

                    rval.push(Ok(keypair));
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

// Lists tls certs for all filtered developers
pub async fn select_result_tlscert(
    filter: &crate::TlsCertFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<crate::tlscert::TlsCert, ErrorStack>>, ErrorStack>
{
    let mut rval = Vec::new();

    let developer_filter: crate::DeveloperFilter = filter.into();

    // Iterate over list of developers - filter happens in list()
    for dev_result in select_result(&developer_filter, app).await? {
        match dev_result {
            Ok(developer) => {
                let gk_priv =
                    crate::TlsCert::Developer(developer.graphkey.clone(), "wcpvt".to_string());
                let gk_pub =
                    crate::TlsCert::Developer(developer.graphkey.clone(), "wcpub".to_string());

                let pvt_canonical_alt_names = vec![format!("*.{}", developer.private_hostname)];
                let pub_canonical_alt_names = vec![format!("*.{}", developer.public_hostname)];

                let pvt_common_name = developer.private_hostname.clone();
                let pub_common_name = developer.public_hostname.clone();

                // **IMPORTANT** use TlsCert::new() to have it automatically read itself from the filesystem (if it exists)
                let priv_tlscert = crate::tlscert::TlsCert::new(
                    &gk_priv,
                    &pvt_common_name,
                    pvt_canonical_alt_names,
                    app,
                )
                .await
                .change_context(ErrorStack::ConstructTlsCert(gk_priv.clone()));

                let pub_tlscert = crate::tlscert::TlsCert::new(
                    &gk_pub,
                    &pub_common_name,
                    pub_canonical_alt_names,
                    app,
                )
                .await
                .change_context(ErrorStack::ConstructTlsCert(gk_pub.clone()));

                rval.push(priv_tlscert);
                rval.push(pub_tlscert);
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }

        // only process devserver-private if it has been asked for
        // ...
    }

    Ok(rval)
}

// /// Returns a tuple of (assume_role_policy, inline_policy)
// /// For use with ace_core::terraform::iam_role.rs/iam-role.tf.
// async fn construct_dev_iam_role_policy(
//     developer: &crate::developer::Developer,
//     ace_db_app: &ace_db::App,
//     config: &crate::config::Config,
// ) -> error_stack::Result<serde_json::Value, ErrorStack> {

//     let mut policy_statement_list = Vec::new();
//     let inline_policy_name = format!("{}-{}-developer-{}", config.account_key, config.region, developer.name);

//     // We are not using ecr at the moment, but this is very painful to figure out, so leaving it here for reference.
//     if false {
//         policy_statement_list.push(json!({
//             "Sid":"ecr-getauth",
//             "Effect":"Allow",
//             "Action":[
//             "ecr:GetAuthorizationToken"
//             ],
//             "Resource":"*"
//         }));

//         policy_statement_list.push(json!({
//             "Sid":"ecr-download",
//             "Effect":"Allow",
//             "Action":[
//             "ecr:GetDownloadUrlForLayer",
//             "ecr:BatchGetImage",
//             "ecr:BatchCheckLayerAvailability"
//             ],
//             "Resource":"*"
//         }));
//     }

//     for app_name in developer.app_config_map.keys() {
//         let app_gk = crate::App::Db(app_name.clone());
//         let developer_app = crate::developer_app::get(
//             &crate::DeveloperApp::Db(developer.graphkey.clone(), app_gk.clone()),
//             ace_db_app,
//         )
//         .await
//         .change_context_lazy(|| ErrorStack::GetDeveloperApp(developer.graphkey.serialize(), app_gk.serialize()))?;

//         for app_bucket in developer_app.developer_access_buckets {
//             // add statement for bucket-level permissions
//             policy_statement_list.push(json!({
//                 "Effect": "Allow",
//                 "Action": [
//                     "s3:ListBucket",
//                     "s3:ListBucketMultipartUploads",
//                     "s3:GetBucketLocation",
//                 ],
//                 "Resource": format!("arn:aws:s3:::{}", app_bucket.bucket_name)
//             }));

//             // add a statement for object-level permissions
//             policy_statement_list.push(json!({
//                 "Effect": "Allow",
//                 "Action": [
//                     "s3:GetObject",
//                     "s3:PutObject",
//                     "s3:DeleteObject",
//                     "s3:AbortMultipartUpload",
//                     "s3:ListMultipartUploadParts"
//                 ],
//                 "Resource": format!("arn:aws:s3:::{}/*", app_bucket.bucket_name)
//             }));
//         }
//     }

//     #[rustfmt::skip]
//     let dev_iam_role_assume_and_inline_policy = json!({
//             "Version": "2012-10-17",
//             "Statement": [
//                 {
//                     "Action": "sts:AssumeRole",
//                     "Principal": {
//                         "Service": "ec2.amazonaws.com"
//                     },
//                     "Effect": "Allow",
//                     "Sid": ""
//                 }
//             ]
//         });
//         // "#,
//         // if !policy_statement_list.is_empty() { CNSL!(r#"
//         //     inline_policy {
//         //         name = "#, JE!(inline_policy_name), r#"
//         //         policy = jsonencode({
//         //             "Version": "2012-10-17",
//         //             "Statement": "#, JE!(policy_statement_list), r#"
//         //         })
//         //     }
//         //     "# ) } else { CNSL!("") },
//             // r#"

//     // "#);

//     Ok(dev_iam_role_assume_and_inline_policy)
// }

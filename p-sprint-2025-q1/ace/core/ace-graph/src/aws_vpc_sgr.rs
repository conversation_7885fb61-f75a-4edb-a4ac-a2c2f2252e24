#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneSecurityGroupRuleFound(crate::AwsVpcSgRule, usize),
}

#[derive(Debug)]
pub struct SecurityGroupRule {
    pub graphkey: crate::AwsVpcSgRule,
    pub name: String,
    pub source: String,
    // Other information...
}

impl crate::GraphValueExt for SecurityGroupRule {}

pub async fn get(
    gk_sg_rule: crate::AwsVpcSgRule,
) -> error_stack::Result<SecurityGroupRule, ErrorStack> {
    let filter = crate::AwsVpcSecurityGroupRuleFilter::One(gk_sg_rule.clone());

    let mut sg_rules = select(filter).await?;
    if sg_rules.len() == 1 {
        return Ok(sg_rules.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneSecurityGroupRuleFound(
        gk_sg_rule,
        sg_rules.len()
    ));
}

pub async fn select(
    filter: crate::AwsVpcSecurityGroupRuleFilter,
) -> error_stack::Result<Vec<SecurityGroupRule>, ErrorStack> {
    let mut rval = Vec::new();

    for sg_rule in select_result(filter).await? {
        match sg_rule {
            Ok(sgr) => rval.push(sgr),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    _filter: crate::AwsVpcSecurityGroupRuleFilter,
) -> error_stack::Result<Vec<error_stack::Result<SecurityGroupRule, ErrorStack>>, ErrorStack> {
    Ok(vec![])
}

use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    GetAcePublicKey,
    GetEtcKeypairs,
    GetKeypairFromDb,
    GetKeypairFromDeveloper,
    GetKeypairFromUser,
    SelectKeypairFromDeveloper,
    SelectKeypairFromUser,
}

#[derive(Debug)]
pub struct KeyPair {
    pub graphkey: crate::KeyPair,
    pub name: String,
    pub public_key: String,
    pub source: String,
}

impl crate::GraphValueExt for KeyPair {}

pub async fn select(
    filter: &crate::KeyPairFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<KeyPair>, ErrorStack> {
    // Fails at ANY error found (but not necessarily at "hey didn't find a match"?).
    let mut rval = Vec::new();

    for keypair_result in select_result(filter, app).await? {
        match keypair_result {
            Ok(keypair) => {
                rval.push(keypair);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

/// TODO: Filtering by key number? (e.g. `Keypair::User(user, key_name))` <-- `key_name` is really "key-{number}"
pub async fn select_result(
    filter: &crate::KeyPairFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<KeyPair, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    #[allow(clippy::match_same_arms)]
    let (etc_filter, include_ace_keypair) = match filter {
        crate::KeyPairFilter::All => (ace_db::Filter::All, true),
        crate::KeyPairFilter::One(keypair_gk) => match keypair_gk {
            crate::KeyPair::Db(name) => (ace_db::Filter::One(name.clone()), false),
            crate::KeyPair::User(_, _) => (ace_db::Filter::All, false),
            crate::KeyPair::Developer(_) => (ace_db::Filter::All, false),
            crate::KeyPair::Ace => (ace_db::Filter::None, true),
        },
        crate::KeyPairFilter::None => return Ok(Vec::new()), // exit early
    };

    let dev_keypairs = crate::developer::select_result_keypair(filter, ace_db_app)
        .await
        .change_context_lazy(|| ErrorStack::SelectKeypairFromDeveloper)?
        .into_iter()
        .map(|x| x.change_context(ErrorStack::GetKeypairFromDeveloper));

    let user_keypairs = crate::user::select_result_keypair(filter, ace_db_app)
        .await
        .change_context_lazy(|| ErrorStack::SelectKeypairFromUser)?
        .into_iter()
        .map(|x| x.change_context(ErrorStack::GetKeypairFromUser));

    rval.extend(dev_keypairs);
    rval.extend(user_keypairs);

    if include_ace_keypair {
        let db_ace_keypair = ace_db::data::ace_public_key::get_public_ace_key(ace_db_app)
            .await
            .change_context(ErrorStack::GetAcePublicKey)?;

        if let Some(ace_keypair) = db_ace_keypair {
            rval.push(Ok(construct(ace_keypair)));
        }
    }

    // Get keypairs from etc and process the results
    match ace_db::keypair::select_result(etc_filter, ace_db_app).await {
        Ok(etc_keypairs) => {
            for keypair in etc_keypairs {
                match keypair {
                    Ok(keypair) => {
                        rval.push(Ok(construct(keypair)));
                    }
                    Err(e) => {
                        rval.push(Err(e.change_context(ErrorStack::GetKeypairFromDb)));
                    }
                }
            }
        }
        Err(e) => {
            rval.push(Err(e.change_context(ErrorStack::GetEtcKeypairs)));
        }
    }

    Ok(rval)
}

fn construct(keypair: ace_db::etc::keypair::KeyPair) -> KeyPair {
    let (name, graphkey) = match keypair.name.as_ref() {
        "ace" => ("ace".to_string(), crate::KeyPair::Ace),
        _ => (
            format!("etc-keypair-{}", keypair.name),
            crate::KeyPair::Db(keypair.name.clone()),
        ),
    };

    KeyPair {
        graphkey,
        name,
        public_key: keypair.public_key,
        source: keypair.source.to_string_lossy().to_string(),
    }
}

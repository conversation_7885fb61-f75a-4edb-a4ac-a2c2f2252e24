use crate::{<PERSON>raph<PERSON>eyExt, GraphKeyName};
use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    DbError,
    NotOneIamRoleFound(crate::IamRole, usize),
    SelectDbIamRoles,
    SelectFromApp,
    SelectFromBrsrc,
    SelectFromDev,
    GetConfig,
}

#[derive(Debug)]
pub struct IamRole {
    pub graphkey: crate::IamRole,
    pub name: String,
    pub role_type: IamRoleType,
    pub with_instance_profile: bool,
    pub policy_statements: Vec<serde_json::Value>,
    pub source: String,
}

#[derive(Debug)]
pub enum IamRoleType {
    Existing {
        iam_role_name: String,
    },
    New {
        assume_role_policy: serde_json::Value,
    },
}

pub type Resource = String;
pub type ResourceName = String;
pub type Comment = String;
pub type TagName = String;

impl IamRole {
    pub fn terraform_resource(
        &self,
        config: &crate::config::Config,
    ) -> (Resource, ResourceName, Comment, TagName) {
        match &self.role_type {
            IamRoleType::Existing { iam_role_name } => (
                iam_role_name.clone(),
                iam_role_name.clone(),
                "Referencing EXISTING IamRole.  Not a graphkey".to_string(),
                iam_role_name.clone(),
            ),
            IamRoleType::New { .. } => {
                let (resource, resource_name) = match &self.graphkey {
                    crate::IamRole::App(app_gk) => {
                        (format!("app-{}", app_gk.get_name()), self.name.clone())
                    }
                    crate::IamRole::AppPreview(app_gk) => (
                        format!("app-preview-{}", app_gk.get_name()),
                        self.name.clone(),
                    ),
                    crate::IamRole::Brsrc(brsrc_gk) => {
                        (format!("{}-brsrc", brsrc_gk.get_name()), self.name.clone())
                    }
                    crate::IamRole::Deploy => (
                        "deploy".to_string(),
                        format!("{}-{}-deploy", config.account_key, config.region),
                    ),
                    crate::IamRole::Developer(dev_gk) => {
                        let resource = format!("developer-{}", dev_gk.get_name());

                        (
                            resource.clone(),
                            format!("{}-{}-{resource}", config.account_key, config.region),
                        )
                    }
                    crate::IamRole::Db(_) => {
                        let dashed_gk = self.graphkey.serialize_dashed();
                        (dashed_gk.clone(), dashed_gk)
                    }
                };

                (
                    resource,
                    resource_name.clone(),
                    format!("Graphkey: {}", self.graphkey.serialize()),
                    resource_name,
                )
            }
        }
    }
}

impl crate::GraphValueExt for IamRole {}

pub async fn get(
    gk_iam_role: crate::IamRole,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<IamRole, ErrorStack> {
    let filter = crate::IamRoleFilter::One(gk_iam_role.clone());

    let mut iam_roles = select(filter, ace_db_app).await?;

    if iam_roles.len() == 1 {
        return Ok(iam_roles.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneIamRoleFound(gk_iam_role, iam_roles.len()));
}

pub async fn select(
    filter: crate::IamRoleFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<IamRole>, ErrorStack> {
    let mut rval = Vec::new();

    for iam_role in select_result(filter, ace_db_app).await? {
        match iam_role {
            Ok(role) => rval.push(role),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: crate::IamRoleFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<IamRole, ErrorStack>>, ErrorStack> {
    let mut rval = vec![];

    let db_filter = match &filter {
        crate::IamRoleFilter::All => ace_db::Filter::All,
        crate::IamRoleFilter::One(crate::IamRole::Db(name)) => ace_db::Filter::One(name.clone()),
        crate::IamRoleFilter::None => return Ok(rval),
        _ => ace_db::Filter::None,
    };

    let db_iam_roles = ace_db::iam_role::select_result(db_filter, ace_db_app)
        .await
        .change_context(ErrorStack::SelectDbIamRoles)?;

    for iam_role in db_iam_roles {
        match iam_role {
            Ok(role) => rval.push(Ok(construct(role))),
            Err(e) => rval.push(Err(e.change_context(ErrorStack::DbError))),
        }
    }

    let config = crate::config::get(&crate::Config::EtcConfig, ace_db_app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    if config.deploy.is_some()
        && matches!(
            filter,
            crate::IamRoleFilter::One(crate::IamRole::Deploy) | crate::IamRoleFilter::All
        )
    {
        rval.push(Ok(IamRole {
            graphkey: crate::IamRole::Deploy,
            name: "deploy".to_string(),
            role_type: IamRoleType::New {
                assume_role_policy: serde_json::json!({
                    "Version": "2012-10-17",
                    "Statement": [
                        {
                            "Action": "sts:AssumeRole",
                            "Principal": {
                                "Service": "ec2.amazonaws.com"
                            },
                            "Effect": "Allow",
                            "Sid": ""
                        }
                    ]
                }),
            },
            with_instance_profile: true,

            // allow push to ecr for any /app/* repo
            policy_statements: vec![
                serde_json::json!({
                    "Sid": "EcrGetAuth",
                    "Effect": "Allow",
                    "Action": [
                        "ecr:GetAuthorizationToken"
                    ],
                    "Resource": "*"
                }),
                serde_json::json!({
                    "Sid": "EcrPush",
                    "Effect": "Allow",
                    "Action": [
                        "ecr:BatchCheckLayerAvailability",
                        "ecr:GetDownloadUrlForLayer",
                        "ecr:GetRepositoryPolicy",
                        "ecr:DescribeRepositories",
                        "ecr:ListImages",
                        "ecr:DescribeImages",
                        "ecr:BatchGetImage",
                        "ecr:InitiateLayerUpload",
                        "ecr:UploadLayerPart",
                        "ecr:CompleteLayerUpload",
                        "ecr:PutImage",
                        "ecr:CreateRepository",
                        "ecr:ListImages"
                    ],
                    "Resource": [
                        format!("arn:aws:ecr:{}:{}:repository/app/*", config.region, config.aws_account_id),
                    ]
                })
            ],
            source: "built-in".to_string(),
        }));
    }

    rval.extend(
        crate::app::select_result_iam_role(&filter, ace_db_app, &config)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromApp)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromApp)),
    );

    rval.extend(
        crate::brsrc::select_result_iam_role(&filter, ace_db_app, &config)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromApp)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromBrsrc)),
    );

    rval.extend(
        crate::developer::select_result_iam_role(&filter, ace_db_app, &config)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromDev)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromDev)),
    );

    Ok(rval)
}

fn construct(db_iam_role: ace_db::etc::iam_role::IamRole) -> IamRole {
    let graphkey = crate::IamRole::Db(db_iam_role.name.clone());

    let role_type = match db_iam_role.role_type {
        ace_db::etc::iam_role::IamRoleType::Existing { iam_role_name } => {
            IamRoleType::Existing { iam_role_name }
        }
        ace_db::etc::iam_role::IamRoleType::New { policy } => IamRoleType::New {
            assume_role_policy: policy,
        },
    };

    IamRole {
        graphkey,
        name: db_iam_role.name,
        role_type,
        with_instance_profile: db_iam_role.with_instance_profile,
        policy_statements: Vec::new(),
        source: db_iam_role.source.to_string_lossy().to_string(),
    }
}

pub async fn select_result_instance_profile(
    filter: crate::IamRoleFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<
    Vec<error_stack::Result<crate::ins_profile::InstanceProfile, ErrorStack>>,
    ErrorStack,
> {
    let mut rval = vec![];

    let iam_roles = select_result(filter, ace_db_app).await?;

    for iam_role in iam_roles {
        match iam_role {
            Ok(role) => {
                if role.with_instance_profile {
                    let instance_profile = crate::ins_profile::InstanceProfile {
                        graphkey: crate::InstanceProfile::IamRole(role.graphkey.clone()),
                        name: role.name,
                        source: role.source,
                    };

                    rval.push(Ok(instance_profile));
                }
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

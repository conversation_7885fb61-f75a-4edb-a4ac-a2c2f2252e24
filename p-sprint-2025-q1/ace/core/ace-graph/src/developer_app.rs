use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    FilterOneByAppNotInDeveloper(crate::Developer, crate::App),
    GetApp(String),
    GetDeveloperApp(crate::DeveloperApp),
    GetDevelopers,
    GetDeveloper(crate::Developer),
    NotOneFound(crate::DeveloperApp),
}

#[derive(Debug, Clone)]
pub struct DeveloperApp {
    pub graphkey: crate::DeveloperApp,
    pub developer_name: String,
    pub ace_legacy_auth: Option<String>,
    pub conf_merged: toml::value::Table,
    pub developer_access_buckets: Vec<crate::app::AppBucket>,
    pub source: String,
}

impl crate::GraphValueExt for DeveloperApp {}

pub async fn get(
    gk_dev_app: &crate::DeveloperApp,
    app: &ace_db::App,
) -> error_stack::Result<DeveloperApp, ErrorStack> {
    let filter = crate::DeveloperAppFilter::One(gk_dev_app.clone());
    let mut devapps = select_result(&filter, app).await?;

    // Validate that one record was returned
    let devapp: Result<DeveloperApp, error_stack::Report<ErrorStack>> = {
        // if length != 1, return error
        if devapps.len() == 1 {
            devapps.pop().unwrap()
        } else {
            error_stack::bail!(ErrorStack::NotOneFound(gk_dev_app.clone()))
        }
    };

    devapp.change_context_lazy(|| ErrorStack::GetDeveloperApp(gk_dev_app.to_owned()))
}

pub async fn select(
    filter: &crate::DeveloperAppFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<DeveloperApp>, ErrorStack> {
    // Fails at ANY error found (but not necessarily at "hey didn't find a match"?).
    let mut rval = Vec::new();

    for devapp in select_result(filter, app).await? {
        match devapp {
            Ok(d) => {
                rval.push(d);
            }
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::DeveloperAppFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<DeveloperApp, ErrorStack>>, ErrorStack> {
    // Talks to ace_graph::dev::select()
    // Pilfers through each Developer, goes + get's the corresponding App.
    // Constructs DevApp.

    let mut rval = vec![];

    // app_gk is Option for...reasons (can check if DevApp actually is related to said App)
    let (dev_filter, app_gk) = match filter {
        crate::DeveloperAppFilter::All => (crate::DeveloperFilter::All, None),

        crate::DeveloperAppFilter::One(dev_app_gk) => match dev_app_gk {
            crate::DeveloperApp::Db(dev_gk, app_gk) => {
                (crate::DeveloperFilter::One(dev_gk.clone()), Some(app_gk))
            }
        },

        crate::DeveloperAppFilter::None => return Ok(rval),
    };

    // Get devs:
    let devs = crate::developer::select(&dev_filter, app)
        .await
        .change_context(ErrorStack::GetDevelopers)?;

    for dev in devs {
        // Are we only filtering by One?
        if let Some(app_gk) = app_gk {
            let crate::App::Db(app_name) = app_gk;

            // If so, does this app exist in the developer's app_config_map?
            match dev.app_config_map.get(app_name) {
                Some(dev_app_conf) => {
                    // Construct the DeveloperApp and push it to the rval
                    let dev_name = dev.name.clone();

                    rval.push(construct(dev_app_conf, dev.graphkey, dev_name, app_name, app).await);
                    return Ok(rval);
                }
                None => {
                    // Add an error and return.  (We're only filtering by one and it wasn't found!)
                    return Err(error_stack::report!(
                        ErrorStack::FilterOneByAppNotInDeveloper(
                            dev.graphkey.clone(),
                            app_gk.clone()
                        )
                    ));
                }
            }
        }

        // Not filtering by One (a.k.a, ALL)
        for (app_name, dev_app_conf) in &dev.app_config_map {
            let dev_name = dev.name.clone();
            rval.push(
                construct(
                    dev_app_conf,
                    dev.graphkey.clone(),
                    dev_name.clone(),
                    app_name,
                    app,
                )
                .await,
            );
        }
    }

    Ok(rval)
}

/// Constructs batches of `DeveloperApps`` at once (possibility of multiple per `ace_graph::dev::Developer`)
async fn construct(
    dev_app_conf: &ace_db::etc::developer::DeveloperAppConfig,
    dev_gk: crate::Developer,
    dev_name: String,
    app_name: &str,
    app: &ace_db::App,
) -> error_stack::Result<DeveloperApp, ErrorStack> {
    let developer = crate::developer::get(&dev_gk, app)
        .await
        .change_context_lazy(|| ErrorStack::GetDeveloper(dev_gk.clone()))?;

    let app = crate::app::get(app_name, app)
        .await
        .change_context_lazy(|| ErrorStack::GetApp(app_name.to_string()))?;

    let mut conf_merged = app.conf.clone();

    // merge conf_test
    for (k, v) in &app.conf_developer {
        conf_merged.insert(k.clone(), v.clone());
    }

    // Merge the developer config with the app config
    for (k, v) in &dev_app_conf.conf {
        conf_merged.insert(k.clone(), v.clone());
    }

    // Create a cloned list of AppBucket structs that have developer_access set to true
    // TODO: this should be derived in a direct way by querying crate::Bucket
    let developer_access_buckets = app
        .db_bucket_map
        .iter()
        .filter(|(_, bucket)| bucket.developer_read)
        .map(|(_, bucket)| bucket.clone())
        .collect();

    let dev_app_gk = crate::DeveloperApp::Db(dev_gk.clone(), app.graphkey.clone());

    let source = format!("{}+{}", dev_gk, app.graphkey);

    Ok(DeveloperApp {
        graphkey: dev_app_gk,
        developer_name: dev_name,
        ace_legacy_auth: developer.ace_legacy_auth,
        conf_merged,
        developer_access_buckets,
        source,
    })
}

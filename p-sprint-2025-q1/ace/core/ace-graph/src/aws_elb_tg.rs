use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneAwsElbTgFound(crate::AwsElbTg, usize),
    SelectFromApp,
}

#[derive(Debug, <PERSON><PERSON>, Copy)]
pub enum AwsElbTgProtocol {
    Tcp,
    Udp,
}
impl std::fmt::Display for AwsElbTgProtocol {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AwsElbTgProtocol::Tcp => write!(f, "TCP"),
            AwsElbTgProtocol::Udp => write!(f, "UDP"),
        }
    }
}

#[derive(Debug)]
pub struct AwsElbTg {
    pub graphkey: crate::AwsElbTg,
    pub name: String,
    pub protocol: AwsElbTgProtocol,
    pub purpose: String,
    pub port: u16,
    pub source: String,
}

impl crate::GraphValueExt for AwsElbTg {}

pub async fn get(
    gk_aws_elb_tg: crate::AwsElbTg,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<AwsElbTg, ErrorStack> {
    let filter = crate::AwsElbTgFilter::One(gk_aws_elb_tg.clone());

    let mut items = select(filter, ace_db_app).await?;
    if items.len() == 1 {
        return Ok(items.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneAwsElbTgFound(gk_aws_elb_tg, items.len()));
}

pub async fn select(
    filter: crate::AwsElbTgFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<AwsElbTg>, ErrorStack> {
    let mut rval = Vec::new();

    for items in select_result(filter, ace_db_app).await? {
        match items {
            Ok(aws_elb_tg) => rval.push(aws_elb_tg),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: crate::AwsElbTgFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<AwsElbTg, ErrorStack>>, ErrorStack> {
    let mut rval = Vec::new();

    rval.extend(
        crate::app::select_result_aws_elb_tg(&filter, ace_db_app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromApp)?
            .into_iter()
            .map(|v| v.change_context(ErrorStack::SelectFromApp)),
    );

    Ok(rval)
}

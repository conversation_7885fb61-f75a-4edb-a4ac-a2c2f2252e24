use std::vec;

use error_stack::FutureExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AcePrivateDomainNotFound,
    AcePublicDomainNotFound,
    GetConfig,
    HostedZoneIdIsNone(crate::config::HostedZone),
    NotOneDnsZoneFound(crate::DnsZone, usize),
    UnexpectedHostedZoneKey(String),
}

#[derive(Debug)]
pub struct DnsZone {
    pub graphkey: crate::DnsZone,
    pub name: String,
    pub hosted_zone_id: String,
    pub source: String,
}

impl crate::GraphValueExt for DnsZone {}

pub async fn get(
    gk_dns_zone: crate::DnsZone,
    app: &ace_db::App,
) -> error_stack::Result<DnsZone, ErrorStack> {
    let filter = crate::DnsZoneFilter::One(gk_dns_zone.clone());

    let mut dns_zones = select(&filter, app).await?;
    if dns_zones.len() == 1 {
        return Ok(dns_zones.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneDnsZoneFound(gk_dns_zone, dns_zones.len()));
}

pub async fn select(
    filter: &crate::DnsZoneFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<DnsZone>, ErrorStack> {
    let mut rval = Vec::new();

    for dns_zone in select_result(filter, app).await? {
        match dns_zone {
            Ok(zone) => rval.push(zone),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: &crate::DnsZoneFilter,
    app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<DnsZone, ErrorStack>>, ErrorStack> {
    let config = crate::config::get(&crate::Config::EtcConfig, app)
        .change_context(ErrorStack::GetConfig)
        .await?;
    let mut rval = vec![];

    match filter {
        crate::DnsZoneFilter::One(gk_dns_zone) => match gk_dns_zone {
            crate::DnsZone::AcePrivateDomain => {
                if let Some(dns_zone) = config.hosted_zone_map.get("ace-private-domain") {
                    rval.push(construct(dns_zone.clone(), &config.graphkey));
                }

                rval.push(Err(error_stack::report!(
                    ErrorStack::AcePrivateDomainNotFound
                )));
            }
            crate::DnsZone::AcePublicDomain => {
                if let Some(dns_zone) = config.hosted_zone_map.get("ace-public-domain") {
                    rval.push(construct(dns_zone.clone(), &config.graphkey));
                }

                rval.push(Err(error_stack::report!(
                    ErrorStack::AcePublicDomainNotFound
                )));
            }
        },
        crate::DnsZoneFilter::All => {
            for dns_zone in config.hosted_zone_map.values() {
                rval.push(construct(dns_zone.clone(), &config.graphkey));
            }
        }
        crate::DnsZoneFilter::None => return Ok(rval),
    }

    Ok(rval)
}

fn construct(
    hosted_zone: crate::config::HostedZone,
    config_source: &crate::Config,
) -> error_stack::Result<DnsZone, ErrorStack> {
    let graphkey = match hosted_zone.key.as_str() {
        "public_domain" => crate::DnsZone::AcePublicDomain,
        "private_domain" => crate::DnsZone::AcePrivateDomain,
        _ => {
            error_stack::bail!(ErrorStack::UnexpectedHostedZoneKey(hosted_zone.key.clone()));
        }
    };

    if let Some(source) = hosted_zone.hosted_zone_id {
        return Ok(DnsZone {
            graphkey,
            name: hosted_zone.key,
            hosted_zone_id: source,
            source: config_source.to_string(),
        });
    }

    error_stack::bail!(ErrorStack::HostedZoneIdIsNone(hosted_zone));
}

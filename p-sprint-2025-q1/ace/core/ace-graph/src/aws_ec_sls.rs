use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    NotOneAwsEcSlsFound(crate::AwsEcSls, usize),
    SelectFromApp,
}

#[derive(Debug)]
pub struct AwsEcSls {
    pub graphkey: crate::AwsEcSls,
    pub name: String,
    pub purpose: String,
    pub description: Option<String>,
    pub engine_version: String,
    pub backup_daily: bool,
    pub subnets: Vec<crate::Subnet>,
    pub source: String,
}

impl crate::GraphValueExt for AwsEcSls {}

pub async fn get(
    gk_aws_ec_sls: crate::AwsEcSls,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<AwsEcSls, ErrorStack> {
    let filter = crate::AwsEcSlsFilter::One(gk_aws_ec_sls.clone());

    let mut items = select(filter, ace_db_app).await?;
    if items.len() == 1 {
        return Ok(items.pop().unwrap());
    }

    error_stack::bail!(ErrorStack::NotOneAwsEcSlsFound(gk_aws_ec_sls, items.len()));
}

pub async fn select(
    filter: crate::AwsEcSlsFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<AwsEcSls>, ErrorStack> {
    let mut rval = Vec::new();

    for items in select_result(filter, ace_db_app).await? {
        match items {
            Ok(aws_ec_sls) => rval.push(aws_ec_sls),
            Err(e) => {
                return Err(e);
            }
        }
    }

    Ok(rval)
}

pub async fn select_result(
    filter: crate::AwsEcSlsFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<Vec<error_stack::Result<AwsEcSls, ErrorStack>>, ErrorStack> {
    let mut rval = vec![];

    // Since AwsEcSls only depends on Apps...
    rval.extend(
        crate::app::select_result_aws_ec_sls(&filter, ace_db_app)
            .await
            .change_context_lazy(|| ErrorStack::SelectFromApp)?
            .into_iter()
            .map(|r| r.change_context_lazy(|| ErrorStack::SelectFromApp)),
    );

    Ok(rval)
}

pub async fn select_result_securitygroup(
    filter: &crate::AwsVpcSecurityGroupFilter,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<
    Vec<error_stack::Result<crate::aws_vpc_sg::AwsVpcSecurityGroup, ErrorStack>>,
    ErrorStack,
> {
    let mut rval = Vec::new();

    let filter = match filter {
        crate::AwsVpcSecurityGroupFilter::All => crate::AwsEcSlsFilter::All,
        crate::AwsVpcSecurityGroupFilter::One(sg_gk) => match sg_gk {
            crate::AwsVpcSg::AwsEcSls(aws_ec_sls) => crate::AwsEcSlsFilter::One(aws_ec_sls.clone()),
            _ => return Ok(rval),
        },
        crate::AwsVpcSecurityGroupFilter::None => return Ok(rval),
    };

    for aws_ec_sls in select_result(filter, ace_db_app).await? {
        match aws_ec_sls {
            Ok(aws_ec_sls) => {
                let ingress = vec![
                    // Allow inbound Redis traffic from vpc
                    crate::ins::Ingress {
                        ports: crate::ins::PortRange::One(6379),
                        protocol: "tcp".to_string(),
                        cidr_blocks: crate::ins::CidrBlocks::MainVpcCidrBlock,
                    },
                ];

                rval.push(Ok(crate::aws_vpc_sg::AwsVpcSecurityGroup {
                    graphkey: crate::AwsVpcSg::AwsEcSls(aws_ec_sls.graphkey.clone()),
                    name: aws_ec_sls.name,
                    ingress,
                    description: "Allow Redis inbound traffic".to_string(),
                    source: aws_ec_sls.graphkey.to_string(),
                }));
            }
            Err(e) => {
                rval.push(Err(e));
            }
        }
    }

    Ok(rval)
}

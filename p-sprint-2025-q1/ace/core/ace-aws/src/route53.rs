use aws_sdk_route53::model::{
    Change, ChangeAction, ChangeBatch, ResourceRecord, ResourceRecordSet, RrType,
};
use error_stack::ResultExt;
use std::time::Duration;
use tokio::time::sleep;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CallbackError,
    ChangeActionDelete,
    ChangeFailed(String),
    ChangeIDExpected,
    ChangeInfoExpected,
    GetChangeInfo,
    GetChangeRequest,
}

pub type AsyncCallback<T, E> = std::pin::Pin<
    Box<dyn std::future::Future<Output = error_stack::Result<T, E>> + core::marker::Send>,
>;

pub struct ClientWrapper {
    client: aws_sdk_route53::Client,
}

pub async fn open() -> error_stack::Result<ClientWrapper, ErrorStack> {
    ClientWrapper::open().await
}

impl ClientWrapper {
    pub async fn open() -> error_stack::Result<Self, ErrorStack> {
        let config = aws_config::load_from_env().await;
        let client = aws_sdk_route53::Client::new(&config);
        Ok(Self { client })
    }

    /// Creates and destroys a transient CNAME record in the specified hosted zone.
    ///
    /// While it exists, after it has been fully propagated, the callback is called.  
    ///
    /// The type parmaters are the types of the callback return:  
    /// * `T`: The type of the callback return value -- used to return from this function
    /// * `E`: The type of the callback error enum -- only used to define the callback signature
    pub async fn transient_cname_record<T, E>(
        &self,
        hosted_zone_id: &str,
        name: &str,
        value: &str,
        callback: AsyncCallback<T, E>,
    ) -> error_stack::Result<T, ErrorStack>
    where
        T: std::marker::Send + std::marker::Sync + 'static,
    {
        let gcb = |change_action| {
            ChangeBatch::builder()
                .changes(
                    Change::builder()
                        .action(change_action)
                        .resource_record_set(
                            ResourceRecordSet::builder()
                                .set_name(Some(name.to_string()))
                                .set_type(Some(RrType::Cname))
                                .set_ttl(Some(300))
                                .resource_records(ResourceRecord::builder().value(value).build())
                                .build(),
                        )
                        .build(),
                )
                .build()
        };

        let change_info = self
            .client
            .change_resource_record_sets()
            .hosted_zone_id(hosted_zone_id)
            .change_batch(gcb(ChangeAction::Create))
            .send()
            .await
            .change_context(ErrorStack::GetChangeInfo)?
            .change_info;

        let change_id = match change_info {
            Some(change_info) => match change_info.id {
                Some(change_id) => change_id,
                None => {
                    error_stack::bail!(ErrorStack::ChangeIDExpected)
                }
            },
            None => {
                error_stack::bail!(ErrorStack::ChangeInfoExpected)
            }
        };

        self.wait_for_change(&change_id).await?;

        let rval = match callback.await {
            Ok(rval) => error_stack::Result::Ok(rval),
            Err(e) => error_stack::Result::Err(e.change_context(ErrorStack::CallbackError)),
        };

        // This MUST be called regardless of the rval, which is why we have the match statement
        self.client
            .change_resource_record_sets()
            .hosted_zone_id(hosted_zone_id)
            .change_batch(gcb(ChangeAction::Delete))
            .send()
            .await
            .change_context(ErrorStack::ChangeActionDelete)?;

        rval
    }

    pub async fn wait_for_change(&self, change_id: &str) -> error_stack::Result<(), ErrorStack> {
        for _i in 0..20 {
            sleep(Duration::from_secs(5)).await; // Wait 5 seconds before checking again
            println!("Checking status of change {change_id}");
            let change_request = self
                .client
                .get_change()
                .id(change_id)
                .send()
                .await
                .change_context(ErrorStack::GetChangeRequest)?;
            if change_request
                .change_info()
                .expect("change info expected")
                .status
                == Some("INSYNC".into())
            {
                return Ok(());
            }
        }

        error_stack::bail!(ErrorStack::ChangeFailed(change_id.to_string()))
    }
}

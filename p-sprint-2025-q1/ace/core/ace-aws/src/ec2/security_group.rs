use std::collections::HashMap;

type RuleKey = (i32, i32, String);

pub mod native {
    use ipnetwork::IpNetwork;

    #[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
    pub struct SecurityGroup {
        pub id: String,
        pub name: String,
        pub description: String,
        pub vpc_id: String,
        pub ingress: Vec<SecurityGroupRule>,
        pub egress: Vec<SecurityGroupRule>,
    }

    #[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
    pub struct SecurityGroupRule {
        pub from_port: i32,
        pub to_port: i32,
        pub protocol: String,
        pub cidr_blocks: Vec<IpNetwork>,
    }
}

use ipnetwork::IpNetwork;

pub fn builder(
    id: String,
    name: String,
    description: String,
    vpc_id: String,
) -> SecurityGroupBuilder {
    SecurityGroupBuilder {
        id,
        name,
        description,
        vpc_id,
        ..Default::default()
    }
}

#[derive(Debug, Default)]
pub struct SecurityGroupBuilder {
    pub id: String,
    pub name: String,
    pub description: String,
    pub vpc_id: String,
    pub ingress: Vec<SecurityGroupRule>,
    pub egress: Vec<SecurityGroupRule>,
}

impl SecurityGroupBuilder {
    pub fn egress(&mut self, rule: SecurityGroupRule) -> &mut Self {
        self.egress.push(rule);
        self
    }

    pub fn egress_all_traffic(&mut self) -> &mut Self {
        self.egress.push(SecurityGroupRule::AllTraffic);
        self
    }

    pub fn ingress(&mut self, rule: SecurityGroupRule) -> &mut Self {
        self.ingress.push(rule);
        self
    }

    pub fn ingress_all_icmp(&mut self) -> &mut Self {
        self.ingress.push(SecurityGroupRule::AllIcmp);
        self
    }

    pub fn ingress_ssh(&mut self, cidr_block: IpNetwork) -> &mut Self {
        self.ingress.push(SecurityGroupRule::Ssh(cidr_block));
        self
    }

    pub fn ingress_http(&mut self, cidr_block: IpNetwork) -> &mut Self {
        self.ingress.push(SecurityGroupRule::Http(cidr_block));
        self
    }

    pub fn ingress_https(&mut self, cidr_block: IpNetwork) -> &mut Self {
        self.ingress.push(SecurityGroupRule::Https(cidr_block));
        self
    }

    pub fn ingress_custom_tcp(&mut self, port: i32, cidr_block: IpNetwork) -> &mut Self {
        self.ingress
            .push(SecurityGroupRule::CustomTcp(port, cidr_block));
        self
    }

    pub fn ingress_custom_udp(&mut self, port: i32, cidr_block: IpNetwork) -> &mut Self {
        self.ingress
            .push(SecurityGroupRule::CustomUdp(port, cidr_block));
        self
    }

    pub fn ingress_custom(
        &mut self,
        from_port: i32,
        to_port: i32,
        protocol: String,
        cidr_block: IpNetwork,
    ) -> &mut Self {
        self.ingress.push(SecurityGroupRule::Custom {
            from_port,
            to_port,
            protocol,
            cidr_block,
        });
        self
    }

    pub fn build(&mut self) -> native::SecurityGroup {
        let process_and_deduplicate_rules = |rules: &Vec<SecurityGroupRule>| {
            let mut map: HashMap<RuleKey, Vec<IpNetwork>> = HashMap::new();
            for rule in rules {
                let (key, value) = rule.get_key_value_tuples();
                let entry = map.entry(key).or_default();
                // push if not exists
                if !entry.contains(&value) {
                    entry.push(value);
                }
            }
            let mut list = vec![];
            for (key, value) in map {
                list.push(native::SecurityGroupRule {
                    from_port: key.0,
                    to_port: key.1,
                    protocol: key.2,
                    cidr_blocks: value,
                });
            }
            list.sort();
            list
        };

        let ingress = process_and_deduplicate_rules(&self.ingress);
        let egress = process_and_deduplicate_rules(&self.egress);

        native::SecurityGroup {
            id: self.id.clone(),
            name: self.name.clone(),
            description: self.description.clone(),
            vpc_id: self.vpc_id.clone(),
            ingress,
            egress,
        }
    }
}

pub struct SecurityGroup {
    pub id: String,
    pub name: String,
    pub description: String,
    pub vpc_id: String,
    pub ingress: Vec<SecurityGroupRule>,
    pub egress: Vec<SecurityGroupRule>,
}

#[derive(Debug, Clone)]
pub enum SecurityGroupRule {
    AllTraffic,
    AllIcmp,
    Ssh(IpNetwork),
    Http(IpNetwork),
    Https(IpNetwork),
    CustomTcp(i32, IpNetwork),
    CustomUdp(i32, IpNetwork),
    Custom {
        from_port: i32,
        to_port: i32,
        protocol: String,
        cidr_block: IpNetwork,
    },
}

impl SecurityGroupRule {
    fn get_key_value_tuples(&self) -> (RuleKey, IpNetwork) {
        let v = match self {
            SecurityGroupRule::AllTraffic => {
                (0, 65535, "all".to_string(), "0.0.0.0/0".parse().unwrap())
            }
            SecurityGroupRule::AllIcmp => {
                (-1, -1, "icmp".to_string(), "0.0.0.0/0".parse().unwrap())
            }
            SecurityGroupRule::Ssh(nw) => (22, 22, "tcp".to_string(), *nw),
            SecurityGroupRule::Http(nw) => (80, 80, "tcp".to_string(), *nw),
            SecurityGroupRule::Https(nw) => (443, 443, "tcp".to_string(), *nw),
            SecurityGroupRule::CustomTcp(port, nw) => (*port, *port, "tcp".to_string(), *nw),
            SecurityGroupRule::CustomUdp(port, nw) => (*port, *port, "udp".to_string(), *nw),
            SecurityGroupRule::Custom {
                from_port,
                to_port,
                protocol,
                cidr_block,
            } => (*from_port, *to_port, protocol.clone(), *cidr_block),
        };

        ((v.0, v.1, v.2), v.3)
    }
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Add tests
#[cfg(test)]
pub mod tests {

    #[test]
    fn test_deduplication_mut() {
        let mut builder = super::builder(
            "sg-1234567890".to_string(),
            "my-security-group".to_string(),
            "my security group".to_string(),
            "vpc-1234567890".to_string(),
        );

        builder.ingress_all_icmp();
        builder.ingress_all_icmp();
        builder.ingress_all_icmp();

        builder.ingress_ssh("1.1.1.1/32".parse().unwrap());
        builder.ingress_ssh("1.1.1.1/32".parse().unwrap());
        builder.ingress_ssh("2.2.2.2/32".parse().unwrap());
        builder.ingress_ssh("2.2.2.2/32".parse().unwrap());
        builder.ingress_custom_tcp(22, "1.1.1.1/32".parse().unwrap());
        builder.ingress_custom_tcp(22, "2.2.2.2/32".parse().unwrap());
        builder.ingress_custom(22, 22, "tcp".to_string(), "1.1.1.1/32".parse().unwrap());
        builder.ingress_custom(22, 22, "tcp".to_string(), "2.2.2.2/32".parse().unwrap());

        let sg = builder.build();

        println!("Contents of test_deduplication_mut():\n{sg:#?}");

        assert_eq!(sg.ingress.len(), 2);
        assert_eq!(sg.ingress[0].cidr_blocks.len(), 1);
        assert_eq!(sg.ingress[1].cidr_blocks.len(), 2);
    }

    #[test]
    fn test_deduplication_immut() {
        let sg = super::builder(
            "sg-1234567890".to_string(),
            "my-security-group".to_string(),
            "my security group".to_string(),
            "vpc-1234567890".to_string(),
        )
        .ingress_all_icmp()
        .ingress_all_icmp()
        .ingress_all_icmp()
        .ingress_ssh("1.1.1.1/32".parse().unwrap())
        .ingress_ssh("1.1.1.1/32".parse().unwrap())
        .ingress_ssh("2.2.2.2/32".parse().unwrap())
        .ingress_ssh("2.2.2.2/32".parse().unwrap())
        .ingress_custom_tcp(22, "1.1.1.1/32".parse().unwrap())
        .ingress_custom_tcp(22, "2.2.2.2/32".parse().unwrap())
        .ingress_custom(22, 22, "tcp".to_string(), "1.1.1.1/32".parse().unwrap())
        .ingress_custom(22, 22, "tcp".to_string(), "2.2.2.2/32".parse().unwrap())
        .build();

        println!("Contents of test_deduplication_immut():\n{sg:#?}");

        assert_eq!(sg.ingress.len(), 2);
        assert_eq!(sg.ingress[0].cidr_blocks.len(), 1);
        assert_eq!(sg.ingress[1].cidr_blocks.len(), 2);
    }
}

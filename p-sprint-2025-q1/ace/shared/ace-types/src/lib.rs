#![allow(non_camel_case_types)]
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct Message_s2a {
    pub msg_id: String,
    pub job: Job_s2a,
    pub timestamp: i64,
    pub msg_type: Message_Type,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Message_a2s {
    pub msg_id: String,
    pub job: Job_a2s,
    pub timestamp: i64,
    pub msg_type: Message_Type,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum Job_s2a {
    SystemInfo(SystemInfo_s2a),
    NotAuthorized,
    Ping,
    Pong,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum Job_a2s {
    SystemInfo(SystemInfo_a2s),
    Ping,
    Pong,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
/// DiskSpaceCheck report/result for agent -> server
pub enum DiskSpaceCheck_a2s {
    Ok { total_space: u64, free_space: u64 },
    <PERSON><PERSON><PERSON>(String),
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum Message_Type {
    Prompt,
    Reply,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SystemInfo_s2a;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum SystemInfo_a2s {
    Ok(SystemInfo),
    Error(String),
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SystemInfo {
    pub root_disk: DiskSpaceCheck_a2s,
    pub total_memory: u64,
    pub used_memory: u64,
    pub system_name: Option<String>,
    pub system_kernel_version: Option<String>,
    pub system_os_version: Option<String>,
}

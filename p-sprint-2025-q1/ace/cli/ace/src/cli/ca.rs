use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AutoCommit,
    InitializeCa,
}

pub async fn init(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    ace_core::ca::init(app)
        .await
        .change_context(ErrorStack::InitializeCa)?;
    ace_core::git::autocommit(app, &"auto commit after vpn init".to_string())
        .await
        .change_context(ErrorStack::AutoCommit)?;

    Ok(())
}

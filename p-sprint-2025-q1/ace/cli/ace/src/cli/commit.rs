use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AutoCommit,
}

pub async fn run(
    app: &ace_core::Application,
    message: &Option<String>,
) -> error_stack::Result<(), ErrorStack> {
    let message = message.clone().unwrap_or("manual commit".to_string());

    ace_core::git::autocommit(app, &message)
        .await
        .change_context(ErrorStack::AutoCommit)?;
    Ok(())
}

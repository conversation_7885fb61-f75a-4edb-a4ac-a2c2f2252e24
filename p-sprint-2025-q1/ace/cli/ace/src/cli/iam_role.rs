use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};
pub async fn list(verbose: bool, app: &ace_core::Application) {
    let iam_roles_result =
        ace_graph::iam_role::select_result(ace_graph::IamRoleFilter::All, &app.ace_db_app).await;
    let mut errors = Vec::new();
    let mut iam_roles_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid IAM role entries
    match iam_roles_result {
        Ok(iam_roles) => {
            for iam_role in iam_roles {
                match iam_role {
                    Ok(iam_role) => iam_roles_sorted.push(iam_role),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            println!("Error fetching IAM roles: {e:#?}");
            return;
        }
    }

    // Sort IAM roles by their graphkey
    iam_roles_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for iam_role in iam_roles_sorted {
            println!("\n{iam_role:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Type", "Source"]);
        for iam_role in iam_roles_sorted {
            let role_type = match iam_role.role_type {
                ace_graph::iam_role::IamRoleType::Existing { .. } => "Exists in AWS".to_string(),
                ace_graph::iam_role::IamRoleType::New { .. } => "Defined Here".to_string(),
            };

            table.add_row(vec![
                iam_role.graphkey.to_string(),
                role_type,
                iam_role.source,
            ]);
        }

        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{error:#?}");
        }
    }
}

pub async fn info(gk_iam_role: &str, app: &ace_core::Application) {
    let iam_role_key = match ace_graph::IamRole::deserialize(gk_iam_role) {
        Ok(key) => key,
        Err(e) => {
            eprintln!("Error parsing IAM Role graphkey: {e}");
            return;
        }
    };

    let iam_role = match ace_graph::iam_role::get(iam_role_key, &app.ace_db_app).await {
        Ok(role) => role,
        Err(e) => {
            eprintln!("Error fetching IAM Role: {e:#?}");
            return;
        }
    };

    println!("{iam_role:#?}");
}

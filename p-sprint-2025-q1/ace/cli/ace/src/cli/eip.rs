use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool) {
    let eips_result = ace_graph::eip::select_result(ace_graph::EipFilter::All).await;
    let mut errors = Vec::new();
    let mut eips_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid EIP entries
    match eips_result {
        Ok(eips) => {
            for eip in eips {
                match eip {
                    Ok(eip) => eips_sorted.push(eip),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching EIPs: {e:#?}");
            return;
        }
    }

    // Sort EIP entries by their graphkey
    eips_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for eip in eips_sorted {
            println!("{eip:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for eip in eips_sorted {
            table.add_row(vec![eip.graphkey.to_string()]);
        }

        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{error:#?}");
        }
    }
}

use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    InvalidGraphKey(String, String),
    GetInstance(String),
}

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let instances_result =
        ace_graph::ins::select_result(&ace_graph::InstanceFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut instances_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid instance entries
    match instances_result {
        Ok(instances) => {
            for instance in instances {
                match instance {
                    Ok(instance) => instances_sorted.push(instance),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching instances: {e:#?}");
            return;
        }
    }

    // Sort instances by their graphkey
    instances_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for instance in instances_sorted {
            println!("\n{instance:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Hostname", "Graphkey", "Instance Type", "Subnet"]);

        for instance in instances_sorted {
            let row = vec![
                instance.private_hostname.to_string(),
                instance.graphkey.to_string(),
                instance.instance_type.to_string(),
                instance.subnet.to_string(),
            ];
            table.add_row(row);
        }

        println!("{table}");
    }

    println!();
    println!(
        "Note: Until the InstanceProfile graphtype is fully integrated into terraform, its information displayed here may not be completely accurate."
    );
    println!(
        "Developers and Apps DO currently have instance profiles and roles that are hardcoded."
    );
    println!();

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("Error: {error:#?}");
        }
    }
}

pub async fn info(gk_instance: &str, ace_db_app: &ace_db::App) -> Result<(), ErrorStack> {
    let instance_key = ace_graph::Instance::deserialize(gk_instance)
        .map_err(|e| ErrorStack::InvalidGraphKey(gk_instance.to_owned(), e.to_string()))?;

    let instance = ace_graph::ins::get(&instance_key, ace_db_app)
        .await
        .map_err(|e| ErrorStack::GetInstance(e.to_string()))?;

    println!("Instance Summary: {instance:#?}");
    Ok(())
}

use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let nlb_result =
        ace_graph::aws_elb::select_result(ace_graph::AwsElbFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut nlbs_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid NLB entries
    match nlb_result {
        Ok(nlbs) => {
            for nlb in nlbs {
                match nlb {
                    Ok(nlb) => nlbs_sorted.push(nlb),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching AWS ELB Network Load Balancers: {e:#?}");
            return;
        }
    }

    // Sort NLBs by their graphkey
    nlbs_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for nlb in nlbs_sorted {
            println!("\n{nlb:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Name", "Subnets", "Cross Zone"]);

        for nlb in nlbs_sorted {
            let cross_zone = if nlb.enable_cross_zone_load_balancing {
                "Yes"
            } else {
                "No"
            };
            let subnets = nlb
                .subnets
                .iter()
                .map(|s| s.serialize())
                .collect::<Vec<String>>()
                .join(", ");

            table.add_row(vec![
                nlb.graphkey.to_string(),
                nlb.name.clone(),
                subnets,
                cross_zone.to_string(),
            ]);
        }

        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{error:#?}");
        }
    }
}

pub async fn info(gk_aws_elb: &str, ace_db_app: &ace_db::App) {
    let nlb = ace_graph::aws_elb::get(ace_graph::AwsElb::Db(gk_aws_elb.to_string()), ace_db_app)
        .await
        .unwrap_or_else(|e| {
            panic!("Error fetching NLB: {e:#?}");
        });

    println!("{nlb:#?}");
}

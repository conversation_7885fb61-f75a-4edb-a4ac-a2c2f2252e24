use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let cluster_result =
        ace_graph::aws_rds::select_result(ace_graph::AwsRdsFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut clusters_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid clusters
    match cluster_result {
        Ok(mariadbs) => {
            for mariadb in mariadbs {
                match mariadb {
                    Ok(mariadb) => clusters_sorted.push(mariadb),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching AWS RDS Clusters: {e:#?}");
            return;
        }
    }

    // Sort
    clusters_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for mariadb in clusters_sorted {
            println!("\n{mariadb:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec![
            "Graphkey",
            "Identifier",
            "Instance Class",
            "Engine Version",
            "Storage",
            "Multi AZ",
            "Availability Zone",
        ]);

        for mariadb in clusters_sorted {
            let multi_az = if mariadb.multi_az { "Yes" } else { "No" };
            let az = mariadb
                .availability_zone
                .as_ref()
                .map_or("N/A".to_string(), |az| az.to_string());

            table.add_row(vec![
                mariadb.graphkey.to_string(),
                mariadb.name.clone(),
                mariadb.instance_class.clone(),
                mariadb.engine_version.clone(),
                format!("{} GB", mariadb.allocated_storage),
                multi_az.to_string(),
                az,
            ]);
        }

        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{error:#?}");
        }
    }
}

pub async fn info(gk_aws_rds_db_maria: &str, ace_db_app: &ace_db::App) {
    // parse the graphkey from string
    let mariadb_key = ace_graph::AwsRds::deserialize(gk_aws_rds_db_maria).unwrap_or_else(|e| {
        panic!("Error parsing mariadb graphkey: {e}");
    });

    let mariadb = ace_graph::aws_rds::get(mariadb_key, ace_db_app)
        .await
        .unwrap_or_else(|e| {
            panic!("Error fetching mariadb: {e:#?}");
        });

    println!("{mariadb:#?}");
}

use ace_core::Application;
use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AutoCommit,
    CheckSecureMountState,
    ExecuteCommand,
    GpgRequiresSecureMounted(String),
}

pub async fn run(app: &Application, args: &[String]) -> error_stack::Result<(), ErrorStack> {
    let homedir = &app.gpg_path;

    if !app
        .is_secure_mounted()
        .change_context(ErrorStack::CheckSecureMountState)?
    {
        error_stack::bail!(ErrorStack::GpgRequiresSecureMounted(
            "You must mount the secure directory before running any ace gpg commands.".to_string()
        ));
    }

    let non_disk_changing_operations: std::collections::HashSet<&str> = [
        "--list-keys",
        "--list-secret-keys",
        "--fingerprint",
        "--check-signatures",
        "--search-keys",
        "--dry-run",
        "--list-config",
        "--version",
        "--help",
    ]
    .iter()
    .cloned()
    .collect();

    let autocommit = args
        .iter()
        .any(|arg| !non_disk_changing_operations.contains(arg.as_str()));

    let use_pinenetry = !args.contains(&"--delete-secret-key".to_string());

    let mut cmd = std::process::Command::new("gpg");
    cmd.arg("--homedir").arg(homedir);

    if use_pinenetry {
        cmd.arg("--pinentry-mode").arg("loopback");
    }

    cmd.args(args.iter());
    cmd.envs(std::env::vars());
    cmd.stdout(std::process::Stdio::inherit());
    cmd.stderr(std::process::Stdio::inherit());
    cmd.stdin(std::process::Stdio::inherit());
    cmd.output().change_context(ErrorStack::ExecuteCommand)?;

    if autocommit {
        ace_core::git::autocommit(
            app,
            &format!(
                "auto commit after gpg {:?}",
                args.first().unwrap_or(&"".to_string())
            )
            .to_string(),
        )
        .await
        .change_context(ErrorStack::AutoCommit)?;
    }

    Ok(())
}

use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    // Catch any overall errors here:
    let hosts = match ace_graph::host::select_result(&ace_graph::HostFilter::All, ace_db_app).await
    {
        Ok(hosts) => hosts,
        Err(e) => {
            eprintln!("Error: {e:#?}");
            return;
        }
    };

    // Proceed, collect individual errors into a vec
    let mut errors = Vec::new();
    let mut sorted_hosts = Vec::new();

    for host in hosts {
        match host {
            Ok(h) => {
                sorted_hosts.push(h);
            }
            Err(e) => {
                errors.push(e);
            }
        }
    }

    sorted_hosts.sort_by(|a, b| a.graphkey.to_string().cmp(&b.graphkey.to_string()));

    if verbose {
        for host in sorted_hosts {
            println!("{host:#?}\n");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);

        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Hostname"]);

        for host in sorted_hosts {
            let hostname = match &host.hostname {
                Some(h) => h.clone(),
                None => "N/A".to_string(),
            };
            table.add_row(vec![host.graphkey.to_string(), hostname]);
        }

        println!("\n{table}");
        if !errors.is_empty() {
            println!("\nErrors:");
            for error in errors {
                println!("{error:#?}\n");
            }
        }
    }
}

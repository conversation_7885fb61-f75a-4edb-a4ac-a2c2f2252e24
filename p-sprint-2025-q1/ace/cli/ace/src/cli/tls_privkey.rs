use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool) {
    let tls_privkey =
        match ace_graph::tls_privkey::select_result(ace_graph::TlsPrivateKeyFilter::All).await {
            Ok(tls_privkey) => tls_privkey,
            Err(e) => {
                eprintln!("Error: {e:#?}");
                return;
            }
        };

    let mut errors = Vec::new();
    let mut tls_privkey_sorted = Vec::new();

    for key in tls_privkey {
        match key {
            Ok(key) => tls_privkey_sorted.push(key),
            Err(e) => errors.push(e),
        }
    }

    // Sort tls_privkey by their graphkey
    tls_privkey_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for key in tls_privkey_sorted {
            println!("\n{key:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);

        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);

        for key in tls_privkey_sorted {
            table.add_row(vec![key.graphkey.to_string()]);
        }

        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{error:#?}");
        }
    }
}

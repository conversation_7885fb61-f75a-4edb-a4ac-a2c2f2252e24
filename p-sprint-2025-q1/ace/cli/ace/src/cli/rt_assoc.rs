use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool) {
    let rt_assocs_result =
        ace_graph::rt_assoc::select_result(ace_graph::RouteTableAssocFilter::All).await;
    let mut errors = Vec::new();
    let mut rt_assocs_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid rt_assoc entries
    match rt_assocs_result {
        Ok(rt_assocs) => {
            for rt_assoc in rt_assocs {
                match rt_assoc {
                    Ok(rt_assoc) => rt_assocs_sorted.push(rt_assoc),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching route table associations: {e:#?}");
            return;
        }
    }

    // Sort rt_assocs by their graphkey
    rt_assocs_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for rt_assoc in rt_assocs_sorted {
            println!("\n{rt_assoc:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for rt_assoc in rt_assocs_sorted {
            table.add_row(vec![rt_assoc.graphkey.to_string()]);
        }

        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("Error: {error:#?}");
        }
    }
}

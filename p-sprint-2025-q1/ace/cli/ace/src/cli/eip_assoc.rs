use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool) {
    let eip_assocs_result =
        ace_graph::eip_assoc::select_result(ace_graph::EipAssocFilter::All).await;
    let mut errors = Vec::new();
    let mut eip_assocs_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid EIP associations
    match eip_assocs_result {
        Ok(eip_assocs) => {
            for eip_assoc in eip_assocs {
                match eip_assoc {
                    Ok(eip_assoc) => eip_assocs_sorted.push(eip_assoc),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching EIP associations: {e:#?}");
            return;
        }
    }

    // Sort EIP associations by their graphkey
    eip_assocs_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for eip_assoc in eip_assocs_sorted {
            println!("\n{eip_assoc:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for eip_assoc in eip_assocs_sorted {
            table.add_row(vec![eip_assoc.graphkey.to_string()]);
        }

        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{error:#?}");
        }
    }
}

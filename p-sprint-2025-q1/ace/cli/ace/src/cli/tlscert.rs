use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};
use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    Gen<PERSON>ert(String),
    Get<PERSON><PERSON>(String),
    GetTlsCert(ace_graph::TlsCert),
    InvalidGraphKey(String, String),
    NewTlsManager,
}

pub async fn ls(verbose: bool, ace_db_app: &ace_db::App) {
    let tlscert_filter = ace_graph::TlsCertFilter::All;

    let tlscerts = match ace_graph::tlscert::select_result(&tlscert_filter, ace_db_app).await {
        Ok(tlscerts) => tlscerts,
        Err(e) => {
            println!("{e:?}");
            return;
        }
    };

    let mut sorted_tlscerts: Vec<_> = tlscerts.into_iter().collect();

    sorted_tlscerts.sort_by(|a, b| {
        a.as_ref()
            .map(|x| x.graphkey.serialize())
            .unwrap_or_default()
            .cmp(
                &b.as_ref()
                    .map(|x| x.graphkey.serialize())
                    .unwrap_or_default(),
            )
    });

    if verbose {
        for tlscert in sorted_tlscerts {
            match tlscert {
                Ok(tlscert) => println!("\n{tlscert:#?}"),
                Err(e) => println!("\n{e:#?}"),
            }
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);

        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::ASCII_HORIZONTAL_ONLY);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Common Name/SAN", "Valid Until"]);

        let mut errors = Vec::new();

        for tlscert in sorted_tlscerts {
            match tlscert {
                Ok(tlscert) => {
                    let (expiry, actual_alt_names, days_til_expiry, common_name) =
                        match tlscert.cert {
                            Some(inner_cert) => (
                                inner_cert.expiration.to_string(),
                                inner_cert.subject_alternative_names.join("\n"),
                                inner_cert.days_til_expiry.to_string(),
                                inner_cert.common_name,
                            ),
                            None => (
                                "Does Not Exist".to_string(),
                                "N/A".to_string(),
                                "N/A".to_string(),
                                "N/A".to_string(),
                            ),
                        };

                    let common_name_san_cell = format!("{common_name}\n{actual_alt_names}",);

                    let expiry_cell = format!("{expiry}\n{days_til_expiry} Days",);

                    table.add_row(vec![
                        tlscert.graphkey.to_string(),
                        common_name_san_cell,
                        expiry_cell,
                    ]);
                }
                Err(e) => errors.push(e),
            }
        }

        println!("{table}");

        if !errors.is_empty() {
            println!("\nErrors:");
            for error in errors {
                println!("{error:#?}");
            }
        }
    }
}
pub async fn info(gk_tlscert: &str, ace_db_app: &ace_db::App) {
    match info_inner(gk_tlscert, ace_db_app).await {
        Ok(()) => {}
        Err(e) => println!("{e:?}"),
    }
}

pub async fn info_inner(
    gk_tlscert: &str,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let gk_tlscert = match ace_graph::TlsCert::deserialize(gk_tlscert) {
        Ok(gk_developer) => gk_developer,
        Err(e) => error_stack::bail!(ErrorStack::InvalidGraphKey(gk_tlscert.to_owned(), e)),
    };

    let tlscert = ace_graph::tlscert::get(&gk_tlscert, ace_db_app)
        .await
        .change_context(ErrorStack::GetTlsCert(gk_tlscert))?;

    let (expiry, actual_alt_names) = match tlscert.cert {
        Some(inner_cert) => (
            inner_cert.expiration.to_string(),
            inner_cert.subject_alternative_names.join("\n"),
        ),
        None => ("Does Not Exist".to_string(), "N/A".to_string()),
    };

    println!("tls cert summary:");
    println!("Graph Key:              {}", tlscert.graphkey.serialize());
    println!("Common Name:            {}", tlscert.common_name);
    println!("Expiration Time:        {expiry}");
    println!(
        "Canonical Subject Alt Names:   {}",
        tlscert.canonical_subject_alt_names.join("\n")
    );
    println!("Actual Subject Alt Names:      {actual_alt_names}");

    Ok(())
}

pub async fn gen_cert(
    app: &ace_core::Application,
    key: &str,
) -> error_stack::Result<(), ErrorStack> {
    let tlscert_gk: ace_graph::TlsCert = match ace_graph::GraphKeyExt::deserialize(key) {
        Ok(gk) => gk,
        Err(e) => error_stack::bail!(ErrorStack::InvalidGraphKey(key.to_owned(), e)),
    };

    let manager = ace_core::tls::TlsManager::new(&app.ace_db_app)
        .await
        .change_context(ErrorStack::NewTlsManager)?;
    let certificate = manager
        .get_cert(&tlscert_gk)
        .change_context(ErrorStack::GetCert(key.to_owned()))?;
    certificate
        .generate(app)
        .await
        .change_context(ErrorStack::GenCert(key.to_owned()))?;
    Ok(())
}

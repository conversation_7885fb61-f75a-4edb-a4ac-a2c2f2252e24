use error_stack::ResultExt;
use garbage::CNSL;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AllowAceToBindToPort443,
    EnableService,
    GetAce2ServerName,
    GetEtcConfig,
    GetHomeEnvVariable,
    ReadFromTlsCertFile(std::path::PathBuf),
    ReadFromTlsKeyFile(std::path::PathBuf),
    SerializeWebServerConfig,
    SourceBashAliases,
    StartService,
    SudoCP,
    TlsCertFileNotFound(std::path::PathBuf),
    TlsKeyFileNotFound(std::path::PathBuf),
    UpdateHostname,
    WriteBashAliases,
    WriteToAceServerToml,
    WriteToBashRcFile,
    WriteToServiceFile,
}

#[rustfmt::skip]
fn get_bashrc(app: &ace_core::Application) -> String {
    CNSL!(r#"
        # ~/.bashrc: executed by bash(1) for non-login shells.
        # see /usr/share/doc/bash/examples/startup-files (in the package bash-doc)
        # for examples
        
        # If not running interactively, don't do anything
        case $- in
            *i*) ;;
            *) return;;
        esac
        
        # don't put duplicate lines or lines starting with space in the history.
        # See bash(1) for more options
        HISTCONTROL=ignoreboth
        
        # append to the history file, don't overwrite it
        shopt -s histappend
        
        # for setting history length see HISTSIZE and HISTFILESIZE in bash(1)
        HISTSIZE=1000
        HISTFILESIZE=2000
        
        # check the window size after each command and, if necessary,
        # update the values of LINES and COLUMNS.
        shopt -s checkwinsize
        
        # If set, the pattern "**" used in a pathname expansion context will
        # match all files and zero or more directories and subdirectories.
        #shopt -s globstar
        
        # make less more friendly for non-text input files, see lesspipe(1)
        [ -x /usr/bin/lesspipe ] && eval "$(SHELL=/bin/sh lesspipe)"
        
        # set variable identifying the chroot you work in (used in the prompt below)
        if [ -z "${debian_chroot:-}" ] && [ -r /etc/debian_chroot ]; then
            debian_chroot=$(cat /etc/debian_chroot)
        fi
        
        # set a fancy prompt (non-color, unless we know we "want" color)
        case "$TERM" in
            xterm-color|*-256color) color_prompt=yes;;
        esac
        
        # uncomment for a colored prompt, if the terminal has the capability; turned
        # off by default to not distract the user: the focus in a terminal window
        # should be on the output of commands, not on the prompt
        #force_color_prompt=yes
        
        if [ -n "$force_color_prompt" ]; then
            if [ -x /usr/bin/tput ] && tput setaf 1 >&/dev/null; then
            # We have color support; assume it's compliant with Ecma-48
            # (ISO/IEC-6429). (Lack of such support is extremely rare, and such
            # a case would tend to support setf rather than setaf.)
            color_prompt=yes
            else
            color_prompt=
            fi
        fi
        
        if [ "$color_prompt" = yes ]; then
            PS1='${debian_chroot:+($debian_chroot)}\[\033[01;32m\]\u@\H\[\033[00m\]:\[\033[01;34m\]\w\[\033[00m\]\$ '
        else
            PS1='${debian_chroot:+($debian_chroot)}\u@\H:\w\$ '
        fi
        unset color_prompt force_color_prompt
        
        # If this is an xterm set the title to user@host:dir
        case "$TERM" in
        xterm*|rxvt*)
            PS1="\[\e]0;${debian_chroot:+($debian_chroot)}\u@\H: \w\a\]$PS1"
            ;;
        *)
            ;;
        esac
        
        # enable color support of ls and also add handy aliases
        if [ -x /usr/bin/dircolors ]; then
            test -r ~/.dircolors && eval "$(dircolors -b ~/.dircolors)" || eval "$(dircolors -b)"
            alias ls='ls --color=auto'
            #alias dir='dir --color=auto'
            #alias vdir='vdir --color=auto'
        
            alias grep='grep --color=auto'
            alias fgrep='fgrep --color=auto'
            alias egrep='egrep --color=auto'
        fi
        
        # colored GCC warnings and errors
        #export GCC_COLORS='error=01;31:warning=01;35:note=01;36:caret=01;32:locus=01:quote=01'
        
        # some more ls aliases
        alias ll='ls -lF'
        alias la='ls -A'
        alias l='ls -CF'
        
        # Add an "alert" alias for long running commands.  Use like so:
        #   sleep 10; alert
        alias alert='notify-send --urgency=low -i "$([ $? = 0 ] && echo terminal || echo error)" "$(history|tail -n1|sed -e '\''s/^\s*[0-9]\+\s*//;s/[;&|]\s*alert$//'\'')"'
        
        # Alias definitions.
        # You may want to put all your additions into a separate file like
        # ~/.bash_aliases, instead of adding them here directly.
        # See /usr/share/doc/bash-doc/examples in the bash-doc package.
        
        if [ -f ~/.bash_aliases ]; then
            . ~/.bash_aliases
        fi
        
        # enable programmable completion features (you don't need to enable
        # this, if it's already enabled in /etc/bash.bashrc and /etc/profile
        # sources /etc/bash.bashrc).
        if ! shopt -oq posix; then
        if [ -f /usr/share/bash-completion/bash_completion ]; then
            . /usr/share/bash-completion/bash_completion
        elif [ -f /etc/bash_completion ]; then
            . /etc/bash_completion
        fi
        fi
    
        # Change to ace dir by default
        cd "#, String::from_utf8_lossy(&shell_quote::Sh::quote_vec(&app.path.display().to_string())), r#"
    "#)

}

pub async fn run(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    let ace2_server_name = ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GetAce2ServerName)?
        .ace2_server_name;

    if !app.is_ace_server {
        eprintln!("Skipping system update because this is not an ace server");
        return Ok(());
    }

    println!("Updating system...");

    println!("replace ~/.bashrc ...");

    let bashrc = get_bashrc(app);
    let homedir = std::env::var("HOME").change_context(ErrorStack::GetHomeEnvVariable)?;
    let bashrc_path = std::path::Path::new(&homedir).join(".bashrc");
    std::fs::write(&bashrc_path, bashrc).change_context(ErrorStack::WriteToBashRcFile)?;

    println!(" ... done!");

    println!("Update hostname ...");

    tokio::process::Command::new("sudo")
        .arg("hostnamectl")
        .arg("set-hostname")
        .arg(ace2_server_name)
        .output()
        .await
        .change_context(ErrorStack::UpdateHostname)?;

    println!(" ... done!");

    // update aliases
    println!("Update aliases ...");

    #[rustfmt::skip]
    let bash_aliases = CNSL!(
        r#"
        alias ace="#, String::from_utf8_lossy(&shell_quote::Sh::quote_vec(&app.bin_executable_path.display().to_string())),r#"
        alias agit="#, String::from_utf8_lossy(&shell_quote::Sh::quote_vec(&app.bin_path.join("agit").display().to_string())),r#"
    "#
    );

    let bash_aliases_path = std::path::Path::new(&homedir).join(".bash_aliases");

    std::fs::write(&bash_aliases_path, bash_aliases)
        .change_context(ErrorStack::WriteBashAliases)?;

    // source .bash_aliases
    tokio::process::Command::new("sh")
        .arg("-c")
        .arg(format!("source {}", bash_aliases_path.to_string_lossy()))
        .output()
        .await
        .change_context(ErrorStack::SourceBashAliases)?;

    println!(" ... done!");

    // Update executable to allow it to server port 443
    println!("Update executable to allow it to server port 443 ...");

    // Allow ace to bind to port 443
    tokio::process::Command::new("sudo")
        .arg("setcap")
        .arg("cap_net_bind_service=+ep")
        .arg(&app.bin_executable_path)
        .output()
        .await
        .change_context(ErrorStack::AllowAceToBindToPort443)?;

    println!(" ... done!");

    println!("Install systemd unit file ...");

    #[rustfmt::skip]
    let systemd_unit = CNSL!(r#"
        [Unit]
        Description=ace server run on port 443
        After=network.target

        [Service]
        User=app
        AmbientCapabilities=CAP_NET_BIND_SERVICE
        WorkingDirectory="#, app.bin_path.display().to_string(), r#"
        ExecStart="#, app.ace_server_bin_path.display().to_string(), r#" run
        Restart=always
        RestartSec=5
        
        [Install]
        WantedBy=multi-user.target
    "#);

    let systemd_unit_path = std::path::Path::new("/etc/systemd/system/ace-server.service");
    let tmp_path = app.temp_path.join("ace-server.service");

    tokio::fs::write(&tmp_path, systemd_unit)
        .await
        .change_context(ErrorStack::WriteToServiceFile)?;

    tokio::process::Command::new("sudo")
        .arg("cp")
        .arg(&tmp_path)
        .arg(systemd_unit_path)
        .output()
        .await
        .change_context(ErrorStack::SudoCP)?;

    // Enable service
    tokio::process::Command::new("sudo")
        .arg("systemctl")
        .arg("enable")
        .arg("ace-server.service")
        .output()
        .await
        .change_context(ErrorStack::EnableService)?;

    // Start service
    tokio::process::Command::new("sudo")
        .arg("systemctl")
        .arg("restart")
        .arg("ace-server.service")
        .output()
        .await
        .change_context(ErrorStack::StartService)?;

    println!(" ... done!");

    Ok(())
}

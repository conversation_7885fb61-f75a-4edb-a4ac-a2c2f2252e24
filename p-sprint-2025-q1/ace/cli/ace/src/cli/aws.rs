use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    Run(String),
}

pub async fn run(args: &Vec<String>) -> error_stack::Result<(), ErrorStack> {
    let cmd_path = "/usr/local/bin/aws";

    // Call env.terraform_path executable with all of the remaining command line arguments
    let mut cmd = std::process::Command::new(cmd_path);
    cmd.args(args);
    cmd.stdout(std::process::Stdio::inherit());
    cmd.stderr(std::process::Stdio::inherit());
    cmd.stdin(std::process::Stdio::inherit());

    cmd.output()
        .change_context(ErrorStack::Run(cmd_path.to_string()))?;

    Ok(())
}

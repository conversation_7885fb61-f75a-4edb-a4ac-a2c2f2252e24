use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetTerraformOutput,
    GetConfig,
    SshKeyFileDoesNotExist,
    NoAce2InstancePublicIpFoundInTerraformOutput,
    NoAce2InstancePrivateIpFoundInTerraformOutput,
    ExecuteCommand(std::path::PathBuf),
}

pub async fn run(
    env: &ace_core::Application,
    server: &str,
    args: &Vec<String>,
) -> error_stack::Result<(), ErrorStack> {
    println!("ssh to {server}");

    let terraform_data = ace_db::data::terraform_output::get(&env.ace_db_app.data_path)
        .await
        .change_context(ErrorStack::GetTerraformOutput)?;

    let (ace2_private_key_file_path, public_subdomain_name) = {
        let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, &env.ace_db_app)
            .await
            .change_context(ErrorStack::GetConfig)?;

        (
            config.ace2_private_key_file_path,
            config.public_subdomain_name,
        )
    };

    if !ace2_private_key_file_path.exists() {
        let report =
            error_stack::Report::new(ErrorStack::SshKeyFileDoesNotExist).attach_printable(format!(
                "ssh key file {} does not exist.\nYou must apply terraform first.",
                ace2_private_key_file_path.display()
            ));
        return Err(report);
    }

    // If no dot is present, then assume we are a local name
    let server_name = match server {
        "ace-public" => match terraform_data.ace2_instance_public_ip {
            Some(ip) => ip,
            None => error_stack::bail!(ErrorStack::NoAce2InstancePublicIpFoundInTerraformOutput),
        },
        "ace-private" => match terraform_data.ace2_instance_private_ip {
            Some(ip) => ip,
            None => error_stack::bail!(ErrorStack::NoAce2InstancePrivateIpFoundInTerraformOutput),
        },
        server_name if server_name.contains('.') => server_name.to_string(),
        server_name => {
            format!("{}.{}", &server_name, public_subdomain_name)
        }
    };

    let username_at_server_name = match server_name.contains('@') {
        true => server_name.clone(),
        false => format!("app@{server_name}"),
    };

    // Call env.terraform_path executable with all of the remaining command line arguments
    let mut cmd = std::process::Command::new(&env.ssh_bin_path);
    cmd.args([
        "-i",
        &ace2_private_key_file_path.to_string_lossy(),
        username_at_server_name.as_str(),
    ]);
    cmd.args(args);
    cmd.current_dir(&env.path);
    cmd.stdout(std::process::Stdio::inherit());
    cmd.stderr(std::process::Stdio::inherit());
    cmd.stdin(std::process::Stdio::inherit());
    cmd.output()
        .change_context(ErrorStack::ExecuteCommand(env.ssh_bin_path.clone()))?;

    Ok(())
}

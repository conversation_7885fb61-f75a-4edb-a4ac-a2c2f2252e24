use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let devapps_result =
        ace_graph::developer_app::select_result(&ace_graph::DeveloperAppFilter::All, ace_db_app)
            .await;
    let mut errors = Vec::new();
    let mut devapps_sorted = Vec::new();

    // Separate valid devapps and errors
    match devapps_result {
        Ok(devapps) => {
            for devapp in devapps {
                match devapp {
                    Ok(devapp) => devapps_sorted.push(devapp),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching developer applications: {e:#?}");
            return;
        }
    }

    // Sort valid developer applications by their graphkey
    devapps_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for devapp in devapps_sorted {
            println!("{devapp:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }
        table.set_header(vec!["Name", "Graphkey"]);
        for devapp in devapps_sorted {
            table.add_row(vec![devapp.developer_name, devapp.graphkey.serialize()]);
        }
        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{error:#?}");
        }
    }
}

use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    InvalidGraphKey(String, String),
    GetAwsEcSls,
}

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let select_result =
        ace_graph::aws_ec_sls::select_result(ace_graph::AwsEcSlsFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid AwsEcSls entries
    match select_result {
        Ok(list) => {
            for aws_ec_sls in list {
                match aws_ec_sls {
                    Ok(aws_ec_sls) => sorted.push(aws_ec_sls),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching AwsEcSls: {e:#?}");
            return;
        }
    }

    // Sort AwsEcSls by their graphkey
    sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for aws_ec_sls in sorted {
            println!("\n{aws_ec_sls:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec![
            "Graphkey",
            "Name",
            "Purpose",
            "Engine Version",
            "Backup Daily",
            "Description",
        ]);
        for aws_ec_sls in sorted {
            let backup_daily = aws_ec_sls.backup_daily.to_string();
            let description = aws_ec_sls.description.unwrap_or("N/A".to_string());

            table.add_row(vec![
                aws_ec_sls.graphkey.to_string(),
                aws_ec_sls.name.clone(),
                aws_ec_sls.purpose.clone(),
                aws_ec_sls.engine_version.clone(),
                backup_daily,
                description,
            ]);
        }

        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("Error: {error:#?}");
        }
    }
}

pub async fn info(gk_aws_ec_sls: &str, ace_db_app: &ace_db::App) -> Result<(), ErrorStack> {
    let gk_aws_ec_sls = ace_graph::AwsEcSls::deserialize(gk_aws_ec_sls)
        .map_err(|e| ErrorStack::InvalidGraphKey(gk_aws_ec_sls.to_owned(), e.to_string()))?;

    let aws_ec_sls = ace_graph::aws_ec_sls::get(gk_aws_ec_sls, ace_db_app)
        .await
        .map_err(|_e| ErrorStack::GetAwsEcSls)?;

    println!("AWS ElastiCache Serverless Summary:");
    println!("  Graph Key:       {}", aws_ec_sls.graphkey.serialize());
    println!("  Name:            {}", aws_ec_sls.name);
    println!("  Purpose:         {}", aws_ec_sls.purpose);
    println!("  Engine Version:  {}", aws_ec_sls.engine_version);

    println!("  Backup Daily:    {}", aws_ec_sls.backup_daily);

    if let Some(description) = &aws_ec_sls.description {
        println!("  Description:     {description}");
    }

    Ok(())
}

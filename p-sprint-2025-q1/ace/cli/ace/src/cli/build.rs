use error_stack::ResultExt;
use tokio::join;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AutoCommit,
    BuildFailed,
    GenerateAceServerToml,
    GenerateAutoUpdate,
    GeneratePacker,
    GenerateRelocateBashScript,
    GenerateTerraform,
    GetConfig,
    ReadFromTlsCertFile(std::path::PathBuf),
    ReadFromTlsKeyFile(std::path::PathBuf),
    ParseTlsCertAndKey,
    SerializeWebServerConfig,
    SetAutoUpdateScriptFilePermissions,
    SetBashScriptFilePermissions,
    SetTransformScriptPermissions,
    TlsCertFileNotFound(std::path::PathBuf),
    TlsKeyFileNotFound(std::path::PathBuf),
    WriteAgitConfigFile,
    WriteAutoUpdateScriptFile,
    WriteBashScriptFile,
    WriteToAceServerToml,
    WriteTransformScriptFile,
    WriteVersionFile,
    CheckSecureMountState,
    MountSecure,
    CheckGpgDirectoryAndGpgAceKeypair,
}

#[derive(Debug)]
enum AceServerJson {
    Error(error_stack::Report<ErrorStack>),
    Ok,
}

pub async fn run(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    let future_generate_packer = generate_packer(app);
    let future_generate_terraform = generate_terraform(app, &config);
    let future_generate_ace_server_toml = generate_ace_server_toml(app);
    let future_check_gpg_dir_and_key = check_gpg_directory_and_key(app, &config);

    let (result_packer, result_terraform, result_server_toml, result_gpg) = join!(
        future_generate_packer,
        future_generate_terraform,
        future_generate_ace_server_toml,
        future_check_gpg_dir_and_key
    );

    let mut statuses: Vec<(&str, &str)> = Vec::new();
    let error_status = "\u{2715}";
    let success_status = "\u{2714}";

    match result_packer {
        Ok(_) => statuses.push((success_status, "Packer")),
        Err(_e) => statuses.push((error_status, "Packer")),
    }
    match result_terraform {
        Ok(_) => statuses.push((success_status, "Terraform")),
        Err(_e) => statuses.push((error_status, "Terraform")),
    }
    match result_server_toml {
        AceServerJson::Ok => statuses.push((success_status, "Create Ace Server Json")),
        AceServerJson::Error(_e) => {
            statuses.push((error_status, "Create Ace Server Json"));
        }
    }
    match result_gpg {
        Ok(_) => statuses.push((success_status, "GPG Dir and Key Check")),
        Err(_e) => statuses.push((error_status, "GPG Dir and Key Check")),
    }

    ace_core::git::write_version_file(app).change_context(ErrorStack::WriteVersionFile)?;

    ace_core::git::autocommit(app, &"auto commit after build operation".to_string())
        .await
        .change_context(ErrorStack::AutoCommit)?;

    println!("Status:");
    for (status, name) in statuses {
        println!(" {status} {name}");
    }

    Ok(())
}

async fn generate_packer(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    match ace_core::packer::build(app)
        .await
        .change_context(ErrorStack::GeneratePacker)
    {
        Ok(_) => Ok(()),
        Err(e) => {
            println!("{e:#?}");
            Err(e)
        }
    }
}

async fn generate_terraform(
    app: &ace_core::Application,
    config: &ace_graph::config::Config,
) -> error_stack::Result<(), ErrorStack> {
    match ace_core::terraform::build(app, config)
        .await
        .change_context(ErrorStack::GenerateTerraform)
    {
        Ok(_) => Ok(()),
        Err(e) => {
            println!("{e:#?}");
            Err(e)
        }
    }
}

async fn generate_ace_server_toml(app: &ace_core::Application) -> AceServerJson {
    match create_ace_server_json(app).await {
        Ok(_) => AceServerJson::Ok,
        Err(e) => {
            tracing::error!("{:#?}", e);
            AceServerJson::Error(e)
        }
    }
}

/// This function will create a basic ace-server.json file if it does not already exist.
/// The tls configuration will be set to static, port 443 and host "0.0.0.0".
/// `ace-server` dynamically loads the correct tls certificate from the data dir at runtime
pub async fn create_ace_server_json(
    app: &ace_core::Application,
) -> error_stack::Result<(), ErrorStack> {
    let ace_server_json_path = app.bin_path.join("ace-server.json");
    // Properly formatted JSON with no leading whitespace
    let contents = r#"{ "webserver": { "host": "0.0.0.0", "port": 443 } }"#;

    tokio::fs::write(ace_server_json_path, contents)
        .await
        .change_context(ErrorStack::WriteToAceServerToml)?;

    Ok(())
}

async fn check_gpg_directory_and_key(
    app: &ace_core::Application,
    config: &ace_graph::config::Config,
) -> error_stack::Result<(), ErrorStack> {
    match ace_core::gpg::check_gpg_directory(app, config).await {
        Ok(_) => Ok(()),
        Err(e) => {
            tracing::error!("{:#?}", e);
            Err(e.change_context(ErrorStack::CheckGpgDirectoryAndGpgAceKeypair))
        }
    }
}

use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let dns_zones_result =
        ace_graph::dns_zone::select_result(&ace_graph::DnsZoneFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut dns_zones_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid DNS zones
    match dns_zones_result {
        Ok(dns_zones) => {
            for dns_zone in dns_zones {
                match dns_zone {
                    Ok(dns_zone) => dns_zones_sorted.push(dns_zone),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching DNS zones: {e:#?}");
            return;
        }
    }

    // Sort DNS zones by their graphkey
    dns_zones_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for dns_zone in dns_zones_sorted {
            println!("\n{dns_zone:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for dns_zone in dns_zones_sorted {
            table.add_row(vec![dns_zone.graphkey.to_string()]);
        }

        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{error:#?}");
        }
    }
}

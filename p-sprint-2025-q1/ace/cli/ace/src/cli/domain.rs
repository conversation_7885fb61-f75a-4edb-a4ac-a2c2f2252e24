use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let domains_result =
        ace_graph::domain::select_result(&ace_graph::DomainFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut domains_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid domain entries
    match domains_result {
        Ok(domains) => {
            for domain in domains {
                match domain {
                    Ok(domain) => domains_sorted.push(domain),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching domains: {e:#?}");
            return;
        }
    }

    // Sort domain entries by their graphkey
    domains_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for domain in domains_sorted {
            println!("{domain:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "Hosted Zone ID"]);
        for domain in domains_sorted {
            table.add_row(vec![domain.graphkey.to_string(), domain.aws.hosted_zone_id]);
        }

        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{error:#?}");
        }
    }
}

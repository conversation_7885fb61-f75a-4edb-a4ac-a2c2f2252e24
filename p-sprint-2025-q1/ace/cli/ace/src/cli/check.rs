use ace_check::CheckKeyTrait;
use comfy_table::{Cell, Color, ContentArrangement, Row, Table, presets};
use error_stack::ResultExt;
use std::collections::HashSet;

pub type NsValues = Vec<String>;

#[derive(Debug)]
pub enum NsStatus {
    MissingCanonicalRecords,
    Mismatch,
    Match,
    Uncalculated,
    Error(ErrorStack),
}

#[derive(Debug)]
pub struct NsCompare {
    pub subdomain: String,
    pub pass: bool,
    pub canonical_entries: NsValues,
    pub actual_entries: NsValues,
    pub extra_records: NsValues,
    pub missing_records: NsValues,
    pub status: NsStatus,
}

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetAuthoritativeNameservers,
    GetConfig,
    GetDataTerraformOutput,
    GetSubdomainNS,
    NoActualNsRecordsFound,

    /// Version Check Variants
    CheckAceBinaryVersion(std::path::PathBuf),
    ReadVersionFile(std::path::PathBuf),
    RunVersionCheck,
}

pub async fn list_checks(app: &ace_core::Application) {
    let checks = ace_check::list_checks(app).await;

    // Print out check keys (using comfy_table)
    let mut table = Table::new();
    table.set_content_arrangement(ContentArrangement::Dynamic);
    if atty::is(atty::Stream::Stdout) {
        table.load_preset(presets::UTF8_FULL_CONDENSED);
    } else {
        table.load_preset(presets::NOTHING);
    }
    table.set_header(vec!["Check Key", "Subject", "Check Type"]);

    for check in checks {
        let check_key = check.to_string();
        let check_object = check.create();
        table.add_row(vec![
            check_key,
            check_object.get_subject_string(app).await,
            check_object.get_check_type(),
        ]);
    }

    println!("{table}");
}

pub async fn run_ns_check(ace_db_app: &ace_db::App) -> error_stack::Result<(), ErrorStack> {
    // First get ns records for both the public and private DOMAIN, not subdomains.
    // After obtaining results, use one of the results to query for the NS records of the subdomains.

    // Get Canonical NS records:
    let data_terraform_output = ace_db::data::terraform_output::get(&ace_db_app.data_path)
        .await
        .change_context(ErrorStack::GetDataTerraformOutput)?;
    let canonical_pvt_ns = data_terraform_output.dns_private_domain_ns_list;
    let canonical_pub_ns = data_terraform_output.dns_public_domain_ns_list;

    // Get private/public subdomains:
    let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, ace_db_app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    let public_domain = config.public_domain_name;
    let private_domain = config.private_domain_name;
    let private_subdomain_name = config.private_subdomain_name;
    let public_subdomain_name = config.public_subdomain_name;

    // Query the DOMAINS for authoritative nameservers.
    let pvt_authoritative_servers = ace_core::dns::dig_ns_records_short(&private_domain)
        .await
        .change_context(ErrorStack::GetAuthoritativeNameservers)?;
    let pub_authoritative_servers = ace_core::dns::dig_ns_records_short(&public_domain)
        .await
        .change_context(ErrorStack::GetAuthoritativeNameservers)?;

    // Now query one of the authoritative servers for the NS records of the subdomains.
    let pvt_subdomain_actual_ns =
        ace_core::dns::dig_authority_ns(&private_subdomain_name, &pvt_authoritative_servers[0])
            .await
            .change_context(ErrorStack::GetSubdomainNS)?;
    let pub_subdomain_actual_ns =
        ace_core::dns::dig_authority_ns(&public_subdomain_name, &pub_authoritative_servers[0])
            .await
            .change_context(ErrorStack::GetSubdomainNS)?;

    // Compare pvt results
    let pvt_ns_compare = compare_ns_values(
        canonical_pvt_ns.unwrap_or_default(),
        pvt_subdomain_actual_ns,
        &private_subdomain_name,
    );

    // Compare pub results
    let pub_ns_compare = compare_ns_values(
        canonical_pub_ns.unwrap_or_default(),
        pub_subdomain_actual_ns,
        &public_subdomain_name,
    );

    let mut pvt_table = Table::new();
    let mut pub_table = Table::new();

    pub_table.set_content_arrangement(ContentArrangement::Dynamic);
    pvt_table.set_content_arrangement(ContentArrangement::Dynamic);

    if atty::is(atty::Stream::Stdout) {
        pub_table.load_preset(presets::UTF8_FULL_CONDENSED);
        pvt_table.load_preset(presets::UTF8_FULL_CONDENSED);
    } else {
        pub_table.load_preset(presets::NOTHING);
        pvt_table.load_preset(presets::NOTHING);
    }

    let headers = vec!["Subdomain", "Canonical NS Records", "Actual NS Records"];
    pvt_table.set_header(&headers);
    pub_table.set_header(headers);

    let (pvt_missing_records, pvt_extra_records) = generate_table(pvt_ns_compare, &mut pvt_table);
    let (pub_missing_records, pub_extra_records) = generate_table(pub_ns_compare, &mut pub_table);

    println!("{pub_table}");
    if !pub_missing_records.is_empty() {
        println!("Records to add:\n{}\n", pub_missing_records.join("\n"));
    }
    if !pub_extra_records.is_empty() {
        println!("Records to remove:\n{}\n", pub_extra_records.join("\n"));
    }

    println!();
    println!("{pvt_table}");
    if !pvt_missing_records.is_empty() {
        println!("Records to add:\n{}\n", pvt_missing_records.join("\n"));
    }
    if !pvt_extra_records.is_empty() {
        println!("Records to remove:\n{}\n", pvt_extra_records.join("\n"));
    }

    Ok(())
}

fn compare_ns_values(
    canonical_ns: Vec<String>,
    actual_ns: Vec<String>,
    subdomain: &str,
) -> NsCompare {
    // Check for empty lists:
    if canonical_ns.is_empty() {
        return NsCompare {
            subdomain: subdomain.to_string(),
            canonical_entries: vec![],
            actual_entries: vec![],
            extra_records: vec![],
            missing_records: vec![],
            status: NsStatus::MissingCanonicalRecords,
            pass: false,
        };
    }

    if actual_ns.is_empty() {
        return NsCompare {
            subdomain: subdomain.to_string(),
            canonical_entries: canonical_ns.clone(),
            actual_entries: vec![],
            extra_records: vec![],
            missing_records: canonical_ns,
            pass: false,
            status: NsStatus::Error(ErrorStack::NoActualNsRecordsFound),
        };
    }

    // Convert to sets for comparison
    let canonical_hs: HashSet<String> = canonical_ns.clone().into_iter().collect();
    let actual_hs: HashSet<String> = actual_ns.clone().into_iter().collect();

    // Calculate/collect missing and extra records
    let mut missing_records: Vec<String> = canonical_hs.difference(&actual_hs).cloned().collect();
    let mut extra_records: Vec<String> = actual_hs.difference(&canonical_hs).cloned().collect();

    // Sort before stuffing into the comparison struct
    missing_records.sort();
    extra_records.sort();

    let mut sorted_canonical = canonical_ns;
    let mut sorted_actual = actual_ns;
    sorted_canonical.sort();
    sorted_actual.sort();

    // Do they match?
    let (ns_status, pass) = match (missing_records.len(), extra_records.len()) {
        (0, 0) => (NsStatus::Match, true),
        _ => (NsStatus::Mismatch, false),
    };

    NsCompare {
        subdomain: subdomain.to_string(),
        canonical_entries: sorted_canonical,
        actual_entries: sorted_actual,
        extra_records,
        missing_records,
        pass,
        status: ns_status,
    }
}

/// Wrapper around `check_ace_version` that parses it's output
pub async fn run_version_check(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    let (matches, readable_version_file, readable_command_output) = check_ace_version(app)
        .await
        .change_context(ErrorStack::RunVersionCheck)?;

    let mut table = Table::new();
    let mut fg_color = comfy_table::Color::Reset;

    table.set_content_arrangement(ContentArrangement::Dynamic);
    if atty::is(atty::Stream::Stdout) {
        table.load_preset(presets::UTF8_FULL_CONDENSED);
    } else {
        table.load_preset(presets::NOTHING);
    }

    table.set_header(vec!["Status", "Bin Version", "Version File"]);

    // Check status to set foreground color
    if !matches {
        fg_color = comfy_table::Color::Red;
    }

    let matches_cell = Cell::new(matches.to_string()).fg(fg_color);
    let bin_version_cell = Cell::new(&readable_command_output).fg(fg_color);
    let version_file_cell = Cell::new(&readable_version_file).fg(fg_color);

    table.add_row(vec![matches_cell, bin_version_cell, version_file_cell]);

    println!("!!NOTE: This command does NOT check your ace alias version!!");
    println!("{table}");

    Ok(())
}

/// Compares the output of `/bin/ace version` with the contents of data/version.txt
pub async fn check_ace_version(
    app: &ace_core::Application,
) -> error_stack::Result<(bool, String, String), ErrorStack> {
    let mut rval = true;

    let version_path = &app.data_version_file_path;
    let version_contents = tokio::fs::read(version_path)
        .await
        .change_context(ErrorStack::ReadVersionFile(version_path.to_path_buf()))?;

    // Run `/bin/ace version`
    let mut cmd = tokio::process::Command::new(&app.bin_executable_path);
    cmd.arg("version");

    let output = cmd
        .output()
        .await
        .change_context(ErrorStack::CheckAceBinaryVersion(
            app.bin_executable_path.to_path_buf(),
        ))?;

    if output.stdout.trim_ascii_end() != version_contents.trim_ascii_end() {
        rval = false;
    }

    let readable_version_file = String::from_utf8_lossy(&version_contents).to_string();
    let readable_command_output = String::from_utf8_lossy(&output.stdout).to_string();

    Ok((rval, readable_version_file, readable_command_output))
}

/// Modifies the table in place.  Returns a tuple of (missing_records, extra_records)
fn generate_table(ns_compare: NsCompare, table: &mut Table) -> (NsValues, NsValues) {
    let missing_records = &ns_compare.missing_records;
    let extra_records = &ns_compare.extra_records;

    let canonical_entries = &ns_compare.canonical_entries;
    let actual_entries = &ns_compare.actual_entries;

    let canonical_cells = {
        let mut rval = vec![];

        for ns in canonical_entries {
            if actual_entries.contains(ns) {
                rval.push(Cell::new(ns).fg(Color::Green));
            } else {
                rval.push(Cell::new(ns).fg(Color::Yellow));
            }
        }

        rval
    };

    let actual_cells = {
        let mut rval = vec![];

        for ns in actual_entries {
            if canonical_entries.contains(ns) {
                rval.push(Cell::new(ns).fg(Color::Green));
            } else {
                rval.push(Cell::new(ns).fg(Color::Red));
            }
        }

        rval
    };

    let mut rows = vec![];

    // Iterate rows until both lists are empty
    let mut canonical_iter = canonical_cells.into_iter().peekable();
    let mut actual_iter = actual_cells.into_iter().peekable();

    while canonical_iter.peek().is_some() | actual_iter.peek().is_some() {
        let mut row = Row::new();

        let subdomain_cell = if rows.is_empty() {
            Cell::new(&ns_compare.subdomain)
        } else {
            Cell::new("")
        };

        let canonical_cell = canonical_iter.next().unwrap_or(Cell::new(""));
        let actual_cell = actual_iter.next().unwrap_or(Cell::new(""));

        row.add_cell(subdomain_cell);
        row.add_cell(canonical_cell);
        row.add_cell(actual_cell);

        rows.push(row);
    }

    table.add_rows(rows);

    (missing_records.to_vec(), extra_records.to_vec())
}

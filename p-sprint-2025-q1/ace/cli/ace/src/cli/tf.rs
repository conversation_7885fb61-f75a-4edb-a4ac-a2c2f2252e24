use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    AutoCommit,
    CheckTerraformState,
    ExecuteCommand(std::path::PathBuf),
    TerraformStateIsUncommitted,
    CheckSecureMountState,
    SecureNotMounted,
}

pub async fn run(
    app: &ace_core::Application,
    args: &[String],
) -> error_stack::Result<(), ErrorStack> {
    let mut autocommit = false;
    let tf_state_rel_path_str = "terraform/terraform.tfstate";

    // Determine if autocommit is needed AND if secure needs mounted.
    if let Some(first_arg) = args.first() {
        match first_arg.as_str() {
            "apply" | "destroy" | "import" | "init" | "input" | "taint" | "untaint" | "refresh" => {
                is_dirty(tf_state_rel_path_str, app).await?;

                if !(app
                    .is_secure_mounted()
                    .change_context(ErrorStack::CheckSecureMountState)?)
                {
                    let err = error_stack::report!(ErrorStack::SecureNotMounted).attach_printable("This terraform command requires secure to be mounted.  Please run `ace secure mount` first.");
                    error_stack::bail!(err);
                }

                autocommit = true;
            }
            "state" => {
                // If the next argument is "list" or "show", we don't need secure mounted.
                if let Some(second_arg) = args.get(1) {
                    match second_arg.as_str() {
                        "list" | "show" => {}
                        _ => {
                            is_dirty(tf_state_rel_path_str, app).await?;

                            if !(app
                                .is_secure_mounted()
                                .change_context(ErrorStack::CheckSecureMountState)?)
                            {
                                let err = error_stack::report!(ErrorStack::SecureNotMounted).attach_printable("This terraform command requires secure to be mounted.  Please run `ace secure mount` first.");
                                error_stack::bail!(err);
                            }
                        }
                    }
                }
            }
            _ => {}
        }
    }

    // Call env.terraform_path executable with all of the remaining command line arguments
    let mut cmd = std::process::Command::new(&app.terraform_bin_path);
    cmd.args(args.iter());
    cmd.current_dir(&app.terraform_path);
    cmd.envs(std::env::vars());
    cmd.stdout(std::process::Stdio::inherit());
    cmd.stderr(std::process::Stdio::inherit());
    cmd.stdin(std::process::Stdio::inherit());
    cmd.output()
        .change_context(ErrorStack::ExecuteCommand(app.terraform_bin_path.clone()))?;

    if autocommit {
        ace_core::git::autocommit(
            app,
            &format!(
                "auto commit after terraform {:?}",
                args.first().unwrap_or(&"".to_string())
            )
            .to_string(),
        )
        .await
        .change_context(ErrorStack::AutoCommit)?;
    }

    Ok(())
}

async fn is_dirty(
    path_str: &str,
    app: &ace_core::Application,
) -> error_stack::Result<bool, ErrorStack> {
    match app.is_dirty(path_str).await {
        Ok(true) => {
            error_stack::bail!(ErrorStack::TerraformStateIsUncommitted);
        }
        Ok(false) => Ok(false),
        Err(e) => Err(e.change_context(ErrorStack::CheckTerraformState)),
    }
}

use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let keypairs_result =
        ace_graph::keypair::select_result(&ace_graph::KeyPairFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut keypairs_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid keypair entries
    match keypairs_result {
        Ok(keypairs) => {
            for keypair in keypairs {
                match keypair {
                    Ok(keypair) => keypairs_sorted.push(keypair),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching keypairs: {e:#?}");
            return;
        }
    }

    // Sort keypairs by their graphkey
    keypairs_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for keypair in keypairs_sorted {
            println!("\n{keypair:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for keypair in keypairs_sorted {
            table.add_row(vec![keypair.graphkey.to_string()]);
        }

        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("Error: {error:#?}");
        }
    }
}

pub async fn add() {
    println!("TODO: add keypair");
}

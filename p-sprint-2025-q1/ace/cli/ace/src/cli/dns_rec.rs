use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool) {
    let dns_recs_result = ace_graph::dns_rec::select_result(ace_graph::DnsRecFilter::All).await;
    let mut errors = Vec::new();
    let mut dns_recs_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid DNS records
    match dns_recs_result {
        Ok(dns_recs) => {
            for dns_rec in dns_recs {
                match dns_rec {
                    Ok(dns_rec) => dns_recs_sorted.push(dns_rec),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching DNS records: {e:#?}");
            return;
        }
    }

    // Sort DNS records by their graphkey
    dns_recs_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for dns_rec in dns_recs_sorted {
            println!("\n{dns_rec:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for dns_rec in dns_recs_sorted {
            table.add_row(vec![dns_rec.graphkey.to_string()]);
        }

        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{error:#?}");
        }
    }
}

use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool, ace_db_app: &ace_db::App) {
    let ecrs_result = ace_graph::ecr::select_result(&ace_graph::EcrFilter::All, ace_db_app).await;
    let mut errors = Vec::new();
    let mut ecrs_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid ECR entries
    match ecrs_result {
        Ok(ecrs) => {
            for ecr in ecrs {
                match ecr {
                    Ok(ecr) => ecrs_sorted.push(ecr),
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching ECRs: {e:#?}");
            return;
        }
    }

    // Sort ECR entries by their graphkey
    ecrs_sorted.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for ecr in ecrs_sorted {
            println!("{ecr:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey", "ARN"]);
        for ecr in ecrs_sorted {
            table.add_row(vec![ecr.graphkey.to_string(), ecr.arn]);
        }

        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{error:#?}");
        }
    }
}

pub async fn info(gk_ecr: &str, ace_db_app: &ace_db::App) {
    let graphkey = match ace_graph::Ecr::deserialize(gk_ecr) {
        Ok(graphkey) => graphkey,
        Err(e) => {
            eprintln!("Error parsing ECR graphkey: {e}");
            return;
        }
    };

    let ecr = match ace_graph::ecr::get(graphkey, ace_db_app).await {
        Ok(ecr) => ecr,
        Err(e) => {
            eprintln!("Error fetching ECR: {e:#?}");
            return;
        }
    };

    println!("{ecr:#?}");
}

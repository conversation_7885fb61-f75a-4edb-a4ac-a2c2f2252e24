use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    Ace2InstancePublicIpIsNone,
    Ace2PrivateKeyFileDoesNotExist,
    CheckMountStatus,
    ExecuteCommand(String),
    ExecuteRemoteGitStatus,
    ExecuteRemoteMount,
    ExecuteRemoteSystemUpdate,
    GetConfig,
    GetTerraformOutput,
    NoAceConfig,
    NoAce2InstancePublicIpFoundInTerraformOutput,
    SelectInteract,
    YouCancelledTheShred,
    YouCancelledTheSync,
}

pub async fn run(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    if app
        .is_secure_mounted()
        .change_context(ErrorStack::CheckMountStatus)?
    {
        println!("Error: you cannot run this command while secure is mounted");
        println!("Run `ace secure unmount` to unmount secure first.");
        std::process::exit(1);
    }

    let config = ace_graph::config::get(&ace_graph::Config::EtcConfig, &app.ace_db_app)
        .await
        .change_context(ErrorStack::GetConfig)?;

    if config.ace.is_none() {
        let err = error_stack::report!(ErrorStack::NoAceConfig).attach_printable("You do not have [ace] specified in your etc/config.toml file - you might not have an Ace instance!");
        error_stack::bail!(err);
    }

    let terraform_data = ace_db::data::terraform_output::get(&app.ace_db_app.data_path)
        .await
        .change_context(ErrorStack::GetTerraformOutput)?;

    let public_ip = match terraform_data.ace2_instance_public_ip {
        Some(ace2_instance_public_ip) => ace2_instance_public_ip,
        None => {
            let report = error_stack::Report::new(ErrorStack::Ace2InstancePublicIpIsNone).attach_printable("\nThe `ace2_instance_public_ip` value is None in the terraform output.\nPerhaps you need to apply terraform first.");
            return Err(report);
        }
    };

    let cmd_path = "/usr/bin/rsync";

    // Super important to remove the trailing slash from the path
    let local_path = match app.path.to_string_lossy().to_string() {
        path if path.ends_with('/') => path.trim_end_matches('/').to_string(),
        path => path,
    };

    let remote_path = format!("app@{public_ip}:");

    let prompt = format!(
        "You are about to sync `{}` to `{}` with the --delete option!\nDO NOT RUN THIS unless you are sure.\n\nDo you want to proceed?",
        &local_path, &remote_path
    );

    let response = dialoguer::Select::new()
        .with_prompt(prompt)
        .items(&[
            "YES, I want to sync.  (WARNING: will overwrite remote ace server directory)",
            "NO, I want to cancel.",
        ])
        .default(1)
        .interact()
        .change_context(ErrorStack::SelectInteract)?;

    let canceled = response == 1;

    if canceled {
        error_stack::bail!(ErrorStack::YouCancelledTheSync)
    }

    // Call env.terraform_path executable with all of the remaining command line arguments
    let mut cmd = std::process::Command::new(cmd_path);
    cmd.arg("-avz");
    cmd.arg("--progress");
    cmd.arg("--delete");

    // /*/ is because the accountkey@region/ dir is seen as the first part of the transfer
    cmd.arg("--exclude").arg("/*/tmp/*");

    cmd.arg(&local_path);
    cmd.arg(&remote_path);
    cmd.stdout(std::process::Stdio::inherit());
    cmd.stderr(std::process::Stdio::inherit());

    cmd.status()
        .change_context(ErrorStack::ExecuteCommand(cmd_path.to_string()))?;

    println!("You moved to ace!");
    println!("Now automatically calling 'git status' on the remote...");
    let username_at_server_name = format!("app@{public_ip}");

    // Call 'git status' on the remote server (necessary to run 'ace system-update', or ace will complain it can't find a git repo)
    let git_status_path = &app.path;

    let mut cmd = std::process::Command::new(&app.ssh_bin_path);
    cmd.arg(username_at_server_name.as_str());
    cmd.arg("--");
    cmd.arg("git");
    cmd.arg("-C");
    cmd.arg(git_status_path);
    cmd.arg("status");
    cmd.stdout(std::process::Stdio::inherit());
    cmd.stderr(std::process::Stdio::inherit());
    cmd.stdin(std::process::Stdio::inherit());
    cmd.status()
        .change_context(ErrorStack::ExecuteRemoteGitStatus)?;

    println!("bin executable path: {:#?}", &app.bin_executable_path);

    let mut cmd = std::process::Command::new(&app.ssh_bin_path);
    cmd.arg(username_at_server_name.as_str());
    cmd.arg("--");
    cmd.arg(&app.bin_executable_path);
    cmd.arg("-C");
    cmd.arg(git_status_path);
    cmd.arg("system-update");
    cmd.stdout(std::process::Stdio::inherit());
    cmd.stderr(std::process::Stdio::inherit());
    cmd.stdin(std::process::Stdio::inherit());
    cmd.status()
        .change_context(ErrorStack::ExecuteRemoteSystemUpdate)?;

    Ok(())
}

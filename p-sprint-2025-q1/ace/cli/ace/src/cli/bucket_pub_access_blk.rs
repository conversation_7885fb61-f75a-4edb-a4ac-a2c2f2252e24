use ace_graph::GraphKeyExt;
use comfy_table::{ContentArrangement, Table, presets};

pub async fn list(verbose: bool) {
    let bucket_pub_access_blks_result = ace_graph::bucket_pub_access_blk::select_result(
        ace_graph::BucketPublicAccessBlockFilter::All,
    )
    .await;

    let mut errors = Vec::new();
    let mut bucket_pub_access_blks_sorted = Vec::new();

    // Handle overall fetching errors or proceed to filter and sort valid items
    match bucket_pub_access_blks_result {
        Ok(bucket_pub_access_blks) => {
            for bucket_pub_access_blk in bucket_pub_access_blks {
                match bucket_pub_access_blk {
                    Ok(bucket_pub_access_blk) => {
                        bucket_pub_access_blks_sorted.push(bucket_pub_access_blk)
                    }
                    Err(e) => errors.push(e),
                }
            }
        }
        Err(e) => {
            eprintln!("Error fetching bucket public access blocks: {e:#?}");
            return;
        }
    }

    // Sort bucket public access blocks by their graphkey
    bucket_pub_access_blks_sorted
        .sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    if verbose {
        for bucket_pub_access_blk in bucket_pub_access_blks_sorted {
            println!("\n{bucket_pub_access_blk:#?}");
        }
    } else {
        let mut table = Table::new();
        table.set_content_arrangement(ContentArrangement::Dynamic);
        if atty::is(atty::Stream::Stdout) {
            table.load_preset(presets::UTF8_FULL_CONDENSED);
        } else {
            table.load_preset(presets::NOTHING);
        }

        table.set_header(vec!["Graphkey"]);
        for bucket_pub_access_blk in bucket_pub_access_blks_sorted {
            table.add_row(vec![bucket_pub_access_blk.graphkey.to_string()]);
        }

        println!("{table}");
    }

    if !errors.is_empty() {
        println!("\nErrors:");
        for error in errors {
            println!("{error:#?}");
        }
    }
}

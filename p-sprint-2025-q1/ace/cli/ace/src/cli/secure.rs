use error_stack::ResultExt;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    CheckSecureStatus,
    MountSecure,
    SecureAlreadyMounted,
    SecureAlreadyUnmounted,
    UnmountSecure,
    GetSecureInfo,
}

pub fn mount(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    let is_mounted = app
        .is_secure_mounted()
        .change_context(ErrorStack::CheckSecureStatus)?;

    if is_mounted {
        error_stack::bail!(ErrorStack::SecureAlreadyMounted)
    }

    ace_core::gocryptfs::secure_mount(app).change_context(ErrorStack::MountSecure)
}

pub fn unmount(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    let is_mounted = app
        .is_secure_mounted()
        .change_context(ErrorStack::CheckSecureStatus)?;

    if !is_mounted {
        error_stack::bail!(ErrorStack::SecureAlreadyUnmounted)
    }

    ace_core::gocryptfs::secure_unmount(app).change_context(ErrorStack::UnmountSecure)
}

pub fn info(app: &ace_core::Application) -> error_stack::Result<(), ErrorStack> {
    ace_core::gocryptfs::secure_info(app).change_context(ErrorStack::GetSecureInfo)?;

    Ok(())
}

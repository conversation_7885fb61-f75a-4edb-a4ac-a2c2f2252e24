# Command-Line Help for `ace`

This document contains the help content for the `ace` command-line program.

**Command Overview:**

- [`ace`↴](#ace)
- [`ace app`↴](#ace-app)
- [`ace cfg`↴](#ace-cfg)
- [`ace etc`↴](#ace-etc)
- [`ace init`↴](#ace-init)
- [`ace build`↴](#ace-build)
- [`ace pkr`↴](#ace-pkr)
- [`ace tf`↴](#ace-tf)
- [`ace aws`↴](#ace-aws)
- [`ace commit`↴](#ace-commit)
- [`ace ssh`↴](#ace-ssh)
- [`ace move-to-ace`↴](#ace-move-to-ace)
- [`ace version`↴](#ace-version)
- [`ace check`↴](#ace-check)
- [`ace check ns`↴](#ace-check-ns)
- [`ace check ec2`↴](#ace-check-ec2)
- [`ace check all`↴](#ace-check-all)
- [`ace check server-cert`↴](#ace-check-server-cert)
- [`ace data`↴](#ace-data)
- [`ace mysqldevimg`↴](#ace-mysqldevimg)
- [`ace mysqldevimg build`↴](#ace-mysqldevimg-build)
- [`ace mysqldevimg push`↴](#ace-mysqldevimg-push)
- [`ace server`↴](#ace-server)
- [`ace server run`↴](#ace-server-run)
- [`ace server gen-cert`↴](#ace-server-gen-cert)
- [`ace dns`↴](#ace-dns)
- [`ace vpn`↴](#ace-vpn)
- [`ace vpn init`↴](#ace-vpn-init)
- [`ace vpn ls`↴](#ace-vpn-ls)
- [`ace vpn create-server`↴](#ace-vpn-create-server)
- [`ace vpn deploy-server`↴](#ace-vpn-deploy-server)
- [`ace vpn create-client`↴](#ace-vpn-create-client)
- [`ace vpn revoke-client`↴](#ace-vpn-revoke-client)
- [`ace vpn nuke-it-all`↴](#ace-vpn-nuke-it-all)
- [`ace system-update`↴](#ace-system-update)
- [`ace upgrade`↴](#ace-upgrade)
- [`ace markdown-help`↴](#ace-markdown-help)

## `ace`

AppCove Config Engine

**Usage:** `ace <COMMAND>`

###### **Subcommands:**

- `app` — Prints the current application configuration
- `cfg` — Prints the current configuration
- `etc` — Prints info from the etc directory
- `init` — Initializes a new local environment in the current directory
- `build` — Builds the output files for subsequent steps
- `pkr` — Run packer, passing additional args to packer binary
- `tf` — Run terraform, passing additional args to terraform binary
- `aws` — Commit all the files in this repo
- `commit` — Call the AWS CLI with the right credentials
- `ssh` — ssh to the server. Supports shortcuts like ace, vpn, graylog.
- `move-to-ace` — Prints the version
- `version` — Move this whole ACE directory to the ACE server
- `check` — Checks the status of DNS records and EC2 instances
- `data` — Prints your data.
- `mysqldevimg` — Builds mysql sanitized images.
- `server` — Ace Server Sub Command
- `dns` — DNS Lookup
- `vpn` — Manage VPN
- `system-update` — Updates things about this server
- `upgrade` — Upgrade this ace installation
- `markdown-help` — Prints the help in markdown format to stdout

## `ace app`

Prints the current application configuration

**Usage:** `ace app`

## `ace cfg`

Prints the current configuration

**Usage:** `ace cfg`

## `ace etc`

Prints info from the etc directory

**Usage:** `ace etc`

## `ace init`

Initializes a new local environment in the current directory

**Usage:** `ace init`

## `ace build`

Builds the output files for subsequent steps

**Usage:** `ace build`

## `ace pkr`

This command will run packer with the provided arguments.

It sets up the packer environment from LOCAL.env
and runs it in the packer sub directory.

**Usage:** `ace pkr [ARGS]...`

###### **Arguments:**

- `<ARGS>`

## `ace tf`

This command will run terraform with the provided arguments.

It sets up the terraform environment from LOCAL.env and runs it in
the terraform sub directory.

**Usage:** `ace tf [ARGS]...`

###### **Arguments:**

- `<ARGS>`

## `ace aws`

Equivilant to `git add -A; git commit -m 'checkpoint'.

**Usage:** `ace aws [ARGS]...`

###### **Arguments:**

- `<ARGS>`

## `ace commit`

This command will run the AWS CLI with the provided arguments.

It sets up the AWS environment from LOCAL.env.

**Usage:** `ace commit [OPTIONS]`

###### **Options:**

- `-m <MESSAGE>`

## `ace ssh`

This command will provide ssh with the proper credentials to ssh to anything that
ace has access to.

It can be called in these ways:
$ ace ssh ace-public
$ ace ssh ace-private
$ ace ssh ace
$ ace ssh vpn
$ ace ssh graylog
$ ace ssh some.server.example.com

If the server name is not fully qualified, it will be assumed to be at .{region}.{domain}.

TODO: more here

**Usage:** `ace ssh <SERVER> [ARGS]...`

###### **Arguments:**

- `<SERVER>`
- `<ARGS>`

## `ace move-to-ace`

Prints the version

**Usage:** `ace move-to-ace`

## `ace version`

Move this whole ACE directory to the ACE server

**Usage:** `ace version`

## `ace check`

Checks the status of DNS records and EC2 instances

**Usage:** `ace check [COMMAND]`

###### **Subcommands:**

- `ns` — Check DNS internal data against responses
- `ec2` — Check EC2 instances
- `all` — Run all checks
- `server-cert` — Check server ssl, ping and status

## `ace check ns`

Check DNS internal data against responses

**Usage:** `ace check ns`

## `ace check ec2`

Check EC2 instances

**Usage:** `ace check ec2`

## `ace check all`

Run all checks

**Usage:** `ace check all`

## `ace check server-cert`

Check server ssl, ping and status

**Usage:** `ace check server-cert`

## `ace data`

Prints your data.

**Usage:** `ace data`

## `ace mysqldevimg`

Builds mysql sanitized images.

**Usage:** `ace mysqldevimg [COMMAND]`

###### **Subcommands:**

- `build` —
- `push` — Push images

## `ace mysqldevimg build`

**Usage:** `ace mysqldevimg build <IDENTIFIER>`

###### **Arguments:**

- `<IDENTIFIER>` — The identifer (_) in the following: mysqldevimg._.toml

## `ace mysqldevimg push`

Push images

**Usage:** `ace mysqldevimg push <IDENTIFIER>`

###### **Arguments:**

- `<IDENTIFIER>` — The identifer (_) in the following: mysqldevimg._.toml

## `ace server`

Ace Server Sub Command

**Usage:** `ace server <COMMAND>`

###### **Subcommands:**

- `run` — Run the server
- `gen-cert` — Generate TLS Certificate

## `ace server run`

Run the server

**Usage:** `ace server run [OPTIONS]`

###### **Options:**

- `--host <HOST>`

  Default value: `0.0.0.0`
- `--port <PORT>`

  Default value: `443`

## `ace server gen-cert`

Generate TLS Certificate

**Usage:** `ace server gen-cert`

## `ace dns`

DNS Lookup

**Usage:** `ace dns <NAME>`

###### **Arguments:**

- `<NAME>`

## `ace vpn`

Manage VPN

**Usage:** `ace vpn <COMMAND>`

###### **Subcommands:**

- `init` — Initialize the data/ca directory with easyrsa
- `ls` — List the configuration files
- `create-server` — Create the server configuration
- `deploy-server` — Deploy the server configuration
- `create-client` — Create the client configuration
- `revoke-client` — Revoke the client configuration
- `nuke-it-all` — Nuke the data/ca and data/vpn-config directories

## `ace vpn init`

Initialize the data/ca directory with easyrsa

**Usage:** `ace vpn init`

## `ace vpn ls`

List the configuration files

**Usage:** `ace vpn ls`

## `ace vpn create-server`

Create the server configuration

**Usage:** `ace vpn create-server`

## `ace vpn deploy-server`

Deploy the server configuration

**Usage:** `ace vpn deploy-server [OPTIONS]`

###### **Options:**

- `--use-public-ip` — Use the public ip instead of the private ip
- `--dump-script` — Dump the script to stdout instead of running it

## `ace vpn create-client`

Create the client configuration

**Usage:** `ace vpn create-client <USERNAME>`

###### **Arguments:**

- `<USERNAME>` — The username to create

## `ace vpn revoke-client`

Revoke the client configuration

**Usage:** `ace vpn revoke-client <USERNAME>`

###### **Arguments:**

- `<USERNAME>` — The username to revoke

## `ace vpn nuke-it-all`

Nuke the data/ca and data/vpn-config directories

**Usage:** `ace vpn nuke-it-all`

## `ace system-update`

Updates things about this server

**Usage:** `ace system-update`

## `ace upgrade`

Upgrade this ace installation

**Usage:** `ace upgrade`

## `ace markdown-help`

Prints the help in markdown format to stdout

**Usage:** `ace markdown-help`

<hr/>

<small><i>
This document was generated automatically by
<a href="https://crates.io/crates/clap-markdown"><code>clap-markdown</code></a>.
</i></small>

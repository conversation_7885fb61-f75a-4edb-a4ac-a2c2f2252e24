#!/bin/bash

cd "$(dirname "$0")"

echo "Starting Docker containers for PostgreSQL and Redis..."
error=$(docker compose up -d 2>&1)

if [[ "$error" == *"Error response from daemon: Conflict. The container name"* ]]; then
    echo "Database container(s) already in place. Removing existing containers with force."
    docker rm postgres --force
    docker rm redis --force
    echo "Removed existing container(s)."
fi

docker compose up -d

echo "Docker containers started on ports 20000 (PostgreSQL) and 20001 (Redis)."


INSERT INTO auth_fence_provider.oauth2_scope
  (scope_id, name, description, active)
VALUES
  ('email', 'Email', 'Email address', true),
  ('profile', 'Profile', 'Profile information (user id, full name, profile picture)', true)
;

-- Example OAuth2 Client for localhost tesing
INSERT INTO auth_fence_provider.oauth2_client
  (oauth2_client_uuid, name, allowed_cidr, secret_hash, secret_salt, redirect_uris, scope_ids, pkce_enabled)
VALUES
-- secret: xT7vN2mWqR9zB8hJ5fD1cP4tL6rY0aZuE3wG9kN4
  ('019687ae-86c8-782d-9ed5-953cfe0ee593', 'Appcove Localhost', '{127.0.0.1/32,::1/128}', 'd62cb4a9921f8178143f9410456c16eb842758201b9bd3981d1f6e043bef9f7c', 'b7f26c364d18b92505a930042507a46d', '{https://local.acp7.net:3009/oauth2/df4l/redirect,https://local.acp7.net:3017/oauth2/google/redirect}', '{email,profile,d2c:read}', true)
;

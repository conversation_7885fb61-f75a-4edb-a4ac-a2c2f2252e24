

CREATE SCHEMA app_partnerapp;


--
-- Name: uuidv7(); Type: FUNCTION; Schema: public; Owner: app
--


--
-- Name: certification; Type: TABLE; Schema: app_partnerapp; Owner: app
--

CREATE TABLE app_partnerapp.certification (
    certification_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    create_ts timestamp(6) with time zone DEFAULT now() NOT NULL,
    name character varying(128) NOT NULL,
    description text,
    active boolean DEFAULT true NOT NULL
);



--
-- Name: partnerapp; Type: TABLE; Schema: app_partnerapp; Owner: app
--

CREATE TABLE app_partnerapp.partnerapp (
    partnerapp_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    create_ts timestamp(6) with time zone DEFAULT now() NOT NULL,
    partner_name character varying(128) NOT NULL,
    partner_email character varying(256) NOT NULL,
    partner_jobtitle text,
    status_xsid character varying(32) NOT NULL,
    company_name text NOT NULL,
    company_line1 text,
    company_line2 text,
    company_city text,
    company_state text,
    company_postal_code text,
    company_country_code character(2),
    company_phone character varying(32),
    company_tax_id text,
    company_history character varying(4096),
    company_management_info character varying(4096),
    company_timezone character varying(64) NOT NULL,
    company_website_url character varying(256),
    datacenter_timezone character varying(64) NOT NULL,
    datacenter_line1 text,
    datacenter_line2 text,
    datacenter_city text,
    datacenter_state text,
    datacenter_postal_code text,
    datacenter_country_code character(2),
    datacenter_phone character varying(32),
    support_ticket_url character varying(256),
    security_incident_contact_name text NOT NULL,
    security_incident_contact_email text NOT NULL,
    security_incident_contact_phone text NOT NULL,
    noc_contact_name text NOT NULL,
    noc_contact_email text NOT NULL,
    noc_contact_phone text NOT NULL,
    offer_info text,
    certification_uuids uuid[] DEFAULT ARRAY[]::uuid[] NOT NULL
);

--
-- Name: status; Type: TABLE; Schema: app_partnerapp; Owner: app
--

CREATE TABLE app_partnerapp.status (
    status_xsid character varying(32) NOT NULL,
    create_ts timestamp(6) with time zone DEFAULT now() NOT NULL,
    label character varying(128) NOT NULL,
    active boolean DEFAULT true NOT NULL,
    sort integer NOT NULL,
    foreground_color character varying(32),
    background_color character varying(32)
);


--
-- Data for Name: certification; Type: TABLE DATA; Schema: app_partnerapp; Owner: app
--

COPY app_partnerapp.certification (certification_uuid, create_ts, name, description, active) FROM stdin;
0192de18-09ed-7467-a797-ffb9868436bb	2024-10-30 15:42:50.348749+00	PCI Level 4	Intended for small merchants with low transaction volumes, emphasizing basic security practices without the need for formal audits.\t	t
\.


--
-- Data for Name: partnerapp; Type: TABLE DATA; Schema: app_partnerapp; Owner: app
--

COPY app_partnerapp.partnerapp (partnerapp_uuid, create_ts, partner_name, partner_email, partner_jobtitle, status_xsid, company_name, company_line1, company_line2, company_city, company_state, company_postal_code, company_country_code, company_phone, company_tax_id, company_history, company_management_info, company_timezone, company_website_url, datacenter_timezone, datacenter_line1, datacenter_line2, datacenter_city, datacenter_state, datacenter_postal_code, datacenter_country_code, datacenter_phone, support_ticket_url, security_incident_contact_name, security_incident_contact_email, security_incident_contact_phone, noc_contact_name, noc_contact_email, noc_contact_phone, offer_info, certification_uuids) FROM stdin;
0192de20-4ee4-7391-a72b-3799aaa4d292	2024-10-30 15:51:52.292021+00	Joe Networker	<EMAIL>	President	Active	Super Switches Networking	600 Adams Ave	\N	Altoon	PA	16601	US	************	75-854-8541	We build stuff.	Run by Joe Networker	US/Eastern	https://wikipedia.org	US/Central	034 Tech Suites Way	\N	Nashville	TN	38838	US	************	\N	Joe Contact	<EMAIL>	************	Joe NOC	<EMAIL>	************	\N	{}
\.


--
-- Data for Name: status; Type: TABLE DATA; Schema: app_partnerapp; Owner: app
--

COPY app_partnerapp.status (status_xsid, create_ts, label, active, sort, foreground_color, background_color) FROM stdin;
Triage	2024-10-30 15:46:17.937963+00	Triage	t	1	#000	#999
AutoReject	2024-10-30 15:46:44.433582+00	Auto Reject	t	20	#f00	#999
Pending	2024-10-30 15:47:19.999463+00	Pending	t	2	#fff	#009
ManualReject	2024-10-30 15:47:45.534446+00	Rejected	t	21	#f00	#999
Active	2024-10-30 15:48:18.8904+00	Active	t	4	#fff	#089
Accepted	2024-10-30 15:48:58.915738+00	Accepted	t	24	#fff	#000
\.


--
-- Name: certification certification_name_key; Type: CONSTRAINT; Schema: app_partnerapp; Owner: app
--

ALTER TABLE ONLY app_partnerapp.certification
    ADD CONSTRAINT certification_name_key UNIQUE (name);


--
-- Name: certification certification_pkey; Type: CONSTRAINT; Schema: app_partnerapp; Owner: app
--

ALTER TABLE ONLY app_partnerapp.certification
    ADD CONSTRAINT certification_pkey PRIMARY KEY (certification_uuid);


--
-- Name: partnerapp partnerapp_pkey; Type: CONSTRAINT; Schema: app_partnerapp; Owner: app
--

ALTER TABLE ONLY app_partnerapp.partnerapp
    ADD CONSTRAINT partnerapp_pkey PRIMARY KEY (partnerapp_uuid);


--
-- Name: status status_label_key; Type: CONSTRAINT; Schema: app_partnerapp; Owner: app
--

ALTER TABLE ONLY app_partnerapp.status
    ADD CONSTRAINT status_label_key UNIQUE (label);


--
-- Name: status status_pkey; Type: CONSTRAINT; Schema: app_partnerapp; Owner: app
--

ALTER TABLE ONLY app_partnerapp.status
    ADD CONSTRAINT status_pkey PRIMARY KEY (status_xsid);


--
-- Name: status_sort; Type: INDEX; Schema: app_partnerapp; Owner: app
--

CREATE UNIQUE INDEX status_sort ON app_partnerapp.status USING btree (sort);


--
-- Name: partnerapp partnerapp_status_fk; Type: FK CONSTRAINT; Schema: app_partnerapp; Owner: app
--

ALTER TABLE ONLY app_partnerapp.partnerapp
    ADD CONSTRAINT partnerapp_status_fk FOREIGN KEY (status_xsid) REFERENCES app_partnerapp.status(status_xsid) ON UPDATE RESTRICT ON DELETE RESTRICT;



CREATE SCHEMA app_metric;

CREATE TABLE app_metric.ingest (
  ingest_uuid uuid DEFAULT public.uuidv7() NOT NULL,
  create_ts timestamptz(6) NOT NULL DEFAULT now(),
  name varchar(128) NOT NULL,
  description text,
  active boolean DEFAULT true NOT NULL,
  CONSTRAINT ingest_pkey PRIMARY KEY (ingest_uuid)
)
;





CREATE SCHEMA app;

CREATE TABLE app.contact_xsid (
  contact_xsid varchar(32) NOT NULL, --Billing, Security Incident, Network Operations
  create_ts timestamptz(6) NOT NULL DEFAULT now(),
  label varchar(128) NOT NULL,
  CONSTRAINT contact_xsid_pkey PRIMARY KEY (contact_xsid)
)
;

--Valid contact types for partners
CREATE TABLE app.partner_contact_xsid (
  contact_xsid varchar(32) NOT NULL,
  create_ts timestamptz(6) NOT NULL DEFAULT now(),
  CONSTRAINT partner_contact_xsid_pkey PRIMARY KEY (contact_xsid),
  CONSTRAINT "partner_contact>>contact_xsid" FOREIGN KEY (contact_xsid) REFERENCES app.contact_xsid (contact_xsid) ON DELETE RESTRICT ON UPDATE CASCADE
);

--app.datacenter_contact_xsid

CREATE TABLE app.datacenter_contact_xsid (
	contact_xsid text,
  create_ts timestamptz(6) NOT NULL DEFAULT now(),
  CONSTRAINT datacenter_contact_xsid_pkey PRIMARY KEY (contact_xsid),
	CONSTRAINT "datacenter_contact_xsid>>contact_xsid" FOREIGN KEY (contact_xsid) REFERENCES app.contact_xsid(contact_xsid) ON DELETE RESTRICT ON UPDATE CASCADE
);




--Runpod partner, specifically in the data center space
CREATE TABLE app.partner (
    partner_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
		name varchar(128) NOT NULL,
		email varchar(256),
    address text,
    note text,
    active bool NOT NULL DEFAULT true,
    CONSTRAINT partner_pkey PRIMARY KEY (partner_uuid)
);



--Contacts represent a specific individual with contact information
CREATE TABLE app.contact (
    contact_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
		firstname varchar(128) NOT NULL,
		lastname varchar(128) NOT NULL,
    title varchar(128),
    company varchar(128),
		email varchar(256),
		timezone varchar(64),
		phone varchar(32),
    note text,
    active bool NOT NULL DEFAULT true,
    CONSTRAINT contact_pkey PRIMARY KEY (contact_uuid)
);

--Which contacts are part of this partner
CREATE TABLE app.partner_contact (
    --partner_contact_uuid uuid DEFAULT public.uuidv7() NOT NULL,
		partner_uuid uuid NOT NULL,
		contact_uuid uuid NOT NULL,
		contact_xsid varchar(32) NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
		note text,
    CONSTRAINT partner_contact_pkey PRIMARY KEY (partner_uuid, contact_uuid, contact_xsid),
		CONSTRAINT "partner_contact>>partner" FOREIGN KEY (partner_uuid) REFERENCES app.partner (partner_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
		CONSTRAINT "partner_contact>>contact" FOREIGN KEY (contact_uuid) REFERENCES app.contact (contact_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
		CONSTRAINT "partner_contact>>contact_xsid" FOREIGN KEY (contact_xsid) REFERENCES app.partner_contact_xsid (contact_xsid) ON DELETE RESTRICT ON UPDATE RESTRICT
);



--Data centers are owned by partners
CREATE TABLE app.datacenter (
  datacenter_uuid uuid DEFAULT public.uuidv7() NOT NULL,
	partner_uuid uuid NOT NULL,
	name varchar(128) NOT NULL,
	description text,
  create_ts timestamptz(6) NOT NULL DEFAULT now(),
  datacenter_esid varchar(32),
  timezone varchar(64) NULL,
  address text,
	note text,
  CONSTRAINT datacenter_pkey PRIMARY KEY (datacenter_uuid), -- Composite primary key
  CONSTRAINT "datacenter>>partner" FOREIGN KEY (partner_uuid) REFERENCES app.partner (partner_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

--Which contacts are tied to this data center. 
CREATE TABLE app.datacenter_contact (
	datacenter_uuid uuid NOT NULL,
	contact_xsid text,
	contact_uuid uuid NOT NULL,
  create_ts timestamptz(6) NOT NULL DEFAULT now(),
	note text,
  CONSTRAINT datacenter_contact_pkey PRIMARY KEY (datacenter_uuid, contact_xsid, contact_uuid), -- Composite primary key
  CONSTRAINT "datacenter_contact>>datacenter" FOREIGN KEY (datacenter_uuid) REFERENCES app.datacenter (datacenter_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
	CONSTRAINT "datacenter_contact>>contact" FOREIGN KEY (contact_uuid) REFERENCES app.contact(contact_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT "datacenter_contact>>contact_xsid" FOREIGN KEY (contact_xsid) REFERENCES app.datacenter_contact_xsid(contact_xsid) ON DELETE RESTRICT ON UPDATE RESTRICT
);




--
-- PostgreSQL database dump complete
--


-- Add partner_gnid to partner table with a start value of 200
ALTER TABLE app.partner ADD COLUMN partner_gnid INTEGER GENERATED ALWAYS AS IDENTITY (START WITH 200),
ADD UNIQUE (partner_gnid);



-- drop existing fkeys
ALTER TABLE app.partner_contact_xsid DROP CONSTRAINT "partner_contact>>contact_xsid";
ALTER TABLE app.partner_contact DROP CONSTRAINT "partner_contact>>contact_xsid";

ALTER TABLE app.datacenter_contact_xsid DROP CONSTRAINT "datacenter_contact_xsid>>contact_xsid";
ALTER TABLE app.datacenter_contact DROP CONSTRAINT "datacenter_contact>>contact_xsid";




CREATE TABLE app.contact_xuid (
    contact_xuid varchar(32) NOT NULL,
    contact_xsid varchar(32),
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    label varchar(128) NOT NULL,
    CONSTRAINT contact_xuid_pk PRIMARY KEY (contact_xuid)
)
;

INSERT INTO app.contact_xuid (contact_xuid, create_ts, label)
SELECT t2.contact_xsid, t2.create_ts, COALESCE(t2.label, '')
FROM app.contact_xsid t2;




CREATE TABLE app.partner_contact_xuid (
    contact_xuid varchar(32) NOT NULL,
    contact_xsid varchar(32),
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    label varchar(128) NOT NULL,
    CONSTRAINT partner_contact_xuid_pk PRIMARY KEY (contact_xuid),
    CONSTRAINT "partner_contact_xuid>>contact_xuid" FOREIGN KEY (contact_xuid) REFERENCES app.contact_xuid (contact_xuid) ON DELETE RESTRICT ON UPDATE CASCADE
)
;

INSERT INTO app.partner_contact_xuid (contact_xuid, create_ts, label)
SELECT t2.contact_xsid, t2.create_ts, ''
FROM app.partner_contact_xsid t2;




CREATE TABLE app.datacenter_contact_xuid (
    contact_xuid varchar(32) NOT NULL,
    contact_xsid varchar(32),
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    label varchar(128) NOT NULL,
    CONSTRAINT datacenter_contact_xuid_pk PRIMARY KEY (contact_xuid),
    CONSTRAINT "datacenter_contact_xuid>>contact_xuid" FOREIGN KEY (contact_xuid) REFERENCES app.contact_xuid (contact_xuid) ON DELETE RESTRICT ON UPDATE CASCADE
)
;

INSERT INTO app.datacenter_contact_xuid (contact_xuid, create_ts, label)
SELECT t2.contact_xsid, t2.create_ts, ''
FROM app.datacenter_contact_xsid t2;



ALTER TABLE app.partner_contact RENAME COLUMN contact_xsid TO contact_xuid;
ALTER TABLE app.datacenter_contact RENAME COLUMN contact_xsid TO contact_xuid;

ALTER TABLE app.partner_contact ADD CONSTRAINT "partner_contact>>contact_xuid" FOREIGN KEY (contact_xuid) REFERENCES app.partner_contact_xuid (contact_xuid);
ALTER TABLE app.datacenter_contact ADD CONSTRAINT "datacenter_contact>>contact_xuid" FOREIGN KEY (contact_xuid) REFERENCES app.datacenter_contact_xuid (contact_xuid);



DROP TABLE app.contact_xsid;
DROP TABLE app.partner_contact_xsid;
DROP TABLE app.datacenter_contact_xsid;


ALTER TABLE app.partner
    RENAME COLUMN partner_gnid TO partner_unid;



ALTER TABLE app.partner
    DROP COLUMN active;

CREATE TYPE app.partner_status as ENUM (
    'Prospect',
    'Rejected',
    'Contracted',
    'Terminated'
);

ALTER TABLE app.partner
    ADD COLUMN status app.partner_status NOT NULL DEFAULT 'Prospect',
    ADD COLUMN active boolean NOT NULL GENERATED ALWAYS AS (status IN ('Prospect', 'Contracted')) STORED;


CREATE TYPE app.datacenter_status as ENUM (
    'Prospect',
    'Rejected',
    'Launching',
    'Active',
    'Retiring',
    'Terminated'
);

ALTER TABLE app.datacenter
    ADD COLUMN status app.datacenter_status NOT NULL DEFAULT 'Prospect',
    ADD COLUMN active boolean NOT NULL GENERATED ALWAYS AS ( status IN ('Prospect', 'Launching', 'Active', 'Retiring')) STORED;


CREATE TABLE app.Δcontact (
    contact_uuid uuid NOT NULL,
    Δuuid uuid NOT NULL DEFAULT public.uuidv7(),
    Δtimestamp timestamp(6) with time zone NOT NULL DEFAULT NOW(),
    firstnameΔ boolean NOT NULL DEFAULT false,
    firstname character varying(128),
    lastnameΔ boolean NOT NULL DEFAULT false,
    lastname character varying(128),
    titleΔ boolean NOT NULL DEFAULT false,
    title character varying(128),
    companyΔ boolean NOT NULL DEFAULT false,
    company character varying(128),
    emailΔ boolean NOT NULL DEFAULT false,
    email character varying(256),
    timezoneΔ boolean NOT NULL DEFAULT false,
    timezone character varying(64),
    phoneΔ boolean NOT NULL DEFAULT false,
    phone character varying(32),
    noteΔ boolean NOT NULL DEFAULT false,
    note text,
    activeΔ boolean NOT NULL DEFAULT false,
    active boolean,
    CONSTRAINT Δcontact_pkey PRIMARY KEY (contact_uuid, Δuuid)
);

-- Insert existing rows into app.Δcontact
INSERT INTO app.Δcontact (
    contact_uuid, Δtimestamp, firstnameΔ, firstname, lastnameΔ, lastname, titleΔ, title, companyΔ, company, emailΔ, email, timezoneΔ, timezone, phoneΔ, phone, noteΔ, note, activeΔ, active
) SELECT 
    contact_uuid, NOW(), false, firstname, false, lastname, false, title, false, company, false, email, false, timezone, false, phone, false, note, false, active 
FROM app.contact;

CREATE OR REPLACE FUNCTION app."contact@insert|update"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δcontact (
        contact_uuid,
        firstnameΔ,
        firstname,
        lastnameΔ,
        lastname,
        titleΔ,
        title,
        companyΔ,
        company,
        emailΔ,
        email,
        timezoneΔ,
        timezone,
        phoneΔ,
        phone,
        noteΔ,
        note,
        activeΔ,
        active
    ) VALUES (
        NEW.contact_uuid,
        CASE WHEN OLD.firstname IS DISTINCT FROM NEW.firstname THEN true ELSE false END,
        CASE WHEN OLD.firstname IS DISTINCT FROM NEW.firstname THEN NEW.firstname ELSE NULL END,
        CASE WHEN OLD.lastname IS DISTINCT FROM NEW.lastname THEN true ELSE false END,
        CASE WHEN OLD.lastname IS DISTINCT FROM NEW.lastname THEN NEW.lastname ELSE NULL END,
        CASE WHEN OLD.title IS DISTINCT FROM NEW.title THEN true ELSE false END,
        CASE WHEN OLD.title IS DISTINCT FROM NEW.title THEN NEW.title ELSE NULL END,
        CASE WHEN OLD.company IS DISTINCT FROM NEW.company THEN true ELSE false END,
        CASE WHEN OLD.company IS DISTINCT FROM NEW.company THEN NEW.company ELSE NULL END,
        CASE WHEN OLD.email IS DISTINCT FROM NEW.email THEN true ELSE false END,
        CASE WHEN OLD.email IS DISTINCT FROM NEW.email THEN NEW.email ELSE NULL END,
        CASE WHEN OLD.timezone IS DISTINCT FROM NEW.timezone THEN true ELSE false END,
        CASE WHEN OLD.timezone IS DISTINCT FROM NEW.timezone THEN NEW.timezone ELSE NULL END,
        CASE WHEN OLD.phone IS DISTINCT FROM NEW.phone THEN true ELSE false END,
        CASE WHEN OLD.phone IS DISTINCT FROM NEW.phone THEN NEW.phone ELSE NULL END,
        CASE WHEN OLD.note IS DISTINCT FROM NEW.note THEN true ELSE false END,
        CASE WHEN OLD.note IS DISTINCT FROM NEW.note THEN NEW.note ELSE NULL END,
        CASE WHEN OLD.active IS DISTINCT FROM NEW.active THEN true ELSE false END,
        CASE WHEN OLD.active IS DISTINCT FROM NEW.active THEN NEW.active ELSE NULL END
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "contact@insert"
AFTER INSERT ON app.contact
FOR EACH ROW
EXECUTE PROCEDURE app."contact@insert|update"();

CREATE TRIGGER "contact@update"
AFTER UPDATE ON app.contact
FOR EACH ROW
WHEN (OLD.* IS DISTINCT FROM NEW.*)
EXECUTE PROCEDURE app."contact@insert|update"();

CREATE OR REPLACE FUNCTION app."contact@delete"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δcontact (
        contact_uuid,
        firstnameΔ, firstname,
        lastnameΔ, lastname,
        titleΔ, title,
        companyΔ, company,
        emailΔ, email,
        timezoneΔ, timezone,
        phoneΔ, phone,
        noteΔ, note,
        activeΔ, active
    ) VALUES (
        OLD.contact_uuid,
        true, NULL,
        true, NULL,
        true, NULL,
        true, NULL,
        true, NULL,
        true, NULL,
        true, NULL,
        true, NULL,
        true, NULL
    );
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "contact@delete"
AFTER DELETE ON app.contact
FOR EACH ROW
EXECUTE PROCEDURE app."contact@delete"();

CREATE TABLE app.Δcontact_xuid (
    contact_xuid uuid NOT NULL,
    Δuuid uuid NOT NULL DEFAULT public.uuidv7(),
    Δtimestamp timestamp(6) with time zone NOT NULL DEFAULT NOW(),
    contact_xsidΔ boolean NOT NULL DEFAULT false,
    contact_xsid character varying(32),
    labelΔ boolean NOT NULL DEFAULT false,
    label character varying(128),
    CONSTRAINT Δcontact_xuid_pkey PRIMARY KEY (contact_xuid, Δuuid)
);

-- Insert existing rows into app.Δcontact_xuid
INSERT INTO app.Δcontact_xuid (
    contact_xuid, Δtimestamp, contact_xsidΔ, contact_xsid, labelΔ, label
) SELECT 
    contact_xuid::uuid, NOW(), false, contact_xsid, false, label 
FROM app.contact_xuid;

CREATE OR REPLACE FUNCTION app."contact_xuid@insert|update"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δcontact_xuid (
        contact_xuid,
        contact_xsidΔ,
        contact_xsid,
        labelΔ,
        label
    ) VALUES (
        NEW.contact_xuid::uuid,
        CASE WHEN OLD.contact_xsid IS DISTINCT FROM NEW.contact_xsid THEN true ELSE false END,
        CASE WHEN OLD.contact_xsid IS DISTINCT FROM NEW.contact_xsid THEN NEW.contact_xsid ELSE NULL END,
        CASE WHEN OLD.label IS DISTINCT FROM NEW.label THEN true ELSE false END,
        CASE WHEN OLD.label IS DISTINCT FROM NEW.label THEN NEW.label ELSE NULL END
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "contact_xuid@insert"
AFTER INSERT ON app.contact_xuid
FOR EACH ROW
EXECUTE PROCEDURE app."contact_xuid@insert|update"();

CREATE TRIGGER "contact_xuid@update"
AFTER UPDATE ON app.contact_xuid
FOR EACH ROW
WHEN (OLD.* IS DISTINCT FROM NEW.*)
EXECUTE PROCEDURE app."contact_xuid@insert|update"();

CREATE OR REPLACE FUNCTION app."contact_xuid@delete"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δcontact_xuid (
        contact_xuid,
        contact_xsidΔ, contact_xsid,
        labelΔ, label
    ) VALUES (
        OLD.contact_xuid::uuid,
        true, NULL,
        true, NULL
    );
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "contact_xuid@delete"
AFTER DELETE ON app.contact_xuid
FOR EACH ROW
EXECUTE PROCEDURE app."contact_xuid@delete"();

CREATE TABLE app.Δdatacenter (
    datacenter_uuid uuid NOT NULL,
    Δuuid uuid NOT NULL DEFAULT public.uuidv7(),
    Δtimestamp timestamp(6) with time zone NOT NULL DEFAULT NOW(),
    partner_uuidΔ boolean NOT NULL DEFAULT false,
    partner_uuid uuid,
    nameΔ boolean NOT NULL DEFAULT false,
    name character varying(128),
    descriptionΔ boolean NOT NULL DEFAULT false,
    description text,
    datacenter_esidΔ boolean NOT NULL DEFAULT false,
    datacenter_esid character varying(32),
    timezoneΔ boolean NOT NULL DEFAULT false,
    timezone character varying(64),
    addressΔ boolean NOT NULL DEFAULT false,
    address text,
    noteΔ boolean NOT NULL DEFAULT false,
    note text,
    CONSTRAINT Δdatacenter_pkey PRIMARY KEY (datacenter_uuid, Δuuid)
);

-- Insert existing rows into app.Δdatacenter
INSERT INTO app.Δdatacenter (
    datacenter_uuid, Δtimestamp, partner_uuidΔ, partner_uuid, nameΔ, name, descriptionΔ, description, datacenter_esidΔ, datacenter_esid, timezoneΔ, timezone, addressΔ, address, noteΔ, note
) SELECT 
    datacenter_uuid, NOW(), false, partner_uuid, false, name, false, description, false, datacenter_esid, false, timezone, false, address, false, note 
FROM app.datacenter;

CREATE OR REPLACE FUNCTION app."datacenter@insert|update"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δdatacenter (
        datacenter_uuid,
        partner_uuidΔ,
        partner_uuid,
        nameΔ,
        name,
        descriptionΔ,
        description,
        datacenter_esidΔ,
        datacenter_esid,
        timezoneΔ,
        timezone,
        addressΔ,
        address,
        noteΔ,
        note
    ) VALUES (
        NEW.datacenter_uuid,
        CASE WHEN OLD.partner_uuid IS DISTINCT FROM NEW.partner_uuid THEN true ELSE false END,
        CASE WHEN OLD.partner_uuid IS DISTINCT FROM NEW.partner_uuid THEN NEW.partner_uuid ELSE NULL END,
        CASE WHEN OLD.name IS DISTINCT FROM NEW.name THEN true ELSE false END,
        CASE WHEN OLD.name IS DISTINCT FROM NEW.name THEN NEW.name ELSE NULL END,
        CASE WHEN OLD.description IS DISTINCT FROM NEW.description THEN true ELSE false END,
        CASE WHEN OLD.description IS DISTINCT FROM NEW.description THEN NEW.description ELSE NULL END,
        CASE WHEN OLD.datacenter_esid IS DISTINCT FROM NEW.datacenter_esid THEN true ELSE false END,
        CASE WHEN OLD.datacenter_esid IS DISTINCT FROM NEW.datacenter_esid THEN NEW.datacenter_esid ELSE NULL END,
        CASE WHEN OLD.timezone IS DISTINCT FROM NEW.timezone THEN true ELSE false END,
        CASE WHEN OLD.timezone IS DISTINCT FROM NEW.timezone THEN NEW.timezone ELSE NULL END,
        CASE WHEN OLD.address IS DISTINCT FROM NEW.address THEN true ELSE false END,
        CASE WHEN OLD.address IS DISTINCT FROM NEW.address THEN NEW.address ELSE NULL END,
        CASE WHEN OLD.note IS DISTINCT FROM NEW.note THEN true ELSE false END,
        CASE WHEN OLD.note IS DISTINCT FROM NEW.note THEN NEW.note ELSE NULL END
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "datacenter@insert"
AFTER INSERT ON app.datacenter
FOR EACH ROW
EXECUTE PROCEDURE app."datacenter@insert|update"();

CREATE TRIGGER "datacenter@update"
AFTER UPDATE ON app.datacenter
FOR EACH ROW
WHEN (OLD.* IS DISTINCT FROM NEW.*)
EXECUTE PROCEDURE app."datacenter@insert|update"();

CREATE OR REPLACE FUNCTION app."datacenter@delete"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δdatacenter (
        datacenter_uuid,
        partner_uuidΔ, partner_uuid,
        nameΔ, name,
        descriptionΔ, description,
        datacenter_esidΔ, datacenter_esid,
        timezoneΔ, timezone,
        addressΔ, address,
        noteΔ, note
    ) VALUES (
        OLD.datacenter_uuid,
        true, NULL,
        true, NULL,
        true, NULL,
        true, NULL,
        true, NULL,
        true, NULL,
        true, NULL
    );
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "datacenter@delete"
AFTER DELETE ON app.datacenter
FOR EACH ROW
EXECUTE PROCEDURE app."datacenter@delete"();

CREATE TABLE app.Δdatacenter_contact (
    datacenter_uuid uuid NOT NULL,
    Δuuid uuid NOT NULL DEFAULT public.uuidv7(),
    Δtimestamp timestamp(6) with time zone NOT NULL DEFAULT NOW(),
    contact_xuidΔ boolean NOT NULL DEFAULT false,
    contact_xuid uuid,
    contact_uuidΔ boolean NOT NULL DEFAULT false,
    contact_uuid uuid,
    noteΔ boolean NOT NULL DEFAULT false,
    note text,
    CONSTRAINT Δdatacenter_contact_pkey PRIMARY KEY (datacenter_uuid, Δuuid)
);

-- Insert existing rows into app.Δdatacenter_contact
INSERT INTO app.Δdatacenter_contact (
    datacenter_uuid, Δtimestamp, contact_xuidΔ, contact_xuid, contact_uuidΔ, contact_uuid, noteΔ, note
) SELECT 
    datacenter_uuid::uuid, NOW(), false, contact_xuid::uuid, false, contact_uuid, false, note 
FROM app.datacenter_contact;

CREATE OR REPLACE FUNCTION app."datacenter_contact@insert|update"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δdatacenter_contact (
        datacenter_uuid,
        contact_xuidΔ,
        contact_xuid,
        contact_uuidΔ,
        contact_uuid,
        noteΔ,
        note
    ) VALUES (
        NEW.datacenter_uuid,
        CASE WHEN OLD.contact_xuid IS DISTINCT FROM NEW.contact_xuid THEN true ELSE false END,
        CASE WHEN OLD.contact_xuid IS DISTINCT FROM NEW.contact_xuid THEN NEW.contact_xuid::uuid ELSE NULL END,
        CASE WHEN OLD.contact_uuid IS DISTINCT FROM NEW.contact_uuid THEN true ELSE false END,
        CASE WHEN OLD.contact_uuid IS DISTINCT FROM NEW.contact_uuid THEN NEW.contact_uuid ELSE NULL END,
        CASE WHEN OLD.note IS DISTINCT FROM NEW.note THEN true ELSE false END,
        CASE WHEN OLD.note IS DISTINCT FROM NEW.note THEN NEW.note ELSE NULL END
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "datacenter_contact@insert"
AFTER INSERT ON app.datacenter_contact
FOR EACH ROW
EXECUTE PROCEDURE app."datacenter_contact@insert|update"();

CREATE TRIGGER "datacenter_contact@update"
AFTER UPDATE ON app.datacenter_contact
FOR EACH ROW
WHEN (OLD.* IS DISTINCT FROM NEW.*)
EXECUTE PROCEDURE app."datacenter_contact@insert|update"();

CREATE OR REPLACE FUNCTION app."datacenter_contact@delete"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δdatacenter_contact (
        datacenter_uuid,
        contact_xuidΔ, contact_xuid,
        contact_uuidΔ, contact_uuid,
        noteΔ, note
    ) VALUES (
        OLD.datacenter_uuid,
        true, NULL,
        true, NULL,
        true, NULL
    );
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "datacenter_contact@delete"
AFTER DELETE ON app.datacenter_contact
FOR EACH ROW
EXECUTE PROCEDURE app."datacenter_contact@delete"();

CREATE TABLE app.Δdatacenter_contact_xuid (
    contact_xuid uuid NOT NULL,
    Δuuid uuid NOT NULL DEFAULT public.uuidv7(),
    Δtimestamp timestamp(6) with time zone NOT NULL DEFAULT NOW(),
    CONSTRAINT Δdatacenter_contact_xuid_pkey PRIMARY KEY (contact_xuid, Δuuid)
);

-- Insert existing rows into app.Δdatacenter_contact_xuid
INSERT INTO app.Δdatacenter_contact_xuid (
    contact_xuid, Δtimestamp
) SELECT 
    contact_xuid::uuid, NOW()
FROM app.datacenter_contact_xuid;

CREATE OR REPLACE FUNCTION app."datacenter_contact_xuid@insert|update"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δdatacenter_contact_xuid (
        contact_xuid
    ) VALUES (
        NEW.contact_xuid::uuid
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "datacenter_contact_xuid@insert"
AFTER INSERT ON app.datacenter_contact_xuid
FOR EACH ROW
EXECUTE PROCEDURE app."datacenter_contact_xuid@insert|update"();

CREATE TRIGGER "datacenter_contact_xuid@update"
AFTER UPDATE ON app.datacenter_contact_xuid
FOR EACH ROW
WHEN (OLD.* IS DISTINCT FROM NEW.*)
EXECUTE PROCEDURE app."datacenter_contact_xuid@insert|update"();

CREATE OR REPLACE FUNCTION app."datacenter_contact_xuid@delete"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δdatacenter_contact_xuid (
        contact_xuid
    ) VALUES (
        OLD.contact_xuid::uuid
    );
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "datacenter_contact_xuid@delete"
AFTER DELETE ON app.datacenter_contact_xuid
FOR EACH ROW
EXECUTE PROCEDURE app."datacenter_contact_xuid@delete"();

CREATE TABLE app.Δpartner (
    partner_uuid uuid NOT NULL,
    Δuuid uuid NOT NULL DEFAULT public.uuidv7(),
    Δtimestamp timestamp(6) with time zone NOT NULL DEFAULT NOW(),
    nameΔ boolean NOT NULL DEFAULT false,
    name character varying(128),
    emailΔ boolean NOT NULL DEFAULT false,
    email character varying(256),
    addressΔ boolean NOT NULL DEFAULT false,
    address text,
    noteΔ boolean NOT NULL DEFAULT false,
    note text,
    activeΔ boolean NOT NULL DEFAULT false,
    active boolean,
    partner_unidΔ boolean NOT NULL DEFAULT false,
    partner_unid integer,
    CONSTRAINT Δpartner_pkey PRIMARY KEY (partner_uuid, Δuuid)
);

-- Insert existing rows into app.Δpartner
INSERT INTO app.Δpartner (
    partner_uuid, Δtimestamp, nameΔ, name, emailΔ, email, addressΔ, address, noteΔ, note, activeΔ, active, partner_unidΔ, partner_unid
) SELECT 
    partner_uuid, NOW(), false, name, false, email, false, address, false, note, false, active, false, partner_unid 
FROM app.partner;

CREATE OR REPLACE FUNCTION app."partner@insert|update"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δpartner (
        partner_uuid,
        nameΔ,
        name,
        emailΔ,
        email,
        addressΔ,
        address,
        noteΔ,
        note,
        activeΔ,
        active,
        partner_unidΔ,
        partner_unid
    ) VALUES (
        NEW.partner_uuid,
        CASE WHEN OLD.name IS DISTINCT FROM NEW.name THEN true ELSE false END,
        CASE WHEN OLD.name IS DISTINCT FROM NEW.name THEN NEW.name ELSE NULL END,
        CASE WHEN OLD.email IS DISTINCT FROM NEW.email THEN true ELSE false END,
        CASE WHEN OLD.email IS DISTINCT FROM NEW.email THEN NEW.email ELSE NULL END,
        CASE WHEN OLD.address IS DISTINCT FROM NEW.address THEN true ELSE false END,
        CASE WHEN OLD.address IS DISTINCT FROM NEW.address THEN NEW.address ELSE NULL END,
        CASE WHEN OLD.note IS DISTINCT FROM NEW.note THEN true ELSE false END,
        CASE WHEN OLD.note IS DISTINCT FROM NEW.note THEN NEW.note ELSE NULL END,
        CASE WHEN OLD.active IS DISTINCT FROM NEW.active THEN true ELSE false END,
        CASE WHEN OLD.active IS DISTINCT FROM NEW.active THEN NEW.active ELSE NULL END,
        CASE WHEN OLD.partner_unid IS DISTINCT FROM NEW.partner_unid THEN true ELSE false END,
        CASE WHEN OLD.partner_unid IS DISTINCT FROM NEW.partner_unid THEN NEW.partner_unid ELSE NULL END
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "partner@insert"
AFTER INSERT ON app.partner
FOR EACH ROW
EXECUTE PROCEDURE app."partner@insert|update"();

CREATE TRIGGER "partner@update"
AFTER UPDATE ON app.partner
FOR EACH ROW
WHEN (OLD.* IS DISTINCT FROM NEW.*)
EXECUTE PROCEDURE app."partner@insert|update"();

CREATE OR REPLACE FUNCTION app."partner@delete"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δpartner (
        partner_uuid,
        nameΔ, name,
        emailΔ, email,
        addressΔ, address,
        noteΔ, note,
        activeΔ, active,
        partner_unidΔ, partner_unid
    ) VALUES (
        OLD.partner_uuid,
        true, NULL,
        true, NULL,
        true, NULL,
        true, NULL,
        true, NULL,
        true, NULL
    );
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "partner@delete"
AFTER DELETE ON app.partner
FOR EACH ROW
EXECUTE PROCEDURE app."partner@delete"();

CREATE TABLE app.Δpartner_contact (
    partner_uuid uuid NOT NULL,
    Δuuid uuid NOT NULL DEFAULT public.uuidv7(),
    Δtimestamp timestamp(6) with time zone NOT NULL DEFAULT NOW(),
    contact_uuidΔ boolean NOT NULL DEFAULT false,
    contact_uuid uuid,
    contact_xuidΔ boolean NOT NULL DEFAULT false,
    contact_xuid uuid,
    noteΔ boolean NOT NULL DEFAULT false,
    note text,
    CONSTRAINT Δpartner_contact_pkey PRIMARY KEY (partner_uuid, Δuuid)
);

-- Insert existing rows into app.Δpartner_contact
INSERT INTO app.Δpartner_contact (
    partner_uuid, Δtimestamp, contact_uuidΔ, contact_uuid, contact_xuidΔ, contact_xuid, noteΔ, note
) SELECT 
    partner_uuid, NOW(), false, contact_uuid, false, contact_xuid::uuid, false, note 
FROM app.partner_contact;

CREATE OR REPLACE FUNCTION app."partner_contact@insert|update"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δpartner_contact (
        partner_uuid,
        contact_uuidΔ,
        contact_uuid,
        contact_xuidΔ,
        contact_xuid,
        noteΔ,
        note
    ) VALUES (
        NEW.partner_uuid::uuid,
        CASE WHEN OLD.contact_uuid IS DISTINCT FROM NEW.contact_uuid THEN true ELSE false END,
        CASE WHEN OLD.contact_uuid IS DISTINCT FROM NEW.contact_uuid THEN NEW.contact_uuid ELSE NULL END,
        CASE WHEN OLD.contact_xuid IS DISTINCT FROM NEW.contact_xuid THEN true ELSE false END,
        CASE WHEN OLD.contact_xuid IS DISTINCT FROM NEW.contact_xuid THEN NEW.contact_xuid::uuid ELSE NULL END,
        CASE WHEN OLD.note IS DISTINCT FROM NEW.note THEN true ELSE false END,
        CASE WHEN OLD.note IS DISTINCT FROM NEW.note THEN NEW.note ELSE NULL END
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "partner_contact@insert"
AFTER INSERT ON app.partner_contact
FOR EACH ROW
EXECUTE PROCEDURE app."partner_contact@insert|update"();

CREATE TRIGGER "partner_contact@update"
AFTER UPDATE ON app.partner_contact
FOR EACH ROW
WHEN (OLD.* IS DISTINCT FROM NEW.*)
EXECUTE PROCEDURE app."partner_contact@insert|update"();

CREATE OR REPLACE FUNCTION app."partner_contact@delete"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δpartner_contact (
        partner_uuid,
        contact_uuidΔ,
        contact_uuid,
        contact_xuidΔ,
        contact_xuid,
        noteΔ,
        note
    ) VALUES (
        OLD.partner_uuid,
        true, NULL,
        true, NULL,
        true, NULL
    );
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "partner_contact@delete"
AFTER DELETE ON app.partner_contact
FOR EACH ROW
EXECUTE PROCEDURE app."partner_contact@delete"();

CREATE TABLE app.Δpartner_contact_xuid (
    contact_xuid uuid NOT NULL,
    Δuuid uuid NOT NULL DEFAULT public.uuidv7(),
    Δtimestamp timestamp(6) with time zone NOT NULL DEFAULT NOW(),
    CONSTRAINT Δpartner_contact_xuid_pkey PRIMARY KEY (contact_xuid, Δuuid)
);

-- Insert existing rows into app.Δpartner_contact_xuid
INSERT INTO app.Δpartner_contact_xuid (
    contact_xuid, Δtimestamp
) SELECT 
    contact_xuid::uuid, NOW()
FROM app.partner_contact_xuid;

CREATE OR REPLACE FUNCTION app."partner_contact_xuid@insert|update"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δpartner_contact_xuid (
        contact_xuid
    ) VALUES (
        NEW.contact_xuid::uuid
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "partner_contact_xuid@insert"
AFTER INSERT ON app.partner_contact_xuid
FOR EACH ROW
EXECUTE PROCEDURE app."partner_contact_xuid@insert|update"();

CREATE TRIGGER "partner_contact_xuid@update"
AFTER UPDATE ON app.partner_contact_xuid
FOR EACH ROW
WHEN (OLD.* IS DISTINCT FROM NEW.*)
EXECUTE PROCEDURE app."partner_contact_xuid@insert|update"();

CREATE OR REPLACE FUNCTION app."partner_contact_xuid@delete"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO app.Δpartner_contact_xuid (
        contact_xuid,
        contact_xsidΔ, contact_xsid
    ) VALUES (
        OLD.contact_xuid::uuid,
        true, NULL
    );
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "partner_contact_xuid@delete"
AFTER DELETE ON app.partner_contact_xuid
FOR EACH ROW
EXECUTE PROCEDURE app."partner_contact_xuid@delete"();




/* must remote fkeys before dropping columns or changing types */
ALTER TABLE app.partner_contact DROP CONSTRAINT "partner_contact>>contact_xuid";
ALTER TABLE app.datacenter_contact DROP CONSTRAINT "datacenter_contact>>contact_xuid";

ALTER TABLE app.partner_contact_xuid DROP CONSTRAINT "partner_contact_xuid>>contact_xuid";
ALTER TABLE app.datacenter_contact_xuid DROP CONSTRAINT "datacenter_contact_xuid>>contact_xuid";

CREATE TABLE app.contact_xuid_new (
    contact_xuid uuid NOT NULL PRIMARY KEY DEFAULT public.uuidv7(),
    contact_xsid varchar(32),
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    label varchar(128) NOT NULL,
    foreground_color VARCHAR(7) DEFAULT '#000000' NOT NULL CHECK (foreground_color ~ '^#[0-9a-f]{6}$'),
    background_color VARCHAR(7) DEFAULT '#ffffff' NOT NULL CHECK (background_color ~ '^#[0-9a-f]{6}$'),
    contact_xuid_old varchar(32) NOT NULL,
    CONSTRAINT contact_xsid_uniq UNIQUE (contact_xsid)
);

INSERT INTO app.contact_xuid_new (contact_xsid, create_ts, label, contact_xuid_old)
SELECT contact_xsid, create_ts, label, contact_xuid
FROM app.contact_xuid;

/* change the length on  the contact_xuid fields to 40 to fit the uuid */
ALTER TABLE app.partner_contact
    ALTER COLUMN contact_xuid TYPE varchar(40);

ALTER TABLE app.partner_contact_xuid
    ALTER COLUMN contact_xuid TYPE varchar(40);

ALTER TABLE app.datacenter_contact
    ALTER COLUMN contact_xuid TYPE varchar(40);

ALTER TABLE app.datacenter_contact_xuid
    ALTER COLUMN contact_xuid TYPE varchar(40);

UPDATE app.partner_contact
SET contact_xuid = contact_xuid_new.contact_xuid
FROM app.contact_xuid_new
WHERE partner_contact.contact_xuid = contact_xuid_new.contact_xuid_old;

UPDATE app.datacenter_contact
SET contact_xuid = contact_xuid_new.contact_xuid
FROM app.contact_xuid_new
WHERE datacenter_contact.contact_xuid = contact_xuid_new.contact_xuid_old;

UPDATE app.partner_contact_xuid
SET contact_xuid = contact_xuid_new.contact_xuid
FROM app.contact_xuid_new
WHERE partner_contact_xuid.contact_xuid = contact_xuid_new.contact_xuid_old;

UPDATE app.datacenter_contact_xuid
SET contact_xuid = contact_xuid_new.contact_xuid
FROM app.contact_xuid_new
WHERE datacenter_contact_xuid.contact_xuid = contact_xuid_new.contact_xuid_old;

DROP TABLE app.contact_xuid;
ALTER TABLE app.contact_xuid_new RENAME TO contact_xuid;
ALTER TABLE app.contact_xuid DROP COLUMN contact_xuid_old;


/* change types */
ALTER TABLE app.partner_contact
    ALTER COLUMN contact_xuid TYPE uuid USING contact_xuid::uuid;

ALTER TABLE app.partner_contact_xuid
    ALTER COLUMN contact_xuid TYPE uuid USING contact_xuid::uuid;

ALTER TABLE app.datacenter_contact
    ALTER COLUMN contact_xuid TYPE uuid USING contact_xuid::uuid;

ALTER TABLE app.datacenter_contact_xuid
    ALTER COLUMN contact_xuid TYPE uuid USING contact_xuid::uuid;

/* add fkeys back */
ALTER TABLE app.partner_contact
    ADD CONSTRAINT "partner_contact>>contact_xuid" FOREIGN KEY (contact_xuid) REFERENCES app.partner_contact_xuid (contact_xuid) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE app.datacenter_contact
    ADD CONSTRAINT "datacenter_contact>>contact_xuid" FOREIGN KEY (contact_xuid) REFERENCES app.datacenter_contact_xuid (contact_xuid) ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE app.partner_contact_xuid
    ADD CONSTRAINT "partner_contact_xuid>>contact_xuid" FOREIGN KEY (contact_xuid) REFERENCES app.contact_xuid (contact_xuid) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE app.datacenter_contact_xuid
    ADD CONSTRAINT "datacenter_contact_xuid>>contact_xuid" FOREIGN KEY (contact_xuid) REFERENCES app.contact_xuid (contact_xuid) ON DELETE RESTRICT ON UPDATE CASCADE;

/* cleanup */
ALTER TABLE app.datacenter_contact_xuid
    DROP COLUMN contact_xsid,
    DROP COLUMN label;

ALTER TABLE app.partner_contact_xuid
    DROP COLUMN contact_xsid,
    DROP COLUMN label;



CREATE SCHEMA "repo_cache";

CREATE TYPE repo_cache.repo_type AS ENUM (
    'HuggingFace',
    'GitHub'
);

CREATE TYPE repo_cache.repo_status AS ENUM (
    'New',
    'Download',
    'Ready',
    'Error'
);

CREATE TYPE repo_cache.repo_file_status AS ENUM (
    'New',
    'Download',
    'Ready',
    'Error'
);

CREATE TYPE repo_cache.repo_owner_status AS ENUM (
    'New',
    'Download',
    'Ready',
    'Error'
);


CREATE TABLE repo_cache.blob (
    blob_githash varchar(40) NOT NULL,
    create_ts timestamptz NOT NULL DEFAULT now(),
    size bigint NOT NULL,
    stored_download_url text NULL,
    is_stored bool NOT NULL GENERATED ALWAYS AS (stored_download_url IS NOT NULL) STORED, 
    CONSTRAINT "blob_pkey" PRIMARY KEY (blob_githash)
);

CREATE TYPE repo_cache.blob_jobqueue_status AS ENUM (
    'Queue',
    'Run',
    'Error',
    'Complete'
);

CREATE TABLE repo_cache.blob_jobqueue (
    blob_githash varchar(40) NOT NULL,    
    create_ts timestamptz NOT NULL DEFAULT now(),
    status repo_cache.blob_jobqueue_status NOT NULL DEFAULT 'Queue',
    status_queue_ts timestamptz NOT NULL DEFAULT now(),
    status_run_ts timestamptz NULL,
    status_error_ts timestamptz NULL,
    status_error_text text NULL,
    status_complete_ts timestamptz NULL,
    status_complete_text text NULL,
    attempt_count smallint NOT NULL DEFAULT 0,
    initial_download_url text NOT NULL,
    authorization_bearer varchar(256),
    CONSTRAINT "blob_jobqueue_pkey" PRIMARY KEY (blob_githash)
);

CREATE INDEX "blob_jobqueue.pick_index" ON repo_cache.blob_jobqueue (status_queue_ts) WHERE status = 'Queue';


CREATE TABLE repo_cache.repo (
    repo_githash varchar(40) NOT NULL,
    create_ts timestamptz NOT NULL DEFAULT now(),
    status repo_cache.repo_status NOT NULL,
    CONSTRAINT "repo_pkey" PRIMARY KEY (repo_githash)
);

CREATE TABLE repo_cache.repo_file (
    repo_githash varchar(40) NOT NULL,
    path text NOT NULL,
    blob_githash varchar(40) NOT NULL,
    mode varchar(10) NOT NULL,
    initial_download_url text NOT NULL,
    CONSTRAINT "repo_file_pkey" PRIMARY KEY (repo_githash, path),
    CONSTRAINT "repo_file>>repo" FOREIGN KEY (repo_githash) REFERENCES repo_cache.repo(repo_githash),
    CONSTRAINT "repo_file>>blob" FOREIGN KEY (blob_githash) REFERENCES repo_cache.blob(blob_githash)
);

CREATE VIEW repo_cache.repo_file_view AS 
    SELECT
        repo_file.repo_githash,
        repo_file.path,
        repo_file.blob_githash,
        repo_file.mode,
        blob.size,
        CASE 
            WHEN blob.is_stored THEN 'Ready'
            WHEN blob_jobqueue.status = 'Queue' THEN 'JobQueued'
            WHEN blob_jobqueue.status = 'Run' THEN 'JobRunning'
            WHEN blob_jobqueue.status IS NULL THEN 'JobMissing'
            ELSE 'Error'
        END as status,
        repo_file.initial_download_url
    FROM
        repo_cache.repo_file
        INNER JOIN repo_cache.blob ON repo_file.blob_githash = blob.blob_githash
        LEFT JOIN repo_cache.blob_jobqueue ON repo_file.blob_githash = blob_jobqueue.blob_githash
;



CREATE TABLE repo_cache.repo_owner (
    repo_owner_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    
    repo_type repo_cache.repo_type NOT NULL,
    repo_owner varchar(64) NOT NULL,
    repo_name varchar(100) NOT NULL,
    repo_ref varchar(100) NOT NULL,
    repo_githash varchar(40) NOT NULL,
    
    account_uuid uuid, -- future linking to owner
    Δidentity uuid, Δremoteip inet, Δtimestamp timestamptz, Δnote text, Δdata jsonb,
    
    create_ts timestamptz NOT NULL DEFAULT now(),

    authorization_bearer_sha256 varchar(64),
    note text,
    status repo_cache.repo_owner_status NOT NULL,
    CONSTRAINT "repo_owner_pkey" PRIMARY KEY (repo_owner_uuid),
    CONSTRAINT "repo_owner>>repo" FOREIGN KEY (repo_githash) REFERENCES repo_cache.repo(repo_githash)
);


CREATE SCHEMA "app_datacenter";

CREATE TABLE app_datacenter.machine (
    machine_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    machine_esid varchar(64) NULL,
    datacenter_uuid uuid NOT NULL,
    Δidentity uuid, Δremoteip inet, Δtimestamp timestamptz, Δnote text, Δdata jsonb,
    hostname varchar(128) NOT NULL,
    pub_ip inet NULL,
    lan_ip inet,
    neb_ip inet NULL,
    -- TODO: secret
    CONSTRAINT "machine_pkey" PRIMARY KEY (machine_uuid),
    CONSTRAINT "machine>>datacenter" FOREIGN KEY (datacenter_uuid) REFERENCES app.datacenter(datacenter_uuid),
    CONSTRAINT "machine.machine_esid.uniq" UNIQUE (machine_esid)
);

CREATE TYPE app_datacenter.machine_repo_status AS ENUM (
    'New',
    'Download',
    'Ready',
    'Error'
);

CREATE TABLE repo_cache.machine_repo (
    machine_uuid uuid NOT NULL,
    repo_githash varchar(40) NOT NULL,
    Δidentity uuid, Δremoteip inet, Δtimestamp timestamptz, Δnote text, Δdata jsonb,
    status app_datacenter.machine_repo_status NOT NULL DEFAULT 'New',
    CONSTRAINT "machine_repo_pkey" PRIMARY KEY (machine_uuid, repo_githash),
    CONSTRAINT "machine_repo>>machine" FOREIGN KEY (machine_uuid) REFERENCES app_datacenter.machine(machine_uuid),
    CONSTRAINT "machine_repo>>repo" FOREIGN KEY (repo_githash) REFERENCES repo_cache.repo(repo_githash)
);



CREATE TABLE app_datacenter.package (
    package_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    name varchar(128) NOT NULL,
    description text,
    CONSTRAINT "package_pkey" PRIMARY KEY (package_uuid)
);

CREATE TABLE app_datacenter.package_version (
    package_uuid uuid NOT NULL,
    version_lsid varchar(64) NOT NULL,
    download_url text NOT NULL,
    sha256 varchar(64) NOT NULL,
    gpgsig text,
    CONSTRAINT "package_version_pkey" PRIMARY KEY (package_uuid, version_lsid),
    CONSTRAINT "package_version>>package" FOREIGN KEY (package_uuid) REFERENCES app_datacenter.package(package_uuid)
);

CREATE TYPE app_datacenter.machine_package_status AS ENUM (
    'InstallPending',
    'InstallDownload',
    'InstallProcess',
    'InstallComplete',
    'InstallError',
    'UpgradePending',
    'UpgradeDownload',
    'UpgradeProcess',
    'UpgradeComplete',
    'UpgradeError',
    'RemovePending',
    'RemoveProcess',
    'RemoveComplete',
    'RemoveError'
);

CREATE TABLE app_datacenter.machine_package (
    machine_uuid uuid NOT NULL,
    package_uuid uuid NOT NULL,
    Δidentity uuid,
    Δtimestamp timestamptz,
    Δnote text,
    Δdata jsonb,
    Δaddr inet,
    version_lsid_current varchar(64),
    version_lsid_pending varchar(64),
    reboot_ts timestamptz,
    package_status app_datacenter.machine_package_status NOT NULL,
    CONSTRAINT "machine_package_pkey" PRIMARY KEY (machine_uuid, package_uuid)
);

CREATE TYPE app_datacenter.deployment_status AS ENUM (
    'Execute',
    'Complete',
    'Cancel'
);

CREATE TABLE app_datacenter.deployment (
    deployment_uuid uuid NOT NULL DEFAULT public.uuidv7(),
    create_ts timestamptz NOT NULL DEFAULT now(),
    note text,
    status app_datacenter.deployment_status NOT NULL,
    CONSTRAINT "deployment_pkey" PRIMARY KEY (deployment_uuid)
);

CREATE TABLE app_datacenter.deployment_machine_package (
    deployment_uuid uuid NOT NULL,
    machine_uuid uuid NOT NULL,
    package_uuid uuid NOT NULL,
    version_lsid_current varchar(64),
    version_lsid_pending varchar(64),
    package_change_type varchar GENERATED ALWAYS AS 
    (
        CASE 
            WHEN version_lsid_current IS NULL AND version_lsid_pending IS NOT NULL THEN 'Install'
            WHEN version_lsid_current IS NOT NULL AND version_lsid_pending IS NULL THEN 'Remove'
            WHEN version_lsid_current != version_lsid_pending THEN 'Change'
            ELSE 'NoChange' 
        END
    ) STORED,
    schedule_ts timestamptz,
    process_ts timestamptz,
    CONSTRAINT "deployment_machine_package_pkey" PRIMARY KEY (deployment_uuid, machine_uuid, package_uuid)
);

CREATE TABLE app_datacenter.Δpackage (
    package_uuid uuid,
    Δuuid uuid NOT NULL DEFAULT public.uuidv7(),
    Δtimestamp timestamptz,
    nameΔ boolean NOT NULL DEFAULT false,
    name varchar(128),
    descriptionΔ boolean NOT NULL DEFAULT false,
    description text,
    CONSTRAINT "Δpackage_pkey" PRIMARY KEY (package_uuid, Δuuid)
);

CREATE OR REPLACE FUNCTION app_datacenter."package@insert|update"() 
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO
        app_datacenter.Δpackage (
            package_uuid,
            Δtimestamp,
            nameΔ, name,
            descriptionΔ, description
        )
    VALUES (
        NEW.package_uuid,
        NOW(),
        CASE WHEN OLD.name IS DISTINCT FROM NEW.name THEN true ELSE false END,
        CASE WHEN OLD.name IS DISTINCT FROM NEW.name THEN NEW.name ELSE NULL END,
        CASE WHEN OLD.description IS DISTINCT FROM NEW.description THEN true ELSE false END,
        CASE WHEN OLD.description IS DISTINCT FROM NEW.description THEN NEW.description ELSE NULL END
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "package@insert"
AFTER INSERT ON app_datacenter.package
FOR EACH ROW
EXECUTE FUNCTION app_datacenter."package@insert|update"();

CREATE TRIGGER "package@update"
AFTER UPDATE ON app_datacenter.package
FOR EACH ROW
WHEN (OLD.* IS DISTINCT FROM NEW.*)
EXECUTE FUNCTION app_datacenter."package@insert|update"();

CREATE OR REPLACE FUNCTION app_datacenter."package@delete"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO
        app_datacenter.Δpackage (
            package_uuid,
            Δtimestamp,
            nameΔ, name,
            descriptionΔ, description
        )
    VALUES (
        OLD.package_uuid,
        NOW(),
        true, NULL,
        true, NULL
    );
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "package@delete"
BEFORE DELETE ON app_datacenter.package
FOR EACH ROW
EXECUTE FUNCTION app_datacenter."package@delete"();


CREATE TABLE app_datacenter.Δpackage_version (
    package_uuid uuid NOT NULL,
    version_lsid varchar(64) NOT NULL,
    Δuuid uuid NOT NULL DEFAULT public.uuidv7(),
    Δtimestamp timestamptz,
    download_urlΔ boolean NOT NULL DEFAULT false,
    download_url text,
    sha256Δ boolean NOT NULL DEFAULT false,
    sha256 varchar(64),
    gpgsigΔ boolean NOT NULL DEFAULT false,
    gpgsig text,
    CONSTRAINT "Δpackage_version_pkey" PRIMARY KEY (package_uuid, version_lsid, Δuuid)
);

CREATE OR REPLACE FUNCTION app_datacenter."package_version@insert|update"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO
        app_datacenter.Δpackage_version (
            package_uuid,
            version_lsid,
            Δtimestamp,
            download_urlΔ,
            download_url,
            sha256Δ,
            sha256,
            gpgsigΔ,
            gpgsig
        )
    VALUES (
        NEW.package_uuid,
        NEW.version_lsid,
        NOW(),
        CASE WHEN OLD.download_url IS DISTINCT FROM NEW.download_url THEN true ELSE false END,
        CASE WHEN OLD.download_url IS DISTINCT FROM NEW.download_url THEN NEW.download_url ELSE NULL END,
        CASE WHEN OLD.sha256 IS DISTINCT FROM NEW.sha256 THEN true ELSE false END,
        CASE WHEN OLD.sha256 IS DISTINCT FROM NEW.sha256 THEN NEW.sha256 ELSE NULL END,
        CASE WHEN OLD.gpgsig IS DISTINCT FROM NEW.gpgsig THEN true ELSE false END,
        CASE WHEN OLD.gpgsig IS DISTINCT FROM NEW.gpgsig THEN NEW.gpgsig ELSE NULL END
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "package_version@insert"
AFTER INSERT ON app_datacenter.package_version
FOR EACH ROW
EXECUTE FUNCTION app_datacenter."package_version@insert|update"();

CREATE TRIGGER "package_version@update"
AFTER UPDATE ON app_datacenter.package_version
FOR EACH ROW
WHEN (OLD.* IS DISTINCT FROM NEW.*)
EXECUTE FUNCTION app_datacenter."package_version@insert|update"();

CREATE OR REPLACE FUNCTION app_datacenter."package_version@delete"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO
        app_datacenter.Δpackage_version (
            package_uuid,
            version_lsid,
            Δtimestamp,
            download_urlΔ, download_url,
            sha256Δ, sha256,
            gpgsigΔ, gpgsig
        )
    VALUES (
        OLD.package_uuid,
        OLD.version_lsid,
        NOW(),
        true, NULL,
        true, NULL,
        true, NULL
    );
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "package_version@delete"
BEFORE DELETE ON app_datacenter.package_version
FOR EACH ROW
EXECUTE FUNCTION app_datacenter."package_version@delete"();

CREATE TABLE app_datacenter.Δmachine_package (
    machine_uuid uuid NOT NULL,
    package_uuid uuid NOT NULL,
    Δuuid uuid NOT NULL DEFAULT public.uuidv7(),
    Δidentity uuid,
    Δtimestamp timestamptz,
    Δnote text,
    Δdata jsonb,
    Δaddr inet,
    version_lsid_currentΔ boolean NOT NULL DEFAULT false,
    version_lsid_current varchar(64),
    version_lsid_pendingΔ boolean NOT NULL DEFAULT false,
    version_lsid_pending varchar(64),
    reboot_tsΔ boolean NOT NULL DEFAULT false,
    reboot_ts timestamptz,
    package_statusΔ boolean NOT NULL DEFAULT false,
    package_status app_datacenter.machine_package_status,
    CONSTRAINT "Δmachine_package_pkey" PRIMARY KEY (machine_uuid, package_uuid, Δuuid)
);

CREATE OR REPLACE FUNCTION app_datacenter."machine_package@insert|update"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO
        app_datacenter.Δmachine_package (
            machine_uuid,
            package_uuid,
            Δidentity,
            Δtimestamp,
            Δnote,
            Δdata,
            Δaddr,
            version_lsid_currentΔ,
            version_lsid_current,
            version_lsid_pendingΔ,
            version_lsid_pending,
            reboot_tsΔ,
            reboot_ts,
            package_statusΔ,
            package_status
        )
    VALUES (
        NEW.machine_uuid,
        NEW.package_uuid,
        NEW.Δidentity,
        NEW.Δtimestamp,
        NEW.Δnote,
        NEW.Δdata,
        NEW.Δaddr,
        CASE WHEN OLD.version_lsid_current IS DISTINCT FROM NEW.version_lsid_current THEN true ELSE false END,
        CASE WHEN OLD.version_lsid_current IS DISTINCT FROM NEW.version_lsid_current THEN NEW.version_lsid_current ELSE NULL END,
        CASE WHEN OLD.version_lsid_pending IS DISTINCT FROM NEW.version_lsid_pending THEN true ELSE false END,
        CASE WHEN OLD.version_lsid_pending IS DISTINCT FROM NEW.version_lsid_pending THEN NEW.version_lsid_pending ELSE NULL END,
        CASE WHEN OLD.reboot_ts IS DISTINCT FROM NEW.reboot_ts THEN true ELSE false END,
        CASE WHEN OLD.reboot_ts IS DISTINCT FROM NEW.reboot_ts THEN NEW.reboot_ts ELSE NULL END,
        CASE WHEN OLD.package_status IS DISTINCT FROM NEW.package_status THEN true ELSE false END,
        CASE WHEN OLD.package_status IS DISTINCT FROM NEW.package_status THEN NEW.package_status ELSE NULL END
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "machine_package@insert"
AFTER INSERT ON app_datacenter.machine_package
FOR EACH ROW
EXECUTE FUNCTION app_datacenter."machine_package@insert|update"();

CREATE TRIGGER "machine_package@update"
AFTER UPDATE ON app_datacenter.machine_package
FOR EACH ROW
WHEN (OLD.* IS DISTINCT FROM NEW.*)
EXECUTE FUNCTION app_datacenter."machine_package@insert|update"();

CREATE OR REPLACE FUNCTION app_datacenter."machine_package@delete"()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO
        app_datacenter.Δmachine_package (
            machine_uuid,
            package_uuid,
            Δidentity,
            Δtimestamp,
            Δnote,
            Δdata,
            Δaddr,
            version_lsid_currentΔ,
            version_lsid_current,
            version_lsid_pendingΔ,
            version_lsid_pending,
            reboot_tsΔ,
            reboot_ts,
            package_statusΔ,
            package_status
        )
    VALUES (
        OLD.machine_uuid,
        OLD.package_uuid,
        OLD.Δidentity,
        OLD.Δtimestamp,
        OLD.Δnote,
        OLD.Δdata,
        OLD.Δaddr,
        true, NULL,
        true, NULL,
        true, NULL,
        true, NULL
    );
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "machine_package@delete"
BEFORE DELETE ON app_datacenter.machine_package
FOR EACH ROW
EXECUTE FUNCTION app_datacenter."machine_package@delete"();




-- https://docs.stripe.com/api
CREATE SCHEMA IF NOT EXISTS api_stripe;

CREATE TABLE api_stripe.customer (
    api_stripe_customer_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    customer_id varchar(64),
    name varchar(128),
    email varchar(256),
    phone varchar(64),
    description varchar(256),
    address jsonb,
    metadata jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT customer_pkey PRIMARY KEY (api_stripe_customer_uuid)
);

CREATE TABLE api_stripe.product (
    api_stripe_product_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    product_id varchar(64) NOT NULL,
    name varchar(128) NOT NULL,
    description varchar(256),
    active boolean NOT NULL DEFAULT true,
    metadata jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT product_pkey PRIMARY KEY (api_stripe_product_uuid),
    CONSTRAINT product_id_unique UNIQUE (product_id)
);

CREATE TABLE api_stripe.price (
    api_stripe_price_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    price_id varchar(64) NOT NULL,
    api_stripe_product_uuid uuid NOT NULL,
    name varchar(128) NOT NULL,
    description varchar(256),
    active boolean NOT NULL DEFAULT true,
    unit_amount varchar(64) NOT NULL, -- in cents
    metadata jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT price_pkey PRIMARY KEY (api_stripe_price_uuid),
    CONSTRAINT price_id_unique UNIQUE (price_id),
    CONSTRAINT price_product_fkey FOREIGN KEY (api_stripe_product_uuid) REFERENCES api_stripe.product (api_stripe_product_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

CREATE TABLE api_stripe.subscription (
    api_stripe_subscription_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    subscription_id varchar(64) NOT NULL,
    api_stripe_customer_uuid uuid NOT NULL,
    customer_id varchar(64) NOT NULL,
    api_stripe_product_uuid uuid NOT NULL,
    description varchar(256),
    trial_period_days integer NOT NULL DEFAULT 0,
    metadata jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT subscription_pkey PRIMARY KEY (api_stripe_subscription_uuid),
    CONSTRAINT subscription_id_unique UNIQUE (subscription_id),
    CONSTRAINT subscription_customer_fkey FOREIGN KEY (api_stripe_customer_uuid) REFERENCES api_stripe.customer (api_stripe_customer_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT subscription_product_fkey FOREIGN KEY (api_stripe_product_uuid) REFERENCES api_stripe.product (api_stripe_product_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

CREATE TABLE api_stripe.invoice_item (
    api_stripe_invoice_item_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    invoice_item_id varchar(64) NOT NULL,
    api_stripe_customer_uuid uuid NOT NULL,
    api_stripe_price_uuid uuid NOT NULL,
    api_stripe_product_uuid uuid NOT NULL,
    api_stripe_subscription_uuid uuid,
    amount bigint NOT NULL, -- in cents
    description varchar(256),
    metadata jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT invoice_item_pkey PRIMARY KEY (api_stripe_invoice_item_uuid),
    CONSTRAINT invoice_item_id_unique UNIQUE (invoice_item_id),
    CONSTRAINT invoice_item_customer_fkey FOREIGN KEY (api_stripe_customer_uuid) REFERENCES api_stripe.customer (api_stripe_customer_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT invoice_item_price_fkey FOREIGN KEY (api_stripe_price_uuid) REFERENCES api_stripe.price (api_stripe_price_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT invoice_item_product_fkey FOREIGN KEY (api_stripe_product_uuid) REFERENCES api_stripe.product (api_stripe_product_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT invoice_item_subscription_fkey FOREIGN KEY (api_stripe_subscription_uuid) REFERENCES api_stripe.subscription (api_stripe_subscription_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);

----------------------------------
-- runpod database initial data --
----------------------------------

-- Insert statements for contact_xuid
INSERT INTO app.contact_xuid (
    create_ts, label
) VALUES
    (now(), 'Billing Contact'),
    (now(), 'Security Incident Contact'),
    (now(), 'Network Operations Contact'),
    (now(), 'Customer Support Contact'),
    (now(), 'Facility Management Contact');

-- Insert statements for partner_contact_xuid
INSERT INTO app.partner_contact_xuid (
    contact_xuid, create_ts
) VALUES
    ((SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Billing Contact'), now()),
    ((SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Customer Support Contact'), now()),
    ((SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Security Incident Contact'), now());

-- Insert statements for datacenter_contact_xuid
INSERT INTO app.datacenter_contact_xuid (
    contact_xuid, create_ts
) VALUES
    ((SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Network Operations Contact'), now()),
    ((SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Facility Management Contact'), now()),
    ((SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Security Incident Contact'), now());

-- Insert statements for partners
INSERT INTO app.partner (
    create_ts, name, email, address, note
) VALUES
    (now(), 'Equinix', '<EMAIL>', '8 Buckingham Avenue, Slough, SL1 4AX, United Kingdom', 'Global data center provider.'),
    (now(), 'CyrusOne', '<EMAIL>', 'Lyoner Strasse 28, 60528 Frankfurt am Main, Germany', 'Colocation services across the globe.'),
    (now(), 'Digital Realty', '<EMAIL>', '3 Loyang Way, Singapore 508719', NULL),
    (now(), 'Vantage Data Centers', '<EMAIL>', 'Wentloog Corporate Park, Cardiff, CF3 2ER, United Kingdom', 'Data centers with a focus on energy efficiency.'),
    (now(), 'Iron Mountain', '<EMAIL>', '29A International Business Park, Singapore 609934', 'Data centers powered by 100% renewable energy.');

-- Insert statements for data centers
INSERT INTO app.datacenter (
    datacenter_uuid, partner_uuid, name, description, create_ts, datacenter_esid, timezone, address, note
) VALUES
    ('10000000-0000-0000-0000-000000000001',(SELECT partner_uuid FROM app.partner WHERE name = 'Equinix'), 'Equinix LD6', 'Equinix data center located in Slough, United Kingdom.', now(), 'EQX-LD6', 'Europe/London', '8 Buckingham Avenue, Slough, SL1 4AX, United Kingdom', NULL),
    ('10000000-0000-0000-0000-000000000002',(SELECT partner_uuid FROM app.partner WHERE name = 'CyrusOne'), 'CyrusOne Frankfurt I', 'CyrusOne data center located in Frankfurt, Germany.', now(), 'CYN-FRA1', 'Europe/Berlin', 'Lyoner Strasse 28, 60528 Frankfurt am Main, Germany', NULL),
    ('10000000-0000-0000-0000-000000000003',(SELECT partner_uuid FROM app.partner WHERE name = 'Digital Realty'), 'Digital Realty SIN10', 'Digital Realty data center located in Singapore.', now(), 'DRT-SIN10', 'Asia/Singapore', '3 Loyang Way, Singapore 508719', NULL),
    ('10000000-0000-0000-0000-000000000004',(SELECT partner_uuid FROM app.partner WHERE name = 'Vantage Data Centers'), 'Vantage CWL1', 'Vantage Data Centers facility located near Cardiff, Wales, UK.', now(), 'VDC-CWL1', 'Europe/London', 'Wentloog Corporate Park, Cardiff, CF3 2ER, United Kingdom', NULL),
    ('10000000-0000-0000-0000-000000000005',(SELECT partner_uuid FROM app.partner WHERE name = 'Iron Mountain'), 'Iron Mountain SIN-1', 'Iron Mountain data center located in Singapore.', now(), 'IMT-SIN1', 'Asia/Singapore', '29A International Business Park, Singapore 609934', NULL);

-- Insert statements for contacts
INSERT INTO app.contact (
    create_ts, firstname, lastname, title, company, email, timezone, phone, note, active
) VALUES
    (now(), 'John', 'Doe', 'Regional Manager', 'Equinix', '<EMAIL>', 'Europe/London', '+44 20 1234 5678', NULL, true),
    (now(), 'Jane', 'Smith', NULL, NULL, '<EMAIL>', 'Europe/London', '+44 20 8765 4321', 'Equinix Technical Lead', true),
    (now(), 'Michael', 'Brown', NULL, NULL, '<EMAIL>', 'Europe/Berlin', '+49 69 1234 5678', 'CyrusOne Data Center Manager', true),
    (now(), 'Emily', 'White', NULL, NULL, '<EMAIL>', 'Europe/Berlin', '+49 69 8765 4321', 'CyrusOne Customer Relations', true),
    (now(), 'Suresh', 'Patel', NULL, NULL, '<EMAIL>', 'Asia/Singapore', '+65 1234 5678', 'Digital Realty Operations Manager', true),
    (now(), 'Wei', 'Zhang', NULL, NULL, '<EMAIL>', 'Asia/Singapore', '+65 8765 4321', 'Digital Realty Technical Specialist', true),
    (now(), 'Gareth', 'Jones', NULL, NULL, '<EMAIL>', 'Europe/London', '+44 29 1234 5678', 'Vantage Data Centers Facility Manager', true),
    (now(), 'Hannah', 'Evans', NULL, NULL, '<EMAIL>', 'Europe/London', '+44 29 8765 4321', 'Vantage Data Centers Customer Support', true),
    (now(), 'Ravi', 'Kumar', NULL, NULL, '<EMAIL>', 'Asia/Singapore', '+65 2345 6789', 'Iron Mountain Security Lead', true),
    (now(), 'Aisha', 'Rahman', NULL, NULL, '<EMAIL>', 'Asia/Singapore', '+65 9876 5432', 'Iron Mountain Sustainability Coordinator', true);

-- Insert statements for partner contacts
INSERT INTO app.partner_contact (
    partner_uuid, contact_uuid, contact_xuid, create_ts, note
) VALUES
    ((SELECT partner_uuid FROM app.partner WHERE name = 'Equinix'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Billing Contact'), now(), 'Billing contact for Equinix'),
    ((SELECT partner_uuid FROM app.partner WHERE name = 'Equinix'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Customer Support Contact'), now(), 'Technical lead for Equinix'),
    ((SELECT partner_uuid FROM app.partner WHERE name = 'CyrusOne'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Billing Contact'), now(), 'Data center manager for CyrusOne'),
    ((SELECT partner_uuid FROM app.partner WHERE name = 'CyrusOne'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Customer Support Contact'), now(), 'Customer relations for CyrusOne'),
    ((SELECT partner_uuid FROM app.partner WHERE name = 'Digital Realty'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Security Incident Contact'), now(), 'Operations manager for Digital Realty'),
    ((SELECT partner_uuid FROM app.partner WHERE name = 'Digital Realty'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Customer Support Contact'), now(), 'Technical specialist for Digital Realty'),
    ((SELECT partner_uuid FROM app.partner WHERE name = 'Vantage Data Centers'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Billing Contact'), now(), 'Facility manager for Vantage Data Centers'),
    ((SELECT partner_uuid FROM app.partner WHERE name = 'Vantage Data Centers'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Customer Support Contact'), now(), 'Customer support for Vantage Data Centers'),
    ((SELECT partner_uuid FROM app.partner WHERE name = 'Iron Mountain'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Security Incident Contact'), now(), 'Security lead for Iron Mountain'),
    ((SELECT partner_uuid FROM app.partner WHERE name = 'Iron Mountain'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Customer Support Contact'), now(), 'Sustainability coordinator for Iron Mountain');

-- Insert statements for data center contacts
INSERT INTO app.datacenter_contact (
    datacenter_uuid, contact_xuid, contact_uuid, create_ts, note
) VALUES
    ((SELECT datacenter_uuid FROM app.datacenter WHERE name = 'Equinix LD6'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Network Operations Contact'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), now(), 'Primary contact for Equinix LD6'),
    ((SELECT datacenter_uuid FROM app.datacenter WHERE name = 'Equinix LD6'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Facility Management Contact'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), now(), 'Technical contact for Equinix LD6'),
    ((SELECT datacenter_uuid FROM app.datacenter WHERE name = 'CyrusOne Frankfurt I'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Network Operations Contact'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), now(), 'Data center manager for CyrusOne Frankfurt I'),
    ((SELECT datacenter_uuid FROM app.datacenter WHERE name = 'CyrusOne Frankfurt I'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Facility Management Contact'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), now(), 'Customer relations for CyrusOne Frankfurt I'),
    ((SELECT datacenter_uuid FROM app.datacenter WHERE name = 'Digital Realty SIN10'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Network Operations Contact'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), now(), 'Operations manager for Digital Realty SIN10'),
    ((SELECT datacenter_uuid FROM app.datacenter WHERE name = 'Digital Realty SIN10'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Security Incident Contact'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), now(), 'Technical specialist for Digital Realty SIN10'),
    ((SELECT datacenter_uuid FROM app.datacenter WHERE name = 'Vantage CWL1'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Facility Management Contact'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), now(), 'Facility manager for Vantage CWL1'),
    ((SELECT datacenter_uuid FROM app.datacenter WHERE name = 'Vantage CWL1'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Security Incident Contact'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), now(), 'Customer support for Vantage CWL1'),
    ((SELECT datacenter_uuid FROM app.datacenter WHERE name = 'Iron Mountain SIN-1'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Network Operations Contact'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), now(), 'Security lead for Iron Mountain SIN-1'),
    ((SELECT datacenter_uuid FROM app.datacenter WHERE name = 'Iron Mountain SIN-1'), (SELECT contact_xuid FROM app.contact_xuid WHERE label = 'Facility Management Contact'), (SELECT contact_uuid FROM app.contact WHERE email = '<EMAIL>'), now(), 'Sustainability coordinator for Iron Mountain SIN-1');



INSERT INTO "app_datacenter"."machine" ("machine_uuid", "machine_esid", "datacenter_uuid", "Δidentity", "Δremoteip", "Δtimestamp", "Δnote", "Δdata", "hostname", "pub_ip", "lan_ip", "neb_ip") VALUES ('11000000-0000-0000-0000-000000000001', NULL, '10000000-0000-0000-0000-000000000001', NULL, NULL, NULL, NULL, NULL, 'test', NULL, '************', NULL);
INSERT INTO "app_datacenter"."machine" ("machine_uuid", "machine_esid", "datacenter_uuid", "Δidentity", "Δremoteip", "Δtimestamp", "Δnote", "Δdata", "hostname", "pub_ip", "lan_ip", "neb_ip") VALUES ('11000000-0000-0000-0000-000000000002', NULL, '10000000-0000-0000-0000-000000000001', NULL, NULL, NULL, NULL, NULL, 'test2', NULL, '************', NULL);


INSERT INTO "repo_cache"."blob" ("blob_githash", "size", "stored_download_url") VALUES ('a6344aac8c09253b3b630fb776ae94478aa0275b', 1519, NULL);
INSERT INTO "repo_cache"."blob" ("blob_githash", "size", "stored_download_url") VALUES ('0bbe30273338d7e19f779c1343808e32617e64d1', 21196, NULL);
INSERT INTO "repo_cache"."blob" ("blob_githash", "size", "stored_download_url") VALUES ('1b33526d33aaa60d79f78ae8651dae50b730185a', 34648, NULL);
INSERT INTO "repo_cache"."blob" ("blob_githash", "size", "stored_download_url") VALUES ('ad2f44ff1ed66e12765b2392dc041469db91a462', 1256, NULL);
INSERT INTO "repo_cache"."blob" ("blob_githash", "size", "stored_download_url") VALUES ('cbe752958dc3e4671b0e0220aa1c545423a6d5f5', 3772, NULL);
INSERT INTO "repo_cache"."blob" ("blob_githash", "size", "stored_download_url") VALUES ('6038932a2a1f09a66991b1c2adae0d14066fa29e', 493869, NULL);
INSERT INTO "repo_cache"."blob" ("blob_githash", "size", "stored_download_url") VALUES ('d31c58c2bcd43b726463567df91f9a2b0581dd8e', 1617824864, NULL);
INSERT INTO "repo_cache"."blob" ("blob_githash", "size", "stored_download_url") VALUES ('dd6ae819ad738ac1a546e9f9282ef325c33b9ea0', 52666, NULL);
INSERT INTO "repo_cache"."blob" ("blob_githash", "size", "stored_download_url") VALUES ('931c77a740890c46365c7ae0c9d350ba3cca908f', 340, NULL);
INSERT INTO "repo_cache"."blob" ("blob_githash", "size", "stored_download_url") VALUES ('312bc106291bb51bf2cc1648df070bef963a0639', 2186, NULL);
INSERT INTO "repo_cache"."blob" ("blob_githash", "size", "stored_download_url") VALUES ('17456db595adc78a973f97d69d8cb50bc87c0b1c', 2710337, NULL);
INSERT INTO "repo_cache"."blob" ("blob_githash", "size", "stored_download_url") VALUES ('06ffdc8308eae6bb7bd1fdd81e94b0a881a539ab', 282843, NULL);
INSERT INTO "repo_cache"."blob" ("blob_githash", "size", "stored_download_url") VALUES ('0f3456460629e21d559c6daa23ab6ce3644e8271', 1036558, NULL);

INSERT INTO "repo_cache"."repo" ("repo_githash", "status") VALUES ('41f01f3fe87f28c78e2fbf8b568835947dd65ed9', 'New');

INSERT INTO "repo_cache"."machine_repo" ("machine_uuid", "repo_githash", "Δidentity", "Δremoteip", "Δtimestamp", "Δnote", "Δdata", "status") VALUES ('11000000-0000-0000-0000-000000000001', '41f01f3fe87f28c78e2fbf8b568835947dd65ed9', NULL, NULL, NULL, NULL, NULL, 'New');
INSERT INTO "repo_cache"."machine_repo" ("machine_uuid", "repo_githash", "Δidentity", "Δremoteip", "Δtimestamp", "Δnote", "Δdata", "status") VALUES ('11000000-0000-0000-0000-000000000002', '41f01f3fe87f28c78e2fbf8b568835947dd65ed9', NULL, NULL, NULL, NULL, NULL, 'New');

INSERT INTO "repo_cache"."repo_file" ("repo_githash", "path", "blob_githash", "mode", "initial_download_url") VALUES ('41f01f3fe87f28c78e2fbf8b568835947dd65ed9', '.gitattributes', 'a6344aac8c09253b3b630fb776ae94478aa0275b', '100644', 'https://huggingface.co/openai/whisper-large-v3-turbo/resolve/main/.gitattributes');
INSERT INTO "repo_cache"."repo_file" ("repo_githash", "path", "blob_githash", "mode", "initial_download_url") VALUES ('41f01f3fe87f28c78e2fbf8b568835947dd65ed9', 'README.md', '0bbe30273338d7e19f779c1343808e32617e64d1', '100644', 'https://huggingface.co/openai/whisper-large-v3-turbo/resolve/main/README.md');
INSERT INTO "repo_cache"."repo_file" ("repo_githash", "path", "blob_githash", "mode", "initial_download_url") VALUES ('41f01f3fe87f28c78e2fbf8b568835947dd65ed9', 'added_tokens.json', '1b33526d33aaa60d79f78ae8651dae50b730185a', '100644', 'https://huggingface.co/openai/whisper-large-v3-turbo/resolve/main/added_tokens.json');
INSERT INTO "repo_cache"."repo_file" ("repo_githash", "path", "blob_githash", "mode", "initial_download_url") VALUES ('41f01f3fe87f28c78e2fbf8b568835947dd65ed9', 'config.json', 'ad2f44ff1ed66e12765b2392dc041469db91a462', '100644', 'https://huggingface.co/openai/whisper-large-v3-turbo/resolve/main/config.json');
INSERT INTO "repo_cache"."repo_file" ("repo_githash", "path", "blob_githash", "mode", "initial_download_url") VALUES ('41f01f3fe87f28c78e2fbf8b568835947dd65ed9', 'generation_config.json', 'cbe752958dc3e4671b0e0220aa1c545423a6d5f5', '100644', 'https://huggingface.co/openai/whisper-large-v3-turbo/resolve/main/generation_config.json');
INSERT INTO "repo_cache"."repo_file" ("repo_githash", "path", "blob_githash", "mode", "initial_download_url") VALUES ('41f01f3fe87f28c78e2fbf8b568835947dd65ed9', 'merges.txt', '6038932a2a1f09a66991b1c2adae0d14066fa29e', '100644', 'https://huggingface.co/openai/whisper-large-v3-turbo/resolve/main/merges.txt');
INSERT INTO "repo_cache"."repo_file" ("repo_githash", "path", "blob_githash", "mode", "initial_download_url") VALUES ('41f01f3fe87f28c78e2fbf8b568835947dd65ed9', 'model.safetensors', 'd31c58c2bcd43b726463567df91f9a2b0581dd8e', '100644', 'https://huggingface.co/openai/whisper-large-v3-turbo/resolve/main/model.safetensors');
INSERT INTO "repo_cache"."repo_file" ("repo_githash", "path", "blob_githash", "mode", "initial_download_url") VALUES ('41f01f3fe87f28c78e2fbf8b568835947dd65ed9', 'normalizer.json', 'dd6ae819ad738ac1a546e9f9282ef325c33b9ea0', '100644', 'https://huggingface.co/openai/whisper-large-v3-turbo/resolve/main/normalizer.json');
INSERT INTO "repo_cache"."repo_file" ("repo_githash", "path", "blob_githash", "mode", "initial_download_url") VALUES ('41f01f3fe87f28c78e2fbf8b568835947dd65ed9', 'preprocessor_config.json', '931c77a740890c46365c7ae0c9d350ba3cca908f', '100644', 'https://huggingface.co/openai/whisper-large-v3-turbo/resolve/main/preprocessor_config.json');
INSERT INTO "repo_cache"."repo_file" ("repo_githash", "path", "blob_githash", "mode", "initial_download_url") VALUES ('41f01f3fe87f28c78e2fbf8b568835947dd65ed9', 'special_tokens_map.json', '312bc106291bb51bf2cc1648df070bef963a0639', '100644', 'https://huggingface.co/openai/whisper-large-v3-turbo/resolve/main/special_tokens_map.json');
INSERT INTO "repo_cache"."repo_file" ("repo_githash", "path", "blob_githash", "mode", "initial_download_url") VALUES ('41f01f3fe87f28c78e2fbf8b568835947dd65ed9', 'tokenizer.json', '17456db595adc78a973f97d69d8cb50bc87c0b1c', '100644', 'https://huggingface.co/openai/whisper-large-v3-turbo/resolve/main/tokenizer.json');
INSERT INTO "repo_cache"."repo_file" ("repo_githash", "path", "blob_githash", "mode", "initial_download_url") VALUES ('41f01f3fe87f28c78e2fbf8b568835947dd65ed9', 'tokenizer_config.json', '06ffdc8308eae6bb7bd1fdd81e94b0a881a539ab', '100644', 'https://huggingface.co/openai/whisper-large-v3-turbo/resolve/main/tokenizer_config.json');
INSERT INTO "repo_cache"."repo_file" ("repo_githash", "path", "blob_githash", "mode", "initial_download_url") VALUES ('41f01f3fe87f28c78e2fbf8b568835947dd65ed9', 'vocab.json', '0f3456460629e21d559c6daa23ab6ce3644e8271', '100644', 'https://huggingface.co/openai/whisper-large-v3-turbo/resolve/main/vocab.json');

INSERT INTO "repo_cache"."blob_jobqueue" ("blob_githash", "initial_download_url") SELECT blob_githash, initial_download_url FROM "repo_cache"."repo_file";

INSERT INTO "repo_cache"."repo_owner" ("repo_owner_uuid", "repo_type", "repo_owner", "repo_name", "repo_ref", "repo_githash", "account_uuid", "Δidentity", "Δremoteip", "Δtimestamp", "Δnote", "Δdata", "authorization_bearer_sha256", "note", "status") VALUES ('0193aec4-cec1-73a1-ac5c-0b75c7a937c0', 'HuggingFace', 'openai', 'whisper-large-v3-turbo', 'main', '41f01f3fe87f28c78e2fbf8b568835947dd65ed9', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'New');

-- Insert statements for app_datacenter.package
INSERT INTO app_datacenter.package (
    package_uuid, name, description
) VALUES
    (public.uuidv7(), 'Apache HTTP Server', 'Open-source HTTP server for modern operating systems including UNIX and Windows.'),
    (public.uuidv7(), 'MySQL Database', 'Open-source relational database management system.'),
    (public.uuidv7(), 'Nginx', 'High-performance HTTP server and reverse proxy.'),
    (public.uuidv7(), 'Docker', 'Platform for developing, shipping, and running applications in containers.'),
    (public.uuidv7(), 'Kubernetes', 'Open-source system for automating deployment, scaling, and management of containerized applications.');

-- Insert statements for app_datacenter.package_version
INSERT INTO app_datacenter.package_version (
    package_uuid, version_lsid, download_url, sha256, gpgsig
) VALUES
    ((SELECT package_uuid FROM app_datacenter.package WHERE name = 'Apache HTTP Server'), '2.4.51', 'http://example.com/apache/2.4.51', 'sha256-value-1', 'gpgsig-value-1'),
    ((SELECT package_uuid FROM app_datacenter.package WHERE name = 'MySQL Database'), '8.0.26', 'http://example.com/mysql/8.0.26', 'sha256-value-2', 'gpgsig-value-2'),
    ((SELECT package_uuid FROM app_datacenter.package WHERE name = 'Nginx'), '1.21.3', 'http://example.com/nginx/1.21.3', 'sha256-value-3', 'gpgsig-value-3'),
    ((SELECT package_uuid FROM app_datacenter.package WHERE name = 'Docker'), '20.10.8', 'http://example.com/docker/20.10.8', 'sha256-value-4', 'gpgsig-value-4'),
    ((SELECT package_uuid FROM app_datacenter.package WHERE name = 'Kubernetes'), '1.22.2', 'http://example.com/kubernetes/1.22.2', 'sha256-value-5', 'gpgsig-value-5');

-- Insert statements for app_datacenter.machine_package
INSERT INTO app_datacenter.machine_package (
    machine_uuid, package_uuid, Δidentity, Δtimestamp, Δnote, Δdata, Δaddr, version_lsid_current, version_lsid_pending, reboot_ts, package_status
) VALUES
    ('11000000-0000-0000-0000-000000000001', (SELECT package_uuid FROM app_datacenter.package WHERE name = 'Apache HTTP Server'), NULL, NOW(), NULL, NULL, NULL, '2.4.51', NULL, NULL, 'InstallComplete'),
    ('11000000-0000-0000-0000-000000000002', (SELECT package_uuid FROM app_datacenter.package WHERE name = 'MySQL Database'), NULL, NOW(), NULL, NULL, NULL, '8.0.26', NULL, NULL, 'InstallComplete');

-- Insert statements for app_datacenter.deployment
INSERT INTO app_datacenter.deployment (
    deployment_uuid, status
) VALUES
    (public.uuidv7(), 'Execute'),
    (public.uuidv7(), 'Complete');

-- Insert statements for app_datacenter.deployment_machine_package
INSERT INTO app_datacenter.deployment_machine_package (
    deployment_uuid, machine_uuid, package_uuid, version_lsid_current, version_lsid_pending, schedule_ts, process_ts
) VALUES
    ((SELECT deployment_uuid FROM app_datacenter.deployment WHERE status = 'Execute'), '11000000-0000-0000-0000-000000000001', (SELECT package_uuid FROM app_datacenter.package WHERE name = 'Apache HTTP Server'), '2.4.51', '2.4.52', NOW(), NULL),
    ((SELECT deployment_uuid FROM app_datacenter.deployment WHERE status = 'Complete'), '11000000-0000-0000-0000-000000000002', (SELECT package_uuid FROM app_datacenter.package WHERE name = 'MySQL Database'), '8.0.26', '8.0.27', NOW(), NOW());

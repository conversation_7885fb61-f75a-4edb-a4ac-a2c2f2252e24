-- AppCove Database Schema
-- This file contains the database schema for the <PERSON>pp<PERSON>ove application

-- Create the appcove schema
CREATE SCHEMA IF NOT EXISTS appcove;

CREATE SCHEMA IF NOT EXISTS appcove_malawi;

CREATE TABLE appcove_malawi.guard (
    guard_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    first_name varchar(128) NOT NULL,
    last_name varchar(128) NOT NULL,
    phone varchar(64),
    note text,
    active boolean DEFAULT true NOT NULL,
    CONSTRAINT guard_pkey PRIMARY KEY (guard_uuid)
);

CREATE TABLE appcove_malawi.guard_schedule (
    guard_schedule_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    guard_uuid uuid NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    start_ts timestamptz NOT NULL,
    end_ts timestamptz NOT NULL,
    note text,
    active boolean DEFAULT true NOT NULL,
    CONSTRAINT guard_schedule_pkey PRIMARY KEY (guard_schedule_uuid),
    CONSTRAINT guard_schedule_guard FOREIGN KEY (guard_uuid) REFERENCES appcove_malawi.guard (guard_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT guard_schedule_start_end CHECK (end_ts > start_ts)
);

CREATE TABLE appcove_malawi.guard_attendance (
    guard_attendance_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    guard_uuid uuid NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    start_ts timestamptz,
    end_ts timestamptz,
    note text,
    active boolean DEFAULT true NOT NULL,
    CONSTRAINT guard_attendance_pkey PRIMARY KEY (guard_attendance_uuid),
    CONSTRAINT guard_attendance_guard FOREIGN KEY (guard_uuid) REFERENCES appcove_malawi.guard (guard_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);
CREATE SCHEMA IF NOT EXISTS df4l;

CREATE TABLE df4l.agency (
    agency_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    agency_esid varchar(20) NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    name varchar(128) NOT NULL,
    active boolean NOT NULL DEFAULT true,
    admin_note text,
    data jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT "agency_pkey" PRIMARY KEY (agency_uuid),
    CONSTRAINT "agency.agency_esid.uniq" UNIQUE (agency_esid)
);

CREATE TABLE df4l.advisor (
    advisor_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    identity_uuid uuid,
    agency_uuid uuid,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    advisor_esid varchar(7) NOT NULL,
    gbu_advisor_esid varchar(64),
    first_name varchar(128) NOT NULL,
    last_name varchar(128) NOT NULL,
    email varchar(256),
    phone varchar(64),
    address1 varchar(128),
    address2 varchar(128),
    city varchar(128),
    state varchar(16),
    zip varchar(20),
    api_stripe_customer_uuid uuid,
    api_stripe_subscription_uuid uuid,
    active boolean NOT NULL DEFAULT true,
    admin_note text,
    data jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT "advisor_pkey" PRIMARY KEY ("advisor_uuid"),
    CONSTRAINT fk_identity FOREIGN KEY (identity_uuid) REFERENCES auth_fence.identity(identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT fk_agency FOREIGN KEY (agency_uuid) REFERENCES df4l.agency(agency_uuid) ON DELETE RESTRICT,
    CONSTRAINT fk_state FOREIGN KEY (state) REFERENCES addr_iso.us_state(state_code) ON DELETE RESTRICT,
    CONSTRAINT fk_customer FOREIGN KEY (api_stripe_customer_uuid) REFERENCES api_stripe.customer(api_stripe_customer_uuid) ON DELETE RESTRICT,
    CONSTRAINT advisor_esid_unique UNIQUE (advisor_esid)
);

CREATE TABLE df4l.client0 (
    client_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    advisor_uuid uuid NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    first_name varchar(64) NOT NULL,
    last_name varchar(64) NOT NULL,
    email varchar(255),
    phone varchar(20),
    phone2 varchar(20),
    address1 varchar(128),
    address2 varchar(128),
    city varchar(32),
    state varchar(16),
    zip varchar(16),
    time_zone varchar(32),
    monthly_budget numeric(10,2),
    file_znid_doc_to_append int4,
    active bool NOT NULL DEFAULT true,
    note varchar(4096),
    annual_insurance_premium numeric(10,2),
    annual_insurance_pua numeric(10,2),
    net_cash_at_end numeric(10,2),
    debt_free_start_date date,
    debt_free_active bool NOT NULL DEFAULT false,
    debt_free_extra_pua_list jsonb NOT NULL DEFAULT '[]'::jsonb,
    data jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT "client0_pkey" PRIMARY KEY ("client_uuid"),
    CONSTRAINT fk_advisor FOREIGN KEY (advisor_uuid) REFERENCES df4l.advisor(advisor_uuid) ON UPDATE RESTRICT ON DELETE RESTRICT
);

CREATE TYPE df4l.client_type AS ENUM (
    'DebtManagement',
    'PolicyOnly'
);

CREATE TABLE df4l.client (
    client_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    advisor_uuid uuid NOT NULL,
    client_type df4l.client_type NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    is_demo boolean NOT NULL DEFAULT false,
    first_name varchar(64) NOT NULL,
    last_name varchar(64) NOT NULL,
    email varchar(255),
    phone varchar(20),
    address1 varchar(128),
    address2 varchar(128),
    city varchar(32),
    state varchar(16),
    zip varchar(16),
    time_zone varchar(32),
    gender varchar(8),
    birth_date date,
    note varchar(4096),
    active boolean NOT NULL DEFAULT true,
    budget_extra_debts numeric(10, 2),
    budget_extra_savings numeric(10, 2),
    budget_extra_retirement numeric(10, 2),
    budget_extra_surplus numeric(10, 2),
    budget_pua_split varchar(16),
    budget_pua_contribution numeric(10, 2),
    policy_total_premium numeric(10, 2),
    api_crs_user_uuid_applicant uuid,
    api_crs_user_enabled_applicant boolean,
    api_crs_user_uuid_spouse uuid,
    api_crs_user_enabled_spouse boolean,
    CONSTRAINT "client_pkey" PRIMARY KEY ("client_uuid"),
    CONSTRAINT fk_advisor FOREIGN KEY (advisor_uuid) REFERENCES df4l.advisor(advisor_uuid) ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT "pua_split_check" CHECK (budget_pua_split IS NULL OR budget_pua_split IN ('None', 'Max')),
    CONSTRAINT "pua_contribution_check" CHECK (budget_pua_contribution IS NULL OR budget_pua_contribution >= 0),
    CONSTRAINT "client>>user(applicant)" FOREIGN KEY (api_crs_user_uuid_applicant) REFERENCES api_crs.user (api_crs_user_uuid) ON UPDATE RESTRICT ON DELETE SET NULL,
    CONSTRAINT "client>>user(spouse)" FOREIGN KEY (api_crs_user_uuid_spouse) REFERENCES api_crs.user (api_crs_user_uuid) ON UPDATE RESTRICT ON DELETE SET NULL
);

CREATE TABLE df4l.client_debt (
    client_debt_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    client_debt_esid varchar(64),
    client_uuid uuid NOT NULL,
    active boolean NOT NULL DEFAULT true,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    update_ts timestamptz(6),
    name varchar(128),
    balance numeric,
    balance_date date,
    annual_interest_percentage numeric,
    monthly_payment_amount numeric,
    note text,
    CONSTRAINT client_debt_pkey PRIMARY KEY (client_debt_uuid),
    CONSTRAINT fk_client FOREIGN KEY (client_uuid) REFERENCES df4l.client(client_uuid) ON DELETE RESTRICT,
    CONSTRAINT check_remote_debt_fields_are_null CHECK (
        (client_debt_esid IS NOT NULL AND name IS NULL AND balance IS NULL AND balance_date IS NULL AND monthly_payment_amount IS NULL AND note IS NULL) OR
        (client_debt_esid IS NULL)
    )
);

CREATE UNIQUE INDEX client_debt_esid_unique ON df4l.client_debt (client_uuid, client_debt_esid);

CREATE TABLE df4l.client_policy (
    client_policy_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    client_uuid uuid NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    update_ts timestamptz(6) NOT NULL DEFAULT now(),
    policy_cash_value numeric(10,2) not null,
    policy_pua_value numeric(10,2) not null,
    policy_loan_balance numeric(10,2) not null,
    policy_active boolean NOT NULL,
    data jsonb null,
    CONSTRAINT client_policy_pkey PRIMARY KEY (client_policy_uuid),
    CONSTRAINT fk_client FOREIGN KEY (client_uuid) REFERENCES df4l.client(client_uuid) ON UPDATE RESTRICT ON DELETE RESTRICT
);

-- Pending client is any client who is active and does not yet have a policy 
CREATE VIEW df4l.client_pending AS 
SELECT client_uuid, advisor_uuid
FROM df4l.client
WHERE active
AND NOT EXISTS (
    SELECT FROM df4l.client_policy
    WHERE client_uuid = df4l.client.client_uuid
);

CREATE VIEW df4l.client_active AS 
SELECT client_uuid, advisor_uuid
FROM df4l.client
WHERE active
AND EXISTS (
    SELECT FROM df4l.client_policy
    WHERE client_uuid = df4l.client.client_uuid
);


CREATE TABLE df4l.client0_debt (
    client_debt_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    client_uuid uuid NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    name varchar(64) not null,
    balance numeric,
    balance_date date,
    interest_rate numeric,
    monthly_payment numeric,
    active boolean NOT NULL DEFAULT true,
    note varchar(1024),
    data jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT client0_debt_pkey PRIMARY KEY (client_debt_uuid),
    CONSTRAINT fk_client FOREIGN KEY (client_uuid) REFERENCES df4l.client0(client_uuid) ON DELETE RESTRICT
);

CREATE TABLE df4l.client0_event (
    client_event_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    client_uuid uuid NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    session_token varchar(64),
    create_addr varchar(64),
    create_auth jsonb NOT NULL DEFAULT '[]'::jsonb,
    create_name varchar(64),
    create_source varchar(64),
    event_type varchar(32) NOT NULL,
    event_date date NOT NULL DEFAULT now(),
    note varchar(1024),
    amount numeric(12,2),
    data jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT client_event_pkey PRIMARY KEY (client_event_uuid),
    CONSTRAINT fk_client FOREIGN KEY (client_uuid) REFERENCES df4l.client0(client_uuid) ON DELETE RESTRICT
);

CREATE TABLE df4l.client0_note (
    client_note_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    client_uuid uuid NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    note varchar(4096) NOT NULL,
    data jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT client_note_pkey PRIMARY KEY (client_note_uuid),
    CONSTRAINT fk_client FOREIGN KEY (client_uuid) REFERENCES df4l.client0(client_uuid) ON DELETE RESTRICT
);


CREATE TABLE df4l.gbu_agent (
    gbu_agent_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    gbu_agent_esid varchar(20) NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    name varchar(128) NOT NULL,
    first_name varchar(128) NOT NULL,
    last_name varchar(128) NOT NULL,
    active boolean NOT NULL DEFAULT true,
    admin_note text,
    data jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT "gbu_agent_pkey" PRIMARY KEY (gbu_agent_uuid),
    CONSTRAINT "gbu_agent.agency_esid.uniq" UNIQUE (gbu_agent_esid)
);

CREATE SCHEMA IF NOT EXISTS df4l_admin;

CREATE TABLE df4l_admin.identity (
    identity_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    perm_api_usage boolean DEFAULT true NOT NULL,
    perm_web_usage boolean DEFAULT true NOT NULL,
    perm_agency_read boolean DEFAULT false NOT NULL,
    perm_agency_write boolean DEFAULT false NOT NULL,
    perm_advisor_read boolean DEFAULT false NOT NULL,
    perm_advisor_write boolean DEFAULT false NOT NULL,
    perm_client_read boolean DEFAULT false NOT NULL,
    perm_client_write boolean DEFAULT false NOT NULL,
    CONSTRAINT identity_pkey PRIMARY KEY (identity_uuid),
    CONSTRAINT "identity>>identity" FOREIGN KEY (identity_uuid) REFERENCES auth_fence.identity (identity_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT
);


CREATE TABLE df4l.signup_advisor_setting (
    signup_advisor_setting_msid varchar(16) NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    name varchar(128) NOT NULL,
    note text,
    active boolean NOT NULL DEFAULT true,
    stripe_product_monthly_d2c_subscription_esid varchar(64) not null,
    stripe_price_monthly_d2c_subscription_esid varchar(64) not null,
    stripe_product_tech_service_fee_esid varchar(64) not null,
    stripe_price_tech_service_fee_esid varchar(64) not null,
    stripe_product_credit_report_charge_esid varchar(64) not null,
    stripe_price_credit_report_charge_esid varchar(64) not null,
    CONSTRAINT "signup_advisor_setting_pkey" PRIMARY KEY (signup_advisor_setting_msid)
);
CREATE TABLE df4l.signup_advisor (
    signup_advisor_uuid uuid DEFAULT public.uuidv7() NOT NULL,
    session_token varchar(64) NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    create_addr cidr NOT NULL,

    -- agency information
    agency_esid varchar(20),

    -- personal information
    first_name varchar(20),
    last_name varchar(20),
        
    email varchar(128),
    email_code varchar(4),
    email_code_sent_ts timestamptz(6),
    email_code_expire_ts timestamptz(6),
    email_code_attempts int4 NOT NULL DEFAULT 0,
    email_verified_ts timestamptz(6),

    phone varchar(64),
    phone_code varchar(4),
    phone_code_sent_ts timestamptz(6),
    phone_code_expire_ts timestamptz(6),
    phone_code_attempts int4 NOT NULL DEFAULT 0,
    phone_verified_ts timestamptz(6),

    -- terms and conditions
    terms_document_uuid uuid,
    terms_revision varchar(64),
    terms_accepted_name varchar(100),
    terms_accepted_ts timestamptz(6),
    terms_accepted_addr cidr,

    stripe_customer_id varchar(128),
    stripe_checkout_session_id varchar(128),
    stripe_payment_status varchar(64),
    stripe_subscription_id varchar(128),
    payment_completed_ts timestamptz(6),

    signup_completed_ts timestamptz(6),
    advisor_uuid_created uuid,
    signup_advisor_setting_msid varchar(16),

    CONSTRAINT "signup_advisor_pkey" PRIMARY KEY (signup_advisor_uuid),
    CONSTRAINT "signup_advisor>>advisor" FOREIGN KEY (advisor_uuid_created) REFERENCES df4l.advisor (advisor_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT "signup_advisor>>terms_document" FOREIGN KEY (terms_document_uuid) REFERENCES "legal_plane"."document" (document_uuid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    CONSTRAINT "signup_advisor>>setting" FOREIGN KEY (signup_advisor_setting_msid) REFERENCES df4l.signup_advisor_setting (signup_advisor_setting_msid) ON DELETE RESTRICT ON UPDATE RESTRICT,
    
    UNIQUE (advisor_uuid_created)
);

CREATE TABLE df4l.statelic (
    state_code char(2) NOT NULL,
    label varchar(128) NOT NULL,
    CONSTRAINT "statelic_pkey" PRIMARY KEY (state_code),
    CONSTRAINT fk_state FOREIGN KEY (state_code) REFERENCES addr_iso.us_state(state_code) ON DELETE RESTRICT
);

CREATE TABLE df4l.advisor_statelic (
    advisor_uuid uuid NOT NULL,
    state_code char(2) NOT NULL,
    create_ts timestamptz(6) NOT NULL DEFAULT now(),
    data jsonb NOT NULL DEFAULT '{}'::jsonb,
    CONSTRAINT "advisor_statelic_pkey" PRIMARY KEY (advisor_uuid, state_code),
    CONSTRAINT fk_advisor FOREIGN KEY (advisor_uuid) REFERENCES df4l.advisor(advisor_uuid) ON DELETE RESTRICT,
    CONSTRAINT fk_statelic FOREIGN KEY (state_code) REFERENCES df4l.statelic(state_code) ON DELETE RESTRICT,
    CONSTRAINT fk_iso_us_state FOREIGN KEY (state_code) REFERENCES addr_iso.us_state(state_code) ON DELETE RESTRICT
);

-- Pre-fill with 50 US states
INSERT INTO df4l.statelic (state_code, label) VALUES
('AL', 'Alabama'),
('AK', 'Alaska'),
('AZ', 'Arizona'),
('AR', 'Arkansas'),
('CO', 'Colorado'),
('CT', 'Connecticut'),
('DC', 'District of Columbia'),
('DE', 'Delaware'),
('FL', 'Florida'),
('GA', 'Georgia'),
('HI', 'Hawaii'),
('ID', 'Idaho'),
('IL', 'Illinois'),
('IN', 'Indiana'),
('IA', 'Iowa'),
('KS', 'Kansas'),
('KY', 'Kentucky'),
('LA', 'Louisiana'),
('ME', 'Maine'),
('MD', 'Maryland'),
('MA', 'Massachusetts'),
('MI', 'Michigan'),
('MN', 'Minnesota'),
('MS', 'Mississippi'),
('MO', 'Missouri'),
('MT', 'Montana'),
('NE', 'Nebraska'),
('NV', 'Nevada'),
('NH', 'New Hampshire'),
('NJ', 'New Jersey'),
('NM', 'New Mexico'),
('NC', 'North Carolina'),
('ND', 'North Dakota'),
('OH', 'Ohio'),
('OK', 'Oklahoma'),
('PA', 'Pennsylvania'),
('RI', 'Rhode Island'),
('SC', 'South Carolina'),
('SD', 'South Dakota'),
('TN', 'Tennessee'),
('TX', 'Texas'),
('UT', 'Utah'),
('VT', 'Vermont'),
('VA', 'Virginia'),
('WA', 'Washington'),
('WV', 'West Virginia'),
('WI', 'Wisconsin'),
('WY', 'Wyoming');


services:
    postgres:
        image: postgres:17.5
        container_name: postgres
        environment:
            POSTGRES_USER: app
            POSTGRES_PASSWORD: pass
        ports:
            - "20000:5432"
        command:
            - "postgres"
            - "-c"
            - "fsync=off"
            - "-c"
            - "synchronous_commit=off"
            - "-c"
            - "full_page_writes=off"
            - "-c"
            - "autovacuum=off"
            - "-c"
            - "wal_level=minimal"
            - "-c"
            - "max_wal_size=4GB"
            - "-c"
            - "max_wal_senders=0"
        volumes:
            - type: tmpfs
              target: /var/lib/postgresql/data

    redis:
        image: redis:7 # Using latest stable 7.x version tag
        container_name: redis
        ports:
            - "20001:6379"
        # 🚀 Disable persistence for max performance. Data is lost on restart.
        command:
            - "redis-server"
            - "--save"
            - '""' # Disables RDB database snapshots.
            - "--appendonly"
            - "no" # Disables the append-only file.

#![allow(dead_code)]
#![allow(unused_assignments)]

use super::*;
use approck::Module;

/// Tests assume that a Redis server is running on localhost:6379
async fn system() -> ModuleStruct {
    let config = ModuleConfig {
        host: "localhost".to_string(),
        port: Some(6379),
        database: 15,
        connect_timeout: None,
        tls: false,
    };

    let system = ModuleStruct::new(config).unwrap();
    system.init().await.unwrap();
    system
}

macro_rules! test_set_get {
    ($name:ident, $value:expr) => {
        #[tokio::test(flavor = "current_thread")]
        async fn $name(){
            let system = system().await;
            let mut dbcx = system.get_dbcx().await.unwrap();
            let key = stringify!(test_set_get $name );
            let value = $value;
            let mut result = $value; // for type inference
            dbcx.set_val(key, value.clone()).await.unwrap();
            result = dbcx.get_val(key).await.unwrap();
            assert_eq!(result, value);
        }
    };
}

test_set_get!(test_set_get_string, "Hello, world!".to_string());
test_set_get!(test_set_get_i32, 68410);

async fn set_mget<
    T: Clone
        + redis::FromRedisValue
        + redis::ToRedisArgs
        + Send
        + Sync
        + std::cmp::PartialEq
        + std::fmt::Debug,
>(
    dbcx: &mut RedisCX<'_>,
    keys: &[&str],
    values: &[T],
) {
    assert_eq!(keys.len(), values.len());
    for (idx, key) in keys.iter().enumerate() {
        dbcx.set_val(key, values[idx].clone()).await.unwrap();
    }

    let result = dbcx.mget_val::<T>(keys).await.unwrap();
    assert_eq!(result, values);
}

macro_rules! test_set_mget {
    ($name:ident, $keys:expr, $values:expr) => {
        #[tokio::test(flavor = "current_thread")]
        async fn $name() {
            let system = system().await;
            let mut dbcx = system.get_dbcx().await.unwrap();
            let keys = $keys;
            let values = $values;
            set_mget(&mut dbcx, &keys, &values).await;
        }
    };
}

test_set_mget!(
    test_set_mget_string,
    vec![
        "set_mget_string_key1",
        "set_mget_string_key2",
        "set_mget_string_key3"
    ],
    vec![
        "value1".to_string(),
        "value2".to_string(),
        "value3".to_string()
    ]
);
test_set_mget!(
    test_set_mget_i32,
    vec![
        "set_mget_i32_key1",
        "set_mget_i32_key2",
        "set_mget_i32_key3"
    ],
    vec![1, 2, 3]
);

#[tokio::test(flavor = "current_thread")]
async fn test_set_get_json() {
    let system = system().await;
    let mut dbcx = system.get_dbcx().await.unwrap();
    // test object
    let json = serde_json::json!({
         "key1": "value1",
         "key2": 2,
         "key3": [3, 4, 5],
    });

    dbcx.set_json("set_get_json_key1", &json).await.unwrap();
    let result: serde_json::Value = dbcx.get_json("set_get_json_key1").await.unwrap();
    assert_eq!(result, json);
}

#[tokio::test(flavor = "current_thread")]
async fn test_set_mget_json() {
    let system = system().await;
    let mut dbcx = system.get_dbcx().await.unwrap();
    // test objects
    let keys = [
        "set_mget_json_key1",
        "set_mget_json_key2",
        "set_mget_json_key3",
        "set_mget_json_key4",
    ];
    let values = vec![
        serde_json::json!("value1"),
        serde_json::json!(2),
        serde_json::json!([3, 4, 5]),
        serde_json::json!({
             "key1": "value1",
             "key2": 2,
             "key3": [3, 4, 5],
        }),
    ];

    for (idx, key) in keys.iter().enumerate() {
        dbcx.set_json(key, &values[idx]).await.unwrap();
    }

    let result = dbcx.mget_json::<serde_json::Value>(&keys).await.unwrap();
    assert_eq!(result, values);
}

#[tokio::test(flavor = "current_thread")]
async fn test_append_str() {
    let system = system().await;
    let mut dbcx = system.get_dbcx().await.unwrap();
    // set example string
    dbcx.set_val("test_append", "Hello").await.unwrap();
    dbcx.append_str("test_append", ", world!").await.unwrap();

    let result: String = dbcx.get_val("test_append").await.unwrap();
    assert_eq!(result, "Hello, world!");
}

#[tokio::test(flavor = "current_thread")]
async fn test_keys_str() {
    let system = system().await;
    let mut dbcx = system.get_dbcx().await.unwrap();

    // set example strings
    dbcx.set_val("test_keys_str_key1", "value1").await.unwrap();
    dbcx.set_val("test_keys_str_key2", "value2").await.unwrap();
    dbcx.set_val("test_keys_str_key3", "value3").await.unwrap();

    let result: Vec<String> = dbcx.keys_str("test_keys_str_key*").await.unwrap();
    assert!(result.contains(&"test_keys_str_key1".to_string()));
    assert!(result.contains(&"test_keys_str_key2".to_string()));
    assert!(result.contains(&"test_keys_str_key3".to_string()));
}

#[tokio::test(flavor = "current_thread")]
async fn test_del() {
    let system = system().await;
    let mut dbcx = system.get_dbcx().await.unwrap();

    // set example strings
    dbcx.set_val("test_del_key1", "value1").await.unwrap();
    dbcx.set_val("test_del_key2", "value2").await.unwrap();
    dbcx.set_val("test_del_key3", "value3").await.unwrap();

    dbcx.del("test_del_key1").await.unwrap();
    dbcx.del("test_del_key2").await.unwrap();
    dbcx.del("test_del_key3").await.unwrap();

    let result: Vec<String> = dbcx.keys_str("test_del_key*").await.unwrap();
    assert_eq!(result, Vec::<String>::new());
}

#[tokio::test(flavor = "current_thread")]
async fn test_exists() {
    let system = system().await;
    let mut dbcx = system.get_dbcx().await.unwrap();

    // set example strings
    dbcx.set_val("test_exists_key1", "value1").await.unwrap();
    dbcx.set_val("test_exists_key2", "value2").await.unwrap();
    dbcx.set_val("test_exists_key3", "value3").await.unwrap();

    let result = dbcx.exists("test_exists_key1").await.unwrap();
    assert!(result);

    let result = dbcx.exists("test_exists_key4").await.unwrap();
    assert!(!result);
}

// list operations
#[tokio::test(flavor = "current_thread")]
async fn test_list_push_pop() {
    let system = system().await;
    let mut dbcx = system.get_dbcx().await.unwrap();

    // push
    dbcx.lpush_val("test_list_push_pop", "value1")
        .await
        .unwrap();
    dbcx.lpush_val("test_list_push_pop", "value2")
        .await
        .unwrap();
    dbcx.lpush_val("test_list_push_pop", "value3")
        .await
        .unwrap();

    // ensure the indexes ae correct
    let result: Vec<String> = dbcx.lrange_val("test_list_push_pop", 0, 2).await.unwrap();
    assert_eq!(result, vec!["value3", "value2", "value1"]);

    // pop
    let result: String = dbcx.lpop_val("test_list_push_pop").await.unwrap();
    assert_eq!(result, "value3");

    let result: String = dbcx.lpop_val("test_list_push_pop").await.unwrap();
    assert_eq!(result, "value2");

    let result: String = dbcx.lpop_val("test_list_push_pop").await.unwrap();
    assert_eq!(result, "value1");
}

#[tokio::test(flavor = "current_thread")]
async fn test_list_push_pop_json() {
    let system = system().await;
    let mut dbcx = system.get_dbcx().await.unwrap();

    // push
    dbcx.lpush_json("test_list_push_pop_json", &serde_json::json!("value1"))
        .await
        .unwrap();
    dbcx.lpush_json("test_list_push_pop_json", &serde_json::json!(2))
        .await
        .unwrap();
    dbcx.lpush_json("test_list_push_pop_json", &serde_json::json!([3, 4, 5]))
        .await
        .unwrap();

    // pop
    let result: serde_json::Value = dbcx.lpop_json("test_list_push_pop_json").await.unwrap();
    assert_eq!(result, serde_json::json!([3, 4, 5]));

    let result: serde_json::Value = dbcx.lpop_json("test_list_push_pop_json").await.unwrap();
    assert_eq!(result, serde_json::json!(2));

    let result: serde_json::Value = dbcx.lpop_json("test_list_push_pop_json").await.unwrap();
    assert_eq!(result, serde_json::json!("value1"));
}

#[tokio::test(flavor = "current_thread")]
async fn test_list_index_value() {
    let system = system().await;
    let mut dbcx = system.get_dbcx().await.unwrap();

    // index
    dbcx.lpush_val("test_list_index_value", "value1")
        .await
        .unwrap();
    dbcx.lpush_val("test_list_index_value", "value2")
        .await
        .unwrap();
    dbcx.lpush_val("test_list_index_value", "value3")
        .await
        .unwrap();

    // index
    let result: String = dbcx.lindex_val("test_list_index_value", 0).await.unwrap();
    assert_eq!(result, "value3");

    let result: String = dbcx.lindex_val("test_list_index_value", 1).await.unwrap();
    assert_eq!(result, "value2");

    let result: String = dbcx.lindex_val("test_list_index_value", 2).await.unwrap();
    assert_eq!(result, "value1");
}

#[tokio::test(flavor = "current_thread")]
async fn test_list_index_range() {
    let system = system().await;
    let mut dbcx = system.get_dbcx().await.unwrap();

    // push
    dbcx.lpush_val("test_list_index_range", "value1")
        .await
        .unwrap();
    dbcx.lpush_val("test_list_index_range", "value2")
        .await
        .unwrap();
    dbcx.lpush_val("test_list_index_range", "value3")
        .await
        .unwrap();

    // range
    let result: Vec<String> = dbcx
        .lrange_val("test_list_index_range", 0, 1)
        .await
        .unwrap();
    assert_eq!(result, vec!["value3", "value2"]);

    let result: Vec<String> = dbcx
        .lrange_val("test_list_index_range", 1, 2)
        .await
        .unwrap();
    assert_eq!(result, vec!["value2", "value1"]);
}

#[tokio::test(flavor = "current_thread")]
async fn test_list_rpop_rpush() {
    let system = system().await;
    let mut dbcx = system.get_dbcx().await.unwrap();
    // push
    dbcx.rpush_val("test_list_rpop_rpush", "value1")
        .await
        .unwrap();
    dbcx.rpush_val("test_list_rpop_rpush", "value2")
        .await
        .unwrap();
    dbcx.rpush_val("test_list_rpop_rpush", "value3")
        .await
        .unwrap();

    // ensure the indexes ae correct
    let result: Vec<String> = dbcx.lrange_val("test_list_rpop_rpush", 0, 2).await.unwrap();
    assert_eq!(result, vec!["value1", "value2", "value3"]);

    // pop
    let result: String = dbcx.rpop_val("test_list_rpop_rpush").await.unwrap();
    assert_eq!(result, "value3");

    let result: String = dbcx.rpop_val("test_list_rpop_rpush").await.unwrap();
    assert_eq!(result, "value2");

    let result: String = dbcx.rpop_val("test_list_rpop_rpush").await.unwrap();
    assert_eq!(result, "value1");
}

#[tokio::test(flavor = "current_thread")]
async fn test_list_rpop_lpush() {
    let system = system().await;
    let mut dbcx = system.get_dbcx().await.unwrap();

    // push
    dbcx.rpush_val("test_list_rpop_lpush", "value1")
        .await
        .unwrap();
    dbcx.rpush_val("test_list_rpop_lpush", "value2")
        .await
        .unwrap();
    dbcx.rpush_val("test_list_rpop_lpush", "value3")
        .await
        .unwrap();

    // pop
    let result: String = dbcx.rpop_val("test_list_rpop_lpush").await.unwrap();
    assert_eq!(result, "value3");

    let result: String = dbcx.rpop_val("test_list_rpop_lpush").await.unwrap();
    assert_eq!(result, "value2");

    let result: String = dbcx.rpop_val("test_list_rpop_lpush").await.unwrap();
    assert_eq!(result, "value1");
}

// Counter operations
#[tokio::test(flavor = "current_thread")]
async fn test_counter_incr_decr() {
    let system = system().await;
    let mut dbcx = system.get_dbcx().await.unwrap();

    // increment
    dbcx.set_val("test_counter_incr_decr", 0).await.unwrap();
    dbcx.incr("test_counter_incr_decr", 3).await.unwrap();

    let result: i32 = dbcx.get_val("test_counter_incr_decr").await.unwrap();
    assert_eq!(result, 3);

    // decrement
    dbcx.decr("test_counter_incr_decr", 2).await.unwrap();

    let result: i32 = dbcx.get_val("test_counter_incr_decr").await.unwrap();
    assert_eq!(result, 1);
}

// Hash operations
#[tokio::test(flavor = "current_thread")]
async fn test_hash_set_get() {
    let system = system().await;
    let mut dbcx = system.get_dbcx().await.unwrap();

    // set
    dbcx.hset_val("test_hash_set_get", "key1", "value1")
        .await
        .unwrap();
    dbcx.hset_val("test_hash_set_get", "key2", "value2")
        .await
        .unwrap();
    dbcx.hset_val("test_hash_set_get", "key3", "value3")
        .await
        .unwrap();

    // get
    let result: String = dbcx.hget_val("test_hash_set_get", "key1").await.unwrap();
    assert_eq!(result, "value1");

    let result: String = dbcx.hget_val("test_hash_set_get", "key2").await.unwrap();
    assert_eq!(result, "value2");

    let result: String = dbcx.hget_val("test_hash_set_get", "key3").await.unwrap();
    assert_eq!(result, "value3");
}

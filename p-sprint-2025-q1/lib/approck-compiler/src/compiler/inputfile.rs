use std::path::PathBuf;

use granite::WebPath;
use granite_compiler::syn_path_equal;
use syn::parse_file;

pub(super) struct ParseResponse {
    pub http_modules: Vec<crate::HttpModule>,
    pub prefix_modules: Vec<crate::PrefixModule>,
    pub api_modules: Vec<crate::ApiModule>,
}

pub(super) fn parse_all(
    app_crate: &super::CrateInfo,
    path_hits: Vec<super::PathHit>,
) -> Result<ParseResponse, crate::CompileError> {
    let mut approck_http_rval = Vec::new();
    let mut approck_prefix_rval = Vec::new();
    let mut approck_api_rval = Vec::new();
    let mut compile_error = crate::CompileError::default();

    // construct a syn::path::Path to "approck::http"
    let approck_http_path = syn::parse_str::<syn::Path>("approck::http").unwrap(); // UNWRAP OK: LITERAL
    let approck_prefix_path = syn::parse_str::<syn::Path>("approck::prefix").unwrap(); // UNWRAP OK: LITERAL
    let approck_api_path = syn::parse_str::<syn::Path>("approck::api").unwrap(); // UNWRAP OK: LITERAL
    let approck_api2_path = syn::parse_str::<syn::Path>("approck::api2").unwrap(); // UNWRAP OK: LITERAL

    for path_hit in path_hits {
        let code = std::fs::read_to_string(&path_hit.abs_path).unwrap_or_else(|e| {
            panic!("\n\nFailed to read file {:?}: {}\n\n", path_hit.abs_path, e)
        });

        let ast = parse_file(&code).unwrap_or_else(|e| {
            panic!(
                "\n\nUnable to parse rust code in `{}`: {:#?}\n\n",
                path_hit.rel_path, e
            )
        });

        let mut approck_http_tuples = vec![];
        let mut approck_prefix_tuples = vec![];
        let mut approck_api_tuples = vec![];

        let rel_path = path_hit.rel_path.to_owned();

        // for every module in the ast at the top level, look at each attribute, and take note of ones that
        // are either approck::http or approck::prefix, at the same time validating that they are mutually
        // exclusive and not duplicated.
        for item in ast.items {
            if let syn::Item::Mod(item_mod) = item {
                let mod_name = item_mod.ident.to_string();

                let span = item_mod.ident.span();
                let mod_line = span.start().line;

                let mut approck_http = None;
                let mut approck_prefix = None;
                let mut approck_api = None;

                for attr in item_mod.attrs.iter() {
                    if syn_path_equal(attr.path(), &approck_http_path) {
                        if approck_http.is_some() {
                            compile_error.macro_conflict_error(
                                &path_hit.rel_path,
                                mod_line,
                                "Found more than one [approck::http] attribute on module",
                            );
                            continue;
                        }
                        approck_http = Some(attr.clone());
                    } else if syn_path_equal(attr.path(), &approck_prefix_path) {
                        if approck_http.is_some() {
                            compile_error.macro_conflict_error(
                                &path_hit.rel_path,
                                mod_line,
                                "Found more than one [approck::prefix] attribute on module",
                            );
                            continue;
                        }
                        approck_prefix = Some(attr.clone());
                    } else if syn_path_equal(attr.path(), &approck_api_path) {
                        if approck_api.is_some() {
                            compile_error.macro_conflict_error(
                                &path_hit.rel_path,
                                mod_line,
                                "Found more than one [approck::api] attribute on module",
                            );
                            continue;
                        }
                        approck_api = Some(attr.clone());
                    } else if syn_path_equal(attr.path(), &approck_api2_path) {
                        if approck_api.is_some() {
                            compile_error.macro_conflict_error(
                                &path_hit.rel_path,
                                mod_line,
                                "Found more than one [approck::api] or [approck::api2] attribute on module",
                            );
                            continue;
                        }
                        approck_api = Some(attr.clone());
                    }
                }

                match (approck_http, approck_prefix, approck_api) {
                    (None, None, None) => {
                        // not an http module
                    }
                    (Some(attr), None, None) => {
                        approck_http_tuples.push((attr, item_mod, mod_name, mod_line));
                    }
                    (None, Some(attr), None) => {
                        approck_prefix_tuples.push((attr, item_mod, mod_name, mod_line));
                    }
                    (None, None, Some(attr)) => {
                        approck_api_tuples.push((attr, item_mod, mod_name, mod_line));
                    }
                    _ => {
                        compile_error.macro_conflict_error(
                            &path_hit.rel_path,
                            mod_line,
                            "Found multiple [approck::*] attributes on module",
                        );
                    }
                }
            }
        }

        // Actually parse each of the attributes
        for (attr, item_mod, mod_name, mod_line) in approck_http_tuples {
            let attr_token_stream: proc_macro2::TokenStream =
                attr.parse_args().unwrap_or_else(|_| {
                    panic!(
                        "Error parsing #[approck::http] on function `{}(...)` in file `{}`",
                        mod_name, path_hit.rel_path
                    )
                });

            let item_mod_ident = item_mod.ident.clone();
            let http_module_inner = match crate::http_macro::parse_module_inner(
                attr_token_stream,
                item_mod,
            ) {
                Ok(attribute) => attribute,
                Err(e) => {
                    panic!(
                        "Error parsing #[approck::http] on function `{}(...)` in file `{}` at span {:?}: {}",
                        mod_name,
                        path_hit.rel_path,
                        e.get_diagnostic(),
                        e.get_error_str()
                    )
                }
            };

            let syn_path = {
                let mut i = path_hit.syn_path.clone();
                i.segments.push(syn::PathSegment {
                    ident: item_mod_ident,
                    arguments: syn::PathArguments::None,
                });
                i
            };

            let (web_dir, script_path) = path_hit.web_dir_and_script_path();

            let web_path = WebPath::from_segments(
                web_dir.clone(),
                http_module_inner.web_path_fragment.clone().into_segments(),
            );

            match crate::HttpModule::new(
                rel_path.clone(),
                PathBuf::from(&app_crate.rel_path),
                path_hit.crate_name.clone(),
                web_path,
                script_path.clone(),
                mod_line,
                http_module_inner,
                syn_path,
            ) {
                Ok(module) => approck_http_rval.push(module),
                Err(err) => compile_error.merge(err),
            }
        }

        // Actually parse each of the attributes
        for (attr, item_mod, mod_name, mod_line) in approck_prefix_tuples {
            let attr_token_stream: proc_macro2::TokenStream =
                attr.parse_args().unwrap_or_else(|e| {
                    panic!(
                        "Error parsing #[approck::prefix] on function `{}(...)` in file `{}`: {}",
                        mod_name, path_hit.rel_path, e
                    )
                });

            let item_mod_ident = item_mod.ident.clone();
            let prefix_module_inner = match crate::prefix_macro::parse_macro_input_and_item(
                attr_token_stream,
                item_mod,
            ) {
                Ok(attribute) => attribute,
                Err(e) => {
                    panic!(
                        "Error parsing #[approck::prefix] on function `{}(...)` in file `{}` at span {:?}: {}",
                        mod_name,
                        path_hit.rel_path,
                        e.get_diagnostic(),
                        e.get_error_str()
                    )
                }
            };

            let syn_path = {
                let mut i = path_hit.syn_path.clone();
                i.segments.push(syn::PathSegment {
                    ident: item_mod_ident,
                    arguments: syn::PathArguments::None,
                });
                i
            };

            let (web_dir, script_path) = path_hit.web_dir_and_script_path();

            let web_path = WebPath::from_segments(
                web_dir.clone(),
                prefix_module_inner
                    .web_path_fragment
                    .clone()
                    .into_segments(),
            );

            match crate::PrefixModule::new(
                path_hit.crate_ident.clone(),
                rel_path.clone(),
                web_path,
                script_path.clone(),
                mod_line,
                prefix_module_inner,
                syn_path,
            ) {
                Ok(module) => approck_prefix_rval.push(module),
                Err(err) => compile_error.merge(err),
            }
        }

        for (_attr, item_mod, mod_name, mod_line) in approck_api_tuples {
            let mod_ident = item_mod.ident.clone();

            let api_module_inner = match crate::api_macro::parse_module_inner(
                quote::quote! {},
                item_mod,
                "api",
            ) {
                Ok(attribute) => attribute,
                Err(e) => {
                    panic!(
                        "Error parsing #[approck::api] on module `{}` in file `{}` at span {:?}: {}",
                        mod_name,
                        path_hit.rel_path,
                        e.get_diagnostic(),
                        e.get_error_str()
                    )
                }
            };

            let api_name =
                crate::rel_path_and_mod_ident_to_api_name(&rel_path, &api_module_inner.mod_ident);

            let syn_path = {
                let mut i = path_hit.syn_path.clone();
                i.segments.push(syn::PathSegment {
                    ident: mod_ident,
                    arguments: syn::PathArguments::None,
                });
                i
            };

            approck_api_rval.push(crate::ApiModule {
                crate_name: path_hit.crate_ident.clone(),
                rel_path: rel_path.clone(),
                line_number: mod_line,
                api_name,
                syn_path,
                inner: api_module_inner,
            });
        }
    }

    // bail out early if there are errors
    if compile_error.has_errors() {
        Err(compile_error)
    } else {
        Ok(ParseResponse {
            http_modules: approck_http_rval,
            prefix_modules: approck_prefix_rval,
            api_modules: approck_api_rval,
        })
    }
}

use granite_compiler::find_single_attr;
use proc_macro2::TokenStream;
use quote::{ToTokens, quote};
use syn::spanned::Spanned;
use syn::{Attribute, Ident, Item, parse_file};

/// Represents an item with potential gtype attributes
struct FoundItem<'a> {
    /// The identifier of the item
    ident: &'a Ident,
    /// The attributes of the item
    attributes: &'a [Attribute],
    /// The full item
    item: &'a Item,
    /// The module identifier if the item is inside a module
    mod_ident: Option<&'a Ident>,
}

pub(super) fn parse(
    path_hit: super::FileSystemScanMatch,
    located_gadt_collection: &mut Vec<super::LocatedItem>,
) {
    // read path
    let code = std::fs::read_to_string(&path_hit.abs_path)
        .unwrap_or_else(|e| panic!("\n\nFailed to read file {:?}: {}\n\n", path_hit.abs_path, e));

    let ast = parse_file(&code).unwrap_or_else(|e| {
        panic!(
            "\n\nUnable to parse rust code in `{}`: {:#?}\n\n",
            path_hit.rel_path, e
        )
    });

    // construct a syn::path::Path to "approck::http"
    let granite_gtype_path = syn::parse_str::<syn::Path>("granite::gtype").unwrap(); // okay because it is a literal
    let approck_api_path = syn::parse_str::<syn::Path>("approck::api").unwrap(); // okay because it is a literal
    let approck_api2_path = syn::parse_str::<syn::Path>("approck::api2").unwrap(); // okay because it is a literal

    for item in ast.items {
        // for lifetimes, we need a place to store injected items for processing after the main loop
        let mut injected_items = Vec::new();

        // Find any structs, enums, or types, or mods with structs, enums, or types
        let mut found_items = match &item {
            syn::Item::Struct(item_struct) => {
                vec![FoundItem {
                    ident: &item_struct.ident,
                    attributes: &item_struct.attrs,
                    item: &item,
                    mod_ident: None,
                }]
            }
            syn::Item::Enum(item_enum) => vec![FoundItem {
                ident: &item_enum.ident,
                attributes: &item_enum.attrs,
                item: &item,
                mod_ident: None,
            }],

            syn::Item::Type(item_type) => vec![FoundItem {
                ident: &item_type.ident,
                attributes: &item_type.attrs,
                item: &item,
                mod_ident: None,
            }],

            // if we encounter a module, we will recurse into it (1 level) and find gtypes there
            syn::Item::Mod(item_mod) => {
                let mut found_items = vec![];
                let items = match &item_mod.content {
                    Some((_, items)) => items,
                    None => continue,
                };

                // Special code to handle approck::api injection of an Error enum
                // Search for APPROCK_API_829332 to find related code.
                match find_single_attr(&item_mod.attrs, &approck_api_path) {
                    // One approck::api attribute
                    Ok(Some(_)) => {
                        injected_items.push((
                            item_mod.ident.clone(),
                            crate::api_macro::approck_api_response_enum(),
                        ));
                        located_gadt_collection.push(super::LocatedItem::ApiCallFunction {
                            rel_path: path_hit.rel_path.clone(),
                            fn_line: item_mod.span().start().line,
                            mod_ident: item_mod.ident.clone(),
                        });
                    }
                    // No approck::api attribute
                    Ok(None) => (),

                    // More than one approck::api attribute
                    Err(duplicate_attr) => {
                        panic!(
                            "Found more than one [approck::api] attribute on mod `{}` defined at {}:{}",
                            item_mod.ident,
                            path_hit.rel_path,
                            duplicate_attr.span().start().line
                        );
                    }
                };

                match find_single_attr(&item_mod.attrs, &approck_api2_path) {
                    // One approck::api2 attribute
                    Ok(Some(_)) => {
                        located_gadt_collection.push(super::LocatedItem::Api2CallFunction {
                            rel_path: path_hit.rel_path.clone(),
                            fn_line: item_mod.span().start().line,
                            mod_ident: item_mod.ident.clone(),
                        });
                    }
                    // No approck::api2 attribute
                    Ok(None) => (),

                    // More than one approck::api2 attribute
                    Err(duplicate_attr) => {
                        panic!(
                            "Found more than one [approck::api2] attribute on mod `{}` defined at {}:{}",
                            item_mod.ident,
                            path_hit.rel_path,
                            duplicate_attr.span().start().line
                        );
                    }
                }

                for item in items {
                    match item {
                        syn::Item::Struct(item_struct) => {
                            found_items.push(FoundItem {
                                ident: &item_struct.ident,
                                attributes: &item_struct.attrs,
                                item,
                                mod_ident: Some(&item_mod.ident),
                            });
                        }
                        syn::Item::Enum(item_enum) => {
                            found_items.push(FoundItem {
                                ident: &item_enum.ident,
                                attributes: &item_enum.attrs,
                                item,
                                mod_ident: Some(&item_mod.ident),
                            });
                        }
                        syn::Item::Type(item_type) => {
                            found_items.push(FoundItem {
                                ident: &item_type.ident,
                                attributes: &item_type.attrs,
                                item,
                                mod_ident: Some(&item_mod.ident),
                            });
                        }
                        _ => continue,
                    }
                }
                found_items
            }

            _ => continue,
        };

        // inject any items that were injected into the module
        for (mod_ident, item) in injected_items.iter() {
            match item {
                syn::Item::Enum(item_enum) => {
                    found_items.push(FoundItem {
                        ident: &item_enum.ident,
                        attributes: &item_enum.attrs,
                        item,
                        mod_ident: Some(mod_ident),
                    });
                }
                syn::Item::Struct(item_struct) => {
                    found_items.push(FoundItem {
                        ident: &item_struct.ident,
                        attributes: &item_struct.attrs,
                        item,
                        mod_ident: Some(mod_ident),
                    });
                }
                syn::Item::Type(item_type) => {
                    found_items.push(FoundItem {
                        ident: &item_type.ident,
                        attributes: &item_type.attrs,
                        item,
                        mod_ident: Some(mod_ident),
                    });
                }
                _ => unreachable!(),
            }
        }

        // Now iterate over each found item, and process gtype attributes
        for found_item in found_items {
            let fn_name = found_item.ident.to_string();
            let span = found_item.ident.span();

            let attr = match find_single_attr(found_item.attributes, &granite_gtype_path) {
                Ok(Some(attr)) => attr,
                Ok(None) => continue,
                Err(duplicate_attr) => {
                    panic!(
                        "Found more than one [granite::gtype] attribute on item `{}` defined at {}:{}",
                        fn_name,
                        path_hit.rel_path,
                        duplicate_attr.span().start().line
                    );
                }
            };

            // we support #[granite::gtype] and #[granite::gtype(...)]
            let attr_tokenstream: TokenStream = match &attr.meta {
                syn::Meta::Path(_) => quote! {},
                syn::Meta::List(metalist) => metalist.tokens.to_token_stream(),
                syn::Meta::NameValue(_) => {
                    panic!(
                        "attribute error on `{}` at `{}`\nexpected a list, found a name-value",
                        fn_name, path_hit.rel_path
                    )
                }
            };

            let mut gadt = granite_compiler::gtype::parser::parse_gadt(
                attr_tokenstream,
                found_item.item.clone(),
            )
            .unwrap_or_else(|e| {
                panic!(
                    "attribute error on `{}` at `{}`\n{}",
                    fn_name,
                    path_hit.rel_path,
                    e.get_error_str()
                )
            });

            gadt.mod_ident = found_item.mod_ident.cloned();

            located_gadt_collection.push(super::LocatedItem::GADT {
                rel_path: path_hit.rel_path.clone(),
                fn_line: span.start().line,
                item_name: fn_name,
                g_macro_input: gadt,
            });
        }
    }
}

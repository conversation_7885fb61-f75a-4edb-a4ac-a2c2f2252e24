/// This module is responsible for the `approck::tstype` macro.
///
/// It is intended to be used on structs and enums to generate TypeScript types.
/// Provided the file it is used on is named `foo.rs`, then output will be written to `fooλ.mts`.
///
/// The macro is used in two ways:
/// 1. By the rust compiler as a derive-like macro which implements the TsSerialize and TsDeserialize traits.
/// 2. By a hook for `git grep` to find files that use the macro, parse them, and generate TypeScript types.
///
mod filesystem;
mod inputfile;

use approck_memfs::MemFs;
use granite_compiler::gtype::{ChunkItem, GeneratedChunk, TSImport};
use indexmap::IndexMap;
use quote::quote;
use std::{collections::HashSet, path::PathBuf};

#[allow(dead_code)]
#[derive(Debug, Clone)]
struct FileSystemScanMatch {
    abs_path: PathBuf,
    rel_path: String,
    ext: Option<String>,
}

#[allow(dead_code, clippy::upper_case_acronyms, clippy::large_enum_variant)]
enum LocatedItem {
    GADT {
        rel_path: String,
        fn_line: usize,
        item_name: String,
        g_macro_input: granite_compiler::gtype::GMacroInput,
    },
    ApiCallFunction {
        rel_path: String,
        fn_line: usize,
        mod_ident: syn::Ident,
    },
    Api2CallFunction {
        rel_path: String,
        fn_line: usize,
        mod_ident: syn::Ident,
    },
}

/// Scans and generates Lambda functions for typescript
/// Returns a list of modified paths
pub fn scan_and_generate(mem_fs: &mut MemFs) {
    let workspace_path = mem_fs.path();
    let mut located_gadt_collection = Vec::new();
    let mut generated_chunks = Vec::new();
    let mut rel_path_to_file_chunks_map: IndexMap<String, Vec<GeneratedChunk>> = IndexMap::new();

    let file_system_scan_matches: Vec<FileSystemScanMatch> =
        self::filesystem::scan_for_tstype_macro(workspace_path);

    // Parse each file and add the GADTs to the collection
    for file_system_scan_match in file_system_scan_matches {
        self::inputfile::parse(file_system_scan_match, &mut located_gadt_collection);
    }

    // Condense and merge into one GeneratedChunk per file
    for located_item in located_gadt_collection {
        match located_item {
            //-------------------------------------------------------------------------------------
            LocatedItem::GADT {
                rel_path,
                fn_line,
                item_name,
                g_macro_input,
            } => {
                let output_path = crate::rel_path_to_lambda_path(&rel_path, "mts");

                generated_chunks.push((
                    output_path,
                    granite_compiler::gtype::gen_ts::g_macro_input_to_generated_chunk(
                        Some(format!("{item_name} @ {rel_path}:{fn_line}")),
                        g_macro_input,
                    ),
                ));
            }

            //-------------------------------------------------------------------------------------
            LocatedItem::ApiCallFunction {
                rel_path,
                fn_line: _,
                mod_ident,
            } => {
                let api_name = crate::rel_path_and_mod_ident_to_api_name(&rel_path, &mod_ident);
                let output_path = crate::rel_path_to_lambda_path(&rel_path, "mts");

                let mut generated_chunk = GeneratedChunk::new(Some(mod_ident.clone()));

                generated_chunk.note(format!("api call function for `{mod_ident}`"));
                generated_chunk.import(TSImport::other_item("call_api", "@approck/app_socket.mts"));
                generated_chunk.code(quote! {

                    export const api = {
                        api: #api_name,
                        call: call,
                        Input_validate: Input_validate,
                        Input_Partial_validate: Input_Partial_validate,
                        Input_encode: Input_encode,
                        Input_Error_decode: Input_Error_decode,
                        Output_decode: Output_decode,
                        Response_decode: Response_decode,
                    };

                    export async function call(data: Input): Promise<Response> {
                        return call_api(#api_name, Input_encode(data)).then(
                            (response) => {
                                const outer_result = Response_decode(response);
                                if ("Ok" in outer_result) {
                                    return outer_result.Ok;
                                }
                                else {
                                    // Convert any decoding errors into a response type anyway
                                    return {"Error": [outer_result.Err]};
                                }
                            },
                        );
                    }
                });

                generated_chunks.push((output_path, generated_chunk));
            }

            //-------------------------------------------------------------------------------------
            LocatedItem::Api2CallFunction {
                rel_path,
                fn_line: _,
                mod_ident,
            } => {
                let api_name = crate::rel_path_and_mod_ident_to_api_name(&rel_path, &mod_ident);
                let output_path = crate::rel_path_to_lambda_path(&rel_path, "mts");

                let mut generated_chunk = GeneratedChunk::new(Some(mod_ident.clone()));

                generated_chunk.note(format!("api call function for `{mod_ident}`"));
                generated_chunk.import(TSImport::other_item("call_api", "@approck/app_socket.mts"));
                generated_chunk.code(quote! {

                    export const api = {
                        api: #api_name,
                        call: call,
                        Input_encode: Input_encode,
                        Output_decode: Output_decode,
                    };

                    export async function call(data: Input): Promise<Output> {
                        const output = await call_api(#api_name, Input_encode(data));
                        const r = Output_decode(output);
                        if ("Err" in r) {
                            throw r.Err;
                        }
                        return r.Ok;
                    }
                });

                generated_chunks.push((output_path, generated_chunk));
            }
        };
    }

    // write out all the chunks, and update the rel_path_to_file_chunk_map
    for (rel_path, generated_chunk) in generated_chunks {
        rel_path_to_file_chunks_map
            .entry(rel_path)
            .or_default()
            .push(generated_chunk);
    }

    // write out all the chunks
    for (rel_path, mut file_chunks) in rel_path_to_file_chunks_map {
        let rel_path = PathBuf::from(rel_path);
        let mut file_content = String::new();

        file_content.push_str("//deno-fmt-ignore-file\n");

        // Collect all imports first
        let mut imports = HashSet::new();
        for file_chunk in &mut file_chunks {
            imports.extend(file_chunk.imports.drain());
        }

        // Write imports
        file_content.push_str(
            granite_compiler::gtype::gen_ts::ts_import_set_to_token_stream(&imports)
                .to_string()
                .as_str(),
        );
        file_content.push('\n');

        // Group chunks by mod_ident
        let mut mod_ident_map: IndexMap<Option<String>, Vec<GeneratedChunk>> = IndexMap::new();
        for chunk in file_chunks {
            let key = chunk.mod_ident.clone().map(|i| i.to_string());
            mod_ident_map.entry(key).or_default().push(chunk);
        }

        mod_ident_map.sort_unstable_keys();

        // Write chunks grouped by namespace
        for (mod_ident_opt, chunks) in mod_ident_map {
            // Open namespace if we have one
            if let Some(mod_ident) = &mod_ident_opt {
                file_content.push_str(&format!("export namespace {mod_ident} {{\n"));
            }

            // Write all chunks for this namespace
            for chunk in chunks {
                for item in chunk.items {
                    match item {
                        ChunkItem::Code(code) => {
                            file_content.push_str(&format!("{code}\n"));
                        }
                        ChunkItem::Note(comment) => {
                            file_content.push_str(&format!("// {comment}\n"));
                        }
                    }
                }
            }

            // Close namespace if we had one
            if mod_ident_opt.is_some() {
                file_content.push_str("}\n");
            }
        }

        // Deal with `== =` -> `=== ` related issues
        crate::patch_token_stream_output_for_typescript(&mut file_content);

        // Write the file
        mem_fs
            .write_typescript(&rel_path, file_content)
            .unwrap_or_else(|e| panic!("Error writing {rel_path:?}: {e:?}"));
    }
}

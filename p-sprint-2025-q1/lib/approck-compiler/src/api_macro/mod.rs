mod parse;
mod process_call_function;
mod process_input_type;
mod process_output_type;

pub use parse::parse_module_inner;

use process_call_function::process_call_function;
use process_input_type::process_input_type;
use process_output_type::process_output_type;

use quote::quote;

pub struct ApiModule {
    pub crate_name: String,
    pub rel_path: String,
    pub line_number: usize,
    pub api_name: String,
    pub syn_path: syn::Path,
    pub inner: ApiModuleInner,
}

pub struct ApiModuleInner {
    pub mod_ident: syn::Ident,
    pub mod_attrs: Vec<syn::Attribute>,
    pub mod_items: Vec<syn::Item>,
    pub input_type: IOType,
    pub output_type: IOType,
    pub call_function: CallFunction,
    pub api_module_type: ApiModuleType,
}

pub enum ApiModuleType {
    WebSocket,
    WebSocket2,
    Internal,
}

pub enum IOType {
    Struct(syn::ItemStruct),
    Enum(syn::ItemEnum),
    Type(syn::ItemType),
}

pub struct CallFunction {
    args: Vec<CallFunctionArg>,
    return_type: CallFunctionReturnType,
    body: syn::Block,
}

pub enum CallFunctionArg {
    App(syn::Ident),
    Identity(syn::Ident),
    Input(syn::Ident),
}

pub enum CallFunctionReturnType {
    ResultOfOutput,
    ResultOfResponse,
    Response,
    Output,
}

impl std::fmt::Debug for ApiModule {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}:{}", self.rel_path, self.line_number)
    }
}

impl quote::ToTokens for IOType {
    fn to_tokens(&self, tokens: &mut proc_macro2::TokenStream) {
        match self {
            IOType::Struct(item_struct) => item_struct.to_tokens(tokens),
            IOType::Enum(item_enum) => item_enum.to_tokens(tokens),
            IOType::Type(item_type) => item_type.to_tokens(tokens),
        }
    }
}

impl quote::ToTokens for CallFunction {
    fn to_tokens(&self, tokens: &mut proc_macro2::TokenStream) {
        let args = &self.args;
        let return_type = &self.return_type;
        let body = &self.body;
        tokens.extend(quote! {
            pub async fn call(#(#args),*) #return_type #body
        });
    }
}

impl CallFunction {
    pub fn arg_call_tokens(&self) -> proc_macro2::TokenStream {
        let args = &self.args;
        quote! {
            #(#args),*
        }
    }
}

impl quote::ToTokens for CallFunctionArg {
    fn to_tokens(&self, tokens: &mut proc_macro2::TokenStream) {
        tokens.extend(match self {
            CallFunctionArg::App(ident) => quote! {
                #ident: &'static impl crate::App
            },
            CallFunctionArg::Identity(ident) => quote! {
                #ident: &::std::sync::Arc<impl crate::Identity>
            },
            CallFunctionArg::Input(ident) => quote! {
                #ident: Input
            },
        });
    }
}

impl quote::ToTokens for CallFunctionReturnType {
    fn to_tokens(&self, tokens: &mut proc_macro2::TokenStream) {
        tokens.extend(match self {
            CallFunctionReturnType::ResultOfOutput => quote! { -> ::granite::Result<Output> },
            CallFunctionReturnType::ResultOfResponse => quote! { -> ::granite::Result<Response> },
            CallFunctionReturnType::Output => quote! { -> Output },
            CallFunctionReturnType::Response => quote! { -> Response },
        });
    }
}

/// Expand the API module into the final code
pub fn expand(api_mod: ApiModuleInner) -> proc_macro2::TokenStream {
    let mod_ident = &api_mod.mod_ident;
    let mod_attrs = &api_mod.mod_attrs;
    let mod_items = &api_mod.mod_items;

    let input_type = &api_mod.input_type;
    let output_type = &api_mod.output_type;
    let call_function = &api_mod.call_function;

    let wrap_function_args = call_function
        .args
        .iter()
        .map(|arg| match arg {
            CallFunctionArg::App(_) => quote! { app },
            CallFunctionArg::Identity(_) => quote! { identity },
            CallFunctionArg::Input(_) => quote! { input },
        })
        .collect::<Vec<_>>();

    let websocket_code = match api_mod.api_module_type {
        //-----------------------------------------------------------------------------------------
        ApiModuleType::WebSocket => {
            let response_enum = approck_api_response_enum();

            // at this point we will have a valid `input` variable.  Need to call the `call()` function and handle correct output
            let invoke_and_return = match &call_function.return_type {
                CallFunctionReturnType::ResultOfOutput => quote! {
                    // call function
                    match call(#(#wrap_function_args),*).await {
                        Ok(output) => {
                            approck::app_socket::Server2ClientPayload::ApiReturn {
                                data: Response::Output(output).gtype_encode()
                            }
                        }
                        Err(e) => {
                            approck::app_socket::Server2ClientPayload::Err {
                                message: e.get_external_message().map(|m| m.to_string()),
                                error_uuid: e.get_error_uuid().to_string(),
                            }
                        }
                    }
                },
                CallFunctionReturnType::ResultOfResponse => quote! {
                    // call function
                    match call(#(#wrap_function_args),*).await {
                        Ok(response) => {
                            approck::app_socket::Server2ClientPayload::ApiReturn {
                                data: response.gtype_encode()
                            }
                        }
                        Err(e) => {
                            approck::app_socket::Server2ClientPayload::Err {
                                message: e.get_external_message().map(|m| m.to_string()),
                                error_uuid: e.get_error_uuid().to_string(),
                            }
                        }
                    }
                },
                CallFunctionReturnType::Output => quote! {
                    // call function
                    let output = call(#(#wrap_function_args),*).await;

                    // encode output
                    approck::app_socket::Server2ClientPayload::ApiReturn {
                        data: Response::Output(output).gtype_encode()
                    }
                },
                CallFunctionReturnType::Response => quote! {
                    // call function
                    let response = call(#(#wrap_function_args),*).await;

                    // encode output
                    approck::app_socket::Server2ClientPayload::ApiReturn {
                        data: response.gtype_encode()
                    }
                },
            };

            quote! {
                pub async fn wrap(
                    app: &'static impl crate::App,
                    identity: &std::sync::Arc<impl crate::Identity>,
                    input: granite::JsonValue,
                ) -> approck::app_socket::Server2ClientPayload {

                    // check api usage
                    if ! crate::Identity::api_usage(&**identity) {
                        return approck::app_socket::Server2ClientPayload::AuthorizationError{message: "API usage not allowed".to_string()};
                    }

                    use ::granite::{GTypeDecode, GTypeEncode, GTypeValidate};

                    // decode input
                    let input = match Input::gtype_decode(Some(input)) {
                        Ok(input) => input,
                        Err(e) => {
                            return approck::app_socket::Server2ClientPayload::BadRequest {
                                message: format!("Invalid input: {}", e),
                            };
                        }
                    };

                    // validate input
                    let input = match input.gtype_validate() {
                        Ok(input) => input,
                        Err(e) => {
                            return approck::app_socket::Server2ClientPayload::ApiReturn {
                                data: Response::ValidationError(e).gtype_encode()
                            };
                        }
                    };

                    #invoke_and_return
                }

                #response_enum
            }
        }
        //-----------------------------------------------------------------------------------------
        ApiModuleType::WebSocket2 => {
            // at this point we will have a valid `input` variable.  Need to call the `call()` function and handle correct output
            let invoke_and_return = match &call_function.return_type {
                CallFunctionReturnType::ResultOfOutput => quote! {
                    // call function
                    match call(#(#wrap_function_args),*).await {
                        Ok(output) => {
                            approck::app_socket::Server2ClientPayload::ApiReturn {
                                data: output.gtype_encode()
                            }
                        }
                        Err(e) => {
                            approck::app_socket::Server2ClientPayload::Err {
                                message: e.get_external_message().map(|m| m.to_string()),
                                error_uuid: e.get_error_uuid().to_string(),
                            }
                        }
                    }
                },
                CallFunctionReturnType::ResultOfResponse => quote! {
                    unreachable!("approck::api2 doesn't support Response types");
                },
                CallFunctionReturnType::Output => quote! {
                    // call function
                    let output = call(#(#wrap_function_args),*).await;

                    // encode output
                    approck::app_socket::Server2ClientPayload::ApiReturn {
                        data: output.gtype_encode()
                    }
                },
                CallFunctionReturnType::Response => quote! {
                    unreachable!("approck::api2 doesn't support Response types");
                },
            };

            quote! {
                pub async fn wrap(
                    app: &'static impl crate::App,
                    identity: &std::sync::Arc<impl crate::Identity>,
                    input: granite::JsonValue,
                ) -> approck::app_socket::Server2ClientPayload {
                    if !crate::Identity::api_usage(&**identity) {
                        return approck::app_socket::Server2ClientPayload::AuthorizationError {
                            message: "API usage not allowed".to_string(),
                        };
                    }

                    use ::granite::{GTypeDecode, GTypeEncode, GTypeValidate};


                    let input = match Input::gtype_decode(Some(input)) {
                        Ok(input) => input,
                        Err(e) => {
                            return approck::app_socket::Server2ClientPayload::BadRequest {
                                message: format!("Invalid input: {}", e),
                            };
                        }
                    };

                    #invoke_and_return
                }
            }
        }
        //-----------------------------------------------------------------------------------------
        ApiModuleType::Internal => quote! {},
    };

    quote! {
        #(#mod_attrs)*
        pub mod #mod_ident {
            #input_type
            #output_type
            #call_function
            #websocket_code

            #(#mod_items)*
        }
    }
}

// Search for APPROCK_API_829332 to find related code.
pub fn approck_api_response_enum() -> syn::Item {
    syn::parse2(quote! {
        #[granite::gtype(ApiOutput)]
        pub enum Response {
            Output(Output),
            ValidationError(NestedError<Input_Error>),
            AuthorizationError(String),
            BadRequest(String),
            Error(String),
        }
    })
    .unwrap()
}

use super::{CallFunction, CallFunction<PERSON>rg, CallFunctionReturnType};
use granite_compiler::tokenator::TokenError;
use quote::ToTokens;
use syn::spanned::Spanned;

pub(super) fn process_call_function(item_fn: syn::ItemFn) -> Result<CallFunction, TokenError> {
    // validate it has `pub`
    if !matches!(item_fn.vis, syn::Visibility::Public(_)) {
        return Err(TokenError::new(
            item_fn.span(),
            "call() function must be public",
        ));
    }

    // validate it has `async`
    if item_fn.sig.asyncness.is_none() {
        return Err(TokenError::new(
            item_fn.span(),
            "call() function must be async",
        ));
    }

    // validate the return value is granite::Result<Output>
    let return_type = {
        let mut token_iter =
            granite_compiler::tokenator::TokenIter::new(item_fn.sig.output.to_token_stream());
        token_iter.step();

        // consume `->`
        token_iter.take_dash()?;
        token_iter.take_greater_than()?;

        // consume `Result<Output>` or `Output`
        match token_iter.get_ident_as_string()?.as_str() {
            "Result" => {
                token_iter.step();
                token_iter.take_less_than()?;

                let rval = match token_iter.get_ident_as_string()?.as_str() {
                    "Output" => {
                        token_iter.step();
                        CallFunctionReturnType::ResultOfOutput
                    }
                    "Response" => {
                        token_iter.step();
                        CallFunctionReturnType::ResultOfResponse
                    }
                    _ => {
                        return Err(token_iter.error("expected `Output` or `Response`"));
                    }
                };

                token_iter.take_greater_than()?;
                token_iter.get_end()?;

                rval
            }
            "Output" => {
                token_iter.step();
                token_iter.get_end()?;
                CallFunctionReturnType::Output
            }
            "Response" => {
                token_iter.step();
                token_iter.get_end()?;
                CallFunctionReturnType::Response
            }
            _ => {
                return Err(token_iter.error("expected `Output` or `Result<Output>`"));
            }
        }
    };

    // calculate and validate the args
    let args = {
        let mut args = Vec::new();

        let mut has_app = false;
        let mut has_identity = false;
        let mut has_input = false;

        for fn_arg in item_fn.sig.inputs {
            let mut token_iter =
                granite_compiler::tokenator::TokenIter::new(fn_arg.to_token_stream());
            token_iter.step();

            let arg_ident = token_iter.take_ident()?;
            token_iter.take_colon()?;

            match token_iter.get_ident_as_string()?.as_str() {
                "App" => {
                    if has_app {
                        return Err(token_iter.error("App parameter already exists"));
                    }
                    has_app = true;

                    token_iter.step();
                    args.push(CallFunctionArg::App(arg_ident));
                }
                "Identity" => {
                    if has_identity {
                        return Err(token_iter.error("Identity parameter already exists"));
                    }
                    has_identity = true;

                    token_iter.step();
                    args.push(CallFunctionArg::Identity(arg_ident));
                }
                "Input" => {
                    if has_input {
                        return Err(token_iter.error("Input parameter already exists"));
                    }
                    has_input = true;

                    token_iter.step();
                    args.push(CallFunctionArg::Input(arg_ident));
                }
                _ => {
                    return Err(token_iter.error("expected one of [`App`, `Identity`, or `Input`]"));
                }
            }

            token_iter.get_end()?;
        }

        args
    };

    Ok(CallFunction {
        args,
        return_type,
        body: *item_fn.block,
    })
}

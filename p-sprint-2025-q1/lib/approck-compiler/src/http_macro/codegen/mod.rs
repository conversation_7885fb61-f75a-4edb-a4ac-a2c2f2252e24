#[cfg(test)]
mod tests;

use proc_macro2::TokenStream;
use quote::{ToTokens, TokenStreamExt, quote};

mod path;
mod post_form;
mod post_json;
mod query_string;

use super::request_params::RequestParamType;

/// One coherent place to aggregate all the tokens that will be used to generate the final code
/// Glossary:
/// * mrf = 'mod request function'
struct CodeGenTokens {
    mod_items: TokenStream,
    wrap_fn_sig_inputs: TokenStream,
    wrap_fn_items: TokenStream,
    wrap_fn_return: TokenStream,
    request_fn_params: TokenStream,
    request_fn_sig_inputs: TokenStream,
    derive_debug: TokenStream,
}

impl Default for CodeGenTokens {
    fn default() -> Self {
        Self {
            mod_items: quote! {},
            wrap_fn_items: quote! {},
            wrap_fn_sig_inputs: quote! {},
            wrap_fn_return: quote! {},
            request_fn_params: quote! {},
            request_fn_sig_inputs: quote! {},
            derive_debug: quote! {},
        }
    }
}

impl CodeGenTokens {
    /// Unless not needed, each statement should end in a `;`
    fn mod_items(&mut self, tokens: TokenStream) {
        self.mod_items.append_all(tokens);
    }

    /// MUST END IN A `,`
    /// Add tokens to the wrap function signature.
    fn wrap_fn_sig_inputs(&mut self, tokens: TokenStream) {
        self.wrap_fn_sig_inputs.append_all(tokens);
    }

    /// Unless not needed, each statement should end in a `;`
    fn wrap_fn_items(&mut self, tokens: TokenStream) {
        self.wrap_fn_items.append_all(tokens);
    }

    /// MUST END IN A `,`
    /// Adds argument tokens to the `let response = request(...)` call
    fn request_fn_params(&mut self, tokens: TokenStream) {
        self.request_fn_params.append_all(tokens);
    }

    /// MUST END IN A `,`
    /// Adds tokens to the request function signature
    fn request_fn_sig_inputs(&mut self, tokens: TokenStream) {
        self.request_fn_sig_inputs.append_all(tokens);
    }
}

pub fn expand(mod_bundle: crate::HttpModuleInner) -> TokenStream {
    let mut codegen_tokens = CodeGenTokens::default();

    // --------------------------------------------------------------------------------------------
    // handle `AUTH {AuthType}`; (required)

    match &mod_bundle.auth_type {
        crate::AuthType::IdentityMethod(ident_string) => {
            let ident = granite_compiler::ident_str!(ident_string);
            let ident_error = format!("identity.{ident}() did not return true");
            codegen_tokens.wrap_fn_items(quote! {
                if ! identity.#ident() {
                    return Err(
                        granite::Error::new(granite::ErrorType::Authorization)
                            .add_context(#ident_error)
                    );
                }
            });
        }
        crate::AuthType::None => {}
    }

    // --------------------------------------------------------------------------------------------
    // handle `derive_debug;`

    if mod_bundle.derive_debug {
        codegen_tokens.derive_debug = quote! {
            #[derive(Debug)]
        };
    }

    // --------------------------------------------------------------------------------------------
    // handle `Path`

    if mod_bundle.has_path_captures() {
        let path_captures = &mod_bundle.iter_path_captures().collect();
        self::path::process(&mut codegen_tokens, path_captures);
    }

    if let Some(qs_parts) = &mod_bundle.query_string {
        self::query_string::process(&mut codegen_tokens, qs_parts);
    }

    // --------------------------------------------------------------------------------------------
    // handle code gen for post type
    match &mod_bundle.mod_post_type {
        crate::PostType::None => {}
        crate::PostType::PostFormStruct(post_form_struct) => {
            codegen_tokens.mod_items(self::post_form::process(post_form_struct));
        }
        crate::PostType::PostJsonStruct(item_struct) => {
            let post_json_parse = post_json::post_json_parse_function();
            codegen_tokens.mod_items(quote! {
                #item_struct
                #post_json_parse
            });
        }
        crate::PostType::PostJsonEnum(item_enum) => {
            let post_json_parse = post_json::post_json_parse_function();
            codegen_tokens.mod_items(quote! {
                #item_enum
                #post_json_parse
            });
        }
        crate::PostType::PostJsonType(item_type) => {
            let post_json_parse = post_json::post_json_parse_function();
            codegen_tokens.mod_items(quote! {
                #item_type
                #post_json_parse
            });
        }
    }

    // --------------------------------------------------------------------------------------------
    // handle `request(*params)`
    for param in &mod_bundle.mod_request_fn_params.0 {
        codegen_tokens.request_fn_sig_inputs(quote! { #param, });

        match &param.param_type {
            RequestParamType::App => {
                // TODO: insert the trait_paths
                codegen_tokens.request_fn_params(quote! { app, });
            }

            // Document is a passthrough argument from the router -> wrap -> request
            RequestParamType::Document(ident_string) => {
                // unwrap acceptable because the origin of this ident_string is an ident
                let ident = granite_compiler::ident_str!(ident_string);

                codegen_tokens.wrap_fn_sig_inputs(quote! {
                    document: impl crate::#ident,
                });

                codegen_tokens.request_fn_params(quote! { document, });
            }

            RequestParamType::Identity => {
                codegen_tokens.request_fn_params(quote! { identity, });
            }

            RequestParamType::AuthBasic => {
                codegen_tokens.mod_items(quote! {
                    type AuthBasic = (String, String);
                });
                codegen_tokens.wrap_fn_items(quote! {
                    // LUKE: realm needs to come from application config
                    let auth_basic = match req.auth_basic() {
                        Some(auth_basic) => auth_basic,
                        None => {
                            let mut response = ::approck::server::response::Empty {
                                status: approck::StatusCode::UNAUTHORIZED,
                                ..Default::default()
                            };
                            response.headers.insert(
                                "WWW-Authenticate",
                                approck::HeaderValue::from_str(r#"Basic realm="approck-example""#).unwrap(),
                            );
                            return Ok(::approck::server::response::Response::Empty(response));
                        }
                    };
                });
                codegen_tokens.request_fn_params(quote! { auth_basic, });
            }

            RequestParamType::AuthBasicOption => {
                codegen_tokens.mod_items(quote! {
                    type AuthBasicOption = Option<(String, String)>;
                });
                codegen_tokens.wrap_fn_items(quote! {
                    let auth_basic = req.auth_basic();
                });
                codegen_tokens.request_fn_params(quote! { auth_basic, });
            }

            RequestParamType::AuthBearer => {
                codegen_tokens.mod_items(quote! {
                    type AuthBearer = String;
                });
                codegen_tokens.wrap_fn_items(quote! {
                    let auth_bearer = match req.auth_bearer() {
                        Some(auth_bearer) => auth_bearer,
                        None => {
                            let mut response = ::approck::server::response::Text {
                                status: approck::StatusCode::UNAUTHORIZED,
                                content: "Header required: `Authorization: Bearer ...`".to_string(),
                                ..Default::default()
                            };
                            return Ok(::approck::server::response::Response::Text(response));
                        }
                    };
                });
                codegen_tokens.request_fn_params(quote! { auth_bearer, });
            }

            RequestParamType::AuthBearerOption => {
                codegen_tokens.mod_items(quote! {
                    type AuthBearerOption = Option<String>;
                });
                codegen_tokens.wrap_fn_items(quote! {
                    let auth_bearer = req.auth_bearer();
                });
                codegen_tokens.request_fn_params(quote! { auth_bearer, });
            }

            RequestParamType::Postgres => {
                codegen_tokens.mod_items(quote! {
                    use approck_postgres::DBCX;
                });
                codegen_tokens.wrap_fn_items(quote! {
                    let dbcx = approck_postgres::App::postgres_dbcx(app).await?;
                });
                codegen_tokens.request_fn_params(quote! { dbcx, });
            }

            RequestParamType::Redis => {
                codegen_tokens.mod_items(quote! {
                    use approck_redis::{RedisCX, AsyncCommands};
                });
                codegen_tokens.wrap_fn_items(quote! {
                    // this is called explicitly as a trait so that it has nicer error messages
                    let redis_cx = approck_redis::App::redis_dbcx(app).await?;
                });
                codegen_tokens.request_fn_params(quote! { redis_cx, });
            }

            RequestParamType::Path => {
                codegen_tokens.request_fn_params(quote! { path, });
            }

            // the Request type is simply an alias and passthrough
            RequestParamType::Request => {
                codegen_tokens.mod_items(quote! {
                    type Request = approck::server::Request;
                });
                codegen_tokens.request_fn_params(quote! {
                    req,
                });
            }

            // handle QueryString
            RequestParamType::QueryString => {
                codegen_tokens.wrap_fn_items(quote! {
                    let qs = QueryString::parse(&req).amend(|e| e
                        .add_context("QueryString::parse()")
                    )?;
                });
                codegen_tokens.request_fn_params(quote! {
                    qs,
                });
            }

            // handle Option<QueryString>
            RequestParamType::QueryStringOption => {
                codegen_tokens.wrap_fn_items(quote! {
                    let qs = if req.has_query_string() {
                        Some(QueryString::parse(&req).amend(|e| e
                            .add_context("QueryString::parse()")
                        )?)
                    } else {
                        None
                    };
                });
                codegen_tokens.request_fn_params(quote! {
                    qs,
                });
            }

            // handler PostForm
            RequestParamType::PostForm => {
                codegen_tokens.wrap_fn_items(quote! {
                    let post_form = PostForm::parse(req).await.amend(|e| e
                        .add_context("PostForm::parse()")
                    )?;
                });
                codegen_tokens.request_fn_params(quote! {
                    post_form,
                });
            }

            // handler Option<PostForm>
            RequestParamType::PostFormOption => {
                codegen_tokens.wrap_fn_items(quote! {
                    let post_form = if req.is_post() {
                        Some(PostForm::parse(req).await.amend(|e| e
                            .add_context("PostForm::parse()")
                        )?)
                    } else {
                        None
                    };
                });
                codegen_tokens.request_fn_params(quote! {
                    post_form,
                });
            }

            // handler PostForm
            RequestParamType::PostJson => {
                codegen_tokens.wrap_fn_items(quote! {
                    let post_json = post_json_parse(req).await.amend(|e| e
                        .add_context("#[approck::http] generated post_json_parse() function")
                    )?;
                });
                codegen_tokens.request_fn_params(quote! {
                    post_json,
                });
            }

            // any other type is an Option gets None
            RequestParamType::OptionalParam(_) => {
                codegen_tokens.request_fn_params(quote! {
                    None,
                });
            }
        }
    }

    // --------------------------------------------------------------------------------------------
    // handle return_types

    {
        let mut mods = Vec::new();
        let mut arms = Vec::new();
        let mut variants = Vec::new();

        if mod_bundle.return_types.Bytes {
            mods.push(quote! {
                use ::approck::server::response::Bytes;
            });
            variants.push(quote! {
                Bytes(approck::server::response::Bytes),
            });
            arms.push(quote! {
                Response::Bytes(v) => approck::server::response::Response::Bytes(v),
            });
        }

        if mod_bundle.return_types.Text {
            mods.push(quote! {
                use ::approck::server::response::Text;
            });
            variants.push(quote! {
                Text(approck::server::response::Text),
            });
            arms.push(quote! {
                Response::Text(v) => approck::server::response::Response::Text(v),
            });
        }

        if mod_bundle.return_types.Empty {
            mods.push(quote! {
                use ::approck::server::response::Empty;
            });
            variants.push(quote! {
                Empty(approck::server::response::Empty),
            });
            arms.push(quote! {
                Response::Empty(v) => approck::server::response::Response::Empty(v),
            });
        }

        if mod_bundle.return_types.HTML {
            mods.push(quote! {
                use ::approck::server::response::HTML;
            });
            variants.push(quote! {
                HTML(approck::server::response::HTML),
            });
            arms.push(quote! {
                Response::HTML(v) => approck::server::response::Response::HTML(v),
            });
        }

        if mod_bundle.return_types.JavaScript {
            mods.push(quote! {
                use ::approck::server::response::JavaScript;
            });
            variants.push(quote! {
                JavaScript(approck::server::response::JavaScript),
            });
            arms.push(quote! {
                Response::JavaScript(v) => approck::server::response::Response::JavaScript(v),
            });
        }

        if mod_bundle.return_types.CSS {
            mods.push(quote! {
                use ::approck::server::response::CSS;
            });
            variants.push(quote! {
                CSS(approck::server::response::CSS),
            });
            arms.push(quote! {
                Response::CSS(v) => approck::server::response::Response::CSS(v),
            });
        }

        if mod_bundle.return_types.JSON {
            mods.push(quote! {
                use ::approck::server::response::JSON;
            });
            variants.push(quote! {
                JSON(approck::server::response::JSON),
            });
            arms.push(quote! {
                Response::JSON(v) => approck::server::response::Response::JSON(v),
            });
        }

        if mod_bundle.return_types.SVG {
            mods.push(quote! {
                use ::approck::server::response::SVG;
            });
            variants.push(quote! {
                SVG(approck::server::response::SVG),
            });
            arms.push(quote! {
                Response::SVG(v) => approck::server::response::Response::SVG(v),
            });
        }

        if mod_bundle.return_types.NotFound {
            mods.push(quote! {
                use ::approck::server::response::NotFound;
            });
            variants.push(quote! {
                NotFound(approck::server::response::NotFound),
            });
            arms.push(quote! {
                Response::NotFound(v) => approck::server::response::Response::NotFound(v),
            });
        }

        if mod_bundle.return_types.Redirect {
            mods.push(quote! {
                use ::approck::server::response::Redirect;
            });
            variants.push(quote! {
                Redirect(approck::server::response::Redirect),
            });
            arms.push(quote! {
                Response::Redirect(v) => approck::server::response::Response::Redirect(v),
            });
        }

        if mod_bundle.return_types.WebSocketUpgrade {
            mods.push(quote! {
                use ::approck::server::websocket::{WebSocket, Message as WebSocketMessage, MessageData as WebSocketMessageData, MessageDataRef as WebSocketMessageDataRef};
                use ::approck::server::response::WebSocketUpgrade;
            });
            variants.push(quote! {
                WebSocketUpgrade(approck::server::response::WebSocketUpgrade),
            });
            arms.push(quote! {
                Response::WebSocketUpgrade(v) => approck::server::response::Response::WebSocketUpgrade(v),
            });
        }

        if mod_bundle.return_types.Stream {
            mods.push(quote! {
                use ::approck::server::response::Stream;
                use ::futures::StreamExt;

            });
            variants.push(quote! {
                Stream(approck::server::response::Stream),
            });
            arms.push(quote! {
                Response::Stream(v) => approck::server::response::Response::Stream(v),
            });
        }

        // generate actual code
        codegen_tokens.mod_items(quote! {
            #(#mods)*
        });

        codegen_tokens.mod_items(quote! {
            pub enum Response {
                #(#variants)*
            }
        });

        codegen_tokens.wrap_fn_return = quote! {
            Ok(match response {
                #(#arms)*
            })
        };
    }

    // --------------------------------------------------------------------------------------------
    // actual code generation

    let mod_items = &mod_bundle.mod_items_remaining;
    let mod_ident = &mod_bundle.mod_ident;
    let mod_attrs = &mod_bundle.mod_attrs;
    let mod_tokens = codegen_tokens.mod_items;

    //let wrap_fn_traits =
    //    crate::codegen::quote_traits("APP", &mod_bundle.get_wrap_fn_app_trait_list());

    let wrap_fn_traits = quote!(APP: crate::App, IDENTITY: crate::Identity);

    let wrap_fn_sig_inputs = codegen_tokens.wrap_fn_sig_inputs;
    let wrap_fn_items = codegen_tokens.wrap_fn_items;
    let wrap_fn_return = codegen_tokens.wrap_fn_return;

    let request_fn_params = codegen_tokens.request_fn_params;
    let request_fn_vis = &mod_bundle.mod_request_fn.vis.to_token_stream();
    let request_fn_async = mod_bundle.mod_request_fn.sig.asyncness.to_token_stream();
    let request_fn_sig_inputs = &codegen_tokens.request_fn_sig_inputs;
    let request_fn_block = &mod_bundle.mod_request_fn.block.to_token_stream();

    // Can be either `-> Response` or `-> Result<Response>`
    let request_fn_sig_output = match &mod_bundle.mod_request_fn_return {
        crate::RequestFunctionReturnType::Response => {
            quote! { -> Response }
        }
        crate::RequestFunctionReturnType::ResultResponse => {
            quote! { -> approck::Result<Response> }
        }
    };

    // When invoking the request() function, we need to place a `?` after it if it returns a Result, otherwise not
    let request_fn_invoke = match &mod_bundle.mod_request_fn_return {
        crate::RequestFunctionReturnType::Response => {
            quote! {
                let response = request(#request_fn_params).await;
            }
        }
        crate::RequestFunctionReturnType::ResultResponse => {
            quote! {
                let uri_string = req.uri_string(); // this is needed because it's being owned by request function
                let response = request(#request_fn_params).await.amend(|e| e
                    .add_context("error propagated out from request()")
                )?;
            }
        }
    };

    // Construct the expanded code
    let expanded_code = quote! {
        #(#mod_attrs)*
        pub mod #mod_ident {
            use approck::ResultExt;
            #mod_tokens
            pub async fn wrap<#wrap_fn_traits>(app: &'static APP, req: &mut approck::server::Request, identity: &::std::sync::Arc<IDENTITY>, #wrap_fn_sig_inputs) -> approck::Result<approck::server::response::Response> {
                if ! crate::Identity::web_usage(&**identity) {
                    approck::error!("web usage not allowed");
                    // return redirect to /
                    return Ok(approck::server::response::Response::Redirect("/?unauthorized".into()));
                }

                #wrap_fn_items
                #request_fn_invoke
                #wrap_fn_return
            }
            #request_fn_vis #request_fn_async fn request(#request_fn_sig_inputs) #request_fn_sig_output {
                #request_fn_block
            }
            #(#mod_items)*
        }
    };

    //panic!("Expanded code: \n {}", expanded_code.to_string());

    expanded_code
}

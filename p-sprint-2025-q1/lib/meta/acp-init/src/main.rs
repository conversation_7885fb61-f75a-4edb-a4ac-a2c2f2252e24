use std::{
    ffi::{OsStr, OsString},
    io::Write,
    os::unix::ffi::OsStrExt,
    path::{Path, PathBuf},
};

use acp_config::{AcpConfig, Binary};

#[derive(Debug, Default)]
struct Error {
    source: Option<Box<dyn std::error::Error + Send + Sync>>,
    context: Vec<String>,
}

impl Error {
    fn new(context: impl std::fmt::Display) -> Self {
        Self {
            context: vec![context.to_string()],
            ..Self::default()
        }
    }
    fn add_context(mut self, context: impl std::fmt::Display) -> Self {
        self.context.push(context.to_string());
        self
    }
}

impl std::fmt::Display for Error {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        for (i, c) in self.context.iter().enumerate() {
            f.write_str(c)?;
            if i < (self.context.len() - 1) {
                f.write_str(": ")?;
            }
        }

        if let Some(source) = &self.source {
            let mut source: &(dyn std::error::Error) = &**source;
            if !self.context.is_empty() {
                f.write_str("\n\n")?;
            }
            f.write_str("Caused By:\n")?;
            loop {
                write!(f, "  {source}")?;

                if let Some(s) = source.source() {
                    source = s
                } else {
                    break;
                }
            }
        }

        Ok(())
    }
}

type Result<T> = std::result::Result<T, Error>;

impl<E> From<E> for Error
where
    E: std::error::Error + Send + Sync + 'static,
{
    fn from(value: E) -> Self {
        Error {
            source: Some(Box::new(value)),
            ..Error::default()
        }
    }
}

trait ResultExt<T> {
    fn amend<CLOSURE>(self, closure: CLOSURE) -> Result<T>
    where
        CLOSURE: Fn(Error) -> Error;
}

/// Implement the Context trait for std::result::Result, before it is converted to Result<self::Error> type
impl<T, E> ResultExt<T> for std::result::Result<T, E>
where
    E: std::error::Error + Send + Sync + 'static,
{
    #[track_caller]
    fn amend<CLOSURE>(self, closure: CLOSURE) -> Result<T>
    where
        CLOSURE: Fn(Error) -> Error,
    {
        match self {
            Ok(v) => Ok(v),
            Err(e) => Err(closure(Error::from(e))),
        }
    }
}

/// Implement the Context trait for our own Result<Error> type, after it has been converted to Result<self::Error> type
impl<T> ResultExt<T> for self::Result<T> {
    fn amend<CLOSURE>(self, closure: CLOSURE) -> Result<T>
    where
        CLOSURE: Fn(Error) -> Error,
    {
        self.map_err(closure)
    }
}

fn shell(command: impl AsRef<OsStr>, capture_output: bool) -> Result<std::process::Output> {
    let command = command.as_ref();
    let command_str = command.to_string_lossy();
    #[cfg(not(any(target_os = "macos", target_os = "linux")))]
    return Err(Error::new("Unsupported operating system"));

    println!("\x1b[1;33m>>>\x1b[0m \x1b[1m{command_str}\x1b[0m");
    #[cfg(target_os = "linux")]
    let mut process_command = {
        let mut c = std::process::Command::new("bash");
        c.args([OsStr::new("-c"), command]);
        c
    };
    #[cfg(target_os = "macos")]
    let mut process_command = {
        let mut c = std::process::Command::new("zsh");
        c.args([OsStr::new("-c"), command]);
        c
    };
    process_command.stderr(std::io::stderr());
    if !capture_output {
        process_command.stdout(std::io::stdout());
    }
    let output = process_command.output().amend(|e| {
        e.add_context(format!(
            "Error executing command \x1b[1m{command_str}\x1b[0m"
        ))
    })?;

    if !output.status.success() {
        return Err(Error::new(format!(
            "Error executing command \x1b[1m{command_str}\x1b[0m, exited with status {}",
            output.status
        )));
    }

    Ok(output)
}

fn confirm(prompt: &str) -> Result<bool> {
    print!("{prompt} [y/N]: ");
    std::io::stdout().flush()?;

    let mut input = String::new();
    std::io::stdin().read_line(&mut input)?;

    Ok(matches!(input.trim().to_lowercase().as_str(), "y" | "yes"))
}

// Run a shell command, but first ask the user to confirm before running it.
// Skips the command if the user rejects.
fn shell_confirm(command: &str) -> Result<()> {
    if confirm(&format!(
        "The command \x1b[1m{command}\x1b[0m needs to be run. Confirm"
    ))? {
        shell(command, false)?;
    } else {
        println!("skipping");
    }
    Ok(())
}

fn which(binary_name: &str) -> Result<Option<PathBuf>> {
    if let Some(paths) = std::env::var_os("PATH") {
        for path in std::env::split_paths(&paths) {
            let full_path = path.join(binary_name);
            if full_path.exists() && std::fs::metadata(&full_path).map(|m| m.is_file())? {
                return Ok(Some(full_path));
            }
        }
    }
    Ok(None)
}

// Returns a list of binaries that are not on the path and probably haven't been installed yet.
fn check_installed<'a>(binaries: &[&'a str]) -> Result<Vec<&'a str>> {
    binaries
        .iter()
        .map(|binary| {
            Ok(if which(binary)?.is_some() {
                None
            } else {
                Some(*binary)
            })
        })
        .filter_map(Result::transpose)
        .collect::<Result<_>>()
}

// Check if a package is installed using dpkg
fn is_package_installed(package_name: &str) -> Result<bool> {
    let output = shell(format!("dpkg -l {package_name}"), true)?;

    if output.status.success() {
        let stdout = String::from_utf8(output.stdout)
            .amend(|e| e.add_context("Error parsing dpkg output as UTF-8"))?;
    
        Ok(stdout.lines().any(|line| {
            line.starts_with("ii") && line.contains(package_name)
        }))
    } else {
        Ok(false)
    }
}

// Returns a list of packages that are not installed yet.
fn check_packages_installed<'a>(packages: &[&'a str]) -> Result<Vec<&'a str>> {
    packages
        .iter()
        .map(|package| {
            Ok(if is_package_installed(package)? {
                None
            } else {
                Some(*package)
            })
        })
        .filter_map(Result::transpose)
        .collect::<Result<_>>()
}

fn hash_sha512(path: impl AsRef<Path>) -> Result<String> {
    let path = path.as_ref();
    let command = [OsStr::new("sha512sum"), quote(path).as_os_str()].join(OsStr::new(" "));
    let output = shell(command, true)?;
    if !output.status.success() {
        return Err(Error::new(format!(
            "Error calculating sha512 hash, exited with status {}",
            output.status
        )));
    }
    let output_string = String::from_utf8(output.stdout)
        .amend(|e| e.add_context("Error parsing sha512 output as valid UTF-8"))?;
    println!("{output_string}");
    let (hash, _) = output_string
        .split_once(" ")
        .ok_or_else(|| Error::new(format!("invalid output for sha512sum: {output_string}")))?;
    if hash.len() != 128 {
        return Err(Error::new(format!(
            "Invalid sha512 hash length {} (expected 128) for hash {output_string}",
            output_string.len()
        )));
    }
    Ok(hash.to_owned())
}

// You not drop this until you are finished using the directory.
// The Drop implementation will remove the temporary directory.
struct TemporaryDirectory {
    /// If set to `true` will prevent cleanup on `Drop`, useful for debugging.
    pub leak: bool,
    pub path: PathBuf,
}

impl Drop for TemporaryDirectory {
    fn drop(&mut self) {
        if !self.leak {
            info(format!("Cleaning up temporary directory {:?}", self.path));
            std::fs::remove_dir_all(&self.path).unwrap_or_else(|e| {
                eprintln!("Error removing temporary directory {:?}: {e}", self.path);
            })
        }
    }
}

/// Make a temporary directory.
fn make_temporary_directory() -> Result<TemporaryDirectory> {
    #[cfg(not(any(target_os = "macos", target_os = "linux")))]
    return Err(Error::new("Unsupported operating system"));

    let output = shell("mktemp -d", true)?;
    let mut stdout = output.stdout;
    if stdout.last() == Some(&b'\n') {
        stdout.pop();
    }
    let path = Path::new(OsStr::from_bytes(&stdout));
    println!("{path:?}");
    Ok(TemporaryDirectory {
        path: path.to_owned(),
        leak: false,
    })
}

fn info(message: impl std::fmt::Display) {
    println!("\x1b[32m{message}\x1b[0m")
}

fn install_dependencies() -> Result<()> {
    info("Installing dependencies (if required)");
    #[cfg(not(any(target_os = "macos", target_os = "linux")))]
    return Err(Error::new("Unsupported operating system"));

    #[cfg(target_os = "linux")]
    {
        // Check for actual binaries in PATH
        let binaries_not_installed = check_installed(&[
            "git-lfs", "clang", "curl",
            "zip", "unzip", "tar", "gzip",
            "pkg-config",
        ])?;

        // Check for packages using dpkg
        let packages_not_installed = check_packages_installed(&[
            "libssl-dev", "build-essential",
        ])?;

        let mut all_not_installed = Vec::new();
        all_not_installed.extend(binaries_not_installed);
        all_not_installed.extend(packages_not_installed);

        if !all_not_installed.is_empty() {
            println!(
                "The following system binaries are not on your path and need to be installed:\n{}",
                all_not_installed.join("\n")
            );
            shell_confirm(&format!("sudo apt install {}", all_not_installed.join(" ")))?;
        }
    }
    #[cfg(target_os = "macos")]
    {
        let not_yet_installed = check_installed(&["git-lfs", "clang"])?;
        if !not_yet_installed.is_empty() {
            println!(
                "The following system binaries are not on your path and need to be installed:\n{}",
                not_yet_installed.join("\n")
            );
            shell_confirm(&format!("brew install {}", not_yet_installed.join(" ")))?;
        }
    }
    info("Finished installing dependencies");
    Ok(())
}

fn configure_git() -> Result<()> {
    info("Configuring git repo");

    for command in [
        "git config core.quotepath false",
        "git config alias.ff 'merge --ff-only'",
        "git lfs install",
        "git lfs fetch",
        "git lfs checkout",
    ] {
        shell(command, false)?;
    }

    info("Finished configuring git repo");
    Ok(())
}

#[allow(unused_variables, reason = "not used in macos")]
fn configure_cargo(config: &acp_config::AcpConfig, low_mem: bool) -> Result<()> {
    info("Configuring cargo");
    #[cfg(not(any(target_os = "macos", target_os = "linux")))]
    return Err(Error::new("Unsupported operating system"));

    let low_mem_config = if low_mem {
        r##"
[build]
jobs = 1  # Limit to 1 parallel job to reduce memory overhead
rustflags = ["-C", "opt-level=0"]  # Disable optimizations for faster, lighter builds

[profile.dev]
opt-level = 0  # Ensure debug builds use minimal optimization
incremental = true  # Enable incremental compilation to save memory
codegen-units = 1  # Reduce memory usage during codegen (trade-off: slower builds)
"##
    } else {
        ""
    };

    std::fs::create_dir_all(".cargo")
        .amend(|e| e.add_context("Error creating .cargo directory"))?;

    #[cfg(target_os = "linux")]
    let config_toml = {
        let mold_path = config
            .get_target_binary_dependency("mold")
            .ok_or_else(|| Error::new("mold dependency missling"))?
            .target_path();
        let mold_path_str = mold_path.to_str().ok_or_else(|| {
            Error::new("Unable to format mold path {mold_path:?} as UTF-8 string")
        })?;
        format!(
            r##"
[net]
git-fetch-with-cli = true

[target.x86_64-unknown-linux-gnu]
linker = "clang"
rustflags = ["-C", "link-arg=--ld-path={mold_path_str}"]

{low_mem_config}
"##
        )
    };

    // mold linker is commercial on macos
    #[cfg(target_os = "macos")]
    let config_toml = format!(
        r##"
[net]
git-fetch-with-cli = true

{low_mem_config}
"##
    );

    info("writing .cargo/config.toml:");
    println!("{config_toml}");

    std::fs::write(".cargo/config.toml", config_toml)
        .amend(|e| e.add_context("Error writing to .cargo/config.toml file"))?;

    info("Finished configuring cargo");
    Ok(())
}

fn build_acp() -> Result<()> {
    info("Building acp");
    shell("cargo build --release -p acp", false)?;
    info("Finished building acp");
    Ok(())
}

fn run_acp() -> Result<()> {
    info("Running acp init");
    shell("target/release/acp init", false)?;
    info("Finished running acp init");
    Ok(())
}

fn format_string(s: &str, args: &[(&str, &str)]) -> String {
    let mut s = s.to_owned();
    for (name, value) in args {
        s = s.replace(&format!("{{{name}}}"), value);
    }
    s
}

fn quote(path: impl AsRef<Path>) -> OsString {
    let mut buffer = OsString::new();
    buffer.push("\"");
    buffer.push(path.as_ref().as_os_str());
    buffer.push("\"");
    buffer
}

fn cp(from: impl AsRef<Path>, to: impl AsRef<Path>) -> Result<()> {
    let command = [
        OsStr::new("cp"),
        quote(from).as_os_str(),
        quote(to).as_os_str(),
    ]
    .join(OsStr::new(" "));
    shell(command, false)?;
    Ok(())
}

fn mkdirall(path: impl AsRef<Path>) -> Result<()> {
    let command = [OsStr::new("mkdir -p"), quote(path).as_os_str()].join(OsStr::new(" "));
    shell(command, false)?;
    Ok(())
}

fn make_executable(path: impl AsRef<Path>) -> Result<()> {
    let command = [OsStr::new("chmod +x"), quote(path).as_os_str()].join(OsStr::new(" "));
    shell(command, false)?;
    Ok(())
}

fn download_binaries(config: &acp_config::AcpConfig) -> Result<()> {
    info(format!(
        "Downloading binary dependencies for {} {}",
        acp_config::TARGET_OS,
        acp_config::TARGET_ARCH
    ));

    let binaries = config.get_target_binary_dependencies()?;

    mkdirall(&*acp_config::TARGET_META_PATH)?;

    for binary in binaries {
        let Binary {
            url,
            hash,
            version,
            zip,
            ..
        } = &binary;
        let url = format_string(url, &[("version", version)]);
        let binary_path = binary.target_path();

        if binary_path.is_file() {
            let calculated_hash = hash_sha512(&binary_path)?;
            if hash == &calculated_hash {
                info(format!(
                    "{binary_path:?} already exists and passes hash check, skipping download"
                ));
                continue;
            }
            info(format!(
                "{binary_path:?} failed hash check, attepting to re-download..."
            ))
        } else {
            info(format!("{binary_path:?} doesn't exist, downloading..."));
        }

        let working = make_temporary_directory()?;
        let url_path = PathBuf::from(
            url.strip_prefix("https://")
                .ok_or_else(|| Error::new("invalid url, unable to strip https:// prefix"))?,
        );
        let url_filename = url_path
            .file_name()
            .ok_or_else(|| Error::new("output_file has no filename"))?;
        let download_file_path = working.path.join(url_filename);

        let command = [
            OsStr::new("curl -L -o"),
            quote(&download_file_path).as_os_str(),
            OsStr::new(&url),
        ]
        .join(OsStr::new(" "));
        shell(command, false)?;

        let download_file_hash = zip.as_ref().map(|z| &z.hash).unwrap_or(hash);
        let calculated_hash = hash_sha512(&download_file_path)?;
        if download_file_hash != &calculated_hash {
            return Err(Error::new("sha512 hash check failed"));
        }

        let extension = download_file_path
            .extension()
            .map(|s| s.to_string_lossy().to_string());
        let downloaded_binary_path = if let Some(zip) = zip {
            let command = match extension.as_deref() {
                Some("zip") => [
                    OsStr::new("unzip"),
                    download_file_path.as_os_str(),
                    OsStr::new("-d"),
                    working.path.as_os_str(),
                ]
                .join(OsStr::new(" ")),
                Some("tgz") | Some("gz") => [
                    OsStr::new("tar -xvf"),
                    download_file_path.as_os_str(),
                    OsStr::new("-C"),
                    working.path.as_os_str(),
                ]
                .join(OsStr::new(" ")),
                None => return Err(Error::new("Not a zip file")),
                _ => {
                    return Err(Error::new(format!(
                        "Unsupported zip file {download_file_path:?}"
                    )));
                }
            };
            shell(command, false)?;
            working
                .path
                .join(format_string(&zip.binary_path, &[("version", version)]))
        } else {
            match extension.as_deref() {
                Some("zip") | Some("tgz") | Some("gz") => {
                    return Err(Error::new("zip file, please specify BinaryZip config"));
                }
                _ => {}
            }
            download_file_path
        };
        cp(&downloaded_binary_path, &binary_path)?;
        make_executable(binary_path)?;
    }

    info("Finished downloading binary dependencies");
    Ok(())
}

fn place_empty_version_file() -> Result<()> {
    info("Placing empty version file");

    let version_file_path = &*acp_config::TARGET_VERSION_PATH;
    std::fs::write(version_file_path, "").amend(|e| {
        e.add_context(format!(
            "Error writing empty version file to {version_file_path:?}"
        ))
    })?;
    Ok(())
}

fn main() {
    if let Err(error) = (|| {
        let config = AcpConfig::read().amend(|e| e.add_context("Error reading acp.json config"))?;
        let local_toml = acp_config::LocalToml::read()
            .amend(|e| e.add_context("Error reading LOCAL.toml config"))?;
        install_dependencies().amend(|e| e.add_context("Error installing dependencies"))?;
        configure_git().amend(|e| e.add_context("Error configuring git repo"))?;
        download_binaries(&config).amend(|e| e.add_context("Error downloading binaries"))?;
        configure_cargo(&config, local_toml.is_low_mem())
            .amend(|e| e.add_context("Error configuring cargo"))?;
        build_acp().amend(|e| e.add_context("Error building acp"))?;
        run_acp().amend(|e| e.add_context("Error running acp"))?;
        place_empty_version_file().amend(|e| e.add_context("Error placing empty version file"))?;
        Result::Ok(())
    })() {
        eprintln!("{error}");
        std::process::exit(1);
    }
}

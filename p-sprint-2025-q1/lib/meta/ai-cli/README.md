# AI CLI Sandboxed Environment

This directory contains the necessary files to build and run multiple AI command-line interface (CLI) tools in a secure, sandboxed Docker container.

## Overview

The environment is designed with security and user-friendliness in mind. It solves common issues with running development tools inside Docker by:

1. **Matching Host User:** The Docker image is built with a user that has the same username, <PERSON><PERSON>, and G<PERSON> as your host user. This eliminates file permission errors when mounting volumes.
2. **Restricting File Access:** The runner scripts only mount the current working directory into the container. This prevents the agent from accessing your entire home directory or other sensitive files.
3. **Maintaining Your Environment:** Your Rust and Node.js tools are installed and configured for the user inside the container, so the development environment remains consistent.

## Supported Tools

- Claude (`claude`)
- <PERSON> (`gemini`)

## Build Instructions

To build the Docker image, simply run the `build` script from this directory:

```bash
./build
```

This script will pass your user's name, UID, and GID to the Docker build process, ensuring the container's user matches your own. The resulting image will be tagged as `ai-cli-<your_uid>` to ensure it is unique to your user.

## Usage

To run an AI CLI tool, use its corresponding runner script (`claude` or `gemini`). You can run it from any directory you want the agent to have access to.

**Claude Example:**

```bash
./path/to/lib/meta/ai-cli/claude "What is this project about?"
```

**Gemini Example:**

```bash
./path/to/lib/meta/ai-cli/gemini "Translate 'hello world' to Spanish."
```

## Running Multiple Instances

The sandboxed nature of this setup makes it easy to run multiple, isolated instances of the agents. Since the runner scripts only mount the current working directory, you can run multiple agents simultaneously by simply running the scripts from different directories.

**Example:**

**Terminal 1 (Claude agent for `project-a`):**

```bash
cd /path/to/project-a
/path/to/lib/meta/ai-cli/claude "Refactor the main function."
```

**Terminal 2 (Gemini agent for `project-b`):**

```bash
cd /path/to/project-b
/path/to/lib/meta/ai-cli/gemini "Write a test for the new API endpoint."
```

Each instance will have its own isolated filesystem view, limited to the directory from which it was launched.

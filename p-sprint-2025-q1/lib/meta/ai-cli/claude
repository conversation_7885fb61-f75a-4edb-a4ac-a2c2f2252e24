#!/bin/bash

# The container now runs as a user with the same UID/GID as the host user.
# We only mount the current working directory into the container for security.

# Get the absolute path of the current working directory
CWD=$(pwd)
USER_ID=$(id -u)

# Run the container, mounting only the current directory
docker run -it --rm \
    -e RUSTUP_HOME="$RUSTUP_HOME" \
    -e CARGO_HOME="$CARGO_HOME" \
    -e NPM_CONFIG_PREFIX="$NPM_CONFIG_PREFIX" \
    -e PATH="$PATH" \
    -v "$HOME:$HOME" \
    -v "$CWD:$CWD" \
    -w "$CWD" \
    ai-cli-$USER_ID \
    claude "$@"

FROM ubuntu:24.04

# 1. Set build-time arguments for user, user ID, group ID, and home directory
ARG USER_NAME
ARG USER_ID
ARG GROUP_ID
ARG HOME_DIR

# 2. Install base dependencies as root
ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    bash \
    curl \
    vim \
    git \
    python3 \
    build-essential \
    git-lfs \
    rsync \
    net-tools \
    npm \
    zlib1g-dev \
    clang && \
    rm -rf /var/lib/apt/lists/*

# Install uv, the fast Python package installer
RUN curl -LsSf https://astral.sh/uv/install.sh | sh

# 3. Create a non-root user with specified username, UID/GID and give it sudo privileges
RUN groupadd --gid $GROUP_ID -o $USER_NAME && \
    useradd --uid $USER_ID --gid $GROUP_ID -m -s /bin/bash -o -d $HOME_DIR $USER_NAME

# Install claude code and gemini
RUN npm install -g @anthropic-ai/claude-code
RUN npm install -g @google/gemini-cli

# 4. Switch to the new user
USER $USER_NAME
WORKDIR $HOME_DIR
ENV HOME=$HOME_DIR
ENV USER=$USER_NAME


/// Identify all of the module crates in use, scan for files, and process them into a mapping of
/// crate -> parsed hcl
///
/// The hcl files are in the directory ./pgmig of each crate, and they all have the `.hcl`
/// extension.  
///
/// Uses a walker that respects .gitignore files.
pub fn process(workspace: &'static crate::Workspace) {
    // Feed all the apps, crates, and files into the a builder
    let input_workspace = read_workspace(workspace);
    let _pgmig_workspace = input_workspace.process();
    //println!("{:#?}", _pgmig_workspace);
}

/// For a given workspace, read all the crates, and also produce a list of all the apps that
/// reference them, returning a pgmig::Workspace
fn read_workspace(workspace: &'static crate::Workspace) -> pgmig::InputWorkspace {
    // input_workspace is used to ingest all apps, crates, and files
    let mut input_workspace = pgmig::InputWorkspace::default();

    // iterate over each app
    for app_ref in workspace.get_build_apps() {
        let app_name = app_ref.crate_name.0.clone();
        let mut app_extended = Vec::new();

        // iterate over each crate this app extends
        for crate_ref in app_ref.get_extended_crates(workspace) {
            let crate_name = &crate_ref.crate_name.0;

            // Record this crate into the app_extended list
            app_extended.push(crate_name.clone());

            // Process the crate only if not already processed by another app
            if !input_workspace.has_crate(crate_name) {
                let pgmig_path = crate_ref.abs_path.join("pgmig");
                let mut paths = Vec::new();

                if pgmig_path.exists() {
                    let walker = ignore::WalkBuilder::new(&pgmig_path)
                        .standard_filters(true)
                        .build();

                    for entry in walker.filter_map(Result::ok) {
                        let path = entry.path();

                        if path.is_file() && path.extension().is_some_and(|ext| ext == "hcl") {
                            paths.push(path.to_path_buf());
                        }
                    }
                }

                // add the crate to the workspace
                input_workspace.add_crate(crate_name.clone(), crate_ref.abs_path.clone(), paths);
            }
        }

        // add the app to the workspace
        input_workspace.add_app(app_name, app_ref.abs_path.clone(), app_extended);
    }

    input_workspace
}

use indexmap::IndexMap;
use serde::Deserialize;
use serde::de::Error as DeError;
use serde::de::{Deserializer, MapAccess, Visitor};
use std::fmt;

// create a struct for member Cargo.toml
#[derive(Debug, Deserialize)]
pub(crate) struct CrateCargoToml {
    pub(crate) package: CrateCargoTomlPackage,

    /// indexmap of dependencies
    #[serde(default, deserialize_with = "deserialize_dependencies")]
    pub(crate) dependencies: IndexMap<String, CrateDependency>,
}

#[derive(Debug, Deserialize)]
pub(crate) struct CrateCargoTomlPackage {
    pub(crate) name: String,
    pub(crate) version: String,
    pub(crate) edition: String,

    #[serde(default)]
    pub(crate) metadata: CrateCargoTomlPackageMetadata,
}

#[derive(Debug, Deserialize, Default)]
pub(crate) struct CrateCargoTomlPackageMetadata {
    #[serde(default)]
    pub(crate) approck: CrateCargoTomlPackageMetadataApprock,

    #[serde(default)]
    pub(crate) acp: CrateCargoTomlPackageMetadataApprock,
}

#[derive(Debug, Deserialize, Default)]
pub(crate) struct CrateCargoTomlPackageMetadataApprock {
    #[serde(default, deserialize_with = "deserialize_optional_app")]
    pub(crate) app: Option<CrateCargoTomlPackageMetadataApprockApp>,

    #[serde(default, rename = "mod", deserialize_with = "deserialize_optional_module")]
    pub(crate) module: Option<CrateCargoTomlPackageMetadataApprockMod>,

    #[serde(default)]
    pub(crate) extends: Vec<String>,
}

#[derive(Debug, Deserialize, Default, Clone)]
pub(crate) struct CrateCargoTomlPackageMetadataApprockApp {
    #[serde(default)]
    pub(crate) port: u16,

    #[serde(default)]
    pub(crate) docmap: IndexMap<String, IndexMap<String, String>>,

    #[serde(default)]
    pub (crate) extends: Vec<String>,
}

#[derive(Debug, Deserialize, Default, Clone)]
pub(crate) struct CrateCargoTomlPackageMetadataApprockMod {
    #[serde(default)]
    pub (crate) extends: Vec<String>,
}

#[derive(Debug, Deserialize, Default)]
pub(crate) struct CrateDependency {
    #[serde(default)]
    pub(crate) workspace: bool,
    pub(crate) version: Option<String>,
    pub(crate) path: Option<String>,
}

// Custom deserializer for the dependencies field to handle varying data formats
fn deserialize_dependencies<'de, D>(
    deserializer: D,
) -> Result<IndexMap<String, CrateDependency>, D::Error>
where
    D: Deserializer<'de>,
{
    struct DependenciesVisitor;

    impl<'de> Visitor<'de> for DependenciesVisitor {
        type Value = IndexMap<String, CrateDependency>;

        fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
            formatter.write_str(
                "a string \"version\", or a table with one or more of {version, workspace, path}",
            )
        }

        fn visit_map<M>(self, mut access: M) -> Result<Self::Value, M::Error>
        where
            M: MapAccess<'de>,
        {
            let mut rval = IndexMap::new();

            while let Some((key, val)) = access.next_entry::<String, toml::Value>()? {
                match val {
                    toml::Value::String(s) => {
                        rval.insert(
                            key,
                            CrateDependency {
                                version: Some(s),
                                ..Default::default()
                            },
                        );
                    }
                    toml::Value::Table(t) => {
                        rval.insert(key, t.try_into().map_err(M::Error::custom)?);
                    }
                    _ => return Err(M::Error::custom("expected a string or a table")),
                }
            }

            Ok(rval)
        }
    }

    deserializer.deserialize_map(DependenciesVisitor)
}

// Custom deserializer for optional app that treats empty tables as Some(default)
fn deserialize_optional_app<'de, D>(
    deserializer: D,
) -> Result<Option<CrateCargoTomlPackageMetadataApprockApp>, D::Error>
where
    D: Deserializer<'de>,
{
    let value: Option<toml::Value> = Option::deserialize(deserializer)?;
    match value {
        Some(toml::Value::Table(_)) => {
            // If we have a table (even empty), deserialize it as Some(struct)
            let app: CrateCargoTomlPackageMetadataApprockApp = value.unwrap().try_into().map_err(serde::de::Error::custom)?;
            Ok(Some(app))
        }
        Some(_) => Err(serde::de::Error::custom("app must be a table")),
        None => Ok(None),
    }
}

// Custom deserializer for optional module that treats empty tables as Some(default)
fn deserialize_optional_module<'de, D>(
    deserializer: D,
) -> Result<Option<CrateCargoTomlPackageMetadataApprockMod>, D::Error>
where
    D: Deserializer<'de>,
{
    let value: Option<toml::Value> = Option::deserialize(deserializer)?;
    match value {
        Some(toml::Value::Table(_)) => {
            // If we have a table (even empty), deserialize it as Some(struct)
            let module: CrateCargoTomlPackageMetadataApprockMod = value.unwrap().try_into().map_err(serde::de::Error::custom)?;
            Ok(Some(module))
        }
        Some(_) => Err(serde::de::Error::custom("module must be a table")),
        None => Ok(None),
    }
}

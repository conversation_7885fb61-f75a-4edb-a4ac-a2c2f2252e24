use std::fmt::Display;

pub enum VError {
    Static(&'static str),
    Dynamic(String),
    Dependency,
}

impl From<&'static str> for VError {
    fn from(value: &'static str) -> Self {
        VError::Static(value)
    }
}
impl From<String> for VError {
    fn from(value: String) -> Self {
        VError::Dynamic(value)
    }
}

pub trait VNumber {
    fn v_min(self, min: Self) -> Result<Self, VError>
    where
        Self: Sized + PartialOrd + Display,
    {
        if self < min {
            Err(VError::Dynamic(format!("must not be less than {min}")))
        } else {
            Ok(self)
        }
    }
    fn v_max(self, max: Self) -> Result<Self, VError>
    where
        Self: Sized + PartialOrd + Display,
    {
        if self > max {
            Err(VError::Dynamic(format!("must not be more than {max}")))
        } else {
            Ok(self)
        }
    }
    fn v_zero_plus(self) -> Result<Self, VError>
    where
        Self: Sized + PartialOrd + Display + From<u8>,
    {
        if self < 0.into() {
            Err(VError::Dynamic("must be 0 or greater".to_string()))
        } else {
            Ok(self)
        }
    }
    fn v_one_plus(self) -> Result<Self, VError>
    where
        Self: Sized + PartialOrd + Display + From<u8>,
    {
        if self < 1.into() {
            Err(VError::Dynamic("must be 1 or greater".to_string()))
        } else {
            Ok(self)
        }
    }
}

pub trait VString {
    fn v_trim(self) -> Result<Self, VError>
    where
        Self: Sized;
    fn v_max(self, max: usize) -> Result<Self, String>
    where
        Self: Sized;
    fn v_min(self, min: usize) -> Result<Self, VError>
    where
        Self: Sized;
}

pub trait VOption<T> {
    fn v_some(self) -> Result<Option<T>, VError>
    where
        Self: Sized;
    fn v_none(self) -> Result<Option<T>, VError>
    where
        Self: Sized;
    fn v_unwrap(self) -> Result<T, VError>
    where
        Self: Sized;
}

impl VNumber for u8 {}
impl VNumber for u16 {}
impl VNumber for u32 {}
impl VNumber for u64 {}
impl VNumber for usize {}
impl VNumber for i8 {}
impl VNumber for i16 {}
impl VNumber for i32 {}
impl VNumber for i64 {}
impl VNumber for crate::Decimal {}

impl VString for String {
    fn v_trim(self) -> Result<Self, VError> {
        Ok(self.trim().to_string())
    }
    fn v_max(self, max: usize) -> Result<Self, String> {
        if self.len() > max {
            Err(format!(
                "must not exceed {max} {characters}",
                characters = if max == 1 { "character" } else { "characters." }
            ))
        } else {
            Ok(self)
        }
    }
    fn v_min(self, min: usize) -> Result<Self, VError> {
        if self.len() < min {
            Err(VError::Dynamic(format!(
                "must be at least {min} {characters}",
                characters = if min == 1 { "character" } else { "characters." }
            )))
        } else {
            Ok(self)
        }
    }
}

impl VString for Option<String> {
    fn v_trim(self) -> Result<Self, VError> {
        match self {
            Some(v) => Ok(Some(v.v_trim()?)),
            None => Ok(None),
        }
    }
    fn v_max(self, max: usize) -> Result<Self, String> {
        match self {
            Some(v) => Ok(Some(v.v_max(max)?)),
            None => Ok(None),
        }
    }
    fn v_min(self, min: usize) -> Result<Self, VError> {
        match self {
            Some(v) => Ok(Some(v.v_min(min)?)),
            None => Ok(None),
        }
    }
}

impl<T> VOption<T> for Option<T> {
    fn v_some(self) -> Result<Option<T>, VError> {
        match self {
            Some(v) => Ok(Some(v)),
            None => Err(VError::Static("Value is required")),
        }
    }
    fn v_none(self) -> Result<Option<T>, VError> {
        match self {
            Some(_) => Err(VError::Static("Value is not allowed")),
            None => Ok(None),
        }
    }
    fn v_unwrap(self) -> Result<T, VError> {
        match self {
            Some(v) => Ok(v),
            None => Err(VError::Static("Value is required")),
        }
    }
}

/// <reference lib="deno.ns" />
/// <reference lib="deno.window" />

import { IpAddr_decode, IpAddr_encode, IpAddr_validate } from "../src/gtype.mts";

Deno.test("IpAddr_validate valid IPv4", () => {
    const result = IpAddr_validate("***********");
    console.assert("Ok" in result && result.Ok === "***********", "Valid IPv4 failed");
});

Deno.test("IpAddr_validate valid IPv6", () => {
    const result = IpAddr_validate("::1");
    console.assert("Ok" in result && result.Ok === "::1", "Valid IPv6 failed");
    const result2 = IpAddr_validate("2001:0db8:85a3:0000:0000:8a2e:0370:7334");
    console.assert(
        "Ok" in result2 && result2.Ok === "2001:0db8:85a3:0000:0000:8a2e:0370:7334",
        "Valid IPv6 long failed",
    );
});

Deno.test("IpAddr_validate invalid IP", () => {
    const result = IpAddr_validate("invalid-ip");
    console.assert("Err" in result, "Invalid IP did not return error");
});

Deno.test("IpAddr_validate undefined value", () => {
    const result = IpAddr_validate(undefined);
    console.assert("Err" in result, "Undefined value did not return error");
});

Deno.test("IpAddr_encode", () => {
    const ip = "***********";
    const encoded = IpAddr_encode(ip);
    console.assert(encoded === ip, "Encode failed");
});

Deno.test("IpAddr_decode valid", () => {
    const ip = "***********";
    const result = IpAddr_decode(ip);
    console.assert("Ok" in result && result.Ok === ip, "Decode valid failed");
});

Deno.test("IpAddr_decode invalid", () => {
    const result = IpAddr_decode("invalid-ip");
    console.assert("Err" in result, "Decode invalid did not return error");
});

Deno.test("IpAddr_decode wrong type", () => {
    const result = IpAddr_decode(123);
    console.assert("Err" in result, "Decode wrong type did not return error");
});

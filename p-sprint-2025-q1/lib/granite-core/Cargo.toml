[package]
name = "granite-core"
version = "0.1.0"
edition = "2024"

[package.metadata.approck.mod]
extends = []

[dependencies]
base64_light.workspace = true
chrono.workspace = true
http.workspace = true
rand.workspace = true
sha2.workspace = true
serde.workspace = true
serde_json.workspace = true
url.workspace = true
num-bigint.workspace = true
rust_decimal = { workspace = true, features = ["db-tokio-postgres"] }
rust_decimal_macros.workspace = true
indexmap.workspace = true
uuid = {workspace = true, features = ["v4", "v7"]}
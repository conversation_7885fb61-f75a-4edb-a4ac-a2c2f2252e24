extern crate proc_macro;

use quote::quote;
use syn::{Data, DeriveInput, parse_macro_input};

use proc_macro::TokenStream;

#[proc_macro]
pub fn pg_execute(input: TokenStream) -> TokenStream {
    match granite_compiler::postgres::QueryBundle::new(input.into()) {
        Ok(query_bundle) => query_bundle.pg_execute().into(),
        Err(e) => e.get_diagnostic().emit_as_item_tokens().into(),
    }
}

#[proc_macro]
pub fn pg_value(input: TokenStream) -> TokenStream {
    match granite_compiler::postgres::QueryBundle::new(input.into()) {
        Ok(query_bundle) => query_bundle.pg_value().into(),
        Err(e) => e.get_diagnostic().emit_as_item_tokens().into(),
    }
}

#[proc_macro]
pub fn pg_value_vec(input: TokenStream) -> TokenStream {
    match granite_compiler::postgres::QueryBundle::new(input.into()) {
        Ok(query_bundle) => query_bundle.pg_value_vec().into(),
        Err(e) => e.get_diagnostic().emit_as_item_tokens().into(),
    }
}

#[proc_macro]
pub fn pg_row(input: TokenStream) -> TokenStream {
    match granite_compiler::postgres::QueryBundle::new(input.into()) {
        Ok(query_bundle) => query_bundle.pg_row().into(),
        Err(e) => e.get_diagnostic().emit_as_item_tokens().into(),
    }
}

#[proc_macro]
pub fn pg_row_option(input: TokenStream) -> TokenStream {
    match granite_compiler::postgres::QueryBundle::new(input.into()) {
        Ok(query_bundle) => query_bundle.pg_row_option().into(),
        Err(e) => e.get_diagnostic().emit_as_item_tokens().into(),
    }
}

#[proc_macro]
pub fn pg_row_vec(input: TokenStream) -> TokenStream {
    match granite_compiler::postgres::QueryBundle::new(input.into()) {
        Ok(query_bundle) => query_bundle.pg_row_vec().into(),
        Err(e) => e.get_diagnostic().emit_as_item_tokens().into(),
    }
}

/// `granite::gtype` is a proc macro which will modify the following struct or enum into standard
/// types, and optionally derive serialization and deserialization code.
///
/// The derive line has this form `#[granite::gtype(...)]` with these optional fields.  
///     RsType
///
/// Valid gtypes:
/// * `i32`, `i64`, `i128`, `u32`, `u64`, `u128`, `f32`, `f64`, `bool`, `char`, `String`
/// * `DateUtc`, `DateTz`, `Decimal`, `Integer`, `BigInt`
/// * `Option<T>`, `Result<T, E>`, `Vec<T>`, `HashSet<T>`, `HashMap<K, V>`, `BTreeMap<K, V>`, `IndexMap<K, V>`
#[proc_macro_attribute]
pub fn gtype(attr: TokenStream, item: TokenStream) -> TokenStream {
    // parse attr as a comma delimited list of token streams
    let item = syn::parse_macro_input!(item as syn::Item);

    match granite_compiler::gtype::macros::gtype(attr.into(), item) {
        Ok(token_stream) => token_stream.into(),

        // Once diagnostic is extracted, emit as ITEM (vs attr) tokens
        Err(e) => e.get_diagnostic().emit_as_item_tokens().into(),
    }
}

/// `granite::FmtDisplay` is a derive macro which will implement the `std::fmt::Display` trait for an enum.
/// The implementation will print the variant name of the enum. Only simple enums are currently supported.
#[proc_macro_derive(FmtDisplay)]
pub fn fmt_display_derive(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let name = &input.ident;

    let variants = if let Data::Enum(data_enum) = &input.data {
        data_enum
            .variants
            .iter()
            .map(|v| &v.ident)
            .collect::<Vec<_>>()
    } else {
        panic!("FmtDisplay can only be used with simple enums");
    };

    let display_impl = variants.iter().map(|v| {
        let v_str = v.to_string();
        quote! {
            #name::#v => write!(f, #v_str),
        }
    });

    let expanded = quote! {
        impl std::fmt::Display for #name {
            fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
                match self {
                    #(#display_impl)*
                }
            }
        }
    };

    TokenStream::from(expanded)
}

/// `granite::FromString` is a derive macro which will implement the `From<String>` trait for an enum.
/// The implementation will convert a string to the corresponding enum variant. Only simple enums are currently supported.
#[proc_macro_derive(FromString)]
pub fn from_string_derive(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let name = &input.ident;
    let name_str = name.to_string();

    let variants = if let Data::Enum(data_enum) = &input.data {
        data_enum
            .variants
            .iter()
            .map(|v| &v.ident)
            .collect::<Vec<_>>()
    } else {
        panic!("FromString can only be used with enums");
    };

    let from_string_impl = variants.iter().map(|v| {
        let v_str = v.to_string();
        quote! {
            #v_str => #name::#v,
        }
    });

    let expanded = quote! {
        impl From<String> for #name {
            fn from(status: String) -> Self {
                match status.as_str() {
                    #(#from_string_impl)*
                    _ => panic!("Invalid value for {}", #name_str),
                }
            }
        }
    };

    TokenStream::from(expanded)
}

/// `granite::AsVec` is a derive macro which will implement a method `as_vec` for an enum.
/// The method will return a vector containing string versions of all of the enum variants. Only simple enums are currently supported.
#[proc_macro_derive(AsVec)]
pub fn as_iter_derive(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let name = &input.ident;

    let variants = if let Data::Enum(data_enum) = &input.data {
        data_enum
            .variants
            .iter()
            .map(|v| &v.ident)
            .collect::<Vec<_>>()
    } else {
        panic!("AsVec can only be used with enums");
    };

    let as_vec_impl = variants.iter().map(|v| {
        let v_str = v.to_string();
        quote! {
            #v_str.to_string(),
        }
    });

    let expanded = quote! {
        impl #name {
            pub fn as_vec() -> Vec<String> {
                vec![
                    #(#as_vec_impl)*
                ]
            }
        }
    };

    TokenStream::from(expanded)
}

/// Macro for parsing web paths at compile time
///
/// ```text
/// webpath!(/foo/bar/{id:i32})
/// ```
///
/// * Must start with `/`
/// * May include path captures like `{id:i32}`
/// * Returns a WebPath
#[proc_macro]
pub fn webpath(input: TokenStream) -> TokenStream {
    let input_tokens = input.into();
    match granite_compiler::webpath::parse_macro_input(input_tokens) {
        Ok(expanded) => expanded.into(),
        Err(e) => e.get_diagnostic().emit_as_item_tokens().into(),
    }
}

#[proc_macro]
pub fn gvalidate(input: TokenStream) -> TokenStream {
    let items = parse_macro_input!(input as syn::File).items;

    match granite_compiler::gvalidate::handle_gvalidate_macro(items) {
        Ok(gv) => gv.codegen().into(),

        // Once diagnostic is extracted, emit as ITEM (vs attr) tokens
        Err(e) => e.get_diagnostic().emit_as_item_tokens().into(),
    }
}

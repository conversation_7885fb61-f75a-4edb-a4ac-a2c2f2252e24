[package]
name = "bux"
version = "0.1.0"
edition = "2024"

[package.metadata.approck.mod]
extends = ["approck","granite"]

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
bux-macros = { path = "../bux-macros" }

approck = { workspace = true }
chrono-tz = { workspace = true }
granite = { workspace = true }
http = { workspace = true }
maud = { workspace = true }
num-format = { workspace = true }
num-traits = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }


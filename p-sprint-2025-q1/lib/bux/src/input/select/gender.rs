use crate::{Mark<PERSON>, html};

pub fn gender_select(name: &str, label: &str, selected: Option<&str>) -> Markup {
    let options = [
        ("", "Select Gender"),
        ("male", "Male"),
        ("female", "Female"),
        ("other", "Other"),
        ("prefer_not_to_say", "Prefer not to say"),
    ];
    let options_json = serde_json::to_string(&options).unwrap_or_else(|_| "[]".to_string());

    html! {
        bux-input-select-gender name=(name) options=(options_json) value=(selected.unwrap_or_default()) {
            { (label) }
        }
    }
}

pub fn gender_select_with_help(
    name: &str,
    label: &str,
    selected: Option<&str>,
    help: &str,
) -> Markup {
    let options = [
        ("", "Select Gender"),
        ("male", "Male"),
        ("female", "Female"),
        ("other", "Other"),
        ("prefer_not_to_say", "Prefer not to say"),
    ];
    let options_json = serde_json::to_string(&options).unwrap_or_else(|_| "[]".to_string());

    html! {
        bux-input-select-gender name=(name) options=(options_json) value=(selected.unwrap_or_default()) help=(help) {
            { (label) }
        }
    }
}

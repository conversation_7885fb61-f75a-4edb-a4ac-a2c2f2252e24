import type { Result } from "@granite/lib.mts";
import { NEWID } from "@granite/lib.mts";
import "./mod.mcss";

import { BuxInput } from "../mod.mts";

abstract class BuxInputText<T> extends BuxInput<T, string> {
    protected $label: HTMLLabelElement | null = this.querySelector("label");
    protected $input: HTMLInputElement = this.querySelector("input") as HTMLInputElement;
    protected $help: HTMLElement | null = this.querySelector("div.help-text");

    /// This is required to take a nullable value and produce a string for the $input text
    /// null means empty, not error.
    protected abstract format(value: T | undefined): string;

    /// This is required to take a string from the $input text and produce a nullable value
    /// null means empty, not error.
    protected abstract parse(text: string): Result<T | undefined, string>;

    connectedCallback(): void {
        const id = NEWID();
        this.$input.id = id;
        if (this.$label) {
            this.$label.htmlFor = id;
        }

        this.$input.addEventListener("input", this.event_on_input.bind(this));
        this.$input.addEventListener("change", this.event_on_change.bind(this));
    }

    disconnectedCallback(): void {
        this.$input.removeEventListener("input", this.event_on_input.bind(this));
        this.$input.removeEventListener("change", this.event_on_change.bind(this));
    }

    on_attr_name(value: string): void {
        this.$input.name = value;
    }

    on_attr_value(value: string): void {
        this.set_text(value);
    }

    on_attr_help(value: string | null): void {
        if (this.$help) {
            this.$help.innerText = value ?? "";
        }
    }

    // clear custom messages while typing
    private event_on_input(): void {
        this.set_e(undefined);
    }

    // revalidate once the user has finished typing
    private event_on_change(): void {
        const result = this.get();

        if ("Ok" in result) {
            this.$input.value = this.format(result.Ok);
            this.set_e(undefined);
        } else {
            this.set_e(result.Err);
        }

        if (this.on_change) {
            if ("Ok" in result) {
                this.on_change(result.Ok);
            } else {
                this.on_change(undefined);
            }
        }
    }

    public override get(): Result<T | undefined, string> {
        return this.parse(this.$input.value);
    }

    public override set_p(value: T | undefined): void {
        this.set_e(undefined);
        if (value === null) {
            this.$input.value = "";
        } else {
            this.$input.value = this.format(value);
        }
    }

    public override set_e(
        value:
            | string
            | null
            | undefined
            | { Ok: string | undefined }
            | { Err: string | undefined }
            | { Some: string | undefined }
            | { None: true }
            | { Outer: string }
            | { Inner: any },
    ): void {
        if (typeof value === "object" && value !== null && !Array.isArray(value)) {
            if ("Ok" in value) {
                value = value.Ok;
            } else if ("Err" in value) {
                value = value.Err;
            } else if ("Some" in value) {
                value = value.Some;
            } else if ("None" in value) {
                value = null;
            } else if ("Outer" in value) {
                value = value.Outer;
            }
        }

        if (typeof value === "string") {
            this.$input.setCustomValidity(value);
            this.$input.title = value;
        } else {
            this.$input.setCustomValidity("");
            this.$input.title = "";
        }
    }

    public override get_e(): string | undefined {
        return this.$input.validationMessage || undefined;
    }

    public set_text(text: string | null) {
        this.$input.value = text ?? "";
        this.event_on_change();
    }

    public get_text(): string {
        return this.$input.value;
    }

    public readonly(readonly: boolean): void {
        this.$input.readOnly = readonly;
    }
}

export default BuxInputText;

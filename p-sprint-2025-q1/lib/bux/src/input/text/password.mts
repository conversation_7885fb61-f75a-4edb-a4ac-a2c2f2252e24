import BuxInputTextString from "./string.mts";
import "./password.mcss";
import { SEC } from "@granite/lib.mts";

export class BuxInputTextPassword extends BuxInputTextString {
    private $button: HTMLButtonElement;

    constructor() {
        super();
        this.$input.type = "password";
        this.$button = SEC(HTMLButtonElement, this, "button");
    }

    override connectedCallback(): void {
        super.connectedCallback();
        this.$button.addEventListener("click", this.event_on_click.bind(this));
    }

    override disconnectedCallback(): void {
        super.disconnectedCallback();
        this.$button.removeEventListener("click", this.event_on_click.bind(this));
    }

    private event_on_click(): void {
        if (this.$input.type === "password") {
            this.$input.type = "text";
            this.$button.innerText = "Hide";
        } else {
            this.$input.type = "password";
            this.$button.innerText = "Show";
        }
    }
}

globalThis.customElements.define("bux-input-text-password", BuxInputTextPassword);
export default BuxInputTextPassword;

use crate::{Markup, html};

pub fn bux_input_textarea_string(
    name: &str,
    label: Option<&str>,
    value: Option<&str>,
    help: Option<&str>,
    readonly: bool,
) -> Markup {
    let readonly = if readonly { Some("readonly") } else { None };

    html! {
        bux-input-textarea-string name=(name) {
            @if let Some(label) = label {
                label {
                    { (label) }
                }
            }
            textarea name=(name) readonly=[readonly] {
                (value.unwrap_or_default())
            }
            @if let Some(help) = help {
                div.help-text {
                    { (help) }
                }
            }
        }
    }
}

pub fn name_label_value(name: &str, label: &str, value: Option<&str>) -> Markup {
    bux_input_textarea_string(name, Some(label), value, None, false)
}

pub fn name_label_value_help(name: &str, label: &str, value: Option<&str>, help: &str) -> Markup {
    bux_input_textarea_string(name, Some(label), value, Some(help), false)
}

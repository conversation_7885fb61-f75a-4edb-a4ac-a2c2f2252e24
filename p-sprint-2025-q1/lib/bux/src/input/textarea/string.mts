import { type Option, type Result } from "@granite/lib.mts";
import BuxInputTextarea from "./mod.mts";
import "./string.mcss";

class BuxInputTextareaString extends BuxInputTextarea<string | undefined> {
    /// return a valid value or throw a new Error
    protected override parse(v: string): Result<string | undefined, string> {
        let value = v;

        if (!this.attr_notrim) {
            value = value.trim();
        }

        return { Ok: value };
    }

    protected override format(value: string | undefined): string {
        return value ?? "";
    }

    static override get observedAttributes(): string[] {
        return BuxInputTextarea.observedAttributes.concat(["notrim"]);
    }

    get attr_notrim(): boolean {
        return this.hasAttribute("notrim");
    }

    public get value_option(): Option<string> {
        const result = this.get();
        if ("Ok" in result && result.Ok !== undefined) {
            return { Some: result.Ok };
        }
        return { None: true };
    }

    public set value_option(value: Option<string>) {
        this.value = value;
    }
}

globalThis.customElements.define("bux-input-textarea-string", BuxInputTextareaString);
export default BuxInputTextareaString;

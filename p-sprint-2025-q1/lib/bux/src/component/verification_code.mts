import "./verification_code.mcss";
import { SE_nullable } from "@granite/lib.mts";

export type VerificationCodeEvent = {
    code: string;
    type: "Phone" | "Email";
};

export class BuxComponentVerificationCode extends HTMLElement {
    // Can function with input rendered or not rendered
    protected $input: HTMLInputElement | null = null;

    protected code_length: number;
    protected contact_type: "Phone" | "Email";

    constructor() {
        super();
        this.$input = SE_nullable(this, ".x-code");

        {
            const code_length = this.getAttribute("code-length");
            if (code_length === null) {
                throw new Error("Missing code-length attribute");
            }
            this.code_length = parseInt(code_length);
        }

        switch (this.getAttribute("contact-type")) {
            case "Phone":
                this.contact_type = "Phone";
                break;
            case "Email":
                this.contact_type = "Email";
                break;
            default:
                throw new Error(`Invalid contact type: ${this.getAttribute("contact-type")}`);
        }
    }

    connectedCallback(): void {
        if (this.$input) {
            this.$input.addEventListener("input", this.on_input.bind(this));

            // Only focus phone inputs by default
            if (this.contact_type === "Phone") {
                this.$input.focus();
            }
        }

        // Add click handler for resend links
        this.addEventListener("click", this.on_click.bind(this));
    }

    disconnectedCallback(): void {
        if (this.$input) {
            this.$input.removeEventListener("input", this.on_input.bind(this));
        }
        this.removeEventListener("click", this.on_click.bind(this));
    }

    private on_input(): void {
        if (this.$input) {
            this.$input.value = this.$input.value.replace(/\D/g, "");

            if (this.$input.value.length === this.code_length) {
                // Dispatch custom event that bubbles up
                const event = new CustomEvent("on_code_entered", {
                    detail: {
                        code: this.$input.value,
                        type: this.contact_type,
                    },
                    bubbles: true,
                });
                this.dispatchEvent(event);
            }
        }
    }

    private on_click(event: Event): void {
        const $target = event.target as HTMLElement;

        // Check if clicked element is a resend link by checking if parent has x-resend-wrapper class
        if (
            $target.tagName === "A" && $target.parentElement?.classList.contains("x-resend-wrapper")
        ) {
            event.preventDefault();

            // Dispatch custom resend event based on contact type
            const event_name = this.contact_type === "Phone"
                ? "PhoneResendRequested"
                : "EmailResendRequested";
            const resend_event = new CustomEvent(event_name, {
                bubbles: true,
            });
            this.dispatchEvent(resend_event);
        }
    }
}

globalThis.customElements.define("bux-component-verification-code", BuxComponentVerificationCode);

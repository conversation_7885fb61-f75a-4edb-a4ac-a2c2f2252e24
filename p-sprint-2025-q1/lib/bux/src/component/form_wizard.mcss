/* FormWizard Component CSS - Root selector: form.bux-form-wizard */
/* Modern rectangular step navigation with clean, contemporary styling */
/* Follows bux framework patterns with semantic nested selectors */

:root {
    --fw-enabled-a-color: #084298;
    --fw-complete-a-bg-color: #90ee90;
    --fw-complete-a-bg-image: none;
    --fw-complete-a-border-color: #82d682;
    --fw-complete-a-color: #084298;
}

form.bux-form-wizard {
    /* Header contains step navigation */
    > header {
        margin-bottom: 2rem;
        border-bottom: 1px solid #ddd;
        padding-bottom: 1rem;

        h1 {
            text-align: center;
            font-size: 20pt;
            margin-bottom: 0;
        }

        p {
            text-align: center;
            margin-top: .5rem;
            margin-bottom: 0;
        }

        /* Step navigation list */
        ul {
            display: grid;
            gap: .75rem;
            margin-bottom: 1rem;
            list-style: none;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 0.75rem;

            @media (min-width: 768px) {
                /* Create as many columns as will fit, with a minimum width of 170px and distribute remaining space equally */
                grid-template-columns: repeat(auto-fit, minmax(170px, 1fr));
            }

            /* Individual step items */
            li {

                /* Step link styling */
                a {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: .375rem .75rem;
                    text-decoration: none;
                    border-radius: .375rem;
                    font-weight: 500;
                    font-size: 0.875rem;

                    @media (min-width: 768px) {
                        justify-content: center;
                        white-space: nowrap;
                    }

                    /* Icon/indicator before text */
                    &::before {
                        content: "\f0c8";
                        font-family: "Font Awesome 5 Free";
                        font-weight: 400;
                        font-size: 14pt;
                    }

                }

                /* Disabled step styling (default) */
                a {
                    background: #e9ecef;
                    border: 1px solid #e9ecef;
                    color: #696969;
                    cursor: not-allowed;
                }

                /* Enabled step styling */
                &.x-enabled a {
                    background: #fff;
                    color: var(--fw-enabled-a-color);
                    cursor: pointer;
                    border: 1px solid #d2d4d7;
                }

                /* Complete step styling */
                &.x-complete a {
                    background: var(--fw-complete-a-bg-color);
                    background-image: var(--fw-complete-a-bg-image);
                    border-color: var(--fw-complete-a-border-color);
                    color: var(--fw-complete-a-color);
                    
                    &::before {
                        content: "\f14a";
                        font-family: "Font Awesome 5 Free";
                        font-weight: 400;
                    }
                }

                /* Selected step styling */
                &.x-selected a {
                    background: linear-gradient(0deg, rgba(8, 66, 152, 1) 0%, rgba(33, 85, 162, 1) 50%, rgba(57, 104, 173, 1) 100%);
                    color: white;
                    border: 1px solid #084298;

                    &::before {
                        color: white;
                    }
                }

                /* Error state styling */
                &.x-error a {
                    background: lightpink;
                    color: #000;
                    border: 1px solid #dc3545;

                    &::before {
                        color: #dc3545;
                    }

                }
            }
        }

        /* Reset counter for step numbering */
        counter-reset: step-counter;

        /* Horizontal rule separator */
        hr {
            margin-top: 1.5rem;
            border: none;
            border-top: 1px solid #e9ecef;
        }
    }

    /* Error message container */
    > error {
        display: block;
        color: #dc3545;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin-bottom: 1rem;

        &:empty {
            display: none;
        }
    }

    /* Main content area */
    > content {
        display: block;

        /* Data list styling for key-value pairs */
        dl {
            display: grid;
            grid-template-columns: auto 1fr;
            gap: 0.5rem 1rem;
            margin-bottom: 1.5rem;

            dt {
                font-weight: 600;
                color: #495057;
            }

            dd {
                margin: 0;
                color: #6c757d;
            }
        }
    }

    > div.x-disabled-content {
            text-align: center;
            font-size: 16pt;
            display: block;
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 0.25rem;
            padding: 0.75rem;
            margin-bottom: 1rem;
    }

    /* Footer with form buttons */
    > footer {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        padding-top: 1rem;
        margin-top: 2rem;
        border-top: 1px solid #ddd;

        /* Ensure buttons are properly spaced */
        > * {
            flex-shrink: 0;
        }
    }
}

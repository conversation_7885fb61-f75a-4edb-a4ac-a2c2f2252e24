bux-accordion {
    header {
        text-align: center;
        margin-bottom: 1.5rem;

        h1 {
            font-size: 2rem;
            color: #333;
            margin-bottom: 0.5rem;
        }

        p {
            color: #666;
            font-size: 1.1rem;
        }
    }
    
    custom-accordion {
        details {
            border: 1px solid #ddd;
            margin-bottom: 1rem;
            border-radius: 5px;
            overflow: hidden;
        }

        details[open] {
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        summary.title-accordion {
            cursor: pointer;
            background-color: #f8f9fa;
            padding: 1rem;
            font-weight: bold;
            display: block;
            outline: none;
            user-select: none;
        }

        summary.title-accordion:hover {
            background-color: #e9ecef;
        }

        body-accordion {
            padding: 1rem;
            background-color: #fff;
            display: none;
        }

        details[open] body-accordion {
            display: block;
        }
    }
}

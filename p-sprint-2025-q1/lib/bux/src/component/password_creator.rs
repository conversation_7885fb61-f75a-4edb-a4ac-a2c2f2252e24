use crate::{Mark<PERSON>, html};

pub fn password_creator() -> Mark<PERSON> {
    password_creator_with_title("Create Password")
}

pub fn password_creator_with_title(title: &str) -> Markup {
    html! {
        bux-component-password-creator {
            div.x-title { (title) }

            section {
                output role="status" aria-live="polite" {}
                meter max="100" value="0" {}
                (crate::input::text::password::new_name_label("password", "Password"))
                (crate::input::text::password::new_name_label("confirm_password", "Confirm Password"))
            }
        }
    }
}
pub fn password_creator_without_title() -> Markup {
    html! {
        bux-component-password-creator {
            section {
                output role="status" aria-live="polite" {}
                meter max="100" value="0" {}
                (crate::input::text::password::new_name_label("password", "Password"))
                (crate::input::text::password::new_name_label("confirm_password", "Confirm Password"))
            }
        }
    }
}

import "./form_wizard.mcss";

import { type GTypeDecode, type GTypeValidate, type NestedError, SE } from "@granite/lib.mts";

type Response<OUTPUT, INPUT_ERROR> =
    | { Output: [OUTPUT] }
    | { ValidationError: [NestedError<INPUT_ERROR>] }
    | { AuthorizationError: [string] }
    | { BadRequest: [string] }
    | { Error: [string] };

export class FormWizard<
    INPUT,
    INPUT_PARTIAL,
    INPUT_ERROR,
    OUTPUT,
    RESPONSE extends Response<OUTPUT, INPUT_ERROR>,
> {
    $form: HTMLFormElement;
    $error: HTMLElement;
    Input_Partial_validate: GTypeValidate<INPUT, INPUT_PARTIAL, NestedError<INPUT_ERROR>>;
    Input_validate: GTypeValidate<INPUT, INPUT, NestedError<INPUT_ERROR>>;
    call: (value: INPUT) => Promise<RESPONSE>;

    err: (errors: INPUT_ERROR) => void;
    get: () => INPUT_PARTIAL;
    set: (value: INPUT_PARTIAL) => void;
    out: (value: OUTPUT) => void;

    constructor(opts: {
        $form: HTMLFormElement;
        api: {
            call: (value: INPUT) => Promise<RESPONSE>;
            Input_validate: GTypeValidate<INPUT, INPUT, NestedError<INPUT_ERROR>>;
            Input_Partial_validate: GTypeValidate<INPUT, INPUT_PARTIAL, NestedError<INPUT_ERROR>>;

            // This is to infer OUTPUT type only
            Output_decode: GTypeDecode<OUTPUT>;
        };
        err: (erros: INPUT_ERROR) => void;
        get: () => INPUT_PARTIAL;
        set: (value: INPUT_PARTIAL) => void;
        out: (value: OUTPUT) => void;
        on_cancel?: (default_url: string) => void;
    }) {
        this.$form = opts.$form;
        this.$error = this.$form.querySelector("error") as HTMLElement;
        this.Input_validate = opts.api.Input_validate;
        this.Input_Partial_validate = opts.api.Input_Partial_validate;
        this.call = opts.api.call;
        this.err = opts.err;
        this.get = opts.get;
        this.set = opts.set;
        this.out = opts.out;

        // if on_cancel was passed, call it instead with a string of the href
        // of the cancel button and let it do it's thing.
        const on_cancel = opts.on_cancel;
        if (on_cancel !== undefined) {
            const $cancel: HTMLAnchorElement = SE(this.$form, "footer a.bux-button-cancel");
            const href = $cancel.href;
            $cancel.addEventListener("click", (event) => {
                event.preventDefault();
                on_cancel(href);
            });
        }

        this.$form.addEventListener("submit", (event) => {
            event.preventDefault();
            this.on_submit();
        });
    }

    protected async on_submit() {
        this.$error.textContent = "";

        const input_result = this.Input_Partial_validate(this.get());
        if ("Err" in input_result) {
            if ("Outer" in input_result.Err) {
                this.$error.textContent = input_result.Err.Outer;
            }
            if ("Inner" in input_result.Err) {
                this.err(input_result.Err.Inner);
            }
            this.$form.reportValidity();
            return;
        }

        const call_response = await this.call(input_result.Ok);
        if ("Output" in call_response) {
            this.out(call_response.Output[0]);
            return;
        }
        if ("ValidationError" in call_response) {
            const e = call_response.ValidationError[0];
            if ("Outer" in e) {
                this.$error.textContent = e.Outer;
            }
            if ("Inner" in e) {
                this.err(e.Inner);
            }
            this.$form.reportValidity();
            return;
        }
        if ("AuthorizationError" in call_response) {
            this.$error.textContent = `Authorization Error: ${call_response.AuthorizationError[0]}`;
            return;
        }
        if ("BadRequest" in call_response) {
            this.$error.textContent = `Bad Request: ${call_response.BadRequest[0]}`;
            return;
        }
        if ("Error" in call_response) {
            this.$error.textContent = `Unexpected Error: ${call_response.Error[0]}`;
            call_response.Error[0];
            return;
        }
    }
}

export default FormWizard;

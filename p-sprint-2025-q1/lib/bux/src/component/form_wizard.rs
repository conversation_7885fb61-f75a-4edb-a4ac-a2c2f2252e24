use maud::{<PERSON><PERSON>, <PERSON><PERSON>, html};

pub fn new<T: FormWizardImpl>(
    current_step: T,
    context: T::Context,
) -> granite::Result<FormWizard<T>> {
    let form_wizard = FormWizard {
        current_step,
        context,
        id: None,
        method: None,
        auto_complete: None,
        hidden_list: Vec::new(),
        next_label: "Continue".to_string(),
        next_icon: "fas fa-arrow-right".to_string(),
        next_class: "primary".to_string(),
        next_href: None,
        extra_button: None,
        back_label: "Back".to_string(),
        back_icon: "fas fa-arrow-left".to_string(),
        back_class: "secondary".to_string(),
        body: Vec::new(),
        heading: None,
        description: None,
    };

    Ok(form_wizard)
}

pub trait FormWizardImpl: PartialEq
where
    Self: Sized,
{
    type Context;
    fn all_variants() -> Vec<Self>;

    fn visible_variants(ctx: &Self::Context) -> Vec<Self> {
        Self::all_variants()
            .into_iter()
            .filter(|variant| {
                matches!(
                    variant.step(ctx),
                    FormWizardStep::Enabled { .. } | FormWizardStep::Disabled { .. }
                )
            })
            .collect()
    }

    fn step(&self, ctx: &Self::Context) -> FormWizardStep;
    fn error_message(&self, _step: &Self, _ctx: &Self::Context) -> Option<String> {
        None
    }
    fn href(&self, ctx: &Self::Context) -> String;

    /// Navigate to the next step in the wizard.
    fn next(&self, ctx: &Self::Context) -> Option<Self> {
        let mut iter = Self::visible_variants(ctx).into_iter();
        while let Some(step) = iter.next() {
            if step == *self {
                return iter.next();
            }
        }
        None
    }

    /// Navigate to the previous step in the wizard.
    fn prev(&self, ctx: &Self::Context) -> Option<Self> {
        let mut iter = Self::visible_variants(ctx).into_iter().rev();
        while let Some(step) = iter.next() {
            if step == *self {
                return iter.next();
            }
        }
        None
    }

    // Find the best step to redirect to if the user is found on a disabled step
    fn best_redirect(&self, ctx: &Self::Context) -> Option<Self> {
        Self::visible_variants(ctx)
            .into_iter()
            // Find the first enabled and incomplete step
            .find(|variant| match variant.step(ctx) {
                FormWizardStep::Enabled { complete, .. } => !complete,
                _ => false,
            })
            // If there are no enabled and incomplete steps, find the first enabled step
            .or_else(|| {
                Self::visible_variants(ctx)
                    .into_iter()
                    .find(|variant| matches!(variant.step(ctx), FormWizardStep::Enabled { .. }))
            })
        // Otherwise, None is returned
    }
}

pub enum FormWizardStep {
    Hidden,
    Disabled { label: String, complete: bool },
    Enabled { label: String, complete: bool },
}

pub struct FormWizard<T: FormWizardImpl> {
    pub current_step: T,
    pub context: T::Context,
    pub id: Option<String>,
    pub method: Option<String>,
    pub auto_complete: Option<bool>,
    pub hidden_list: Vec<(String, String)>,
    pub next_label: String,
    pub next_icon: String,
    pub next_class: String,
    pub next_href: Option<String>,
    pub extra_button: Option<Markup>,
    pub back_label: String,
    pub back_icon: String,
    pub back_class: String,
    pub body: Vec<Markup>,
    pub heading: Option<String>,
    pub description: Option<String>,
}

impl<T: FormWizardImpl> FormWizard<T> {
    pub fn add_body(&mut self, body: Markup) {
        self.body.push(body);
    }

    pub fn set_id(&mut self, id: &str) {
        self.id = Some(id.to_string());
    }

    pub fn set_hidden(&mut self, key: &str, value: impl Into<String>) {
        self.hidden_list.push((key.to_string(), value.into()));
    }

    pub fn add_heading(&mut self, heading: &str) {
        self.heading = Some(heading.to_string());
    }

    pub fn add_description(&mut self, description: &str) {
        self.description = Some(description.to_string());
    }

    pub fn disable_auto_complete(&mut self) {
        self.auto_complete = Some(false);
    }

    pub fn set_extra_button(&mut self, button: Markup) {
        self.extra_button = Some(button);
    }

    pub fn override_next_href(&mut self, href: &str) {
        self.next_href = Some(href.to_string());
    }

    pub fn error(&self, ctx: &T::Context) -> Option<String> {
        self.current_step.error_message(&self.current_step, ctx)
    }
}

impl<T: FormWizardImpl> Render for FormWizard<T> {
    fn render(&self) -> Markup {
        let all_variants = T::all_variants();
        //let current_index = all_variants.iter().position(|step| step == &self.current_step);

        html!(
            form.bux-form-wizard
                id=[&self.id]
                method=[&self.method]
                autocomplete=[&self.auto_complete.map(|b| if b { "on" } else { "off" })]
            {
                @for (key, value) in &self.hidden_list {
                    input type="hidden" name=(key) value=(value);
                }
                header {
                    ul {
                        @for (index, (variant, step)) in all_variants
                            .iter()
                            .map(|variant| (variant, variant.step(&self.context)))
                            .filter(|(_, step)| matches!(
                                step, FormWizardStep::Enabled { .. } | FormWizardStep::Disabled { .. }
                            ))
                            .enumerate()
                        {
                            @let error_message = variant.error_message(&self.current_step, &self.context);
                            @let error_class = if error_message.is_some() { "x-error" } else { "" };
                            @let selected_class = if variant == &self.current_step { "x-selected" } else { "" };

                            @match step {
                                FormWizardStep::Hidden => {
                                    // Don't render hidden steps
                                }
                                FormWizardStep::Disabled { label, complete } => {
                                    @let complete_class = if complete { "x-complete" } else { "x-incomplete" };
                                    li class=(format!("x-disabled {} {} {}", complete_class, selected_class, error_class)) {
                                        a {
                                            (format!("{}. {}", index + 1, label))
                                        }
                                    }
                                }
                                FormWizardStep::Enabled { label, complete } => {
                                    @let complete_class = if complete { "x-complete" } else { "x-incomplete" };
                                    li class=(format!("x-enabled {} {} {}", complete_class, selected_class, error_class)) {
                                        a href=(variant.href(&self.context)) title=[error_message] {
                                            (format!("{}. {}", index + 1, label))
                                        }
                                    }
                                }
                            }
                        }
                    }
                    @if let Some(heading) = &self.heading {
                        h1 {
                            (heading)
                        }
                    }
                    @if let Some(description) = &self.description {
                        p {
                            (description)
                        }
                    }
                }
                error {
                    @if let Some(error_message) = self.error(&self.context) {
                        (error_message)
                    }
                }

                // DO NOT render content element if there is an error
                @if matches!(self.current_step.step(&self.context), FormWizardStep::Enabled { .. }) {
                    content {
                        @for chunk in &self.body {
                            (chunk)
                        }
                    }
                }
                @else {
                    div.x-disabled-content {
                        "This step is not enabled. "
                        @if let Some(first_enabled) = self.current_step.best_redirect(&self.context) {
                            "Please visit "
                            @let step = first_enabled.step(&self.context);
                            @match step {
                                FormWizardStep::Enabled { label, .. } | FormWizardStep::Disabled { label, .. } => {
                                    a href=(first_enabled.href(&self.context)) { (label) }
                                }
                                FormWizardStep::Hidden => {
                                    a href=(first_enabled.href(&self.context)) { "next step" }
                                }
                            }
                            "."
                        }
                    }
                }

                footer {
                    @let current_variant = &self.current_step;
                    @if let Some(prev_variant) = current_variant.prev(&self.context) {
                        @let class = format!("x-prev-button {}", self.back_class);
                        (crate::button::link::label_icon_class(&self.back_label, &self.back_icon, &prev_variant.href(&self.context), &class))
                    }
                    " "
                    @if let Some(extra_button) = &self.extra_button {
                        (extra_button)
                    }
                    " "
                    @if let Some(next_variant) = current_variant.next(&self.context) {
                        @let class = format!("x-next-button {}", self.next_class);
                        @let href = if let Some(override_href) = &self.next_href {
                            override_href.clone()
                        } else {
                            next_variant.href(&self.context)
                        };
                        (crate::button::link::label_icon_class(&self.next_label, &self.next_icon, &href, &class))
                    }
                }
            }
        )
    }
}

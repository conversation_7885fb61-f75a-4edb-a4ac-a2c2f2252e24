insight-deck {

    contact-info {
        display: block;
        text-align: center;

        h1 {
            font-size: 18pt;
        }

        *:last-child {
            margin-bottom: 0;
        }
    }

    dl {
        margin-bottom: 0;

        dt {
            font-weight: normal;
        }

        dd {
            font-size: 12pt;

            &:last-child {
                margin-bottom: 0;
            }

            label-tag {
                font-size: 9pt;
            }
        }
    }


    panel {

        header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 1rem;
            background-color: #fff !important;
            border-bottom: none !important;
            padding-top: 1rem !important;

            h5 {
                font-size: 1.25rem;
                font-weight: 600;
                margin-bottom: 0.25rem;
            }

            p {
                margin-bottom: 0;
            }
        }

        content {

            ul.data-list {
                margin-bottom: 0;
                padding-left: 0;
                list-style-type: none;

                li {
                    border-bottom: 1px solid #ddd;
                    padding: .75rem 0;

                    hbox {
                        align-items: flex-start;
                        justify-content: flex-start;
                        line-height: 1.25;

                        i {
                            width: 2rem;
                            text-align: center;
                            color: #777;
                            line-height: 1.25;
                            flex-shrink: 0;
                        }

                        dl {
                            display: flex;
                            justify-content: flex-start;
                            margin-right: auto;

                            dt {
                                font-size: 12pt;
                                width: 20rem;
                                flex-shrink: 0;
                            }
                        }
                    }

                    &:first-child {
                        border-top: 1px solid #ddd;
                    }
                }
            }
        }

        &.gray-tile {
            background-color: #f5f5f5;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            margin-bottom: 0;

            content {

                hbox {
                    align-items: center;
                    justify-content: flex-start;
                    display: flex;
                    flex-direction: row;
                    gap: .50rem !important;

                    i {
                        width: 3rem;
                        height: 3rem;
                        flex-shrink: 0;
                        background-color: white;
                        border-radius: .375rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #777;
                    }
                }
            }
        }

        /* Selects the grid-2 element ONLY IF:
            1. It contains panel.gray-tile
            2. AND it is immediately followed by ul.data-list
        */
        grid-2:has(panel.gray-tile):has(+ ul.data-list) {
            margin-bottom: 1rem;
        }
    }
}

@media (max-width: 768px) {
    insight-deck panel {
        &.gray-tile {
            content {
                hbox {
                    align-items: flex-start;
                    gap: 0.5rem;
                }

                dl {
                    width: 100% !important;
                }

                i {
                    width: 2.5rem !important;
                    height: 2.5rem !important;
                }
            }
        }
    }
}

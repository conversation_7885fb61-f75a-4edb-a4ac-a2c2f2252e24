use crate::{<PERSON><PERSON>, <PERSON><PERSON>, html};

pub struct SetupChecklist<'a> {
    pub title: &'a str,
    pub description: Option<&'a str>,
    pub items: Vec<SetupItem<'a>>,
}

pub struct SetupItem<'a> {
    pub name: &'a str,
    pub complete: bool,
    pub action_url: &'a str,
}

pub fn setup_checklist<'a>() -> SetupChecklist<'a> {
    SetupChecklist {
        title: "Setup Checklist",
        description: None,
        items: Vec::new(),
    }
}

impl<'a> SetupChecklist<'a> {
    pub fn set_description(&mut self, description: &'a str) {
        self.description = Some(description);
    }

    pub fn add_item(&mut self, name: &'a str, complete: bool, action_url: &'a str) {
        self.items.push(SetupItem {
            name,
            complete,
            action_url,
        });
    }
}

impl<'a> Render for SetupChecklist<'a> {
    fn render(&self) -> Markup {
        html! {
            bux-component-setup-checklist {
                panel.setup-checklist {
                    header {
                        div.heading-wrapper {
                            h2 { (self.title) }
                            @if let Some(description) = self.description {
                                p { (description) }
                            }
                        }
                    }
                    content {
                        @if self.items.is_empty() {
                            p.no-items { "No setup items available." }
                        } @else {
                            ul.setup-items {
                                @for item in &self.items {
                                    li class=(if item.complete { "complete" } else { "incomplete" }) {
                                        i class=(if item.complete { "fas fa-check-circle" } else { "far fa-circle" }) aria-hidden="true" {}
                                        dl {
                                            dt { (item.name) }
                                            dd {
                                                @if item.complete {
                                                    label-tag.success { "Complete" }
                                                } @else {
                                                    label-tag.warning { "Incomplete" }
                                                }
                                            }
                                        }
                                        a.stretched-link href=(item.action_url) title="Edit" aria-label=(format!("Edit {}", item.name)) {
                                            i.fas.fa-pencil-alt aria-hidden="true" {}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

use crate::{<PERSON><PERSON>, Render, html};

pub struct Accordion<'a> {
    pub heading: Option<&'a str>,
    pub description: Option<&'a str>,
    pub items: Vec<AccordionItem<'a>>,
}

pub struct AccordionItem<'a> {
    pub title: &'a str,
    pub content: Markup,
}

pub fn bux_accordion<'a>() -> Accordion<'a> {
    Accordion {
        heading: None,
        description: None,
        items: Vec::new(),
    }
}

impl<'a> Accordion<'a> {
    pub fn set_heading(&mut self, heading: &'a str) {
        self.heading = Some(heading);
    }

    pub fn set_description(&mut self, description: &'a str) {
        self.description = Some(description);
    }

    pub fn add_item(&mut self, title: &'a str, content: Markup) {
        self.items.push(AccordionItem { title, content });
    }

    pub fn add_text_item(&mut self, title: &'a str, text: &'a str) {
        let content = html! { p { (text) } };
        self.add_item(title, content);
    }

    pub fn add_html_item(&mut self, title: &'a str, content: Markup) {
        self.add_item(title, content);
    }
}

impl Render for Accordion<'_> {
    fn render(&self) -> Markup {
        html! {
            bux-accordion {
                @if let Some(heading) = self.heading {
                    header {
                        h1 { (heading) }
                        @if let Some(description) = self.description {
                            p { (description) }
                        }
                    }
                }

                custom-accordion {
                    @for item in &self.items {
                        details {
                            summary.title-accordion {
                                (item.title)
                            }
                            body-accordion {
                                (item.content)
                            }
                        }
                    }
                }
            }
        }
    }
}

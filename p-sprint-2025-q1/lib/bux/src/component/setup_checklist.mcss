bux-component-setup-checklist {
    display: block;

    ul.setup-items {
        padding-left: 0;
        list-style-type: none;
        margin-bottom: 0;

        li {
            display: flex;
            position: relative;
            align-items: center;
            border-bottom: 1px solid #ddd;
            padding: .75rem 0;
            gap: .5rem;

            &:first-child {
                border-top: 1px solid #ddd;
            }

            i {
                width: 2rem;
                text-align: center;
                flex-shrink: 0;
            }

            dl {
                margin-right: auto;
                display: flex;
                gap: 1rem;
                margin-bottom: 0;

                dt {
                    font-size: 12pt;
                    flex-shrink: 0;
                    font-weight: 500;
                }

                dd {
                    margin-bottom: 0;
                }
            }

            &.incomplete {
                background-color: #fff3cd;

                > i {
                    color: #777;
                }
            }

            &.complete {
                > i {
                    color: #198754;
                }

                dl dt {
                    text-decoration: line-through;
                }
            }

            a.stretched-link {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                left: 0;
                z-index: 1;
                text-decoration: none;

                i {
                    position: absolute;
                    top: 50%;
                    right: 1rem;
                    transform: translateY(-50%);
                    color: #666;
                }

                &:hover i {
                    color: #333;
                }
            }
        }
    }
}

pub use bux_macros::document;

pub mod button;
pub mod component;
pub mod document;
pub mod element;
pub mod form;
pub mod input;
pub mod ui;

use granite::{DateUtc, Decimal, RoundingStrategy};
pub use maud::{<PERSON><PERSON>, PreEscaped, Render, html};
use num_format::{Locale, ToFormattedString};
use num_traits::cast::ToPrimitive;

pub trait Identity {
    fn role(&self) -> String;
    fn name(&self) -> Option<String>;
    fn email(&self) -> Option<String>;
    fn avatar_uri(&self) -> Option<String>;
}

/// Accepts a single query string value defined as Option<String> and parses it according to
/// standard bux active filter rules.
///
/// Valid inputs are:
/// * `None` -> use default
/// * `Some(all)` -> Show Active and Inactive
/// * `Some(active)` -> Show Active
/// * `Some(inactive)` -> Show Inactive
///
/// The meaning of the output to be consumed by APIs is:
/// * `Some(true)` -> Show Active
/// * `Some(false)` -> Show Inactive
/// * `None` -> Show Active and Inactive
pub fn parse_active_qs(qs_param: &Option<String>, default: Option<bool>) -> Option<bool> {
    match qs_param.as_deref() {
        Some("all") => None,
        Some("active") => Some(true),
        Some("inactive") => Some(false),

        // otherwise use default
        _ => default,
    }
}
/// US Based formatting of a date into MM/DD/YYYY format
pub fn format_date_us(date: DateUtc) -> String {
    date.format("%m/%d/%Y").to_string()
}

pub fn format_date_us_option(date: Option<DateUtc>) -> String {
    match date {
        Some(date) => format_date_us(date),
        None => "-".to_string(),
    }
}

/// US Based formatting of a Decimal into n,nnn format (0 decimal places)
pub fn format_decimal_us_0(amount: Decimal) -> String {
    let rounded = amount.round_dp_with_strategy(0, RoundingStrategy::MidpointAwayFromZero);

    let i64_part = match rounded.to_i64() {
        Some(i64_num) => i64_num,
        None => return format!("{amount}"),
    };

    i64_part.to_formatted_string(&Locale::en)
}

/// US Based formatting of a Decimal into $n,nnn format
pub fn format_currency_us_0(amount: Decimal) -> String {
    format!("${}", format_decimal_us_0(amount))
}

pub fn format_currency_us_0_option(amount: Option<Decimal>) -> String {
    match amount {
        Some(amount) => format_currency_us_0(amount),
        None => "-".to_string(),
    }
}

/// US Based formatting of a Decimal into n,nnn.nn format (2 decimal places)
pub fn format_decimal_us_2(amount: Decimal) -> String {
    let rounded = amount.round_dp_with_strategy(2, RoundingStrategy::MidpointAwayFromZero);
    let i64_part = match rounded.trunc().to_i64() {
        Some(i64_num) => i64_num,
        None => return format!("{amount}"),
    };

    // unwrap acceptable because we are multiplying the positive fraction by 100
    let decimal_part = (rounded.abs().fract() * Decimal::new(100, 0))
        .to_u8()
        .unwrap();

    format!(
        "{}.{:02}",
        i64_part.to_formatted_string(&Locale::en),
        decimal_part
    )
}

/// US Based formatting of a Decimal into $n,nnn.nn format
pub fn format_currency_us_2(amount: Decimal) -> String {
    format!("${}", format_decimal_us_2(amount))
}

pub fn format_currency_us_2_option(amount: Option<Decimal>) -> String {
    match amount {
        Some(amount) => format_currency_us_2(amount),
        None => "-".to_string(),
    }
}

pub fn format_percentage_us_0(amount: Decimal) -> String {
    format!("{}%", format_decimal_us_0(amount))
}

pub fn format_percentage_us_0_option(amount: Option<Decimal>) -> String {
    match amount {
        Some(amount) => format_percentage_us_0(amount),
        None => "-".to_string(),
    }
}

pub fn format_percentage_us_2(amount: Decimal) -> String {
    format!("{}%", format_decimal_us_2(amount))
}

pub fn format_percentage_us_2_option(amount: Option<Decimal>) -> String {
    match amount {
        Some(amount) => format_percentage_us_2(amount),
        None => "-".to_string(),
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::str::FromStr;

    // Tests for format_decimal_us_0
    #[test]
    fn test_format_decimal_us_0_positive_integer() {
        let amount = Decimal::from_str("1234").unwrap();
        assert_eq!(format_decimal_us_0(amount), "1,234");
    }

    #[test]
    fn test_format_decimal_us_0_positive_decimal_round_down() {
        let amount = Decimal::from_str("1234.49").unwrap();
        assert_eq!(format_decimal_us_0(amount), "1,234");
    }

    #[test]
    fn test_format_decimal_us_0_positive_decimal_round_up() {
        let amount = Decimal::from_str("1234.50").unwrap();
        assert_eq!(format_decimal_us_0(amount), "1,235");
    }

    #[test]
    fn test_format_decimal_us_0_negative_integer() {
        let amount = Decimal::from_str("-1234").unwrap();
        assert_eq!(format_decimal_us_0(amount), "-1,234");
    }

    #[test]
    fn test_format_decimal_us_0_negative_decimal_round_down() {
        let amount = Decimal::from_str("-1234.49").unwrap();
        assert_eq!(format_decimal_us_0(amount), "-1,234");
    }

    #[test]
    fn test_format_decimal_us_0_negative_decimal_round_up() {
        let amount = Decimal::from_str("-1234.50").unwrap();
        assert_eq!(format_decimal_us_0(amount), "-1,235");
    }

    #[test]
    fn test_format_decimal_us_0_zero() {
        let amount = Decimal::from_str("0").unwrap();
        assert_eq!(format_decimal_us_0(amount), "0");
    }

    #[test]
    fn test_format_decimal_us_0_small_decimal_round_up() {
        let amount = Decimal::from_str("0.50").unwrap();
        assert_eq!(format_decimal_us_0(amount), "1");
    }

    #[test]
    fn test_format_decimal_us_0_positive_one_decimal_place() {
        let amount = Decimal::from_str("3.5").unwrap();
        assert_eq!(format_decimal_us_0(amount), "4");
    }

    #[test]
    fn test_format_decimal_us_0_negative_one_decimal_place() {
        let amount = Decimal::from_str("-3.5").unwrap();
        assert_eq!(format_decimal_us_0(amount), "-4");
    }

    #[test]
    fn test_format_decimal_us_0_positive_one_digit() {
        let amount = Decimal::from_str("5").unwrap();
        assert_eq!(format_decimal_us_0(amount), "5");
    }

    #[test]
    fn test_format_decimal_us_0_negative_one_digit() {
        let amount = Decimal::from_str("-5").unwrap();
        assert_eq!(format_decimal_us_0(amount), "-5");
    }

    #[test]
    fn test_format_decimal_us_0_large_number() {
        let amount = Decimal::from_str("1234567890").unwrap();
        assert_eq!(format_decimal_us_0(amount), "1,234,567,890");
    }

    // Tests for format_decimal_us_2
    #[test]
    fn test_format_decimal_us_2_positive_integer() {
        let amount = Decimal::from_str("1234").unwrap();
        assert_eq!(format_decimal_us_2(amount), "1,234.00");
    }

    #[test]
    fn test_format_decimal_us_2_positive_decimal_exact() {
        let amount = Decimal::from_str("1234.56").unwrap();
        assert_eq!(format_decimal_us_2(amount), "1,234.56");
    }

    #[test]
    fn test_format_decimal_us_2_positive_decimal_round_down() {
        let amount = Decimal::from_str("1234.561").unwrap();
        assert_eq!(format_decimal_us_2(amount), "1,234.56");
    }

    #[test]
    fn test_format_decimal_us_2_positive_decimal_round_up() {
        let amount = Decimal::from_str("1234.565").unwrap();
        assert_eq!(format_decimal_us_2(amount), "1,234.57");
    }

    #[test]
    fn test_format_decimal_us_2_negative_integer() {
        let amount = Decimal::from_str("-1234").unwrap();
        assert_eq!(format_decimal_us_2(amount), "-1,234.00");
    }

    #[test]
    fn test_format_decimal_us_2_negative_decimal_exact() {
        let amount = Decimal::from_str("-1234.56").unwrap();
        assert_eq!(format_decimal_us_2(amount), "-1,234.56");
    }

    #[test]
    fn test_format_decimal_us_2_negative_decimal_round_down() {
        let amount = Decimal::from_str("-1234.561").unwrap();
        assert_eq!(format_decimal_us_2(amount), "-1,234.56");
    }

    #[test]
    fn test_format_decimal_us_2_negative_decimal_round_up() {
        let amount = Decimal::from_str("-1234.565").unwrap();
        assert_eq!(format_decimal_us_2(amount), "-1,234.57");
    }

    #[test]
    fn test_format_decimal_us_2_zero() {
        let amount = Decimal::from_str("0").unwrap();
        assert_eq!(format_decimal_us_2(amount), "0.00");
    }

    #[test]
    fn test_format_decimal_us_2_small_decimal_round_up() {
        let amount = Decimal::from_str("0.005").unwrap();
        assert_eq!(format_decimal_us_2(amount), "0.01");
    }

    #[test]
    fn test_format_decimal_us_2_positive_one_decimal_place() {
        let amount = Decimal::from_str("3.5").unwrap();
        assert_eq!(format_decimal_us_2(amount), "3.50");
    }

    #[test]
    fn test_format_decimal_us_2_negative_one_decimal_place() {
        let amount = Decimal::from_str("-3.5").unwrap();
        assert_eq!(format_decimal_us_2(amount), "-3.50");
    }

    #[test]
    fn test_format_decimal_us_2_positive_one_digit() {
        let amount = Decimal::from_str("5").unwrap();
        assert_eq!(format_decimal_us_2(amount), "5.00");
    }

    #[test]
    fn test_format_decimal_us_2_negative_one_digit() {
        let amount = Decimal::from_str("-5").unwrap();
        assert_eq!(format_decimal_us_2(amount), "-5.00");
    }

    #[test]
    fn test_format_decimal_us_2_large_number() {
        let amount = Decimal::from_str("12345678.90").unwrap();
        assert_eq!(format_decimal_us_2(amount), "12,345,678.90");
    }

    // Tests for format_currency_us_0
    #[test]
    fn test_format_currency_us_0_positive_integer() {
        let amount = Decimal::from_str("1234").unwrap();
        assert_eq!(format_currency_us_0(amount), "$1,234");
    }

    #[test]
    fn test_format_currency_us_0_positive_decimal_round_down() {
        let amount = Decimal::from_str("1234.49").unwrap();
        assert_eq!(format_currency_us_0(amount), "$1,234");
    }

    #[test]
    fn test_format_currency_us_0_positive_decimal_round_up() {
        let amount = Decimal::from_str("1234.50").unwrap();
        assert_eq!(format_currency_us_0(amount), "$1,235");
    }

    #[test]
    fn test_format_currency_us_0_negative_integer() {
        let amount = Decimal::from_str("-1234").unwrap();
        assert_eq!(format_currency_us_0(amount), "$-1,234");
    }

    #[test]
    fn test_format_currency_us_0_negative_decimal_round_down() {
        let amount = Decimal::from_str("-1234.49").unwrap();
        assert_eq!(format_currency_us_0(amount), "$-1,234");
    }

    #[test]
    fn test_format_currency_us_0_negative_decimal_round_up() {
        let amount = Decimal::from_str("-1234.50").unwrap();
        assert_eq!(format_currency_us_0(amount), "$-1,235");
    }

    #[test]
    fn test_format_currency_us_0_zero() {
        let amount = Decimal::from_str("0").unwrap();
        assert_eq!(format_currency_us_0(amount), "$0");
    }

    #[test]
    fn test_format_currency_us_0_small_decimal_round_up() {
        let amount = Decimal::from_str("0.50").unwrap();
        assert_eq!(format_currency_us_0(amount), "$1");
    }

    #[test]
    fn test_format_currency_us_0_positive_one_decimal_place() {
        let amount = Decimal::from_str("3.5").unwrap();
        assert_eq!(format_currency_us_0(amount), "$4");
    }

    #[test]
    fn test_format_currency_us_0_negative_one_decimal_place() {
        let amount = Decimal::from_str("-3.5").unwrap();
        assert_eq!(format_currency_us_0(amount), "$-4");
    }

    #[test]
    fn test_format_currency_us_0_positive_one_digit() {
        let amount = Decimal::from_str("5").unwrap();
        assert_eq!(format_currency_us_0(amount), "$5");
    }

    #[test]
    fn test_format_currency_us_0_negative_one_digit() {
        let amount = Decimal::from_str("-5").unwrap();
        assert_eq!(format_currency_us_0(amount), "$-5");
    }

    #[test]
    fn test_format_currency_us_0_large_number() {
        let amount = Decimal::from_str("1234567890").unwrap();
        assert_eq!(format_currency_us_0(amount), "$1,234,567,890");
    }

    #[test]
    fn test_format_currency_us_2_positive_integer() {
        let amount = Decimal::from_str("1234").unwrap();
        assert_eq!(format_currency_us_2(amount), "$1,234.00");
    }

    #[test]
    fn test_format_currency_us_2_positive_decimal_exact() {
        let amount = Decimal::from_str("1234.56").unwrap();
        assert_eq!(format_currency_us_2(amount), "$1,234.56");
    }

    #[test]
    fn test_format_currency_us_2_positive_decimal_round_down() {
        let amount = Decimal::from_str("1234.561").unwrap();
        assert_eq!(format_currency_us_2(amount), "$1,234.56");
    }

    #[test]
    fn test_format_currency_us_2_positive_decimal_round_up() {
        let amount = Decimal::from_str("1234.565").unwrap();
        assert_eq!(format_currency_us_2(amount), "$1,234.57");
    }

    #[test]
    fn test_format_currency_us_2_negative_integer() {
        let amount = Decimal::from_str("-1234").unwrap();
        assert_eq!(format_currency_us_2(amount), "$-1,234.00");
    }

    #[test]
    fn test_format_currency_us_2_negative_decimal_exact() {
        let amount = Decimal::from_str("-1234.56").unwrap();
        assert_eq!(format_currency_us_2(amount), "$-1,234.56");
    }

    #[test]
    fn test_format_currency_us_2_negative_decimal_round_down() {
        let amount = Decimal::from_str("-1234.561").unwrap();
        assert_eq!(format_currency_us_2(amount), "$-1,234.56");
    }

    #[test]
    fn test_format_currency_us_2_negative_decimal_round_up() {
        let amount = Decimal::from_str("-1234.565").unwrap();
        assert_eq!(format_currency_us_2(amount), "$-1,234.57");
    }

    #[test]
    fn test_format_currency_us_2_zero() {
        let amount = Decimal::from_str("0").unwrap();
        assert_eq!(format_currency_us_2(amount), "$0.00");
    }

    #[test]
    fn test_format_currency_us_2_small_decimal_round_up() {
        let amount = Decimal::from_str("0.005").unwrap();
        assert_eq!(format_currency_us_2(amount), "$0.01");
    }

    #[test]
    fn test_format_currency_us_2_positive_one_decimal_place() {
        let amount = Decimal::from_str("3.5").unwrap();
        assert_eq!(format_currency_us_2(amount), "$3.50");
    }

    #[test]
    fn test_format_currency_us_2_negative_one_decimal_place() {
        let amount = Decimal::from_str("-3.5").unwrap();
        assert_eq!(format_currency_us_2(amount), "$-3.50");
    }

    #[test]
    fn test_format_currency_us_2_positive_one_digit() {
        let amount = Decimal::from_str("5").unwrap();
        assert_eq!(format_currency_us_2(amount), "$5.00");
    }

    #[test]
    fn test_format_currency_us_2_negative_one_digit() {
        let amount = Decimal::from_str("-5").unwrap();
        assert_eq!(format_currency_us_2(amount), "$-5.00");
    }

    #[test]
    fn test_format_currency_us_2_large_number() {
        let amount = Decimal::from_str("12345678.90").unwrap();
        assert_eq!(format_currency_us_2(amount), "$12,345,678.90");
    }

    // Tests for format_percentage_us_0
    #[test]
    fn test_format_percentage_us_0_positive_integer() {
        let amount = Decimal::from_str("1234").unwrap();
        assert_eq!(format_percentage_us_0(amount), "1,234%");
    }

    #[test]
    fn test_format_percentage_us_0_positive_decimal_round_down() {
        let amount = Decimal::from_str("1234.49").unwrap();
        assert_eq!(format_percentage_us_0(amount), "1,234%");
    }

    #[test]
    fn test_format_percentage_us_0_positive_decimal_round_up() {
        let amount = Decimal::from_str("1234.50").unwrap();
        assert_eq!(format_percentage_us_0(amount), "1,235%");
    }

    #[test]
    fn test_format_percentage_us_0_negative_integer() {
        let amount = Decimal::from_str("-1234").unwrap();
        assert_eq!(format_percentage_us_0(amount), "-1,234%");
    }

    #[test]
    fn test_format_percentage_us_0_zero() {
        let amount = Decimal::from_str("0").unwrap();
        assert_eq!(format_percentage_us_0(amount), "0%");
    }

    #[test]
    fn test_format_percentage_us_0_small_decimal_round_up() {
        let amount = Decimal::from_str("0.50").unwrap();
        assert_eq!(format_percentage_us_0(amount), "1%");
    }

    #[test]
    fn test_format_percentage_us_0_positive_one_decimal_place() {
        let amount = Decimal::from_str("3.5").unwrap();
        assert_eq!(format_percentage_us_0(amount), "4%");
    }

    #[test]
    fn test_format_percentage_us_0_negative_one_decimal_place() {
        let amount = Decimal::from_str("-3.5").unwrap();
        assert_eq!(format_percentage_us_0(amount), "-4%");
    }

    #[test]
    fn test_format_percentage_us_0_positive_one_digit() {
        let amount = Decimal::from_str("5").unwrap();
        assert_eq!(format_percentage_us_0(amount), "5%");
    }

    #[test]
    fn test_format_percentage_us_0_negative_one_digit() {
        let amount = Decimal::from_str("-5").unwrap();
        assert_eq!(format_percentage_us_0(amount), "-5%");
    }

    #[test]
    fn test_format_percentage_us_0_large_number() {
        let amount = Decimal::from_str("1234567890").unwrap();
        assert_eq!(format_percentage_us_0(amount), "1,234,567,890%");
    }

    // Tests for format_percentage_us_2
    #[test]
    fn test_format_percentage_us_2_positive_integer() {
        let amount = Decimal::from_str("1234").unwrap();
        assert_eq!(format_percentage_us_2(amount), "1,234.00%");
    }

    #[test]
    fn test_format_percentage_us_2_positive_decimal_exact() {
        let amount = Decimal::from_str("1234.56").unwrap();
        assert_eq!(format_percentage_us_2(amount), "1,234.56%");
    }

    #[test]
    fn test_format_percentage_us_2_positive_decimal_round_down() {
        let amount = Decimal::from_str("1234.561").unwrap();
        assert_eq!(format_percentage_us_2(amount), "1,234.56%");
    }

    #[test]
    fn test_format_percentage_us_2_positive_decimal_round_up() {
        let amount = Decimal::from_str("1234.565").unwrap();
        assert_eq!(format_percentage_us_2(amount), "1,234.57%");
    }

    #[test]
    fn test_format_percentage_us_2_negative_integer() {
        let amount = Decimal::from_str("-1234").unwrap();
        assert_eq!(format_percentage_us_2(amount), "-1,234.00%");
    }

    #[test]
    fn test_format_percentage_us_2_zero() {
        let amount = Decimal::from_str("0").unwrap();
        assert_eq!(format_percentage_us_2(amount), "0.00%");
    }

    #[test]
    fn test_format_percentage_us_2_small_decimal_round_up() {
        let amount = Decimal::from_str("0.005").unwrap();
        assert_eq!(format_percentage_us_2(amount), "0.01%");
    }

    #[test]
    fn test_format_percentage_us_2_positive_one_decimal_place() {
        let amount = Decimal::from_str("3.5").unwrap();
        assert_eq!(format_percentage_us_2(amount), "3.50%");
    }

    #[test]
    fn test_format_percentage_us_2_negative_one_decimal_place() {
        let amount = Decimal::from_str("-3.5").unwrap();
        assert_eq!(format_percentage_us_2(amount), "-3.50%");
    }

    #[test]
    fn test_format_percentage_us_2_positive_one_digit() {
        let amount = Decimal::from_str("5").unwrap();
        assert_eq!(format_percentage_us_2(amount), "5.00%");
    }

    #[test]
    fn test_format_percentage_us_2_negative_one_digit() {
        let amount = Decimal::from_str("-5").unwrap();
        assert_eq!(format_percentage_us_2(amount), "-5.00%");
    }

    #[test]
    fn test_format_percentage_us_2_large_number() {
        let amount = Decimal::from_str("12345678.90").unwrap();
        assert_eq!(format_percentage_us_2(amount), "12,345,678.90%");
    }
}

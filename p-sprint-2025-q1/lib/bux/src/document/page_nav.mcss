
:root {

    --bux-page-nav-bg-color: #e4e6e7;
    --bux-page-nav-border-bottom: 1px solid #c4c7ca;
    --bux-page-nav-a-color: rgb(78, 78, 255);
    --bux-page-nav-a-hover-color: #f00;
    --bux-page-nav-a-fw: 500;
    --bux-page-nav-a-bg-color: #fff;
    --bux-page-nav-a-border: 1px solid #fff;
    --bux-page-nav-a-selected-td: underline;
    --bux-page-nav-a-selected-color: #fff;
    --bux-page-nav-a-selected-hover-color: #f00;
    --bux-page-nav-a-selected-fw: inherit;
    --bux-page-nav-a-selected-bg-color: #707070;
    --bux-page-nav-a-selected-border-color: #707070;
    
}

/*********************************************************/

page-nav-wrapper {
    display: block;
    padding: 1rem 0;
    background-color: var(--bux-page-nav-bg-color);
    border-bottom: var(--bux-page-nav-border-bottom);
    
    content-container {
        display: flex;
        justify-content: space-between;
        gap: 1rem;
        align-items: flex-start;

        nav#page-navigation {
        
            ul {
                padding-left: 0;
                margin-bottom: 0;
                display: flex;
                flex-wrap: wrap;
                gap: .5rem;
        
                li {
                    list-style-type: none;
                    display: inline-block;
                    vertical-align: middle;
        
                    a {
                        text-decoration: underline;
                        color: var(--bux-page-nav-a-color);
                        font-weight: var(--bux-page-nav-a-fw);
                        display: block;
                        background-color: var(--bux-page-nav-a-bg-color);
                        border: var(--bux-page-nav-a-border);
                        padding: .25rem .5rem;
                        font-size: .875rem;
                        border-radius: .375rem;
                        
                        u::before {
                            content: " 🞂 ";
                        }

                        &.selected {
                            text-decoration: var(--bux-page-nav-a-selected-td);
                            color: var(--bux-page-nav-a-selected-color);
                            font-weight: var(--bux-page-nav-a-selected-fw);
                            background-color: var(--bux-page-nav-a-selected-bg-color);
                            border-color: var(--bux-page-nav-a-selected-border-color);
                        }

                        &.external-link {
                            display: flex;
                            column-gap: .5rem;
                            align-items: center;
                        }

                    }

                    a:hover {
                        color: var(--bux-page-nav-a-hover-color);

                        &.selected {
                            color: var(--bux-page-nav-a-selected-hover-color);
                        }
                    }
                }
            }
        }

        button-wrapper {
            margin-left: auto;
            display: flex;
            gap: .25rem;
        }
    }
}

/*********************************************************/
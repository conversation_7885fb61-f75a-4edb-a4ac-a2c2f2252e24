use maud::html;

#[derive(Default)]
pub struct PageNavData {
    menu_items: Vec<(String, String)>,
    button_items: Vec<PageNavItem>,
}

pub enum PageNavItem {
    Link {
        title: String,
        href: String,
        additional_classes: Option<String>,
        icon: Option<String>,
        target: Option<String>,
    },
    Action {
        title: String,
        id: String,
        additional_classes: Option<String>,
        icon: Option<String>,
    },
}

impl PageNavItem {
    fn css_classes(&self) -> String {
        match self {
            PageNavItem::Link {
                additional_classes, ..
            } => {
                format!("btn sm {}", additional_classes.clone().unwrap_or_default())
            }
            PageNavItem::Action {
                additional_classes, ..
            } => {
                format!("sm {}", additional_classes.clone().unwrap_or_default())
            }
        }
    }
}

pub trait PageNav: super::Base {
    fn data(&self) -> &PageNavData;
    fn data_mut(&mut self) -> &mut PageNavData;

    fn add_page_nav_menu(&mut self, title: &str, href: &str) {
        PageNav::data_mut(self)
            .menu_items
            .push((title.to_owned(), href.to_owned()));
    }

    fn page_nav_add_record(&mut self, label: &str, href: &str) {
        self.add_page_nav_button(label, href, "link", Some("primary"), Some("fas fa-plus"));
    }

    fn page_nav_edit_record(&mut self, label: &str, href: &str) {
        self.add_page_nav_button(label, href, "link", Some(""), Some("fas fa-pencil-alt"));
    }

    fn page_nav_delete_record(&mut self, label: &str, href: &str) {
        self.add_page_nav_button(
            label,
            href,
            "link",
            Some("secondary"),
            Some("fas fa-trash-alt"),
        );
    }

    fn page_nav_for_button(&mut self, label: &str, href: &str) {
        self.add_page_nav_button(label, href, "link", Some("primary"), Some(""));
    }

    fn page_nav_enable_go_back(&mut self, href: &str) {
        self.add_page_nav_button(
            "Go Back",
            href,
            "link",
            Some("primary"),
            Some("fas fa-arrow-left"),
        );
    }

    /// Add a button to the page nav
    fn add_page_nav_button(
        &mut self,
        title: &str,
        href_or_id: &str,
        button_type: &str, // Accepts "link" or "action"
        additional_classes: Option<&str>,
        icon: Option<&str>,
    ) {
        let nav_item = match button_type {
            "link" => PageNavItem::Link {
                title: title.to_owned(),
                href: href_or_id.to_owned(),
                additional_classes: additional_classes.map(|cls| cls.to_owned()),
                icon: icon.map(|i| i.to_owned()),
                target: None,
            },
            "action" => PageNavItem::Action {
                title: title.to_owned(),
                id: href_or_id.to_owned(),
                additional_classes: additional_classes.map(|cls| cls.to_owned()),
                icon: icon.map(|i| i.to_owned()),
            },
            _ => panic!("Invalid button type: expected 'link' or 'action'"),
        };

        PageNav::data_mut(self).button_items.push(nav_item);
    }

    /// Add a page nav action
    fn add_page_nav_action(
        &mut self,
        title: &str,
        id: &str,
        additional_classes: Option<&str>,
        icon: Option<&str>,
    ) {
        let nav_item = PageNavItem::Action {
            title: title.to_owned(),
            id: id.to_owned(),
            additional_classes: additional_classes.map(|cls| cls.to_owned()),
            icon: icon.map(|i| i.to_owned()),
        };

        PageNav::data_mut(self).button_items.push(nav_item);
    }

    /// Add a link with a target attribute
    fn add_page_nav_link_targeted(
        &mut self,
        title: &str,
        href: &str,
        additional_classes: Option<&str>,
        icon: Option<&str>,
        target: &str,
    ) {
        let nav_item = PageNavItem::Link {
            title: title.to_owned(),
            href: href.to_owned(),
            additional_classes: additional_classes.map(|cls| cls.to_owned()),
            icon: icon.map(|i| i.to_owned()),
            target: Some(target.to_owned()),
        };

        PageNav::data_mut(self).button_items.push(nav_item);
    }

    fn render_nav_menu(&self) -> maud::Markup {
        let last_group = super::Base::menu(self).last_group();
        html! {
            nav aria-label="Page Navigation" id="page-navigation" {
                ul {
                    @if let Some(heading) = &last_group.heading {
                        li {
                            a class=(if super::Base::data(self).uri == heading.uri { "selected" } else { "" })
                              href=(heading.uri) { (heading.label) }
                        }
                    }
                    @for item in last_group {
                        li {
                            @match &item.inner {
                                approck::MenuItemInner::Link { uri } => {
                                    a class=(if &super::Base::data(self).uri == uri { "selected" } else { "" })
                                      href=(uri) { (item.label) }
                                },
                                approck::MenuItemInner::ExternalLink { uri } => {
                                    a.external-link href=(uri) target="_blank" rel="noopener noreferrer" aria-label=(format!("{} (opens in a new tab)", item.label)) {
                                        (item.label)
                                        i.fas.fa-external-link-alt aria-hidden="true" {}
                                    }
                                },
                                approck::MenuItemInner::SubMenu { .. } => {
                                    // This is the flyout
                                }
                            }
                        }
                    }

                    @for (title, href) in &PageNav::data(self).menu_items {
                        li class=(if &super::Base::data(self).uri == href { "selected" } else { "" }) {
                            a href=(href) { (title) }
                        }
                    }
                }
            }
        }
    }

    fn render_button_items(&self) -> maud::Markup {
        html! {
            button-wrapper {
                @for nav_item in &PageNav::data(self).button_items {
                    @match nav_item {
                        PageNavItem::Link { title, href, icon, target, .. } => {
                            a class=(nav_item.css_classes()) href=(href) target=[target] {
                                @if let Some(icon_class) = icon {
                                    i class=(icon_class) aria-hidden="true" {}
                                    " "
                                }
                                span { (title) }
                            }
                        },
                        PageNavItem::Action { title, id, icon, .. } => {
                            button class=(nav_item.css_classes()) id=(id) {
                                @if let Some(icon_class) = icon {
                                    i class=(icon_class) aria-hidden="true" {}
                                    " "
                                }
                                span { (title) }
                            }
                        },
                    }
                }
            }
        }
    }

    fn render(&self) -> maud::Markup {
        html! {
            page-nav-wrapper {

                content-container.fluid {

                    (self.render_nav_menu())

                    (self.render_button_items())
                }
            }
        }
    }
}

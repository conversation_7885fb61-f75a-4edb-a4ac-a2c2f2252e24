:root {
    /* split button */
    --cloudy-splitbtn-a-color: #333;
    --cloudy-splitbtn-a-hover-color: #333;
    --cloudy-splitbtn-a-bg-color: #fff;
    --cloudy-splitbtn-a-border-color: #ccc;
    --cloudy-splitbtn-btn-color: #333;
    --cloudy-splitbtn-btn-bg-color: #fff;
    --cloudy-splitbtn-btn-border-color: #ccc;

    /* breadcrumb */
    --cloudy-breadcrumb-a-color: #0d6efd;
    --cloudy-breadcrumb-a-hover-color: #0a58ca;
    --cloudy-breadcrumb-separator-color: #333;
}

/* Use a more specific selector to override bux css variables */
body {
    /* nav-wrapper */
    --bux-nav-wrap-bg-color: transparent;
    --bux-nav-wrap-padding: 1rem 0;

    /* navigation */
    --bux-nav-a-color: #0d6efd;
    --bux-nav-a-hover-color: #0a58ca;

    /* page navigation */
    --bux-page-nav-bg-color: transparent;
    --bux-page-nav-a-color: #0d6efd;
    --bux-page-nav-a-hover-color: #0a58ca;
    --bux-page-nav-a-fw: normal;
    --bux-page-nav-a-selected-color: #0d6efd;
    --bux-page-nav-a-selected-fw: bold;
    --bux-page-nav-a-selected-bg-color: transparent;
    --bux-page-nav-a-selected-border-color: transparent;

    /* footer */
    --bux-footer-bg-color: transparent;
    --bux-footer-color: #333;
}

/*********************************************************/
/* Default padding for content container */

layout-wrapper-outer.cloudy {

    layout-wrapper-inner {

        > cloudy-content-wrapper {

            content-container {
                padding-top: 1rem;
                padding-bottom: 1rem;
            }
        }
    }
}

/*********************************************************/
/* Adjust the layout to accomodate the sidebar */

layout-wrapper-outer.cloudy {

    layout-wrapper-inner {
        display: flex;
        flex-direction: column;

        > cloudy-content-wrapper {
            display: flex;
            flex-direction: column;
            flex: 1 0 auto;
            row-gap: 1rem;

            @media (min-width: 992px) {
                flex-direction: row;
                row-gap: 0;
            }
        }
    }
}

/*********************************************************/
/* Create a queryable container for the bux set width containers */

layout-wrapper-outer.cloudy {

    layout-wrapper-inner {

        > cloudy-content-wrapper {

            content-container {
                container-type: inline-size;
                container-name: content-container;
            }
        }
    }
}

/*********************************************************/
/* Stylistic support for BodyDisplay variants */

/* Apply these styles if cloudy-content-wrapper has the fixed class */
layout-wrapper-outer.cloudy {
    
    layout-wrapper-inner:has( > cloudy-content-wrapper.fixed) {
        height: 100vh;

        > cloudy-content-wrapper.fixed {
            overflow-y: auto;
            overflow-x: auto;
            flex-grow: 1;
            position: relative;
        }
    }

    /* Hide the footer if cloudy-content-wrapper has the fixed class */
    &:has( > layout-wrapper-inner > cloudy-content-wrapper.fixed) {
        
        footer {
            display: none;
        }
    }
}

/* Apply these styles if content-container has the narrow class */
layout-wrapper-outer.cloudy {
    
    layout-wrapper-inner {

        > cloudy-content-wrapper {

            content-container.narrow {
                width: 100%;

                @media (min-width: 576px) {
                    width: 540px;
                }
            }
        }
    }
}

/*********************************************************/
/* Sidebar */

.cloudy sidebar {
    display: block;
    padding: 1rem;
    background-color: #f1f3f6;
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.125);
    margin: 1rem;

    @media (min-width: 992px) {
        width: 320px;
        flex-shrink: 0;
        flex-grow: 0;
    }
}

/*********************************************************/
/* Flex wrapper for the go back button and breadcrumb */

.cloudy nav-wrapper {

    nav-controls {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

}

/*********************************************************/
/* Go Back Button */

.cloudy nav-wrapper {

    split-button {
        display: inline-flex;

        > a {
            display: inline-block;
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            border: 1px solid;
            color: var(--cloudy-splitbtn-a-color);
            background-color: var(--cloudy-splitbtn-a-bg-color);
            border-color: var(--cloudy-splitbtn-a-border-color);
            padding: .375rem .75rem;
            font-size: 1rem;
            border-radius: .375rem 0 0 .375rem;
            border-right: none;
            transition: background-color .15s ease-in-out, border-color .15s ease-in-out;
            white-space: nowrap;
            text-decoration: none;

            &:hover {
                color: var(--cloudy-splitbtn-a-hover-color);
            }
        }

        div.dropdown-wrapper {
            position: relative;

            button.dropdown-menu-toggle {
                border-radius: 0 .375rem .375rem 0;
                color: var(--cloudy-splitbtn-btn-color);
                background-color: var(--cloudy-splitbtn-btn-bg-color);
                border-color: var(--cloudy-splitbtn-btn-border-color);
        
                &::after {
                    content: "\f078";
                    font-family: "Font Awesome 5 Free";
                    font-weight: 900; /* Font weight for solid Font Awesome icons */
                    color: #333;
                }

                &:focus {
                    box-shadow: none;
                    outline: revert;
                }
            }
        
            ul {
                display: none;
                background-color: #fff;
                border: 1px solid #00000026;
                border-radius: .25rem;
                padding: .5rem 0;
                text-align: left;
                list-style-type: none;
                position: absolute;
                z-index: 1000;
                left: 0;
                right: auto;
                top: 100%;
                min-width: 18rem;
        
                li {
        
                    a {
                        text-decoration: none;
                        padding: .5rem 1rem;
                        color: #212529;
                        font-size: 1rem;
                        font-weight: 500;
                        display: block;
        
                        &:hover {
                            color: #1e2125;
                            background-color: #e9ecef;
                        }
                    }
                }
        
                &.show {
                    display: block;
                }
            }
        }
    }
}

/*********************************************************/
/* Breadcrumbs */

.cloudy nav.breadcrumb {
    margin-bottom: 0;

    ol {
        padding-left: 0;
        margin-bottom: 0;
        display: flex;
        flex-wrap: wrap;
        list-style-type: none;
        column-gap: 0.5rem;
        /* Add baseline alignment */
        align-items: baseline;

        li {
            a {
                color: var(--cloudy-breadcrumb-a-color);

                &:hover {
                    color: var(--cloudy-breadcrumb-a-hover-color);
                }
            }

            &.active {
                color: rgba(33, 37, 41, 0.75);
            }
            
            /* Style for last breadcrumb item */
            &:last-child a {
                font-weight: bold;
            }
        }

        li+li:before {
            content: "/";
            padding-right: 0.5rem;
            color: var(--cloudy-breadcrumb-separator-color);
        }
    }
}

/*********************************************************/
/* Base styles for the primary/secondary navigation */

.cloudy nav-wrapper {

    border-bottom: 1px solid #c4c7ca;

    content-container {
        grid-template-columns: 1fr max-content 1fr;

        @media (min-width: 992px) {
            > nav#primary-navigation {
                /*
                Revert to default grid placement behavior in 
                order to accommodate the addition of the Go Back button 
                */
                grid-area: auto;
            }        
        }

        > nav-header {
            @media (min-width: 992px) {
                grid-column: 2;
            }
        }
    }
}

/*********************************************************/
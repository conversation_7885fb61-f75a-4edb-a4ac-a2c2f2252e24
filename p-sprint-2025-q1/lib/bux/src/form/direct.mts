import "./direct.mcss";

import { SEC } from "@granite/lib.mts";

export class BuxFormDirect<
    INPUT,
    OUTPUT,
> {
    $form: HTMLFormElement;
    $error: HTMLElement;
    call: (value: INPUT) => Promise<OUTPUT>;

    get: () => INPUT;
    out: (value: OUTPUT) => void;

    constructor(opts: {
        $form: HTMLFormElement;
        api: {
            call: (value: INPUT) => Promise<OUTPUT>;
        };
        get: () => INPUT;
        out: (value: OUTPUT) => void;
        on_cancel?: (default_url: string) => void;
    }) {
        this.$form = opts.$form;
        this.$error = SEC(HTMLElement, this.$form, "error");
        this.call = opts.api.call;
        this.get = opts.get;
        this.out = opts.out;

        // if on_cancel was passed, call it instead with a string of the href
        // of the cancel button and let it do it's thing.
        const on_cancel = opts.on_cancel;
        if (on_cancel !== undefined) {
            const $cancel: HTMLAnchorElement = SEC(
                HTMLAnchorElement,
                this.$form,
                "footer a.bux-button-cancel",
            );
            const href = $cancel.href;
            $cancel.addEventListener("click", (event) => {
                event.preventDefault();
                on_cancel(href);
            });
        }

        this.$form.addEventListener("submit", (event) => {
            event.preventDefault();
            this.on_submit();
        });
    }

    protected async on_submit() {
        this.$error.textContent = "";

        const input = this.get();

        const output = await this.call(input);
        this.out(output);
    }
}

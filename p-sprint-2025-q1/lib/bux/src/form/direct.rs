use maud::html;

pub fn save_cancel(title: &str, cancel_href: &str) -> BuxFormDirect {
    BuxFormDirect {
        id: None,
        title: title.to_string(),
        submit_label: "Save".to_string(),
        submit_icon: "fas fa-check".to_string(),
        submit_class: "primary".to_string(),
        data_list: Vec::new(),
        body: Vec::new(),
        cancel_href: cancel_href.to_string(),
        hidden_list: Vec::new(),
    }
}

#[derive(Clone)]
pub struct BuxFormDirect {
    pub id: Option<String>,
    pub title: String,
    pub submit_label: String,
    pub submit_icon: String,
    pub submit_class: String,
    pub data_list: Vec<(String, maud::Markup)>,
    pub body: Vec<maud::Markup>,
    pub cancel_href: String,
    pub hidden_list: Vec<(String, String)>,
}

impl BuxFormDirect {
    pub fn add_data(&mut self, key: &str, value: &impl maud::Render) {
        self.data_list.push((key.to_string(), value.render()));
    }

    pub fn add_body(&mut self, body: maud::Markup) {
        self.body.push(body);
    }

    pub fn set_id(&mut self, id: &str) {
        self.id = Some(id.to_string());
    }

    pub fn set_hidden(&mut self, key: &str, value: impl Into<String>) {
        self.hidden_list.push((key.to_string(), value.into()));
    }
}

impl maud::Render for BuxFormDirect {
    fn render(&self) -> maud::Markup {
        html!(
            form.bux-form-direct id=[&self.id] {
                error {

                }
                @for (key, value) in &self.hidden_list {
                    input type="hidden" name=(key) value=(value);
                }
                @for chunk in &self.body {
                    (chunk)
                }
                footer {
                    (crate::button::submit::label_icon_class(&self.submit_label, &self.submit_icon, &self.submit_class))
                    " "
                    (crate::button::link::cancel(&self.cancel_href))
                }
            }
        )
    }
}

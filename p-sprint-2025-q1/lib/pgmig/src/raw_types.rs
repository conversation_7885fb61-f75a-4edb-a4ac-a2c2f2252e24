#![allow(unused_imports, unused_variables, dead_code)]
#![allow(clippy::get_first)]

use hcl::BlockLabel::Identifier as LabelIdent;
use hcl::BlockLabel::String as LabelString;
use hcl::Structure::Attribute as StructureAttribute;
use hcl::Structure::Block as StructureBlock;
use hcl::from_str;
use indexmap::IndexMap;
use std::path::{Path, PathBuf};

pub fn parse_file_or_panic(path: &Path) -> File {
    let text = std::fs::read_to_string(path).expect("Error reading file");
    // parse into hcl generic body
    let parsed: hcl::Body = from_str(&text).expect("Error parsing HCL");

    match process_file(path, parsed) {
        Ok(file) => file,
        Err(err) => {
            panic!("{}", err.path(path));
        }
    }
}

pub struct Error {
    pub message: String,
    pub path: Option<PathBuf>,
    pub context: Option<String>,
    pub stack: Vec<String>,
}

impl Error {
    pub fn new(message: impl Into<String>) -> Self {
        Self {
            message: message.into(),
            path: None,
            context: None,
            stack: Vec::new(),
        }
    }
    pub fn path(mut self, path: impl Into<PathBuf>) -> Self {
        self.path = Some(path.into());
        self
    }
    pub fn stack(mut self, stack: String) -> Self {
        self.stack.push(stack);
        self
    }
    pub fn context(mut self, context: impl Into<String>) -> Self {
        self.context = Some(context.into());
        self
    }
}

// implement display with showing the stack
impl std::fmt::Display for Error {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match &self.path {
            Some(path) => writeln!(f, ">>> Error in {}", path.display())?,
            None => writeln!(f, ">>> Error")?,
        }
        if let Some(context) = &self.context {
            writeln!(f, ">>> Context: {context}")?;
        }
        for (i, s) in self.stack.iter().enumerate() {
            if i > 0 {
                f.write_str(">>>   ")?;
            }
            f.write_str(s)?;
            f.write_str("\n")?;
        }
        writeln!(f, ">>> {}\n", self.message)?;
        Ok(())
    }
}

impl std::fmt::Debug for Error {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{self}")
    }
}

#[derive(Debug)]
pub struct File {
    pub path: std::path::PathBuf,
    pub type_map: IndexMap<String, Type>,
    pub schema_map: IndexMap<String, Schema>,
}

#[derive(Debug)]
pub enum Type {
    String {
        pgtype: Option<String>,
        allow_empty: Option<bool>,
        max_length: Option<usize>,
        strip: Option<bool>,
    },
    Bool {
        pgtype: Option<String>,
        default: Option<bool>,
    },
}

#[derive(Debug)]
pub struct Schema {
    pub name: String,
}

fn process_file(path: &std::path::Path, body: hcl::Body) -> Result<File, Error> {
    // get the top level blocks
    let mut file = File {
        path: path.to_path_buf(),
        type_map: IndexMap::new(),
        schema_map: IndexMap::new(),
    };

    for item in body.iter() {
        match item {
            StructureBlock(block) if block.identifier() == "type" => {
                let labels = block.labels();
                match (labels.get(0), labels.get(1), labels.get(2)) {
                    (Some(LabelIdent(name)), Some(LabelIdent(ty)), None) => {
                        let name = name.as_str();
                        let ty = ty.as_str();
                        let type_struct = match process_type(name, ty, block) {
                            Ok(t) => t,
                            Err(e) => return Err(e.stack(format!("type {name} {ty}"))),
                        };

                        file.type_map.insert(name.to_owned(), type_struct);
                    }
                    _ => {
                        return Err(Error::new("expected: type <ident> <ident> {{...}}")
                            .context(format!("{labels:?}")));
                    }
                }
            }
            StructureBlock(block) if block.identifier() == "column_set" => {
                let labels = block.labels();
                match (labels.get(0), labels.get(1)) {
                    (Some(LabelIdent(name)), None) => {
                        let name = name.as_str();
                        println!("Processing column_set: {name}");
                    }
                    _ => {
                        return Err(Error::new("expected: column_set <ident> {{...}}")
                            .context(format!("{labels:?}")));
                    }
                }
            }
            StructureBlock(block) if block.identifier() == "schema" => {
                let labels = block.labels();
                match (labels.get(0), labels.get(1)) {
                    (Some(LabelIdent(name)), None) => {
                        let name = name.as_str();

                        let schema_struct = match process_schema(name, block) {
                            Ok(s) => s,
                            Err(e) => return Err(e.stack(format!("schema {name}"))),
                        };
                        file.schema_map.insert(name.to_owned(), schema_struct);
                    }
                    _ => {
                        return Err(Error::new("expected: schema <ident> {{...}}")
                            .context(format!("{labels:?}")));
                    }
                }
            }
            StructureBlock(block) => {
                return Err(Error::new("Unexpected block at top level")
                    .stack(format!("block {}", block.identifier())));
            }
            StructureAttribute(attr) => {
                return Err(Error::new("Unexpected attribute at top level")
                    .stack(format!("attribute {}", attr.key())));
            }
        }
    }

    Ok(file)
}

fn process_type(name: &str, ty: &str, block: &hcl::Block) -> Result<Type, Error> {
    match ty {
        "String" => {
            println!("Processing String type: {name}");
        }
        "bool" => {
            println!("Processing bool type: {name}");
        }
        _ => panic!("Unexpected type: {ty}"),
    }

    Ok(Type::String {
        pgtype: None,
        allow_empty: None,
        max_length: None,
        strip: None,
    })
}

fn process_schema(name: &str, block: &hcl::Block) -> Result<Schema, Error> {
    Ok(Schema {
        name: name.to_owned(),
    })
}

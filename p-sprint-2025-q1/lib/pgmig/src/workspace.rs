use indexmap::IndexMap;
use std::path::PathBuf;

#[derive(Debug)]
pub struct PgmigWorkspace {
    app_map: IndexMap<String, App>,
    crate_map: IndexMap<String, Crate>,
}

#[derive(Debug)]
struct App {
    name: String,
    extended_crate_names: Vec<String>,
}

#[derive(Debug)]
struct Crate {
    name: String,
    path: PathBuf,
    raw_files: Vec<crate::raw_types::File>,
}

impl crate::InputWorkspace {
    /// Process the input workspace into a PgmigWorkspace.
    /// Will panic if there are any errors.
    ///
    /// This function assumes that the internal state of InputWorkspace is valid because
    /// it has already been validated by the `add_app(...)` and `add_crate(...)` methods.
    pub fn process(self) -> PgmigWorkspace {
        let mut this = PgmigWorkspace {
            app_map: IndexMap::new(),
            crate_map: IndexMap::new(),
        };

        // process each InputCrate, creating a map of output crates
        for (crate_name, input_crate) in self.crate_map {
            let mut crate_item = Crate {
                name: crate_name.clone(),
                path: input_crate.path,
                raw_files: Vec::new(),
            };

            for file_path in input_crate.files {
                let file = crate::raw_types::parse_file_or_panic(&file_path);
                crate_item.raw_files.push(file);
            }

            this.crate_map.insert(crate_name, crate_item);
        }

        // process each InputApp, creating a map of output apps
        for (app_name, input_app) in self.app_map {
            this.app_map.insert(
                app_name.clone(),
                App {
                    name: app_name,
                    extended_crate_names: input_app.extended_crate_names,
                },
            );
        }

        this
    }
}

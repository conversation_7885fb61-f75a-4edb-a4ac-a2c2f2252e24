//! This crate is designed to operate in a workspace context, though it can be
//! used with a single crate if desired.
//!
//! InputWorkspace -> Used to feed input into the Workspace
//!
//! ```
//! let mut input_workspace = pgmig::InputWorkspace::default();
//! // add crates this way
//! input_workspace.add_crate(
//!    "my_crate".to_string(),
//!     PathBuf::from("/path/to/my_crate"),
//!     vec![
//!         PathBuf::from("/path/to/my_crate/pgmig/file1.hcl"),
//!         PathBuf::from("/path/to/my_crate/pgmig/file2.hcl")
//!     ]
//! );
//!
//! // add apps this way
//! input_workspace.add_app(
//!     "my_app".to_string(),
//!     PathBuf::from("/path/to/my_app"),
//!     vec!["my_crate".to_string()]
//! );
//!
//! // Once you have finished, call the `.process()` method
//! let pgmig_workspace = input_workspace.process();
//!
//! ------
//!
//! Because this is designed to be used as part of a CLI, it currently panics
//! on errors.
//!
//! ```

#![allow(unused_imports, unused_variables, dead_code)]

use indexmap::IndexMap;

mod raw_types;
mod workspace;

pub use workspace::PgmigWorkspace;

#[derive(Debug)]
pub struct InputWorkspace {
    app_map: IndexMap<String, InputApp>,
    crate_map: IndexMap<String, InputCrate>,
}

#[derive(Debug)]
struct InputApp {
    /// The name of the app (crate name)
    name: String,

    /// the absolute path to the app crate
    path: std::path::PathBuf,

    /// The list of crates that this app extends (crate names)  
    /// Typically this would include the app crate itself
    extended_crate_names: Vec<String>,
}
#[derive(Debug)]
struct InputCrate {
    /// The name of the crate
    name: String,

    /// The absolute path to the crate
    path: std::path::PathBuf,

    /// The list of pgmig hcl files in this crate
    files: Vec<std::path::PathBuf>,
}

impl Default for InputWorkspace {
    fn default() -> Self {
        Self {
            app_map: IndexMap::new(),
            crate_map: IndexMap::new(),
        }
    }
}

impl InputWorkspace {
    /// true if an app has already been added with `add_app(...)`
    pub fn has_app(&self, app_name: &str) -> bool {
        self.app_map.contains_key(app_name)
    }

    /// true if a crate has already been added with `add_crate(...)`
    pub fn has_crate(&self, crate_name: &str) -> bool {
        self.crate_map.contains_key(crate_name)
    }

    /// Add an app to the workspace.
    /// - Panic if the app already exists
    /// - Panic if any extended crate has not been added already
    pub fn add_app(
        &mut self,
        app_name: String,
        app_path: std::path::PathBuf,
        extended_crate_names: Vec<String>,
    ) {
        // panic if the app already exists
        if self.app_map.contains_key(&app_name) {
            panic!("Duplicate app name: {app_name}");
        }

        // panic if any extended crate has not been added already
        for crate_name in &extended_crate_names {
            if !self.crate_map.contains_key(crate_name) {
                panic!(
                    "App `{app_name}` extends crate `{crate_name}`, but that crate has not been added yet"
                );
            }
        }

        self.app_map.insert(
            app_name.clone(),
            InputApp {
                name: app_name,
                path: app_path,
                extended_crate_names,
            },
        );
    }

    /// Add a crate to the workspace.
    /// - Panic if the crate already exists
    /// - Panic if any file does not exist
    /// - Panic if any file is not a .hcl file
    pub fn add_crate(
        &mut self,
        crate_name: String,
        crate_path: std::path::PathBuf,
        files: Vec<std::path::PathBuf>,
    ) {
        // panic if the crate already exists
        if self.crate_map.contains_key(&crate_name) {
            panic!("Duplicate crate name: {crate_name}");
        }

        // panic if any file does not exist or is not a .hcl file
        for file in &files {
            if !file.exists() {
                panic!("Crate file does not exist: {}", file.display());
            }
            if file.extension().is_none_or(|ext| ext != "hcl") {
                panic!("Crate file is not a .hcl file: {}", file.display());
            }
        }

        self.crate_map.insert(
            crate_name.clone(),
            InputCrate {
                name: crate_name,
                path: crate_path,
                files,
            },
        );
    }

    // note: process() is implemented in workspace.rs
}

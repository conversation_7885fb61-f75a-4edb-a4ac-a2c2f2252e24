type EsidNull String {
    nullable = true
    strip = true
    empty = null_if_empty()
    max_length = 16
    unique = true
}

type Esid String {
    nullable = false
    strip = true
    empty = false
    max_length = 16
    unique = true
}


type FirstName String {
    pgtype = "varchar(26)"
    allow_empty = false
    max_length = 26
    strip = true
}

type LastName String {
    pgtype = "varchar(26)"
    allow_empty = false
    max_length = 26
    strip = true
}

type Active bool {
    pgtype = "boolean"
    default = true
}


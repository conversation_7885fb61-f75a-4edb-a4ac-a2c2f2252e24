// LUKE: Review these websocket todos
// TODO: we need to limit the amount of in-flight requests via tokio semaphore or something
// TODO: tasks needs spawed into a tokio task
// TODO: review CHANNEL_SIZE

use crate::server::response::Response;
use crate::server::websocket::{MessageData, WebSocket};
use granite::{GTypeDecode, GTypeEncode, Uuid};
use std::sync::Arc;
use tokio::sync::mpsc;

// Channel size for the websocket sender
const CHANNEL_SIZE: usize = 100;

pub async fn app_socket_handler<APP>(
    app: &'static APP,
    req: &mut crate::server::Request,
    identity: &std::sync::Arc<APP::Identity>,
) -> crate::Result<crate::server::response::Response>
where
    APP: crate::server::App + Sync,
{
    crate::info!("app_socket_handler");
    let identity = Arc::clone(identity);

    match req
        .upgrade_to_websocket(move |socket| async move { websocket(app, identity, socket).await })
        .await
    {
        Ok(Some(response)) => Ok(Response::WebSocketUpgrade(response)),
        Ok(None) => todo!(),
        Err(_error) => todo!(),
    }
}

async fn websocket<APP>(
    app: &'static APP,
    identity: Arc<APP::Identity>,
    websocket: WebSocket,
) -> crate::Result<()>
where
    APP: crate::server::App + Sync,
{
    // Create channel for sending messages to the websocket
    let (tx, mut rx) = mpsc::channel::<Server2Client>(CHANNEL_SIZE);

    // Split the websocket
    let (mut sender, mut receiver) = websocket.split();

    tx.send(Server2Client {
        uuid: Uuid::now_v7(),
        uuid_replyto: None,
        payload: Server2ClientPayload::Hello,
    })
    .await?;

    // Spawn a task to handle sending messages
    let sender_task = async move {
        while let Some(message) = rx.recv().await {
            let json = message.gtype_encode_string()?;
            if let Err(e) = sender.send(json.into()).await {
                crate::error!("Error sending message: {e:?}");
                break;
            }
        }
        Ok::<_, granite::Error>(())
    };

    // Handle incoming messages
    let receiver_task = async move {
        while let Some(result) = receiver.recv().await {
            match result {
                Ok(message) => match message.into_data() {
                    MessageData::Text(text) => {
                        let json = match serde_json::from_str::<serde_json::Value>(&text) {
                            Ok(json) => json,
                            Err(e) => {
                                crate::error!("Error parsing message: {e:?}");
                                continue;
                            }
                        };

                        let incoming_message = match Client2Server::gtype_decode(Some(json)) {
                            Ok(message) => message,
                            Err(e) => {
                                crate::error!("Error parsing message: {e:?}");
                                continue;
                            }
                        };

                        crate::debug!("Received message: {:#?}", incoming_message);

                        let Client2Server {
                            uuid,
                            uuid_replyto: _,
                            payload: data,
                        } = incoming_message;

                        match data {
                            Client2ServerPayload::Ping => {
                                let reply = Server2Client {
                                    uuid: Uuid::now_v7(),
                                    uuid_replyto: Some(uuid),
                                    payload: Server2ClientPayload::Pong,
                                };
                                if tx.send(reply).await.is_err() {
                                    break;
                                }
                            }
                            Client2ServerPayload::ApiInvoke { api, data } => {
                                let tx = tx.clone();
                                let identity = Arc::clone(&identity);
                                tokio::spawn(async move {
                                    let response = app.handle_api(&identity, &api, data).await;
                                    let reply = Server2Client {
                                        uuid: Uuid::now_v7(),
                                        uuid_replyto: Some(uuid),
                                        payload: response,
                                    };
                                    if tx.send(reply).await.is_err() {
                                        crate::error!("Error sending message");
                                    }
                                });
                            }
                        }
                    }
                    MessageData::Binary(_) => {}
                    _ => {}
                },
                Err(error) => {
                    crate::error!("Error receiving message: {error:?}");
                    break;
                }
            }
        }
        Ok::<_, granite::Error>(())
    };

    // Wait for both tasks to complete
    let _ = tokio::try_join!(sender_task, receiver_task)?;

    Ok(())
}

#[granite::gtype(RsType, RsTypeEncode, TsType, TsTypeDecode, RsDebug)]
pub struct Server2Client {
    uuid: Uuid,
    uuid_replyto: Option<Uuid>,
    payload: Server2ClientPayload,
}

#[granite::gtype(RsType, RsTypeEncode, TsType, TsTypeDecode, RsDebug)]
pub enum Server2ClientPayload {
    Hello,
    Pong,
    ApiReturn {
        data: JsonValue,
    },
    AuthorizationError {
        message: String,
    },
    AuthenticationError {
        message: String,
    },
    BadRequest {
        message: String,
    },
    Err {
        message: Option<String>,
        error_uuid: String,
    },
}

#[granite::gtype(RsType, RsTypeDecode, TsType, TsTypeEncode, RsDebug)]
pub struct Client2Server {
    uuid: Uuid,
    uuid_replyto: Option<Uuid>,
    payload: Client2ServerPayload,
}

#[granite::gtype(RsType, RsTypeDecode, TsType, TsTypeEncode, RsDebug)]
pub enum Client2ServerPayload {
    Ping,
    ApiInvoke { api: String, data: JsonValue },
}

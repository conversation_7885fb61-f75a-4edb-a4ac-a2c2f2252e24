use clap::{Parser, Subcommand};

use std::path::PathBuf;

/// A fictional versioning CLI
#[derive(Debug, Parser)] // requires `derive` feature
#[command(name = "approck")]
pub struct Cli {
    #[clap(
        short = 'C',
        long,
        help = "Run in this directory instead of the current directory"
    )]
    current_directory: Option<PathBuf>,

    #[clap(
        long = "require-version",
        help = "require this version to be the one running or die with an error"
    )]
    require_version: Option<String>,

    #[command(subcommand)]
    command: Commands,
}

#[derive(Debug, Subcommand)]
enum Commands {
    // Init
    #[clap(about = "Initialize or Re-Initialize the workspace")]
    Init,

    #[clap(about = "Build configured projects")]
    Build {
        #[clap(short, long, help = "Build only these packages")]
        packages: Vec<String>,

        #[clap(long, help = "Release build")]
        release: bool,

        #[clap(long, help = "Prepare the build without actually running cargo build")]
        prep_only: bool,
    },

    #[clap(about = "Build and run configured projects")]
    Run {
        #[clap(short, long, help = "Build only these packages")]
        packages: Vec<String>,

        #[clap(long, help = "Release build")]
        release: bool,
    },

    #[clap(about = "Run configured projects, assuming they are already built")]
    RunOnly {
        #[clap(short, long, help = "Build only these packages")]
        packages: Vec<String>,

        #[clap(long, help = "Release build")]
        release: bool,
    },

    #[clap(about = "Lint and check the codebase")]
    Check,

    #[clap(about = "Format the codebase")]
    Format,

    #[clap(about = "Run unit tests for configured projects")]
    Test {
        #[clap(subcommand)]
        subcommand: Option<TestCommand>,
    },

    #[clap(about = "Routing information for this workspace")]
    Route {
        // multiple -p should be accepted
        #[clap(short, long)]
        package: Vec<String>,

        // Debug flag
        #[clap(short, long)]
        debug: bool,

        // Verbose flag
        #[clap(short, long)]
        verbose: bool,

        #[clap(long, conflicts_with = "no-color")]
        color: bool,

        // Color flag
        #[arg(long, conflicts_with = "color")]
        no_color: bool,

        // show map files
        #[clap(long)]
        show_source_maps: bool,

        // show nodes instead of routes
        #[clap(long, help = "Show nodes instead of routes")]
        raw: bool,

        // Optional string should be used as an argument for the filter
        #[clap()]
        filters: Option<Vec<String>>,
    },

    #[clap(about = "View info on this workspace")]
    Workspace,

    #[clap(about = "Audit the workspace for potential issues")]
    Audit,

    #[clap(about = "Cleans up all build artifacts")]
    Clean,

    #[clap(about = "Manage the aws-sdk custom builds")]
    AwsSdk {
        #[clap(subcommand)]
        subcommand: AwsSdkCommands,
    },

    #[clap(about = "Utility commands")]
    Util {
        #[clap(subcommand)]
        subcommand: UtilityCommands,
    },

    #[clap(about = "Print Version")]
    Version,
}

#[derive(Debug, Subcommand)]
enum UtilityCommands {
    #[clap(about = "Output a randomly generated 256 bit key in base64")]
    GenerateAesKey,
}

#[derive(Debug, Parser)]
struct AllTheArgs {
    args: Vec<String>,
}

#[derive(Debug, Subcommand)]
enum AwsSdkCommands {
    #[clap(about = "Clone smithy-rs and set on proper commit")]
    Clone,

    #[clap(about = "Set smithy-rs repo to proper state, modify input files, and build the aws-sdk")]
    Build,

    #[clap(about = "Push the generated aws-sdk to the repo")]
    Push,

    #[clap(about = "Clean-up temp files")]
    Clean,
}

#[derive(Debug, Subcommand)]
enum TestCommand {
    #[clap(about = "Start Postgres Docker Container, and run tests")]
    Postgres,

    #[clap(about = "Start Redis Docker Container, and run tests")]
    Redis,

    #[clap(about = "Run the tests for specific crate, including extended modules")]
    Crate { crate_name: String },
}

impl Cli {
    pub fn main(self) {
        match self.command {
            Commands::Init => println!("Executing init"),
            Commands::Build {
                packages,
                release,
                prep_only,
            } => {
                println!("Executing build");
                println!("Packages: {packages:?}");
                println!("Release: {release}");
                println!("Prep only: {prep_only}");
            }
            Commands::Run { packages, release } => {
                println!("Executing run");
                println!("Packages: {packages:?}");
                println!("Release: {release}");
            }
            // ... handle other commands ...
            _ => println!("Handling other commands"),
        }
    }
}

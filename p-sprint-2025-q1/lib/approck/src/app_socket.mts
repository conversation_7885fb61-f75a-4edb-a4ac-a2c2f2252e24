import { JsonValue, Option, uuid_v7 } from "@granite/lib.mts";
import { Client2Server_encode, Server2Client_decode } from "./app_socketλ.mts";

// Define the possible states for the WebSocket connection
enum SocketState {
    DISCONNECTED = "DISCONNECTED",
    CONNECTING = "CONNECTING",
    CONNECTED = "CONNECTED",
    RECONNECTING = "RECONNECTING",
}

// Define possible transitions between states
type StateTransition = {
    from: SocketState;
    to: SocketState;
    action: () => void;
};

class AppSocket {
    private socket: WebSocket | null = null;
    private reconnect_attempts: number = 0;
    // Remove max_reconnect_attempts since we'll always retry
    private reconnect_timer: number | null = null;
    private current_state: SocketState = SocketState.DISCONNECTED;
    private pending_requests: Map<
        string,
        { resolve: (data: any) => void; reject: (error: Error) => void; timeout: number }
    > = new Map();

    // Define allowed state transitions
    private transitions: StateTransition[] = [
        {
            from: SocketState.DISCONNECTED,
            to: SocketState.CONNECTING,
            action: () => this.initiate_connection(),
        },
        {
            from: SocketState.CONNECTING,
            to: SocketState.CONNECTED,
            action: () => this.on_connected(),
        },
        {
            from: SocketState.CONNECTING,
            to: SocketState.DISCONNECTED,
            action: () => this.on_connection_failed(),
        },
        {
            from: SocketState.CONNECTED,
            to: SocketState.DISCONNECTED,
            action: () => this.on_disconnected(false),
        },
        {
            from: SocketState.CONNECTED,
            to: SocketState.RECONNECTING,
            action: () => this.start_reconnection(),
        },
        {
            from: SocketState.DISCONNECTED,
            to: SocketState.RECONNECTING,
            action: () => this.start_reconnection(),
        },
        {
            from: SocketState.RECONNECTING,
            to: SocketState.CONNECTING,
            action: () => this.initiate_connection(),
        },
        {
            from: SocketState.RECONNECTING,
            to: SocketState.DISCONNECTED,
            action: () => this.on_reconnection_failed(),
        },
    ];

    constructor() {
        // Start in DISCONNECTED state and transition to CONNECTING
        this.transition_to(SocketState.CONNECTING);
    }

    // State transition method
    private transition_to(new_state: SocketState): boolean {
        // Find valid transition
        const transition = this.transitions.find((t) =>
            t.from === this.current_state && t.to === new_state
        );

        if (!transition) {
            console.error(`Invalid state transition: ${this.current_state} -> ${new_state}`);
            return false;
        }

        console.log(`Socket state transition: ${this.current_state} -> ${new_state}`);
        this.current_state = new_state;

        // Execute the transition action
        transition.action();
        return true;
    }

    // State-specific implementation methods
    private initiate_connection(): void {
        // Clear any existing reconnect timer
        if (this.reconnect_timer !== null) {
            clearTimeout(this.reconnect_timer);
            this.reconnect_timer = null;
        }

        const ws_protocol = globalThis.location.protocol === "https:" ? "wss:" : "ws:";
        const ws_url = `${ws_protocol}//${globalThis.location.host}/.socket`;

        this.socket = new WebSocket(ws_url);

        // Set up event handlers
        this.socket.onopen = () => {
            this.transition_to(SocketState.CONNECTED);
        };

        this.socket.onmessage = (event) => {
            if (this.current_state === SocketState.CONNECTED) {
                console.log("Received message:", event.data);
                this.handle_message(event.data);
            }
        };

        this.socket.onclose = () => {
            console.log("WebSocket disconnected");
            this.socket = null;

            if (this.current_state === SocketState.CONNECTING) {
                this.transition_to(SocketState.DISCONNECTED);
            } else if (this.current_state === SocketState.CONNECTED) {
                this.on_disconnected(true);
            }
        };

        this.socket.onerror = (error) => {
            console.error("WebSocket error:", error);
            // Errors are typically followed by onclose
        };
    }

    private on_connected(): void {
        console.log("WebSocket connected");
        this.reconnect_attempts = 0;
    }

    private on_connection_failed(): void {
        console.log("Connection attempt failed");
        // Always attempt to reconnect
        this.transition_to(SocketState.RECONNECTING);
    }

    private on_disconnected(attempt_reconnect: boolean): void {
        if (attempt_reconnect) {
            this.transition_to(SocketState.RECONNECTING);
        } else {
            console.log("Disconnected without reconnection attempt");
        }
    }

    private start_reconnection(): void {
        // Linear backoff: 0, 1, 2, 3... seconds
        // First attempt is immediate (0 seconds)
        const delay = this.reconnect_attempts * 1000;

        console.log(
            `Scheduling reconnect in ${delay / 1000} seconds (attempt ${
                this.reconnect_attempts + 1
            })`,
        );

        this.reconnect_timer = setTimeout(() => {
            this.reconnect_attempts++;
            this.transition_to(SocketState.CONNECTING);
        }, delay) as unknown as number;
    }

    private on_reconnection_failed(): void {
        console.log("Max reconnection attempts reached");
        this.reconnect_attempts = 0;
    }

    private handle_message(data: any) {
        if (typeof data !== "string") {
            console.error("Received non-string message:", data);
            return;
        }

        let json_data;
        try {
            json_data = JSON.parse(data);
        } catch (e) {
            console.error("Error parsing JSON:", e);
            return;
        }

        let message;
        {
            const r = Server2Client_decode(json_data);
            if ("Err" in r) {
                console.error("Error parsing message:", r.Err);
                return;
            }
            message = r.Ok;
        }

        if ("Hello" in message.payload) {
            console.log("Received Hello");
            return;
        } else if ("Pong" in message.payload) {
            console.log("Received Pong");
            return;
        } else if ("ApiReturn" in message.payload) {
            if (!("Some" in message.uuid_replyto)) {
                throw new Error("ApiReturn received without uuid_replyto");
            }

            const pending = this.pending_requests.get(message.uuid_replyto.Some);
            if (!pending) {
                throw new Error("ApiReturn received without pending request");
            }

            // Clear the timeout and resolve the promise
            clearTimeout(pending.timeout);
            this.pending_requests.delete(message.uuid_replyto.Some);
            pending.resolve(message.payload.ApiReturn.data);
            return;
        } else if ("AuthorizationError" in message.payload) {
            throw new Error(message.payload.AuthorizationError.message);
        } else if ("AuthenticationError" in message.payload) {
            throw new Error(message.payload.AuthenticationError.message);
        } else if ("BadRequest" in message.payload) {
            throw new Error(message.payload.BadRequest.message);
        } else if ("Err" in message.payload) {
            const e = message.payload.Err;
            if ("Some" in e.message) {
                throw new Error(`Error: ${e.message.Some} (error_uuid: ${e.error_uuid})`);
            } else {
                throw new Error(`Error: (error_uuid: ${e.error_uuid})`);
            }
        } else {
            console.log("Unhandled message:", message);
        }
    }

    // Public methods
    public connect(): boolean {
        return this.transition_to(SocketState.CONNECTING);
    }

    public disconnect(): boolean {
        if (this.current_state === SocketState.CONNECTED && this.socket) {
            this.socket.close();
            return true;
        }
        return this.transition_to(SocketState.DISCONNECTED);
    }

    private send(api: string, data: JsonValue, uuid: string): boolean {
        const outgoing_message = {
            uuid: uuid,
            uuid_replyto: { None: true } as Option<string>,
            payload: {
                ApiInvoke: {
                    api: api,
                    data: data,
                },
            },
        };

        const encoded_message = JSON.stringify(Client2Server_encode(outgoing_message));

        if (this.current_state !== SocketState.CONNECTED || !this.socket) {
            console.error(
                `Cannot send message because socket is not connected (state: ${this.current_state}): ${encoded_message}`,
            );
            return false;
        }

        try {
            this.socket.send(encoded_message);
            return true;
        } catch (e) {
            console.error("Error sending message:", e);
            return false;
        }
    }

    public call_api(api: string, data: JsonValue): Promise<JsonValue> {
        return new Promise((resolve, reject) => {
            const msg_uuid = uuid_v7();

            // Set a timeout to prevent hanging promises
            const timeout = setTimeout(() => {
                const pending = this.pending_requests.get(msg_uuid);
                if (pending) {
                    this.pending_requests.delete(msg_uuid);
                    reject(new Error("Request timed out"));
                }
            }, 15000); // 15 second timeout

            // Store the promise callbacks
            this.pending_requests.set(msg_uuid, {
                resolve,
                reject,
                timeout,
            });

            // Send the message
            const success = this.send(api, data, msg_uuid);
            if (!success) {
                clearTimeout(timeout);
                this.pending_requests.delete(msg_uuid);
                reject(new Error("Failed to send message"));
            }
        });
    }

    public get_state(): SocketState {
        return this.current_state;
    }
}

// Create singleton instance
const app_socket = new AppSocket();

// Public API - only keep the essential functions
export function ping(): Promise<any> {
    return app_socket.call_api("ping", null);
}

export function call_api(api: string, data: JsonValue): Promise<JsonValue> {
    return app_socket.call_api(api, data);
}

export { SocketState };

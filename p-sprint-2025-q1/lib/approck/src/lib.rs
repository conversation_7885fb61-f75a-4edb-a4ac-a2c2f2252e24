mod cache;
mod icon;
mod menu;

pub mod app_socket;
pub mod cli;
pub mod server;

pub use approck_macros::{api, api2, function, http, prefix};

pub use granite::{Error, ErrorType, Result, ResultExt};

pub use clap::{Parser, Subcommand};
pub use http::StatusCode;
pub use http::header::{HeaderMap, HeaderValue};
pub use maud::{Markup, PreEscaped, Render, html};
pub use phf;

pub use cache::{UuidCache, UuidCacheConfig};
pub use icon::Icon;
pub use menu::{Menu, MenuGroup, MenuGroupProxy, MenuItem, MenuItemInner};

pub use tracing::{debug, error, info, trace, warn};
pub use tracing_subscriber;

pub async fn sleep_ms(ms: u64) {
    tokio::time::sleep(std::time::Duration::from_millis(ms)).await;
}

pub trait Module {
    type Config: serde::de::DeserializeOwned + Send + Sync;
    fn new(config: Self::Config) -> granite::Result<Self>
    where
        Self: Sized;
    fn init(&self) -> impl std::future::Future<Output = granite::Result<()>> + Send;
}

pub trait App {
    type Config: serde::de::DeserializeOwned + Send + Sync;
    type Identity: Send + Sync + std::fmt::Debug;

    fn new(config: Self::Config) -> granite::Result<Self>
    where
        Self: Sized;
    fn init(&'static self) -> impl std::future::Future<Output = granite::Result<()>> + Send;
    fn auth(
        &self,
        _req: &server::Request,
    ) -> impl std::future::Future<Output = granite::Result<Self::Identity>> + Send;

    // Override this if you want uuid labels
    fn uuid_to_label(&'static self, _uuid: granite::Uuid) -> Option<String> {
        None
    }
}

pub trait Identity {
    fn web_usage(&self) -> bool {
        true
    }
    fn api_usage(&self) -> bool {
        true
    }
}

// create a macro that takes one identifier and returns struct <id> {}
#[macro_export]
macro_rules! main {
    ($cratelib:ident) => {
        fn main() {
            use clap::{Parser, Subcommand};
            use $crate::tracing_subscriber::EnvFilter;

            #[derive(Debug, Parser)]
            enum Commands {
                #[clap(about = "Print the webserver config")]
                Cfg,

                #[clap(about = "Run the application")]
                Run,

                #[clap(about = "Print the version")]
                Version,
            }

            #[derive(Debug, Parser)]
            #[command(name = "CARGO_PKG_NAME")]
            struct Cli {
                #[command(subcommand)]
                command: Option<Commands>,
            }

            approck::server::tls::install_crypto_provider();

            // setup tracing subscriber with env filter
            $crate::tracing_subscriber::fmt()
                .with_env_filter(std::env::var("RUST_LOG").unwrap_or_else(|_| "info".to_string()))
                .init();

            // get path to this executable
            let json_path = std::env::current_exe()
                .expect("current_exe")
                .with_extension("json");
            let json_data = std::fs::read_to_string(&json_path)
                .expect(format!("unable to read from {json_path:?}").as_str());

            // read configuration into $cratelib::AppConfig
            let config: $cratelib::AppConfig =
                match approck::server::exports::serde_json::from_str(&json_data) {
                    Ok(config) => config,
                    Err(e) => {
                        println!("Configuration Parsing Error: {:#?}", e);
                        std::process::exit(1);
                    }
                };

            let app: &'static $cratelib::AppStruct = Box::leak(Box::new(
                <$cratelib::AppStruct as approck::App>::new(config).unwrap(),
            ));

            // Parse /match cli here
            let cli = Cli::parse();

            let pkg_name = env!("CARGO_PKG_NAME");

            println!("{} {}\n", env!("CARGO_PKG_NAME"), env!("CARGO_PKG_VERSION"));

            match cli.command {
                Some(Commands::Cfg) => {
                    println!("{:#?}", app.webserver);
                    return;
                }
                Some(Commands::Run) => {}
                Some(Commands::Version) => {
                    println!("{}", $cratelib::libλ::VERSION_INFO);
                    return;
                }
                None => {}
            }

            // Initialize tokio runtime
            tokio::runtime::Builder::new_multi_thread()
                .enable_all()
                .thread_stack_size(4 * 1024 * 1024)
                .build()
                .unwrap()
                .block_on(async {
                    $crate::info!("Initializing application");
                    approck::App::init(app)
                        .await
                        .expect("Failed to initialize app");
                    $crate::info!("Application initialized");

                    println!("Welcome to `{}`", env!("CARGO_PKG_NAME"));
                    println!(
                        "  Visit {} in your browser",
                        approck::server::App::webserver_system(app).url()
                    );

                    $crate::info!("Starting server");
                    approck::server::serve(app).await.unwrap();
                    $crate::info!("Server stopped");
                });
        }
    };
}

#[allow(clippy::crate_in_macro_def)]
#[macro_export]
macro_rules! make_run_function {
    ($run_function:ident, $task_function:ident) => {
        /// this should be invoked with tokio::spawn

        pub async fn $run_function(app: &'static impl crate::App) {
            let mut sleep_time = 0;

            loop {
                approck::debug!("background task running at {}", file!());
                // this is spawing a background task to make use of tokio panic handling
                // so we have a reliable way to restart the task.
                let result = tokio::spawn(async move { $task_function(app).await }).await;

                match result {
                    Ok(Ok(_)) => {
                        approck::debug!("background task completed at {}", file!());
                    }
                    Ok(Err(e)) => {
                        approck::debug!(
                            "Error running background task at {}:{}: {:#?}",
                            file!(),
                            line!(),
                            e
                        );
                    }
                    Err(e) => {
                        approck::debug!(
                            "error joining background task at {}:{}: {:#?}",
                            file!(),
                            line!(),
                            e
                        );
                    }
                }

                // TODO: more reliable sleep timing.
                tokio::time::sleep(std::time::Duration::from_secs(sleep_time)).await;
                sleep_time += 1;
            }
        }
    };
}

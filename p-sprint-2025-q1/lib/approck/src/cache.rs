use dashmap::DashMap;
use granite::Uuid;

#[derive(Debug, serde::Deserialize, Default)]
pub struct UuidCacheConfig;

impl UuidCacheConfig {
    pub async fn into_system(self) -> granite::Result<UuidCache> {
        Ok(UuidCache::default())
    }
}

pub struct UuidCache {
    cache: DashMap<Uuid, String>,
}

impl Default for UuidCache {
    fn default() -> Self {
        Self {
            cache: DashMap::new(),
        }
    }
}

impl UuidCache {
    pub fn get(&self, key: Uuid) -> Option<String> {
        self.cache.get(&key).map(|value| value.clone())
    }

    pub fn set(&self, key: Uuid, value: String) {
        self.cache.insert(key, value);
    }
}

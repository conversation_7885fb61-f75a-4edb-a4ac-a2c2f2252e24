extern crate proc_macro;

use proc_macro::TokenStream;

#[proc_macro_attribute]
pub fn http(input: TokenStream, item: TokenStream) -> TokenStream {
    // Parse the function item
    let item_mod = syn::parse_macro_input!(item as syn::ItemMod);

    // Get the attribute struct
    let module_inner =
        match approck_compiler::http_macro::parse_module_inner(input.into(), item_mod) {
            Ok(attribute) => attribute,

            // Once diagnostic is extracted, emit as ITEM (vs attr) tokens
            Err(e) => return e.get_diagnostic().emit_as_item_tokens().into(),
        };

    // Expand it
    let expanded = approck_compiler::http_macro::codegen::expand(module_inner);

    // Return the generated function
    expanded.into()
}

/// Macro for setting up settings on URL prefixes
///
/// ```text
/// #[approck::prefix( /foo/bar/{id:i32}/ )
/// ```
///
/// * Must start with `/`
/// * Must end with `/`
/// * May include path captures like `{id:i32}`
/// * `/` is a valid prefix
#[proc_macro_attribute]
pub fn prefix(input: TokenStream, item: TokenStream) -> TokenStream {
    // Parse the function item
    let item_mod = syn::parse_macro_input!(item as syn::ItemMod);

    // Get the attribute struct
    let prefix_module_inner =
        match approck_compiler::prefix_macro::parse_macro_input_and_item(input.into(), item_mod) {
            Ok(attribute) => attribute,

            // Once diagnostic is extracted, emit as ITEM (vs attr) tokens
            Err(e) => return e.get_diagnostic().emit_as_item_tokens().into(),
        };

    // Expand it
    let expanded = approck_compiler::prefix_macro::expand_macro_output(prefix_module_inner);

    // Return the generated function
    expanded.into()
}

/// Macro for exposing a function as an API
#[proc_macro_attribute]
pub fn api(input: TokenStream, item: TokenStream) -> TokenStream {
    // Parse the function item
    let item_fn = syn::parse_macro_input!(item as syn::ItemMod);

    // Get the API function struct
    let api_fn = match approck_compiler::api_macro::parse_module_inner(input.into(), item_fn, "api")
    {
        Ok(api_fn) => api_fn,
        Err(e) => return e.get_diagnostic().emit_as_item_tokens().into(),
    };

    // Expand it
    let expanded = approck_compiler::api_macro::expand(api_fn);

    // Return the generated code
    expanded.into()
}

/// Macro for exposing a function as an API
#[proc_macro_attribute]
pub fn api2(input: TokenStream, item: TokenStream) -> TokenStream {
    // Parse the function item
    let item_fn = syn::parse_macro_input!(item as syn::ItemMod);

    // Get the API function struct
    let api_fn =
        match approck_compiler::api_macro::parse_module_inner(input.into(), item_fn, "api2") {
            Ok(api_fn) => api_fn,
            Err(e) => return e.get_diagnostic().emit_as_item_tokens().into(),
        };

    // Expand it
    let expanded = approck_compiler::api_macro::expand(api_fn);

    // Return the generated code
    expanded.into()
}

#[proc_macro_attribute]
pub fn function(input: TokenStream, item: TokenStream) -> TokenStream {
    // Parse the function item
    let item_fn = syn::parse_macro_input!(item as syn::ItemMod);

    // Get the API function struct
    let api_fn =
        match approck_compiler::api_macro::parse_module_inner(input.into(), item_fn, "function") {
            Ok(api_fn) => api_fn,
            Err(e) => return e.get_diagnostic().emit_as_item_tokens().into(),
        };

    // Expand it
    let expanded = approck_compiler::api_macro::expand(api_fn);

    // Return the generated code
    expanded.into()
}

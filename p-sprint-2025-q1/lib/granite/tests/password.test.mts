/// <reference lib="deno.ns" />
/// <reference lib="deno.window" />

import { password_analysis } from "../src/password.mts";
import { assert, assertEquals } from "https://deno.land/std@0.224.0/assert/mod.ts";

Deno.test("password_analysis empty password", () => {
    const analysis = password_analysis("");
    assertEquals(analysis.entropy, 0);
    assertEquals(analysis.meter_value, 0);
    assertEquals(analysis.has_lowercase, false);
    assertEquals(analysis.has_uppercase, false);
    assertEquals(analysis.has_numbers, false);
    assertEquals(analysis.has_symbols, false);
    assertEquals(analysis.has_unicode, false);
});

Deno.test("password_analysis weak password 'password'", () => {
    const analysis = password_analysis("password");
    assertEquals(analysis.has_lowercase, true);
    assertEquals(analysis.has_uppercase, false);
    assertEquals(analysis.has_numbers, false);
    assertEquals(analysis.has_symbols, false);
    assertEquals(analysis.has_unicode, false);
    assert(
        analysis.entropy > 35 && analysis.entropy < 40,
        `entropy ${analysis.entropy} is not in range (35, 40)`,
    ); // 8 * log2(26) = 37.5
});

Deno.test("password_analysis strong password 'MyStrongP4$$word!'", () => {
    const analysis = password_analysis("MyStrongP4$$word!");
    assertEquals(analysis.has_lowercase, true);
    assertEquals(analysis.has_uppercase, true);
    assertEquals(analysis.has_numbers, true);
    assertEquals(analysis.has_symbols, true);
    assertEquals(analysis.has_unicode, false);
    assert(
        analysis.entropy > 110 && analysis.entropy < 115,
        `entropy ${analysis.entropy} is not in range (110, 115)`,
    ); // 17 * log2(26+26+10+32) = 17 * log2(94) = 111.4
});

Deno.test("password_analysis unicode password 'こんにちは世界123!'", () => {
    const analysis = password_analysis("こんにちは世界123!");
    assertEquals(analysis.has_lowercase, false);
    assertEquals(analysis.has_uppercase, false);
    assertEquals(analysis.has_numbers, true);
    assertEquals(analysis.has_symbols, true);
    assertEquals(analysis.has_unicode, true);
    assert(analysis.entropy > 140, `entropy ${analysis.entropy} should be > 140`); // 11 * log2(10+32+8617) = 11 * log2(8659) = 143.9
});

Deno.test("password_analysis only numbers '12345678'", () => {
    const analysis = password_analysis("12345678");
    assertEquals(analysis.has_numbers, true);
    assert(
        analysis.entropy > 26 && analysis.entropy < 27,
        `entropy ${analysis.entropy} is not in range (26, 27)`,
    ); // 8 * log2(10) = 26.57
});

Deno.test("password_analysis only symbols '!@#$%^&*'", () => {
    const analysis = password_analysis("!@#$%^&*");
    assertEquals(analysis.has_symbols, true);
    assert(
        analysis.entropy > 39 && analysis.entropy < 41,
        `entropy ${analysis.entropy} is not in range (39, 41)`,
    ); // 8 * log2(32) = 40
});

Deno.test("password_analysis only uppercase 'UPPERCASE'", () => {
    const analysis = password_analysis("UPPERCASE");
    assertEquals(analysis.has_uppercase, true);
    assert(
        analysis.entropy > 42 && analysis.entropy < 43,
        `entropy ${analysis.entropy} is not in range (42, 43)`,
    ); // 9 * log2(26) = 42.2
});

Deno.test("password_analysis all ascii types 'aA1!'", () => {
    const analysis = password_analysis("aA1!");
    assertEquals(analysis.has_lowercase, true);
    assertEquals(analysis.has_uppercase, true);
    assertEquals(analysis.has_numbers, true);
    assertEquals(analysis.has_symbols, true);
    assert(
        analysis.entropy > 26 && analysis.entropy < 27,
        `entropy ${analysis.entropy} is not in range (26, 27)`,
    ); // 4 * log2(94) = 26.2
});

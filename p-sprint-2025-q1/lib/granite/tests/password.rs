use granite::password::{PasswordAnalysis, password_analysis};

#[test]
fn test_empty_password() {
    let analysis = password_analysis("");
    assert_eq!(analysis, PasswordAnalysis::default());
}

#[test]
fn test_short_lowercase() {
    let analysis = password_analysis("short");
    assert!(analysis.entropy > 0.0);
    assert_eq!(
        analysis.meter_text,
        "⚠⚠ Very Weak.  Add more characters. ⚠⚠"
    );
    assert!(!analysis.acceptable);
    assert!(analysis.has_lowercase);
    assert!(!analysis.has_uppercase);
    assert!(!analysis.has_numbers);
    assert!(!analysis.has_symbols);
    assert!(!analysis.has_unicode);
}

#[test]
fn test_strong_password() {
    let analysis = password_analysis("MyStrongP4$$word!");
    assert!(analysis.entropy >= 90.0);
    assert_eq!(analysis.meter_text, "Strong.  You are good to go.");
    assert!(analysis.acceptable);
    assert!(analysis.has_lowercase);
    assert!(analysis.has_uppercase);
    assert!(analysis.has_numbers);
    assert!(analysis.has_symbols);
    assert!(!analysis.has_unicode);
}

#[test]
fn test_very_strong_password() {
    let analysis = password_analysis("VeryLongAndComplexPassword123!@#$");
    assert!(analysis.entropy >= 120.0);
    assert_eq!(analysis.meter_text, "Very Strong.  Excellent!");
    assert!(analysis.acceptable);
}

#[test]
fn test_unicode_password() {
    let analysis = password_analysis("こんにちは世界123!");
    assert!(analysis.entropy > 120.0);
    assert_eq!(analysis.meter_text, "Very Strong.  Excellent!");
    assert!(analysis.acceptable);
    assert!(!analysis.has_lowercase);
    assert!(!analysis.has_uppercase);
    assert!(analysis.has_numbers);
    assert!(analysis.has_symbols);
    assert!(analysis.has_unicode);
}

#[test]
fn test_extended_latin_password() {
    let analysis = password_analysis("éàüöç");
    assert!(analysis.entropy > 0.0);
    assert!(analysis.has_unicode);
    assert!(!analysis.has_lowercase);
}

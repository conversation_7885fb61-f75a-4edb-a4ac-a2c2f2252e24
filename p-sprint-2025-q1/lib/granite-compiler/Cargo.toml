[package]
name = "granite-compiler"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
granite-core = { path = "../granite-core" }
json = { workspace = true }
once_cell = { workspace = true }
proc-macro2 = { workspace = true, features = ["span-locations"] }
proc-macro2-diagnostics = { workspace = true }
quote = { workspace = true }
serde_json = { workspace = true }
syn = {workspace = true, features = ["full"]}
tracing = { workspace = true }
indexmap = { workspace = true }


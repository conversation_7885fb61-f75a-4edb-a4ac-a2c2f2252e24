use crate::ident_str;

use super::{
    GADT, GInner, GInnerEnum, GInnerStruct, GInnerType, GMacroInput, GScalar, GType, GVariant,
    GeneratedChunk, TSImport, Trim,
};
use std::collections::{HashMap, HashSet};

use proc_macro2::{Ident, Span, TokenStream};
use quote::{ToTokens, quote};

macro_rules! mi_type {
    ($ident:expr) => {
        Ident::new(&format!("{}", $ident), Span::call_site())
    };
}

macro_rules! mi_partial {
    ($ident:expr) => {
        Ident::new(&format!("{}_Partial", $ident), Span::call_site())
    };
}

macro_rules! mi_error {
    ($ident:expr) => {
        Ident::new(&format!("{}_Error", $ident), Span::call_site())
    };
}

macro_rules! mi_validate_result_error {
    ($ident:expr) => {
        Ident::new(
            &format!("{}_validate_result_error", make_ident_t!($ident)),
            Span::call_site(),
        )
    };
}

macro_rules! mi_validate_result_string {
    ($ident:expr) => {
        Ident::new(
            &format!("{}_validate_result_string", make_ident_t!($ident)),
            Span::call_site(),
        )
    };
}

macro_rules! mi_decode_result_error {
    ($ident:expr) => {
        Ident::new(
            &format!("{}_decode_result_error", make_ident_t!($ident)),
            Span::call_site(),
        )
    };
}

macro_rules! mi_decode_result_string {
    ($ident:expr) => {
        Ident::new(
            &format!("{}_decode_result_string", make_ident_t!($ident)),
            Span::call_site(),
        )
    };
}

pub fn ts_import_set_to_token_stream(imports: &HashSet<TSImport>) -> TokenStream {
    // map the imports into a hashmap of files to import vecs
    let mut file_to_import_map: HashMap<&String, Vec<&TSImport>> = HashMap::new();
    for import in imports {
        file_to_import_map
            .entry(&import.from_path)
            .or_default()
            .push(import);
    }

    let mut file_and_import_vec = file_to_import_map.into_iter().collect::<Vec<_>>();
    file_and_import_vec.sort();
    for (_from_path, imports) in file_and_import_vec.iter_mut() {
        imports.sort();
    }

    let import_lines = file_and_import_vec.into_iter().map(|(from_path, imports)| {
        let items = imports.into_iter().map(|import| {
            let ident = ident_str!(import.ident);
            if import.is_type {
                quote! {
                    type #ident
                }
            } else {
                quote! {
                    #ident
                }
            }
        });

        quote! {
            import { #(#items),* } from #from_path;
        }
    });

    quote! {
        #(#import_lines)*
    }
}

/// Returns the generated typescript code for the given GADT struct or enum along with requested TSI and TSO code
pub fn g_macro_input_to_generated_chunk(g_macro_input: GMacroInput) -> GeneratedChunk {
    let mut generated_chunk = GeneratedChunk::new(g_macro_input.mod_ident.clone());

    for gadt in [
        g_macro_input.gadt_type,
        g_macro_input.gadt_partial,
        g_macro_input.gadt_error,
    ] {
        if gadt.ts_type {
            derive_type(&gadt, &mut generated_chunk);
        }
        if gadt.ts_type_encode {
            dervive_type_validate(&gadt, &mut generated_chunk);
        }
    }

    generated_chunk
}

fn derive_type(gadt: &GADT, generated_chunk: &mut GeneratedChunk) {
    let type_t = &gadt.ident;

    match &gadt.inner {
        GInner::Struct(GInnerStruct::Unit) => {
            generated_chunk.add_comment("type_t for struct (unit)");
            generated_chunk.add_code(quote! {
                export type #type_t = true;
            });
        }
        GInner::Struct(GInnerStruct::Tuple(singles)) => {
            let mut gtypes_tokens = Vec::with_capacity(singles.len());

            for (_vis, gtype) in singles {
                gtype.import_t(&mut generated_chunk.imports);
                gtypes_tokens.push(gtype.type_t());
            }

            generated_chunk.add_comment("type_t for struct (tuple)");
            generated_chunk.add_code(quote! {
                export type #type_t = [#(#gtypes_tokens),*];
            });
        }
        GInner::Struct(GInnerStruct::Named(vis_ident_gtype_list)) => {
            let mut gtypes_tokens = Vec::with_capacity(vis_ident_gtype_list.len());

            for (_vis, ident, gtype) in vis_ident_gtype_list {
                gtype.import_t(&mut generated_chunk.imports);
                let tstype = gtype.type_t();
                gtypes_tokens.push(quote! {
                    #ident: #tstype;
                });
            }

            generated_chunk.add_comment("type_t for struct (named)");
            generated_chunk.add_code(quote! {
                export type #type_t = {
                    #(#gtypes_tokens)*
                };
            });
        }
        GInner::Enum(GInnerEnum(ident_gvariant_list)) => {
            let mut variant_tokens = Vec::with_capacity(ident_gvariant_list.len());
            for (variant_ident, gvariant) in ident_gvariant_list {
                match gvariant {
                    GVariant::Unit => {
                        variant_tokens.push(quote! {
                            { #variant_ident: true }
                        });
                    }
                    GVariant::Tuple(singles) => {
                        let mut fields_tokens = Vec::with_capacity(singles.len());
                        for (gtype,) in singles {
                            gtype.import_t(&mut generated_chunk.imports);
                            fields_tokens.push(gtype.type_t());
                        }
                        variant_tokens.push(quote! {
                            { #variant_ident: [#(#fields_tokens),*]}
                        });
                    }
                    GVariant::Struct(ident_gtype_list) => {
                        let mut fields_tokens = Vec::with_capacity(ident_gtype_list.len());
                        for (ident, gtype) in ident_gtype_list {
                            gtype.import_t(&mut generated_chunk.imports);
                            let tstype = gtype.type_t();
                            fields_tokens.push(quote! {
                                #ident: #tstype
                            });
                        }
                        variant_tokens.push(quote! {
                            { #variant_ident: {#(#fields_tokens),*} }
                        });
                    }
                }
            }

            generated_chunk.add_comment("type_t for enum");
            generated_chunk.add_code(quote! {
                export type #type_t = #(#variant_tokens) | *;
            });
        }
        GInner::Type(GInnerType { .. }) => match &gadt.ts_type_from {
            Some(typescript_from) => {
                generated_chunk.add_ts_import(TSImport::other_type(
                    &gadt.ident.to_string(),
                    typescript_from,
                ));
            }
            None => {
                // no need to do anything
            }
        },
    };
}

fn dervive_type_validate(gadt: &GADT, generated_chunk: &mut GeneratedChunk) {
    let gadt_ident = &gadt.ident;
    let gadt_ident_e = mi_error!(gadt.ident);
    let ident_t2t = mi_validate_result_error!(gadt.ident);

    match &gadt.inner {
        GInner::Struct(GInnerStruct::Unit) => {
            generated_chunk.add_comment("impl_t2t for struct (unit)");
            generated_chunk.add_ts_import(TSImport::granite_type("Result"));
            generated_chunk.add_code(quote! {
                export function #ident_t2t(data: #gadt_ident | null): Result<#gadt_ident, #gadt_ident_e> {
                    if (data === true){
                        return { Ok: true };
                    } else if (data === null) {
                        return { Err: "Unit: value is missing" };
                    } else {
                        return { Err: "Unit: wrong type: " + typeof data };
                    }
                };
            });
        }
        GInner::Struct(GInnerStruct::Tuple(singles)) => {
            let mut terms1 = Vec::with_capacity(singles.len());
            let mut terms2 = Vec::with_capacity(singles.len());
            let mut terms3 = Vec::with_capacity(singles.len());
            let mut terms4 = Vec::with_capacity(singles.len());

            for (index, (_vis, gtype)) in singles.iter().enumerate() {
                gtype.import_t2t(&mut generated_chunk.imports);
                let index = proc_macro2::Literal::usize_unsuffixed(index);
                let tuple_field_name = Ident::new(&format!("v{}", index), Span::call_site());
                let field_t2t_call = gtype.callable_t2t();

                terms1.push(quote! {
                    const #tuple_field_name = #field_t2t_call(data[#index]);
                });
                terms2.push(quote! {
                    "Ok" in #tuple_field_name
                });
                terms3.push(quote! {
                    #tuple_field_name.Ok
                });
                terms4.push(quote! {
                    "Err" in #tuple_field_name ? #tuple_field_name.Err : null
                });
            }

            generated_chunk.add_comment("impl_t2t for struct (tuple)");
            generated_chunk.add_ts_import(TSImport::granite_type("Result"));
            generated_chunk.add_code(quote! {
                export function #ident_t2t(data: #gadt_ident | null): Result<#gadt_ident, #gadt_ident_e> {
                    // ensure it is an actual object that is an array
                    if (typeof data === "object" && data !== null && Array.isArray(data)) {
                        #(#terms1)*
                        if (#(#terms2)&&*) {
                            return { Ok: [#(#terms3),*] };
                        }
                        else {
                            return { Err: [#(#terms4),*] };
                        }
                    } else if (data == null) {
                        return { Err: "Tuple: value is missing" };
                    } else {
                        return { Err: "Tuple: wrong type: " + typeof data };
                    }
                };
            });
        }
        GInner::Struct(GInnerStruct::Named(vis_ident_gtype_list)) => {
            let mut terms1 = Vec::with_capacity(vis_ident_gtype_list.len());
            let mut terms2 = Vec::with_capacity(vis_ident_gtype_list.len());
            let mut terms3 = Vec::with_capacity(vis_ident_gtype_list.len());
            let mut terms4 = Vec::with_capacity(vis_ident_gtype_list.len());

            for (_vis, struct_field_ident, gtype) in vis_ident_gtype_list {
                gtype.import_t2t(&mut generated_chunk.imports);
                let struct_field_ident_string = struct_field_ident.to_string();
                let field_t2t_call = gtype.callable_t2t();

                terms1.push(quote! {
                    const #struct_field_ident = #field_t2t_call(data[#struct_field_ident_string]);
                });
                terms2.push(quote! {
                    "Ok" in #struct_field_ident
                });
                terms3.push(quote! {
                    #struct_field_ident: #struct_field_ident.Ok
                });
                terms4.push(quote! {
                    #struct_field_ident: "Err" in #struct_field_ident ? #struct_field_ident.Err : null
                });
            }

            generated_chunk.add_comment("impl_t2t for struct (named)");
            generated_chunk.add_ts_import(TSImport::granite_type("Result"));
            generated_chunk.add_code(quote! {
                export function #ident_t2t(data: #gadt_ident | null): Result<#gadt_ident, #gadt_ident_e> {
                    // ensure it is an actual object that is not an array
                    if (typeof data === "object" && data !== null && !Array.isArray(data)) {
                        #(#terms1)*
                        if (#(#terms2)&&*) {
                            return { Ok: { #(#terms3),* } };
                        }
                        else {
                            return { Err: { #(#terms4),* } };
                        }
                    } else if (data == null) {
                        return { Err: "Named: value is missing" };
                    } else {
                        return { Err: "Named: wrong type: " + typeof data };
                    }
                };
            });
        }
        GInner::Enum(GInnerEnum(ident_gvariant_list)) => {
            let mut terms = Vec::with_capacity(ident_gvariant_list.len());
            for (variant_ident, gvariant) in ident_gvariant_list {
                let variant_ident_string = variant_ident.to_string();
                let variant_ident_invalid_string_e = format!(
                    "Enum: invalid variant - add variant: {}",
                    variant_ident_string
                );
                match gvariant {
                    GVariant::Unit => {
                        terms.push(quote! {
                            if (#variant_ident_string in data) {
                                return { Ok: { #variant_ident: true } };
                            }
                        });
                    }
                    GVariant::Tuple(singles) => {
                        let mut terms1 = Vec::with_capacity(singles.len());
                        let mut terms2 = Vec::with_capacity(singles.len());

                        let mut terms_ok = Vec::with_capacity(singles.len());
                        let mut terms_err = Vec::with_capacity(singles.len());

                        for (index, (gtype,)) in singles.iter().enumerate() {
                            gtype.import_t2t(&mut generated_chunk.imports);
                            let index = proc_macro2::Literal::usize_unsuffixed(index);
                            let tuple_field_ident =
                                Ident::new(&format!("v{}", index), Span::call_site());
                            let tuple_field_t2t_call = gtype.callable_t2t();

                            terms1
                                .push(quote! { const #tuple_field_ident = #tuple_field_t2t_call(arr[#index]); });
                            terms2.push(quote! { #tuple_field_ident.Ok });

                            terms_ok.push(quote! { "Ok" in #tuple_field_ident });
                            terms_err.push(quote! { "Err" in #tuple_field_ident ? #tuple_field_ident.Err : null });
                        }

                        terms.push(quote! {
                            // Ensure the variant is an object with the correct key
                            if (#variant_ident_string in data) {
                                // Ensure the value of that key is an array
                                const arr = data[#variant_ident_string];
                                if (typeof arr === "object" && arr !== null && Array.isArray(arr)) {
                                    #(#terms1)*
                                    if (#(#terms_ok)&&*) {
                                        return { Ok: { #variant_ident: [#(#terms2),*] } };
                                    }
                                    return { Err: { #variant_ident: [#(#terms_err),*] } };
                                }
                                return { Err: #variant_ident_invalid_string_e };
                            }
                        });
                    }
                    GVariant::Struct(doubles) => {
                        let ident_gtype_list_len: usize = doubles.len();
                        let mut terms1 = Vec::with_capacity(ident_gtype_list_len);
                        let mut terms2 = Vec::with_capacity(ident_gtype_list_len);

                        let mut terms_ok = Vec::with_capacity(ident_gtype_list_len);
                        let mut terms_err = Vec::with_capacity(ident_gtype_list_len);

                        for (named_field_ident, gtype) in doubles {
                            gtype.import_t2t(&mut generated_chunk.imports);
                            let named_field_ident_string = named_field_ident.to_string();
                            let named_field_t2t_call = gtype.callable_t2t();

                            terms1
                                .push(quote! { const #named_field_ident = #named_field_t2t_call(obj[#named_field_ident_string]); });
                            terms2.push(quote! { #named_field_ident: #named_field_ident.Ok });

                            terms_ok.push(quote! { "Ok" in #named_field_ident });
                            terms_err.push(quote! { #named_field_ident: "Err" in #named_field_ident ? #named_field_ident.Err : null });
                        }

                        terms.push(quote! {
                            // Ensure the variant is an object with the correct key
                            if (#variant_ident_string in data) {
                                // Ensure the value of that key is an object
                                const obj = data[#variant_ident_string];
                                if (typeof obj === "object" && obj !== null && !Array.isArray(obj)) {
                                    #(#terms1)*
                                    if (#(#terms_ok)&&*) {
                                        return { Ok: { #variant_ident: { #(#terms2),* } } };
                                    }
                                    return { Err: { #variant_ident: {#(#terms_err),*} } };
                                } else if (obj === null) {
                                    return { Err: "Enum: value is missing" };
                                }
                                return { Err: #variant_ident_invalid_string_e };
                            }
                        });
                    }
                }
            }

            generated_chunk.add_comment("impl_t2t for enum");
            generated_chunk.add_ts_import(TSImport::granite_type("Result"));
            generated_chunk.add_code(quote! {
                export function #ident_t2t(data: #gadt_ident | null): Result<#gadt_ident, #gadt_ident_e> {
                    if (typeof data === "object" && data !== null && !Array.isArray(data)) {
                        #(#terms)*
                        return { Err: "Enum: no variant found" };
                    } else if (data === null) {
                        return { Err: "Enum: value is missing" };
                    }
                    return { Err: "Enum: wrong type: " + typeof data };
                };
            });
        }
        GInner::Type(GInnerType { .. }) => {
            if let Some(typescript_import) = &gadt.ts_type_from {
                let t2t = mi_validate_result_error!(gadt_ident).to_string();
                generated_chunk.add_ts_import(TSImport::other_item(&t2t, typescript_import))
            } else {
                unreachable!("Due to validation of attribute parser, this should be unreachable");
            }
        }
    }
}

fn derive_validate(gadt: &GADT, generated_chunk: &mut GeneratedChunk) {
    quote! {}
}

fn derive_type_encode(gadt: &GADT, generated_chunk: &mut GeneratedChunk) {
    let gadt_ident: &Ident = &gadt.ident;
    let t2j_ident = make_ident_t2j!(gadt_ident);

    match &gadt.inner {
        GInner::Struct(GInnerStruct::Unit) => {
            generated_chunk.add_comment("impl_t2j for struct (unit)");
            generated_chunk.add_code(quote! {
                export function #t2j_ident(_value: #gadt_ident): true {
                    return true;
                };
            });
        }
        GInner::Struct(GInnerStruct::Tuple(singles)) => {
            let mut terms = Vec::with_capacity(singles.len());

            for (index, (_vis, gtype)) in singles.iter().enumerate() {
                gtype.import_t2j(&mut generated_chunk.imports);
                let index = proc_macro2::Literal::usize_unsuffixed(index);
                let field_t2j_call = gtype.callable_t2j();
                terms.push(quote! {
                    #field_t2j_call(value[#index])
                });
            }

            generated_chunk.add_comment("impl_t2j for struct (tuple)");
            generated_chunk.add_ts_import(TSImport::granite_type("JsonArray"));
            generated_chunk.add_code(quote! {
                export function #t2j_ident(value: #gadt_ident): JsonArray {
                    return [#(#terms),*];
                };
            });
        }
        GInner::Struct(GInnerStruct::Named(vis_ident_gtype_list)) => {
            let mut terms = Vec::with_capacity(vis_ident_gtype_list.len());

            for (_vis, ident, gtype) in vis_ident_gtype_list {
                gtype.import_t2j(&mut generated_chunk.imports);
                let field_t2j_call = gtype.callable_t2j();
                terms.push(quote! {
                    #ident: #field_t2j_call(value.#ident)
                });
            }

            generated_chunk.add_comment("impl_t2j for struct (named)");
            generated_chunk.add_ts_import(TSImport::granite_type("JsonObject"));
            generated_chunk.add_code(quote! {
                // JsonObject is used here because a named struct will ALWAYS produce a JsonObject | null
                // This is very important because of integration with Stepper and other libraries that expect a object.
                export function #t2j_ident(value: #gadt_ident): JsonObject {
                    return {#(#terms),*};
                };
            });
        }
        GInner::Enum(GInnerEnum(ident_gvariant_list)) => {
            let mut terms = Vec::with_capacity(ident_gvariant_list.len());
            for (variant_ident, gvariant) in ident_gvariant_list {
                let variant_ident_string = variant_ident.to_string();
                match gvariant {
                    GVariant::Unit => {
                        terms.push(quote! {
                            if (#variant_ident_string in value) {
                                return { #variant_ident: true };
                            }
                        });
                    }
                    GVariant::Tuple(singles) => {
                        let mut fields_t2j_calls = Vec::with_capacity(singles.len());
                        for (index, (gtype,)) in singles.iter().enumerate() {
                            gtype.import_t2j(&mut generated_chunk.imports);
                            let index = proc_macro2::Literal::usize_unsuffixed(index);
                            let field_t2j_call = gtype.callable_t2j();
                            fields_t2j_calls.push(quote! {
                                #field_t2j_call(value.#variant_ident[#index])
                            });
                        }
                        terms.push(quote! {
                            if (#variant_ident_string in value) {
                                return {
                                    #variant_ident: [#(#fields_t2j_calls),*]
                                };
                            }
                        });
                    }
                    GVariant::Struct(doubles) => {
                        let mut variant_terms = Vec::with_capacity(doubles.len());
                        for (variant_field_ident, gtype) in doubles {
                            gtype.import_t2j(&mut generated_chunk.imports);
                            let field_t2j_call = gtype.callable_t2j();
                            variant_terms.push(quote! {
                                #variant_field_ident: #field_t2j_call(value.#variant_ident.#variant_field_ident)
                            });
                        }
                        terms.push(quote! {
                            if (#variant_ident_string in value) {
                                return {
                                    #variant_ident: {#(#variant_terms),*}
                                };
                            }
                        });
                    }
                }
            }

            generated_chunk.add_comment("impl_t2j for enum");
            generated_chunk.add_ts_import(TSImport::granite_type("JsonObject"));
            generated_chunk.add_code(quote! {
                export function #t2j_ident(value: #gadt_ident): JsonObject {
                    #(#terms)*
                    // if no term matches, return an empty object
                    return {};
                };
            });
        }
        GInner::Type(GInnerType { .. }) => {
            if let Some(typescript_import) = &gadt.ts_type_from {
                let t2j = make_ident_t2j!(gadt_ident).to_string();
                generated_chunk.add_ts_import(TSImport::other_item(&t2j, typescript_import))
            } else {
                unreachable!("Due to validation of attribute parser, this should be unreachable");
            }
        }
    }
}

fn derive_decode_result_string(gadt: &GADT, generated_chunk: &mut GeneratedChunk) {
    let gadt_ident = &gadt.ident;
    let j2t_ident = make_ident_j2t!(gadt_ident);

    match &gadt.inner {
        GInner::Struct(GInnerStruct::Unit) => {
            generated_chunk.add_comment("impl_j2t for struct (unit)");
            generated_chunk.add_ts_import(TSImport::granite_type("JsonValue"));
            generated_chunk.add_ts_import(TSImport::granite_type("Result"));
            generated_chunk.add_code(quote! {
                export function #j2t_ident(_data: JsonValue): Result<#gadt_ident, string> {
                    if (_data === true) {
                        return { Ok: _data };
                    } else {
                        return { Err: "Unit: wrong type: " + typeof _data };
                    }
                };
            });
        }
        GInner::Struct(GInnerStruct::Tuple(singles)) => {
            let mut terms1 = Vec::with_capacity(singles.len());
            let mut terms2 = Vec::with_capacity(singles.len());

            for (index, (_vis, gtype)) in singles.iter().enumerate() {
                gtype.import_j2t(&mut generated_chunk.imports);
                let index = proc_macro2::Literal::usize_unsuffixed(index);
                let tuple_field_name = Ident::new(&format!("v{}", index), Span::call_site());
                let field_j2t_call = gtype.callable_j2t();
                let default_j = gtype.default_j();

                terms1.push(quote! {
                    const #tuple_field_name = #field_j2t_call(_data[#index] #default_j);
                    if ("Err" in #tuple_field_name) {
                        return { Err: #tuple_field_name.Err };
                    }
                });
                terms2.push(quote! {
                    #tuple_field_name.Ok
                });
            }

            generated_chunk.add_comment("impl_j2t for struct (tuple)");
            generated_chunk.add_ts_import(TSImport::granite_type("JsonValue"));
            generated_chunk.add_ts_import(TSImport::granite_type("Result"));
            generated_chunk.add_code(quote! {
                export function #j2t_ident(_data: JsonValue): Result<#gadt_ident, string> {
                    // ensure it is an actual object that is an array
                    if (typeof _data === "object" && _data !== null && Array.isArray(_data)) {
                        #(#terms1)*
                        return { Ok: [#(#terms2),*] };
                    } else {
                        return { Err: "Tuple: wrong type: " + typeof _data };
                    }
                };
            });
        }
        GInner::Struct(GInnerStruct::Named(vis_ident_gtype_list)) => {
            let mut terms1 = Vec::with_capacity(vis_ident_gtype_list.len());
            let mut terms2 = Vec::with_capacity(vis_ident_gtype_list.len());

            for (_vis, struct_field_ident, gtype) in vis_ident_gtype_list {
                gtype.import_j2t(&mut generated_chunk.imports);
                let struct_field_ident_string = struct_field_ident.to_string();
                let field_j2t_call = gtype.callable_j2t();
                let default_j = gtype.default_j();

                terms1.push(quote! {
                    const #struct_field_ident = #field_j2t_call(_data[#struct_field_ident_string] #default_j);
                    if ("Err" in #struct_field_ident) {
                        return { Err: #struct_field_ident.Err };
                    }
                });
                terms2.push(quote! {
                    #struct_field_ident: #struct_field_ident.Ok
                });
            }

            generated_chunk.add_comment("impl_j2t for struct (named)");
            generated_chunk.add_ts_import(TSImport::granite_type("JsonValue"));
            generated_chunk.add_ts_import(TSImport::granite_type("Result"));
            generated_chunk.add_code(quote! {
                export function #j2t_ident(_data: JsonValue): Result<#gadt_ident, string> {
                    // ensure it is an actual object that is not an array
                    if (typeof _data === "object" && _data !== null && !Array.isArray(_data)) {
                        #(#terms1)*
                        return { Ok: {#(#terms2),*} };
                    } else {
                        return { Err: "Named: wrong type: " + typeof _data };
                    }
                };
            });
        }
        GInner::Enum(GInnerEnum(ident_gvariant_list)) => {
            let mut terms = Vec::with_capacity(ident_gvariant_list.len());
            for (variant_ident, gvariant) in ident_gvariant_list {
                let variant_ident_string = variant_ident.to_string();
                let variant_ident_invalid_string_e = format!(
                    "Enum: invalid variant - add variant: {}",
                    variant_ident_string
                );
                match gvariant {
                    GVariant::Unit => {
                        terms.push(quote! {
                            if (#variant_ident_string in _data) {
                                return { Ok: {#variant_ident: true} };
                            }
                        });
                    }

                    // tuple variant: { variant: [...] }
                    GVariant::Tuple(singles) => {
                        let gtype_list_len: usize = singles.len();
                        let mut terms1 = Vec::with_capacity(gtype_list_len);
                        let mut terms2 = Vec::with_capacity(gtype_list_len);

                        for (index, (gtype,)) in singles.iter().enumerate() {
                            gtype.import_j2t(&mut generated_chunk.imports);
                            let index = proc_macro2::Literal::usize_unsuffixed(index);
                            let tuple_field_ident =
                                Ident::new(&format!("v{}", index), Span::call_site());
                            let tuple_field_j2t_call = gtype.callable_j2t();
                            let default_j = gtype.default_j();

                            terms1.push(quote! {
                                const #tuple_field_ident = #tuple_field_j2t_call(arr[#index] #default_j);
                                if ("Err" in #tuple_field_ident) {
                                    return { Err: #tuple_field_ident.Err };
                                }
                            });
                            terms2.push(quote! { #tuple_field_ident.Ok });
                        }

                        terms.push(quote! {
                            // Ensure the variant is an object with the correct key
                            if (#variant_ident_string in _data) {
                                const arr = _data[#variant_ident_string];
                                // ensure the value of that key is an array
                                if (typeof arr === "object" && arr !== null && Array.isArray(arr)) {
                                    #(#terms1)*
                                    return { Ok: { #variant_ident: [#(#terms2),*] } };
                                }
                                return { Err: #variant_ident_invalid_string_e };
                            }
                        });
                    }
                    GVariant::Struct(doubles) => {
                        let ident_gtype_list_len: usize = doubles.len();
                        let mut terms1 = Vec::with_capacity(ident_gtype_list_len);
                        let mut terms2 = Vec::with_capacity(ident_gtype_list_len);

                        for (named_field_ident, gtype) in doubles {
                            gtype.import_j2t(&mut generated_chunk.imports);
                            let named_field_ident_string = named_field_ident.to_string();
                            let named_field_j2t_call = gtype.callable_j2t();
                            let default_j = gtype.default_j();

                            terms1.push(quote! {
                                const #named_field_ident = #named_field_j2t_call(obj[#named_field_ident_string] #default_j);
                                if ("Err" in #named_field_ident) {
                                    return { Err: #named_field_ident.Err };
                                }
                            });

                            terms2.push(quote! { #named_field_ident: #named_field_ident.Ok });
                        }

                        terms.push(quote! {
                            // Ensure the variant is an object with the correct key
                            if (#variant_ident_string in _data) {
                                const obj = _data[#variant_ident_string];
                                // ensure the value of that key is an object
                                if (typeof obj === "object" && obj !== null && !Array.isArray(obj)) {
                                    #(#terms1)*
                                    return { Ok: { #variant_ident: {#(#terms2),*} } };
                                }
                                return { Err: #variant_ident_invalid_string_e };
                            }
                        });
                    }
                }
            }

            generated_chunk.add_comment("impl_j2t for enum");
            generated_chunk.add_ts_import(TSImport::granite_type("JsonValue"));
            generated_chunk.add_ts_import(TSImport::granite_type("Result"));
            generated_chunk.add_code(quote! {
                export function #j2t_ident(_data: JsonValue): Result<#gadt_ident, string> {
                    if (typeof _data === "object" && _data !== null && !Array.isArray(_data)) {
                        #(#terms)*
                        return { Err: "Enum: invalid variant" };
                    }
                    return { Err: "Enum: wrong type: " + typeof _data };
                };
            });
        }
        GInner::Type(GInnerType { .. }) => {
            if let Some(typescript_import) = &gadt.ts_type_from {
                let j2t = make_ident_j2t!(gadt_ident).to_string();
                generated_chunk.add_ts_import(TSImport::other_item(&j2t, typescript_import))
            } else {
                unreachable!("Due to validation of attribute parser, this should be unreachable");
            }
        }
    }
}

trait GTypeExtType {
    fn type_t(&self) -> TokenStream;
    fn type_p(&self) -> TokenStream;
    fn type_e(&self) -> TokenStream;
}

trait GTypeExtIdent {
    fn ident_t2t(&self) -> Ident;
    //fn ident_t2p(&self) -> Ident; - Never used
    fn ident_t2j(&self) -> Ident;
    fn ident_p2t(&self) -> Ident;
    fn ident_p2j(&self) -> Ident;
    //fn ident_p2e(&self) -> Ident; - Never used
    fn ident_j2t(&self) -> Ident;
    fn ident_j2p(&self) -> Ident;
    fn ident_j2e(&self) -> Ident;
    //fn ident_e2j(&self) -> Ident; - Never used
}

trait GTypeExtImport {
    fn import_t(&self, imports: &mut HashSet<TSImport>);
    fn import_p(&self, imports: &mut HashSet<TSImport>);
    fn import_e(&self, imports: &mut HashSet<TSImport>);
    fn import_t2t(&self, imports: &mut HashSet<TSImport>);
    //fn import_t2p(&self, imports: &mut HashSet<TSImport>); - Unused
    fn import_t2j(&self, imports: &mut HashSet<TSImport>);
    fn import_p2t(&self, imports: &mut HashSet<TSImport>);
    fn import_p2j(&self, imports: &mut HashSet<TSImport>);
    //fn import_p2e(&self, imports: &mut HashSet<TSImport>); - Unused
    fn import_j2t(&self, imports: &mut HashSet<TSImport>);
    fn import_j2p(&self, imports: &mut HashSet<TSImport>);
    fn import_j2e(&self, imports: &mut HashSet<TSImport>);
    //fn import_e2j(&self, imports: &mut HashSet<TSImport>); - Unused
}

trait GTypeExtCallable {
    fn callable_t2t(&self) -> TokenStream;
    //fn callable_t2p(&self) -> TokenStream; - Unused
    fn callable_t2j(&self) -> TokenStream;
    fn callable_p2t(&self) -> TokenStream;
    fn callable_p2j(&self) -> TokenStream;
    //fn callable_p2e(&self) -> TokenStream; - Unused
    fn callable_j2t(&self) -> TokenStream;
    fn callable_j2p(&self) -> TokenStream;
    fn callable_j2e(&self) -> TokenStream;
    //fn callable_e2j(&self) -> TokenStream; - Unused
}

trait GTypeExtDefault {
    fn default_j(&self) -> TokenStream;
    fn default_p(&self) -> TokenStream;
}

trait GTypeExtValidation {
    fn validation(&self, _vterms: &mut Vec<TokenStream>) {}
}

impl GTypeExtType for GType {
    fn type_t(&self) -> TokenStream {
        match self {
            GType::GType(ident) => ident.to_token_stream(),
            GType::Scalar(gscalar) => gscalar.type_t(),
            GType::Option {
                some_type: vtype, ..
            } => {
                let typ = vtype.type_t();
                quote! { Option<#typ> }
            }
            GType::Result(ok, err) => {
                let typ_ok = ok.type_t();
                let typ_err = err.type_t();
                quote! { Result<#typ_ok, #typ_err> }
            }
            GType::Vec(gtype) => {
                let typ = gtype.type_t();
                quote! { Vec<#typ> }
            }
            GType::HashSet(gscalar) => {
                let typ = gscalar.type_t();
                quote! { HashSet<#typ> }
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                let typ_key = ktype.type_t();
                let typ_value = vtype.type_t();
                quote! { HashMap<#typ_key, #typ_value> }
            }
            GType::BTreeMap(key_gscalar, value_gtype) => {
                let typ_key = key_gscalar.type_t();
                let typ_value = value_gtype.type_t();
                quote! { BTreeMap<#typ_key, #typ_value> }
            }
            GType::IndexMap(key_gscalar, value_gtype) => {
                let typ_key = key_gscalar.type_t();
                let typ_value = value_gtype.type_t();
                quote! { IndexMap<#typ_key, #typ_value> }
            }
            GType::JsonValue => {
                quote! {
                    JsonValue
                }
            }
            GType::JsonObject => {
                quote! {
                    JsonObject
                }
            }
            GType::JsonArray => {
                quote! {
                    JsonArray
                }
            }
            GType::Range(_scalar) => {
                todo!("not implemented yet");
            }
            GType::NestedError(gtype) => {
                let inner_token = gtype.type_t();
                quote! {
                    NestedError<#inner_token>
                }
            }
        }
    }
    fn type_p(&self) -> TokenStream {
        match self {
            GType::GType(ident) => {
                let type_p = mi_partial!(ident);
                quote! {
                    #type_p
                }
            }
            GType::Scalar(scalar) => GScalar::type_p(scalar),
            GType::Option {
                some_type: vtype, ..
            } => {
                let p_type = vtype.type_p();
                quote! {
                    Option_p<#p_type>
                }
            }
            GType::Result(ok, err) => {
                let type_p_ok = ok.type_p();
                let type_p_err = err.type_p();
                quote! {
                    Result_p<#type_p_ok, #type_p_err>
                }
            }
            GType::Vec(inner) => {
                let type_p = inner.type_p();
                quote! {
                    Vec_p<#type_p>
                }
            }
            GType::HashSet(inner) => {
                let type_p = inner.type_p();
                quote! {
                    HashSet_p<#type_p>
                }
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                let type_t_key = ktype.type_t();
                let type_p_value = vtype.type_p();
                quote! {
                    HashMap_p<#type_t_key, #type_p_value>
                }
            }
            GType::BTreeMap(key, value) => {
                let type_p_key = key.type_p();
                let type_p_value = value.type_p();
                quote! {
                    BTreeMap_p<#type_p_key, #type_p_value>
                }
            }
            GType::IndexMap(key, value) => {
                let type_p_key = key.type_p();
                let type_p_value = value.type_p();
                quote! {
                    IndexMap_p<#type_p_key, #type_p_value>
                }
            }
            GType::Range(scalar) => {
                let type_p_scalar = scalar.type_p();
                quote! {
                    Range_p<#type_p_scalar>
                }
            }
            GType::JsonValue => {
                quote! {
                    JsonValue_p
                }
            }
            GType::JsonObject => {
                quote! {
                    JsonObject_p
                }
            }
            GType::JsonArray => {
                quote! {
                    JsonArray_p
                }
            }
            GType::NestedError(gtype) => {
                let inner_token = gtype.type_p();
                quote! {
                    NestedError_p<#inner_token>
                }
            }
        }
    }
    fn type_e(&self) -> TokenStream {
        match self {
            GType::GType(ident) => {
                let type_e = mi_error!(ident);
                quote! {
                    #type_e
                }
            }
            GType::Scalar(scalar) => scalar.type_e(),
            GType::Option {
                some_type: vtype, ..
            } => {
                let type_e = vtype.type_e();
                quote! {
                    Option_e<#type_e>
                }
            }
            // Result<T,E> only error mode is a string | undefined.
            GType::Result(ok, err) => {
                let ok_e = ok.type_e();
                let err_e = err.type_e();
                quote! {
                    Result_e<#ok_e, #err_e>
                }
            }
            GType::Vec(inner) => {
                let type_e = inner.type_e();
                quote! {
                    Vec_e<#type_e>
                }
            }
            GType::HashSet(inner) => {
                let type_e = inner.type_e();
                quote! {
                    HashSet_e<#type_e>
                }
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                let type_e_key = ktype.type_e();
                let type_e_value = vtype.type_e();
                quote! {
                    HashMap_e<#type_e_key, #type_e_value>
                }
            }
            GType::BTreeMap(key, value) => {
                let type_e_key = key.type_e();
                let type_e_value = value.type_e();
                quote! {
                    BTreeMap_e<#type_e_key, #type_e_value>
                }
            }
            GType::IndexMap(key, value) => {
                let type_e_key = key.type_e();
                let type_e_value = value.type_e();
                quote! {
                    IndexMap_e<#type_e_key, #type_e_value>
                }
            }
            GType::Range(scalar) => {
                let type_e_scalar = scalar.type_e();
                quote! {
                    Range_e<#type_e_scalar>
                }
            }
            GType::JsonValue => {
                quote! {
                    JsonValue_e
                }
            }
            GType::JsonObject => {
                quote! {
                    JsonObject_e
                }
            }
            GType::JsonArray => {
                quote! {
                    JsonArray_e
                }
            }
            GType::NestedError(gtype) => {
                let inner_token = gtype.type_e();
                quote! {
                    NestedError_e<#inner_token>
                }
            }
        }
    }
}

impl GTypeExtType for GScalar {
    fn type_t(&self) -> TokenStream {
        match self {
            GScalar::char { .. } => quote! { char },
            GScalar::bool { .. } => quote! { boolean },
            GScalar::i8 { .. } => quote! { i8 },
            GScalar::u8 { .. } => quote! { u8 },
            GScalar::i16 { .. } => quote! { i16 },
            GScalar::u16 { .. } => quote! { u16 },
            GScalar::i32 { .. } => quote! { i32 },
            GScalar::u32 { .. } => quote! { u32 },
            GScalar::i64 { .. } => quote! { i64 },
            GScalar::u64 { .. } => quote! { u64 },
            GScalar::f32 { .. } => quote! { f32 },
            GScalar::f64 { .. } => quote! { f64 },
            GScalar::String { .. } => quote! { string }, // this is the only exception because `String` means something in TS
            GScalar::Uuid { .. } => quote! { Uuid }, // uuid is not native type in javascript - we store it as string
            GScalar::Decimal { .. } => quote! { Decimal },
            GScalar::Integer => quote! { Integer },
            GScalar::BigInt => quote! { BigInt },
            GScalar::DateUtc => quote! { DateUtc },
            GScalar::DateTz => quote! { DateTz },
            GScalar::DateTimeUtc => quote! { DateTimeUtc },
            GScalar::DateTimeTz => quote! { DateTimeTz },
            GScalar::Duration => quote! { Duration },
            GScalar::Time => quote! { Time },
        }
    }
    fn type_p(&self) -> TokenStream {
        match self {
            GScalar::char { .. } => quote! { char_p },
            GScalar::bool { .. } => quote! { boolean_p },
            GScalar::i8 { .. } => quote! { i8_p },
            GScalar::u8 { .. } => quote! { u8_p },
            GScalar::i16 { .. } => quote! { i16_p },
            GScalar::u16 { .. } => quote! { u16_p },
            GScalar::i32 { .. } => quote! { i32_p },
            GScalar::u32 { .. } => quote! { u32_p },
            GScalar::i64 { .. } => quote! { i64_p },
            GScalar::u64 { .. } => quote! { u64_p },
            GScalar::f32 { .. } => quote! { f32_p },
            GScalar::f64 { .. } => quote! { f64_p },
            GScalar::String { .. } => quote! { string_p },
            GScalar::Uuid { .. } => quote! { Uuid_p },
            GScalar::Decimal { .. } => quote! { Decimal_p },
            GScalar::Integer => quote! { Integer_p },
            GScalar::BigInt => quote! { BigInt_p },
            GScalar::DateUtc => quote! { DateUtc_p },
            GScalar::DateTz => quote! { DateTz_p },
            GScalar::DateTimeUtc => quote! { DateTimeUtc_p },
            GScalar::DateTimeTz => quote! { DateTimeTz_p },
            GScalar::Duration => quote! { Duration_p },
            GScalar::Time => quote! { Time_p },
        }
    }
    fn type_e(&self) -> TokenStream {
        match self {
            GScalar::char { .. } => quote! { char_e },
            GScalar::bool { .. } => quote! { boolean_e },
            GScalar::i8 { .. } => quote! { i8_e },
            GScalar::u8 { .. } => quote! { u8_e },
            GScalar::i16 { .. } => quote! { i16_e },
            GScalar::u16 { .. } => quote! { u16_e },
            GScalar::i32 { .. } => quote! { i32_e },
            GScalar::u32 { .. } => quote! { u32_e },
            GScalar::i64 { .. } => quote! { i64_e },
            GScalar::u64 { .. } => quote! { u64_e },
            GScalar::f32 { .. } => quote! { f32_e },
            GScalar::f64 { .. } => quote! { f64_e },
            GScalar::String { .. } => quote! { string_e },
            GScalar::Uuid { .. } => quote! { Uuid_e },
            GScalar::Decimal { .. } => quote! { Decimal_e },
            GScalar::Integer => quote! { Integer_e },
            GScalar::BigInt => quote! { BigInt_e },
            GScalar::DateUtc => quote! { DateUtc_e },
            GScalar::DateTz => quote! { DateTz_e },
            GScalar::DateTimeUtc => quote! { DateTimeUtc_e },
            GScalar::DateTimeTz => quote! { DateTimeTz_e },
            GScalar::Duration => quote! { Duration_e },
            GScalar::Time => quote! { Time_e },
        }
    }
}

impl GTypeExtIdent for GType {
    fn ident_t2t(&self) -> Ident {
        match &self {
            GType::GType(ident) => {
                mi_validate_result_error!(ident)
            }
            GType::Scalar(scalar) => scalar.ident_t2t(),
            GType::Option { .. } => ident_str!("Option_t2t"),
            GType::Result(_ok, _err) => ident_str!("Result_t2t"),
            GType::Vec(_inner) => ident_str!("Vec_t2t"),
            GType::HashSet(_inner) => ident_str!("HashSet_t2t"),
            GType::HashMap { .. } => ident_str!("HashMap_t2t"),
            GType::BTreeMap(_key, _value) => ident_str!("BTreeMap_t2t"),
            GType::IndexMap(_key, _value) => ident_str!("IndexMap_t2t"),
            GType::Range(_scalar) => ident_str!("Range_t2t"),
            GType::JsonValue => ident_str!("JsonValue_t2t"),
            GType::JsonObject => ident_str!("JsonObject_t2t"),
            GType::JsonArray => ident_str!("JsonArray_t2t"),
            GType::NestedError(_inner) => ident_str!("NestedError_t2t"),
        }
    }

    fn ident_t2j(&self) -> Ident {
        match &self {
            GType::GType(ident) => {
                make_ident_t2j!(ident)
            }
            GType::Scalar(scalar) => scalar.ident_t2j(),
            GType::Option { .. } => ident_str!("Option_t2j"),
            GType::Result(_ok, _err) => ident_str!("Result_t2j"),
            GType::Vec(_inner) => ident_str!("Vec_t2j"),
            GType::HashSet(_inner) => ident_str!("HashSet_t2j"),
            GType::HashMap { .. } => ident_str!("HashMap_t2j"),
            GType::BTreeMap(_key, _value) => ident_str!("BTreeMap_t2j"),
            GType::IndexMap(_key, _value) => ident_str!("IndexMap_t2j"),
            GType::Range(_scalar) => ident_str!("Range_t2j"),
            GType::JsonValue => ident_str!("JsonValue_t2j"),
            GType::JsonObject => ident_str!("JsonObject_t2j"),
            GType::JsonArray => ident_str!("JsonArray_t2j"),
            GType::NestedError(_inner) => ident_str!("NestedError_t2j"),
        }
    }

    fn ident_p2t(&self) -> Ident {
        match &self {
            GType::GType(ident) => {
                make_ident_p2t!(ident)
            }
            GType::Scalar(scalar) => scalar.ident_p2t(),
            GType::Option { .. } => ident_str!("Option_p2t"),
            GType::Result(_ok, _err) => ident_str!("Result_p2t"),
            GType::Vec(_inner) => ident_str!("Vec_p2t"),
            GType::HashSet(_inner) => ident_str!("HashSet_p2t"),
            GType::HashMap { .. } => ident_str!("HashMap_p2t"),
            GType::BTreeMap(_key, _value) => ident_str!("BTreeMap_p2t"),
            GType::IndexMap(_key, _value) => ident_str!("IndexMap_p2t"),
            GType::Range(_scalar) => ident_str!("Range_p2t"),
            GType::JsonValue => ident_str!("JsonValue_p2t"),
            GType::JsonObject => ident_str!("JsonObject_p2t"),
            GType::JsonArray => ident_str!("JsonArray_p2t"),
            GType::NestedError(_inner) => ident_str!("NestedError_p2t"),
        }
    }

    fn ident_p2j(&self) -> Ident {
        match &self {
            GType::GType(ident) => {
                make_ident_p2j!(ident)
            }
            GType::Scalar(scalar) => scalar.ident_p2j(),
            GType::Option { .. } => ident_str!("Option_p2j"),
            GType::Result(_ok, _err) => ident_str!("Result_p2j"),
            GType::Vec(_inner) => ident_str!("Vec_p2j"),
            GType::HashSet(_inner) => ident_str!("HashSet_p2j"),
            GType::HashMap { .. } => ident_str!("HashMap_p2j"),
            GType::BTreeMap(_key, _value) => ident_str!("BTreeMap_p2j"),
            GType::IndexMap(_key, _value) => ident_str!("IndexMap_p2j"),
            GType::Range(_scalar) => ident_str!("Range_p2j"),
            GType::JsonValue => ident_str!("JsonValue_p2j"),
            GType::JsonObject => ident_str!("JsonObject_p2j"),
            GType::JsonArray => ident_str!("JsonArray_p2j"),
            GType::NestedError(_inner) => ident_str!("NestedError_p2j"),
        }
    }

    fn ident_j2t(&self) -> Ident {
        match &self {
            GType::GType(ident) => {
                make_ident_j2t!(ident)
            }
            GType::Scalar(scalar) => scalar.ident_j2t(),
            GType::Option { .. } => ident_str!("Option_j2t"),
            GType::Result(_ok, _err) => ident_str!("Result_j2t"),
            GType::Vec(_inner) => ident_str!("Vec_j2t"),
            GType::HashSet(_inner) => ident_str!("HashSet_j2t"),
            GType::HashMap { .. } => ident_str!("HashMap_j2t"),
            GType::BTreeMap(_key, _value) => ident_str!("BTreeMap_j2t"),
            GType::IndexMap(_key, _value) => ident_str!("IndexMap_j2t"),
            GType::Range(_scalar) => ident_str!("Range_j2t"),
            GType::JsonValue => ident_str!("JsonValue_j2t"),
            GType::JsonObject => ident_str!("JsonObject_j2t"),
            GType::JsonArray => ident_str!("JsonArray_j2t"),
            GType::NestedError(_inner) => ident_str!("NestedError_j2t"),
        }
    }

    fn ident_j2p(&self) -> Ident {
        match &self {
            GType::GType(ident) => {
                make_ident_j2p!(ident)
            }
            GType::Scalar(scalar) => scalar.ident_j2p(),
            GType::Option { .. } => ident_str!("Option_j2p"),
            GType::Result(_ok, _err) => ident_str!("Result_j2p"),
            GType::Vec(_inner) => ident_str!("Vec_j2p"),
            GType::HashSet(_inner) => ident_str!("HashSet_j2p"),
            GType::HashMap { .. } => ident_str!("HashMap_j2p"),
            GType::BTreeMap(_key, _value) => ident_str!("BTreeMap_j2p"),
            GType::IndexMap(_key, _value) => ident_str!("IndexMap_j2p"),
            GType::Range(_scalar) => ident_str!("Range_j2p"),
            GType::JsonValue => ident_str!("JsonValue_j2p"),
            GType::JsonObject => ident_str!("JsonObject_j2p"),
            GType::JsonArray => ident_str!("JsonArray_j2p"),
            GType::NestedError(_inner) => ident_str!("NestedError_j2p"),
        }
    }

    fn ident_j2e(&self) -> Ident {
        match &self {
            GType::GType(ident) => make_ident_j2e!(ident),
            GType::Scalar(scalar) => scalar.ident_j2e(),
            GType::Option { .. } => ident_str!("Option_j2e"),
            GType::Result(_ok, _err) => ident_str!("Result_j2e"),
            GType::Vec(_inner) => ident_str!("Vec_j2e"),
            GType::HashSet(_inner) => ident_str!("HashSet_j2e"),
            GType::HashMap { .. } => ident_str!("HashMap_j2e"),
            GType::BTreeMap(_key, _value) => ident_str!("BTreeMap_j2e"),
            GType::IndexMap(_key, _value) => ident_str!("IndexMap_j2e"),
            GType::Range(_scalar) => ident_str!("Range_j2e"),
            GType::JsonValue => ident_str!("JsonValue_j2e"),
            GType::JsonObject => ident_str!("JsonObject_j2e"),
            GType::JsonArray => ident_str!("JsonArray_j2e"),
            GType::NestedError(_inner) => ident_str!("NestedError_j2e"),
        }
    }
}

impl GTypeExtIdent for GScalar {
    fn ident_t2t(&self) -> Ident {
        match self {
            GScalar::char { .. } => ident_str!("char_t2t"),
            GScalar::bool { .. } => ident_str!("boolean_t2t"),
            GScalar::i8 { .. } => ident_str!("i8_t2t"),
            GScalar::u8 { .. } => ident_str!("u8_t2t"),
            GScalar::i16 { .. } => ident_str!("i16_t2t"),
            GScalar::u16 { .. } => ident_str!("u16_t2t"),
            GScalar::i32 { .. } => ident_str!("i32_t2t"),
            GScalar::u32 { .. } => ident_str!("u32_t2t"),
            GScalar::i64 { .. } => ident_str!("i64_t2t"),
            GScalar::u64 { .. } => ident_str!("u64_t2t"),
            GScalar::f32 { .. } => ident_str!("f32_t2t"),
            GScalar::f64 { .. } => ident_str!("f64_t2t"),
            GScalar::String { .. } => ident_str!("string_t2t"),
            GScalar::Uuid { .. } => ident_str!("Uuid_t2t"),
            GScalar::Decimal { .. } => ident_str!("Decimal_t2t"),
            GScalar::Integer => ident_str!("Integer_t2t"),
            GScalar::BigInt => ident_str!("BigInt_t2t"),
            GScalar::DateUtc => ident_str!("DateUtc_t2t"),
            GScalar::DateTz => ident_str!("DateTz_t2t"),
            GScalar::DateTimeUtc => ident_str!("DateTimeUtc_t2t"),
            GScalar::DateTimeTz => ident_str!("DateTimeTz_t2t"),
            GScalar::Duration => ident_str!("Duration_t2t"),
            GScalar::Time => ident_str!("Time_t2t"),
        }
    }
    fn ident_t2j(&self) -> Ident {
        match self {
            GScalar::char { .. } => ident_str!("char_t2j"),
            GScalar::bool { .. } => ident_str!("boolean_t2j"),
            GScalar::i8 { .. } => ident_str!("i8_t2j"),
            GScalar::u8 { .. } => ident_str!("u8_t2j"),
            GScalar::i16 { .. } => ident_str!("i16_t2j"),
            GScalar::u16 { .. } => ident_str!("u16_t2j"),
            GScalar::i32 { .. } => ident_str!("i32_t2j"),
            GScalar::u32 { .. } => ident_str!("u32_t2j"),
            GScalar::i64 { .. } => ident_str!("i64_t2j"),
            GScalar::u64 { .. } => ident_str!("u64_t2j"),
            GScalar::f32 { .. } => ident_str!("f32_t2j"),
            GScalar::f64 { .. } => ident_str!("f64_t2j"),
            GScalar::String { .. } => ident_str!("string_t2j"),
            GScalar::Uuid { .. } => ident_str!("Uuid_t2j"),
            GScalar::Decimal { .. } => ident_str!("Decimal_t2j"),
            GScalar::Integer => ident_str!("Integer_t2j"),
            GScalar::BigInt => ident_str!("BigInt_t2j"),
            GScalar::DateUtc => ident_str!("DateUtc_t2j"),
            GScalar::DateTz => ident_str!("DateTz_t2j"),
            GScalar::DateTimeUtc => ident_str!("DateTimeUtc_t2j"),
            GScalar::DateTimeTz => ident_str!("DateTimeTz_t2j"),
            GScalar::Duration => ident_str!("Duration_t2j"),
            GScalar::Time => ident_str!("Time_t2j"),
        }
    }
    fn ident_p2t(&self) -> Ident {
        match self {
            GScalar::char { .. } => ident_str!("char_p2t"),
            GScalar::bool { .. } => ident_str!("boolean_p2t"),
            GScalar::i8 { .. } => ident_str!("i8_p2t"),
            GScalar::u8 { .. } => ident_str!("u8_p2t"),
            GScalar::i16 { .. } => ident_str!("i16_p2t"),
            GScalar::u16 { .. } => ident_str!("u16_p2t"),
            GScalar::i32 { .. } => ident_str!("i32_p2t"),
            GScalar::u32 { .. } => ident_str!("u32_p2t"),
            GScalar::i64 { .. } => ident_str!("i64_p2t"),
            GScalar::u64 { .. } => ident_str!("u64_p2t"),
            GScalar::f32 { .. } => ident_str!("f32_p2t"),
            GScalar::f64 { .. } => ident_str!("f64_p2t"),
            GScalar::String { .. } => ident_str!("string_p2t"),
            GScalar::Uuid { .. } => ident_str!("Uuid_p2t"),
            GScalar::Decimal { .. } => ident_str!("Decimal_p2t"),
            GScalar::Integer => ident_str!("Integer_p2t"),
            GScalar::BigInt => ident_str!("BigInt_p2t"),
            GScalar::DateUtc => ident_str!("DateUtc_p2t"),
            GScalar::DateTz => ident_str!("DateTz_p2t"),
            GScalar::DateTimeUtc => ident_str!("DateTimeUtc_p2t"),
            GScalar::DateTimeTz => ident_str!("DateTimeTz_p2t"),
            GScalar::Duration => ident_str!("Duration_p2t"),
            GScalar::Time => ident_str!("Time_p2t"),
        }
    }
    fn ident_p2j(&self) -> Ident {
        match self {
            GScalar::char { .. } => ident_str!("char_p2j"),
            GScalar::bool { .. } => ident_str!("boolean_p2j"),
            GScalar::i8 { .. } => ident_str!("i8_p2j"),
            GScalar::u8 { .. } => ident_str!("u8_p2j"),
            GScalar::i16 { .. } => ident_str!("i16_p2j"),
            GScalar::u16 { .. } => ident_str!("u16_p2j"),
            GScalar::i32 { .. } => ident_str!("i32_p2j"),
            GScalar::u32 { .. } => ident_str!("u32_p2j"),
            GScalar::i64 { .. } => ident_str!("i64_p2j"),
            GScalar::u64 { .. } => ident_str!("u64_p2j"),
            GScalar::f32 { .. } => ident_str!("f32_p2j"),
            GScalar::f64 { .. } => ident_str!("f64_p2j"),
            GScalar::String { .. } => ident_str!("string_p2j"),
            GScalar::Uuid { .. } => ident_str!("Uuid_p2j"),
            GScalar::Decimal { .. } => ident_str!("Decimal_p2j"),
            GScalar::Integer => ident_str!("Integer_p2j"),
            GScalar::BigInt => ident_str!("BigInt_p2j"),
            GScalar::DateUtc => ident_str!("DateUtc_p2j"),
            GScalar::DateTz => ident_str!("DateTz_p2j"),
            GScalar::DateTimeUtc => ident_str!("DateTimeUtc_p2j"),
            GScalar::DateTimeTz => ident_str!("DateTimeTz_p2j"),
            GScalar::Duration => ident_str!("Duration_p2j"),
            GScalar::Time => ident_str!("Time_p2j"),
        }
    }
    fn ident_j2t(&self) -> Ident {
        match self {
            GScalar::char { .. } => ident_str!("char_j2t"),
            GScalar::bool { .. } => ident_str!("boolean_j2t"),
            GScalar::i8 { .. } => ident_str!("i8_j2t"),
            GScalar::u8 { .. } => ident_str!("u8_j2t"),
            GScalar::i16 { .. } => ident_str!("i16_j2t"),
            GScalar::u16 { .. } => ident_str!("u16_j2t"),
            GScalar::i32 { .. } => ident_str!("i32_j2t"),
            GScalar::u32 { .. } => ident_str!("u32_j2t"),
            GScalar::i64 { .. } => ident_str!("i64_j2t"),
            GScalar::u64 { .. } => ident_str!("u64_j2t"),
            GScalar::f32 { .. } => ident_str!("f32_j2t"),
            GScalar::f64 { .. } => ident_str!("f64_j2t"),
            GScalar::String { .. } => ident_str!("string_j2t"),
            GScalar::Uuid { .. } => ident_str!("Uuid_j2t"),
            GScalar::Decimal { .. } => ident_str!("Decimal_j2t"),
            GScalar::Integer => ident_str!("Integer_j2t"),
            GScalar::BigInt => ident_str!("BigInt_j2t"),
            GScalar::DateUtc => ident_str!("DateUtc_j2t"),
            GScalar::DateTz => ident_str!("DateTz_j2t"),
            GScalar::DateTimeUtc => ident_str!("DateTimeUtc_j2t"),
            GScalar::DateTimeTz => ident_str!("DateTimeTz_j2t"),
            GScalar::Duration => ident_str!("Duration_j2t"),
            GScalar::Time => ident_str!("Time_j2t"),
        }
    }
    fn ident_j2p(&self) -> Ident {
        match self {
            GScalar::char { .. } => ident_str!("char_j2p"),
            GScalar::bool { .. } => ident_str!("boolean_j2p"),
            GScalar::i8 { .. } => ident_str!("i8_j2p"),
            GScalar::u8 { .. } => ident_str!("u8_j2p"),
            GScalar::i16 { .. } => ident_str!("i16_j2p"),
            GScalar::u16 { .. } => ident_str!("u16_j2p"),
            GScalar::i32 { .. } => ident_str!("i32_j2p"),
            GScalar::u32 { .. } => ident_str!("u32_j2p"),
            GScalar::i64 { .. } => ident_str!("i64_j2p"),
            GScalar::u64 { .. } => ident_str!("u64_j2p"),
            GScalar::f32 { .. } => ident_str!("f32_j2p"),
            GScalar::f64 { .. } => ident_str!("f64_j2p"),
            GScalar::String { .. } => ident_str!("string_j2p"),
            GScalar::Uuid { .. } => ident_str!("Uuid_j2p"),
            GScalar::Decimal { .. } => ident_str!("Decimal_j2p"),
            GScalar::Integer => ident_str!("Integer_j2p"),
            GScalar::BigInt => ident_str!("BigInt_j2p"),
            GScalar::DateUtc => ident_str!("DateUtc_j2p"),
            GScalar::DateTz => ident_str!("DateTz_j2p"),
            GScalar::DateTimeUtc => ident_str!("DateTimeUtc_j2p"),
            GScalar::DateTimeTz => ident_str!("DateTimeTz_j2p"),
            GScalar::Duration => ident_str!("Duration_j2p"),
            GScalar::Time => ident_str!("Time_j2p"),
        }
    }
    fn ident_j2e(&self) -> Ident {
        ident_str!("string_j2e")
    }
}

impl GTypeExtImport for GType {
    fn import_t(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GType::GType(_ident) => {}
            GType::Scalar(gscalar) => {
                gscalar.import_t(imports);
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                imports.insert(TSImport::granite_type("Option"));
                vtype.import_t(imports);
            }
            GType::Result(ok, err) => {
                imports.insert(TSImport::granite_type("Result"));
                ok.import_t(imports);
                err.import_t(imports);
            }
            GType::Vec(item) => {
                imports.insert(TSImport::granite_type("Vec"));
                item.import_t(imports);
            }
            GType::HashSet(item) => {
                imports.insert(TSImport::granite_type("HashSet"));
                item.import_t(imports);
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                imports.insert(TSImport::granite_type("HashMap"));
                ktype.import_t(imports);
                vtype.import_t(imports);
            }
            GType::BTreeMap(key, value) => {
                imports.insert(TSImport::granite_type("BTreeMap"));
                key.import_t(imports);
                value.import_t(imports);
            }
            GType::IndexMap(key, value) => {
                imports.insert(TSImport::granite_type("IndexMap"));
                key.import_t(imports);
                value.import_t(imports);
            }
            GType::Range(item) => {
                imports.insert(TSImport::granite_type("Range"));
                item.import_t(imports);
            }
            GType::JsonValue => {
                imports.insert(TSImport::granite_type("JsonValue"));
            }
            GType::JsonObject => {
                imports.insert(TSImport::granite_type("JsonObject"));
            }
            GType::JsonArray => {
                imports.insert(TSImport::granite_type("JsonArray"));
            }
            GType::NestedError(gtype) => {
                imports.insert(TSImport::granite_type("NestedError"));
                gtype.import_t(imports);
            }
        }
    }

    fn import_p(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GType::GType(_ident) => {}
            GType::Scalar(gscalar) => {
                gscalar.import_p(imports);
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                imports.insert(TSImport::granite_item("Option_p"));
                vtype.import_p(imports);
            }
            GType::Result(ok, err) => {
                imports.insert(TSImport::granite_item("Result_p"));
                ok.import_p(imports);
                err.import_p(imports);
            }
            GType::Vec(item) => {
                imports.insert(TSImport::granite_item("Vec_p"));
                item.import_p(imports);
            }
            GType::HashSet(item) => {
                imports.insert(TSImport::granite_item("HashSet_p"));
                item.import_p(imports);
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                imports.insert(TSImport::granite_item("HashMap_p"));
                ktype.import_p(imports);
                vtype.import_p(imports);
            }
            GType::BTreeMap(key, value) => {
                imports.insert(TSImport::granite_item("BTreeMap_p"));
                key.import_p(imports);
                value.import_p(imports);
            }
            GType::IndexMap(key, value) => {
                imports.insert(TSImport::granite_item("IndexMap_p"));
                key.import_p(imports);
                value.import_p(imports);
            }
            GType::Range(item) => {
                imports.insert(TSImport::granite_item("Range_p"));
                item.import_p(imports);
            }
            GType::JsonValue => {
                imports.insert(TSImport::granite_item("JsonValue_p"));
            }
            GType::JsonObject => {
                imports.insert(TSImport::granite_item("JsonObject_p"));
            }
            GType::JsonArray => {
                imports.insert(TSImport::granite_item("JsonArray_p"));
            }
            GType::NestedError(gtype) => {
                imports.insert(TSImport::granite_item("NestedError_p"));
                gtype.import_p(imports);
            }
        }
    }

    fn import_e(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GType::GType(_ident) => {}
            GType::Scalar(gscalar) => {
                gscalar.import_e(imports);
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                imports.insert(TSImport::granite_item("Option_e"));
                vtype.import_e(imports);
            }
            GType::Result(ok, err) => {
                imports.insert(TSImport::granite_item("Result_e"));
                ok.import_e(imports);
                err.import_e(imports);
            }
            GType::Vec(item) => {
                imports.insert(TSImport::granite_item("Vec_e"));
                item.import_e(imports);
            }
            GType::HashSet(item) => {
                imports.insert(TSImport::granite_item("HashSet_e"));
                item.import_e(imports);
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                imports.insert(TSImport::granite_item("HashMap_e"));
                ktype.import_e(imports);
                vtype.import_e(imports);
            }
            GType::BTreeMap(key, value) => {
                imports.insert(TSImport::granite_item("BTreeMap_e"));
                key.import_e(imports);
                value.import_e(imports);
            }
            GType::IndexMap(key, value) => {
                imports.insert(TSImport::granite_item("IndexMap_e"));
                key.import_e(imports);
                value.import_e(imports);
            }
            GType::Range(item) => {
                imports.insert(TSImport::granite_item("Range_e"));
                item.import_e(imports);
            }
            GType::JsonValue => {
                imports.insert(TSImport::granite_item("JsonValue_e"));
            }
            GType::JsonObject => {
                imports.insert(TSImport::granite_item("JsonObject_e"));
            }
            GType::JsonArray => {
                imports.insert(TSImport::granite_item("JsonArray_e"));
            }
            GType::NestedError(gtype) => {
                imports.insert(TSImport::granite_item("NestedError_e"));
                gtype.import_e(imports);
            }
        }
    }

    fn import_t2t(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GType::GType(_ident) => {}
            GType::Scalar(gscalar) => {
                gscalar.import_t2t(imports);
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                imports.insert(TSImport::granite_item("Option_t2t"));
                vtype.import_t2t(imports);
            }
            GType::Result(ok, err) => {
                imports.insert(TSImport::granite_item("Result_t2t"));
                ok.import_t2t(imports);
                err.import_t2t(imports);
            }
            GType::Vec(item) => {
                imports.insert(TSImport::granite_item("Vec_t2t"));
                item.import_t2t(imports);
            }
            GType::HashSet(item) => {
                imports.insert(TSImport::granite_item("HashSet_t2t"));
                item.import_t2t(imports);
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                imports.insert(TSImport::granite_item("HashMap_t2t"));
                ktype.import_t2t(imports);
                vtype.import_t2t(imports);
            }
            GType::BTreeMap(key, value) => {
                imports.insert(TSImport::granite_item("BTreeMap_t2t"));
                key.import_t2t(imports);
                value.import_t2t(imports);
            }
            GType::IndexMap(key, value) => {
                imports.insert(TSImport::granite_item("IndexMap_t2t"));
                key.import_t2t(imports);
                value.import_t2t(imports);
            }
            GType::Range(item) => {
                imports.insert(TSImport::granite_item("Range_t2t"));
                item.import_t2t(imports);
            }
            GType::JsonValue => {
                imports.insert(TSImport::granite_item("JsonValue_t2t"));
            }
            GType::JsonObject => {
                imports.insert(TSImport::granite_item("JsonObject_t2t"));
            }
            GType::JsonArray => {
                imports.insert(TSImport::granite_item("JsonArray_t2t"));
            }
            GType::NestedError(gtype) => {
                imports.insert(TSImport::granite_item("NestedError_t2t"));
                gtype.import_t2t(imports);
            }
        }
    }

    fn import_t2j(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GType::GType(_ident) => {}
            GType::Scalar(gscalar) => {
                gscalar.import_t2j(imports);
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                imports.insert(TSImport::granite_item("Option_t2j"));
                vtype.import_t2j(imports);
            }
            GType::Result(ok, err) => {
                imports.insert(TSImport::granite_item("Result_t2j"));
                ok.import_t2j(imports);
                err.import_t2j(imports);
            }
            GType::Vec(item) => {
                imports.insert(TSImport::granite_item("Vec_t2j"));
                item.import_t2j(imports);
            }
            GType::HashSet(item) => {
                imports.insert(TSImport::granite_item("HashSet_t2j"));
                item.import_t2j(imports);
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                imports.insert(TSImport::granite_item("HashMap_t2j"));
                ktype.import_t2j(imports);
                vtype.import_t2j(imports);
            }
            GType::BTreeMap(key, value) => {
                imports.insert(TSImport::granite_item("BTreeMap_t2j"));
                key.import_t2j(imports);
                value.import_t2j(imports);
            }
            GType::IndexMap(key, value) => {
                imports.insert(TSImport::granite_item("IndexMap_t2j"));
                key.import_t2j(imports);
                value.import_t2j(imports);
            }
            GType::Range(item) => {
                imports.insert(TSImport::granite_item("Range_t2j"));
                item.import_t2j(imports);
            }
            GType::JsonValue => {
                imports.insert(TSImport::granite_item("JsonValue_t2j"));
            }
            GType::JsonObject => {
                imports.insert(TSImport::granite_item("JsonObject_t2j"));
            }
            GType::JsonArray => {
                imports.insert(TSImport::granite_item("JsonArray_t2j"));
            }
            GType::NestedError(gtype) => {
                imports.insert(TSImport::granite_item("NestedError_t2j"));
                gtype.import_t2j(imports);
            }
        }
    }

    fn import_p2t(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GType::GType(_ident) => {}
            GType::Scalar(gscalar) => {
                gscalar.import_p2t(imports);
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                imports.insert(TSImport::granite_item("Option_p2t"));
                vtype.import_p2t(imports);
            }
            GType::Result(ok, err) => {
                imports.insert(TSImport::granite_item("Result_p2t"));
                ok.import_p2t(imports);
                err.import_p2t(imports);
            }
            GType::Vec(item) => {
                imports.insert(TSImport::granite_item("Vec_p2t"));
                item.import_p2t(imports);
            }
            GType::HashSet(item) => {
                imports.insert(TSImport::granite_item("HashSet_p2t"));
                item.import_p2t(imports);
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                imports.insert(TSImport::granite_item("HashMap_p2t"));
                ktype.import_t2t(imports);
                vtype.import_p2t(imports);
            }
            GType::BTreeMap(key, value) => {
                imports.insert(TSImport::granite_item("BTreeMap_p2t"));
                key.import_p2t(imports);
                value.import_p2t(imports);
            }
            GType::IndexMap(key, value) => {
                imports.insert(TSImport::granite_item("IndexMap_p2t"));
                key.import_p2t(imports);
                value.import_p2t(imports);
            }
            GType::Range(item) => {
                imports.insert(TSImport::granite_item("Range_p2t"));
                item.import_p2t(imports);
            }
            GType::JsonValue => {
                imports.insert(TSImport::granite_item("JsonValue_p2t"));
            }
            GType::JsonObject => {
                imports.insert(TSImport::granite_item("JsonObject_p2t"));
            }
            GType::JsonArray => {
                imports.insert(TSImport::granite_item("JsonArray_p2t"));
            }
            GType::NestedError(gtype) => {
                imports.insert(TSImport::granite_item("NestedError_p2t"));
                gtype.import_p2t(imports);
            }
        }
    }

    fn import_p2j(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GType::GType(_ident) => {}
            GType::Scalar(gscalar) => {
                gscalar.import_p2j(imports);
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                imports.insert(TSImport::granite_item("Option_p2j"));
                vtype.import_p2j(imports);
            }
            GType::Result(ok, err) => {
                imports.insert(TSImport::granite_item("Result_p2j"));
                ok.import_p2j(imports);
                err.import_p2j(imports);
            }
            GType::Vec(item) => {
                imports.insert(TSImport::granite_item("Vec_p2j"));
                item.import_p2j(imports);
            }
            GType::HashSet(item) => {
                imports.insert(TSImport::granite_item("HashSet_p2j"));
                item.import_p2j(imports);
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                imports.insert(TSImport::granite_item("HashMap_p2j"));
                ktype.import_t2j(imports);
                vtype.import_p2j(imports);
            }
            GType::BTreeMap(key, value) => {
                imports.insert(TSImport::granite_item("BTreeMap_p2j"));
                key.import_p2j(imports);
                value.import_p2j(imports);
            }
            GType::IndexMap(key, value) => {
                imports.insert(TSImport::granite_item("IndexMap_p2j"));
                key.import_p2j(imports);
                value.import_p2j(imports);
            }
            GType::Range(item) => {
                imports.insert(TSImport::granite_item("Range_p2j"));
                item.import_p2j(imports);
            }
            GType::JsonValue => {
                imports.insert(TSImport::granite_item("JsonValue_p2j"));
            }
            GType::JsonObject => {
                imports.insert(TSImport::granite_item("JsonObject_p2j"));
            }
            GType::JsonArray => {
                imports.insert(TSImport::granite_item("JsonArray_p2j"));
            }
            GType::NestedError(gtype) => {
                imports.insert(TSImport::granite_item("NestedError_p2j"));
                gtype.import_p2j(imports);
            }
        }
    }

    fn import_j2t(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GType::GType(_ident) => {}
            GType::Scalar(gscalar) => {
                gscalar.import_j2t(imports);
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                imports.insert(TSImport::granite_item("Option_j2t"));
                vtype.import_j2t(imports);
            }
            GType::Result(ok, err) => {
                imports.insert(TSImport::granite_item("Result_j2t"));
                ok.import_j2t(imports);
                err.import_j2t(imports);
            }
            GType::Vec(item) => {
                imports.insert(TSImport::granite_item("Vec_j2t"));
                item.import_j2t(imports);
            }
            GType::HashSet(item) => {
                imports.insert(TSImport::granite_item("HashSet_j2t"));
                item.import_j2t(imports);
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                imports.insert(TSImport::granite_item("HashMap_j2t"));
                ktype.import_j2t(imports);
                vtype.import_j2t(imports);
            }
            GType::BTreeMap(key, value) => {
                imports.insert(TSImport::granite_item("BTreeMap_j2t"));
                key.import_j2t(imports);
                value.import_j2t(imports);
            }
            GType::IndexMap(key, value) => {
                imports.insert(TSImport::granite_item("IndexMap_j2t"));
                key.import_j2t(imports);
                value.import_j2t(imports);
            }
            GType::Range(item) => {
                imports.insert(TSImport::granite_item("Range_j2t"));
                item.import_j2t(imports);
            }
            GType::JsonValue => {
                imports.insert(TSImport::granite_item("JsonValue_j2t"));
            }
            GType::JsonObject => {
                imports.insert(TSImport::granite_item("JsonObject_j2t"));
            }
            GType::JsonArray => {
                imports.insert(TSImport::granite_item("JsonArray_j2t"));
            }
            GType::NestedError(gtype) => {
                imports.insert(TSImport::granite_item("NestedError_j2t"));
                gtype.import_j2t(imports);
            }
        }
    }

    fn import_j2p(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GType::GType(_ident) => {}
            GType::Scalar(gscalar) => {
                gscalar.import_j2p(imports);
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                imports.insert(TSImport::granite_item("Option_j2p"));
                vtype.import_j2p(imports);
            }
            GType::Result(ok, err) => {
                imports.insert(TSImport::granite_item("Result_j2p"));
                ok.import_j2p(imports);
                err.import_j2p(imports);
            }
            GType::Vec(item) => {
                imports.insert(TSImport::granite_item("Vec_j2p"));
                item.import_j2p(imports);
            }
            GType::HashSet(item) => {
                imports.insert(TSImport::granite_item("HashSet_j2p"));
                item.import_j2p(imports);
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                imports.insert(TSImport::granite_item("HashMap_j2p"));
                ktype.import_j2p(imports);
                vtype.import_j2p(imports);
            }
            GType::BTreeMap(key, value) => {
                imports.insert(TSImport::granite_item("BTreeMap_j2p"));
                key.import_j2p(imports);
                value.import_j2p(imports);
            }
            GType::IndexMap(key, value) => {
                imports.insert(TSImport::granite_item("IndexMap_j2p"));
                key.import_j2p(imports);
                value.import_j2p(imports);
            }
            GType::Range(item) => {
                imports.insert(TSImport::granite_item("Range_j2p"));
                item.import_j2p(imports);
            }
            GType::JsonValue => {
                imports.insert(TSImport::granite_item("JsonValue_j2p"));
            }
            GType::JsonObject => {
                imports.insert(TSImport::granite_item("JsonObject_j2p"));
            }
            GType::JsonArray => {
                imports.insert(TSImport::granite_item("JsonArray_j2p"));
            }
            GType::NestedError(gtype) => {
                imports.insert(TSImport::granite_item("NestedError_j2p"));
                gtype.import_j2p(imports);
            }
        }
    }

    fn import_j2e(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GType::GType(_ident) => {}
            GType::Scalar(gscalar) => {
                gscalar.import_j2e(imports);
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                imports.insert(TSImport::granite_item("Option_j2e"));
                vtype.import_j2e(imports);
            }
            GType::Result(ok, err) => {
                imports.insert(TSImport::granite_item("Result_j2e"));
                ok.import_j2e(imports);
                err.import_j2e(imports);
            }
            GType::Vec(item) => {
                imports.insert(TSImport::granite_item("Vec_j2e"));
                item.import_j2e(imports);
            }
            GType::HashSet(item) => {
                imports.insert(TSImport::granite_item("HashSet_j2e"));
                item.import_j2e(imports);
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                max_items: _,
            } => {
                imports.insert(TSImport::granite_item("HashMap_j2e"));
                ktype.import_j2e(imports);
                vtype.import_j2e(imports);
            }
            GType::BTreeMap(key, value) => {
                imports.insert(TSImport::granite_item("BTreeMap_j2e"));
                key.import_j2e(imports);
                value.import_j2e(imports);
            }
            GType::IndexMap(key, value) => {
                imports.insert(TSImport::granite_item("IndexMap_j2e"));
                key.import_j2e(imports);
                value.import_j2e(imports);
            }
            GType::Range(item) => {
                imports.insert(TSImport::granite_item("Range_j2e"));
                item.import_j2e(imports);
            }
            GType::JsonValue => {
                imports.insert(TSImport::granite_item("JsonValue_j2e"));
            }
            GType::JsonObject => {
                imports.insert(TSImport::granite_item("JsonObject_j2e"));
            }
            GType::JsonArray => {
                imports.insert(TSImport::granite_item("JsonArray_j2e"));
            }
            GType::NestedError(gtype) => {
                imports.insert(TSImport::granite_item("NestedError_j2e"));
                gtype.import_j2e(imports);
            }
        }
    }
}

impl GTypeExtImport for GScalar {
    fn import_t(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GScalar::char => {
                imports.insert(TSImport::granite_type("char"));
            }
            GScalar::bool => {
                // bool is a boolean which is a ts builtin type
            }
            GScalar::i8 { .. } => {
                imports.insert(TSImport::granite_type("i8"));
            }
            GScalar::u8 { .. } => {
                imports.insert(TSImport::granite_type("u8"));
            }
            GScalar::i16 { .. } => {
                imports.insert(TSImport::granite_type("i16"));
            }
            GScalar::u16 { .. } => {
                imports.insert(TSImport::granite_type("u16"));
            }
            GScalar::i32 { .. } => {
                imports.insert(TSImport::granite_type("i32"));
            }
            GScalar::u32 { .. } => {
                imports.insert(TSImport::granite_type("u32"));
            }
            GScalar::i64 { .. } => {
                imports.insert(TSImport::granite_type("i64"));
            }
            GScalar::u64 { .. } => {
                imports.insert(TSImport::granite_type("u64"));
            }
            GScalar::f32 { .. } => {
                imports.insert(TSImport::granite_type("f32"));
            }
            GScalar::f64 { .. } => {
                imports.insert(TSImport::granite_type("f64"));
            }
            GScalar::String { .. } => {
                // string is a ts builtin type
            }
            GScalar::Uuid { .. } => {
                imports.insert(TSImport::granite_type("Uuid"));
            }
            GScalar::Decimal { .. } => {
                imports.insert(TSImport::granite_type("Decimal"));
            }
            GScalar::Integer => {
                imports.insert(TSImport::granite_type("Integer"));
            }
            GScalar::BigInt => {
                imports.insert(TSImport::granite_type("BigInt"));
            }
            GScalar::DateUtc => {
                imports.insert(TSImport::granite_type("DateUtc"));
            }
            GScalar::DateTz => {
                imports.insert(TSImport::granite_type("DateTz"));
            }
            GScalar::DateTimeUtc => {
                imports.insert(TSImport::granite_type("DateTimeUtc"));
            }
            GScalar::DateTimeTz => {
                imports.insert(TSImport::granite_type("DateTimeTz"));
            }
            GScalar::Duration => {
                imports.insert(TSImport::granite_type("Duration"));
            }
            GScalar::Time => {
                imports.insert(TSImport::granite_type("Time"));
            }
        }
    }
    fn import_p(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GScalar::char => {
                imports.insert(TSImport::granite_type("char_p"));
            }
            GScalar::bool => {
                imports.insert(TSImport::granite_type("boolean_p"));
            }
            GScalar::i8 { .. } => {
                imports.insert(TSImport::granite_type("i8_p"));
            }
            GScalar::u8 { .. } => {
                imports.insert(TSImport::granite_type("u8_p"));
            }
            GScalar::i16 { .. } => {
                imports.insert(TSImport::granite_type("i16_p"));
            }
            GScalar::u16 { .. } => {
                imports.insert(TSImport::granite_type("u16_p"));
            }
            GScalar::i32 { .. } => {
                imports.insert(TSImport::granite_type("i32_p"));
            }
            GScalar::u32 { .. } => {
                imports.insert(TSImport::granite_type("u32_p"));
            }
            GScalar::i64 { .. } => {
                imports.insert(TSImport::granite_type("i64_p"));
            }
            GScalar::u64 { .. } => {
                imports.insert(TSImport::granite_type("u64_p"));
            }
            GScalar::f32 { .. } => {
                imports.insert(TSImport::granite_type("f32_p"));
            }
            GScalar::f64 { .. } => {
                imports.insert(TSImport::granite_type("f64_p"));
            }
            GScalar::String { .. } => {
                imports.insert(TSImport::granite_type("string_p"));
            }
            GScalar::Uuid { .. } => {
                imports.insert(TSImport::granite_type("Uuid_p"));
            }
            GScalar::Decimal { .. } => {
                imports.insert(TSImport::granite_type("Decimal_p"));
            }
            GScalar::Integer => {
                imports.insert(TSImport::granite_type("Integer_p"));
            }
            GScalar::BigInt => {
                imports.insert(TSImport::granite_type("BigInt_p"));
            }
            GScalar::DateUtc => {
                imports.insert(TSImport::granite_type("DateUtc_p"));
            }
            GScalar::DateTz => {
                imports.insert(TSImport::granite_type("DateTz_p"));
            }
            GScalar::DateTimeUtc => {
                imports.insert(TSImport::granite_type("DateTimeUtc_p"));
            }
            GScalar::DateTimeTz => {
                imports.insert(TSImport::granite_type("DateTimeTz_p"));
            }
            GScalar::Duration => {
                imports.insert(TSImport::granite_type("Duration_p"));
            }
            GScalar::Time => {
                imports.insert(TSImport::granite_type("Time_p"));
            }
        }
    }
    fn import_e(&self, _imports: &mut HashSet<TSImport>) {
        // noop
    }
    fn import_t2t(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GScalar::char => {
                imports.insert(TSImport::granite_item("char_t2t"));
            }
            GScalar::bool => {
                imports.insert(TSImport::granite_item("boolean_t2t"));
            }
            GScalar::i8 { .. } => {
                imports.insert(TSImport::granite_item("i8_t2t"));
            }
            GScalar::u8 { .. } => {
                imports.insert(TSImport::granite_item("u8_t2t"));
            }
            GScalar::i16 { .. } => {
                imports.insert(TSImport::granite_item("i16_t2t"));
            }
            GScalar::u16 { .. } => {
                imports.insert(TSImport::granite_item("u16_t2t"));
            }
            GScalar::i32 { .. } => {
                imports.insert(TSImport::granite_item("i32_t2t"));
            }
            GScalar::u32 { .. } => {
                imports.insert(TSImport::granite_item("u32_t2t"));
            }
            GScalar::i64 { .. } => {
                imports.insert(TSImport::granite_item("i64_t2t"));
            }
            GScalar::u64 { .. } => {
                imports.insert(TSImport::granite_item("u64_t2t"));
            }
            GScalar::f32 { .. } => {
                imports.insert(TSImport::granite_item("f32_t2t"));
            }
            GScalar::f64 { .. } => {
                imports.insert(TSImport::granite_item("f64_t2t"));
            }
            GScalar::String { .. } => {
                imports.insert(TSImport::granite_item("string_t2t"));
            }
            GScalar::Uuid { .. } => {
                imports.insert(TSImport::granite_item("Uuid_t2t"));
            }
            GScalar::Decimal { .. } => {
                imports.insert(TSImport::granite_item("Decimal_t2t"));
            }
            GScalar::Integer => {
                imports.insert(TSImport::granite_item("Integer_t2t"));
            }
            GScalar::BigInt => {
                imports.insert(TSImport::granite_item("BigInt_t2t"));
            }
            GScalar::DateUtc => {
                imports.insert(TSImport::granite_item("DateUtc_t2t"));
            }
            GScalar::DateTz => {
                imports.insert(TSImport::granite_item("DateTz_t2t"));
            }
            GScalar::DateTimeUtc => {
                imports.insert(TSImport::granite_item("DateTimeUtc_t2t"));
            }
            GScalar::DateTimeTz => {
                imports.insert(TSImport::granite_item("DateTimeTz_t2t"));
            }
            GScalar::Duration => {
                imports.insert(TSImport::granite_item("Duration_t2t"));
            }
            GScalar::Time => {
                imports.insert(TSImport::granite_item("Time_t2t"));
            }
        }
    }
    fn import_t2j(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GScalar::char => {
                imports.insert(TSImport::granite_item("char_t2j"));
            }
            GScalar::bool => {
                imports.insert(TSImport::granite_item("boolean_t2j"));
            }
            GScalar::i8 { .. } => {
                imports.insert(TSImport::granite_item("i8_t2j"));
            }
            GScalar::u8 { .. } => {
                imports.insert(TSImport::granite_item("u8_t2j"));
            }
            GScalar::i16 { .. } => {
                imports.insert(TSImport::granite_item("i16_t2j"));
            }
            GScalar::u16 { .. } => {
                imports.insert(TSImport::granite_item("u16_t2j"));
            }
            GScalar::i32 { .. } => {
                imports.insert(TSImport::granite_item("i32_t2j"));
            }
            GScalar::u32 { .. } => {
                imports.insert(TSImport::granite_item("u32_t2j"));
            }
            GScalar::i64 { .. } => {
                imports.insert(TSImport::granite_item("i64_t2j"));
            }
            GScalar::u64 { .. } => {
                imports.insert(TSImport::granite_item("u64_t2j"));
            }
            GScalar::f32 { .. } => {
                imports.insert(TSImport::granite_item("f32_t2j"));
            }
            GScalar::f64 { .. } => {
                imports.insert(TSImport::granite_item("f64_t2j"));
            }
            GScalar::String { .. } => {
                imports.insert(TSImport::granite_item("string_t2j"));
            }
            GScalar::Uuid { .. } => {
                imports.insert(TSImport::granite_item("Uuid_t2j"));
            }
            GScalar::Decimal { .. } => {
                imports.insert(TSImport::granite_item("Decimal_t2j"));
            }
            GScalar::Integer => {
                imports.insert(TSImport::granite_item("Integer_t2j"));
            }
            GScalar::BigInt => {
                imports.insert(TSImport::granite_item("BigInt_t2j"));
            }
            GScalar::DateUtc => {
                imports.insert(TSImport::granite_item("DateUtc_t2j"));
            }
            GScalar::DateTz => {
                imports.insert(TSImport::granite_item("DateTz_t2j"));
            }
            GScalar::DateTimeUtc => {
                imports.insert(TSImport::granite_item("DateTimeUtc_t2j"));
            }
            GScalar::DateTimeTz => {
                imports.insert(TSImport::granite_item("DateTimeTz_t2j"));
            }
            GScalar::Duration => {
                imports.insert(TSImport::granite_item("Duration_t2j"));
            }
            GScalar::Time => {
                imports.insert(TSImport::granite_item("Time_t2j"));
            }
        }
    }
    fn import_p2t(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GScalar::char => {
                imports.insert(TSImport::granite_item("char_p2t"));
            }
            GScalar::bool => {
                imports.insert(TSImport::granite_item("boolean_p2t"));
            }
            GScalar::i8 { .. } => {
                imports.insert(TSImport::granite_item("i8_p2t"));
            }
            GScalar::u8 { .. } => {
                imports.insert(TSImport::granite_item("u8_p2t"));
            }
            GScalar::i16 { .. } => {
                imports.insert(TSImport::granite_item("i16_p2t"));
            }
            GScalar::u16 { .. } => {
                imports.insert(TSImport::granite_item("u16_p2t"));
            }
            GScalar::i32 { .. } => {
                imports.insert(TSImport::granite_item("i32_p2t"));
            }
            GScalar::u32 { .. } => {
                imports.insert(TSImport::granite_item("u32_p2t"));
            }
            GScalar::i64 { .. } => {
                imports.insert(TSImport::granite_item("i64_p2t"));
            }
            GScalar::u64 { .. } => {
                imports.insert(TSImport::granite_item("u64_p2t"));
            }
            GScalar::f32 { .. } => {
                imports.insert(TSImport::granite_item("f32_p2t"));
            }
            GScalar::f64 { .. } => {
                imports.insert(TSImport::granite_item("f64_p2t"));
            }
            GScalar::String { .. } => {
                imports.insert(TSImport::granite_item("string_p2t"));
            }
            GScalar::Uuid { .. } => {
                imports.insert(TSImport::granite_item("Uuid_p2t"));
            }
            GScalar::Decimal { .. } => {
                imports.insert(TSImport::granite_item("Decimal_p2t"));
            }
            GScalar::Integer => {
                imports.insert(TSImport::granite_item("Integer_p2t"));
            }
            GScalar::BigInt => {
                imports.insert(TSImport::granite_item("BigInt_p2t"));
            }
            GScalar::DateUtc => {
                imports.insert(TSImport::granite_item("DateUtc_p2t"));
            }
            GScalar::DateTz => {
                imports.insert(TSImport::granite_item("DateTz_p2t"));
            }
            GScalar::DateTimeUtc => {
                imports.insert(TSImport::granite_item("DateTimeUtc_p2t"));
            }
            GScalar::DateTimeTz => {
                imports.insert(TSImport::granite_item("DateTimeTz_p2t"));
            }
            GScalar::Duration => {
                imports.insert(TSImport::granite_item("Duration_p2t"));
            }
            GScalar::Time => {
                imports.insert(TSImport::granite_item("Time_p2t"));
            }
        }
    }
    fn import_p2j(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GScalar::char => {
                imports.insert(TSImport::granite_item("char_p2j"));
            }
            GScalar::bool => {
                imports.insert(TSImport::granite_item("boolean_p2j"));
            }
            GScalar::i8 { .. } => {
                imports.insert(TSImport::granite_item("i8_p2j"));
            }
            GScalar::u8 { .. } => {
                imports.insert(TSImport::granite_item("u8_p2j"));
            }
            GScalar::i16 { .. } => {
                imports.insert(TSImport::granite_item("i16_p2j"));
            }
            GScalar::u16 { .. } => {
                imports.insert(TSImport::granite_item("u16_p2j"));
            }
            GScalar::i32 { .. } => {
                imports.insert(TSImport::granite_item("i32_p2j"));
            }
            GScalar::u32 { .. } => {
                imports.insert(TSImport::granite_item("u32_p2j"));
            }
            GScalar::i64 { .. } => {
                imports.insert(TSImport::granite_item("i64_p2j"));
            }
            GScalar::u64 { .. } => {
                imports.insert(TSImport::granite_item("u64_p2j"));
            }
            GScalar::f32 { .. } => {
                imports.insert(TSImport::granite_item("f32_p2j"));
            }
            GScalar::f64 { .. } => {
                imports.insert(TSImport::granite_item("f64_p2j"));
            }
            GScalar::String { .. } => {
                imports.insert(TSImport::granite_item("string_p2j"));
            }
            GScalar::Uuid { .. } => {
                imports.insert(TSImport::granite_item("Uuid_p2j"));
            }
            GScalar::Decimal { .. } => {
                imports.insert(TSImport::granite_item("Decimal_p2j"));
            }
            GScalar::Integer => {
                imports.insert(TSImport::granite_item("Integer_p2j"));
            }
            GScalar::BigInt => {
                imports.insert(TSImport::granite_item("BigInt_p2j"));
            }
            GScalar::DateUtc => {
                imports.insert(TSImport::granite_item("DateUtc_p2j"));
            }
            GScalar::DateTz => {
                imports.insert(TSImport::granite_item("DateTz_p2j"));
            }
            GScalar::DateTimeUtc => {
                imports.insert(TSImport::granite_item("DateTimeUtc_p2j"));
            }
            GScalar::DateTimeTz => {
                imports.insert(TSImport::granite_item("DateTimeTz_p2j"));
            }
            GScalar::Duration => {
                imports.insert(TSImport::granite_item("Duration_p2j"));
            }
            GScalar::Time => {
                imports.insert(TSImport::granite_item("Time_p2j"));
            }
        }
    }
    fn import_j2t(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GScalar::char => {
                imports.insert(TSImport::granite_item("char_j2t"));
            }
            GScalar::bool => {
                imports.insert(TSImport::granite_item("boolean_j2t"));
            }
            GScalar::i8 { .. } => {
                imports.insert(TSImport::granite_item("i8_j2t"));
            }
            GScalar::u8 { .. } => {
                imports.insert(TSImport::granite_item("u8_j2t"));
            }
            GScalar::i16 { .. } => {
                imports.insert(TSImport::granite_item("i16_j2t"));
            }
            GScalar::u16 { .. } => {
                imports.insert(TSImport::granite_item("u16_j2t"));
            }
            GScalar::i32 { .. } => {
                imports.insert(TSImport::granite_item("i32_j2t"));
            }
            GScalar::u32 { .. } => {
                imports.insert(TSImport::granite_item("u32_j2t"));
            }
            GScalar::i64 { .. } => {
                imports.insert(TSImport::granite_item("i64_j2t"));
            }
            GScalar::u64 { .. } => {
                imports.insert(TSImport::granite_item("u64_j2t"));
            }
            GScalar::f32 { .. } => {
                imports.insert(TSImport::granite_item("f32_j2t"));
            }
            GScalar::f64 { .. } => {
                imports.insert(TSImport::granite_item("f64_j2t"));
            }
            GScalar::String { .. } => {
                imports.insert(TSImport::granite_item("string_j2t"));
            }
            GScalar::Uuid { .. } => {
                imports.insert(TSImport::granite_item("Uuid_j2t"));
            }
            GScalar::Decimal { .. } => {
                imports.insert(TSImport::granite_item("Decimal_j2t"));
            }
            GScalar::Integer => {
                imports.insert(TSImport::granite_item("Integer_j2t"));
            }
            GScalar::BigInt => {
                imports.insert(TSImport::granite_item("BigInt_j2t"));
            }
            GScalar::DateUtc => {
                imports.insert(TSImport::granite_item("DateUtc_j2t"));
            }
            GScalar::DateTz => {
                imports.insert(TSImport::granite_item("DateTz_j2t"));
            }
            GScalar::DateTimeUtc => {
                imports.insert(TSImport::granite_item("DateTimeUtc_j2t"));
            }
            GScalar::DateTimeTz => {
                imports.insert(TSImport::granite_item("DateTimeTz_j2t"));
            }
            GScalar::Duration => {
                imports.insert(TSImport::granite_item("Duration_j2t"));
            }
            GScalar::Time => {
                imports.insert(TSImport::granite_item("Time_j2t"));
            }
        }
    }
    fn import_j2p(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GScalar::char => {
                imports.insert(TSImport::granite_item("char_j2p"));
            }
            GScalar::bool => {
                imports.insert(TSImport::granite_item("boolean_j2p"));
            }
            GScalar::i8 { .. } => {
                imports.insert(TSImport::granite_item("i8_j2p"));
            }
            GScalar::u8 { .. } => {
                imports.insert(TSImport::granite_item("u8_j2p"));
            }
            GScalar::i16 { .. } => {
                imports.insert(TSImport::granite_item("i16_j2p"));
            }
            GScalar::u16 { .. } => {
                imports.insert(TSImport::granite_item("u16_j2p"));
            }
            GScalar::i32 { .. } => {
                imports.insert(TSImport::granite_item("i32_j2p"));
            }
            GScalar::u32 { .. } => {
                imports.insert(TSImport::granite_item("u32_j2p"));
            }
            GScalar::i64 { .. } => {
                imports.insert(TSImport::granite_item("i64_j2p"));
            }
            GScalar::u64 { .. } => {
                imports.insert(TSImport::granite_item("u64_j2p"));
            }
            GScalar::f32 { .. } => {
                imports.insert(TSImport::granite_item("f32_j2p"));
            }
            GScalar::f64 { .. } => {
                imports.insert(TSImport::granite_item("f64_j2p"));
            }
            GScalar::String { .. } => {
                imports.insert(TSImport::granite_item("string_j2p"));
            }
            GScalar::Uuid { .. } => {
                imports.insert(TSImport::granite_item("Uuid_j2p"));
            }
            GScalar::Decimal { .. } => {
                imports.insert(TSImport::granite_item("Decimal_j2p"));
            }
            GScalar::Integer => {
                imports.insert(TSImport::granite_item("Integer_j2p"));
            }
            GScalar::BigInt => {
                imports.insert(TSImport::granite_item("BigInt_j2p"));
            }
            GScalar::DateUtc => {
                imports.insert(TSImport::granite_item("DateUtc_j2p"));
            }
            GScalar::DateTz => {
                imports.insert(TSImport::granite_item("DateTz_j2p"));
            }
            GScalar::DateTimeUtc => {
                imports.insert(TSImport::granite_item("DateTimeUtc_j2p"));
            }
            GScalar::DateTimeTz => {
                imports.insert(TSImport::granite_item("DateTimeTz_j2p"));
            }
            GScalar::Duration => {
                imports.insert(TSImport::granite_item("Duration_j2p"));
            }
            GScalar::Time => {
                imports.insert(TSImport::granite_item("Time_j2p"));
            }
        }
    }
    fn import_j2e(&self, imports: &mut HashSet<TSImport>) {
        match self {
            GScalar::char => {
                imports.insert(TSImport::granite_item("char_j2e"));
            }
            GScalar::bool => {
                imports.insert(TSImport::granite_item("boolean_j2e"));
            }
            GScalar::i8 { .. } => {
                imports.insert(TSImport::granite_item("i8_j2e"));
            }
            GScalar::u8 { .. } => {
                imports.insert(TSImport::granite_item("u8_j2e"));
            }
            GScalar::i16 { .. } => {
                imports.insert(TSImport::granite_item("i16_j2e"));
            }
            GScalar::u16 { .. } => {
                imports.insert(TSImport::granite_item("u16_j2e"));
            }
            GScalar::i32 { .. } => {
                imports.insert(TSImport::granite_item("i32_j2e"));
            }
            GScalar::u32 { .. } => {
                imports.insert(TSImport::granite_item("u32_j2e"));
            }
            GScalar::i64 { .. } => {
                imports.insert(TSImport::granite_item("i64_j2e"));
            }
            GScalar::u64 { .. } => {
                imports.insert(TSImport::granite_item("u64_j2e"));
            }
            GScalar::f32 { .. } => {
                imports.insert(TSImport::granite_item("f32_j2e"));
            }
            GScalar::f64 { .. } => {
                imports.insert(TSImport::granite_item("f64_j2e"));
            }
            GScalar::String { .. } => {
                imports.insert(TSImport::granite_item("string_j2e"));
            }
            GScalar::Uuid { .. } => {
                imports.insert(TSImport::granite_item("Uuid_j2e"));
            }
            GScalar::Decimal { .. } => {
                imports.insert(TSImport::granite_item("Decimal_j2e"));
            }
            GScalar::Integer => {
                imports.insert(TSImport::granite_item("Integer_j2e"));
            }
            GScalar::BigInt => {
                imports.insert(TSImport::granite_item("BigInt_j2e"));
            }
            GScalar::DateUtc => {
                imports.insert(TSImport::granite_item("DateUtc_j2e"));
            }
            GScalar::DateTz => {
                imports.insert(TSImport::granite_item("DateTz_j2e"));
            }
            GScalar::DateTimeUtc => {
                imports.insert(TSImport::granite_item("DateTimeUtc_j2e"));
            }
            GScalar::DateTimeTz => {
                imports.insert(TSImport::granite_item("DateTimeTz_j2e"));
            }
            GScalar::Duration => {
                imports.insert(TSImport::granite_item("Duration_j2e"));
            }
            GScalar::Time => {
                imports.insert(TSImport::granite_item("Time_j2e"));
            }
        }
    }
}

impl GTypeExtCallable for GType {
    fn callable_t2t(&self) -> TokenStream {
        let t2t_ident = self.ident_t2t();
        match self {
            // GType is local refrence, so call to t2t_ident above is sufficient
            GType::GType(_) => {
                quote! {
                    #t2t_ident
                }
            }
            // Scalar is not generic, so call to t2t_ident above is sufficient
            GType::Scalar(gscalar) => gscalar.callable_t2t(),
            GType::Option {
                zero_to_none,
                some_type: vtype,
            } => {
                let inner_t2t_call = vtype.callable_t2t();
                let mut has_outer = false;

                let zero_to_none_code = match zero_to_none {
                    true => {
                        has_outer = true;
                        quote! {
                            if ("Some" in a && typeof a.Some === "number" && a.Some === 0){
                                a = { None: true };
                            }
                        }
                    }
                    false => quote! {},
                };

                let outer_t2t_call = if has_outer {
                    quote! {
                        (a) => {
                            #zero_to_none_code
                            return { Ok: a };
                        }
                    }
                } else {
                    quote! { undefined }
                };

                quote! {
                    ((v) => {
                        return #t2t_ident(v, #inner_t2t_call, #outer_t2t_call);
                    })
                }
            }
            GType::Result(ok, err) => {
                let ok_t2t_call = ok.callable_t2t();
                let err_t2t_call = err.callable_t2t();
                quote! {
                    ((v) => {
                        return #t2t_ident(v, #ok_t2t_call, #err_t2t_call);
                    })

                }
            }
            GType::Vec(inner) => {
                let inner_t2t_call = inner.callable_t2t();
                quote! {
                    ((v) => {
                        return #t2t_ident(v, #inner_t2t_call);
                    })
                }
            }
            GType::HashSet(inner) => {
                let inner_t2t = inner.callable_t2t();
                quote! {
                    ((v) => {
                        return #t2t_ident(v, #inner_t2t);
                    })
                }
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                let key_t2t = ktype.callable_t2t();
                let value_t2t_call = vtype.callable_t2t();
                quote! {
                    ((v) => {
                        return #t2t_ident(v, #key_t2t, #value_t2t_call);
                    })
                }
            }
            GType::BTreeMap(key, value) => {
                let key_t2t = key.ident_t2t();
                let value_t2t_call = value.callable_t2t();
                quote! {
                    ((v) => {
                        return #t2t_ident(v, #key_t2t, (a) => #value_t2t_call);
                    })
                }
            }
            GType::IndexMap(key, value) => {
                let key_t2t = key.ident_t2t();
                let value_t2t_call = value.callable_t2t();
                quote! {
                    ((v) => {
                        return #t2t_ident(v, #key_t2t, (a) => #value_t2t_call);
                    })
                }
            }
            GType::Range(scalar) => {
                let inner_t2t = scalar.ident_t2t();
                quote! {
                    ((v) => {
                        return #t2t_ident(v, #inner_t2t);
                    })
                }
            }
            GType::JsonValue => {
                quote! {
                    #t2t_ident
                }
            }
            GType::JsonObject => {
                quote! {
                    #t2t_ident
                }
            }
            GType::JsonArray => {
                quote! {
                    #t2t_ident
                }
            }
            GType::NestedError(gtype) => {
                let inner_t2t = gtype.callable_t2t();
                quote! {
                    ((v) => {
                        return #t2t_ident(v, #inner_t2t);
                    })
                }
            }
        }
    }
    fn callable_t2j(&self) -> TokenStream {
        let t2j_ident = self.ident_t2j();
        match &self {
            // GType is local refrence, so call to t2j_ident above is sufficient
            GType::GType(_) => {
                quote! {
                    #t2j_ident
                }
            }
            // Scalar is not generic, so call to t2j_ident above is sufficient
            GType::Scalar(_scalar) => {
                quote! {
                    #t2j_ident
                }
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                let inner_t2j_call = vtype.callable_t2j();
                quote! {
                    ((v) => {
                        return #t2j_ident(v, #inner_t2j_call)
                    })
                }
            }
            GType::Result(ok, err) => {
                let ok_t2j_call = ok.callable_t2j();
                let err_t2j_call = err.callable_t2j();
                quote! {
                    ((v) => {
                        return #t2j_ident(v, #ok_t2j_call, #err_t2j_call)
                    })
                }
            }
            GType::Vec(inner) => {
                let inner_t2j_call = inner.callable_t2j();
                quote! {
                    ((v) => {
                        return #t2j_ident(v, #inner_t2j_call)
                    })
                }
            }
            GType::HashSet(inner) => {
                let inner_t2j = inner.ident_t2j();
                quote! {
                    ((v) => {
                        return #t2j_ident(v, #inner_t2j)
                    })
                }
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                let key_t2j = ktype.ident_t2j();
                let value_t2j_call = vtype.callable_t2j();
                quote! {
                    ((v) => {
                        return #t2j_ident(v, #key_t2j, #value_t2j_call)
                    })
                }
            }
            GType::BTreeMap(key, value) => {
                let key_t2j = key.ident_t2j();
                let value_t2j_call = value.callable_t2j();
                quote! {
                    ((v) => {
                        return #t2j_ident(v, #key_t2j, #value_t2j_call)
                    })
                }
            }
            GType::IndexMap(key, value) => {
                let key_t2j = key.ident_t2j();
                let value_t2j_call = value.callable_t2j();
                quote! {
                    ((v) => {
                        return #t2j_ident(v, #key_t2j, #value_t2j_call)
                    })
                }
            }
            GType::Range(scalar) => {
                let inner_t2j = scalar.ident_t2j();
                quote! {
                    ((v) => {
                        return #t2j_ident(v, #inner_t2j)
                    })
                }
            }
            GType::JsonValue => {
                quote! {
                    #t2j_ident
                }
            }
            GType::JsonObject => {
                quote! {
                    #t2j_ident
                }
            }
            GType::JsonArray => {
                quote! {
                    #t2j_ident
                }
            }
            GType::NestedError(gtype) => {
                let inner_t2j = gtype.callable_t2j();
                quote! {
                    ((v) => {
                        return #t2j_ident(v, #inner_t2j)
                    })
                }
            }
        }
    }
    fn callable_p2t(&self) -> TokenStream {
        let p2t_ident = self.ident_p2t();
        match &self {
            // GType is local reference, so call to p2t_ident above is sufficient
            GType::GType(_) => {
                quote! {
                    #p2t_ident
                }
            }
            GType::Scalar(gscalar) => gscalar.callable_p2t(),
            // Scalar is not generic, so call to p2t_ident above is sufficient
            GType::Option {
                some_type: vtype,
                zero_to_none,
            } => {
                let inner_p2t_call = vtype.callable_p2t();
                let mut has_outer = false;

                let zero_to_none_code = match zero_to_none {
                    true => {
                        has_outer = true;
                        quote! {
                            if ("Some" in a && typeof a.Some === "number" && a.Some === 0){
                                a = { None: true };
                            }
                        }
                    }
                    false => quote! {},
                };

                let outer_p2t_call = if has_outer {
                    quote! {
                        (a) => {
                            #zero_to_none_code
                            return { Ok: a };
                        }
                    }
                } else {
                    quote! { undefined }
                };

                quote! {
                    ((v) => {
                        return #p2t_ident(v, #inner_p2t_call, #outer_p2t_call);
                    })
                }
            }
            GType::Result(_inner_ok, _inner_err) => {
                // Result_e is not generic
                quote! {
                    #p2t_ident
                }
            }
            GType::Vec(inner) => {
                let inner_p2t_call = inner.callable_p2t();
                quote! {
                    ((v) => {
                        return #p2t_ident(v, #inner_p2t_call);
                    })
                }
            }
            GType::HashSet(inner) => {
                let inner_p2t = inner.callable_p2t();
                quote! {
                    ((v) => {
                        return #p2t_ident(v, #inner_p2t);
                    })
                }
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                let key_t2t_call = ktype.callable_t2t();
                let value_p2t_call = vtype.callable_p2t();
                quote! {
                    ((v) => {
                        return #p2t_ident(v, #key_t2t_call, #value_p2t_call);
                    })
                }
            }
            GType::BTreeMap(key, value) => {
                let key_p2t = key.ident_p2t();
                let value_p2t_call = value.callable_p2t();
                quote! {
                    ((v) => {
                        return #p2t_ident(v, #key_p2t, #value_p2t_call);
                    })
                }
            }
            GType::IndexMap(key, value) => {
                let key_p2t = key.ident_p2t();
                let value_p2t_call = value.callable_p2t();
                quote! {
                    ((v) => {
                        return #p2t_ident(v, #key_p2t, #value_p2t_call)
                    })
                }
            }
            GType::Range(scalar) => {
                let inner_p2t = scalar.ident_p2t();
                quote! {
                    ((v) => {
                        return #p2t_ident(v, #inner_p2t);
                    })
                }
            }
            GType::JsonValue => {
                quote! {
                    #p2t_ident
                }
            }
            GType::JsonObject => {
                quote! {
                    #p2t_ident
                }
            }
            GType::JsonArray => {
                quote! {
                    #p2t_ident
                }
            }
            GType::NestedError(gtype) => {
                let inner_p2t = gtype.callable_p2t();
                quote! {
                    ((v) => {
                        return #p2t_ident(v, #inner_p2t);
                    })
                }
            }
        }
    }
    fn callable_p2j(&self) -> TokenStream {
        let p2j_ident = self.ident_p2j();
        match &self {
            // GType is local refrence, so call to p2j_ident above is sufficient
            GType::GType(_) => {
                quote! {
                    #p2j_ident
                }
            }
            // Scalar is not generic, so call to p2j_ident above is sufficient
            GType::Scalar(_scalar) => {
                quote! {
                    #p2j_ident
                }
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                let inner_p2j_call = vtype.callable_p2j();
                quote! {
                    ((v) => {
                        return #p2j_ident(v, #inner_p2j_call)
                    })
                }
            }
            GType::Result(ok, err) => {
                let ok_p2j_call = ok.callable_p2j();
                let err_p2j_call = err.callable_p2j();
                quote! {
                    ((v) => {
                        return #p2j_ident(v, #ok_p2j_call, #err_p2j_call)
                    })
                }
            }
            GType::Vec(inner) => {
                let inner_p2j_call = inner.callable_p2j();
                quote! {
                    ((v) => {
                        return #p2j_ident(v, #inner_p2j_call)
                    })
                }
            }
            GType::HashSet(inner) => {
                let inner_p2j = inner.ident_p2j();
                quote! {
                    ((v) => {
                        return #p2j_ident(v, #inner_p2j)
                    })
                }
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                let key_p2j = ktype.ident_p2j();
                let value_p2j_call = vtype.callable_p2j();
                quote! {
                    ((v) => {
                        return #p2j_ident(v, #key_p2j, #value_p2j_call)
                    })
                }
            }
            GType::BTreeMap(key, value) => {
                let key_p2j = key.ident_p2j();
                let value_p2j_call = value.callable_p2j();
                quote! {
                    ((v) => {
                        return #p2j_ident(v, #key_p2j, #value_p2j_call)
                    })
                }
            }
            GType::IndexMap(key, value) => {
                let key_p2j = key.ident_p2j();
                let value_p2j_call = value.callable_p2j();
                quote! {
                    ((v) => {
                        return #p2j_ident(v, #key_p2j, #value_p2j_call)
                    })
                }
            }
            GType::Range(scalar) => {
                let inner_p2j = scalar.ident_p2j();
                quote! {
                    ((v) => {
                        return #p2j_ident(v, #inner_p2j)
                    })
                }
            }
            GType::JsonValue => {
                quote! {
                    #p2j_ident
                }
            }
            GType::JsonObject => {
                quote! {
                    #p2j_ident
                }
            }
            GType::JsonArray => {
                quote! {
                    #p2j_ident
                }
            }
            GType::NestedError(gtype) => {
                let inner_p2j = gtype.callable_p2j();
                quote! {
                    ((v) => {
                        return #p2j_ident(v, #inner_p2j)
                    })
                }
            }
        }
    }
    fn callable_j2t(&self) -> TokenStream {
        let j2t_ident = self.ident_j2t();

        match &self {
            // GType is local refrence, so call to j2t_ident above is sufficient
            GType::GType(_) => {
                quote! {
                    #j2t_ident
                }
            }
            // Scalar is not generic, so call to j2t_ident above is sufficient
            GType::Scalar(gscalar) => gscalar.callable_j2t(),
            GType::Option {
                some_type: vtype, ..
            } => {
                let inner_j2t_call = vtype.callable_j2t();

                quote! {
                    ((v) => {
                        return #j2t_ident(v, #inner_j2t_call)
                    })
                }
            }
            GType::Result(ok, err) => {
                let ok_j2t_call = ok.callable_j2t();
                let err_j2t_call = err.callable_j2t();
                quote! {
                    ((v) => {
                        return #j2t_ident(v, #ok_j2t_call, #err_j2t_call)
                    })
                }
            }
            GType::Vec(inner) => {
                let inner_j2t_call = inner.callable_j2t();
                quote! {
                    ((v) => {
                        return #j2t_ident(v, #inner_j2t_call)
                    })
                }
            }
            GType::HashSet(inner) => {
                let inner_j2t = inner.ident_j2t();
                quote! {
                    ((v) => {
                        return #j2t_ident(v, #inner_j2t)
                    })
                }
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                let key_j2t = ktype.callable_j2t();
                let value_j2t_call = vtype.callable_j2t();
                quote! {
                    ((v) => {
                        return #j2t_ident(v, #key_j2t, #value_j2t_call)
                    })
                }
            }
            GType::BTreeMap(key, value) => {
                let key_j2t = key.ident_j2t();
                let value_j2t_call = value.callable_j2t();
                quote! {
                    ((v) => {
                        return #j2t_ident(v, #key_j2t, #value_j2t_call)
                    })
                }
            }
            GType::IndexMap(key, value) => {
                let key_j2t = key.ident_j2t();
                let value_j2t_call = value.callable_j2t();
                quote! {
                    ((v) => {
                        return #j2t_ident(v, #key_j2t, #value_j2t_call)
                    })
                }
            }
            GType::Range(scalar) => {
                let inner_j2t = scalar.ident_j2t();
                quote! {
                    ((v) => {
                        return #j2t_ident(v, #inner_j2t)
                    })
                }
            }
            GType::JsonValue => {
                quote! {
                    #j2t_ident
                }
            }
            GType::JsonObject => {
                quote! {
                    #j2t_ident
                }
            }
            GType::JsonArray => {
                quote! {
                    #j2t_ident
                }
            }
            GType::NestedError(gtype) => {
                let inner_j2t = gtype.callable_j2t();
                quote! {
                    ((v) => {
                        return #j2t_ident(v, #inner_j2t)
                    })
                }
            }
        }
    }
    fn callable_j2p(&self) -> TokenStream {
        let j2p_ident = self.ident_j2p();
        match &self {
            // GType is local reference, so call to j2p_ident above is sufficient
            GType::GType(_) => {
                quote! {
                    #j2p_ident
                }
            }
            // Scalar is not generic, so call to j2p_ident above is sufficient
            GType::Scalar(_) => {
                quote! {
                    #j2p_ident
                }
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                let inner_j2p_call = vtype.callable_j2p();
                quote! {
                    ((v) => {
                        return #j2p_ident(v, #inner_j2p_call)
                    })
                }
            }
            GType::Result(ok, err) => {
                let ok_j2p_call = ok.callable_j2p();
                let err_j2p_call = err.callable_j2p();
                quote! {
                    ((v) => {
                        return #j2p_ident(v, #ok_j2p_call, #err_j2p_call)
                    })
                }
            }
            GType::Vec(inner) => {
                let inner_j2p_call = inner.callable_j2p();
                quote! {
                    ((v) => {
                        return #j2p_ident(v, #inner_j2p_call)
                    })
                }
            }
            GType::HashSet(inner) => {
                let inner_j2p = inner.ident_j2p();
                quote! {
                    ((v) => {
                        return #j2p_ident(v, #inner_j2p)
                    })
                }
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                let key_j2t = ktype.ident_j2t();
                let value_j2p_call = vtype.callable_j2p();
                quote! {
                    ((v) => {
                        return #j2p_ident(v, #key_j2t, #value_j2p_call)
                    })
                }
            }
            GType::BTreeMap(key, value) => {
                let key_j2p = key.ident_j2p();
                let value_j2p_call = value.callable_j2p();
                quote! {
                    ((v) => {
                        return #j2p_ident(v, #key_j2p, #value_j2p_call)
                    })
                }
            }
            GType::IndexMap(key, value) => {
                let key_j2p = key.ident_j2p();
                let value_j2p_call = value.callable_j2p();
                quote! {
                    ((v) => {
                        return #j2p_ident(v, #key_j2p, #value_j2p_call)
                    })
                }
            }
            GType::Range(scalar) => {
                let inner_j2p = scalar.ident_j2p();
                quote! {
                    ((v) => {
                        return #j2p_ident(v, #inner_j2p)
                    })
                }
            }
            GType::JsonValue => {
                quote! {
                    #j2p_ident
                }
            }
            GType::JsonObject => {
                quote! {
                    #j2p_ident
                }
            }
            GType::JsonArray => {
                quote! {
                    #j2p_ident
                }
            }
            GType::NestedError(gtype) => {
                let inner_j2p = gtype.callable_j2p();
                quote! {
                    ((v) => {
                        return #j2p_ident(v, #inner_j2p)
                    })
                }
            }
        }
    }
    fn callable_j2e(&self) -> TokenStream {
        let j2e_ident = self.ident_j2e();
        match &self {
            // GType is local reference, so call to j2e_ident above is sufficient
            GType::GType(_) => {
                quote! {
                    #j2e_ident
                }
            }
            // Scalar is not generic, so call to j2e_ident above is sufficient
            GType::Scalar(_) => {
                quote! {
                    #j2e_ident
                }
            }
            GType::Option {
                some_type: vtype, ..
            } => {
                let inner_j2e_call = vtype.callable_j2e();
                quote! {
                    ((v) => {
                        return #j2e_ident(v, #inner_j2e_call)
                    })
                }
            }
            GType::Result(_inner_ok, _inner_err) => {
                // Result_e is not generic
                quote! {
                    #j2e_ident
                }
            }
            GType::Vec(inner) => {
                let inner_j2e_call = inner.callable_j2e();
                quote! {
                    ((v) => {
                        return #j2e_ident(v, #inner_j2e_call)
                    })
                }
            }
            GType::HashSet(inner) => {
                let inner_j2e = inner.ident_j2e();
                quote! {
                    ((v) => {
                        return #j2e_ident(v, #inner_j2e)
                    })
                }
            }
            GType::HashMap {
                key_type: ktype,
                value_type: vtype,
                ..
            } => {
                let key_j2t = ktype.ident_j2t();
                let value_j2e_call = vtype.callable_j2e();
                quote! {
                    ((v) => {
                        return #j2e_ident(v, #key_j2t, #value_j2e_call)
                    })
                }
            }
            GType::BTreeMap(key, value) => {
                let key_j2e = key.ident_j2e();
                let value_j2e_call = value.callable_j2e();
                quote! {
                    ((v) => {
                        return #j2e_ident(v, #key_j2e, #value_j2e_call)
                    })
                }
            }
            GType::IndexMap(key, value) => {
                let key_j2e = key.ident_j2e();
                let value_j2e_call = value.callable_j2e();
                quote! {
                    ((v) => {
                        return #j2e_ident(v, #key_j2e, #value_j2e_call)
                    })
                }
            }
            GType::Range(scalar) => {
                let inner_j2e = scalar.ident_j2e();
                quote! {
                    ((v) => {
                        return #j2e_ident(v, #inner_j2e)
                    })
                }
            }
            GType::JsonValue => {
                quote! {
                    #j2e_ident
                }
            }
            GType::JsonObject => {
                quote! {
                    #j2e_ident
                }
            }
            GType::JsonArray => {
                quote! {
                    #j2e_ident
                }
            }
            GType::NestedError(gtype) => {
                let inner_j2e = gtype.callable_j2e();
                quote! {
                    ((v) => {
                        return #j2e_ident(v, #inner_j2e)
                    })
                }
            }
        }
    }
}

impl GTypeExtCallable for GScalar {
    fn callable_t2t(&self) -> TokenStream {
        let t2t_ident = self.ident_t2t();

        // The maximum number of possible validation terms is three (String is three, the rest are two)
        let mut vterms = match self {
            GScalar::String { .. } => Vec::with_capacity(3),
            _ => Vec::with_capacity(2),
        };

        // this will mutate vterms and add TypeScript validation code (TokenStream's)
        // based on the presence of validation attributes (ex. min, max, trim, etc.) per scalar type
        self.validation(&mut vterms);

        if vterms.is_empty() {
            quote! {
                #t2t_ident
            }
        } else {
            quote! {
                ((v) => {
                    return #t2t_ident(v, (v) => {
                        #(#vterms)*
                        return { Ok: v };
                    })
                })
            }
        }
    }
    fn callable_t2j(&self) -> TokenStream {
        let t2j_ident = self.ident_t2j();

        quote! {
            #t2j_ident
        }
    }
    fn callable_p2t(&self) -> TokenStream {
        let p2t_ident = self.ident_p2t();

        // The maximum number of possible validation terms is three (String is three, the rest are two)
        let mut vterms = match self {
            GScalar::String { .. } => Vec::with_capacity(3),
            _ => Vec::with_capacity(2),
        };

        // This will mutate vterms and add TypeScript validation code (TokenStream's)
        // based on the presence of validation attributes (ex. min, max, trim, etc.) per scalar type
        self.validation(&mut vterms);

        if vterms.is_empty() {
            quote! {
                #p2t_ident
            }
        } else {
            quote! {
                ((v) => {
                    return #p2t_ident(v, (v) => {
                        #(#vterms)*
                        return { Ok: v };
                    })
                })
            }
        }
    }
    fn callable_p2j(&self) -> TokenStream {
        let p2j_ident = self.ident_p2j();

        quote! {
            #p2j_ident
        }
    }
    fn callable_j2t(&self) -> TokenStream {
        let j2t_ident = self.ident_j2t();

        // The maximum number of possible validation terms is four (String is four, the rest are three)
        let mut vterms = match self {
            GScalar::String { .. } => Vec::with_capacity(4),
            _ => Vec::with_capacity(3),
        };

        // This will mutate vterms and add TypeScript validation code (TokenStream's)
        // based on the presence of validation attributes (ex. min, max, trim, etc.) per scalar type
        self.validation(&mut vterms);

        if vterms.is_empty() {
            quote! {
                #j2t_ident
            }
        } else {
            quote! {
                ((v) => {
                    return #j2t_ident(v, (v) => {
                        #(#vterms)*
                        return { Ok: v };
                    })
                })
            }
        }
    }
    fn callable_j2p(&self) -> TokenStream {
        let j2p_ident = self.ident_j2p();

        quote! {
            #j2p_ident
        }
    }
    fn callable_j2e(&self) -> TokenStream {
        let j2e_ident = self.ident_j2e();

        quote! {
            #j2e_ident
        }
    }
}

impl GTypeExtDefault for GType {
    fn default_p(&self) -> TokenStream {
        match self {
            GType::Scalar(scalar) => scalar.default_p(),
            _ => quote! {},
        }
    }

    fn default_j(&self) -> TokenStream {
        match self {
            GType::Scalar(scalar) => scalar.default_j(),
            _ => quote! { ?? null },
        }
    }
}

impl GTypeExtDefault for GScalar {
    fn default_p(&self) -> TokenStream {
        match self {
            GScalar::i8 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::i8_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::u8 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::u8_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::i16 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::i16_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::u16 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::u16_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::i32 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::i32_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::u32 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::u32_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::i64 {
                default: Some(default),
                ..
            } => {
                let value = default.to_string();
                quote! { ?? BigInt(#value) }
            }
            GScalar::u64 {
                default: Some(default),
                ..
            } => {
                let value = default.to_string();
                quote! { ?? BigInt(#value) }
            }
            GScalar::f32 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::f32_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::f64 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::f64_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::String {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::string(default);
                quote! { ?? #value }
            }
            GScalar::Decimal {
                default: Some(default),
                ..
            } => {
                let value = default.to_string();
                quote! { ?? Decimal::from(#value) }
            }
            _ => {
                quote! {}
            }
        }
    }

    fn default_j(&self) -> TokenStream {
        match self {
            GScalar::i8 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::i8_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::u8 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::u8_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::i16 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::i16_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::u16 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::u16_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::i32 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::i32_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::u32 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::u32_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::i64 {
                default: Some(default),
                ..
            } => {
                let value = default.to_string();
                quote! { ?? #value }
            }
            GScalar::u64 {
                default: Some(default),
                ..
            } => {
                let value = default.to_string();
                quote! { ?? #value }
            }
            GScalar::f32 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::f32_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::f64 {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::f64_unsuffixed(*default);
                quote! { ?? #value }
            }
            GScalar::String {
                default: Some(default),
                ..
            } => {
                let value = proc_macro2::Literal::string(default);
                quote! { ?? #value }
            }
            GScalar::Decimal {
                default: Some(default),
                ..
            } => {
                let value = default.to_string();
                quote! { ?? #value }
            }
            _ => {
                quote! { ?? null}
            }
        }
    }
}

impl GTypeExtValidation for GScalar {
    fn validation(&self, vterms: &mut Vec<TokenStream>) {
        match self {
            GScalar::i8 { min, max, .. } => {
                if let Some(value) = min {
                    let value = proc_macro2::Literal::i8_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v < #value) {
                            return { Err: "must be at least " + #value }
                        }
                    });
                }

                if let Some(value) = max {
                    let value = proc_macro2::Literal::i8_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v > #value) {
                            return { Err: "must not exceed " + #value }
                        }
                    });
                }
            }
            GScalar::u8 { min, max, .. } => {
                if let Some(value) = min {
                    let value = proc_macro2::Literal::u8_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v < #value) {
                            return { Err: "must be at least " + #value };
                        }
                    });
                }

                if let Some(value) = max {
                    let value = proc_macro2::Literal::u8_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v > #value) {
                            return { Err: "must not exceed " + #value };
                        }
                    });
                }
            }
            GScalar::i16 { min, max, .. } => {
                if let Some(value) = min {
                    let value = proc_macro2::Literal::i16_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v < #value) {
                            return { Err: "must be at least " + #value };
                        }
                    });
                }

                if let Some(value) = max {
                    let value = proc_macro2::Literal::i16_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v > #value) {
                            return { Err: "must not exceed " + #value };
                        }
                    });
                }
            }
            GScalar::u16 { min, max, .. } => {
                if let Some(value) = min {
                    let value = proc_macro2::Literal::u16_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v < #value) {
                            return { Err: "must be at least " + #value };
                        }
                    });
                }

                if let Some(value) = max {
                    let value = proc_macro2::Literal::u16_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v > #value) {
                            return { Err: "must not exceed " + #value };
                        }
                    });
                }
            }
            GScalar::i32 { min, max, .. } => {
                if let Some(value) = min {
                    let value = proc_macro2::Literal::i32_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v < #value) {
                            return { Err: "must be at least " + #value };
                        }
                    });
                }

                if let Some(value) = max {
                    let value = proc_macro2::Literal::i32_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v > #value) {
                            return { Err: "must not exceed " + #value };
                        }
                    });
                }
            }
            GScalar::u32 { min, max, .. } => {
                if let Some(value) = min {
                    let value = proc_macro2::Literal::u32_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v < #value) {
                            return { Err: "must be at least " + #value };
                        }
                    });
                }

                if let Some(value) = max {
                    let value = proc_macro2::Literal::u32_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v > #value) {
                            return { Err: "must not exceed " + #value };
                        }
                    });
                }
            }
            GScalar::i64 { min, max, .. } => {
                if let Some(value) = min {
                    let value = proc_macro2::Literal::i64_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v < #value) {
                            return { Err: "must be at least " + #value };
                        }
                    });
                }

                if let Some(value) = max {
                    let value = proc_macro2::Literal::i64_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v > #value) {
                            return { Err: "must not exceed " + #value };
                        }
                    });
                }
            }
            GScalar::u64 { min, max, .. } => {
                if let Some(value) = min {
                    let value = proc_macro2::Literal::u64_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v < #value) {
                            return { Err: "must be at least " + #value };
                        }
                    });
                }

                if let Some(value) = max {
                    let value = proc_macro2::Literal::u64_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v > #value) {
                            return { Err: "must not exceed " + #value };
                        }
                    });
                }
            }
            GScalar::f32 { min, max, .. } => {
                if let Some(value) = min {
                    let value = proc_macro2::Literal::f32_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v < #value) {
                            return { Err: "must be at least " + #value };
                        }
                    });
                }

                if let Some(value) = max {
                    let value = proc_macro2::Literal::f32_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v > #value) {
                            return { Err: "must not exceed " + #value };
                        }
                    });
                }
            }
            GScalar::f64 { min, max, .. } => {
                if let Some(value) = min {
                    let value = proc_macro2::Literal::f64_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v < #value) {
                            return { Err: "must be at least " + #value };
                        }
                    });
                }

                if let Some(value) = max {
                    let value = proc_macro2::Literal::f64_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v > #value) {
                            return { Err: "must not exceed " + #value };
                        }
                    });
                }
            }
            GScalar::String { min, max, trim, .. } => {
                if let Some(value) = min {
                    let value = proc_macro2::Literal::usize_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v.length < #value) {
                            const under = #value - v.length;
                            return { Err: under + (under === 1 ? " character" : " characters") + " too short" };
                        }
                    });
                }

                if let Some(value) = max {
                    let value = proc_macro2::Literal::usize_unsuffixed(*value);
                    vterms.push(quote! {
                        if (v.length > #value) {
                            const over = v.length - #value;
                            return { Err: over + (over === 1 ? " character" : " characters") + " too long" };
                        }
                    });
                }

                if let Some(value) = trim {
                    match value {
                        Trim::Start => {
                            vterms.push(quote! {
                                if (v) {
                                    v = v.trimStart();
                                }
                            });
                        }
                        Trim::End => {
                            vterms.push(quote! {
                                if (v) {
                                    v = v.trimEnd();
                                }
                            });
                        }
                        Trim::Both => {
                            vterms.push(quote! {
                                if (v) {
                                    v = v.trim();
                                }
                            });
                        }
                    }
                }
            }
            GScalar::Uuid { no_empty, version } => {
                if let Some(value) = no_empty {
                    if *value {
                        vterms.push(quote! {
                            if (!v) {
                                return { Err: "must not be empty" };
                            }
                        });
                    }
                }

                if let Some(value) = version {
                    let value = *value as u8;
                    // version must be between 1 and 7
                    match value {
                        1..=7 => {
                            // add regex to validate Uuid version
                            let number = proc_macro2::Literal::u8_unsuffixed(value);
                            let error_text = format!("must be Uuidv{}", value);
                            vterms.push(quote! {
                                if (!v.match(/^[0-9a-fA-F]{8}-[0-9a-f]{4}-[#number][0-9a-fA-F]{3}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/)) {
                                    return { Err: #error_text };
                                }
                            });
                        }
                        _ => {
                            // no-op
                        }
                    }
                }
            }
            GScalar::Decimal { min: _, max: _, .. } => {
                // TODO: implement
            }
            _ => {
                // no-op
            }
        }
    }
}

//! This module is responsible for parsing the `gtype` attribute macro.
//!
//! Under rust:
//!     e2j means "implement t2j on error type"
//!     

use granite_core::Decimal;
use quote::ToTokens;

use super::{
    GInner, GInnerEnum, GInnerStruct, GInnerType, GMacroInput, GMacroInputBuilder, GScalar, GType,
    GVariant, MacroOption, Trim,
};
use crate::tokenator::{Token, TokenError, TokenIter};
use proc_macro2::TokenStream;

/// Parse a syn::Item into a GADT enum
pub fn parse_gadt(attr: TokenStream, item: syn::Item) -> Result<GMacroInput, TokenError> {
    let macro_input_builder = match item {
        syn::Item::Enum(item_enum) => {
            let derive_option = parse_attr(attr, false)?;
            parse_enum(derive_option, item_enum)
        }
        syn::Item::Struct(item_struct) => {
            let derive_option = parse_attr(attr, false)?;
            parse_struct(derive_option, item_struct)
        }
        syn::Item::Type(item_type) => {
            let derive_option = parse_attr(attr, true)?;
            parse_type(derive_option, item_type)
        }
        _ => Err(TokenError::new_call_site(
            "gtype macro may only be used with `struct`, `enum`, or `type`",
        )),
    }?;

    Ok(macro_input_builder.build())
}

fn parse_attr(attr: TokenStream, is_type: bool) -> Result<MacroOption, TokenError> {
    let mut token_iter = TokenIter::new(attr);
    token_iter.step();

    let mut derive_option = MacroOption {
        rs_type: false,
        rs_type_validate: false,
        rs_type_encode: false,
        rs_type_decode: false,
        rs_partial: false,
        rs_partial_validate: false,
        rs_partial_encode: false,
        rs_partial_decode: false,
        rs_error: false,
        rs_error_encode: false,
        rs_error_decode: false,
        rs_debug: false,
        rs_clone: false,
        rs_partial_eq: false,

        ts_type: false,
        ts_type_validate: false,
        ts_type_encode: false,
        ts_type_decode: false,
        ts_partial: false,
        ts_partial_validate: false,
        ts_partial_encode: false,
        ts_partial_decode: false,
        ts_error: false,
        ts_error_encode: false,
        ts_error_decode: false,
        ts_from: None,
    };

    // Because we are in rust, if no attributes are specified, we assume that we want a rust type.
    if token_iter.is_end() {
        derive_option.rs_type = true;
        return Ok(derive_option);
    }

    loop {
        match token_iter.get_ident_as_string()?.as_ref() {
            "ApiInput" => {
                derive_option.rs_type = true;
                derive_option.rs_type_validate = true;
                derive_option.rs_type_decode = true;
                derive_option.rs_error = true;
                derive_option.rs_error_encode = true;
                derive_option.rs_debug = true;
                derive_option.ts_type = true;
                derive_option.ts_type_validate = true;
                derive_option.ts_type_encode = true;
                derive_option.ts_partial = true;
                derive_option.ts_partial_validate = true;
                derive_option.ts_error = true;
                derive_option.ts_error_decode = true;
                token_iter.step();
            }

            "ApiOutput" => {
                derive_option.rs_type = true;
                derive_option.rs_type_encode = true;
                derive_option.rs_debug = true;
                derive_option.ts_type = true;
                derive_option.ts_type_decode = true;
                token_iter.step();
            }

            "Api2Input" => {
                derive_option.rs_type = true;
                derive_option.rs_type_decode = true;
                derive_option.rs_debug = true;
                derive_option.ts_type = true;
                derive_option.ts_type_encode = true;
                token_iter.step();
            }

            "Api2Output" => {
                derive_option.rs_type = true;
                derive_option.rs_type_encode = true;
                derive_option.rs_debug = true;
                derive_option.ts_type = true;
                derive_option.ts_type_decode = true;
                token_iter.step();
            }

            "RsAll" => {
                derive_option.rs_type = true;
                derive_option.rs_type_validate = true;
                derive_option.rs_type_encode = true;
                derive_option.rs_type_decode = true;
                derive_option.rs_partial = true;
                derive_option.rs_partial_validate = true;
                derive_option.rs_partial_encode = true;
                derive_option.rs_partial_decode = true;
                derive_option.rs_error = true;
                derive_option.rs_error_encode = true;
                derive_option.rs_error_decode = true;
                token_iter.step();
            }

            "RsType" => {
                if derive_option.rs_type {
                    return Err(token_iter.error("`RsType` already specified"));
                }
                derive_option.rs_type = true;
                token_iter.step();
            }

            "RsTypeValidate" => {
                if derive_option.rs_type_validate {
                    return Err(token_iter.error("`RsTypeValidate` already specified"));
                }
                derive_option.rs_type_validate = true;
                token_iter.step();
            }

            "RsTypeEncode" => {
                if derive_option.rs_type_encode {
                    return Err(token_iter.error("`RsTypeEncode` already specified"));
                }
                derive_option.rs_type_encode = true;
                token_iter.step();
            }

            "RsTypeDecode" => {
                if derive_option.rs_type_decode {
                    return Err(token_iter.error("`RsTypeDecode` already specified"));
                }
                derive_option.rs_type_decode = true;
                token_iter.step();
            }

            "RsPartial" => {
                if derive_option.rs_partial {
                    return Err(token_iter.error("`RsPartial` already specified"));
                }
                derive_option.rs_partial = true;
                token_iter.step();
            }

            "RsPartialValidate" => {
                if derive_option.rs_partial_validate {
                    return Err(token_iter.error("`RsPartialValidate` already specified"));
                }
                derive_option.rs_partial_validate = true;
                token_iter.step();
            }

            "RsPartialEncode" => {
                if derive_option.rs_partial_encode {
                    return Err(token_iter.error("`RsPartialEncode` already specified"));
                }
                derive_option.rs_partial_encode = true;
                token_iter.step();
            }

            "RsPartialDecode" => {
                if derive_option.rs_partial_decode {
                    return Err(token_iter.error("`RsPartialDecode` already specified"));
                }
                derive_option.rs_partial_decode = true;
                token_iter.step();
            }

            "RsError" => {
                if derive_option.rs_error {
                    return Err(token_iter.error("`RsError` already specified"));
                }
                derive_option.rs_error = true;
                token_iter.step();
            }

            "RsErrorEncode" => {
                if derive_option.rs_error_encode {
                    return Err(token_iter.error("`RsErrorEncode` already specified"));
                }
                derive_option.rs_error_encode = true;
                token_iter.step();
            }

            "RsErrorDecode" => {
                if derive_option.rs_error_decode {
                    return Err(token_iter.error("`RsErrorDecode` already specified"));
                }
                derive_option.rs_error_decode = true;
                token_iter.step();
            }

            "RsDebug" => {
                if derive_option.rs_debug {
                    return Err(token_iter.error("`RsDebug` already specified"));
                }
                derive_option.rs_debug = true;
                token_iter.step();
            }

            "RsClone" => {
                if derive_option.rs_clone {
                    return Err(token_iter.error("`RsClone` already specified"));
                }
                derive_option.rs_clone = true;
                token_iter.step();
            }

            "RsPartialEq" => {
                if derive_option.rs_partial_eq {
                    return Err(token_iter.error("`RsPartialEq` already specified"));
                }
                derive_option.rs_partial_eq = true;
                token_iter.step();
            }

            "TsAll" => {
                derive_option.ts_type = true;
                derive_option.ts_type_validate = true;
                derive_option.ts_type_encode = true;
                derive_option.ts_type_decode = true;
                derive_option.ts_partial = true;
                derive_option.ts_partial_validate = true;
                derive_option.ts_partial_encode = true;
                derive_option.ts_partial_decode = true;
                derive_option.ts_error = true;
                derive_option.ts_error_encode = true;
                derive_option.ts_error_decode = true;
                token_iter.step();
            }

            "TsType" => {
                if derive_option.ts_type {
                    return Err(token_iter.error("`TsType` already specified"));
                }
                derive_option.ts_type = true;
                token_iter.step();
            }

            "TsTypeValidate" => {
                if derive_option.ts_type_validate {
                    return Err(token_iter.error("`TsTypeValidate` already specified"));
                }
                derive_option.ts_type_validate = true;
                token_iter.step();
            }

            "TsTypeEncode" => {
                if derive_option.ts_type_encode {
                    return Err(token_iter.error("`TsTypeEncode` already specified"));
                }
                derive_option.ts_type_encode = true;
                token_iter.step();
            }

            "TsTypeDecode" => {
                if derive_option.ts_type_decode {
                    return Err(token_iter.error("`TsTypeDecode` already specified"));
                }
                derive_option.ts_type_decode = true;
                token_iter.step();
            }

            "TsPartial" => {
                if derive_option.ts_partial {
                    return Err(token_iter.error("`TsPartial` already specified"));
                }
                derive_option.ts_partial = true;
                token_iter.step();
            }

            "TsPartialValidate" => {
                if derive_option.ts_partial_validate {
                    return Err(token_iter.error("`TsPartialValidate` already specified"));
                }
                derive_option.ts_partial_validate = true;
                token_iter.step();
            }

            "TsPartialEncode" => {
                if derive_option.ts_partial_encode {
                    return Err(token_iter.error("`TsPartialEncode` already specified"));
                }
                derive_option.ts_partial_encode = true;
                token_iter.step();
            }

            "TsPartialDecode" => {
                if derive_option.ts_partial_decode {
                    return Err(token_iter.error("`TsPartialDecode` already specified"));
                }
                derive_option.ts_partial_decode = true;
                token_iter.step();
            }

            "TsError" => {
                if derive_option.ts_error {
                    return Err(token_iter.error("`TsError` already specified"));
                }
                derive_option.ts_error = true;
                token_iter.step();
            }

            "TsErrorEncode" => {
                if derive_option.ts_error_encode {
                    return Err(token_iter.error("`TsErrorEncode` already specified"));
                }
                derive_option.ts_error_encode = true;
                token_iter.step();
            }

            "TsErrorDecode" => {
                if derive_option.ts_error_decode {
                    return Err(token_iter.error("`TsErrorDecode` already specified"));
                }
                derive_option.ts_error_decode = true;
                token_iter.step();
            }

            "ts_from" => {
                if !is_type {
                    return Err(token_iter.error("`ts_from` only valid for `type`"));
                }

                if derive_option.ts_from.is_some() {
                    return Err(token_iter.error("`ts_from` already specified"));
                }
                token_iter.step();
                token_iter.take_equals()?;
                let ident_str = token_iter.take_string()?;
                derive_option.ts_from = Some(ident_str);
            }

            ident => {
                return Err(token_iter.error(&format!("Invalid input: `{ident}`; Expected one of [`RsType`, `RsTypeValidate`, `RsTypeEncode`, `RsTypeDecode`, `RsPartial`, `RsPartialValidate`, `RsPartialEncode`, `RsPartialDecode`, `RsError`, `RsErrorEncode`, `RsErrorDecode`, `RsDebug`, `RsClone`, `RsPartialEq`, `TsType`, `TsTypeValidate`, `TsTypeEncode`, `TsTypeDecode`, `TsPartial`, `TsPartialValidate`, `TsPartialEncode`, `TsPartialDecode`, `TsError`, `TsErrorEncode`, `TsErrorDecode`, `ts_from`].")));
            }
        }

        if token_iter.is_end() {
            break;
        }

        token_iter.take_comma()?;

        if token_iter.is_end() {
            break;
        }
    }

    // validate
    if derive_option.rs_type_validate && (!derive_option.rs_type || !derive_option.rs_error) {
        return Err(token_iter.error("`RsTypeValidate` requires `RsType` and `RsError`"));
    }

    if derive_option.rs_type_encode && !derive_option.rs_type {
        return Err(token_iter.error("`RsTypeEncode` requires `RsType`"));
    }

    if derive_option.rs_type_decode && !derive_option.rs_type {
        return Err(token_iter.error("`RsTypeDecode` requires `RsType`"));
    }

    if derive_option.rs_partial_validate && (!derive_option.rs_partial || !derive_option.rs_error) {
        return Err(token_iter.error("`RsPartialValidate` requires `RsPartial` and `RsError`"));
    }

    if derive_option.rs_partial_encode && !derive_option.rs_partial {
        return Err(token_iter.error("`RsPartialEncode` requires `RsPartial`"));
    }

    if derive_option.rs_partial_decode && !derive_option.rs_partial {
        return Err(token_iter.error("`RsPartialDecode` requires `RsPartial`"));
    }

    if derive_option.rs_error_encode && !derive_option.rs_error {
        return Err(token_iter.error("`RsErrorEncode` requires `RsError`"));
    }

    if derive_option.rs_error_decode && !derive_option.rs_error {
        return Err(token_iter.error("`RsErrorDecode` requires `RsError`"));
    }

    if derive_option.ts_type_validate && (!derive_option.ts_type || !derive_option.ts_error) {
        return Err(token_iter.error("`TsTypeValidate` requires `TsType` and `TsError`"));
    }

    if derive_option.ts_type_encode && !derive_option.ts_type {
        return Err(token_iter.error("`TsTypeEncode` requires `TsType`"));
    }

    if derive_option.ts_type_decode && !derive_option.ts_type {
        return Err(token_iter.error("`TsTypeDecode` requires `TsType`"));
    }

    if derive_option.ts_partial_validate
        && (!derive_option.ts_type || !derive_option.ts_partial || !derive_option.ts_error)
    {
        return Err(
            token_iter.error("`TsPartialValidate` requires `TSType` and `TsPartial` and `TsError`")
        );
    }

    if derive_option.ts_partial_encode && !derive_option.ts_partial {
        return Err(token_iter.error("`TsPartialEncode` requires `TsPartial`"));
    }

    if derive_option.ts_partial_decode && !derive_option.ts_partial {
        return Err(token_iter.error("`TsPartialDecode` requires `TsPartial`"));
    }

    if derive_option.ts_error_encode && !derive_option.ts_error {
        return Err(token_iter.error("`TsErrorEncode` requires `TsError`"));
    }

    if derive_option.ts_error_decode && !derive_option.ts_error {
        return Err(token_iter.error("`TsErrorDecode` requires `TsError`"));
    }

    if derive_option.ts_from.is_some() && !is_type {
        return Err(token_iter.error("`ts_from` only valid for `type`"));
    }

    Ok(derive_option)
}

fn parse_struct(
    macro_option: MacroOption,
    item_struct: syn::ItemStruct,
) -> Result<GMacroInputBuilder, TokenError> {
    let ident = item_struct.ident;
    let vis = item_struct.vis;
    let attrs = item_struct.attrs;

    // construct a syn::path::Path to "gtype"
    let gtype_path = syn::parse_str::<syn::Path>("gtype").unwrap(); // okay because it is a literal

    match item_struct.fields {
        syn::Fields::Unit => Ok(GMacroInputBuilder {
            macro_option,
            vis,
            ident,
            attrs,
            g_inner: GInner::Struct(GInnerStruct::Unit),
            mod_ident: None,
        }),
        syn::Fields::Unnamed(fields) => {
            let mut doubles = Vec::with_capacity(fields.unnamed.len());

            for field in fields.unnamed.iter() {
                let vis = field.vis.clone();

                let mut token_iter = crate::tokenator::TokenIter::new(field.ty.to_token_stream());
                // advance to first token
                token_iter.step();

                // Parse the gtype, and leave iterator on token after gtype
                let gtype = take_gtype(None, &mut token_iter)?;

                // Ensure we are at the end of the token stream (for this field.ty token stream)
                token_iter.get_end()?;

                doubles.push((vis, gtype));
            }
            Ok(GMacroInputBuilder {
                macro_option,
                vis,
                ident,
                attrs,
                g_inner: GInner::Struct(GInnerStruct::Tuple(doubles)),
                mod_ident: None,
            })
        }
        syn::Fields::Named(fields) => {
            let mut triples = Vec::with_capacity(fields.named.len());

            for field in fields.named.iter() {
                let vis = field.vis.clone();
                let ident = field.ident.clone().expect("all fields must have an ident");

                let mut token_iter = crate::tokenator::TokenIter::new(field.ty.to_token_stream());
                // advance to first token
                token_iter.step();

                // Parse the gtype validation attributes, and leave iterator on token after gtype
                let validation_token_iter = parse_gtype_validation_attribute(field, &gtype_path)?;

                let error_span = token_iter.span();

                // Parse the gtype, and leave iterator on token after gtype
                let gtype = take_gtype(validation_token_iter, &mut token_iter)?;

                // Validate excluded types
                if (macro_option.rs_error || macro_option.ts_error)
                    && matches!(gtype, GType::Result(_, _))
                {
                    return Err(TokenError::new(
                        error_span,
                        "Result type may not be used with Error type",
                    ));
                }

                // Ensure we are at the end of the token stream (for this field.ty token stream)
                token_iter.get_end()?;

                // save the field
                triples.push((vis, ident, gtype));
            }

            Ok(GMacroInputBuilder {
                macro_option,
                vis,
                ident,
                attrs,
                g_inner: GInner::Struct(GInnerStruct::Named(triples)),
                mod_ident: None,
            })
        }
    }
}

fn parse_enum(
    derive_option: MacroOption,
    item_enum: syn::ItemEnum,
) -> Result<GMacroInputBuilder, TokenError> {
    let ident = item_enum.ident;
    let vis = item_enum.vis;
    let attrs = item_enum.attrs;

    let mut ident_gvariant_list = Vec::with_capacity(item_enum.variants.len());

    let gtype_path = syn::parse_str::<syn::Path>("gtype").unwrap(); // okay because it is a literal

    for variant in item_enum.variants {
        let ident = variant.ident;

        let gvariant = match variant.fields {
            syn::Fields::Unit => GVariant::Unit,
            syn::Fields::Unnamed(fields) => {
                let mut singles = Vec::with_capacity(fields.unnamed.len());

                for field in fields.unnamed.iter() {
                    let mut token_iter =
                        crate::tokenator::TokenIter::new(field.ty.to_token_stream());
                    // advance to first token
                    token_iter.step();

                    // Parse the gtype, and leave iterator on token after gtype
                    let gtype = take_gtype(None, &mut token_iter)?;

                    // Ensure we are at the end of the token stream (for this field.ty token stream)
                    token_iter.get_end()?;

                    singles.push((gtype,));
                }
                GVariant::Tuple(singles)
            }
            syn::Fields::Named(fields) => {
                let mut doubles = Vec::with_capacity(fields.named.len());

                for field in fields.named.iter() {
                    let ident = field
                        .ident
                        .clone()
                        .expect("named fields must have an ident");

                    let mut token_iter =
                        crate::tokenator::TokenIter::new(field.ty.to_token_stream());
                    // advance to first token
                    token_iter.step();

                    // Parse the gtype validation attributes, and leave iterator on token after gtype
                    let validation_token_iter =
                        parse_gtype_validation_attribute(field, &gtype_path)?;

                    // Parse the gtype, and leave iterator on token after gtype
                    let gtype = take_gtype(validation_token_iter, &mut token_iter)?;

                    // Ensure we are at the end of the token stream (for this field.ty token stream)
                    token_iter.get_end()?;

                    // save the field (double)
                    doubles.push((ident, gtype));
                }

                GVariant::Struct(doubles)
            }
        };

        ident_gvariant_list.push((ident, gvariant));
    }

    Ok(GMacroInputBuilder {
        macro_option: derive_option,
        vis,
        ident,
        attrs,
        g_inner: GInner::Enum(GInnerEnum(ident_gvariant_list)),
        mod_ident: None,
    })
}

fn parse_gtype_validation_attribute(
    field: &syn::Field,
    gtype_path: &syn::Path,
) -> Result<Option<TokenIter>, TokenError> {
    // Don't allow more than one gtype attribute
    let gtype_attr = {
        let mut v_attr_iter: Vec<&syn::Attribute> = field
            .attrs
            .iter()
            .filter(|attr| crate::syn_path_equal(attr.path(), gtype_path))
            .collect();
        match v_attr_iter.len() {
            0 => {
                // No gtype attribute found
                None
            }
            1 => {
                // One gtype attribute found
                Some(v_attr_iter.pop().unwrap())
            }
            _ => {
                // More than one gtype attribute found
                return Err(TokenError::new_call_site(
                    "Only one `gtype` validation attribute allowed per named field",
                ));
            }
        }
    };

    match gtype_attr {
        Some(attr) => match attr.parse_args::<proc_macro2::TokenStream>() {
            Ok(tokens) => {
                let mut validation_token_iter = crate::tokenator::TokenIter::new(tokens);
                validation_token_iter.step();
                Ok(Some(validation_token_iter))
            }
            Err(e) => Err(TokenError::new_call_site(&format!(
                "Error parsing `gtype` attribute: {e}"
            ))),
        },
        None => Ok(None),
    }
}

fn parse_type(
    macro_option: MacroOption,
    item_type: syn::ItemType,
) -> Result<GMacroInputBuilder, TokenError> {
    let ident = item_type.ident;
    let vis = item_type.vis;
    let attrs = item_type.attrs;
    let type_path = match *item_type.ty {
        syn::Type::Path(ty) => ty,
        _ => {
            return Err(TokenError::new_call_site(
                "gtype macro may only be used with `type`",
            ));
        }
    };

    let mut token_iter = crate::tokenator::TokenIter::new(type_path.to_token_stream());
    token_iter.step();

    // Must start with `super`, `self`, `crate`, or `::`
    match token_iter.token() {
        Token::Colon => {
            // do not step
        }
        Token::Ident(ident) => match ident.to_string().as_ref() {
            "super" | "self" | "crate" => {
                token_iter.step();
            }
            s => {
                return Err(token_iter.error(&format!(
                    "expected `super`, `self`, `crate`, or `::`, not `{s}`"
                )));
            }
        },
        t => {
            return Err(token_iter.error(&format!(
                "expected `super`, `self`, `crate`, or `::`, not `{t:?}`"
            )));
        }
    }

    // Afterwards, it is ::ident<repeat>
    loop {
        token_iter.take_colon()?;
        token_iter.take_colon()?;

        token_iter.take_ident()?;

        if token_iter.is_end() {
            break;
        }
    }

    let import_from = macro_option.ts_from.clone();

    Ok(GMacroInputBuilder {
        macro_option,
        vis,
        ident,
        attrs,
        g_inner: GInner::Type(GInnerType {
            type_path,
            import_from,
        }),
        mod_ident: None,
    })
}

trait TokenIterExt {
    fn take_default_signed<T>(&mut self, value: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr + std::ops::Neg<Output = T>,
        T::Err: std::fmt::Display;

    fn take_min_signed<T>(&mut self, value: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr + std::ops::Neg<Output = T>,
        T::Err: std::fmt::Display;

    fn take_max_signed<T>(&mut self, max: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr + std::ops::Neg<Output = T>,
        T::Err: std::fmt::Display;

    fn take_default_unsigned<T>(&mut self, value: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr,
        T::Err: std::fmt::Display;

    fn take_min_unsigned<T>(&mut self, min: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr,
        T::Err: std::fmt::Display;

    fn take_max_unsigned<T>(&mut self, max: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr,
        T::Err: std::fmt::Display;

    fn take_default_float<T>(&mut self, value: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr,
        T::Err: std::fmt::Display;

    fn take_min_float<T>(&mut self, min: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr + std::ops::Neg<Output = T>,
        T::Err: std::fmt::Display;

    fn take_max_float<T>(&mut self, max: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr + std::ops::Neg<Output = T>,
        T::Err: std::fmt::Display;

    fn take_default_decimal(&mut self, value: &mut Option<Decimal>) -> Result<(), TokenError>;

    fn take_min_decimal(&mut self, value: &mut Option<Decimal>) -> Result<(), TokenError>;

    fn take_max_decimal(&mut self, value: &mut Option<Decimal>) -> Result<(), TokenError>;

    fn take_max_items(&mut self, value: &mut Option<usize>) -> Result<(), TokenError>
    where
        usize: std::str::FromStr;

    fn take_default_string(&mut self, value: &mut Option<String>) -> Result<(), TokenError>
    where
        String: std::str::FromStr;

    fn take_zero_to_none(&mut self, value: &mut Option<bool>) -> Result<(), TokenError>;

    fn take_empty_to_none(&mut self, value: &mut Option<bool>) -> Result<(), TokenError>;

    fn take_no_empty(&mut self, value: &mut Option<bool>) -> Result<(), TokenError>;

    fn take_trim(&mut self, value: &mut Option<Trim>) -> Result<(), TokenError>;

    fn take_version(&mut self, value: &mut Option<usize>) -> Result<(), TokenError>;
}

impl TokenIterExt for TokenIter {
    fn take_default_signed<T>(&mut self, value: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr + std::ops::Neg<Output = T>,
        T::Err: std::fmt::Display,
    {
        if value.is_some() {
            return Err(self.error("default already set"));
        }

        self.take_ident_match("default")?;
        self.take_equals()?;
        let v = self.take_signed_number::<T>()?;
        self.take_semicolon()?;
        *value = Some(v);
        Ok(())
    }

    fn take_min_signed<T>(&mut self, value: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr + std::ops::Neg<Output = T>,
        T::Err: std::fmt::Display,
    {
        if value.is_some() {
            return Err(self.error("min already set"));
        }

        self.take_ident_match("min")?;
        self.take_equals()?;
        let v = self.take_signed_number::<T>()?;
        self.take_semicolon()?;
        *value = Some(v);
        Ok(())
    }

    fn take_max_signed<T>(&mut self, value: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr + std::ops::Neg<Output = T>,
        T::Err: std::fmt::Display,
    {
        if value.is_some() {
            return Err(self.error("max already set"));
        }

        self.take_ident_match("max")?;
        self.take_equals()?;
        let v = self.take_signed_number::<T>()?;
        self.take_semicolon()?;
        *value = Some(v);
        Ok(())
    }

    fn take_default_unsigned<T>(&mut self, value: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr,
        T::Err: std::fmt::Display,
    {
        if value.is_some() {
            return Err(self.error("default already set"));
        }

        self.take_ident_match("default")?;
        self.take_equals()?;
        let v = self.take_unsigned_number::<T>()?;
        self.take_semicolon()?;
        *value = Some(v);
        Ok(())
    }

    fn take_min_unsigned<T>(&mut self, min: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr,
        T::Err: std::fmt::Display,
    {
        if min.is_some() {
            return Err(self.error("min already set"));
        }

        self.take_ident_match("min")?;
        self.take_equals()?;
        let v = self.take_unsigned_number::<T>()?;
        self.take_semicolon()?;
        *min = Some(v);
        Ok(())
    }

    fn take_max_unsigned<T>(&mut self, max: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr,
        T::Err: std::fmt::Display,
    {
        if max.is_some() {
            return Err(self.error("max already set"));
        }

        self.take_ident_match("max")?;
        self.take_equals()?;
        let v = self.take_unsigned_number::<T>()?;
        self.take_semicolon()?;
        *max = Some(v);
        Ok(())
    }

    fn take_default_float<T>(&mut self, value: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr,
        T::Err: std::fmt::Display,
    {
        if value.is_some() {
            return Err(self.error("default already set"));
        }

        self.take_ident_match("default")?;
        self.take_equals()?;
        let v = self.take_float::<T>()?;
        self.take_semicolon()?;
        *value = Some(v);
        Ok(())
    }

    fn take_min_float<T>(&mut self, value: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr,
        T::Err: std::fmt::Display,
    {
        if value.is_some() {
            return Err(self.error("min already set"));
        }

        self.take_ident_match("min")?;
        self.take_equals()?;
        let v = self.take_float::<T>()?;
        self.take_semicolon()?;
        *value = Some(v);
        Ok(())
    }

    fn take_max_float<T>(&mut self, value: &mut Option<T>) -> Result<(), TokenError>
    where
        T: std::str::FromStr,
        T::Err: std::fmt::Display,
    {
        if value.is_some() {
            return Err(self.error("max already set"));
        }

        self.take_ident_match("max")?;
        self.take_equals()?;
        let v = self.take_float::<T>()?;
        self.take_semicolon()?;
        *value = Some(v);
        Ok(())
    }

    fn take_default_decimal(&mut self, value: &mut Option<Decimal>) -> Result<(), TokenError> {
        if value.is_some() {
            return Err(self.error("default already set"));
        }

        self.take_ident_match("default")?;
        self.take_equals()?;
        let v = self.take_decimal()?;
        self.take_semicolon()?;
        *value = Some(v);
        Ok(())
    }

    fn take_min_decimal(&mut self, value: &mut Option<Decimal>) -> Result<(), TokenError> {
        if value.is_some() {
            return Err(self.error("min already set"));
        }

        self.take_ident_match("min")?;
        self.take_equals()?;
        let v = self.take_decimal()?;
        self.take_semicolon()?;
        *value = Some(v);
        Ok(())
    }

    fn take_max_decimal(&mut self, value: &mut Option<Decimal>) -> Result<(), TokenError> {
        if value.is_some() {
            return Err(self.error("max already set"));
        }

        self.take_ident_match("max")?;
        self.take_equals()?;
        let v = self.take_decimal()?;
        self.take_semicolon()?;
        *value = Some(v);
        Ok(())
    }

    fn take_max_items(&mut self, value: &mut Option<usize>) -> Result<(), TokenError> {
        if value.is_some() {
            return Err(self.error("max_items already set"));
        }

        self.take_ident_match("max_items")?;
        self.take_equals()?;
        let v = self.take_unsigned_number::<usize>()?;
        self.take_semicolon()?;
        *value = Some(v);
        Ok(())
    }

    fn take_default_string(&mut self, value: &mut Option<String>) -> Result<(), TokenError>
    where
        String: std::str::FromStr,
    {
        if value.is_some() {
            return Err(self.error("default already set"));
        }

        self.take_ident_match("default")?;
        self.take_equals()?;
        let v = self.take_string()?;
        self.take_semicolon()?;
        *value = Some(v);
        Ok(())
    }

    fn take_zero_to_none(&mut self, value: &mut Option<bool>) -> Result<(), TokenError> {
        if value.is_some() {
            return Err(self.error("zero_to_none already set"));
        }

        self.take_ident_match("zero_to_none")?;
        self.take_semicolon()?;
        *value = Some(true);
        Ok(())
    }

    fn take_empty_to_none(&mut self, value: &mut Option<bool>) -> Result<(), TokenError> {
        if value.is_some() {
            return Err(self.error("empty_to_none already set"));
        }

        self.take_ident_match("empty_to_none")?;
        self.take_semicolon()?;
        *value = Some(true);
        Ok(())
    }

    fn take_no_empty(&mut self, value: &mut Option<bool>) -> Result<(), TokenError> {
        if value.is_some() {
            return Err(self.error("no_empty already set"));
        }

        self.take_ident_match("no_empty")?;
        self.take_semicolon()?;
        *value = Some(true);
        Ok(())
    }

    fn take_trim(&mut self, value: &mut Option<Trim>) -> Result<(), TokenError> {
        if value.is_some() {
            return Err(self.error("trim already set"));
        }

        self.take_ident_match("trim")?;
        self.take_equals()?;

        match self.token() {
            Token::Ident(ident) => match ident.to_string().as_ref() {
                "start" => {
                    self.take_ident_match("start")?;
                    *value = Some(Trim::Start);
                }
                "end" => {
                    self.take_ident_match("end")?;
                    *value = Some(Trim::End);
                }
                "both" => {
                    self.take_ident_match("both")?;
                    *value = Some(Trim::Both);
                }
                t => {
                    return Err(self
                        .error(format!("expected `start`, `end`, or `both` not: {t:?}").as_str()));
                }
            },
            t => {
                return Err(self.error(&format!("expected `start`, `end`, or both` not: {t:?}")));
            }
        }

        self.take_semicolon()?;

        Ok(())
    }

    fn take_version(&mut self, value: &mut Option<usize>) -> Result<(), TokenError> {
        if value.is_some() {
            return Err(self.error("version already set"));
        }

        self.take_ident_match("version")?;
        self.take_equals()?;
        let v = self.take_unsigned_number::<usize>()?;
        self.take_semicolon()?;
        *value = Some(v);
        Ok(())
    }
}

// TODO: Check for valid String attributes
// TODO: Are `empty_to_none` and `no_empty` mutually exclusive?
// TODO: Add precision and scale to float/decimal types
fn take_gtype(
    token_iter_attr: Option<TokenIter>,
    token_iter: &mut TokenIter,
) -> Result<GType, TokenError> {
    macro_rules! check_number_default_min_max {
        ($token_iter_attr:expr, $default:expr, $min:expr, $max:expr) => {
            if let (Some(default), Some(min)) = ($default, $min) {
                if default < min {
                    return Err(
                        token_iter.error(format!("default must be at least {min}").as_str())
                    );
                }
            }

            if let (Some(default), Some(max)) = ($default, $max) {
                if default > max {
                    return Err(token_iter.error(format!("default must not exceed {max}").as_str()));
                }
            }

            if let (Some(min), Some(max)) = ($min, $max) {
                if min > max {
                    return Err($token_iter_attr
                        .error(format!("min ({min}) must be less than max ({max})").as_str()));
                }
            }
        };
    }

    let gtype = match token_iter.get_ident_as_string()?.as_ref() {
        // scalar types
        "char" => {
            token_iter.step();
            GType::Scalar(GScalar::char)
        }
        "bool" => {
            token_iter.step();
            GType::Scalar(GScalar::bool)
        }
        "i8" => {
            let mut default: Option<i8> = None;
            let mut min: Option<i8> = None;
            let mut max: Option<i8> = None;

            if let Some(mut token_iter_attr) = token_iter_attr {
                loop {
                    match token_iter_attr.token() {
                        Token::Ident(ident) => match ident.to_string().as_ref() {
                            "default" => token_iter_attr.take_default_signed(&mut default)?,
                            "min" => token_iter_attr.take_min_signed(&mut min)?,
                            "max" => token_iter_attr.take_max_signed(&mut max)?,
                            t => {
                                return Err(
                                    token_iter_attr.error(&format!("Unexpected Ident: `{t}`"))
                                );
                            }
                        },
                        Token::End => {
                            break;
                        }
                        t => {
                            return Err(token_iter_attr.error(&format!("Unexpected Token: {t:?}")));
                        }
                    }
                }
                check_number_default_min_max!(token_iter_attr, default, min, max);
            }

            token_iter.step();
            GType::Scalar(GScalar::i8 { default, min, max })
        }
        "u8" => {
            let mut default: Option<u8> = None;
            let mut min: Option<u8> = None;
            let mut max: Option<u8> = None;

            if let Some(mut token_iter_attr) = token_iter_attr {
                loop {
                    match token_iter_attr.token() {
                        Token::Ident(ident) => match ident.to_string().as_ref() {
                            "default" => token_iter_attr.take_default_unsigned(&mut default)?,
                            "min" => token_iter_attr.take_min_unsigned(&mut min)?,
                            "max" => token_iter_attr.take_max_unsigned(&mut max)?,
                            t => {
                                return Err(
                                    token_iter_attr.error(&format!("Unexpected token `{t}`"))
                                );
                            }
                        },
                        Token::End => {
                            break;
                        }
                        t => return Err(token_iter_attr.error(&format!("Unexpect Token: {t:?}"))),
                    }
                }
                check_number_default_min_max!(token_iter_attr, default, min, max);
            }

            token_iter.step();
            GType::Scalar(GScalar::u8 { default, min, max })
        }
        "i16" => {
            let mut default: Option<i16> = None;
            let mut min: Option<i16> = None;
            let mut max: Option<i16> = None;

            if let Some(mut token_iter_attr) = token_iter_attr {
                loop {
                    match token_iter_attr.token() {
                        Token::Ident(ident) => match ident.to_string().as_ref() {
                            "default" => token_iter_attr.take_default_signed(&mut default)?,
                            "min" => token_iter_attr.take_min_signed(&mut min)?,
                            "max" => token_iter_attr.take_max_signed(&mut max)?,
                            t => {
                                return Err(
                                    token_iter_attr.error(&format!("Unexpected Ident: `{t}`"))
                                );
                            }
                        },
                        Token::End => {
                            break;
                        }
                        t => {
                            return Err(token_iter_attr.error(&format!("Unepexcted Token: {t:?}")));
                        }
                    }
                }
                check_number_default_min_max!(token_iter_attr, default, min, max);
            }

            token_iter.step();
            GType::Scalar(GScalar::i16 { default, min, max })
        }
        "u16" => {
            let mut default: Option<u16> = None;
            let mut min: Option<u16> = None;
            let mut max: Option<u16> = None;

            if let Some(mut token_iter_attr) = token_iter_attr {
                loop {
                    match token_iter_attr.token() {
                        Token::Ident(ident) => match ident.to_string().as_ref() {
                            "default" => token_iter_attr.take_default_unsigned(&mut default)?,
                            "min" => token_iter_attr.take_min_unsigned(&mut min)?,
                            "max" => token_iter_attr.take_max_unsigned(&mut max)?,
                            t => {
                                return Err(
                                    token_iter_attr.error(&format!("Unexpected Ident: `{t}`"))
                                );
                            }
                        },
                        Token::End => {
                            break;
                        }
                        t => {
                            return Err(token_iter_attr.error(&format!("Unexpected Token: {t:?}")));
                        }
                    }
                }
                check_number_default_min_max!(token_iter_attr, default, min, max);
            }

            token_iter.step();
            GType::Scalar(GScalar::u16 { default, min, max })
        }
        "i32" => {
            let mut default: Option<i32> = None;
            let mut min: Option<i32> = None;
            let mut max: Option<i32> = None;

            if let Some(mut token_iter_attr) = token_iter_attr {
                loop {
                    match token_iter_attr.token() {
                        Token::Ident(ident) => match ident.to_string().as_ref() {
                            "default" => token_iter_attr.take_default_signed(&mut default)?,
                            "min" => token_iter_attr.take_min_signed(&mut min)?,
                            "max" => token_iter_attr.take_max_signed(&mut max)?,
                            t => {
                                return Err(
                                    token_iter_attr.error(&format!("Unexpected Ident: `{t}`"))
                                );
                            }
                        },
                        Token::End => {
                            break;
                        }
                        t => {
                            return Err(token_iter_attr.error(&format!("Unepexcted Token: {t:?}")));
                        }
                    }
                }
                check_number_default_min_max!(token_iter_attr, default, min, max);
            }

            token_iter.step();
            GType::Scalar(GScalar::i32 { default, min, max })
        }
        "u32" => {
            let mut default: Option<u32> = None;
            let mut min: Option<u32> = None;
            let mut max: Option<u32> = None;

            if let Some(mut token_iter_attr) = token_iter_attr {
                loop {
                    match token_iter_attr.token() {
                        Token::Ident(ident) => match ident.to_string().as_ref() {
                            "default" => token_iter_attr.take_default_unsigned(&mut default)?,
                            "min" => token_iter_attr.take_min_unsigned(&mut min)?,
                            "max" => token_iter_attr.take_max_unsigned(&mut max)?,
                            t => {
                                return Err(
                                    token_iter_attr.error(&format!("Unexpected Ident: `{t}`"))
                                );
                            }
                        },
                        Token::End => {
                            break;
                        }
                        t => {
                            return Err(token_iter_attr.error(&format!("Unexpected Token: {t:?}")));
                        }
                    }
                }
                check_number_default_min_max!(token_iter_attr, default, min, max);
            }

            token_iter.step();
            GType::Scalar(GScalar::u32 { default, min, max })
        }
        "i64" => {
            let mut default: Option<i64> = None;
            let mut min: Option<i64> = None;
            let mut max: Option<i64> = None;

            if let Some(mut token_iter_attr) = token_iter_attr {
                loop {
                    match token_iter_attr.token() {
                        Token::Ident(ident) => match ident.to_string().as_ref() {
                            "default" => token_iter_attr.take_default_signed(&mut default)?,
                            "min" => token_iter_attr.take_min_signed(&mut min)?,
                            "max" => token_iter_attr.take_max_signed(&mut max)?,
                            t => {
                                return Err(
                                    token_iter_attr.error(&format!("Unexpected Ident: `{t}`"))
                                );
                            }
                        },
                        Token::End => {
                            break;
                        }
                        t => {
                            return Err(token_iter_attr.error(&format!("Unepexcted Token: {t:?}")));
                        }
                    }
                }
                check_number_default_min_max!(token_iter_attr, default, min, max);
            }

            token_iter.step();
            GType::Scalar(GScalar::i64 { default, min, max })
        }
        "u64" => {
            let mut default: Option<u64> = None;
            let mut min: Option<u64> = None;
            let mut max: Option<u64> = None;

            if let Some(mut token_iter_attr) = token_iter_attr {
                loop {
                    match token_iter_attr.token() {
                        Token::Ident(ident) => match ident.to_string().as_ref() {
                            "default" => token_iter_attr.take_default_unsigned(&mut default)?,
                            "min" => token_iter_attr.take_min_unsigned(&mut min)?,
                            "max" => token_iter_attr.take_max_unsigned(&mut max)?,
                            t => {
                                return Err(
                                    token_iter_attr.error(&format!("Unexpected Ident: `{t}`"))
                                );
                            }
                        },
                        Token::End => {
                            break;
                        }
                        t => {
                            return Err(token_iter_attr.error(&format!("Unexpected Token: {t:?}")));
                        }
                    }
                }
                check_number_default_min_max!(token_iter_attr, default, min, max);
            }

            token_iter.step();
            GType::Scalar(GScalar::u64 { default, min, max })
        }
        "f32" => {
            let mut default: Option<f32> = None;
            let mut min: Option<f32> = None;
            let mut max: Option<f32> = None;

            if let Some(mut token_iter_attr) = token_iter_attr {
                loop {
                    match token_iter_attr.token() {
                        Token::Ident(ident) => match ident.to_string().as_ref() {
                            "default" => token_iter_attr.take_default_float(&mut default)?,
                            "min" => token_iter_attr.take_min_float(&mut min)?,
                            "max" => token_iter_attr.take_max_float(&mut max)?,
                            t => {
                                return Err(
                                    token_iter_attr.error(&format!("Unexpected Ident: `{t}`"))
                                );
                            }
                        },
                        Token::End => {
                            break;
                        }
                        t => {
                            return Err(token_iter_attr.error(&format!("Unexpected Token: {t:?}")));
                        }
                    }
                }
                check_number_default_min_max!(token_iter_attr, default, min, max);
            }

            token_iter.step();
            GType::Scalar(GScalar::f32 { default, min, max })
        }
        "f64" => {
            let mut default: Option<f64> = None;
            let mut min: Option<f64> = None;
            let mut max: Option<f64> = None;

            if let Some(mut token_iter_attr) = token_iter_attr {
                loop {
                    match token_iter_attr.token() {
                        Token::Ident(ident) => match ident.to_string().as_ref() {
                            "default" => token_iter_attr.take_default_float(&mut default)?,
                            "min" => token_iter_attr.take_min_float(&mut min)?,
                            "max" => token_iter_attr.take_max_float(&mut max)?,
                            t => {
                                return Err(
                                    token_iter_attr.error(&format!("Unexpected Ident: `{t}`"))
                                );
                            }
                        },
                        Token::End => {
                            break;
                        }
                        t => {
                            return Err(token_iter_attr.error(&format!("Unexpected Token: {t:?}")));
                        }
                    }
                }
                check_number_default_min_max!(token_iter_attr, default, min, max);
            }

            token_iter.step();
            GType::Scalar(GScalar::f64 { default, min, max })
        }
        "String" => {
            let mut default: Option<String> = None;
            let mut min: Option<usize> = None;
            let mut max: Option<usize> = None;
            let mut trim: Option<Trim> = None;
            let mut empty_to_none: Option<bool> = None;
            let mut no_empty: Option<bool> = None;

            if let Some(mut token_iter_attr) = token_iter_attr {
                loop {
                    match token_iter_attr.token() {
                        Token::Ident(ident) => match ident.to_string().as_ref() {
                            "default" => token_iter_attr.take_default_string(&mut default)?,
                            "min" => token_iter_attr.take_min_unsigned(&mut min)?,
                            "max" => token_iter_attr.take_max_unsigned(&mut max)?,
                            "trim" => token_iter_attr.take_trim(&mut trim)?,
                            "empty_to_none" => {
                                token_iter_attr.take_empty_to_none(&mut empty_to_none)?
                            }
                            "no_empty" => token_iter_attr.take_no_empty(&mut no_empty)?,
                            t => {
                                return Err(
                                    token_iter_attr.error(&format!("Unexpected Ident: `{t}`"))
                                );
                            }
                        },
                        Token::End => {
                            break;
                        }
                        t => {
                            return Err(token_iter_attr.error(&format!("Unexpected Token: {t:?}")));
                        }
                    }
                }
                check_number_default_min_max!(token_iter_attr, None as Option<usize>, min, max);
            }

            token_iter.step();
            GType::Scalar(GScalar::String {
                default,
                min,
                max,
                empty_to_none,
                no_empty,
                trim,
            })
        }
        "Uuid" => {
            let mut no_empty: Option<bool> = None;
            let mut version: Option<usize> = None;

            if let Some(mut token_iter_attr) = token_iter_attr {
                loop {
                    match token_iter_attr.token() {
                        Token::Ident(ident) => match ident.to_string().as_ref() {
                            "no_empty" => token_iter_attr.take_no_empty(&mut no_empty)?,
                            "version" => token_iter_attr.take_version(&mut version)?,
                            t => {
                                return Err(
                                    token_iter_attr.error(&format!("Unexpected Ident: {t}"))
                                );
                            }
                        },
                        Token::End => {
                            break;
                        }
                        t => {
                            return Err(token_iter_attr.error(&format!("Unexpected Token: {t:?}")));
                        }
                    }
                }
            }

            token_iter.step();
            GType::Scalar(GScalar::Uuid { no_empty, version })
        }
        "Decimal" => {
            let mut default: Option<Decimal> = None;
            let mut min: Option<Decimal> = None;
            let mut max: Option<Decimal> = None;

            if let Some(mut token_iter_attr) = token_iter_attr {
                loop {
                    match token_iter_attr.token() {
                        Token::Ident(ident) => match ident.to_string().as_ref() {
                            "default" => token_iter_attr.take_default_decimal(&mut default)?,
                            "min" => token_iter_attr.take_min_decimal(&mut min)?,
                            "max" => token_iter_attr.take_max_decimal(&mut max)?,
                            t => {
                                return Err(
                                    token_iter_attr.error(&format!("Unexpected Ident: `{t}`"))
                                );
                            }
                        },
                        Token::End => {
                            break;
                        }
                        t => {
                            return Err(token_iter_attr.error(&format!("Unexpected Token: {t:?}")));
                        }
                    }
                }
                check_number_default_min_max!(token_iter_attr, default, min, max);
            }

            token_iter.step();
            GType::Scalar(GScalar::Decimal { default, min, max })
        }
        "Integer" => {
            token_iter.step();
            GType::Scalar(GScalar::Integer)
        }
        "BigInt" => {
            token_iter.step();
            GType::Scalar(GScalar::BigInt)
        }
        "DateUtc" => {
            token_iter.step();
            GType::Scalar(GScalar::DateUtc)
        }
        "DateTz" => {
            token_iter.step();
            GType::Scalar(GScalar::DateTz)
        }
        "DateTimeUtc" => {
            token_iter.step();
            GType::Scalar(GScalar::DateTimeUtc)
        }
        "DateTimeTz" => {
            token_iter.step();
            GType::Scalar(GScalar::DateTimeTz)
        }
        "Time" => {
            token_iter.step();
            GType::Scalar(GScalar::Time)
        }
        "Duration" => {
            token_iter.step();
            GType::Scalar(GScalar::Duration)
        }
        "IpAddr" => {
            token_iter.step();
            GType::Scalar(GScalar::IpAddr)
        }

        "Option" => {
            // Step 1: parse the Attribute
            // The format of the attribute is `attr1=val1; ... <(some_attr_1=val_1; ...)>`

            // Set defaults
            let mut zero_to_none: Option<bool> = None;
            let inner_token_iter_attr: Option<TokenIter>;

            // TODO: Validate zero_to_none against valid T types (only numeric)
            match token_iter_attr {
                Some(mut token_iter_attr) => {
                    // move past the start token of the attribute
                    loop {
                        match token_iter_attr.token() {
                            // can repeat as many times as needed to grab all of the attributes
                            Token::Ident(ident) => match ident.to_string().as_str() {
                                "zero_to_none" => {
                                    token_iter_attr.take_zero_to_none(&mut zero_to_none)?
                                }
                                t => {
                                    return Err(token_iter_attr.error(
                                        format!("Unexpected Option validation token {t}").as_str(),
                                    ));
                                }
                            },
                            Token::LessThan => {
                                token_iter_attr.step();
                                inner_token_iter_attr = Some({
                                    let mut t = token_iter_attr.take_parenthesis_group_iter()?;
                                    t.step();
                                    t
                                });
                                token_iter_attr.take_greater_than()?;
                                token_iter_attr.get_end()?;
                                break;
                            }
                            t => {
                                return Err(token_iter_attr.error(
                                    format!("Expected `zero_to_none` | `<`: not `{t:?}`").as_str(),
                                ));
                            }
                        }
                    }
                }
                None => {
                    inner_token_iter_attr = None;
                }
            }

            // Step 2: parse the GType

            // We were already sitting on the Option token, so move past it
            token_iter.step();

            token_iter.take_less_than()?;
            let inner_gtype = take_gtype(inner_token_iter_attr, token_iter)?;

            token_iter.take_greater_than()?;

            // Default zero_to_none to false
            let zero_to_none = zero_to_none.unwrap_or(false);

            GType::Option {
                zero_to_none,
                some_type: Box::new(inner_gtype),
            }

            // Iterator is left on the token after `Option<...>`
        }
        "NestedError" => {
            token_iter.step();

            token_iter.take_less_than()?;
            let inner_gtype = take_gtype(None, token_iter)?;
            token_iter.take_greater_than()?;

            GType::NestedError(Box::new(inner_gtype))
        }
        "Result" => {
            token_iter.step();

            token_iter.take_less_than()?;

            let gok = take_gtype(None, token_iter)?;
            token_iter.take_comma()?;

            let gerr = take_gtype(None, token_iter)?;

            token_iter.take_greater_than()?;

            GType::Result(Box::new(gok), Box::new(gerr))
        }
        "Vec" => {
            token_iter.step();

            token_iter.take_less_than()?;
            let gtype = take_gtype(None, token_iter)?;
            token_iter.take_greater_than()?;
            GType::Vec(Box::new(gtype))
        }
        "HashSet" => {
            token_iter.step();

            token_iter.take_less_than()?;

            let scalar = match take_gtype(None, token_iter)? {
                GType::Scalar(scalar) => scalar,
                _ => return Err(token_iter.error("Only scalar types allowed as HashSet items")),
            };

            token_iter.take_greater_than()?;

            GType::HashSet(scalar)
        }
        "HashMap" => {
            // Step 1: parse the Attribute
            // The format of the Attribute is `attr1=val1; ... <(inner_attr_1=inner_val_1; ...), (inner_attr_2=inner_val_2; ...)>`

            // Set defaults
            let mut max_items: Option<usize> = None;
            let key_token_iter_attr: Option<TokenIter>;
            let value_token_iter_attr: Option<TokenIter>;

            match token_iter_attr {
                Some(mut token_iter_attr) => {
                    // move past the start token of the attribute
                    loop {
                        match token_iter_attr.token() {
                            // can repeat as many times as needed to grab all of the attributes
                            Token::Ident(ident) => match ident.to_string().as_str() {
                                "max_items" => token_iter_attr.take_max_items(&mut max_items)?,
                                t => {
                                    return Err(token_iter_attr.error(
                                        format!("Unexpected HashMap validation token {t}").as_str(),
                                    ));
                                }
                            },
                            Token::LessThan => {
                                token_iter_attr.step();
                                key_token_iter_attr = Some({
                                    let mut t = token_iter_attr.take_parenthesis_group_iter()?;
                                    t.step();
                                    t
                                });
                                token_iter_attr.take_comma()?;
                                value_token_iter_attr = Some({
                                    let mut t = token_iter_attr.take_parenthesis_group_iter()?;
                                    t.step();
                                    t
                                });
                                token_iter_attr.take_greater_than()?;
                                token_iter_attr.get_end()?;
                                break;
                            }
                            t => {
                                return Err(token_iter_attr.error(
                                    format!("Expected `max_items` | `<`: not `{t:?}`").as_str(),
                                ));
                            }
                        }
                    }
                }
                None => {
                    key_token_iter_attr = None;
                    value_token_iter_attr = None;
                }
            }

            // Step 2: parse the key GType::Scalar
            // We were already sitting on the HashMap token, so move past it
            token_iter.step();

            token_iter.take_less_than()?;

            // make the gtype a scalar for the HashMap key
            let ktype = match take_gtype(key_token_iter_attr, token_iter)? {
                GType::Scalar(scalar) => scalar,
                _ => return Err(token_iter.error("Only scalar types allowed as HashMap keys")),
            };

            // Step 3: parse the value GType
            token_iter.take_comma()?;

            let vtype = take_gtype(value_token_iter_attr, token_iter)?;

            token_iter.take_greater_than()?;

            GType::HashMap {
                key_type: ktype,
                value_type: Box::new(vtype),
                max_items,
            }
        }
        "BTreeMap" => {
            token_iter.step();

            token_iter.take_less_than()?;

            let gkey = match take_gtype(None, token_iter)? {
                GType::Scalar(scalar) => scalar,
                _ => return Err(token_iter.error("Only scalar types allowed as BTreeMap keys")),
            };

            token_iter.take_comma()?;

            let gvalue = take_gtype(None, token_iter)?;

            token_iter.take_greater_than()?;

            GType::BTreeMap(gkey, Box::new(gvalue))
        }
        "IndexMap" => {
            token_iter.step();

            token_iter.take_less_than()?;

            let gkey = match take_gtype(token_iter_attr, token_iter)? {
                GType::Scalar(scalar) => scalar,
                _ => return Err(token_iter.error("Only scalar types allowed as IndexMap keys")),
            };

            token_iter.take_comma()?;

            let gvalue = take_gtype(None, token_iter)?;

            token_iter.take_greater_than()?;

            GType::IndexMap(gkey, Box::new(gvalue))
        }
        "Range" => {
            token_iter.step();

            token_iter.take_less_than()?;

            let scalar = match take_gtype(None, token_iter)? {
                GType::Scalar(scalar) => scalar,
                _ => return Err(token_iter.error("Only scalar types allowed as Range items")),
            };

            token_iter.take_greater_than()?;

            GType::Range(scalar)
        }
        "JsonValue" => {
            token_iter.step();
            GType::JsonValue
        }
        "JsonObject" => {
            token_iter.step();
            GType::JsonObject
        }
        "JsonArray" => {
            token_iter.step();
            GType::JsonArray
        }

        // Aliases
        "BigIntSet" => {
            token_iter.step();
            GType::HashSet(GScalar::BigInt)
        }
        "BigIntVec" => {
            token_iter.step();
            GType::Vec(Box::new(GType::Scalar(GScalar::BigInt)))
        }
        "IntegerSet" => {
            token_iter.step();
            GType::HashSet(GScalar::Integer)
        }
        "IntegerVec" => {
            token_iter.step();
            GType::Vec(Box::new(GType::Scalar(GScalar::Integer)))
        }
        "StringSet" => {
            token_iter.step();
            GType::HashSet(GScalar::String {
                default: None,
                min: None,
                max: None,
                empty_to_none: None,
                no_empty: None,
                trim: None,
            })
        }
        "StringVec" => {
            token_iter.step();
            GType::Vec(Box::new(GType::Scalar(GScalar::String {
                default: None,
                min: None,
                max: None,
                empty_to_none: None,
                no_empty: None,
                trim: None,
            })))
        }
        ident_str => {
            token_iter.step();
            GType::GType(syn::Ident::new(ident_str, proc_macro2::Span::call_site()))
        }
    };

    Ok(gtype)
}

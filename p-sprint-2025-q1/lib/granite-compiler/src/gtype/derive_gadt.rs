#![allow(unused_imports, unused_macros)]

use std::default;

use super::{
    CodeGenOption, GADT, GInner, GInnerEnum, GInnerStruct, GInnerType, GMacroInput,
    GMacroInputBuilder, GScalar, GStyle, GType, GVariant, MacroOption,
};
use proc_macro2::{Ident, Span};
use quote::quote;
use syn::Visibility;

macro_rules! make_ident_p {
    ($ident:expr) => {
        Ident::new(&format!("{}_Partial", $ident), Span::call_site())
    };
}

macro_rules! make_ident_e {
    ($ident:expr) => {
        Ident::new(&format!("{}_Error", $ident), Span::call_site())
    };
}

impl GMacroInputBuilder {
    pub fn build(self) -> GMacroInput {
        GMacroInput {
            macro_option: self.macro_option.clone(),
            mod_ident: self.mod_ident.clone(),
            gadt_type: self.build_gadt_type(),
            gadt_partial: self.build_gadt_partial(),
            gadt_error: self.build_gadt_error(),
        }
    }

    fn build_gadt_type(&self) -> GADT {
        let g_inner = self.g_inner.clone();

        let code_gen = CodeGenOption {
            rs_type: self.macro_option.rs_type,
            rs_validate: self.macro_option.rs_type_validate,
            rs_encode: self.macro_option.rs_type_encode,
            rs_decode: self.macro_option.rs_type_decode,
            rs_debug: self.macro_option.rs_debug,
            rs_clone: self.macro_option.rs_clone,
            rs_partial_eq: self.macro_option.rs_partial_eq,
            ts_type: self.macro_option.ts_type,
            ts_validate: self.macro_option.ts_type_validate,
            ts_encode: self.macro_option.ts_type_encode,
            ts_decode: self.macro_option.ts_type_decode,
        };

        GADT {
            code_gen,
            vis: self.vis.clone(),
            attrs: self.attrs.clone(),
            g_inner,
            g_style: GStyle::Type {
                type_ident: self.ident.clone(),
                error_ident: make_ident_e!(self.ident),
            },
            mod_ident: self.mod_ident.clone(),
        }
    }

    fn build_gadt_partial(&self) -> GADT {
        fn _clone(gtype: &GType) -> GType {
            match gtype {
                GType::Undefinable(_) => unreachable!(),
                GType::NestedError(gtype) => GType::NestedError(gtype.clone()),
                GType::Undefined => GType::Undefined,

                GType::GType(ident) => GType::GType(make_ident_p!(ident)),
                GType::Scalar(scalar) => GType::Scalar(scalar.clone()),
                GType::Option {
                    some_type,
                    zero_to_none,
                } => GType::Option {
                    zero_to_none: *zero_to_none,
                    some_type: Box::new(_clone(some_type)),
                },
                GType::Result(ok, err) => {
                    GType::Result(Box::new(_clone(ok)), Box::new(_clone(err)))
                }
                GType::Vec(gtype) => GType::Vec(Box::new(_clone(gtype))),
                GType::HashSet(gscalar) => GType::HashSet(gscalar.clone()),
                GType::HashMap {
                    key_type,
                    value_type,
                    max_items,
                } => GType::HashMap {
                    key_type: key_type.clone(),
                    value_type: Box::new(_clone(value_type)),
                    max_items: *max_items,
                },
                GType::BTreeMap(gscalar, gtype) => {
                    GType::BTreeMap(gscalar.clone(), Box::new(_clone(gtype)))
                }
                GType::IndexMap(gscalar, gtype) => {
                    GType::IndexMap(gscalar.clone(), Box::new(_clone(gtype)))
                }
                GType::Range(gscalar) => GType::Range(gscalar.clone()),
                GType::JsonValue => GType::JsonValue,
                GType::JsonObject => GType::JsonObject,
                GType::JsonArray => GType::JsonArray,
            }
        }

        let g_inner = match &self.g_inner {
            GInner::Struct(GInnerStruct::Unit) => GInner::Struct(GInnerStruct::Unit),
            GInner::Struct(GInnerStruct::Tuple(fields)) => GInner::Struct(GInnerStruct::Tuple(
                fields
                    .iter()
                    .map(|(vis, gtype)| (vis.clone(), _undefinable(_clone(gtype))))
                    .collect(),
            )),
            GInner::Struct(GInnerStruct::Named(fields)) => GInner::Struct(GInnerStruct::Named(
                fields
                    .iter()
                    .map(|(vis, ident, gtype)| {
                        (vis.clone(), ident.clone(), _undefinable(_clone(gtype)))
                    })
                    .collect(),
            )),
            GInner::Enum(GInnerEnum(variants)) => GInner::Enum(GInnerEnum(
                variants
                    .iter()
                    .map(|(ident, gvariant)| {
                        let new_variant = match gvariant {
                            GVariant::Unit => GVariant::Unit,
                            GVariant::Tuple(singles) => GVariant::Tuple(
                                singles
                                    .iter()
                                    .map(|(gtype,)| (_undefinable(_clone(gtype)),))
                                    .collect(),
                            ),
                            GVariant::Struct(singles) => GVariant::Struct(
                                singles
                                    .iter()
                                    .map(|(ident, gtype)| {
                                        (ident.clone(), _undefinable(_clone(gtype)))
                                    })
                                    .collect(),
                            ),
                        };
                        (ident.clone(), new_variant)
                    })
                    .collect(),
            )),
            GInner::Type(GInnerType {
                type_path,
                import_from,
            }) => GInner::Type(GInnerType {
                type_path: {
                    let mut type_path = type_path.clone();
                    // unwrap is okay because we already checked this when the type was parsed
                    let last_segment = type_path.path.segments.last_mut().unwrap();
                    last_segment.ident = make_ident_p!(last_segment.ident);
                    type_path
                },
                import_from: import_from.clone(),
            }),
        };

        let code_gen = super::CodeGenOption {
            rs_type: self.macro_option.rs_partial,
            rs_validate: self.macro_option.rs_partial_validate,
            rs_encode: self.macro_option.rs_partial_encode,
            rs_decode: self.macro_option.rs_partial_decode,
            rs_debug: self.macro_option.rs_debug,
            rs_clone: self.macro_option.rs_clone,
            rs_partial_eq: self.macro_option.rs_partial_eq,

            ts_type: self.macro_option.ts_partial,
            ts_validate: self.macro_option.ts_partial_validate,
            ts_encode: self.macro_option.ts_partial_encode,
            ts_decode: self.macro_option.ts_partial_decode,
        };

        GADT {
            code_gen,
            vis: self.vis.clone(),
            attrs: vec![],
            g_inner,
            g_style: GStyle::Partial {
                type_ident: self.ident.clone(),
                partial_ident: make_ident_p!(self.ident),
                error_ident: make_ident_e!(self.ident),
            },
            mod_ident: self.mod_ident.clone(),
        }
    }

    /// translate the GInner into a GInner for holding error types
    fn build_gadt_error(&self) -> GADT {
        fn _clone(gtype: &GType) -> GType {
            match gtype {
                GType::Undefinable(_) => {
                    unreachable!("Undefinable should not be used with GStyle::Error")
                }
                GType::NestedError(_gtype) => _string(),

                GType::Undefined => unreachable!("Undefined should not be used with GStyle::Error"),

                GType::GType(ident) => _nested(GType::GType(make_ident_e!(ident))),
                GType::Scalar(_) => _string(),
                GType::Option { some_type, .. } => _clone(some_type), //NOT double nested
                GType::Result(_ok, _err) => {
                    unreachable!("Result should not be used with GStyle::Error")
                }
                GType::Vec(gtype) => _nested(_vec(_undefinable(_clone(gtype)))),
                GType::HashSet(_) => _nested(_vec(_string())),
                GType::HashMap {
                    key_type,
                    value_type,
                    ..
                } => _nested(_hashmap(key_type.clone(), _clone(value_type))),
                GType::BTreeMap(gscalar, gtype) => {
                    _nested(_hashmap(gscalar.clone(), _clone(gtype)))
                }
                GType::IndexMap(gscalar, gtype) => {
                    _nested(_hashmap(gscalar.clone(), _clone(gtype)))
                }
                GType::Range(_) => _string(),
                GType::JsonValue => _string(),
                GType::JsonObject => _string(),
                GType::JsonArray => _string(),
            }
        }

        fn vis_pub() -> Visibility {
            Visibility::Public(syn::Token![pub](Span::call_site()))
        }

        let g_inner = match &self.g_inner {
            GInner::Struct(GInnerStruct::Unit) => GInner::Struct(GInnerStruct::Tuple(vec![(
                vis_pub(),
                _undefinable(_string()),
            )])),
            GInner::Struct(GInnerStruct::Tuple(fields)) => GInner::Struct(GInnerStruct::Tuple(
                fields
                    .iter()
                    .map(|(vis, gtype)| (vis.clone(), _undefinable(_clone(gtype))))
                    .collect(),
            )),
            GInner::Struct(GInnerStruct::Named(fields)) => GInner::Struct(GInnerStruct::Named(
                fields
                    .iter()
                    .map(|(vis, ident, gtype)| {
                        (vis.clone(), ident.clone(), _undefinable(_clone(gtype)))
                    })
                    .collect(),
            )),
            GInner::Enum(GInnerEnum(variants)) => GInner::Enum(GInnerEnum(
                variants
                    .iter()
                    .map(|(ident, gvariant)| {
                        let new_variant = match gvariant {
                            GVariant::Unit => GVariant::Tuple(vec![(_undefinable(_string()),)]),
                            GVariant::Tuple(singles) => GVariant::Tuple(
                                singles
                                    .iter()
                                    .map(|(gtype,)| (_undefinable(_clone(gtype)),))
                                    .collect(),
                            ),
                            GVariant::Struct(singles) => GVariant::Struct(
                                singles
                                    .iter()
                                    .map(|(ident, gtype)| {
                                        (ident.clone(), _undefinable(_clone(gtype)))
                                    })
                                    .collect(),
                            ),
                        };
                        (ident.clone(), new_variant)
                    })
                    .collect(),
            )),

            GInner::Type(GInnerType {
                type_path,
                import_from,
            }) => {
                GInner::Type(GInnerType {
                    type_path: {
                        let mut type_path = type_path.clone();
                        // unwrap is okay because we already checked this when the type was parsed
                        let last_segment = type_path.path.segments.last_mut().unwrap();
                        last_segment.ident = make_ident_e!(last_segment.ident);
                        type_path
                    },
                    import_from: import_from.clone(),
                })
            }
        };

        let code_gen = CodeGenOption {
            rs_type: self.macro_option.rs_error,
            rs_validate: false,
            rs_encode: self.macro_option.rs_error_encode,
            rs_decode: self.macro_option.rs_error_decode,
            rs_debug: self.macro_option.rs_debug,
            rs_clone: self.macro_option.rs_clone,
            rs_partial_eq: false,

            ts_type: self.macro_option.ts_error,
            ts_validate: false,
            ts_encode: self.macro_option.ts_error_encode,
            ts_decode: self.macro_option.ts_error_decode,
        };

        GADT {
            code_gen,
            vis: self.vis.clone(),
            attrs: vec![],
            g_inner,
            g_style: GStyle::Error {
                error_ident: make_ident_e!(self.ident),
            },
            mod_ident: self.mod_ident.clone(),
        }
    }
}

fn _string() -> GType {
    GType::Scalar(GScalar::String {
        default: None,
        max: None,
        min: None,
        empty_to_none: None,
        no_empty: None,
        trim: None,
    })
}

fn _option(gtype: GType) -> GType {
    GType::Option {
        zero_to_none: false,
        some_type: Box::new(gtype),
    }
}

fn _undefinable(gtype: GType) -> GType {
    GType::Undefinable(Box::new(gtype))
}

fn _nested(gtype: GType) -> GType {
    GType::NestedError(Box::new(gtype))
}

fn _vec(gtype: GType) -> GType {
    GType::Vec(Box::new(gtype))
}

fn _hashmap(key_type: GScalar, value_type: GType) -> GType {
    GType::HashMap {
        key_type,
        value_type: Box::new(value_type),
        max_items: None,
    }
}

mod codegen;
mod parse;
use crate::tokenator::TokenError;
use indexmap::IndexMap;

pub struct GValidate {
    pub items: Vec<syn::Item>,
    pub gvgroups: Vec<GVGroup>,
}

pub struct GVGroup {
    pub ident: syn::Ident,
    pub struct_in: GVStruct,
    pub struct_out: GVStruct,
    pub struct_err: GVStruct,
    pub trait_ident: syn::Ident,
    pub fn_validate: GVFnValidate,
    pub fn_field_map: IndexMap<syn::Ident, GVFnField>,
}

pub struct GVStruct {
    pub ident: syn::Ident,
    pub fields: Vec<GVField>,
}

pub struct GVField {
    pub ident: syn::Ident,
    pub ty: syn::Type,
}

// Represents the main validate function
pub struct GVFnValidate {
    pub args: Vec<GVFnValidateArg>,
}

#[allow(clippy::large_enum_variant)]
pub enum GVFnValidateArg {
    SelfArg,
    NamedArg { ident: syn::Ident, ty: syn::Type },
}

pub enum GvFnValidateReturn {
    ResultOkErr,
}

pub struct GVFnField {
    pub ident: syn::Ident,
    pub args: Vec<GVFnFieldArg>,
    pub return_type: GvFnFieldReturn,
}

pub struct GVFnFieldArg {
    pub ident: syn::Ident,
    pub ty: syn::Type,
}

pub enum GvFnFieldReturn {
    ResultOkErr,
}

pub fn handle_gvalidate_macro(items: Vec<syn::Item>) -> Result<GValidate, TokenError> {
    // step 1 is to parse
    let gvalidate = GValidate::parse(items)?;

    // step 2 is to return the GValidate struct
    Ok(gvalidate)
}

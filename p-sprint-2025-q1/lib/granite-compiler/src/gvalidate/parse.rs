use super::{
    GVField, GVFnField, GVFnValidate, GVFnValidateArg, GVGroup, GVStruct, GValidate,
    GvFnFieldReturn,
};
use crate::tokenator::TokenError;
use indexmap::IndexMap;
use syn::spanned::Spanned;

impl GValidate {
    /// Takes all the items that are inside the granite::gvalidate!{...} macro, and parses
    pub fn parse(items: Vec<syn::Item>) -> Result<Self, TokenError> {
        // Identify all traits that end in `Validate` and process them into GVGroup structs
        let gvgroups = {
            let mut gvgroups = Vec::new();
            for item in items.iter() {
                if let syn::Item::Trait(t) = item {
                    if let Some(ident) = t.ident.to_string().strip_suffix("Validate") {
                        // The main struct is the same as the trait with the "Validate" suffix removed
                        let struct_ident = syn::Ident::new(ident, t.ident.span());
                        gvgroups.push(self::process_gvgroup(&items, struct_ident, t)?);
                    }
                }
            }
            gvgroups
        };

        Ok(GValidate { items, gvgroups })
    }
}

/// A GVGroup is essentially a group of some
/// - `struct Foo`
/// - `struct FooOk`
/// - `struct FooErr`
/// - `traite FooValidate`
///
/// Because gvalidate!{} can hold more than one GVGroup, we break it down into groups.
fn process_gvgroup(
    items: &[syn::Item],
    struct_ident: syn::Ident,
    validate_trait: &syn::ItemTrait,
) -> Result<GVGroup, TokenError> {
    let struct_ident_in = struct_ident.clone();
    let struct_ident_ok = quote::format_ident!("{}Ok", struct_ident);
    let struct_ident_err = quote::format_ident!("{}Err", struct_ident);
    let trait_ident = validate_trait.ident.clone();

    let struct_in = parse_struct(items, &struct_ident_in)?;
    let struct_out = parse_struct(items, &struct_ident_ok)?;
    let struct_err = parse_struct(items, &struct_ident_err)?;

    let (fn_validate, fn_field_map) = parse_validation_trait(validate_trait)?;

    Ok(GVGroup {
        ident: struct_ident,
        struct_in,
        struct_out,
        struct_err,
        trait_ident,
        fn_validate,
        fn_field_map,
    })
}

fn parse_struct(items: &[syn::Item], ident: &syn::Ident) -> Result<GVStruct, TokenError> {
    let found_struct = find_struct(items, ident)?;

    let ident = found_struct.ident.clone();
    let mut fields = Vec::with_capacity(found_struct.fields.len());
    for field in found_struct.fields.iter() {
        let ident = match &field.ident {
            Some(ident) => ident.clone(),
            None => {
                return Err(TokenError::new(
                    ident.span(),
                    "Only named fields are allowed.",
                ));
            }
        };

        let ty = field.ty.clone();

        fields.push(GVField { ident, ty });
    }

    Ok(GVStruct { ident, fields })
}

/// Parse the validation trait into a list of arguments, and a map of functions
fn parse_validation_trait(
    validate_trait: &syn::ItemTrait,
) -> Result<(GVFnValidate, IndexMap<syn::Ident, GVFnField>), TokenError> {
    let mut fn_validate: Option<GVFnValidate> = None; // none means it is not found

    let mut validation_fn_map = IndexMap::new();

    for item in validate_trait.items.iter() {
        match item {
            syn::TraitItem::Fn(fn_item) if fn_item.sig.ident == "validate" => {
                fn_validate = Some(parse_fn_validate(fn_item)?);
            }
            syn::TraitItem::Fn(fn_item) => {
                let fn_field = parse_fn_field(fn_item)?;
                validation_fn_map.insert(fn_field.ident.clone(), fn_field);
            }
            other => {
                return Err(TokenError::new(
                    other.span(),
                    "Only functions are allowed in validation traits.",
                ));
            }
        }
    }

    let fn_validate = match fn_validate {
        Some(fn_validate) => fn_validate,
        None => {
            return Err(TokenError::new_call_site(
                "`fn validate(self, ...) -> Result<..., ...>` not found in trait",
            ));
        }
    };

    Ok((fn_validate, validation_fn_map))
}

fn parse_fn_validate(fn_item: &syn::TraitItemFn) -> Result<GVFnValidate, TokenError> {
    // Parse args
    let args = {
        let mut args = Vec::new();
        for arg in fn_item.sig.inputs.iter() {
            match arg {
                // Make sure the receiver is owned
                syn::FnArg::Receiver(receiver) => {
                    // reference, mutability, and colon token must all be none
                    if receiver.reference.is_some() {
                        return Err(TokenError::new(
                            receiver.span(),
                            "The receiver must be owned (i.e. `self`, not `&self` or `&mut self`)",
                        ));
                    }

                    if receiver.mutability.is_some() {
                        return Err(TokenError::new(
                            receiver.span(),
                            "The receiver must be owned (i.e. `self`, not `&self` or `&mut self`)",
                        ));
                    }

                    if receiver.colon_token.is_some() {
                        return Err(TokenError::new(
                            receiver.span(),
                            "The receiver must be owned (i.e. `self`, not `&self` or `&mut self`)",
                        ));
                    }

                    args.push(GVFnValidateArg::SelfArg);
                }
                syn::FnArg::Typed(pat_type) => {
                    let ident = match &*pat_type.pat {
                        syn::Pat::Ident(pat_ident) => pat_ident.ident.clone(),
                        _ => {
                            return Err(TokenError::new(
                                pat_type.span(),
                                "Only named arguments are allowed.",
                            ));
                        }
                    };

                    let ty = match &*pat_type.ty {
                        syn::Type::Path(ty_path) => {
                            // qself must be none
                            if ty_path.qself.is_some() {
                                return Err(TokenError::new(
                                    ty_path.span(),
                                    "Only named arguments are allowed.",
                                ));
                            }
                            (*pat_type.ty).clone()
                        }
                        _ => {
                            return Err(TokenError::new(
                                pat_type.span(),
                                "Only named arguments are allowed.",
                            ));
                        }
                    };

                    args.push(GVFnValidateArg::NamedArg { ident, ty });
                }
            }
        }
        args
    };

    Ok(GVFnValidate { args })
}

fn parse_fn_field(fn_item: &syn::TraitItemFn) -> Result<GVFnField, TokenError> {
    let ident = fn_item.sig.ident.clone();

    let args = Vec::new();

    Ok(GVFnField {
        ident,
        args,
        return_type: GvFnFieldReturn::ResultOkErr,
    })
}

/// Find a specifically named struct in the list of items
fn find_struct<'a>(
    items: &'a [syn::Item],
    ident: &syn::Ident,
) -> Result<&'a syn::ItemStruct, TokenError> {
    for item in items {
        if let syn::Item::Struct(s) = item {
            if s.ident == *ident {
                return Ok(s);
            }
        }
    }
    Err(TokenError::new_call_site(
        format!("Struct not found: {ident}").as_str(),
    ))
}

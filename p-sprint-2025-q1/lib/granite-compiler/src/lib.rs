pub mod gtype;
pub mod gvalidate;
pub mod postgres;
pub mod tokenator;
pub mod webpath;

#[macro_export]
macro_rules! ident_str {
    ($ident:expr) => {
        proc_macro2::Ident::new(&$ident, proc_macro2::Span::call_site())
    };
}

/// Utility function for implementing == for syn::Path
pub fn syn_path_equal(path1: &syn::Path, path2: &syn::Path) -> bool {
    if path1.segments.len() != path2.segments.len() {
        return false;
    }

    path1
        .segments
        .iter()
        .zip(path2.segments.iter())
        .all(|(seg1, seg2)| seg1.ident == seg2.ident)
}

pub fn syn_path_equal_ident_str(path1: &syn::Path, ident_str: &str) -> bool {
    path1.segments.len() == 1 && (path1.segments.get(0).unwrap().ident == ident_str)
}

pub fn syn_path_to_ident(path: &syn::Path) -> Option<syn::Ident> {
    if path.segments.len() == 1 {
        Some(path.segments.get(0).unwrap().ident.clone())
    } else {
        None
    }
}

pub fn syn_path_push_str(mut path: syn::Path, ident_str: &str) -> syn::Path {
    path.segments.push(syn::PathSegment {
        ident: ident_str!(ident_str),
        arguments: syn::PathArguments::None,
    });
    path
}

pub fn syn_path_push_ident(mut path: syn::Path, ident: syn::Ident) -> syn::Path {
    path.segments.push(syn::PathSegment {
        ident,
        arguments: syn::PathArguments::None,
    });
    path
}

pub fn syn_path_display(path: &syn::Path) -> String {
    path.segments
        .iter()
        .map(|seg| seg.ident.to_string())
        .collect::<Vec<_>>()
        .join("::")
}

/// Finds a single attribute matching the given path
pub fn find_single_attr<'a>(
    attributes: &'a [syn::Attribute],
    search_syn_path: &syn::Path,
) -> Result<Option<&'a syn::Attribute>, &'a syn::Attribute> {
    let mut found_attr = None;

    for attribute in attributes {
        if crate::syn_path_equal(attribute.path(), search_syn_path) {
            if found_attr.is_some() {
                return Err(attribute);
            }
            found_attr = Some(attribute);
        }
    }

    Ok(found_attr)
}
